# Environment variables declared in this file are automatically made available to <PERSON>risma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# POSTGRES_DB=ssdm_dev
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=root
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432

POSTGRES_DB=ssdm_dev
POSTGRES_USER=ssdm_devs
POSTGRES_PASSWORD=M3WWsIzgu51tkcJ
POSTGRES_HOST=**************
POSTGRES_PORT=5434

CORS_METHOD='GET,PUT,PATCH,POST,DELETE'
CORS_DOMAIN='http://localhost:3000'

AWS_S3_BUCKET=ssdm-devs
AWS_S3_REGION=ap-southeast-1
AWS_S3_ACCESS_KEY=********************
AWS_S3_SECRET_KEY=vDt5BDINvTF+qjhC1s3BY3FbMxcpumhgFn2/nDlA

#MINIO PATH
MINIO_ENDPOINT='minio-api.satusdm.com'
MINIO_PROTOCOL='https'
MINIO_SSL=true
MINIO_BUCKET_NAME='ssdm-objectstorage'
MINIO_PATH_FILE='/dev/ssdm-object/'
MINIO_ACCESS_KEY=lLJcLiyAG46uhcaB0GUN
MINIO_SECRET_KEY=DCYQJMzgrAgBZALbnBCnaGVb4380ucpNoLmFAHds

#CONFIG FCM
FCM_CLIENT_EMAIL='-'
FCM_PRIVATE_KEY='-'
FCM_PROJECT_ID='-'

PORT=3000

DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public

SALT='$ab$23$CDe4567fg8.9hij0KL1MNop2qR3StuVWxyzA4BCdef5gh6iJK7lm8'