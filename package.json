{"name": "ssdm-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "db:migrate": "prisma migrate dev", "db:pull": "prisma db pull -schema=./prisma/schema/", "db:push": "prisma db push -schema=./prisma/schema/", "db:generate": "rm -rf node_modules/.prisma dist && prisma generate", "db:seed": "prisma db seed", "start": "nest start", "start:dev": "npm run db:generate && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.4.15", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.1", "@nestjs/sequelize": "^10.0.1", "@nestjs/swagger": "^7.3.0", "@prisma/client": "^6.3.1", "archiver": "^7.0.1", "aws-sdk": "^2.1628.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "cjs": "^0.0.11", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "express": "^4.18.3", "firebase-admin": "^12.4.0", "flatted": "^3.3.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "minio": "^8.0.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "nest-winston": "^1.10.2", "nestjs-sequelize-seeder-v2": "^2.1.0", "node-cache": "^5.1.2", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.15.0", "pdfmake": "^0.2.14", "pg": "^8.11.3", "prisma": "^6.3.1", "puppeteer": "^23.11.0", "randomstring": "^1.3.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sequelize": "^6.37.1", "sequelize-cli": "^6.6.2", "sequelize-typescript": "^2.1.6", "speakeasy": "^2.0.0", "uuid": "^11.0.3", "validator": "^13.12.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/archiver": "^6.0.2", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/pdfkit": "^0.13.5", "@types/pdfmake": "^0.2.11", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts", "schema": "./prisma/schema/"}}