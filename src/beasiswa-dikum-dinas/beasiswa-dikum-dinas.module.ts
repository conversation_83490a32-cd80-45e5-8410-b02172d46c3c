import { forwardRef, Module } from '@nestjs/common';
import { BeasiswaDikumDinasController } from './controller/beasiswa-dikum-dinas.controller';
import { BeasiswaDikumDinasService } from './service/beasiswa-dikum-dinas.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [BeasiswaDikumDinasController],
  providers: [BeasiswaDikumDinasService, MinioService],
})
export class BeasiswaDikumDinasModule {}
