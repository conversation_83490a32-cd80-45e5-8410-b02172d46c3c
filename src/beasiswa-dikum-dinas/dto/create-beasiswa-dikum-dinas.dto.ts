import { PartialType } from '@nestjs/mapped-types';
import { status_beasiswa_dikum_dinas_enum } from '@prisma/client';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export const RequiredFiles = ['surat_perintah', 'surat_telegram'];

export class CreateBeasiswaDinasDikumDTO {
  @IsNotEmpty({ message: 'Personel ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Personel ID harus berupa angka' })
  personel_id: number;

  @IsNotEmpty({ message: 'Beasiswa tidak boleh kosong' })
  @IsString({ message: 'Beasiswa harus berupa string' })
  jenis_beasiswa: string;

  @IsNotEmpty({ message: 'Jenjang pendidikan tidak boleh kosong' })
  @IsString({ message: 'Jenjang pendidikan harus berupa string' })
  jenjang_pendidikan: string;

  @IsNotEmpty({ message: 'Universitas tidak boleh kosong' })
  @IsString({ message: 'Universitas harus berupa string' })
  nama_universitas: string;

  @IsNotEmpty({ message: 'Jurusan tidak boleh kosong' })
  @IsString({ message: 'Jurusan harus berupa string' })
  jurusan: string;

  @IsNotEmpty({ message: 'Waktu mulai tidak boleh kosong' })
  @IsDateString({}, { message: 'Waktu mulai harus berupa date string' })
  waktu_mulai: string;

  @IsOptional()
  @IsDateString({}, { message: 'Waktu lulus harus berupa date string' })
  waktu_lulus?: string;

  @IsNotEmpty({ message: 'Status tidak boleh kosong' })
  @IsEnum(status_beasiswa_dikum_dinas_enum)
  status: status_beasiswa_dikum_dinas_enum;
}

export class UpdateBeasiswaDinasDikumDTO extends PartialType(
  CreateBeasiswaDinasDikumDTO,
) {}

export class CreateBulkInsertDinasDikumDTO extends PartialType(
  CreateBeasiswaDinasDikumDTO,
) {
  @IsNotEmpty({ message: 'NRP tidak boleh kosong' })
  @IsString({ message: 'NRP harus berupa string' })
  nrp: string;
}

export class BulkInsertBeasiswaDTO {
  @IsArray()
  beasiswaData: CreateBulkInsertDinasDikumDTO[];
}
