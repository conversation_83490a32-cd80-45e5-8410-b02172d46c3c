import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateBeasiswaDinasDikumDTO,
  CreateBulkInsertDinasDikumDTO,
  RequiredFiles,
  UpdateBeasiswaDinasDikumDTO,
} from '../dto/create-beasiswa-dikum-dinas.dto';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

const ExcelJS = require('exceljs');

@Injectable()
export class BeasiswaDikumDinasService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const { page, limit } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string';
      };
    } = {
      name: { field: 'personel.nama_lengkap', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, listBeasiswa] = await this.prisma.$transaction([
      this.prisma.beasiswa_dikum_dinas.count({
        where,
      }),
      this.prisma.beasiswa_dikum_dinas.findMany({
        select: {
          id: true,
          personel: {
            select: {
              id: true,
              nrp: true,
              nama_lengkap: true,
            },
          },
          jenis_beasiswa: true,
          jenjang_pendidikan: true,
          nama_universitas: true,
          jurusan: true,
          waktu_mulai: true,
          waktu_lulus: true,
          status: true,
        },
        where,
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      listBeasiswa.map(async ({ personel, ...beasiswa }) => {
        const pangkat = await this.prisma.pangkat_personel.findFirst({
          select: {
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
          },
          where: { personel_id: personel.id },
          orderBy: {
            tmt: 'desc',
          },
        });

        const jabatan = await this.prisma.jabatan_personel.findFirst({
          select: {
            jabatans: {
              select: {
                nama: true,
              },
            },
          },
          where: { personel_id: personel.id },
          orderBy: {
            tmt_jabatan: 'desc',
          },
        });

        return {
          ...beasiswa,
          personel_id: personel.id,
          nrp: personel.nrp,
          nama_lengkap: personel.nama_lengkap,
          pangkat: pangkat?.pangkat?.nama_singkat || null,
          jabatan: jabatan?.jabatans?.nama || null,
        };
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BEASISWA_DIKUM_DINAS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: +page,
      totalPage,
      totalData,
      data: queryResult,
    };
  }

  async getById(req: any, id: string) {
    const detailBeasiswa = await this.prisma.beasiswa_dikum_dinas.findFirst({
      select: {
        id: true,
        personel: {
          select: {
            id: true,
            nrp: true,
            nama_lengkap: true,
          },
        },
        jenis_beasiswa: true,
        jenjang_pendidikan: true,
        nama_universitas: true,
        jurusan: true,
        waktu_mulai: true,
        waktu_lulus: true,
        status: true,
        dokumen_beasiswa_dikum_dinas: true,
      },
      where: { id: +id },
    });

    if (!detailBeasiswa) {
      throw new NotFoundException('Detail personel beasiswa tidak ditemukan.');
    }

    const { personel, ...data } = detailBeasiswa;

    const pangkat = await this.prisma.pangkat_personel.findFirst({
      select: {
        pangkat: {
          select: {
            nama_singkat: true,
          },
        },
      },
      where: { personel_id: personel.id },
      orderBy: {
        tmt: 'desc',
      },
    });

    const jabatan = await this.prisma.jabatan_personel.findFirst({
      select: {
        jabatans: {
          select: {
            nama: true,
          },
        },
      },
      where: { personel_id: personel.id },
      orderBy: {
        tmt_jabatan: 'desc',
      },
    });

    for (const doc of data.dokumen_beasiswa_dikum_dinas) {
      if (doc.filename) {
        doc.url = await this.minioService.convertFileKeyToURL(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}/${doc.filename}`,
        );
      }
    }

    const queryResult = {
      ...data,
      personel_id: personel.id,
      nrp: personel.nrp,
      nama_lengkap: personel.nama_lengkap,
      pangkat: pangkat?.pangkat?.nama_singkat || null,
      jabatan: jabatan?.jabatans?.nama || null,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BEASISWA_DIKUM_DINAS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async create(req: any, body: CreateBeasiswaDinasDikumDTO, files: any) {
    const {
      personel_id,
      jenis_beasiswa,
      jenjang_pendidikan,
      nama_universitas,
      jurusan,
      waktu_mulai,
      waktu_lulus,
      status,
    } = body;

    const resultFiles = await this.validatieFiles(files);

    const personel = await this.prisma.personel.findFirst({
      where: {
        id: personel_id,
      },
    });

    if (!personel) {
      throw new NotFoundException(
        `ID Personel '${personel_id}' tidak ditemukan.`,
      );
    }

    try {
      const createPromises = [];
      let uploadIndex = 0;
      const uploadResults = await this.createDokumen(resultFiles, files);

      const queryResult = await this.prisma.$transaction(
        async (prismaClient) => {
          const createBeasiswa = await prismaClient.beasiswa_dikum_dinas.create(
            {
              data: {
                personel_id,
                jenis_beasiswa,
                jenjang_pendidikan,
                nama_universitas,
                jurusan,
                waktu_mulai: waktu_mulai ? new Date(waktu_mulai) : null,
                waktu_lulus: waktu_lulus ? new Date(waktu_lulus) : null,
                status,
              },
            },
          );

          for (const key in files) {
            const fileArray = files[key];
            for (const file of fileArray) {
              if (resultFiles.includes(file.fieldname)) {
                const uploadResult = uploadResults[uploadIndex];

                if (uploadResult) {
                  createPromises.push(
                    await prismaClient.dokumen_beasiswa_dikum_dinas.create({
                      data: {
                        beasiswa_dikum_dinas_id: Number(createBeasiswa.id),
                        jenis_dokumen: file.fieldname,
                        originalname: file.originalname,
                        encoding: file.encoding,
                        mimetype: file.mimetype,
                        size: file.size,
                        key: uploadResult.Key || '',
                        url: uploadResult.Location || '',
                        filename: uploadResult.filename || '',
                      },
                    }),
                  );
                  uploadIndex++;
                }
              }
            }
          }

          return createBeasiswa;
        },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BEASISWA_DIKUM_DINAS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(error.toString());
    }
  }

  async bulkInsert(req: any, body: CreateBulkInsertDinasDikumDTO[]) {
    try {
      const createPromises = [];
      const beasiswaResults = [];

      const queryResult = await this.prisma.$transaction(
        async (prismaClient) => {
          for (const [index, data] of body.entries()) {
            const {
              nrp,
              jenis_beasiswa,
              jenjang_pendidikan,
              nama_universitas,
              jurusan,
              waktu_mulai,
              waktu_lulus,
              status,
            } = data;

            const personel = await prismaClient.personel.findFirst({
              where: { nrp: nrp },
            });

            if (!personel) {
              throw new NotFoundException(
                `NRP Personel '${nrp}' tidak ditemukan.`,
              );
            }

            const createBeasiswa =
              await prismaClient.beasiswa_dikum_dinas.create({
                data: {
                  personel_id: personel.id,
                  jenis_beasiswa,
                  jenjang_pendidikan,
                  nama_universitas,
                  jurusan,
                  waktu_mulai: waktu_mulai ? new Date(waktu_mulai) : null,
                  waktu_lulus: waktu_lulus ? new Date(waktu_lulus) : null,
                  status,
                },
              });

            beasiswaResults.push(createBeasiswa);
          }

          await Promise.all(createPromises);
          return beasiswaResults;
        },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BEASISWA_DIKUM_DINAS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(error.message || error.toString());
    }
  }

  async update(req: any, id: string, body: UpdateBeasiswaDinasDikumDTO) {
    const {
      personel_id,
      jenis_beasiswa,
      jenjang_pendidikan,
      nama_universitas,
      jurusan,
      waktu_mulai,
      waktu_lulus,
      status,
    } = body;

    const beasiswa = await this.prisma.beasiswa_dikum_dinas.findFirst({
      where: { id: +id },
    });

    if (!beasiswa) {
      throw new NotFoundException(`ID Beasiswa '${id}' tidak ditemukan.`);
    }

    const personel = await this.prisma.personel.findFirst({
      where: {
        id: personel_id,
      },
    });

    if (!personel) {
      throw new NotFoundException(
        `ID Personel '${personel_id}' tidak ditemukan.`,
      );
    }

    try {
      const queryResult = await this.prisma.beasiswa_dikum_dinas.update({
        where: { id: beasiswa.id },
        data: {
          personel_id,
          jenis_beasiswa,
          jenjang_pendidikan,
          nama_universitas,
          jurusan,
          waktu_mulai: waktu_mulai ? new Date(waktu_mulai) : null,
          waktu_lulus: waktu_lulus ? new Date(waktu_lulus) : null,
          status,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BEASISWA_DIKUM_DINAS_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(error.toString());
    }
  }

  async uploadFile(req: any, id: string, files: any) {
    const beasiswa = await this.prisma.beasiswa_dikum_dinas.findFirst({
      where: { id: +id },
    });

    if (!beasiswa) {
      throw new NotFoundException(`ID Beasiswa '${id}' tidak ditemukan.`);
    }

    const resultFiles = await this.validatieFiles(files);

    try {
      const uploadResults = await this.createDokumen(resultFiles, files);
      let uploadIndex = 0;

      const queryResult = await this.prisma.$transaction(
        async (prismaClient) => {
          for (const key in files) {
            const fileArray = files[key];
            for (const file of fileArray) {
              if (resultFiles.includes(file.fieldname)) {
                const uploadResult = uploadResults[uploadIndex];

                if (uploadResult) {
                  const existingDokumen =
                    await prismaClient.dokumen_beasiswa_dikum_dinas.findFirst({
                      where: {
                        beasiswa_dikum_dinas_id: beasiswa.id,
                        jenis_dokumen: file.fieldname,
                      },
                    });

                  if (existingDokumen) {
                    return await prismaClient.dokumen_beasiswa_dikum_dinas.update(
                      {
                        where: { id: existingDokumen.id },
                        data: {
                          originalname: file.originalname,
                          encoding: file.encoding,
                          mimetype: file.mimetype,
                          size: file.size,
                          key: uploadResult.Key || '',
                          url: uploadResult.Location || '',
                          filename: uploadResult.filename || '',
                          updated_at: new Date(),
                        },
                      },
                    );
                  } else {
                    return await prismaClient.dokumen_beasiswa_dikum_dinas.create(
                      {
                        data: {
                          beasiswa_dikum_dinas_id: beasiswa.id,
                          jenis_dokumen: file.fieldname,
                          originalname: file.originalname,
                          encoding: file.encoding,
                          mimetype: file.mimetype,
                          size: file.size,
                          key: uploadResult.Key || '',
                          url: uploadResult.Location || '',
                          filename: uploadResult.filename || '',
                        },
                      },
                    );
                  }

                  uploadIndex++;
                }
              }
            }
          }
        },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BEASISWA_DIKUM_DINAS_UPLOAD as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(error.message || error.toString());
    }
  }

  async validatieFiles(files: { [key: string]: Express.Multer.File[] }) {
    const requiredFiles = RequiredFiles;
    const errors = {};
    let hasValidFile = false;

    for (const file of requiredFiles) {
      if (files[file] && files[file].length > 0) {
        hasValidFile = true;
      } else {
        errors[file] = `${file.replace(/_/g, ' ')} tidak boleh kosong`;
      }
    }

    if (!hasValidFile) {
      throw new UnprocessableEntityException(
        'Minimal salah satu file surat telegram atau surat perintah harus ada',
      );
    }

    await this.validateFilesSize(files, undefined, requiredFiles);

    return requiredFiles.filter(
      (file) => files[file] && files[file].length > 0,
    );
  }

  async validateFilesSize(
    files: { [key: string]: Express.Multer.File[] },
    maxFileSize = 4 * 1024 * 1024,
    requiredFiles?: string[],
  ) {
    const errors = {};

    for (const fieldName in files) {
      if (requiredFiles && !requiredFiles.includes(fieldName)) {
        continue;
      }

      const fieldFiles = files[fieldName];
      fieldFiles.forEach((file: Express.Multer.File) => {
        if (file.size > maxFileSize) {
          errors[fieldName] =
            `${fieldName.replace(/_/g, ' ')} file terlalu besar`;
        }
      });
    }

    if (Object.keys(errors).length > 0) {
      throw new UnprocessableEntityException({
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        error: 'Unprocessable Entity',
        message: errors,
      });
    }
  }

  async createDokumen(requiredFiles: string[], files: any): Promise<any[]> {
    const uploadPromises = [];

    for (const key in files) {
      const fileArray = files[key];
      for (const file of fileArray) {
        if (requiredFiles.includes(file.fieldname)) {
          uploadPromises.push(this.minioService.uploadFile(file));
        }
      }
    }

    const uploadResults = await Promise.all(uploadPromises);

    return uploadResults;
  }

  async generateTempleteXlsx(req: any) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Data Beasiswa');

    worksheet.columns = [
      { header: 'nrp', key: 'nrp', width: 15 },
      { header: 'jenis_beasiswa', key: 'jenis_beasiswa', width: 20 },
      { header: 'jenjang_pendidikan', key: 'jenjang_pendidikan', width: 20 },
      { header: 'nama_universitas', key: 'nama_universitas', width: 25 },
      { header: 'jurusan', key: 'jurusan', width: 20 },
      { header: 'waktu_mulai', key: 'waktu_mulai', width: 15 },
      { header: 'waktu_lulus', key: 'waktu_lulus', width: 15 },
      { header: 'status', key: 'status', width: 10 },
    ];

    const data = [
      {
        nrp: '12345678',
        jenis_beasiswa: 'LPDP',
        jenjang_pendidikan: 'S1',
        nama_universitas: 'Universitas XYZ',
        jurusan: 'Teknik Informatika',
        waktu_mulai: '2023-01-01',
        waktu_lulus: '2027-12-31',
        status: 'DINAS',
      },
    ];

    worksheet.addRows(data);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BEASISWA_DIKUM_DINAS_WRITE_EXCEL as ConstantLogType,
        message,
        data,
      ),
    );

    return await workbook.xlsx.writeBuffer();
  }
}
