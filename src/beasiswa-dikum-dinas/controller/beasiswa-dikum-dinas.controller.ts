import {
  Body,
  Controller,
  Get,
  HttpCode,
  InternalServerErrorException,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { BeasiswaDikumDinasService } from '../service/beasiswa-dikum-dinas.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  BulkInsertBeasiswaDTO,
  CreateBeasiswaDinasDikumDTO,
  UpdateBeasiswaDinasDikumDTO,
} from '../dto/create-beasiswa-dikum-dinas.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';

@Controller('beasiswa-dikum-dinas')
@UseGuards(JwtAuthGuard)
export class BeasiswaDikumDinasController {
  private readonly logger = new Logger(BeasiswaDikumDinasController.name);

  constructor(
    private readonly beasiswaDikumDinasService: BeasiswaDikumDinasService,
  ) {}

  @Get('/templete')
  @HttpCode(200)
  async exportExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.exportExcel.name}`);

    try {
      const buffer =
        await this.beasiswaDikumDinasService.generateTempleteXlsx(req);

      // Set response headers
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="templete-beasiswa-dikum-dinas.xlsx"`,
      );
      this.logger.log(`Leaving ${this.exportExcel.name}`);

      // Send buffer as response
      res.send(buffer);
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }

  @Get()
  @HttpCode(200)
  async getAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAll.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.beasiswaDikumDinasService.getAll(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getAll.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @HttpCode(200)
  async getById(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getById.name} with id ${id}`);

    const response = await this.beasiswaDikumDinasService.getById(req, id);

    this.logger.log(
      `Leaving ${this.getById.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post()
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'surat_perintah', maxCount: 1 },
      { name: 'surat_telegram', maxCount: 1 },
    ]),
  )
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateBeasiswaDinasDikumDTO,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(`Entering ${this.create.name} with body ${body}`);

    const response = await this.beasiswaDikumDinasService.create(
      req,
      body,
      files,
    );

    this.logger.log(
      `Leaving ${this.create.name} with body ${body} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('bulk-insert')
  @HttpCode(201)
  async bulkInsert(
    @Req() req: any,
    @Body(new FieldValidatorPipe())
    body: BulkInsertBeasiswaDTO,
  ) {
    this.logger.log(`Entering ${this.bulkInsert.name} with body ${body}`);

    const response = await this.beasiswaDikumDinasService.bulkInsert(
      req,
      body.beasiswaData,
    );

    this.logger.log(
      `Leaving ${this.bulkInsert.name} with body ${body} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: UpdateBeasiswaDinasDikumDTO,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${body}`,
    );

    const response = await this.beasiswaDikumDinasService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${body} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Patch(':id/upload-file')
  @HttpCode(200)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'surat_perintah', maxCount: 1 },
      { name: 'surat_telegram', maxCount: 1 },
    ]),
  )
  async uploadFile(
    @Req() req: any,
    @Param('id') id: string,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(`Entering ${this.uploadFile.name} with id ${id}`);

    const response = await this.beasiswaDikumDinasService.uploadFile(
      req,
      id,
      files,
    );

    this.logger.log(
      `Leaving ${this.uploadFile.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
