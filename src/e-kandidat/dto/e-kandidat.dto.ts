import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';

export class GetKeywordSuggestionDto {
  @IsString({})
  @IsNotEmpty()
  @MinLength(3)
  @Matches(/^[\w\s\d()]+$/gi, {
    message: 'Query Only accept Alphanumeric value',
  })
  q: string;
}

export class SearchPersonelDto {
  @IsString({})
  @IsNotEmpty()
  @MinLength(3)
  @Matches(/^[\w\s\d-]+$/gi, {
    message: 'Query Only accept Alphanumeric value',
  })
  q: string;

  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @Type(() => Number)
  limit?: number;
}

export class SearchFungsiDto {
  @IsNotEmpty()
  @IsString()
  fungsi: string;
}

export class PersonelRecommendationDto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^[\w\s\d()]+$/gi, {
    message: 'Query Only accept Alphanumeric value',
  })
  q: string;

  @IsString()
  @IsOptional()
  nivel?: string;

  @IsString()
  @IsOptional()
  fungsi?: string;

  @IsString()
  @IsOptional()
  pangkat?: string;

  @IsString()
  @IsOptional()
  polda?: string;
}

export class RecommendationDetailDto {
  @IsOptional()
  @Type(() => Number)
  isKawaka?: number;

  @IsOptional()
  @IsString()
  fungsi?: string;
}
