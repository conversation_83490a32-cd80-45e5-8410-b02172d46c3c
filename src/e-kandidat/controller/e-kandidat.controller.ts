import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  <PERSON><PERSON>,
  Param,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { EKandidatProfileService } from '../service/e-kandidat.profile.service';
import { EKandidateScoreService } from '../service/e-kandidate.score.service';
import { EKandidateHistoryService } from '../service/e-kandidate.history.service';
import {
  GetKeywordSuggestionDto,
  PersonelRecommendationDto,
  RecommendationDetailDto,
  SearchFungsiDto,
  SearchPersonelDto,
} from '../dto/e-kandidat.dto';
import { PaginationDto } from 'src/core/dtos';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { Module, Permission } from '../../core/decorators';
import { MODULES } from '../../core/constants/module.constant';

@Controller('kandidat')
@Module(MODULES.E_CANDIDATE)
@UseGuards(JwtAuthGuard, PermissionGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class EKandidatController {
  private readonly logger = new Logger(EKandidatController.name);

  constructor(
    private readonly profileService: EKandidatProfileService,
    private readonly scoreService: EKandidateScoreService,
    private readonly historyService: EKandidateHistoryService,
  ) {}

  @Get('/recommendation')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getPersonelRecommendations(
    @Req() req: any,
    @Query() queries: PersonelRecommendationDto,
    @Query() paginationData: PaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.getPersonelRecommendations.name} with queries ${JSON.stringify(queries)} and pagination ${JSON.stringify(paginationData)}`,
    );

    const response = await this.profileService.personelRecommendation3(
      req,
      queries,
      paginationData,
    );

    this.logger.log(
      `Leaving ${this.getPersonelRecommendations.name} with queries ${JSON.stringify(queries)} and pagination ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/recommendation/filters')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getRecommendationFilters(
    @Req() req: any,
    @Query() queries: GetKeywordSuggestionDto,
  ) {
    this.logger.log(
      `Entering ${this.getRecommendationFilters.name} with query ${JSON.stringify(queries)}`,
    );

    const response = await this.profileService.getRecommendationFilters(
      req,
      queries.q,
    );

    this.logger.log(
      `Leaving ${this.getRecommendationFilters.name} with query and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/recommendation/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getRecommendationDetail(
    @Req() req: any,
    @Param('id') recommendationId: string,
    @Query() queries: RecommendationDetailDto,
  ) {
    this.logger.log(
      `Entering ${this.getRecommendationDetail.name} with id ${recommendationId} and queries ${JSON.stringify(queries)}`,
    );

    let response;
    if (queries.isKawaka) {
      const fungsi = queries?.fungsi?.split(',');
      response = await this.profileService.personelRecommendationDetailKawaka(
        req,
        +recommendationId,
        fungsi,
      );
    } else {
      response = await this.profileService.personelRecommendationDetail(
        req,
        +recommendationId,
      );
    }

    this.logger.log(
      `Leaving ${this.getRecommendationDetail.name} with id ${recommendationId} and queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/search')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async searchPersonel(@Req() req: any, @Query() queries: SearchPersonelDto) {
    this.logger.log(
      `Entering ${this.searchPersonel.name} with queries ${JSON.stringify(queries)}`,
    );

    const response = await this.profileService.searchPersonel(req, queries);

    this.logger.log(
      `Leaving ${this.searchPersonel.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/suggestion/jabatan')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getJabatanKeywordSuggestion(
    @Req() req: any,
    @Query() queries: GetKeywordSuggestionDto,
  ) {
    this.logger.log(
      `Entering ${this.getJabatanKeywordSuggestion.name} with query ${JSON.stringify(queries)}`,
    );

    const response = await this.profileService.jabatanKeywordSuggestionsV2(
      req,
      queries.q,
    );

    this.logger.log(
      `Leaving ${this.getJabatanKeywordSuggestion.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/suggestion/personel')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getPersonelKeywordSuggestion(
    @Req() req: any,
    @Query() queries: GetKeywordSuggestionDto,
  ) {
    this.logger.log(
      `Entering ${this.getPersonelKeywordSuggestion.name} with query ${JSON.stringify(queries)}`,
    );

    const response = await this.profileService.personelKeywordSuggestions(
      req,
      queries.q,
    );

    this.logger.log(
      `Leaving ${this.getPersonelKeywordSuggestion.name} with query ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  // personel detail section
  @Get('/:personel_id/bio')
  @HttpCode(200)
  async getProfileCandidate(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getProfileCandidate.name} with personel id ${personelUid}`,
    );

    const response = await this.profileService.getProfile(req, personelUid);

    this.logger.log(
      `Leaving ${this.getProfileCandidate.name} with personel id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/scores')
  @HttpCode(200)
  async getAllScore(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllScore.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.scoreService.getAllScore(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllScore.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/dinas')
  @HttpCode(200)
  async getAllDinas(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
    @Query() queries: SearchFungsiDto,
  ) {
    this.logger.log(
      `Entering ${this.getAllDinas.name} with personel_id ${personelUid} and queries ${JSON.stringify(queries)}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    let response;
    if (queries.fungsi.includes('SPECIAL')) {
      const queryParsed = queries.fungsi.split(',');
      queryParsed.shift();
      response = await this.historyService.lamaDinasKawakaComplete(
        req,
        personelId,
        queryParsed,
      );
    } else {
      response = await this.historyService.lamaDinas(
        req,
        personelId,
        queries.fungsi,
      );
    }

    this.logger.log(
      `Leaving ${this.getAllDinas.name} with personel_id ${personelUid} and queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/dikpol')
  @HttpCode(200)
  async getAllDikpol(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllDikpol.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.dikpol(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllDikpol.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/dikbangspes')
  @HttpCode(200)
  async getAlldikbangspes(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAlldikbangspes.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.dikbangspes(req, personelId);

    this.logger.log(
      `Leaving ${this.getAlldikbangspes.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/pangkat')
  @HttpCode(200)
  async getAllPangkat(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllPangkat.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.pangkat(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllPangkat.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/assessment')
  @HttpCode(200)
  async getAllAssessment(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllAssessment.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.assessment(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllAssessment.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/smk')
  @HttpCode(200)
  async getAllSmk(@Req() req: any, @Param('personel_id') personelUid: string) {
    this.logger.log(
      `Entering ${this.getAllSmk.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.smk(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllSmk.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/rewards')
  @HttpCode(200)
  async getAllRewards(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllRewards.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.reward(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllRewards.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/punishments')
  @HttpCode(200)
  async getAllPunishment(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllPunishment.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);
    const response = await this.historyService.punishment(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllPunishment.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/jabatan')
  @HttpCode(200)
  async getAllJabatanHistory(
    @Req() req: any,
    @Param('personel_id') personelUid: string,
  ) {
    this.logger.log(
      `Entering ${this.getAllJabatanHistory.name} with personel_id ${personelUid}`,
    );

    const personelId = await this.profileService.getPersonelId(personelUid);

    const response = await this.historyService.jabatan(req, personelId);

    this.logger.log(
      `Leaving ${this.getAllJabatanHistory.name} with personel_id ${personelUid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
