import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from 'src/core/enums/log.enum';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from 'src/core/interfaces/log.type';

@Injectable()
export class EKandidateHistoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async lamaDinas(req: any, personel_id: number, fungsi?: string) {
    try {
      const dinas = ['MABES', 'POLDA', 'POLRES', 'POLSEK'];
      const filterFungsi = [
        { fungsi },
        { fungsi: { not: 'BELUM TERMAPPING' } },
      ];
      const data = await Promise.all(
        dinas.map(async (tempatDinas) => {
          return await this.prisma.score_lama_dinas_personel.findMany({
            where: {
              personel_id,
              tempat_dinas: tempatDinas,
              AND: filterFungsi,
            },
            select: {
              jabatan: true,
              lama_menjabat_bulan: true,
              score_lama_dinas: true,
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
          });
        }),
      );
      const [mabes, polda, polres, polsek] = data.map((dinas) => {
        let totalScore = 0;
        dinas.forEach((data) => {
          if (data.score_lama_dinas >= totalScore)
            totalScore = data.score_lama_dinas;

          delete data.score_lama_dinas;
        });
        return {
          history: dinas,
          totalScore,
        };
      });

      const queryResult = {
        mabes,
        polda,
        polres,
        polsek,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async lamaDinasKawakaComplete(
    req: any,
    personel_id: number,
    fungsiQueries?: string[],
  ) {
    try {
      const dinas = ['MABES', 'POLDA', 'POLRES', 'POLSEK'];
      const jabatanKawaka = await this._jabatanKawaka();
      const orQueryJabatan = jabatanKawaka.map((jabatan) => ({
        jabatan: { startsWith: jabatan },
      }));
      const query = {
        personel_id,
        OR: [...orQueryJabatan],
      };

      const data = await Promise.all(
        dinas.map(async (tempatDinas) => {
          const finalQuery = Object.assign(query, {
            tempat_dinas: tempatDinas,
          });
          return await this.prisma.score_lama_dinas_personel_kawaka_fungsi.findMany(
            {
              // where: finalQuery,
              where: {
                personel_id,
                tempat_dinas: tempatDinas,
                OR: [...orQueryJabatan, { fungsi: { in: fungsiQueries } }],
              },
              select: {
                jabatan: true,
                lama_menjabat_bulan: true,
                score_lama_dinas: true,
              },
              orderBy: {
                tmt_jabatan: 'desc',
              },
            },
          );
        }),
      );

      const [mabes, polda, polres, polsek] = data.map((dinas) => {
        const dinasScore: Record<string, number> = {};
        const history = dinas.map((data) => {
          const key = `${data.score_lama_dinas}`;
          if (!(key in dinasScore)) {
            dinasScore[key] = data.score_lama_dinas;
          }
          delete data.score_lama_dinas;
          return data;
        });

        const totalScore = Object.values(dinasScore).reduce(
          (final, data) => final + data,
          0,
        );

        return {
          history,
          totalScore,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );

      let queryResult;
      const isDinasAtMabes = mabes.history.length;
      if (isDinasAtMabes) {
        queryResult = {
          mabes,
          polda,
          polres,
        };
      } else {
        queryResult = {
          polda,
          polres,
          polsek,
        };
      }

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async dikpol(req: any, personel_id: number) {
    try {
      const data = await this.prisma.score_rank_tahun_dikpol.findMany({
        where: { personel_id },
        select: {
          diklat_tingkat: true,
          tanggal_selesai: true,
          lama_dikpol_bulan: true,
          ranking: true,
          score_tahun_dikpol: true,
          score_rank_dikpol: true,
        },
        orderBy: {
          tanggal_selesai: 'desc',
        },
      });

      const queryResult = data.map((dt) => {
        return {
          pendidikan: dt.diklat_tingkat,
          tahun_lulus: new Date(dt.tanggal_selesai).getFullYear(),
          lama_setelah_lulus_bulan: dt.lama_dikpol_bulan,
          ranking: dt.ranking || 0,
          skor: dt.score_tahun_dikpol || 0,
          skor_ranking: dt.score_rank_dikpol || 0,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async dikbangspes(req: any, personel_id: number) {
    try {
      const data = await this.prisma.lama_dikbangspes_personel.findMany({
        where: { personel_id },
        select: {
          tanggal_masuk: true,
          tanggal_selesai: true,
          lama_dikbangspes_hari: true,
          dikbangspes: {
            select: {
              nama: true,
            },
          },
          dikbangspes_tingkat: {
            select: {
              nama: true,
            },
          },
        },
        orderBy: {
          tanggal_selesai: 'desc',
        },
      });

      const queryResult = data.map((dt) => ({
        tanggal_masuk: dt.tanggal_masuk,
        tanggal_selesai: dt.tanggal_selesai,
        dikbangspes: dt.dikbangspes.nama,
        tingkat: dt.dikbangspes_tingkat.nama,
        durasi_hari: dt.lama_dikbangspes_hari,
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async pangkat(req: any, personel_id: number) {
    try {
      const pangkat = await this.prisma.lama_mddp_personel.findMany({
        where: {
          personel_id,
          lama_menjabat_bulan: { gt: 0 },
        },
        select: {
          lama_menjabat_bulan: true,
          pangkat: {
            select: { nama: true },
          },
          tmt: true,
        },
        orderBy: {
          tmt: 'desc',
        },
      });

      const rewards = await this.prisma.penghargaan_personel.findMany({
        where: { personel_id },
        select: {
          penghargaan: { select: { nama: true } },
          penghargaan_tingkat: { select: { nama: true } },
          tgl_penghargaan: true,
        },
      });

      const queryResult = pangkat.map((data, idx, arr) => {
        const jabatanStart = new Date(data.tmt);
        const jabatanEnd = idx === 0 ? null : new Date(arr[idx - 1].tmt);
        const achievements = this._assignRewardToPangkat(
          rewards,
          jabatanStart,
          jabatanEnd,
        );

        return {
          pangkat: data.pangkat.nama,
          lama_menjabat_bulan: data.lama_menjabat_bulan,
          jabatan_start: jabatanStart,
          jabatan_end: jabatanEnd,
          achievements,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async assessment(req: any, personel_id: number) {
    try {
      const queryResult = [];

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async smk(req: any, personel_id: number) {
    try {
      const queryResult = await this.prisma.score_nilai_smk_personel.findMany({
        where: { personel_id },
        select: {
          tahun: true,
          semester: true,
          nilai: true,
          score_nilai_smk: true,
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async reward(req: any, personel_id: number) {
    try {
      const rewards = await this.prisma.penghargaan_personel.findMany({
        where: { personel_id },
        select: {
          tgl_penghargaan: true,
          penghargaan: {
            select: { nama: true },
          },
          penghargaan_tingkat: {
            select: { nama: true },
          },
        },
        orderBy: { tgl_penghargaan: 'desc' },
      });

      const queryResult = rewards.map((reward) => ({
        penghargaan: reward.penghargaan.nama,
        tingkat: reward.penghargaan_tingkat.nama,
        tanggal_penghargaan: reward.tgl_penghargaan,
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: rewards,
      };
    } catch (err) {
      throw err;
    }
  }

  async punishment(req: any, personel_id: number) {
    try {
      const queryResult = [];

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async jabatan(req: any, personel_id: number) {
    try {
      const historyJabatan = await this.prisma.jabatan_personel.findMany({
        where: { personel_id },
        select: {
          jabatans: true,
          tmt_jabatan: true,
        },
        orderBy: { tmt_jabatan: 'desc' },
      });

      const queryResult = historyJabatan.map((data, idx, arr) => {
        const jabatanStart = new Date(data.tmt_jabatan);
        const jabatanEnd =
          idx === 0 ? null : new Date(arr[idx - 1].tmt_jabatan);

        const lamaMenjabatBulan = this._monthsDifference(
          jabatanStart,
          jabatanEnd || new Date(),
        );
        return {
          pangkat: data.jabatans.nama,
          lama_menjabat_bulan: lamaMenjabatBulan,
          jabatan_start: jabatanStart,
          jabatan_end: jabatanEnd,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private _assignRewardToPangkat(
    rewards: any[],
    startDate: Date,
    endDate: Date,
  ) {
    try {
      if (rewards.length == 0) return rewards;

      const result = rewards
        .filter((reward) => {
          const rewardDate = new Date(reward.tgl_penghargaan);
          const withinDates = rewardDate >= startDate && rewardDate <= endDate;
          return withinDates;
        })
        .map((reward) => ({
          tipe: reward.penghargaan.nama,
          tingkat: reward.penghargaan_tingkat.nama,
          tanggal: reward.tgl_penghargaan,
        }));

      return result;
    } catch (err) {
      throw err;
    }
  }

  private _monthsDifference(date1: Date, date2: Date) {
    let months: number = 0;
    months = (date2.getFullYear() - date1.getFullYear()) * 12;
    months -= date1.getMonth();
    months += date2.getMonth();
    return months <= 0 ? 0 : months;
  }

  async _jabatanKawaka() {
    try {
      const jabatans = [
        'KAPOLRI',
        'WAKAPOLRI',
        'WAKA POLRI',

        'KAPOLDA',
        'KA POLDA',
        'WAKAPOLDA',
        'WA KAPOLDA',

        'KAPOLRES',
        'KA POLRES',
        'WAKAPOLRES',
        'WAKA POLRES',
        'KAPOLSEK',
        'KA POLSEK',
        'WAKAPOLSEK',
        'WAKA POLSEK',
      ];

      return jabatans;
    } catch (err) {
      throw err;
    }
  }
}
