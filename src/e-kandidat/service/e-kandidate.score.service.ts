import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class EKandidateScoreService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getAllScore(req: any, personel_id: number) {
    if (!personel_id) throw new Error(`personel_id is required`);

    const scores =
      await this.prisma.mv_personel_score_compilation_complete.findFirst({
        where: { personel_id },
      });

    const queryResult = {
      total: scores.total_score,
      mdpp: scores.score_mddp,
      mddn: scores.score_nievelering,
      fungsi: scores.score_lama_dinas,
      tahun_dikpol: scores.score_tahun_dikpol,
      ranking_dikpol: scores.score_rank_dikpol,
      dikbangspes: scores.score_dikbangspes,
      smk: scores.score_nilai_smk,
      assessment: 0,
      reward_punishment: scores.score_penghargaan,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.E_KANDIDAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getMDPP(personel_id: number) {
    try {
      const scores = await this.prisma.score_mddp_personel.findMany({
        where: { personel_id },
        select: { score_mddp: true },
      });

      if (!scores.length) return 0;
      const totalScore = scores.reduce((total, score) => {
        total += score.score_mddp;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getMDDN(personel_id: number) {
    try {
      const scores = await this.prisma.score_nieve_personel.findMany({
        where: { personel_id },
        select: { score_nievelering: true },
      });

      if (!scores.length) return 0;
      const totalScore = scores.reduce((total, score) => {
        total += score.score_nievelering;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getFungsi(personel_id: number) {
    try {
      const dinas = await this.prisma.score_lama_dinas_personel.findMany({
        where: { personel_id },
        select: { score_lama_dinas: true },
      });

      const totalScore = dinas.reduce((total, score) => {
        total += Number(score.score_lama_dinas);
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getScoreLamaDinasKawaka(personel_id: number) {
    try {
      const dinas =
        await this.prisma.score_lama_dinas_personel_kawaka_fungsi.findMany({
          where: { personel_id },
          select: { score_lama_dinas: true, tempat_dinas: true },
        });

      const scoreDictionary = {};
      dinas.forEach((data) => {
        const tempatDinas = data.tempat_dinas;
        if (!(tempatDinas in scoreDictionary)) {
          scoreDictionary[tempatDinas] = data.score_lama_dinas;
        }
      });

      const scores = Object.values(scoreDictionary) as number[];
      const totalScore = scores.reduce(
        (total: number, score: number) => total + score,
      );

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getTahunDikpol(personel_id: number) {
    try {
      const dikpols = await this.prisma.score_rank_tahun_dikpol.findMany({
        where: { personel_id },
        select: {
          score_tahun_dikpol: true,
        },
      });

      const totalScore = dikpols.reduce((total, dikpol) => {
        const score = Number(dikpol.score_tahun_dikpol || 0);
        total += score;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getRangkingDikpol(personel_id: number) {
    try {
      const dikpols = await this.prisma.score_rank_tahun_dikpol.findMany({
        where: { personel_id },
        select: {
          score_rank_dikpol: true,
        },
      });

      const totalScore = dikpols.reduce((total, dikpol) => {
        const score = Number(dikpol.score_rank_dikpol || 0);
        total += score;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getDikbangspes(personel_id: number) {
    try {
      const scores = await this.prisma.score_dikbangspes_personel.findMany({
        where: { personel_id },
        select: { score_dikbangspes: true },
      });

      if (!scores.length) return 0;

      const totalScore = scores.reduce((total, score) => {
        total += score.score_dikbangspes;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getSMK(personel_id: number) {
    try {
      const score = await this.prisma.score_nilai_smk_personel.findFirst({
        where: { personel_id },
        select: { score_nilai_smk: true },
      });

      if (!score) return 0;

      return score.score_nilai_smk;
    } catch (err) {
      throw err;
    }
  }

  async getAssessment(personel_id: number) {
    try {
      return 0;
    } catch (err) {
      throw err;
    }
  }

  async getRewardAndPunishment(personel_id: number) {
    try {
      const scores = await this.prisma.score_penghargaan_personel.findMany({
        where: { personel_id },
        select: { score_penghargaan: true },
      });

      if (!scores.length) return 0;
      const totalScore = scores.reduce((total, score) => {
        total += score.score_penghargaan;
        return total;
      }, 0);

      return totalScore;
    } catch (err) {
      throw err;
    }
  }

  async getThDikpolBulan_AKPOL(personel_id: number) {
    try {
      const dikpol = await this.prisma.score_rank_tahun_dikpol.findMany({
        where: { personel_id, diklat_tingkat: 'AKPOL' },
        select: { lama_dikpol_bulan: true },
      });

      if (!dikpol) return 0;

      const result = dikpol.reduce(
        (total, data) => total + data.lama_dikpol_bulan,
        0,
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  async getThDikpolBulan_ALL(personel_id: number) {
    try {
      const dikpol = await this.prisma.score_rank_tahun_dikpol.findMany({
        where: { personel_id },
        select: { lama_dikpol_bulan: true },
      });

      if (!dikpol) return 0;

      const result = dikpol.reduce(
        (total, data) => total + data.lama_dikpol_bulan,
        0,
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  async getDurasiMDDP(personel_id: number) {
    try {
      const duration = await this.prisma.score_mddp_personel.findFirst({
        where: { personel_id },
        select: { lama_menjabat_bulan: true },
      });

      if (!duration) return 0;
      return duration.lama_menjabat_bulan;
    } catch (err) {
      throw err;
    }
  }

  async getDurasiMDDN(personel_id: number) {
    try {
      const duration = await this.prisma.score_nieve_personel.findFirst({
        where: { personel_id },
        select: { lama_menjabat_bulan: true },
      });
      if (!duration) return 0;
      return duration.lama_menjabat_bulan;
    } catch (err) {
      throw err;
    }
  }

  async getDurasiFungsi(personel_id: number, fungsi?: string) {
    try {
      // const filterFungsi = [
      //   { fungsi },
      //   { fungsi: { not: 'BELUM TERMAPPING' } },
      // ];

      const duration = await this.prisma.score_lama_dinas_personel.findMany({
        where: {
          personel_id,
          AND: [{ fungsi }, { fungsi: { not: 'BELUM TERMAPPING' } }],
          // AND: filterFungsi,
        },
        select: { lama_menjabat_bulan: true },
      });
      if (!duration) return 0;

      const result = duration.reduce(
        (total, data) => total + Number(data.lama_menjabat_bulan),
        0,
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  async getDurasiFungsiKawaka(personel_id: number, fungsi?: string[]) {
    try {
      const dinas = ['POLDA', 'POLRES', 'POLSEK'];
      let additionalMabesQuery = [];
      if (fungsi?.length) {
        additionalMabesQuery.push({
          tempat_dinas: 'MABES',
          fungsi: { in: fungsi },
        });
      }

      const duration =
        await this.prisma.score_lama_dinas_personel_kawaka_fungsi.findMany({
          where: {
            personel_id,
            OR: [{ tempat_dinas: { in: dinas } }, ...additionalMabesQuery],
          },
          select: { lama_menjabat_bulan: true },
        });
      if (!duration) return 0;

      const result = duration.reduce(
        (total, data) => total + Number(data.lama_menjabat_bulan),
        0,
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  async getDurasiDikbangspers(personel_id: number) {
    try {
      const duration = await this.prisma.score_dikbangspes_personel.findMany({
        where: { personel_id },
        select: { lama_dikbangspes_hari: true },
      });

      if (!duration) return 0;
      const result = duration.reduce(
        (total, data) => total + Number(data.lama_dikbangspes_hari),
        0,
      );

      return result;
    } catch (err) {
      throw err;
    }
  }
}
