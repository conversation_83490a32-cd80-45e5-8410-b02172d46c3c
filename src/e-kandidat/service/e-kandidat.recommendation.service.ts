import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import { IRecommendationVariable } from 'src/core/interfaces/e-kandidat.interface';
import {
  CONSTANT_JABATAN_KAWAKA,
  CONSTANT_JABATAN_SPECIAL1,
  CONSTANT_JABATAN_SPECIAL2,
} from '../../core/constants/e-kandidate.constant';

@Injectable()
export class EKandidatRecommendationService {
  constructor(private readonly prisma: PrismaService) {}

  // DEPRECATED!
  // async specialRecommendation(params: IRecommendationVariable) {
  //   try {
  //     const AND_FILTERS = [
  //       { fungsi: { not: 'BELUM TERMAPPING' } },
  //       { nievelering_terkini: { not: null } },
  //       { nievelering_terkini: { in: params.nivels } },
  //       this._queryJabatanRecommendation(params.jabatan),
  //     ];
  //     if (params.pangkat.length) {
  //       AND_FILTERS.push({ pangkat_terkini: { in: params.pangkat } });
  //     }
  //
  //     const topScores =
  //       await this.prisma.mv_personel_score_compilation_complete.groupBy({
  //         by: ['personel_id'],
  //         _max: {
  //           total_score: true,
  //         },
  //         where: {
  //           AND: AND_FILTERS,
  //         },
  //         orderBy: {
  //           _max: {
  //             total_score: 'desc',
  //           },
  //         },
  //         take: params.paginationData.limit,
  //         skip: (params.paginationData.page - 1) * params.paginationData.limit,
  //       });
  //
  //     const filters = topScores.map((score) => {
  //       const str = String(score._max.total_score).split('');
  //       str.pop();
  //       const final = parseFloat(str.join(''));
  //       return {
  //         personel_id: Number(score.personel_id),
  //         total_score: { gte: final },
  //       };
  //     });
  //
  //     const [count, data] = await this.prisma.$transaction([
  //       this.prisma.mv_personel_score_compilation_complete.findMany({
  //         distinct: 'personel_id',
  //         where: { AND: AND_FILTERS },
  //       }),
  //       this.prisma.mv_personel_score_compilation_complete.findMany({
  //         where: { OR: filters },
  //         orderBy: {
  //           total_score: 'desc',
  //         },
  //       }),
  //     ]);
  //
  //     return { totalData: count.length, scores: data };
  //   } catch (err) {
  //     throw err;
  //   }
  // }
  //
  // // DEPRECATED!
  // // Special recommendation 2 get all the data from
  // // table personel_score_compilation_complete_kawaka
  // async specialRecommendation2(params: IRecommendationVariable) {
  //   try {
  //     const AND_FILTERS = [
  //       { nievelering_terkini: { not: null } },
  //       { nievelering_terkini: { in: params.nivels } },
  //       this._queryJabatanRecommendation(params.jabatan),
  //     ];
  //     if (params.pangkat.length) {
  //       AND_FILTERS.push({ pangkat_terkini: { in: params.pangkat } });
  //     }
  //
  //     const [count, data] = await this.prisma.$transaction([
  //       this.prisma.personel_score_compilation_complete_kawaka.count({
  //         where: { AND: AND_FILTERS },
  //       }),
  //       this.prisma.personel_score_compilation_complete_kawaka.findMany({
  //         where: { AND: AND_FILTERS },
  //         orderBy: {
  //           total_score: 'desc',
  //         },
  //         take: params.paginationData.limit,
  //         skip: (params.paginationData.page - 1) * params.paginationData.limit,
  //       }),
  //     ]);
  //
  //     return { totalData: count, scores: data };
  //   } catch (err) {
  //     throw err;
  //   }
  // }
  //
  // async specialRecommendationByMaxLamaDinasValue(
  //   params: IRecommendationVariable,
  // ) {
  //   try {
  //     const AND_FILTERS = [
  //       { nievelering_terkini: { not: null } },
  //       { nievelering_terkini: { in: params.nivels } },
  //       { pangkat_terkini: { in: params.pangkat } },
  //       this._queryJabatanRecommendation(params.jabatan),
  //     ];
  //
  //     let filteredPersonelIds = {};
  //     if (params?.fungsi?.length) {
  //       const tmpQuery = AND_FILTERS.concat([
  //         { fungsi: { in: params.fungsi } },
  //       ]);
  //       const personelWithFungsi =
  //         await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
  //           {
  //             distinct: 'personel_id',
  //             where: { AND: tmpQuery },
  //             select: { personel_id: true },
  //           },
  //         );
  //       personelWithFungsi.forEach(
  //         (personel) =>
  //           (filteredPersonelIds[Number(personel.personel_id)] = true),
  //       );
  //     }
  //
  //     if (params?.polda) {
  //       const tmpQuery = AND_FILTERS.concat([
  //         { lokasi_kerja: { in: params.polda } },
  //       ]);
  //       const personelWithLokasiKerja =
  //         await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
  //           {
  //             distinct: 'personel_id',
  //             where: { AND: tmpQuery },
  //             select: { personel_id: true },
  //           },
  //         );
  //       personelWithLokasiKerja.forEach(
  //         (personel) =>
  //           (filteredPersonelIds[Number(personel.personel_id)] = true),
  //       );
  //     }
  //
  //     const uniquePersonelId = Object.keys(filteredPersonelIds).map(Number);
  //     if (uniquePersonelId.length) {
  //       AND_FILTERS.push({ personel_id: { in: uniquePersonelId } });
  //     }
  //
  //     const topScores =
  //       await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.groupBy(
  //         {
  //           by: ['personel_id'],
  //           _max: {
  //             total_score: true,
  //           },
  //           where: {
  //             AND: AND_FILTERS,
  //           },
  //           orderBy: {
  //             _max: {
  //               total_score: 'desc',
  //             },
  //           },
  //           take: params.paginationData.limit,
  //           skip:
  //             (params.paginationData.page - 1) * params.paginationData.limit,
  //         },
  //       );
  //
  //     // const filters = topScores.map((score) => {
  //     //   const str = String(score._max.total_score).split('');
  //     //   str.pop();
  //     //   const final = parseFloat(str.join(''));
  //     //   return {
  //     //     personel_id: Number(score.personel_id),
  //     //     total_score: { gte: final },
  //     //   };
  //     // });
  //
  //     if (!topScores.length) {
  //       return { totalData: 0, scores: [] };
  //     }
  //     const personelIDs = topScores.map((score) => Number(score.personel_id));
  //     const rawQueryPersonelData: any = Prisma.sql`
  //         WITH temp_ranking as (SELECT *,
  //                                      ROW_NUMBER() OVER (PARTITION BY personel_id ORDER BY total_score DESC) AS row_num
  //                               FROM mv_personel_score_compilation_complete_kawaka_fungsi
  //                               WHERE personel_id IN (${Prisma.join(personelIDs)}))
  //         SELECT *
  //         FROM temp_ranking
  //         WHERE row_num = 1
  //         ORDER BY total_score DESC
  //     `;
  //
  //     const [count, data] = await this.prisma.$transaction([
  //       this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany({
  //         distinct: 'personel_id',
  //         where: { AND: AND_FILTERS },
  //         select: { personel_id: true },
  //       }),
  //       this.prisma.$queryRaw<any[]>(rawQueryPersonelData),
  //       // this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany({
  //       //   where: { OR: filters },
  //       //   orderBy: {
  //       //     total_score: 'desc',
  //       //   },
  //       // }),
  //     ]);
  //
  //     return { totalData: count.length, scores: data };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  async specialRecommendationBySumLamaDinasValues(
    params: IRecommendationVariable,
  ) {
    try {
      const AND_FILTERS = [
        { nievelering_terkini: { not: null } },
        { nievelering_terkini: { in: params.nivels } },
        { pangkat_terkini: { in: params.pangkat } },
        this._queryJabatanRecommendation(params.jabatan),
      ];

      // -- section to handle manual filter from FE
      const filteredPersonelIds = {};
      const filterFungsiExist = params?.fungsi?.length > 0;
      if (filterFungsiExist) {
        const tmpQuery = AND_FILTERS.concat([
          { fungsi: { in: params.fungsi } },
        ]);
        const personelWithFungsi =
          await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
            {
              distinct: 'personel_id',
              where: { AND: tmpQuery },
              select: { personel_id: true },
            },
          );
        personelWithFungsi.forEach(
          (personel) =>
            (filteredPersonelIds[Number(personel.personel_id)] = true),
        );
      }

      if (params?.polda) {
        const tmpQuery = AND_FILTERS.concat([
          { lokasi_kerja: { in: params.polda } },
        ]);
        const personelWithLokasiKerja =
          await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
            {
              distinct: 'personel_id',
              where: { AND: tmpQuery },
              select: { personel_id: true },
            },
          );
        personelWithLokasiKerja.forEach(
          (personel) =>
            (filteredPersonelIds[Number(personel.personel_id)] = true),
        );
      }

      const uniquePersonelIdByFilterPolda =
        Object.keys(filteredPersonelIds).map(Number);
      if (uniquePersonelIdByFilterPolda.length) {
        AND_FILTERS.push({
          personel_id: { in: uniquePersonelIdByFilterPolda },
        });
      }
      const personelIdsByFilterPolda = Prisma.sql`AND personel_id IN (${uniquePersonelIdByFilterPolda.length ? Prisma.join(uniquePersonelIdByFilterPolda) : Prisma.empty})`;
      // -- section to handle manual filter from FE

      // section to handle dikbangum matching
      let personelFilterByDikbangum = [];
      if (params.dikbangum.length) {
        const personelNivelMatch =
          await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
            {
              distinct: 'personel_id',
              where: { AND: AND_FILTERS },
              select: { personel_id: true },
            },
          );

        const personelDikbangum = await this.prisma.dikbangum_personel.findMany(
          {
            distinct: 'personel_id',
            where: {
              dikbangum_id: { in: params.dikbangum },
              personel_id: { in: personelNivelMatch.map((x) => x.personel_id) },
            },
            select: { personel_id: true },
          },
        );
        personelFilterByDikbangum = personelDikbangum.map((x) => x.personel_id);
      }

      if (personelFilterByDikbangum.length) {
        AND_FILTERS.push({ personel_id: { in: personelFilterByDikbangum } });
      }
      const filteredPersonelsByDikbangum = Prisma.sql`AND personel_id IN (${personelFilterByDikbangum.length ? Prisma.join(personelFilterByDikbangum) : Prisma.empty})`;
      // section to handle dikbangum matching

      // --- section lama_mddn requirement
      const timeSpentRequiredToMutate =
        await this._getNivelMutationDurationRequirement(params.nivels[0]);
      const eligiblePersonel =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.groupBy(
          {
            by: ['personel_id'],
            _min: {
              nievelering_terkini: true,
            },
            where: { AND: AND_FILTERS },
            // select: {personel_id}
          },
        );

      if (!eligiblePersonel.length) {
        return { totalData: 0, scores: [] };
      }

      const filteredByMddnDuration = await Promise.all(
        eligiblePersonel.filter(async (personel) => {
          const originNivel = personel._min.nievelering_terkini;

          const minimumRequirement = timeSpentRequiredToMutate[originNivel];
          const lamaMenjabat = await this.prisma.lama_mddn_personel.findFirst({
            where: { personel_id: Number(personel.personel_id) },
            select: { lama_menjabat_bulan: true },
            orderBy: { nivellering: { nama: 'asc' } },
          });

          return lamaMenjabat.lama_menjabat_bulan >= minimumRequirement;
        }),
      );
      const finalPersonelIds = filteredByMddnDuration.map((x) =>
        Number(x.personel_id),
      );
      const filteredByLamaMddn = Prisma.sql`AND personel_id IN (${finalPersonelIds.length ? Prisma.join(finalPersonelIds) : Prisma.empty})`;
      // --- section lama_mddn requirement

      // const _sql = Prisma.sql`
      //   WITH
      //   lama_dinas AS (
      //     SELECT
      //       personel_id,
      //       tempat_dinas,
      //       fungsi,
      //       MAX(score_lama_dinas) as score
      //     FROM score_lama_dinas_personel_kawaka_fungsi
      //     GROUP BY personel_id, tempat_dinas, fungsi
      //   ),
      //   lama_dinas_filtered AS ( ${this._getLamaDinasRawQuery()} ),
      //   personel_ranking AS (
      //     SELECT
      //       personel_id,
      //       MAX(nievelering_terkini) AS nivel_terkini,
      //       -- SUM(DISTINCT(score_lama_dinas)) AS final_score_lama_dinas,
      //       (
      //         score_nilai_smk
      //         -- +SUM(DISTINCT(score_lama_dinas))
      //         +score_nievelering
      //         +score_mddp
      //         +score_tahun_dikpol
      //         +score_rank_dikpol
      //         +score_dikbangspes
      //         +score_penghargaan
      //       ) as total_score
      //     FROM (
      //       SELECT * FROM mv_personel_score_compilation_complete_kawaka_fungsi
      //       WHERE
      //         nievelering_terkini IS NOT null
      //         AND nievelering_terkini IN (${Prisma.join(params.nivels)})
      //         AND pangkat_terkini IN (${Prisma.join(params.pangkat)})
      //         AND ${this._rawQueryJabatanRecommendation(params.jabatan)}
      //         ${uniquePersonelIdByFilters.length ? personelIdsByFiltes : Prisma.empty}
      //         ${personelFilterByDikbangum.length ? filteredPersonelsByDikbangum : Prisma.empty}
      //         ${filteredByLamaMddn}
      //     ) AS comp
      //     GROUP BY
      //       personel_id,
      //       score_nilai_smk,
      //       score_nievelering,
      //       score_mddp,
      //       score_tahun_dikpol,
      //       score_rank_dikpol,
      //       score_dikbangspes,
      //       score_penghargaan
      //   ),
      //   personel_ranking_final AS (
      //     SELECT
      //       pr.personel_id,
      //       pr.nivel_terkini,
      //       dinas.score AS final_score_lama_dinas,
      //       pr.total_score + dinas.score AS total_score
      //     FROM personel_ranking pr
      //     JOIN lama_dinas_filtered dinas ON dinas.personel_id = pr.personel_id
      //   )
      //   SELECT *
      //   FROM personel_ranking_final
      //   ORDER BY total_score DESC
      //   LIMIT ${params.paginationData.limit}
      //   OFFSET ${(params.paginationData.page - 1) * params.paginationData.limit}
      //   `;

      let filterFungsi: Prisma.Sql;
      if (filterFungsiExist) {
        filterFungsi = Prisma.sql` OR ${await this._getDinasRawQuery(params.fungsi)} `;
      } else {
        filterFungsi = Prisma.empty;
      }

      const sql = Prisma.sql`
          WITH lama_dinas AS (SELECT personel_id,
                                     tempat_dinas,
                                     fungsi,
                                     MAX(score_lama_dinas) AS score
                              FROM score_lama_dinas_personel_kawaka_fungsi
                              WHERE (
                                        ${this._jabatanKawaka()}
                                        )
              ${filterFungsi}
          GROUP BY personel_id, tempat_dinas, fungsi
              ),
              lama_dinas_filtered AS (
          SELECT
              personel_id, SUM (score) as score
          FROM lama_dinas
          WHERE
              tempat_dinas IN (
              SELECT DISTINCT (tempat_dinas) as fungsi
              FROM lama_dinas
              ORDER BY fungsi
              LIMIT 3
              )
          GROUP BY personel_id
              ),
              personel_ranking AS (
          SELECT
              personel_id, MAX (nievelering_terkini) AS nivel_terkini,
              -- SUM(DISTINCT(score_lama_dinas)) AS final_score_lama_dinas,
              (
              score_nilai_smk
              -- +SUM(DISTINCT(score_lama_dinas))
              +score_nievelering
              +score_mddp
              +score_tahun_dikpol
              +score_rank_dikpol
              +score_dikbangspes
              +score_penghargaan
              ) as total_score
          FROM (
              SELECT * FROM mv_personel_score_compilation_complete_kawaka_fungsi
              WHERE
              nievelering_terkini IS NOT null
              AND nievelering_terkini IN (${Prisma.join(params.nivels)})
              AND pangkat_terkini IN (${Prisma.join(params.pangkat)})
              AND ${this._rawQueryJabatanRecommendation(params.jabatan)}
              ${uniquePersonelIdByFilterPolda.length ? personelIdsByFilterPolda : Prisma.empty}
              ${personelFilterByDikbangum.length ? filteredPersonelsByDikbangum : Prisma.empty}
              ${filteredByLamaMddn}
              ) AS comp
          GROUP BY
              personel_id,
              score_nilai_smk,
              score_nievelering,
              score_mddp,
              score_tahun_dikpol,
              score_rank_dikpol,
              score_dikbangspes,
              score_penghargaan
              ),
              personel_ranking_final AS (
          SELECT
              pr.personel_id, pr.nivel_terkini, dinas.score AS final_score_lama_dinas, pr.total_score + dinas.score AS total_score
          FROM personel_ranking pr
              JOIN lama_dinas_filtered dinas
          ON dinas.personel_id = pr.personel_id
              )
          SELECT *
          FROM personel_ranking_final
          ORDER BY total_score DESC
              LIMIT ${params.paginationData.limit}
          OFFSET ${(params.paginationData.page - 1) * params.paginationData.limit}
      `;
      const topScores = await this.prisma.$queryRaw<any[]>(sql);
      if (!topScores.length) {
        return { totalData: 0, scores: [] };
      }

      // replace and repopulate personel scores with pre-calculated score lama dinas and total score from topScores
      const data = await Promise.all(
        topScores.map(async (personel) => {
          const personeldata =
            await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findFirst(
              {
                where: { personel_id: personel.personel_id },
                orderBy: { total_score: 'desc' },
                take: 1,
              },
            );
          personeldata.score_lama_dinas = personel.final_score_lama_dinas;
          personeldata.total_score = personel.total_score;
          return personeldata;
        }),
      );

      const count =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
          {
            distinct: 'personel_id',
            where: { AND: AND_FILTERS },
            select: { personel_id: true },
          },
        );

      return { totalData: count.length, scores: data };
    } catch (err) {
      throw err;
    }
  }

  async regularRecommendation(params: IRecommendationVariable) {
    try {
      const AND_FILTERS: Array<any> = [
        { jabatan_terkini: { not: params.jabatan } },
        { fungsi: { not: 'BELUM TERMAPPING' } },
        { fungsi: { in: params.fungsi } },
        { nievelering_terkini: { not: null } },
        { nievelering_terkini: { in: params.nivels } },
      ];

      // filter by defined pangkat from FE
      if (params.pangkat.length) {
        AND_FILTERS.push({ pangkat_terkini: { in: params.pangkat } });
      }

      // filter by dikbangum
      if (params.dikbangum.length) {
        const personelNivels =
          await this.prisma.mv_personel_score_compilation_complete.findMany({
            where: { AND: AND_FILTERS },
            select: { personel_id: true },
          });
        const personelNivelIDs = personelNivels.map((x) =>
          Number(x.personel_id),
        );

        const personelDikbangum = await this.prisma.dikbangum_personel.findMany(
          {
            where: {
              dikbangum_id: { in: params.dikbangum },
              personel_id: { in: personelNivelIDs },
            },
            select: { personel_id: true },
          },
        );

        const personelIdDikbangum = personelDikbangum.map((x) =>
          Number(x.personel_id),
        );
        AND_FILTERS.push({ personel_id: { in: personelIdDikbangum } });
      }

      // filter by lama_mddn
      const timeSpentRequiredToMutate =
        await this._getNivelMutationDurationRequirement(params.nivels[0]);
      const allPersonel =
        await this.prisma.mv_personel_score_compilation_complete.findMany({
          where: { AND: AND_FILTERS },
          select: { nievelering_terkini: true, personel_id: true },
        });
      const filteredByMddnDuration = await Promise.all(
        allPersonel.filter(async (personel) => {
          const originNivel = personel.nievelering_terkini;

          const minimumRequirement = timeSpentRequiredToMutate[originNivel];
          const lamaMenjabat = await this.prisma.lama_mddn_personel.findFirst({
            where: { personel_id: Number(personel.personel_id) },
            select: { lama_menjabat_bulan: true },
            orderBy: { nivellering: { nama: 'asc' } },
          });

          return lamaMenjabat.lama_menjabat_bulan >= minimumRequirement;
        }),
      );
      const finalPersonelIds = filteredByMddnDuration.map((x) => x.personel_id);
      AND_FILTERS.push({ personel_id: { in: finalPersonelIds } });

      const [totalData, scores] = await this.prisma.$transaction([
        this.prisma.mv_personel_score_compilation_complete.count({
          where: {
            AND: AND_FILTERS,
          },
        }),
        this.prisma.mv_personel_score_compilation_complete.findMany({
          where: {
            AND: AND_FILTERS,
          },
          // orderBy: [{ total_score: 'desc' }, { nievelering_terkini: 'asc' }],
          // orderBy: [{ nievelering_terkini: 'asc' }, { total_score: 'desc' }],
          orderBy: [{ total_score: 'desc' }],
          take: params.paginationData.limit,
          skip: (params.paginationData.page - 1) * params.paginationData.limit,
        }),
      ]);

      return { totalData, scores };
    } catch (err) {
      throw err;
    }
  }

  async regularFilterPangkat(params: IRecommendationVariable) {
    try {
      const eligiblePangkat = await this._getNivelPangkatMapping(
        params.nivelQuery,
      );
      const distinctPangkat =
        await this.prisma.mv_personel_score_compilation_complete.findMany({
          distinct: ['pangkat_terkini'],
          select: { pangkat_terkini: true },
          where: {
            AND: [
              { jabatan_terkini: { not: params.jabatan } },
              { fungsi: { not: 'BELUM TERMAPPING' } },
              { fungsi: { in: params.fungsi } },
              { nievelering_terkini: { not: null } },
              { nievelering_terkini: { in: params.nivels } },
              { pangkat_terkini: { in: eligiblePangkat } },
            ],
          },
        });

      const result = distinctPangkat.map((x) => x.pangkat_terkini);
      return result;
    } catch (err) {
      throw err;
    }
  }

  async specialFilterPangkat(params: IRecommendationVariable) {
    try {
      const eligiblePangkat = await this._getNivelPangkatMapping(
        params.nivelQuery,
      );
      const AND_FILTERS = [
        { fungsi: { not: 'BELUM TERMAPPING' } },
        { nievelering_terkini: { not: null } },
        { nievelering_terkini: { in: params.nivels } },
        { jabatan_terkini: { not: params.query.jabatan } },
        { pangkat_terkini: { in: eligiblePangkat } },
        this._queryJabatanRecommendation(params.jabatan),
      ];

      const distinctPangkat =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
          {
            distinct: ['pangkat_terkini'],
            select: { pangkat_terkini: true },
            where: { AND: AND_FILTERS },
          },
        );

      const result = distinctPangkat.map((x) => x.pangkat_terkini);
      return result;
    } catch (err) {
      throw err;
    }
  }

  async specialFilterFungsi(params: IRecommendationVariable) {
    try {
      // const eligiblePangkat = await this._getNivelPangkatMapping(
      //   params.nivelQuery,
      // );
      const AND_FILTERS = [
        { fungsi: { not: 'BELUM TERMAPPING' } },
        { nievelering_terkini: { not: null } },
        { nievelering_terkini: { in: params.nivels } },
        { jabatan_terkini: { not: params.query.jabatan } },
        { pangkat_terkini: { in: params.pangkat } },
        this._queryJabatanRecommendation(params.jabatan),
      ];

      const distinctFungsi =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
          {
            distinct: ['fungsi'],
            select: { fungsi: true },
            where: { AND: AND_FILTERS },
          },
        );

      const result = distinctFungsi.map((x) => x.fungsi);
      result.sort();
      return result;
    } catch (err) {
      throw err;
    }
  }

  async specialFilterLokasiPolda(params: IRecommendationVariable) {
    try {
      // const eligiblePangkat = await this._getNivelPangkatMapping(
      //   params.nivelQuery,
      // );
      const AND_FILTERS = [
        { nievelering_terkini: { not: null } },
        { nievelering_terkini: { in: params.nivels } },
        { jabatan_terkini: { not: params.query.jabatan } },
        { pangkat_terkini: { in: params.pangkat } },
        this._queryJabatanRecommendation(params.jabatan),
      ];

      const distinctPolda =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findMany(
          {
            distinct: ['lokasi_kerja'],
            select: { lokasi_kerja: true },
            where: { AND: AND_FILTERS },
          },
        );

      return distinctPolda
        .map((x) => x.lokasi_kerja)
        .filter((x) => x != 'MABES');
    } catch (err) {
      throw err;
    }
  }

  private async _getNivelPangkatMapping(nivel: string) {
    try {
      const nivelPangkat = await this.prisma.candidate_pangkat_filter.findMany({
        where: {
          nivellering: {
            nama: {
              contains: nivel,
              mode: 'insensitive',
            },
          },
          kategori_id: 2,
        },
        select: { pangkat: true },
      });

      const eligiblePangkat = nivelPangkat.map((x) => x.pangkat.nama);

      return eligiblePangkat;
    } catch (err) {
      throw err;
    }
  }

  isJabatanSpecial(jabatan: string) {
    const title = jabatan.split(' ')[0];
    return title in CONSTANT_JABATAN_SPECIAL1 ? title : '';
  }

  private _queryJabatanRecommendation(jabatan: string) {
    const successors: string[] = this._getJabatanSpecial(jabatan);
    let queries: Record<string, any>;
    if (jabatan === 'WAKAPOLRI') {
      queries = { jabatan_terkini: { in: this._getJabatanSpecial(jabatan) } };
    } else {
      queries = {
        OR: successors.map((jabatan) => ({
          jabatan_terkini: { startsWith: jabatan },
        })),
      };
    }

    return queries;
  }

  private _rawQueryJabatanRecommendation(jabatan: string) {
    const successors: string[] = this._getJabatanSpecial(jabatan);
    let result: any;

    if (jabatan === 'KAPOLRI') {
      result = this._jabatanKawaka();
      return result;
    }

    if (jabatan === 'WAKAPOLRI') {
      result = Prisma.sql`jabatan_terkini IN (${Prisma.join(this._getJabatanSpecial(jabatan))})`;
    } else {
      const conditions = successors.map(
        (jabatan) =>
          Prisma.sql`${Prisma.raw('jabatan_terkini')} LIKE ${jabatan + '%'}`,
      );
      result = Prisma.sql`(${Prisma.join(conditions, ' OR ')})`;
    }
    return result;
  }

  private _getJabatanSpecial(jabatan: string) {
    // return jabatanSpecial[jabatan];
    return CONSTANT_JABATAN_SPECIAL2[jabatan];
  }

  private async _getNivelMutationDurationRequirement(destinationNivel: string) {
    const nivelMutationRequirement =
      await this.prisma.nivellering_mutation_requirements.findMany({
        where: { destination_nivel: destinationNivel },
      });
    const timeSpentRequiredToMutate = nivelMutationRequirement.reduce(
      (final, data) => {
        final[data.origin_nivel] = data.min_duration_month;
        return final;
      },
      {},
    );

    return timeSpentRequiredToMutate;
  }

  // private _getLamaDinasRawQuery() {
  //   try {
  //     const sql = Prisma.sql`
  //         SELECT personel_id,
  //                SUM(score) as score
  //         FROM lama_dinas
  //         WHERE tempat_dinas IN (SELECT DISTINCT(tempat_dinas) as fungsi
  //                                FROM lama_dinas
  //                                ORDER BY fungsi
  //             LIMIT 3
  //             )
  //         GROUP BY personel_id
  //     `;
  //
  //     return sql;
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  private async _getDinasRawQuery(fungsi: string[]) {
    try {
      if (!fungsi.length) {
        return Prisma.empty;
      }

      const conditions = fungsi.map(
        (x) => Prisma.sql`${Prisma.raw('fungsi')} = ${x}`,
      );

      const sql = Prisma.sql`(${Prisma.join(conditions, ' AND ')})`;
      return sql;
    } catch (err) {
      throw err;
    }
  }

  private _jabatanKawaka() {
    const conditions = CONSTANT_JABATAN_KAWAKA.map(
      (jabatan) => Prisma.sql`${Prisma.raw('jabatan')} LIKE ${jabatan + '%'}`,
    );
    const result = Prisma.sql`(${Prisma.join(conditions, ' OR ')})`;

    return result;
  }
}
