import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import * as moment from 'moment';
import { Prisma } from '@prisma/client';
import {
  PersonelRecommendationDto,
  SearchPersonelDto,
} from '../dto/e-kandidat.dto';
import { EKandidateScoreService } from './e-kandidate.score.service';
import { EKandidateHistoryService } from './e-kandidate.history.service';
import { PaginationDto } from 'src/core/dtos';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import { IRecommendationVariable } from '../../core/interfaces/e-kandidat.interface';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { EKandidatRecommendationService } from './e-kandidat.recommendation.service';

@Injectable()
export class EKandidatProfileService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly scoreService: EKandidateScoreService,
    private readonly recommendationService: EKandidatRecommendationService,
    private readonly minioService: MinioService,
    private readonly historyService: EKandidateHistoryService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getProfile(req: any, uid: string) {
    try {
      const dataPersonel = await this.prisma.personel.findFirst({
        where: {
          uid: uid,
          deleted_at: null,
        },
        select: {
          id: true,
          uid: true,
          nama_lengkap: true,
          nrp: true,
          tanggal_lahir: true,
          foto_file: true,
        },
      });

      if (!dataPersonel) {
        throw new NotFoundException(`Data personel tidak ditemukan!`);
      }

      const age = this._calculateAge(new Date(dataPersonel.tanggal_lahir));
      const timeToRetire = this._calculateRetire(
        new Date(dataPersonel.tanggal_lahir),
      );

      const latestData =
        await this.prisma.mv_personel_score_compilation_complete.findFirst({
          where: { personel_id: Number(dataPersonel.id) },
          select: {
            pangkat_terkini: true,
            nievelering_terkini: true,
            jabatan_terkini: true,
          },
        });

      const queryResult = {
        nama: dataPersonel.nama_lengkap,
        nrp: dataPersonel.nrp,
        pangkat: latestData?.pangkat_terkini || '_',
        jabatan: latestData?.jabatan_terkini || '-',
        nivel: latestData?.nievelering_terkini || '-',
        usia: age,
        pensiun: timeToRetire,
        // foto: dataPersonel.foto_file,
        foto: await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${dataPersonel.nrp}/${dataPersonel.foto_file}`,
        ),
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getPersonelId(uid: string) {
    try {
      const personel = await this.prisma.personel.findFirst({
        where: { uid },
        select: { id: true },
      });

      if (!personel) {
        throw new BadRequestException(`Data personel tidak ditemukan`);
      }

      return Number(personel.id);
    } catch (err) {
      throw err;
    }
  }

  // async personelRecommendation(
  //   queries: GetKeywordSuggestionDto,
  //   paginationData: PaginationDto,
  // ) {
  //   try {
  //     const requirements = this._parseQueries(queries.q);
  //     const recommendedNivel = await this._getAssociatedNivels(
  //       requirements.jabatan,
  //     );
  //     const recommendedFungsi = await this._getAssociatedFungsi(
  //       requirements.jabatan,
  //     );
  //
  //     const [totalData, scores] = await this.prisma.$transaction([
  //       this.prisma.mv_personel_score_compilation_complete.count({
  //         where: {
  //           AND: [
  //             { jabatan_terkini: { not: requirements.jabatan } },
  //             { fungsi: { not: 'BELUM TERMAPPING' } },
  //             { fungsi: { in: recommendedFungsi } },
  //             { nievelering_terkini: { not: null } },
  //             { nievelering_terkini: { in: recommendedNivel } },
  //           ],
  //         },
  //       }),
  //       this.prisma.mv_personel_score_compilation_complete.findMany({
  //         where: {
  //           AND: [
  //             { jabatan_terkini: { not: requirements.jabatan } },
  //             { fungsi: { not: 'BELUM TERMAPPING' } },
  //             { fungsi: { in: recommendedFungsi } },
  //             { nievelering_terkini: { not: null } },
  //             { nievelering_terkini: { in: recommendedNivel } },
  //           ],
  //         },
  //         orderBy: [{ total_score: 'desc' }, { nievelering_terkini: 'asc' }],
  //         take: paginationData.limit,
  //         skip: (paginationData.page - 1) * paginationData.limit,
  //       }),
  //     ]);
  //
  //     const personelScores = await Promise.all(
  //       scores.map(async (_scores) => {
  //         const personelId = Number(_scores.personel_id);
  //         const scores = Object.assign(_scores, { personel_id: personelId });
  //         return {
  //           ...scores,
  //           durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
  //           durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
  //           dikpol_bulan: {
  //             akpol: await this.scoreService.getThDikpolBulan_AKPOL(personelId),
  //             all: await this.scoreService.getThDikpolBulan_ALL(personelId),
  //           },
  //           durasiFungsi: await this.scoreService.getDurasiFungsi(
  //             personelId,
  //             _scores.fungsi,
  //           ),
  //           durasiDikbangspers:
  //             await this.scoreService.getDurasiDikbangspers(personelId),
  //         };
  //       }),
  //     );
  //
  //     const result = await this._populatePersonelProfile(personelScores);
  //     return {
  //       recommendation: result,
  //       page: paginationData.page,
  //       totalPage: Math.ceil(totalData / paginationData.limit),
  //       totalData,
  //       query_meta: {
  //         keyword: queries.q,
  //         jabatan: requirements.jabatan,
  //         nivel: recommendedNivel,
  //         fungsi: recommendedFungsi,
  //       },
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }
  //
  // async personelRecommendation2(
  //   queries: PersonelRecommendationDto,
  //   paginationData: PaginationDto,
  // ) {
  //   try {
  //     const requirements = this._parseQueries(queries.q);
  //     let associatedNivels = await this._getAssociatedNivels(
  //       requirements.jabatan,
  //       queries.nivel,
  //     );
  //
  //     let recommendedFungsi = await this._getAssociatedFungsi(
  //       requirements.jabatan,
  //       queries.fungsi,
  //     );
  //
  //     const specialJabatan = this.recommendationService.isJabatanSpecial(
  //       requirements.jabatan,
  //     );
  //
  //     // if (queries.nivel) {
  //     //   associatedNivels = queries.nivel.split(',');
  //     // }
  //
  //     // if (queries.fungsi) {
  //     //   recommendedFungsi = queries.fungsi.split(',');
  //     // }
  //
  //     let recommendations;
  //     const pangkat = queries.pangkat ? queries.pangkat.split(',') : [];
  //     if (specialJabatan) {
  //       recommendations =
  //         await this.recommendationService.specialRecommendation2({
  //           jabatan: specialJabatan,
  //           nivels: associatedNivels,
  //           paginationData,
  //           query: requirements,
  //           pangkat,
  //         });
  //     } else {
  //       recommendations =
  //         await this.recommendationService.regularRecommendation({
  //           jabatan: requirements.jabatan,
  //           nivels: associatedNivels,
  //           fungsi: recommendedFungsi,
  //           paginationData,
  //           pangkat,
  //         });
  //     }
  //
  //     const { totalData, scores } = recommendations;
  //     const personelScores = await Promise.all(
  //       scores.map(async (_scores) => {
  //         const personelId = Number(_scores.personel_id);
  //         const scores = Object.assign(_scores, { personel_id: personelId });
  //         const lamaDinasTemp = scores.score_lama_dinas;
  //         delete scores.score_lama_dinas;
  //         return {
  //           ...scores,
  //           durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
  //           durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
  //           dikpol_bulan: {
  //             akpol: await this.scoreService.getThDikpolBulan_AKPOL(personelId),
  //             all: await this.scoreService.getThDikpolBulan_ALL(personelId),
  //           },
  //           // durasiFungsi: await this.scoreService.getDurasiFungsi(
  //           //   personelId,
  //           //   // _scores.fungsi,
  //           //   specialJabatan ? 'SPECIAL' : recommendedFungsi[0],
  //           // ),
  //           durasiFungsi: specialJabatan
  //             ? await this.scoreService.getDurasiFungsiKawaka(personelId)
  //             : await this.scoreService.getDurasiFungsi(
  //                 personelId,
  //                 recommendedFungsi[0],
  //               ),
  //           score_lama_dinas: specialJabatan
  //             ? await this.scoreService.getScoreLamaDinasKawaka(personelId)
  //             : lamaDinasTemp,
  //           durasiDikbangspers:
  //             await this.scoreService.getDurasiDikbangspers(personelId),
  //         };
  //       }),
  //     );
  //
  //     const result = await this._populatePersonelProfile(personelScores);
  //     return {
  //       recommendation: result,
  //       page: paginationData.page,
  //       totalPage: Math.ceil(totalData / paginationData.limit),
  //       totalData: totalData,
  //       query_meta: {
  //         keyword: queries.q,
  //         jabatan: requirements.jabatan,
  //         nivel: associatedNivels,
  //         fungsi: specialJabatan ? ['SPECIAL'] : recommendedFungsi,
  //       },
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  async personelRecommendation3(
    req: any,
    queries: PersonelRecommendationDto,
    paginationData: PaginationDto,
  ) {
    try {
      const requirements = this._parseQueries(queries.q);
      const [
        associatedNivels,
        recommendedFungsi,
        recommendedPangkat,
        // recommendedDikbangum,
      ] = await Promise.all([
        this._getAssociatedNivels(requirements.jabatan, queries.nivel),
        this._getAssociatedFungsi(requirements.jabatan, queries.fungsi),
        this._getAssociatedPangkat(
          requirements.nivel,
          queries.pangkat,
          requirements.jabatan,
        ),
        // this._getAssociatedDikbangum(requirements.nivel),
      ]);

      const specialJabatan = this.recommendationService.isJabatanSpecial(
        requirements.jabatan,
      );

      let recommendations: Record<string, any>;
      if (specialJabatan) {
        const params: IRecommendationVariable = {
          jabatan: specialJabatan,
          nivels: associatedNivels,
          paginationData,
          query: requirements,
          pangkat: recommendedPangkat,
          dikbangum: [],
        };

        if (queries.fungsi) {
          params['fungsi'] = recommendedFungsi;
        }

        if (queries.polda) {
          params['polda'] = queries.polda.split(',');
        }

        recommendations =
          await this.recommendationService.specialRecommendationBySumLamaDinasValues(
            params,
          );
        // await this.recommendationService.specialRecommendationByMaxLamaDinasValue(
        //   params,
        // );
      } else {
        recommendations =
          await this.recommendationService.regularRecommendation({
            jabatan: requirements.jabatan,
            nivels: associatedNivels,
            fungsi: recommendedFungsi,
            paginationData,
            pangkat: recommendedPangkat,
            dikbangum: [],
          });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );

      const { totalData, scores } = recommendations;
      if (!totalData) {
        const queryResult = {
          recommendation: [],
          page: paginationData.page,
          totalPage: 1,
          totalData: totalData,
          query_meta: {
            keyword: queries.q,
            jabatan: requirements.jabatan,
            nivel: associatedNivels,
            fungsi: specialJabatan ? ['SPECIAL'] : recommendedFungsi,
          },
        };

        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        };
      }

      const personelScores = await Promise.all(
        scores.map(async (_scores) => {
          const personelId = Number(_scores.personel_id);
          const scores = Object.assign(_scores, { personel_id: personelId });
          const kawakaFilterFungsi =
            specialJabatan && queries?.fungsi ? recommendedFungsi : undefined;

          const { durasiDinas } = await this._getLamaDinasDetails(
            req,
            personelId,
            recommendedFungsi,
          );

          return {
            ...scores,
            // additional data below!
            durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
            durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
            dikpol_bulan: {
              akpol: await this.scoreService.getThDikpolBulan_AKPOL(personelId),
              all: await this.scoreService.getThDikpolBulan_ALL(personelId),
            },
            durasiFungsi: specialJabatan
              ? durasiDinas
              : // await this.scoreService.getDurasiFungsiKawaka(
                //     personelId,
                //     kawakaFilterFungsi,
                //   )
                await this.scoreService.getDurasiFungsi(
                  personelId,
                  recommendedFungsi[0],
                ),
            // score_lama_dinas: specialJabatan
            //   ? await this.scoreService.getScoreLamaDinasKawaka(personelId)
            //   : lamaDinasTemp,
            durasiDikbangspers:
              await this.scoreService.getDurasiDikbangspers(personelId),
          };
        }),
      );

      const result = await this._BulkPopulatePersonelProfile(personelScores);
      const queryResult = {
        recommendation: result,
        page: paginationData.page,
        totalPage: Math.ceil(totalData / paginationData.limit),
        totalData: totalData,
        query_meta: {
          keyword: queries.q,
          jabatan: requirements.jabatan,
          nivel: associatedNivels,
          fungsi: specialJabatan ? ['SPECIAL'] : recommendedFungsi,
        },
      };

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async personelRecommendationDetail(req: any, id: number) {
    try {
      const scoresTemp =
        await this.prisma.mv_personel_score_compilation_complete.findFirst({
          where: {
            id,
          },
        });

      const personelScores = await Promise.all(
        [scoresTemp].map(async (_scores) => {
          const personelId = Number(_scores.personel_id);
          const scores = Object.assign(_scores, { personel_id: personelId });
          return {
            ...scores,
            durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
            durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
            dikpol_bulan: {
              akpol: await this.scoreService.getThDikpolBulan_AKPOL(personelId),
              all: await this.scoreService.getThDikpolBulan_ALL(personelId),
            },
            durasiFungsi: await this.scoreService.getDurasiFungsi(
              personelId,
              scoresTemp.fungsi,
            ),
            durasiDikbangspers:
              await this.scoreService.getDurasiDikbangspers(personelId),
          };
        }),
      );

      // const result = await this._populatePersonelProfile(personelScores);
      const queryResult =
        await this._BulkPopulatePersonelProfile(personelScores);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async personelRecommendationDetailKawaka(
    req: any,
    id: number,
    fungsi: string[],
  ) {
    try {
      const scoresTemp =
        await this.prisma.mv_personel_score_compilation_complete_kawaka_fungsi.findFirst(
          {
            where: {
              id,
            },
          },
        );

      const personelId = Number(scoresTemp.personel_id);
      const { durasiDinas, skorDinas } = await this._getLamaDinasDetails(
        req,
        personelId,
        fungsi,
      );
      const scores = Object.assign(scoresTemp, { personel_id: personelId });
      const newTotalScore =
        scores.total_score - scores.score_lama_dinas + skorDinas;

      const personelResult = {
        ...scores,
        durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
        durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
        dikpol_bulan: {
          akpol: await this.scoreService.getThDikpolBulan_AKPOL(personelId),
          all: await this.scoreService.getThDikpolBulan_ALL(personelId),
        },
        durasiFungsi: durasiDinas,
        score_lama_dinas: skorDinas,
        total_score: newTotalScore,
        durasiDikbangspers:
          await this.scoreService.getDurasiDikbangspers(personelId),
      };

      const queryResult = await this._BulkPopulatePersonelProfile([
        personelResult,
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async searchPersonel(req: any, queries: SearchPersonelDto) {
    try {
      // const scorestemp = await this.prisma.personel_score_compilation.findMany({
      //   where: {
      //     OR: [
      //       {
      //         nama_lengkap: queries.q,
      //       },
      //       {
      //         nrp: queries.q,
      //       },
      //       {
      //         pangkat_terkini: queries.q,
      //       },
      //     ],
      //   },
      //   select: {
      //     personel_id: true,
      //     total_score: true,
      //     score_mddp: true,
      //     score_nievelering: true,
      //     score_lama_dinas: true,
      //     score_penghargaan: true,
      //     score_nilai_smk: true,
      //     score_dikbangspes: true,
      //   },
      //   take: 10,
      //   orderBy: { total_score: 'desc' },
      // });

      const { page, limit } = queries;
      const offset = (page - 1) * limit;
      const query = Prisma.sql`
          WITH LatestTmtJabatan AS (SELECT personel_id,
                                           MAX(tmt_jabatan) AS max_tmt_jabatan
                                    FROM mv_personel_score_compilation_complete
                                    GROUP BY personel_id)
          SELECT p1.personel_id,
                 p1.total_score,
                 p1.score_mddp,
                 p1.score_nievelering,
                 p1.score_lama_dinas,
                 p1.score_penghargaan,
                 p1.score_nilai_smk,
                 p1.score_dikbangspes
              p1.score_tahun_dikpol
          FROM (SELECT *
                from mv_personel_score_compilation_complete
                WHERE nievelering_terkini IS NOT null
                  AND (
                    pangkat_terkini = ${queries.q}
                        OR nama_lengkap = ${queries.q}
                        OR nrp = ${queries.q}
                    )) p1
                   INNER JOIN LatestTmtJabatan ltj
                              ON p1.personel_id = ltj.personel_id AND p1.tmt_jabatan = ltj.max_tmt_jabatan
          ORDER BY p1.nama_lengkap
              LIMIT ${limit}
          OFFSET ${offset}
      `;
      const scoresTemp = await this.prisma.$queryRaw<any[]>(query);
      const personelScores = await Promise.all(
        scoresTemp.map(async (_scores) => {
          const personelId = Number(_scores.personel_id);
          const scores = Object.assign(_scores, { personel_id: personelId });
          return {
            ...scores,
            personel_id: Number(scores.personel_id),
            // dikpol_bulan: await this.scoreService._getThDikpol(personelId),
            durasiMDDPBulan: await this.scoreService.getDurasiMDDP(personelId),
            durasiMDDNBulan: await this.scoreService.getDurasiMDDN(personelId),
            durasiFungsi: await this.scoreService.getDurasiFungsi(personelId),
            durasiDikbangspers:
              await this.scoreService.getDurasiDikbangspers(personelId),
          };
        }),
      );

      const queryResult = await this._populatePersonelProfile(
        req,
        personelScores,
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  // async jabatanKeywordSuggestions(keyword: string) {
  //   try {
  //     const uppercase = keyword.toUpperCase();
  //
  //     const query = Prisma.sql`
  //         SELECT j.nama as jabatan, n.nama as nivel
  //         FROM (SELECT nama, nivellering_id
  //               FROM "jabatan"
  //               WHERE nivellering_id IS NOT null
  //                 AND atasan_id IS NOT null
  //                 AND (nama >= ${uppercase} AND nama <= ${uppercase + 'ÿ'})) AS j
  //                  JOIN "nivellering" n on j.nivellering_id = n.id LIMIT 10`;
  //
  //     // const query = Prisma.sql`
  //     //   SELECT CONCAT(jabatan_terkini,'-', fungsi,'-', tempat_dinas,'-', nievelering_terkini) as title
  //     //   FROM "mv_personel_score_compilation_complete"
  //     //   WHERE
  //     //     nievelering_terkini IS NOT null
  //     //     AND fungsi != 'BELUM TERMAPPING'
  //     //     AND jabatan_terkini BETWEEN ${uppercase} AND ${uppercase + 'ÿ'}
  //     //   GROUP BY jabatan_terkini, nievelering_terkini, fungsi, tempat_dinas
  //     //   LIMIT 10`;
  //     const suggestions = await this.prisma.$queryRaw<any[]>(query);
  //     const result = suggestions.map((x) => `${x.jabatan} (${x.nivel})`);
  //
  //     return result;
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  // this version support kapolri recommendation
  async jabatanKeywordSuggestionsV2(req: any, keyword: string) {
    try {
      const uppercase = keyword.toUpperCase();

      const query = Prisma.sql`
          SELECT j.nama as jabatan,
                 n.nama as nivel
          FROM (SELECT nama, nivellering_id
                FROM jabatan
                WHERE (nama >= ${uppercase} AND nama <= ${uppercase + 'ÿ'})
                  AND (
                    CASE
                        WHEN nama = 'KAPOLRI' THEN TRUE
                        ELSE nivellering_id IS NOT NULL AND atasan_id IS NOT NULL
                        END
                    )) AS j
                   LEFT JOIN "nivellering" n on j.nivellering_id = n.id LIMIT 10
      `;

      const suggestions =
        await this.prisma.$queryRaw<{ jabatan: string; nivel: string }[]>(
          query,
        );

      const queryResult = suggestions.map(({ jabatan, nivel }) => {
        if (!nivel) return jabatan;

        return `${jabatan} (${nivel})`;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async personelKeywordSuggestions(req: any, keyword: string) {
    try {
      const lowercase = keyword.toLowerCase();
      const uppercase = keyword.toUpperCase();
      const lowercaseUpperbound = lowercase + 'ÿ';
      const uppercaseUpperbound = uppercase + 'ÿ';

      const suggestions =
        await this.prisma.mv_personel_score_compilation_complete.findMany({
          where: {
            OR: [
              { nama_lengkap: { gte: lowercase, lte: lowercaseUpperbound } },
              { nama_lengkap: { gte: uppercase, lte: uppercaseUpperbound } },
              { nrp: { gte: keyword, lte: keyword + 'ÿ' } },
              { pangkat_terkini: { gte: lowercase, lte: lowercaseUpperbound } },
              { pangkat_terkini: { gte: uppercase, lte: uppercaseUpperbound } },
            ],
          },
          select: { nama_lengkap: true, nrp: true, pangkat_terkini: true },
          take: 10,
        });

      if (!suggestions.length) {
        return suggestions;
      }

      let selectedColumn: string;
      for (const [colName, colValue] of Object.entries(suggestions[0])) {
        if (colValue.startsWith(uppercase)) {
          selectedColumn = colName;
          break;
        }
      }

      const queryResult = suggestions.map((x) => x[selectedColumn]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getRecommendationFilters(req: any, query: string) {
    try {
      const requirements = this._parseQueries(query);
      let [recommendedFungsi, associatedNivels, associatedPangkat] =
        await Promise.all([
          this._getAssociatedFungsi(requirements.jabatan),
          this._getAssociatedNivels(requirements.jabatan),
          this._getAssociatedPangkat(
            requirements.nivel,
            '',
            requirements.jabatan,
          ),
        ]);
      const specialJabatan = this.recommendationService.isJabatanSpecial(
        requirements.jabatan,
      );

      //
      // let pangkat: string[];
      let lokasiPolda: string[] = [];
      if (specialJabatan) {
        recommendedFungsi =
          await this.recommendationService.specialFilterFungsi({
            jabatan: specialJabatan,
            nivels: associatedNivels,
            query: requirements,
            pangkat: associatedPangkat,
          });

        // pangkat = await this.recommendationService.specialFilterPangkat({
        //   jabatan: specialJabatan,
        //   nivels: associatedNivels,
        //   query: requirements,
        //   nivelQuery: requirements.nivel,
        // });

        lokasiPolda = await this.recommendationService.specialFilterLokasiPolda(
          {
            jabatan: specialJabatan,
            nivels: associatedNivels,
            query: requirements,
            pangkat: associatedPangkat,
          },
        );
      } else {
        // pangkat = await this.recommendationService.regularFilterPangkat({
        //   jabatan: requirements.jabatan,
        //   nivels: associatedNivels,
        //   fungsi: recommendedFungsi,
        //   nivelQuery: requirements.nivel,
        // });
      }

      const queryResult = {
        nivel: associatedNivels,
        fungsi: await this._sortRecommendationFungsiByKode(recommendedFungsi),
        pangkat: associatedPangkat,
        polda: this._sortPoldaByName(lokasiPolda),
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.E_KANDIDAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KANDIDAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _populatePersonelProfile(
    req: any,
    personelScores: Record<string, any>[],
  ) {
    try {
      // populate score with personel's profile
      const result = await Promise.all(
        personelScores.map(async (personelScore) => {
          const personelUID = await this.prisma.personel.findFirst({
            where: { id: personelScore.personel_id },
            select: { uid: true },
          });

          const profile = await this.getProfile(req, personelUID.uid);

          return {
            id: personelScore.id,
            profile: {
              personel_id: personelScore.personel_id,
              personel_uid: personelUID.uid,
              nama: profile.data.nama,
              pangkat: profile.data.pangkat,
              jabatan: profile.data.jabatan,
              nivel: profile.data.nivel,
              nrp: profile.data.nrp,
              foto: profile.data.foto,
            },
            scores: {
              total: personelScore.total_score,
              mddp: {
                durasi: personelScore.durasiMDDPBulan,
                score: personelScore.score_mddp,
              },
              mddn: {
                durasi_bulan: personelScore.durasiMDDNBulan,
                score: personelScore.score_nievelering,
              },
              fungsi: {
                durasi_bulan: personelScore.durasiFungsi,
                score: personelScore.score_lama_dinas,
              },
              dikbangspes: {
                durasi_hari: personelScore.durasiDikbangspers,
                score: personelScore.score_dikbangspes,
              },
              th_dikpol: {
                durasi_bulan: 0,
                score: personelScore.score_tahun_dikpol,
              },
              // th_dikpol: personelScore.score_tahun_dikpol,
              // dikpol_bulan: 0,
              // {
              //   akpol: personelScore.dikpol_bulan.akpol,
              //   all: personelScore.dikpol_bulan.all,
              // },
              ranking: personelScore.score_rank_dikpol,
              reward: personelScore.score_penghargaan,
              assessment: 0,
              punishment: 0,
              smk: personelScore.score_nilai_smk,
            },
          };
        }),
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  private async _BulkPopulatePersonelProfile(
    personelScores: Record<string, any>[],
  ) {
    try {
      const personelIDs = personelScores.map((x) => Number(x.personel_id));
      const personelData = await this.prisma.personel.findMany({
        where: { id: { in: personelIDs } },
        select: {
          id: true,
          uid: true,
          nama_lengkap: true,
          nrp: true,
          tanggal_lahir: true,
          foto_file: true,
        },
      });
      const personelDataDict: Record<string, any> = personelData.reduce(
        (final, data) => {
          const personelId = Number(data.id);
          final[personelId] = data;
          return final;
        },
        {},
      );
      const rawQueryPersonelData: any = Prisma.sql`
          WITH temp_ranking as (SELECT personel_id,
                                       pangkat_terkini,
                                       jabatan_terkini,
                                       nievelering_terkini,
                                       pangkat_terkini_singkat,
                                       ROW_NUMBER() OVER (PARTITION BY personel_id ORDER BY total_score DESC) AS row_num
                                FROM mv_personel_score_compilation_complete_kawaka_fungsi
                                WHERE personel_id IN (${Prisma.join(personelIDs)}))
          SELECT *
          FROM temp_ranking
          WHERE row_num = 1
      `;

      const personelLatestData =
        await this.prisma.$queryRaw<any[]>(rawQueryPersonelData);

      const personelLatestDataDict = personelLatestData.reduce(
        (final, data) => {
          const personelId = Number(data.personel_id);
          final[personelId] = {
            pangkat_terkini: data.pangkat_terkini,
            jabatan_terkini: data.jabatan_terkini,
            nievelering_terkini: data.nievelering_terkini,
          };
          return final;
        },
        {},
      );

      const result = await Promise.all(
        personelScores.map(async (personel) => {
          const personelId = personel.personel_id;
          const foto = await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${personelDataDict[personelId].nrp}/${personelDataDict[personelId].foto_file}`,
          );

          return {
            id: personel.id,
            profile: {
              personel_id: personel.personel_id,
              personel_uid: personelDataDict[personelId].uid,
              nama: personelDataDict[personelId].nama_lengkap,
              pangkat: personel.pangkat_terkini_singkat,
              jabatan: personel.jabatan_terkini,
              nivel: personel.nievelering_terkini,
              nrp: personelDataDict[personelId].nrp,
              foto, // Resolved from await
            },
            scores: {
              total: personel.total_score,
              mddp: {
                durasi: personel.durasiMDDPBulan,
                score: personel.score_mddp,
              },
              mddn: {
                durasi_bulan: personel.durasiMDDNBulan,
                score: personel.score_nievelering,
              },
              fungsi: {
                durasi_bulan: personel.durasiFungsi,
                score: personel.score_lama_dinas,
              },
              dikbangspes: {
                durasi_hari: personel.durasiDikbangspers,
                score: personel.score_dikbangspes,
              },
              th_dikpol: {
                durasi_bulan: personel.dikpol_bulan.akpol,
                score: personel.score_tahun_dikpol,
              },
              ranking: personel.score_rank_dikpol,
              reward: personel.score_penghargaan,
              assessment: 0,
              punishment: 0,
              smk: personel.score_nilai_smk,
            },
          };
        }),
      );

      return result;
    } catch (err) {
      throw err;
    }
  }

  private _parseQueries(query: string) {
    if (query === 'KAPOLRI') {
      return {
        jabatan: 'KAPOLRI',
        nivel: 'IA',
      };
    }

    const splitted = query.split(' ');
    let nivel = splitted.pop();
    nivel = nivel.replace(/[()]/g, '');
    const jabatan = splitted.join(' ');
    return {
      jabatan,
      nivel,
    };
  }

  private async _getAssociatedNivels(jabatan: string, nivelQuery?: string) {
    try {
      if (jabatan === 'KAPOLRI') {
        return ['IA'];
      }

      if (nivelQuery) {
        return nivelQuery.split(',');
      }

      // get nivel by jabatan name
      const jabatanNivel = await this.prisma.jabatan.findFirst({
        where: { nama: jabatan },
        select: { nivellering_id: true, nama: true },
      });

      if (!jabatanNivel) {
        return [];
      }

      // get assosiated nivel for recommendation
      const nivels = await this.prisma.nivellering.findFirst({
        where: { id: jabatanNivel.nivellering_id },
        select: {
          nama: true,
          nivellering_down: { select: { nama: true } },
        },
      });

      if (!nivels) {
        return [];
      }

      const recommendationNivel = [nivels.nama];
      if (nivels?.nivellering_down?.nama) {
        recommendationNivel.push(nivels.nivellering_down.nama);
      }

      return recommendationNivel;
    } catch (err) {
      throw err;
    }
  }

  private async _getAssociatedFungsi(jabatan: string, fungsiQuery?: string) {
    try {
      if (fungsiQuery) {
        return fungsiQuery.split(',');
      }

      const jbtn = await this.prisma.jabatan.findFirst({
        where: { nama: jabatan },
        select: {
          satuan_id: true,
        },
      });

      const fungsiSatuan = await this.prisma.fungsi_satuan.findMany({
        where: { satuan_id: jbtn.satuan_id },
        select: { fungsi_id: true },
      });

      const uniqueFungsiID = Array.from(
        new Set(fungsiSatuan.map((x) => Number(x.fungsi_id))),
      );

      const fungsi = await this.prisma.fungsi.findMany({
        where: { id: { in: uniqueFungsiID } },
        select: {
          nama: true,
        },
      });

      const result = fungsi.map((x) => x.nama);
      return result;
    } catch (err) {
      throw err;
    }
  }

  private _calculateAge(date: Date) {
    const birthday = moment(date);
    const now = moment(new Date());

    const yearDiff = now.diff(birthday, 'year');
    birthday.add(yearDiff, 'years');

    const monthDiff = now.diff(birthday, 'months');
    birthday.add(monthDiff, 'months');

    const day = now.diff(birthday, 'days');

    return {
      year: yearDiff,
      month: monthDiff,
      day,
    };
  }

  private _calculateRetire(birthday: Date) {
    const retirementAge = 51;

    const retiredAge = new Date(birthday).setFullYear(
      new Date(birthday).getFullYear() + retirementAge,
    );

    const retired = moment(retiredAge);
    const now = moment(new Date());

    const yearDiff = retired.diff(now, 'year');
    now.add(yearDiff, 'years');

    const monthDiff = retired.diff(now, 'months');
    now.add(monthDiff, 'months');

    const dayDiff = retired.diff(now, 'days');

    return {
      year: yearDiff,
      month: monthDiff,
      day: dayDiff,
    };
  }

  private async _getAssociatedPangkat(
    nivel: string,
    queryPangkat: string,
    jabatan?: string,
  ) {
    try {
      if (jabatan === 'KAPOLRI') {
        return ['KOMISARIS JENDERAL POLISI'];
      }

      // if filter pangkat is applied from FE, return immediately
      if (queryPangkat) {
        return queryPangkat.split(',');
      }

      const pangkatNivel = await this.prisma.candidate_pangkat_filter.findMany({
        where: {
          nivellering: {
            nama: {
              contains: nivel,
              mode: 'insensitive',
            },
          },
          kategori_id: 2,
        },
        select: { pangkat: true, pangkat_id: true },
        orderBy: { pangkat_id: 'desc' },
      });

      // translate jabatan to pangkat
      const selectedJabatans = await this.prisma.jabatan_personel.findMany({
        distinct: ['jabatan_id'],
        where: {
          jabatans: { nama: { startsWith: jabatan } },
          jabatan_id: { not: null },
        },
        select: { jabatan_id: true },
      });

      if (!selectedJabatans?.length) return [];

      const [{ pangkat_id: selectedPangkat }] =
        await this.prisma.jabatan_pangkat.findMany({
          distinct: ['pangkat_id'],
          where: {
            jabatan_id: { in: selectedJabatans.map((x) => x.jabatan_id) },
          },
          select: { pangkat_id: true },
        });

      // if selectedPangkat only matches with the lower tier pangkat from pangkatNivel,
      // then remove the top rank.
      // if pangkat matches with the top tier pangkat, do nothing
      const idx = 0;
      while (idx < Object.keys(pangkatNivel).length) {
        const pangkatMatch = pangkatNivel[idx].pangkat_id == selectedPangkat;
        // take current pangkat at index and the rest of the element
        // and then break immediately
        if (pangkatMatch) {
          break;
        }
        pangkatNivel.shift();
      }

      const result = pangkatNivel.map((data) => data.pangkat.nama);
      return result;
    } catch (err) {
      throw err;
    }
  }

  private async _sortRecommendationFungsiByKode(fungsi: string[]) {
    try {
      const referenceFungsi = await this.prisma.fungsi.findMany({
        where: {
          // kode: { not: null },
          nama: { not: 'POLRI' },
        },
        select: {
          nama: true,
        },
        orderBy: { kode: 'desc' },
      });
      const referenceFungsiSorted = referenceFungsi.map((x) => x.nama);

      fungsi.sort((a, b) => {
        return (
          referenceFungsiSorted.indexOf(b) - referenceFungsiSorted.indexOf(a)
        );
      });

      return fungsi;
    } catch (err) {
      throw err;
    }
  }

  private _sortPoldaByName(polda: string[]) {
    polda.sort();
    return polda;
  }

  private async _getLamaDinasDetails(
    req: any,
    personelId: number,
    fungsi: string[],
  ) {
    try {
      const dataDinas = await this.historyService.lamaDinasKawakaComplete(
        req,
        Number(personelId),
        fungsi,
      );

      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      const [newLamaDinas, newScoreLamaDinas] = Object.values(
        dataDinas?.data,
      ).reduce(
        (final, { history, totalScore }) => {
          const lamaDinas = history.reduce((total, { lama_menjabat_bulan }) => {
            total += Number(lama_menjabat_bulan);
            return total;
          }, 0);
          final[0] += lamaDinas;
          final[1] += totalScore;
          return final;
        },
        [0, 0],
      );

      return {
        durasiDinas: newLamaDinas,
        skorDinas: newScoreLamaDinas,
      };
    } catch (err) {
      throw err;
    }
  }
}
