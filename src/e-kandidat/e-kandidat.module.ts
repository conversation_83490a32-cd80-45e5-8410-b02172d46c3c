import { Modu<PERSON> } from '@nestjs/common';
import { EKandidatProfileService } from './service/e-kandidat.profile.service';
import { EKandidatController } from './controller/e-kandidat.controller';
import { EKandidateScoreService } from './service/e-kandidate.score.service';
import { EKandidateHistoryService } from './service/e-kandidate.history.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';
import { EKandidatRecommendationService } from './service/e-kandidat.recommendation.service';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [EKandidatController],
  providers: [
    EKandidatProfileService,
    EKandidateScoreService,
    EKandidateHistoryService,
    EKandidatRecommendationService,
    MinioService,
  ],
})
export class EKandidatModule {}
