import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ValidasiSiswaDto } from '../dto/siswa.dto';
import { chunkedUpdate } from '../../../core/utils/common.utils';
import { IDocumentRules } from '../../../core/interfaces/siswa.interface';
import { validateFile } from '../../../core/utils/validation.utils';
import { MinioService } from '../../../api-utils/minio/service/minio.service';

@Injectable()
export class SiswaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minio: MinioService,
  ) {}

  private sectionMappings: Record<string, string[]> = {
    identify: ['id'],
    data_klasifikasi_siswa_diktuk: [
      'username',
      'nama',
      'nama_panggilan',
      'gelar',
      'suku',
      'agama',
      'jenis_kelamin',
      'tempat_lahir',
      'tanggal_lahir',
      'nrp',
      'nik',
      'no_ujian_polda',
      'no_ak_nosis',
      'jenis_diktuk',
      'kompetensi_diktuk',
      'sub_kompetensi_diktuk',
      'sub_sub_kompetensi_diktuk',
      'ket_jalur_rekpro',
      'orang_asli_papua',
      'tempat_pendidikan',
      'asal_rim_polda',
      'asal_rim_polres',
    ],
    data_pendidikan_umum: ['dikum'],
    ijazah_yang_digunakan_saat_mendaftar_sebagai_calon_anggota_polri: [
      'ijazah_dikum_gun_seleksi_rim',
    ],
    data_genetika: ['genetik', 'hobi'],
    data_keluarga: ['keluarga'],
    data_kemampuan: ['keahlian'],
    data_domisili: ['alamat'],
  };

  private documentRules: Record<string, IDocumentRules> = {
    pas_photo_rim: {
      title: 'PAS FOTO RIM',
      isRequired: true,
      mimeType: ['jpg', 'jpeg', 'png'],
    },
    pas_photo_diktuk: {
      title: 'PAS FOTO DIKTUK',
      isRequired: true,
      mimeType: ['jpg', 'jpeg', 'png'],
    },
    ktp: { title: 'KTP', isRequired: false, mimeType: ['pdf'] },
    kk: { title: 'KK', isRequired: false, mimeType: ['pdf'] },
    akte_lahir: { title: 'AKTE LAHIR', isRequired: false, mimeType: ['pdf'] },
    sim_c: { title: 'SIM C', isRequired: false, mimeType: ['pdf'] },
    ijazah_sd: { title: 'IJAZAH SD', isRequired: false, mimeType: ['pdf'] },
    ijazah_smp: { title: 'IJAZAH SMP', isRequired: false, mimeType: ['pdf'] },
    ijazah_sma: { title: 'IJAZAH SMA', isRequired: false, mimeType: ['pdf'] },
    ijazah_d3: { title: 'IJAZAH D3', isRequired: false, mimeType: ['pdf'] },
    ijazah_d4_s1: {
      title: 'IJAZAH D4/S1',
      isRequired: false,
      mimeType: ['pdf'],
    },
    ijazah_s2_spes: {
      title: 'IJAZAH S2/SPES',
      isRequired: false,
      mimeType: ['pdf'],
    },
  };

  async dataValidation(req: any) {
    const { user } = req;

    const siswa = await this.prisma.data_siswa.findFirst({
      where: { unique_id: user.unique_id, deleted_at: null },
      select: {
        id: true,
        username: true,
        nama: true,
        nama_panggilan: true,
        gelar: { select: { id: true, nama: true } },
        jenis_kelamin: true,
        tempat_lahir: true,
        tanggal_lahir: true,
        nrp: true,
        nik: true,
        no_ujian_polda: true,
        no_ak_nosis: true,
        jenis_diktuk: { select: { id: true, nama: true } },
        kompetensi_diktuk: { select: { id: true, nama: true } },
        sub_kompetensi_diktuk: { select: { id: true, nama: true } },
        sub_sub_kompetensi_diktuk: { select: { id: true, nama: true } },
        ket_jalur_rekpro: true,
        orang_asli_papua: { select: { id: true, nama: true } },
        tempat_pendidikan: { select: { id: true, nama: true } },
        asal_rim_polda: { select: { id: true, nama: true } },
        asal_rim_polres: { select: { id: true, nama: true } },
        dikum: {
          where: { deleted_at: null },
          select: {
            id: true,
            dikum: { select: { id: true, nama: true } },
            institusi: { select: { id: true, nama: true } },
            provinsi_institusi: { select: { id: true, nama: true } },
            tahun_lulus: true,
            nilai: true,
            rata2_nilai_rapot: true,
            akreditasi_banpt: true,
            jurusan: { select: { id: true, nama: true } },
          },
        },
        ijazah_dikum_gun_seleksi_rim: { select: { id: true, nama: true } },
        genetik: {
          where: { deleted_at: null },
          select: {
            tinggi_badan_cm: true,
            berat_badan_kg: true,
            warna_kulit: true,
            warna_mata: true,
            warna_rambut: true,
            jenis_rambut: true,
            golongan_darah: true,
            ukuran_topi: { select: { id: true, nama: true } },
            ukuran_celana: true,
            ukuran_baju: { select: { id: true, nama: true } },
            ukuran_sepatu: true,
          },
        },
        keahlian: {
          where: { deleted_at: null },
          select: {
            nama_keahlian: true,
            kategori_keahlian: true,
            tingkat_kemampuan: true,
            tahun_pengalaman: true,
            deskripsi: true,
          },
        },
        hobi: {
          where: { deleted_at: null },
          select: {
            hobi: { select: { id: true, nama: true } },
          },
        },
        keluarga: {
          where: { deleted_at: null },
          select: {
            id: true,
            nama: true,
            umur: true,
            pekerjaan: true,
            jabatan: true,
            gol_pangkat: true,
            anak_ke: true,
            status_hidup: true,
            email: true,
            no_hp: true,
            medsos_instagram: true,
            medsos_facebook: true,
            medsos_twitter: true,
            suku: { select: { id: true, nama: true } },
            agama: { select: { id: true, nama: true } },
            hubungan: { select: { id: true, hubungan: true } },
          },
        },
        alamat: {
          where: { deleted_at: null },
          select: {
            provinsi: { select: { id: true, nama: true } },
            kecamatan: { select: { id: true, nama: true } },
            kelurahan: { select: { id: true, nama: true } },
            kabupaten: { select: { id: true, nama: true } },
            alamat_lengkap: true,
          },
        },
      },
    });

    const siswaToSectionMap: Record<string, string> = {};
    for (const [section, keys] of Object.entries(this.sectionMappings)) {
      for (const key of keys) {
        siswaToSectionMap[key] = section;
      }
    }

    const mappedSiswa: Record<string, any> = {};
    for (const [siswaItem, value] of Object.entries(siswa)) {
      const section = siswaToSectionMap[siswaItem];
      if (!section) continue;

      if (!mappedSiswa[section]) mappedSiswa[section] = {};

      mappedSiswa[section][siswaItem] = value;
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: mappedSiswa,
    };
  }

  async validateNrp(req: any) {}

  async createDocumentVerification(
    req: any,
    files: { [key: string]: Express.Multer.File },
  ) {
    try {
      const { user } = req;

      const fileUploaded = await Promise.all(
        Object.entries(this.documentRules).map(
          async ([key, { title, maxSize, mimeType, isRequired }]) => {
            const file = files[key]?.[0];

            if (isRequired && !file)
              throw new BadRequestException(
                `Harap unggah file ${title} karena bersifat wajib.`,
              );

            return await this.processFile(file, key, maxSize, mimeType);
          },
        ),
      );

      const payload = fileUploaded.reduce((acc, curr) => {
        const items = curr.split('|');
        if (items[1] != 'undefined') acc[items[0]] = items[1];

        return acc;
      }, {});

      await this.prisma.$transaction(async (tx) => {
        await this.prisma.data_document_siswa.upsert({
          where: { siswa_id: user.id },
          create: {
            siswa_id: user.id,
            ...payload,
          },
          update: payload,
        });

        await tx.data_validation_siswa.upsert({
          where: { siswa_id: user.id },
          create: {
            siswa_id: user.id,
            is_upload: true,
          },
          update: { is_upload: true },
        });
      });

      return {
        statusCode: HttpStatus.OK,
        message: 'Success',
        data: null,
      };
    } catch (error) {
      throw error;
    }
  }

  async createDataValidation(req: any, body: ValidasiSiswaDto) {
    const { user } = req;
    const {
      ijazah_dikum_gun_seleksi_rim_id,
      dikum,
      genetik,
      keluarga,
      hobi,
      alamat,
      ...siswa
    } = body;

    const [existingDikum, existingFamily, existingHobby] = await Promise.all([
      this.prisma.data_dikum_siswa.findMany({
        where: { siswa_id: user.id, deleted_at: null },
        select: { id: true },
      }),
      this.prisma.data_keluarga_siswa.findMany({
        where: { siswa_id: user.id, deleted_at: null },
        select: { id: true },
      }),
      this.prisma.data_hobi_siswa.findMany({
        where: { siswa_id: user.id, deleted_at: null },
        select: { id: true },
      }),
    ]);

    const [dikumPayload, familyPayload, hobbyPayload] = [
      this.findUpsertData(existingDikum, dikum),
      this.findUpsertData(existingFamily, keluarga),
      this.findUpsertData(existingHobby, hobi),
    ];

    await this.prisma.$transaction(async (tx) => {
      // Update Siswa
      await tx.data_siswa.update({
        where: { id: user.id },
        data: {
          username: siswa.username,
          nama: siswa.nama,
          nama_panggilan: siswa.nama_panggilan,
          gelar_id: siswa.gelar_id,
          jenis_kelamin: siswa.jenis_kelamin,
          tempat_lahir: siswa.tempat_lahir,
          tanggal_lahir: new Date(siswa.tanggal_lahir),
          nrp: siswa.nrp,
          nik: siswa.nik,
          no_ujian_polda: siswa.no_ujian_polda,
          no_ak_nosis: siswa.no_ak_nosis,
          jenis_diktuk_id: siswa.jenis_diktuk_id,
          kompetensi_diktuk_id: siswa.kompetensi_diktuk_id,
          sub_kompetensi_diktuk_id: siswa.sub_kompetensi_diktuk_id,
          sub_sub_kompetensi_diktuk_id: siswa.sub_sub_kompetensi_diktuk_id,
          ket_jalur_rekpro: siswa.ket_jalur_rekpro,
          orang_asli_papua_id: siswa.orang_asli_papua_id,
          tmp_dik: siswa.tempat_pendidikan_id,
          asal_rim_polda_id: siswa.asal_rim_polda_id,
          asal_rim_polres_id: siswa.asal_rim_polres_id,
          ijazah_dikum_gun_seleksi_rim_id,
        },
      });

      // Upsert Genetik
      await tx.data_genetik_siswa.upsert({
        where: { siswa_id: user.id },
        create: { siswa_id: user.id, ...genetik },
        update: { ...genetik, deleted_at: null },
      });

      await tx.data_alamat_siswa.upsert({
        where: { siswa_id: user.id },
        create: { siswa_id: user.id, ...alamat },
        update: { ...alamat, deleted_at: null },
      });

      // hobi, dikum, keluarga
      await Promise.all([
        await this.upsertGroupedData(
          tx.data_hobi_siswa,
          user.id,
          hobbyPayload,
          (item) => item,
        ),
        await this.upsertGroupedData(
          tx.data_dikum_siswa,
          user.id,
          dikumPayload,
          (item) => item,
        ),
        await this.upsertGroupedData(
          tx.data_keluarga_siswa,
          user.id,
          familyPayload,
          (item) => ({
            status_hubungan_id: item.status_hubungan_id,
            agama_id: item.agama_id,
            suku_id: item.suku_id,
            nama: item.nama,
            pekerjaan: item.pekerjaan,
            jabatan: item.jabatan,
            umur: item.umur,
            status_hidup: item.status_hidup,
            anak_ke: item.anak_ke,
            gol_pangkat: item.gol_pangkat,
            email: item.email,
            no_hp: item.no_hp,
            medsos_instagram: item.medsos_instagram,
            medsos_facebook: item.medsos_facebook,
            medsos_twitter: item.medsos_twitter,
            keterangan_lain: item.keterangan_lain,
          }),
        ),
      ]);

      await tx.data_validation_siswa.upsert({
        where: { siswa_id: user.id },
        create: {
          siswa_id: user.id,
          is_validation: true,
        },
        update: { is_validation: true },
      });
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private findUpsertData(current: any[], input: any[]) {
    const currentMap = new Map(current.map((item) => [item.id, item]));
    const inputMap = new Map(input.map((item) => [item.id, item]));

    const toCreate = input.filter((x) => !x.id || !currentMap.has(x.id));
    const toUpdate = input.filter((x) => x.id && currentMap.has(x.id));
    const toDelete = current.filter((x) => !inputMap.has(x.id));

    return { toCreate, toUpdate, toDelete };
  }

  private async upsertGroupedData(
    model: any,
    siswaId: string,
    payload: { toCreate: any[]; toUpdate: any[]; toDelete: any[] },
    updateHandler?: (item: any) => any,
  ) {
    const { toCreate, toUpdate, toDelete } = payload;

    if (toCreate.length) {
      await model.createMany({
        data: toCreate.map((item) => ({ siswa_id: siswaId, ...item })),
      });
    }

    if (toDelete.length) {
      await model.updateMany({
        where: { id: { in: toDelete.map((i) => i.id) } },
        data: { deleted_at: new Date() },
      });
    }

    if (toUpdate.length && updateHandler) {
      await chunkedUpdate(toUpdate, (item) =>
        model.update({
          where: { id: item.id, siswa_id: siswaId },
          data: updateHandler(item),
        }),
      );
    }
  }

  private async processFile(
    file: Express.Multer.File | undefined,
    fieldname: string,
    maxSize?: number | null,
    extensions?: string[] | null,
  ) {
    if (!file) return `${fieldname}|undefined`;
    validateFile(file, fieldname, maxSize, extensions);
    const fileName = await this.uploadFile(file);
    return `${fieldname}|${fileName}`;
  }

  private async uploadFile(file: Express.Multer.File) {
    if (!file) return undefined;
    const uploadedFile = await this.minio.uploadFile(file);
    return uploadedFile.filename;
  }
}
