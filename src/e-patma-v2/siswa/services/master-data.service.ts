import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import { AsalPenerimaanDto, KompetensiDiktukDto } from '../dto/master-data.dto';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { IColumnMapping, IWhere } from '../../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import { buildWhereClause } from '../../../core/utils/db.utils';
import {
  IConvertedKompetensiDiktukResult,
  IKompetensiDiktukResult,
} from '../../../core/interfaces/siswa.interface';
import { getYearsRange } from '../../../core/utils/common.utils';

@Injectable()
export class SiswaMasterDataService {
  constructor(private readonly prisma: PrismaService) {}

  private simpleColumns: IColumnMapping = {
    id: { field: 'id', type: 'number' },
    nama: { field: 'nama', type: 'string' },
  };

  private instituteColumns: IColumnMapping = {
    institusi_id: { field: 'institusi.id', type: 'bigint' },
    institusi_nama: { field: 'institusi.nama', type: 'string' },
    dikum_id: { field: 'dikum.id', type: 'bigint' },
    dikum_nama: { field: 'dikum.nama', type: 'string' },
    jurusan_id: { field: 'jurusan.id', type: 'bigint' },
    jurusan_nama: { field: 'jurusan.nama', type: 'string' },
  };

  async getFilters() {
    const years = getYearsRange();
    const gelombang = ['', 'GEL 1', 'GEL 2'];

    const results = [];
    for (const year of years) {
      for (const gel of gelombang) {
        results.push(`${gel} (${year})`);
      }
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getYears() {
    const results = getYearsRange();

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getGelombang() {
    const results = ['-', 'GEL 1', 'GEL 2'];

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getGelar(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'gelar',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getAgama(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'agama',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getHubunganKeluarga(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'hubungan_keluarga',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, hubungan: true },
    );
  }

  async getSuku(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'suku',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getUkuran(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'ukuran_fisik',
      {
        ...this.simpleColumns,
        kategori: { field: 'kategori', type: 'string' },
      },
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getWarna(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'warna_fisik',
      {
        ...this.simpleColumns,
        kategori: { field: 'kategori', type: 'string' },
      },
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getJenisRambut(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'jenis_rambut',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getGolonganDarah(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'golongan_darah',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getAkreditas(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'akreditasi',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getHobi(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'hobi',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getJenisDiktuk(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'jenis_diktuk',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getOrangAsliPapua(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'orang_asli_papua',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getTempatPendidikan(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'tmpdik',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getProvinsi(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'provinsi',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getKabupaten(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'kabupaten',
      {
        ...this.simpleColumns,
        provinsi_id: { field: 'provinsi_id', type: 'number' },
      },
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getKecamatan(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'kecamatan',
      {
        ...this.simpleColumns,
        kabupaten_id: { field: 'kabupaten_id', type: 'number' },
      },
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getKelurahan(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'kelurahan',
      {
        ...this.simpleColumns,
        kecamatan_id: { field: 'kecamatan_id', type: 'number' },
      },
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getDikum(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    return await this.getPaginatedData(
      'dikum',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
    );
  }

  async getInstitusi(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    const results = await this.getPaginatedData(
      'dikum_detail',
      this.instituteColumns,
      pagination,
      searchDto,
      { institusi: { select: { id: true, nama: true } } },
    );

    const { data, ...rest } = results;
    const res = data.map((item) => ({
      id: item.institusi.id,
      nama: item.institusi.nama,
    }));

    return {
      ...rest,
      data: res,
    };
  }

  async getJurusan(
    _: any,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    const results = await this.getPaginatedData(
      'dikum_detail',
      this.instituteColumns,
      pagination,
      searchDto,
      { jurusan: { select: { id: true, nama: true } } },
      { NOT: { jurusan_id: null } },
    );

    const { data, ...rest } = results;
    const res = data.map((item) => ({
      id: item.jurusan.id,
      nama: item.jurusan.nama,
    }));

    return {
      ...rest,
      data: res,
    };
  }

  async getAsalPenerimaan(
    _: any,
    asalDto: AsalPenerimaanDto,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    const { jenis } = asalDto;
    return await this.getPaginatedData(
      'satuan',
      this.simpleColumns,
      pagination,
      searchDto,
      { id: true, nama: true },
      {
        satuan_jenis: {
          nama: { startsWith: jenis, mode: 'insensitive' },
        },
      },
    );
  }

  async getKompetensiDiktuk(
    _: any,
    query: KompetensiDiktukDto,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    const { jenis_diktuk_id } = query;
    const { limit, page } = pagination;
    const { search, search_column, search_text, sort_column, sort_desc } =
      searchDto;

    const columns: IColumnMapping = {
      nama: { field: 'kd|nama', type: 'string' },
    };

    const skip = (page - 1) * limit;
    const take = limit;
    const { where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columns,
      true,
    );
    const whereClause = buildWhereClause(where);

    const results = await this.prisma.$queryRaw<IKompetensiDiktukResult[]>`
      WITH RECURSIVE
        hierarky AS (SELECT kd.id,
                            kd.nama,
                            d.nama      AS diktuk_name,
                            kd.parent_id,
                            kd.id::text AS chain_parent,
                            1           AS level
                     FROM kompetensi_diktuk kd
                            LEFT JOIN diktuk d ON kd.diktuk_id = d.id
                     WHERE kd.parent_id IS NULL
                       AND kd.deleted_at IS NULL
                       AND kd.diktuk_id = ${jenis_diktuk_id}
                       ${Prisma.raw(whereClause)}

                     UNION ALL

                     SELECT k.id,
                            k.nama,
                            NULL::varchar(255) AS diktuk_name,
                            k.parent_id,
                            k.id::TEXT || ' - ' || h.chain_parent,
                            h.level + 1
                     FROM kompetensi_diktuk k
                            INNER JOIN hierarky h ON k.parent_id = h.id
                     WHERE k.deleted_at IS NULL),
        filtering_data AS (SELECT DISTINCT id
                           FROM hierarky
                           LIMIT ${take} OFFSET ${skip}),
        distinct_data AS (SELECT DISTINCT id
                          FROM hierarky)
      SELECT (SELECT COUNT(*) FROM distinct_data) AS total_data, h.*
      FROM hierarky h
             JOIN filtering_data fd ON h.id = fd.id;
    `;

    const convertedResults = this.convertKompetensiDiktukResult([], results);

    const totalData = results?.[0]?.total_data
      ? Number(results[0].total_data)
      : 0;
    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page,
      totalData,
      totalPage,
      data: convertedResults,
    };
  }

  async getKompetensiDiktukV2(
    _: any,
    query: KompetensiDiktukDto,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
  ) {
    const { jenis_diktuk_id, parent_id } = query;
    const { limit, page } = pagination;
    const { search, search_column, search_text, sort_column, sort_desc } =
      searchDto;

    if (parent_id !== undefined && Number.isNaN(parent_id))
      throw new BadRequestException('Parent yang Anda maksud tidak terdaftar');

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
    };

    const { where, orderBy } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const whereClause = {
      ...where,
      parent_id,
      diktuk_id: !parent_id ? jenis_diktuk_id : null,
    };

    const [totalData, results] = await Promise.all([
      this.prisma.kompetensi_diktuk.count({
        where: whereClause,
      }),
      this.prisma.kompetensi_diktuk.findMany({
        select: { id: true, nama: true },
        where: whereClause,
        orderBy,
        take: limit,
        skip: limit * (page - 1),
      }),
    ]);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page,
      totalPage: Math.ceil(totalData / limit),
      totalData,
      data: results,
    };
  }

  private appendData(
    current: IConvertedKompetensiDiktukResult,
    data: IKompetensiDiktukResult,
  ): void {
    const chainParts = data.chain_parent.split(' - ').reverse().slice(1);
    let node = current;

    for (const partId of chainParts) {
      let child = node.children.find((child) => child.id.toString() === partId);

      if (!child) {
        child = {
          id: data.id,
          nama: data.nama,
          nama_diktuk: data.diktuk_name,
          chain_parent: data.chain_parent,
          children: [],
        };
        node.children.push(child);
      }

      node = child;
    }
  }

  private convertKompetensiDiktukResult(
    rawData: IConvertedKompetensiDiktukResult[],
    flatData: IKompetensiDiktukResult[],
  ): IConvertedKompetensiDiktukResult[] {
    flatData.forEach((item) => {
      const parts = item.chain_parent.split(' - ');
      const isRoot = parts.length <= 1;

      if (isRoot) {
        rawData.push({
          id: item.id,
          nama: item.nama,
          nama_diktuk: item.diktuk_name,
          chain_parent: item.chain_parent,
          children: [],
        });
      } else {
        const rootId = parts[parts.length - 1];
        const rootNode = rawData.find((root) => root.id.toString() === rootId);

        if (rootNode) {
          this.appendData(rootNode, item);
        }
      }
    });

    return rawData;
  }

  private async getPaginatedData(
    modelKey: keyof PrismaService,
    columns: IColumnMapping,
    pagination: PaginationDto,
    searchDto: SearchAndSortDTO,
    select: Record<string, any>,
    whereClause?: IWhere,
  ): Promise<{
    statusCode: HttpStatus;
    message: string;
    page: number;
    totalData: number;
    totalPage: number;
    data: any[];
  }> {
    const { limit, page } = pagination;
    const skip = (page - 1) * limit;
    const take = limit === 0 ? undefined : limit;

    const { search, search_column, search_text, sort_column, sort_desc } =
      searchDto;
    const { where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columns,
      true,
    );

    const model = this.prisma[modelKey] as any;

    const [totalData, data] = await Promise.all([
      model.count({
        where: {
          ...where,
          ...whereClause,
        },
      }),
      model.findMany({
        where: {
          ...where,
          ...whereClause,
        },
        select,
        skip,
        take,
      }),
    ]);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page,
      totalData,
      totalPage: limit === 0 ? 1 : Math.ceil(totalData / limit),
      data,
    };
  }
}
