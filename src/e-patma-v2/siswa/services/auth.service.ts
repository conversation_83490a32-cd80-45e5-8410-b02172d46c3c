import { HttpStatus, Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { LoginSiswaDto } from '../dto/auth.dto';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthSiswaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
  ) {}

  async login(req: any, body: LoginSiswaDto): Promise<any> {
    const { username, password } = body;

    const siswa = await this.prisma.data_siswa.findUnique({
      where: { username, password, deleted_at: null },
      select: {
        nrp: true,
        nama: true,
        unique_id: true,
        tempat_lahir: true,
        tanggal_lahir: true,
        jenis_diktuk: { select: { nama: true } },
        keluarga: {
          where: { deleted_at: null, hubungan: { hubungan: 'DIRI' } },
          select: {
            agama: { select: { nama: true } },
            no_hp: true,
            email: true,
          },
        },
      },
    });

    if (!siswa)
      return {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Username atau password yang Anda masukkan salah',
      };

    // If siswa found, create a session or token
    const accessToken = this.jwtService.sign({ unique_id: siswa.unique_id });
    const results = {
      user: {
        nrp: siswa.nrp,
        nama: siswa.nama,
        jenis_diktuk: siswa.jenis_diktuk.nama,
        tempat_lahir: siswa.tempat_lahir,
        tanggal_lahir: siswa.tanggal_lahir,
        agama: siswa.keluarga?.[0]?.agama?.nama || null,
        no_hp: siswa.keluarga?.[0]?.no_hp || null,
        email: siswa.keluarga?.[0]?.email || null,
        is_siswa: true,
      },
      accessToken,
    };

    return {
      statusCode: HttpStatus.OK,
      message: 'Login successful',
      data: results,
    };
  }

  async validateUser(uniqueID: string) {
    const siswa = await this.prisma.data_siswa.findFirst({
      where: { unique_id: uniqueID, deleted_at: null },
      select: {
        id: true,
        unique_id: true,
        nama: true,
        nama_panggilan: true,
      },
    });

    if (!siswa) throw new UnauthorizedException();
    return { user: siswa };
  }

  async logout(req: any): Promise<any> {
    return { statusCode: HttpStatus.OK, message: 'Logout successful' };
  }
}
