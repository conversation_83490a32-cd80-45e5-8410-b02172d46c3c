import { Global, Module } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { AuthSiswaController } from './controllers/auth.controller';
import { AuthSiswaService } from './services/auth.service';
import { SiswaJwtStrategy } from '../../core/configs/siswa-jwt.strategy';
import { SiswaService } from './services/siswa.service';
import { SiswaController } from './controllers/siswa.controller';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SiswaMasterDataController } from './controllers/master-data.controller';
import { SiswaMasterDataService } from './services/master-data.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';

@Global()
@Module({
  imports: [
    PrismaModule,
    PassportModule.register({ defaultStrategy: 'jwt-siswa' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET_KEY_SISWA'),
        signOptions: {
          expiresIn: config.get<string>('JWT_EXPIRED_TIME_SISWA'),
        },
      }),
    }),
  ],
  controllers: [
    AuthSiswaController,
    SiswaController,
    SiswaMasterDataController,
  ],
  providers: [
    AuthSiswaService,
    SiswaService,
    SiswaMasterDataService,
    SiswaJwtStrategy,
    MinioService,
  ],
  exports: [AuthSiswaService],
})
export class EPatmaSiswaModule {}
