import {
  Controller,
  Get,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AsalPenerimaanDto, KompetensiDiktukDto } from '../dto/master-data.dto';
import { SiswaMasterDataService } from '../services/master-data.service';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { AuthGuard } from '@nestjs/passport';

@Controller('siswa')
@UseGuards(AuthGuard(['jwt-web', 'jwt-siswa']))
@UsePipes(new ValidationPipe({ transform: true }))
export class SiswaMasterDataController {
  constructor(
    private readonly siswaMasterDataService: SiswaMasterDataService,
  ) {}

  @Get('/filters')
  async getFilters() {
    return this.siswaMasterDataService.getFilters();
  }

  @Get('/years')
  async getYears() {
    return this.siswaMasterDataService.getYears();
  }

  @Get('/gelombang')
  async getGelombang() {
    return this.siswaMasterDataService.getGelombang();
  }

  @Get('/gelar')
  async gelar(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getGelar(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/agama')
  async agama(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getAgama(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/suku')
  async suku(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getSuku(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/ukuran')
  async ukuran(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getUkuran(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/warna')
  async warna(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getWarna(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/jenis-rambut')
  async jenisRambut(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getJenisRambut(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/golongan-darah')
  async golonganDarah(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getGolonganDarah(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/akreditasi')
  async akreditasi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getAkreditas(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/hobi')
  async hobi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getHobi(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/hubungan-keluarga')
  async hubunganKeluarga(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getHubunganKeluarga(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/jenis-diktuk')
  async jenisDiktuk(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getJenisDiktuk(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/kompetensi-diktuk')
  async kompetensiDiktuk(
    @Req() req: any,
    @Query() query: KompetensiDiktukDto,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getKompetensiDiktukV2(
      req,
      query,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/orang-asli-papua')
  async orangAsliPapua(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getOrangAsliPapua(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/tempat-pendidikan')
  async tempatPendidikan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getTempatPendidikan(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/dikum')
  async dikum(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getDikum(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/institusi')
  async institusi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getInstitusi(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/provinsi')
  async provinsi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getProvinsi(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/kabupaten')
  async kabupaten(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getKabupaten(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/kecamatan')
  async kecamatan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getKecamatan(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/kelurahan')
  async kelurahan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getKelurahan(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/jurusan')
  async jurusan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.siswaMasterDataService.getJurusan(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/asal-penerimaan')
  async asalPenerimaan(
    @Req() req: any,
    @Query() query: AsalPenerimaanDto,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    console.log(query);
    return await this.siswaMasterDataService.getAsalPenerimaan(
      req,
      query,
      paginationData,
      searchAndSortData,
    );
  }
}
