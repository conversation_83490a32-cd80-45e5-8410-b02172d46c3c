import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SiswaService } from '../services/siswa.service';
import { SiswaJwtAuthGuard } from '../../../core/guards/siswa-jwt-auth.guard';
import { ValidasiSiswaDto } from '../dto/siswa.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express/multer/interceptors/file-fields.interceptor';

@Controller('siswa')
@UseGuards(SiswaJwtAuthGuard)
@UsePipes(new ValidationPipe({ transform: true }))
export class SiswaController {
  constructor(private readonly siswaService: SiswaService) {}

  @Get('/data-validation')
  async dataValidation(@Req() req: any) {
    return this.siswaService.dataValidation(req.user);
  }

  @Put('/data-validation')
  async createDataValidation(@Req() req: any, @Body() body: ValidasiSiswaDto) {
    return this.siswaService.createDataValidation(req.user, body);
  }

  @Get('/validate-nrp')
  async validateNrp(@Req() req: any) {
    return this.siswaService.validateNrp(req.user);
  }

  @Post('/document')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'pas_photo_rim', maxCount: 1 },
      { name: 'pas_photo_diktuk', maxCount: 1 },
      { name: 'ktp', maxCount: 1 },
      { name: 'kk', maxCount: 1 },
      { name: 'akte_lahir', maxCount: 1 },
      { name: 'sim_c', maxCount: 1 },
      { name: 'ijazah_sd', maxCount: 1 },
      { name: 'ijazah_smp', maxCount: 1 },
      { name: 'ijazah_sma', maxCount: 1 },
      { name: 'ijazah_d3', maxCount: 1 },
      { name: 'ijazah_d4_s1', maxCount: 1 },
      { name: 'ijazah_s2_spes', maxCount: 1 },
    ]),
  )
  async uploadDocument(
    @Req() req: any,
    @UploadedFiles() files: { [key: string]: Express.Multer.File },
  ) {
    return this.siswaService.createDocumentVerification(req.user, files);
  }
}
