import { Body, Controller, Post, Req } from '@nestjs/common';
import { AuthSiswaService } from '../services/auth.service';
import { LoginSiswaDto } from '../dto/auth.dto';

@Controller('siswa/auth')
export class AuthSiswaController {
  constructor(private authService: AuthSiswaService) {}

  @Post('login')
  async login(@Req() req: any, @Body() body: LoginSiswaDto) {
    return this.authService.login(req, body);
  }

  @Post('logout')
  async logout(@Req() req: any) {
    return this.authService.logout(req.user);
  }
}
