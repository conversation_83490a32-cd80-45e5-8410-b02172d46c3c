import {
  <PERSON><PERSON><PERSON>y,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { jenis_kelamin_enum } from '@prisma/client';

class PendidikanUmumDto {
  @IsOptional()
  @IsNumber()
  id?: number;

  @IsNumber()
  dikum_id: number;

  @IsNumber()
  institusi_id: number;

  @IsNumber()
  provinsi_institusi_id: number;

  @IsString()
  tahun_lulus: string;

  @IsString()
  nilai: string;

  @IsString()
  rata2_nilai_rapot: string;

  @IsOptional()
  @IsString()
  akreditasi_banpt?: string;

  @IsOptional()
  @IsNumber()
  jurusan_id?: number;
}

class HobbyDto {
  @IsOptional()
  @IsNumber()
  id?: number;

  @IsNumber()
  hobi_id: number;
}

class GenetikDto {
  @IsString()
  tinggi_badan_cm: string;

  @IsString()
  berat_badan_kg: string;

  @IsOptional()
  @IsNumber()
  warna_kulit_id?: number;

  @IsOptional()
  @IsNumber()
  warna_mata_id?: number;

  @IsOptional()
  @IsNumber()
  warna_rambut_id?: number;

  @IsOptional()
  @IsNumber()
  jenis_rambut_id?: number;

  @IsOptional()
  @IsNumber()
  golongan_darah_id?: number;

  @IsNumber()
  ukuran_topi_id: number;

  @IsString()
  ukuran_celana: string;

  @IsNumber()
  ukuran_baju_id: number;

  @IsString()
  ukuran_sepatu: string;
}

class KeluargaDto {
  @IsOptional()
  @IsNumber()
  id?: number;

  @IsString()
  nama: string;

  @IsNumber()
  umur: number;

  @IsOptional()
  @IsString()
  pekerjaan: string;

  @IsOptional()
  @IsString()
  jabatan: string;

  @IsOptional()
  @IsString()
  gol_pangkat?: string;

  @IsOptional()
  @IsNumber()
  anak_ke?: number;

  @IsString()
  status_hidup: string;

  @IsOptional()
  @IsString()
  email: string;

  @IsString()
  no_hp: string;

  @IsOptional()
  @IsString()
  medsos_instagram: string;

  @IsOptional()
  @IsString()
  medsos_facebook: string;

  @IsOptional()
  @IsString()
  medsos_twitter: string;

  @IsNumber()
  suku_id: number;

  @IsNumber()
  agama_id: number;

  @IsNumber()
  hubungan_id: number;
}

class AlamatDto {
  @IsNumber()
  provinsi_id: number;

  @IsNumber()
  kecamatan_id: number;

  @IsNumber()
  kelurahan_id: number;

  @IsNumber()
  kabupaten_id: number;

  @IsString()
  alamat_lengkap: string;
}

export class ValidasiSiswaDto {
  @IsString()
  username: string;

  @IsString()
  nama: string;

  @IsString()
  nama_panggilan: string;

  @IsOptional()
  @IsNumber()
  gelar_id?: number;

  @IsString()
  @IsEnum(jenis_kelamin_enum)
  jenis_kelamin: jenis_kelamin_enum;

  @IsString()
  tempat_lahir: string;

  @IsString()
  tanggal_lahir: string;

  @IsOptional()
  @IsString()
  nrp: string;

  @IsString()
  nik: string;

  @IsString()
  no_ujian_polda: string;

  @IsString()
  no_ak_nosis: string;

  @IsNumber()
  jenis_diktuk_id: number;

  @IsNumber()
  kompetensi_diktuk_id: number;

  @IsNumber()
  sub_kompetensi_diktuk_id: number;

  @IsNumber()
  sub_sub_kompetensi_diktuk_id: number;

  @IsString()
  ket_jalur_rekpro: string;

  @IsNumber()
  orang_asli_papua_id: number;

  @IsNumber()
  tempat_pendidikan_id: number;

  @IsNumber()
  asal_rim_polda_id: number;

  @IsNumber()
  asal_rim_polres_id: number;

  @ValidateNested({ each: true })
  @Type(() => PendidikanUmumDto)
  @IsArray()
  dikum: PendidikanUmumDto[];

  @IsNumber()
  ijazah_dikum_gun_seleksi_rim_id: number;

  @ValidateNested()
  @Type(() => GenetikDto)
  genetik: GenetikDto;

  @ValidateNested({ each: true })
  @Type(() => HobbyDto)
  @IsArray()
  hobi: HobbyDto[];

  @IsOptional()
  @IsArray()
  keahlian?: any[];

  @ValidateNested({ each: true })
  @Type(() => KeluargaDto)
  @IsArray()
  keluarga: KeluargaDto[];

  @ValidateNested()
  @Type(() => AlamatDto)
  alamat: AlamatDto;
}
