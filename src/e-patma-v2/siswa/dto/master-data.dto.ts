import { IsEnum, IsInt, IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

const ToNumber = () =>
  Transform(({ value }) =>
    value === '' || value === 0 || isNaN(Number(value))
      ? undefined
      : Number(value),
  );

export class AsalPenerimaanDto {
  @IsNotEmpty()
  @IsEnum(['polres', 'polda'])
  jenis: string;
}

export class KompetensiDiktukDto {
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  jenis_diktuk_id: number;

  @IsOptional()
  @ToNumber()
  @IsInt()
  parent_id?: number;
}
