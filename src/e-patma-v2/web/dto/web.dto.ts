import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import { IntersectionType } from '@nestjs/mapped-types';
import { jenis_kelamin_enum } from '@prisma/client';

const ToNumber = () =>
  Transform(({ value }) =>
    value === '' || value === 0 || isNaN(Number(value))
      ? undefined
      : Number(value),
  );

const ToNull = () =>
  Transform(({ value }) => (value === '' || !value ? undefined : value));

export class SatuanQueryDto {
  @IsNotEmpty()
  @IsString()
  jenis_satuan: string;
}

export class RecapQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @ToNumber()
  limit?: number = 10;

  @IsOptional()
  @IsInt()
  @Min(0)
  @ToNumber()
  page?: number = 1;

  @IsOptional()
  @IsObject()
  @Transform(({ value }) => value ?? {})
  sort?: Record<string, any> = {};

  @IsOptional()
  jenis_diktuk?: string[];

  @IsOptional()
  filters?: string[];

  @IsString()
  @IsNotEmpty()
  @IsEnum([
    'data-kelulusan-rekrutmen',
    'data-pendidikan-pembentukan',
    'pengolahan-data-dokumen-patma',
    'distribusi-data-dokumen-patma',
  ])
  portal: string;
}

export class TemplateDto {
  @IsNotEmpty()
  @IsInt()
  @ToNumber()
  jenis_diktuk: number;

  @IsNotEmpty()
  @IsString()
  tahun: string;

  @IsNotEmpty()
  @IsString()
  gelombang: string;

  @IsString()
  @IsNotEmpty()
  @IsEnum([
    'data-kelulusan-rekrutmen',
    'data-pendidikan-pembentukan',
    'pengolahan-data-dokumen-patma',
    'distribusi-data-dokumen-patma',
  ])
  portal: string;
}

export class SiswaDto {
  @Expose()
  @IsNotEmpty()
  @IsInt()
  @ToNumber()
  id: number;

  @Expose()
  @IsNotEmpty()
  @IsString()
  unique_id: string;

  @Expose()
  @IsOptional()
  @IsString()
  username?: string;

  @Expose()
  @IsOptional()
  @IsString()
  password?: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  no_registrasi_online: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  no_ujian_polda: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  nik: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  nama: string;

  @Expose()
  @IsOptional()
  @IsInt()
  @ToNumber()
  gelar?: number;

  @Expose()
  @IsNotEmpty()
  @IsString()
  nama_dengan_gelar: string;

  @Expose()
  @IsNotEmpty()
  @IsEnum(jenis_kelamin_enum)
  jenis_kelamin: jenis_kelamin_enum;

  @Expose()
  @IsNotEmpty()
  @IsString()
  tempat_lahir: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  tanggal_lahir: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  ta_rim_diktuk: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  gelombang_rim_diktuk: string;

  @Expose()
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  jenis_diktuk: number;

  @Expose()
  @IsNotEmpty()
  kompetensi_diktuk: string;

  @Expose()
  @IsOptional()
  sub_kompetensi_diktuk?: string;

  @Expose()
  @IsOptional()
  sub_sub_kompetensi_diktuk?: string;

  @Expose()
  @IsOptional()
  @IsString()
  ket_jalur_rekpro?: string;

  @Expose()
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  orang_asli_papua: number;

  @Expose()
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  tmp_dik: number;

  @Expose()
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  asal_rim_polda: number;

  @Expose()
  @IsNotEmpty()
  @ToNumber()
  @IsInt()
  asal_rim_polres: number;

  @Expose()
  @IsNotEmpty()
  @IsString()
  ta_pat_diktuk: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  gelombang_pat_diktuk: string;

  @Expose()
  @IsNotEmpty()
  @IsString()
  jenis_pekerjaan: string;

  @Expose()
  @IsOptional()
  @ToNumber()
  @IsInt()
  status_kawin?: number;

  @Expose()
  @ToNumber()
  @IsInt()
  ijazah_dikum_gun_seleksi_rim: number;
}

export class RekrutmenPandaDTO {
  @Expose()
  @IsOptional()
  @IsString()
  @ToNull()
  catatan_khusus_seleksi_rim?: string;
  @Expose()
  @IsOptional()
  @IsString()
  @ToNull()
  data_deteksi_dini_densus?: string;
  @Expose() @IsOptional() @IsString() @ToNull() diagnosa_rikkes_1?: string;
  @Expose() @IsOptional() @IsString() @ToNull() diagnosa_rikkes_2?: string;

  @Expose() @IsOptional() @ToNumber() @IsInt() hasil_lari_12_m_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() hasil_pull_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() hasil_push_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() hasil_shuttle_run_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() hasil_sit_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() jarak_renang_meter_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() waktu_renang_detik_ukj?: number;
  @Expose() @IsOptional() @IsString() @ToNull() kelainan_antro_ukj?: string;

  @Expose() @IsOptional() @IsString() @ToNull() ket_akademik?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_pmk?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_rikmin_awal?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_rikmin_akhir?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_rikpsi_1?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_rikpsi_2?: string;
  @Expose() @IsOptional() @IsString() @ToNull() ket_ukj?: string;

  @Expose() @IsOptional() @IsString() @ToNull() prestasi_seleksi_rim?: string;

  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_rikkes_2?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_antro_ukj?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_akhir_ukj?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_rikpsi_1?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_rikpsi_2?: string;
  @Expose()
  @IsOptional()
  @IsString()
  @ToNull()
  n_kualitatif_rikmin_awal?: string;
  @Expose()
  @IsOptional()
  @IsString()
  @ToNull()
  n_kualitatif_rikmin_akhir?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_rikkes_1?: string;
  @Expose() @IsOptional() @IsString() @ToNull() n_kualitatif_pmk?: string;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_rikkes_2?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_antro_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_akhir_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_rikpsi_1?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_rikpsi_2?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_rikkes_1?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_kuantitatif_pmk?: number;

  @Expose() @IsOptional() @ToNumber() @IsInt() n_peng_u_akademik?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_mtk_akademik?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_wwsn_k_akademik?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_gbg_akhir_akademik?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_b_ing_akademik?: number;

  @Expose() @IsOptional() @ToNumber() @IsInt() n_a_lari_12_menit_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_pull_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_push_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_shuttle_run_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_sit_up_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_renang_ukj?: number;
  @Expose() @IsOptional() @ToNumber() @IsInt() n_ab_ukj?: number;

  @Expose() @IsOptional() @ToNumber() @IsInt() rata2_n_b_ukj?: number;
  @Expose() @IsOptional() @IsString() @ToNull() temuan_pmk?: string;
  @Expose() @IsOptional() @IsString() @ToNull() temuan_rikpsi_2?: string;
}

export class UploadSiswaDto extends IntersectionType(
  SiswaDto,
  RekrutmenPandaDTO,
) {}

export class UploadDataWrapperDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UploadSiswaDto)
  data: UploadSiswaDto[];
}
