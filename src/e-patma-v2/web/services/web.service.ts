import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import * as ExcelJS from 'exceljs';
import { jenis_kelamin_enum } from '@prisma/client';
import {
  getNestedValue,
  getYearsRange,
  makeRandomString,
  toPercentage,
} from '../../../core/utils/common.utils';
import {
  RecapQueryDto,
  RekrutmenPandaDTO,
  SiswaDto,
  TemplateDto,
  UploadDataWrapperDto,
} from '../dto/web.dto';
import { getCellStyleByType } from '../../../core/utils/excel.utils';
import { plainToInstance } from 'class-transformer';
import { ExcelColumn } from '../../../core/interfaces/e-patma.interface';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../../core/utils/search.utils';

@Injectable()
export class EPatmaWebService {
  constructor(private readonly prismaService: PrismaService) {}

  async getStatistics(req: any, jenisDiktuk?: string[], filters?: string[]) {
    const { years, conditions: filterConditions } =
      this.filterGelombangTahunFormat(filters);
    const jenisDiktukCondition = this.buildDiktukConditions(jenisDiktuk);

    const [diktukList, siswaList] = await Promise.all([
      this.prismaService.jenis_diktuk.findMany({
        where: {
          deleted_at: null,
          OR: jenisDiktukCondition.length ? jenisDiktukCondition : undefined,
        },
        select: { nama: true, background_color: true, deskripsi: true },
      }),
      this.prismaService.data_siswa.findMany({
        where: {
          deleted_at: null,
          OR: filterConditions,
          jenis_diktuk: jenisDiktukCondition.length
            ? { OR: jenisDiktukCondition }
            : undefined,
        },
        select: {
          jenis_kelamin: true,
          ta_rim_diktuk: true,
          gelombang_rim_diktuk: true,
          jenis_diktuk: { select: { nama: true } },
        },
      }),
    ]);

    const results = Object.fromEntries(
      diktukList.map(({ nama, background_color, deskripsi }) => [
        nama,
        {
          background_color,
          deskripsi,
          laki_laki: { total: 0 },
          perempuan: { total: 0 },
          total: 0,
          detail: Object.fromEntries(years.map((year) => [year, {}])),
        },
      ]),
    );

    siswaList.forEach(
      ({
        jenis_diktuk: { nama },
        jenis_kelamin,
        ta_rim_diktuk,
        gelombang_rim_diktuk,
      }) => {
        const diktuk = results[nama];
        if (!diktuk) return;

        const tahunData = (diktuk.detail[ta_rim_diktuk] ??= {});
        const gelombangData = (tahunData[gelombang_rim_diktuk] ??= {
          laki_laki: { total: 0 },
          perempuan: { total: 0 },
          total: 0,
        });

        this.updateCounts(diktuk, jenis_kelamin);
        this.updateCounts(gelombangData, jenis_kelamin);
      },
    );

    const finalResults = Object.entries(results).reduce((acc, [key, val]) => {
      const { detail, ...summary } = val;
      this.addPercentage(summary);

      const details = Object.entries(detail).flatMap(([tahun, gelombangMap]) =>
        Object.entries(gelombangMap).map(([gelombang, data]) => {
          this.addPercentage(data);
          return {
            tahun,
            gelombang,
            ...(data as {}),
          };
        }),
      );

      acc[key] = {
        ...summary,
        details,
      };

      return acc;
    }, {});

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: finalResults,
    };
  }

  async getRecapPortalOne(req: any, queryData: RecapQueryDto) {
    const { limit, page, sort, jenis_diktuk, filters } = queryData;
    const jenisDiktukCondition = this.buildDiktukConditions(jenis_diktuk);

    const [poldaData, siswaList] = await Promise.all([
      this.getPoldaList(limit, page, sort),
      this.getSiswaList(filters, jenisDiktukCondition),
    ]);

    const results = Object.fromEntries(
      poldaData.list.map(({ nama }) => [
        nama,
        {
          laki_laki: { total: 0 },
          perempuan: { total: 0 },
          total: 0,
        },
      ]),
    );

    for (const siswa of siswaList) {
      const polda = results[siswa.asal_rim_polda.nama];
      if (polda) this.updateCounts(polda, siswa.jenis_kelamin);
    }

    Object.values(results).forEach(this.addPercentage);

    let finalResults = Object.entries(results).map(([nama, val]) => ({
      asal_rim_polda: nama,
      ...val,
    }));

    ['laki_laki', 'perempuan', 'total'].forEach((key) => {
      if (sort[key])
        finalResults = this.sortByField(
          finalResults,
          `${key}.total`,
          sort[key],
        );
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page,
      totalPage: Math.ceil(poldaData.total / limit),
      totalData: poldaData.total,
      data: finalResults,
    };
  }

  async getRecapPortalTwo(req: any, queryData: RecapQueryDto) {
    const { filters, jenis_diktuk } = queryData;
    const jenisDiktukCondition = this.buildDiktukConditions(jenis_diktuk);
    const { conditions: filterConditions } =
      this.filterGelombangTahunFormat(filters);

    const [diktukList, siswaList] = await Promise.all([
      this.prismaService.jenis_diktuk.findMany({
        where: {
          deleted_at: null,
          OR: jenisDiktukCondition.length ? jenisDiktukCondition : undefined,
        },
        select: { nama: true, background_color: true, deskripsi: true },
      }),
      this.prismaService.data_siswa.findMany({
        where: {
          deleted_at: null,
          OR: filterConditions,
          jenis_diktuk: jenisDiktukCondition.length
            ? { OR: jenisDiktukCondition }
            : undefined,
        },
        select: {
          jenis_diktuk: { select: { nama: true } },
          validation: {
            select: {
              is_validation: true,
              is_upload: true,
              is_angket: true,
              status_diktuk: { select: { nama: true } },
            },
          },
        },
      }),
    ]);

    const results = Object.fromEntries(
      diktukList.map(({ nama, background_color, deskripsi }) => [
        nama,
        {
          background_color,
          deskripsi,
          total: 0,
          validation: {
            verification_nrp: {
              filled: { total: 0 },
              not_filled_yet: { total: 0 },
            },
            angket: {
              filled: { total: 0 },
              not_filled_yet: { total: 0 },
            },
            upload_document: {
              filled: { total: 0 },
              not_filled_yet: { total: 0 },
            },
          },
          status: {
            active: { total: 0 },
            non_active: { total: 0 },
            dead: { total: 0 },
            etc: { total: 0 },
          },
        },
      ]),
    );

    for (const siswa of siswaList) {
      const diktukName = siswa.jenis_diktuk?.nama;
      const target = results[diktukName];
      if (!target) continue;

      const { validation } = siswa;

      target.total++;
      this.updateCounts(
        target.validation.verification_nrp,
        validation?.is_validation ?? false,
      );
      this.updateCounts(
        target.validation.angket,
        validation?.is_angket ?? false,
      );
      this.updateCounts(
        target.validation.upload_document,
        validation?.is_upload ?? false,
      );

      this.updateCounts(target.status, validation?.status_diktuk?.nama);
    }

    Object.values(results).forEach(this.addPercentagePortalTwo);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getKelolaData(
    req: any,
    { limit, page }: PaginationDto,
    {
      search,
      search_column,
      search_text,
      sort_column,
      sort_desc,
    }: SearchAndSortDTO,
    { jenis_diktuk, gelombang, tahun, portal }: TemplateDto,
  ) {
    const columnMapping: IColumnMapping = {
      unique_id: { field: 'unique_id', type: 'string' },
      pers_id: { field: 'pers_id', type: 'number' },
      username: { field: 'username', type: 'string' },
      password: { field: 'password', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      nama_dengan_gelar: { field: 'nama_dengan_gelar', type: 'string' },
      nama_panggilan: { field: 'nama_panggilan', type: 'string' },
      jenis_kelamin: { field: 'jenis_kelamin', type: 'string' },
      tempat_lahir: { field: 'tempat_lahir', type: 'string' },
      tanggal_lahir: { field: 'tanggal_lahir', type: 'string' },
      nrp: { field: 'nrp', type: 'string' },
      nik: { field: 'nik', type: 'string' },
      jenis_pekerjaan: { field: 'jenis_pekerjaan', type: 'string' },
      no_ujian_polda: { field: 'no_ujian_polda', type: 'string' },
      no_registrasi_online: { field: 'no_registrasi_online', type: 'string' },
      no_ak_nosis: { field: 'no_ak_nosis', type: 'string' },
      ta_rim_diktuk: { field: 'ta_rim_diktuk', type: 'string' },
      gelombang_rim_diktuk: { field: 'gelombang_rim_diktuk', type: 'string' },
      ta_pat_diktuk: { field: 'ta_pat_diktuk', type: 'string' },
      gelombang_pat_diktuk: { field: 'gelombang_pat_diktuk', type: 'string' },
      ijazah_dikum_gun_seleksi_rim: {
        field: 'ijazah_dikum_gun_seleksi_rim',
        type: 'string',
      },
      ket_jalur_rekpro: { field: 'ket_jalur_rekpro', type: 'string' },
      orang_asli_papua: { field: 'orang_asli_papua.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, siswaList] = await Promise.all([
      this.prismaService.data_siswa.count({
        where: {
          ...where,
          jenis_diktuk_id: jenis_diktuk,
          gelombang_rim_diktuk: gelombang,
          ta_rim_diktuk: tahun,
          deleted_at: null,
        },
      }),
      this.prismaService.data_siswa.findMany({
        where: {
          ...where,
          jenis_diktuk_id: jenis_diktuk,
          gelombang_rim_diktuk: gelombang,
          ta_rim_diktuk: tahun,
          deleted_at: null,
        },
        select: {
          id: true,
          unique_id: true,
          pers_id: true,
          username: true,
          password: true,
          nama: true,
          nama_dengan_gelar: true,
          nama_panggilan: true,
          jenis_kelamin: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          nrp: true,
          nik: true,
          jenis_pekerjaan: true,
          no_ujian_polda: true,
          no_registrasi_online: true,
          no_ak_nosis: true,
          ta_rim_diktuk: true,
          gelombang_rim_diktuk: true,
          ta_pat_diktuk: true,
          gelombang_pat_diktuk: true,
          ijazah_dikum_gun_seleksi_rim: true,
          ket_jalur_rekpro: true,
          orang_asli_papua: { select: { id: true, nama: true } },
          keluarga: {
            select: {
              id: true,
              nama: true,
              pekerjaan: true,
              jabatan: true,
              umur: true,
              status_hidup: true,
              email: true,
              no_hp: true,
              anak_ke: true,
              hubungan: { select: { id: true, hubungan: true } },
              suku: { select: { id: true, nama: true } },
              agama: { select: { id: true, nama: true } },
            },
          },
          gelar: { select: { id: true, nama: true } },
          jenis_diktuk: { select: { id: true, nama: true } },
          status_kawin: { select: { id: true, nama: true } },
          tempat_pendidikan: { select: { id: true, nama: true } },
          asal_rim_polda: { select: { id: true, nama: true } },
          asal_rim_polres: { select: { id: true, nama: true } },
          kompetensi_diktuk: { select: { id: true, nama: true } },
          sub_kompetensi_diktuk: { select: { id: true, nama: true } },
          sub_sub_kompetensi_diktuk: { select: { id: true, nama: true } },
          rekrutmen: {
            include: {
              recruitment_type: {
                select: { id: true, nama: true, deskripsi: true },
              },
            },
          },
          keahlian: true,
          hobi: true,
          genetik: true,
          dikum: true,
          alamat: true,
          validation: true,
        },
        orderBy,
        skip: limit * (page - 1),
        take: limit,
      }),
    ]);

    const results = siswaList.map((siswa) => {
      const { keluarga, rekrutmen, ...rest } = siswa;

      const mappingKeluarga = keluarga?.reduce((acc, item) => {
        const key = item.hubungan?.hubungan.toLowerCase();
        acc[key] = item;
        return acc;
      }, {});

      const mappingRekrutmen = this.mapRekrutmen(rekrutmen || []);

      return {
        ...rest,
        keluarga: mappingKeluarga,
        rekrutmen: mappingRekrutmen,
      };
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page,
      totalPage: Math.ceil(totalData / limit),
      totalData,
      data: results,
    };
  }

  async uploadDataSiswa(req: any, body: UploadDataWrapperDto) {
    const { data } = body;

    const normalizeId = (value: any): number | null =>
      value && !isNaN(Number(value)) ? Number(value) : null;

    const findKompetensiId = async (value: string | number) => {
      if (!value) return null;
      return this.prismaService.kompetensi_diktuk.findFirst({
        select: { id: true },
        where: {
          OR: [
            { nama: { contains: String(value), mode: 'insensitive' } },
            { id: normalizeId(value) ?? -1 },
          ],
        },
      });
    };

    const buildRelationalData = async (siswa: SiswaDto) => {
      const [kompetensi, subKompetensi, subSubKompetensi] = await Promise.all([
        findKompetensiId(siswa.kompetensi_diktuk),
        findKompetensiId(siswa.sub_kompetensi_diktuk),
        findKompetensiId(siswa.sub_sub_kompetensi_diktuk),
      ]);

      return {
        asal_rim_polda_id: normalizeId(siswa.asal_rim_polda),
        asal_rim_polres_id: normalizeId(siswa.asal_rim_polres),
        tmp_dik: normalizeId(siswa.tmp_dik),
        jenis_diktuk_id: normalizeId(siswa.jenis_diktuk),
        orang_asli_papua_id: normalizeId(siswa.orang_asli_papua),
        gelar_id: normalizeId(siswa.gelar),
        status_kawin_id: normalizeId(siswa.status_kawin),
        kompetensi_diktuk_id: kompetensi?.id ?? null,
        sub_kompetensi_diktuk_id: subKompetensi?.id ?? null,
        sub_sub_kompetensi_diktuk_id: subSubKompetensi?.id ?? null,
        ijazah_dikum_gun_seleksi_rim_id: normalizeId(
          siswa.ijazah_dikum_gun_seleksi_rim,
        ),
      };
    };

    const upserts = data.map(async (rawItem) => {
      const siswa = plainToInstance(SiswaDto, rawItem, {
        enableImplicitConversion: true,
        excludeExtraneousValues: true,
      });

      const rekrutmen = plainToInstance(RekrutmenPandaDTO, rawItem, {
        enableImplicitConversion: true,
        excludeExtraneousValues: true,
      });

      const {
        kompetensi_diktuk,
        sub_kompetensi_diktuk,
        sub_sub_kompetensi_diktuk,
        status_kawin,
        jenis_diktuk,
        gelar,
        tmp_dik,
        orang_asli_papua,
        ijazah_dikum_gun_seleksi_rim,
        asal_rim_polda,
        asal_rim_polres,
        ...restSiswa
      } = siswa;

      const relationalData = await buildRelationalData(siswa);

      const payload = {
        ...restSiswa,
        ...relationalData,
      };

      return this.prismaService.$transaction(async (tx) => {
        const siswaUpsert = await tx.data_siswa.upsert({
          where: { id: siswa.id },
          update: payload,
          create: {
            ...payload,
            pers_id: Number(
              makeRandomString({
                length: 3,
                isNumeric: true,
                isLowerCase: false,
              }),
            ),
          },
          select: {
            id: true,
            rekrutmen: {
              select: {
                id: true,
                recruitment_type: { select: { id: true, nama: true } },
              },
            },
          },
        });

        const existingPanda = siswaUpsert.rekrutmen?.find(
          (item) => item.recruitment_type.id === 2,
        );

        if (!existingPanda) {
          return tx.data_rekrutmen_siswa.create({
            data: {
              ...rekrutmen,
              siswa_id: siswaUpsert.id,
              type_rekrutmen_id: 2,
            },
          });
        }

        return tx.data_rekrutmen_siswa.update({
          where: {
            id: existingPanda.id,
            siswa_id: siswaUpsert.id,
          },
          data: {
            ...rekrutmen,
          },
        });
      });
    });

    await Promise.all(upserts);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  async createTemplateExcel(req: any, body: TemplateDto) {
    const { jenis_diktuk, gelombang, tahun, portal } = body;

    // Setup workbook and sheet
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet('Template');

    // Fetch data
    const siswaList = await this.prismaService.data_siswa.findMany({
      where: {
        deleted_at: null,
        ta_rim_diktuk: tahun,
        gelombang_rim_diktuk: gelombang,
        jenis_diktuk_id: jenis_diktuk,
      },
      select: this.getSiswaSelectFields(),
    });

    let excelColumns: ExcelColumn[];
    switch (portal) {
      case 'data-kelulusan-rekrutmen':
        excelColumns = this.getDataRimColumns();
        break;
      case 'data-pendidikan-pembentukan':
        excelColumns = this.getDataJasDiktukColumns();
        break;
    }

    // Column setup
    const columns = [...this.getSiswaExcelColumns(), ...excelColumns];

    sheet.columns = columns.map(({ header, key, hidden, width, numFmt }) => ({
      header,
      key,
      hidden,
      width,
      style: getCellStyleByType(
        numFmt as 'integer' | 'number' | 'string' | 'date' | 'currency',
      ),
    }));

    // Header styling
    const headerRow = sheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFF00' },
      };
      cell.font = { bold: true };
    });

    // Add rows
    siswaList.forEach((siswa, idx) => {
      const rowData: Record<string, any> = {};
      const rekrutmenMap = this.mapRekrutmen(siswa.rekrutmen || []);

      columns.forEach(({ key, numFmt }) => {
        let value =
          getNestedValue(siswa, key) ?? getNestedValue(rekrutmenMap, key);

        if (numFmt === 'number' && value) {
          value = parseFloat(value);
        }

        if (key === 'jenis_kelamin') {
          value =
            siswa.jenis_kelamin === jenis_kelamin_enum.LAKI_LAKI
              ? 'LAKI-LAKI'
              : siswa.jenis_kelamin;
        } else if (key === 'no') {
          value = idx + 1;
        }

        rowData[key] = value ?? '-';
      });

      sheet.addRow(rowData);
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return { filename: 'Template-Upload', buffer };
  }

  async deleteDataSiswa(req: any, body: { ids: number[] }) {
    const { ids } = body;

    if (!ids || !ids.length) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'No IDs provided for deletion',
        data: null,
      };
    }

    await this.prismaService.data_siswa.updateMany({
      where: { id: { in: ids }, deleted_at: null },
      data: { deleted_at: new Date() },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Data siswa successfully deleted',
      data: null,
    };
  }

  async listFiles(
    req: any,
    {
      search,
      search_column,
      search_text,
      sort_column,
      sort_desc,
    }: SearchAndSortDTO,
  ) {
    const columnMapping: IColumnMapping = {
      parent_id: { field: 'parent_id', type: 'bigint' },
      name: { field: 'name', type: 'string' },
      updated_at: { field: 'updated_at', type: 'date' },
    };

    const { where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const files = await this.prismaService.file_manager_patma.findMany({
      where: {
        ...where,
        deleted_at: null,
      },
      select: {
        id: true,
        name: true,
        type: true,
        path: true,
        size: true,
        updated_at: true,
      },
      orderBy: [{ type: 'desc' }, { updated_at: 'desc' }],
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: files,
    };
  }

  private getSiswaSelectFields() {
    return {
      id: true,
      unique_id: true,
      nama: true,
      username: true,
      password: true,
      gelar: { select: { nama: true } },
      nama_dengan_gelar: true,
      jenis_kelamin: true,
      tempat_lahir: true,
      tanggal_lahir: true,
      nik: true,
      jenis_pekerjaan: true,
      status_kawin: { select: { nama: true } },
      no_ujian_polda: true,
      no_registrasi_online: true,
      jenis_diktuk: { select: { nama: true } },
      ta_rim_diktuk: true,
      gelombang_rim_diktuk: true,
      kompetensi_diktuk: { select: { nama: true } },
      sub_kompetensi_diktuk: { select: { nama: true } },
      sub_sub_kompetensi_diktuk: { select: { nama: true } },
      ket_jalur_rekpro: true,
      orang_asli_papua: { select: { nama: true } },
      tempat_pendidikan: { select: { nama: true } },
      asal_rim_polda: { select: { nama: true } },
      asal_rim_polres: { select: { nama: true } },
      ta_pat_diktuk: true,
      gelombang_pat_diktuk: true,
      ijazah_dikum_gun_seleksi_rim: { select: { nama: true } },
      rekrutmen: {
        include: { recruitment_type: { select: { nama: true } } },
      },
      jasdiktuk: true,
    };
  }

  private mapRekrutmen(rekrutmenArray: any[]): Record<string, any> {
    return rekrutmenArray.reduce(
      (acc, item) => {
        const key = item.recruitment_type?.nama?.toLowerCase();
        if (key) acc[key] = item;
        return acc;
      },
      {} as Record<string, any>,
    );
  }

  private getSiswaExcelColumns(): ExcelColumn[] {
    return [
      { header: 'ID', key: 'id', width: 15, numFmt: 'integer', hidden: true },
      { header: 'NO', key: 'no', width: 15, numFmt: 'integer' },
      { header: 'UNIQUE ID', key: 'unique_id', width: 20, numFmt: 'string' },
      { header: 'USERNAME', key: 'username', width: 20, numFmt: 'string' },
      { header: 'PASSWORD', key: 'password', width: 20, numFmt: 'string' },
      {
        header: 'NO REGISTRASI ONLINE',
        key: 'no_registrasi_online',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'NO UJIAN POLDA',
        key: 'no_ujian_polda',
        width: 20,
        numFmt: 'string',
      },
      { header: 'NIK', key: 'nik', width: 20, numFmt: 'string' },
      { header: 'NAMA', key: 'nama', width: 20, numFmt: 'string' },
      { header: 'GELAR', key: 'gelar.nama', width: 15, numFmt: 'string' },
      {
        header: 'NAMA DENGAN GELAR',
        key: 'nama_dengan_gelar',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'JENIS KELAMIN',
        key: 'jenis_kelamin',
        width: 15,
        numFmt: 'string',
      },
      {
        header: 'TEMPAT LAHIR',
        key: 'tempat_lahir',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'TANGGAL LAHIR',
        key: 'tanggal_lahir',
        width: 20,
        numFmt: 'date',
      },
      {
        header: 'TA RIM DIKTUK',
        key: 'ta_rim_diktuk',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'GELOMBANG RIM DIKTUK',
        key: 'gelombang_rim_diktuk',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'JENIS DIKTUK',
        key: 'jenis_diktuk.nama',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'KOMPETENSI DIKTUK',
        key: 'kompetensi_diktuk.nama',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'SUB KOMPETENSI DIKTUK',
        key: 'sub_kompetensi_diktuk.nama',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'SUB SUB KOMPETENSI DIKTUK',
        key: 'sub_sub_kompetensi_diktuk.nama',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'KET JALUR REKPRO',
        key: 'ket_jalur_rekpro',
        width: 40,
        numFmt: 'string',
      },
      {
        header: 'TMP DIK',
        key: 'tempat_pendidikan.nama',
        width: 15,
        numFmt: 'string',
      },
      {
        header: 'ASAL RIM POLDA',
        key: 'asal_rim_polda.nama',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'ASAL RIM POLRES',
        key: 'asal_rim_polres.nama',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'TA PAT DIKTUK',
        key: 'ta_pat_diktuk',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'GELOMBANG PAT DIKTUK',
        key: 'gelombang_pat_diktuk',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'JENIS PEKERJAAN',
        key: 'jenis_pekerjaan',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'STATUS KAWIN',
        key: 'status_kawin.nama',
        width: 20,
        numFmt: 'string',
      },
      {
        header: 'ORANG ASLI PAPUA',
        key: 'orang_asli_papua.nama',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'IJAZAH DIKUM GUN SELEKSI RIM',
        key: 'ijazah_dikum_gun_seleksi_rim.nama',
        width: 30,
        numFmt: 'string',
      },
    ];
  }

  private getDataJasDiktukColumns(): ExcelColumn[] {
    return [
      {
        header: 'HASIL LARI 12 M',
        key: 'jasdiktuk.hasil_lari_12_m',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'N A LARI 12 MENIT',
        key: 'jasdiktuk.n_a_lari_12_menit',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'HASIL PULL UP',
        key: 'jasdiktuk.hasil_pull_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N PULL UP',
        key: 'jasdiktuk.n_pull_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL SIT UP',
        key: 'jasdiktuk.hasil_sit_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N SIT UP',
        key: 'jasdiktuk.n_sit_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL PUSH UP',
        key: 'jasdiktuk.hasil_push_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N PUSH UP',
        key: 'jasdiktuk.n_push_up',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL SHUTTLE RUN',
        key: 'jasdiktuk.hasil_shuttle_run',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'N SHUTTLE RUN',
        key: 'jasdiktuk.n_shuttle_run',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'RATA2 N B',
        key: 'jasdiktuk.rata2_n_b',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N AB',
        key: 'jasdiktuk.n_ab',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N JASMANI',
        key: 'jasdiktuk.n_jasmani',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N MENTAL KEP KAR',
        key: 'jasdiktuk.n_mental_kep_kar',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N AKADEMIK PENG',
        key: 'jasdiktuk.n_akademik_peng',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N GBG AKHIR',
        key: 'jasdiktuk.n_gbg_akhir',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'RANKING',
        key: 'jasdiktuk.ranking',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'CATATAN KESEHATAN',
        key: 'jasdiktuk.catatan_kesehatan',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'CATATAN PELANGGARAN',
        key: 'jasdiktuk.catatan_pelanggaran',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'CATATAN PSIKOLOGI',
        key: 'jasdiktuk.catatan_psikologi',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'PRESTASI',
        key: 'jasdiktuk.prestasi',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'CATATAN KHUSUS',
        key: 'jasdiktuk.catatan_khusus',
        width: 25,
        numFmt: 'string',
      },
    ];
  }

  private getDataRimColumns(): ExcelColumn[] {
    return [
      {
        header: 'N KUALITATIF RIKMIN AWAL',
        key: 'panda.n_kualitatif_rikmin_awal',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'KET RIKMIN AWAL',
        key: 'panda.ket_rikmin_awal',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF RIKMIN AKHIR',
        key: 'panda.n_kualitatif_rikmin_akhir',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'KET RIKMIN AKHIR',
        key: 'panda.ket_rikmin_akhir',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF RIKKES 1',
        key: 'panda.n_kualitatif_rikkes_1',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF RIKKES 1',
        key: 'panda.n_kuantitatif_rikkes_1',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'DIAGNOSA RIKKES 1',
        key: 'panda.diagnosa_rikkes_1',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF RIKKES 2',
        key: 'panda.n_kualitatif_rikkes_2',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF RIKKES 2',
        key: 'panda.n_kuantitatif_rikkes_2',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'DIAGNOSA RIKKES 2',
        key: 'panda.diagnosa_rikkes_2',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF RIKPSI 1',
        key: 'panda.n_kualitatif_rikpsi_1',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF RIKPSI 1',
        key: 'panda.n_kuantitatif_rikpsi_1',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'KET RIKPSI 1',
        key: 'panda.ket_rikpsi_1',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF RIKPSI 2',
        key: 'panda.n_kualitatif_rikpsi_2',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF RIKPSI 2',
        key: 'panda.n_kuantitatif_rikpsi_2',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'TEMUAN RIKPSI 2',
        key: 'panda.temuan_rikpsi_2',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'KET RIKPSI 2',
        key: 'panda.ket_rikpsi_2',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N PENG U AKADEMIK',
        key: 'panda.n_peng_u_akademik',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N WWSN K AKADEMIK',
        key: 'panda.n_wwsn_k_akademik',
        width: 25,
        numFmt: 'integer',
      },
      {
        header: 'N MTK AKADEMIK',
        key: 'panda.n_mtk_akademik',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N B ING AKADEMIK',
        key: 'panda.n_b_ing_akademik',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N GBG AKHIR AKADEMIK',
        key: 'panda.n_gbg_akhir_akademik',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'KET AKADEMIK',
        key: 'panda.ket_akademik',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'HASIL LARI 12 M UKJ',
        key: 'panda.hasil_lari_12_m_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'N A LARI 12 MENIT UKJ',
        key: 'panda.n_a_lari_12_menit_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'HASIL PULL UP UKJ',
        key: 'panda.hasil_pull_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N PULL UP UKJ',
        key: 'panda.n_pull_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL SIT UP UKJ',
        key: 'panda.hasil_sit_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N SIT UP UKJ',
        key: 'panda.n_sit_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL PUSH UP UKJ',
        key: 'panda.hasil_push_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N PUSH UP UKJ',
        key: 'panda.n_push_up_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'HASIL SHUTTLE RUN UKJ',
        key: 'panda.hasil_shuttle_run_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'N SHUTTLE RUN UKJ',
        key: 'panda.n_shuttle_run_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'RATA2 N B UKJ',
        key: 'panda.rata2_n_b_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N AB UKJ',
        key: 'panda.n_ab_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'JARAK RENANG METER UKJ',
        key: 'panda.jarak_renang_meter_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'WAKTU RENANG DETIK UKJ',
        key: 'panda.waktu_renang_detik_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N RENANG UKJ',
        key: 'panda.n_renang_ukj',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'N KUALITATIF ANTRO UKJ',
        key: 'panda.n_kualitatif_antro_ukj',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF ANTRO UKJ',
        key: 'panda.n_kuantitatif_antro_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'KELAINAN ANTRO UKJ',
        key: 'panda.kelainan_antro_ukj',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF AKHIR UKJ',
        key: 'panda.n_kuantitatif_akhir_ukj',
        width: 30,
        numFmt: 'number',
      },
      {
        header: 'N KUALITATIF AKHIR UKJ',
        key: 'panda.n_kualitatif_akhir_ukj',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'KET UKJ',
        key: 'panda.ket_ukj',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUALITATIF PMK',
        key: 'panda.n_kualitatif_pmk',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'N KUANTITATIF PMK',
        key: 'panda.n_kuantitatif_pmk',
        width: 25,
        numFmt: 'number',
      },
      {
        header: 'TEMUAN PMK',
        key: 'panda.temuan_pmk',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'KET PMK',
        key: 'panda.ket_pmk',
        width: 25,
        numFmt: 'string',
      },
      {
        header: 'DATA DETEKSI DINI DENSUS',
        key: 'panda.data_deteksi_dini_densus',
        width: 40,
        numFmt: 'string',
      },
      {
        header: 'PRESTASI SELEKSI RIM',
        key: 'panda.prestasi_seleksi_rim',
        width: 30,
        numFmt: 'string',
      },
      {
        header: 'CATATAN KHUSUS SELEKSI RIM',
        key: 'panda.catatan_khusus_seleksi_rim',
        width: 40,
        numFmt: 'string',
      },
    ];
  }

  private addPercentage(target: any) {
    target.laki_laki.percentage = toPercentage(
      target.laki_laki.total,
      target.total,
    );
    target.perempuan.percentage = toPercentage(
      target.perempuan.total,
      target.total,
    );
  }

  private addPercentagePortalTwo(target: any) {
    Object.entries(target.validation).forEach(([key, _]) => {
      target.validation[key].filled.percentage = toPercentage(
        target.validation[key].filled.total,
        target.total,
      );
      target.validation[key].not_filled_yet.percentage = toPercentage(
        target.validation[key].not_filled_yet.total,
        target.total,
      );
    });

    Object.entries(target.status).forEach(([key, _]) => {
      target.status[key].percentage = toPercentage(
        target.status[key].total,
        target.total,
      );
    });
  }

  private sortByField(data: any[], field: string, order: 'asc' | 'desc') {
    return [...data].sort((a, b) =>
      order === 'asc'
        ? getNestedValue(a, field) - getNestedValue(b, field)
        : getNestedValue(b, field) - getNestedValue(a, field),
    );
  }

  private filterGelombangTahunFormat(data?: string[]) {
    const yearSet = new Set<number>();

    const filterConditions = data?.map((filter) => {
      const [gelombang, yearStr] = filter.split(' > ');
      const year = Number(yearStr);
      yearSet.add(year);

      return {
        AND: {
          gelombang_rim_diktuk: gelombang !== '' ? gelombang : undefined,
          ta_rim_diktuk: yearStr,
        },
      };
    });

    const years = yearSet.size ? [...yearSet].sort() : getYearsRange();

    return {
      years,
      conditions: filterConditions,
    };
  }

  private buildDiktukConditions(jenisDiktuk?: string[]) {
    return (
      jenisDiktuk?.map((jenis) => ({
        nama: { contains: jenis, mode: 'insensitive' as const },
      })) || []
    );
  }

  private updateCounts(target: any, gender: string | boolean): void {
    if (gender === 'LAKI_LAKI') {
      target.laki_laki.total++;
    } else if (gender === 'PEREMPUAN') {
      target.perempuan.total++;
    } else if (typeof gender === 'boolean') {
      gender ? target.filled.total++ : target.not_filled_yet.total++;
      return;
    } else {
      switch (gender) {
        case 'Aktif':
          target.active.total++;
          break;
        case 'MD (Meninggal Dunia)':
          target.dead.total++;
          break;
        case 'Lain-Lain (Taruna turun tingkat)':
          target.etc.total++;
          break;
        default:
          target.non_active.total++;
          break;
      }
      return;
    }
    target.total++;
  }

  private async getPoldaList(limit: number, page: number, sort: any) {
    const orderBy = sort['asal_rim_polda']
      ? { nama: sort['asal_rim_polda'] }
      : undefined;
    const commonWhere: any = {
      OR: [{ deleted_at: null, deleted_by: null, is_aktif: true }],
      nama: { startsWith: 'POLDA', mode: 'insensitive' },
    };

    const [total, list] = await Promise.all([
      this.prismaService.satuan.count({ where: commonWhere }),
      this.prismaService.satuan.findMany({
        where: commonWhere,
        select: { id: true, nama: true },
        orderBy,
        take: limit,
        skip: limit * (page - 1),
      }),
    ]);

    return { total, list };
  }

  private async getSiswaList(filters: any, jenisDiktukCondition: any[]) {
    const { conditions: filterConditions } =
      this.filterGelombangTahunFormat(filters);

    return this.prismaService.data_siswa.findMany({
      where: {
        deleted_at: null,
        OR: filterConditions,
        jenis_diktuk: jenisDiktukCondition.length
          ? { OR: jenisDiktukCondition }
          : undefined,
      },
      select: {
        jenis_kelamin: true,
        ta_rim_diktuk: true,
        gelombang_rim_diktuk: true,
        asal_rim_polda: { select: { nama: true } },
      },
    });
  }
}
