import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Post,
  Query,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { EPatmaWebService } from '../services/web.service';
import {
  RecapQueryDto,
  TemplateDto,
  UploadDataWrapperDto,
} from '../dto/web.dto';
import { Response } from 'express';
import { PaginationDto } from '../../../core/dtos';
import { SearchAndSortRoleDto } from '../../../access-management/roles/dto/roles.dto';

@Controller('patma')
export class EPatmaWebController {
  constructor(private readonly ePatmaWebService: EPatmaWebService) {}

  @Get('/statistics')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getStatistics(
    @Req() req: any,
    @Query('jenis_diktuk') jenisDiktuk: string[],
    @Query('filters') filters: string[],
  ) {
    return this.ePatmaWebService.getStatistics(req, jenisDiktuk, filters);
  }

  @Post('/upload')
  async uploadDataSiswa(@Req() req: any, @Body() body: UploadDataWrapperDto) {
    return this.ePatmaWebService.uploadDataSiswa(req, body);
  }

  @Get('/recap')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getRecruitment(@Req() req: any, @Query() query: RecapQueryDto) {
    const { portal } = query;

    switch (portal) {
      case 'data-kelulusan-rekrutmen':
        return this.ePatmaWebService.getRecapPortalOne(req, query);
      case 'data-pendidikan-pembentukan':
        return this.ePatmaWebService.getRecapPortalTwo(req, query);
    }
  }

  @Get('/kelola-data')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getKelolaData(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortRoleDto,
    @Query() search: TemplateDto,
  ) {
    return this.ePatmaWebService.getKelolaData(
      req,
      paginationData,
      searchAndSortData,
      search,
    );
  }

  @Get('/template-excel')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async createTemplateExcel(
    @Req() req: any,
    @Res() res: Response,
    @Query() body: TemplateDto,
  ) {
    const { buffer, filename } =
      await this.ePatmaWebService.createTemplateExcel(req, body);
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    res.end(buffer);
  }

  @Get('/file-manager')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getFiles(
    @Req() req: any,
    @Query() searchAndSortData: SearchAndSortRoleDto,
  ) {
    return this.ePatmaWebService.listFiles(req, searchAndSortData);
  }

  @Delete('/siswa')
  @UsePipes(new ValidationPipe({ transform: true }))
  async deleteDataSiswa(@Req() req: any, @Body() body: { ids: number[] }) {
    return this.ePatmaWebService.deleteDataSiswa(req, body);
  }
}
