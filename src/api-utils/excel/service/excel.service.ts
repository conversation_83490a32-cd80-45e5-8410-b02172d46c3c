import { HttpStatus, Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { PrismaService } from '../../prisma/service/prisma.service';
import {
  getUniqueDataToArrayObject,
  parsingValueOfFormulaExcel,
} from '../../../core/utils/common.utils';
import { LogsActivityService } from '../../logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class ExcelService {
  public static readonly TEMPLATE_NRP_KEY = 'nrp';
  public static readonly TEMPLATE_NAMA_KEY = 'nama';

  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  public isHeaderValid(header: string[], expectedHeader: string[]): boolean {
    return (
      header.length === expectedHeader.length &&
      header.every((value, index) => value === expectedHeader[index])
    );
  }

  public async extractToJson(
    req: any,
    filePath: string,
    isLowerCase: boolean,
  ): Promise<any> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    const worksheet = workbook.getWorksheet(1);
    const lastRow = worksheet.actualRowCount;
    const queryResult = [];
    const headerObject = await this.getHeaderFromExcel(filePath, isLowerCase);

    const { header, numberOfRow } = headerObject;
    const rows = worksheet.getRows(numberOfRow + 1, lastRow - 1);

    for (const row of rows) {
      const rowData: any = {};

      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        if (rowData[header[colNumber]] === undefined) {
          rowData[header[colNumber]] =
            cell.value || typeof cell.value === 'boolean'
              ? parsingValueOfFormulaExcel(cell.value)
              : null;
        }
      });

      queryResult.push(rowData);
    }

    const cleanedData = queryResult.filter((item) => {
      return Object.values(item).some(
        (value) => value !== null && value !== '',
      );
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EXCEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EXCEL_READ_EXCEL as ConstantLogType,
        message,
        cleanedData,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: cleanedData,
    };
  }

  public async getPersonelNamaLengkapByNrp(nrp: string) {
    const personel = await this.prisma.personel.findFirst({
      where: {
        nrp: nrp ?? null,
      },
      select: {
        nama_lengkap: true,
      },
    });
    return personel ? personel.nama_lengkap : '';
  }

  public async extractToJsonLowercase(
    req: any,
    filePath: string,
  ): Promise<any> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    const worksheet = workbook.getWorksheet(1);
    const queryResult = [];
    const headerFromExcel = await this.getHeaderFromExcel(filePath, true);
    const { header, numberOfRow } = headerFromExcel;

    const rows = worksheet.getRows(numberOfRow + 1, worksheet.rowCount);

    if (!rows) return;
    for (let rowNumber = 0; rowNumber <= rows.length; rowNumber++) {
      const row = rows[rowNumber - 1];
      if (
        !(
          Array.isArray(row?.values) &&
          (row.values as any[]).some((value) => value)
        )
      ) {
        continue;
      }

      const rowData: any = {};

      row.eachCell((cell, colNumber) => {
        rowData[header[`${colNumber}`]] = cell.value
          ? cell.value.toString().trim()
          : null;
      });

      rowData[ExcelService.TEMPLATE_NAMA_KEY] =
        await this.getPersonelNamaLengkapByNrp(
          rowData[ExcelService.TEMPLATE_NRP_KEY],
        );
      queryResult.push(rowData);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EXCEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EXCEL_READ_EXCEL as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: getUniqueDataToArrayObject(queryResult),
    };
  }

  public async getHeaderFromExcel(
    filepath: string,
    replaceKey: boolean = false,
  ): Promise<{
    header: Record<string, string>;
    numberOfRow: number;
  }> {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filepath);
      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) throw new Error('Worksheet tidak ditemukan!');

      const { headerRow, numberOfRow } = this.findFirstNonEmptyRow(worksheet);
      if (!headerRow)
        return {
          header: {},
          numberOfRow: 0,
        };

      const header: Record<string, string> = {};
      headerRow.eachCell((cell, colNumber) => {
        let key = replaceKey
          ? cell.value.toString().toLowerCase()
          : cell.value.toString();
        if (replaceKey) key = key.replaceAll(' ', '_');
        header[`${colNumber}`] = key;
      });

      return {
        header,
        numberOfRow,
      };
    } catch (error) {
      console.error(`Error membaca file Excel: ${error.message}`);
      return {
        header: {},
        numberOfRow: 0,
      };
    }
  }

  public findFirstNonEmptyRow(worksheet: ExcelJS.Worksheet) {
    for (let rowNumber = 1; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      if (
        (row.values as any[]).some(
          (value) => value && value.toString().trim() !== '',
        )
      ) {
        return {
          headerRow: row,
          numberOfRow: rowNumber,
        };
      }
    }
    return {
      headerRow: null,
      numberOfRow: 0,
    };
  }

  public async getHeader(filePath: string): Promise<any> {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    const worksheet = workbook.getWorksheet(1);
    let is_header = true;
    const header = [];
    worksheet.eachRow((row) => {
      if (is_header) {
        is_header = false;
        row.eachCell((cell) => {
          header.push(cell.value);
        });
      }
    });
    return header;
  }

  /**
   * Dynamically determines the depth of the headers by checking non-empty cells.
   * @param worksheet - The Excel worksheet.
   * @param listReserveWordNameHeaders - The list of name header for excel identification header.
   */
  public getHeaderDepth(
    worksheet: ExcelJS.Worksheet,
    listReserveWordNameHeaders: string[],
  ): number {
    let depth = 0;
    worksheet.eachRow((row, rowIndex) => {
      let isEmpty = true;
      row.eachCell((cell) => {
        const match = listReserveWordNameHeaders.find(
          (item) =>
            typeof item === 'string' &&
            typeof cell.value === 'string' &&
            item.toLowerCase() === cell.value.toLowerCase(),
        );
        if (cell.value && match) {
          isEmpty = false;
        }
      });
      if (!isEmpty) {
        depth++;
      } else {
        // Stop counting once we hit an empty row (assuming headers are at the top)
        return depth;
      }
    });
    return depth;
  }

  /**
   * Parses headers into a flat structure with dot notation keys.
   * Replaces spaces with underscores, dots with blank, and converts to lowercase.
   * @param worksheet - The Excel worksheet.
   * @param headerDepth - The number of rows used for headers.
   */
  public parseHeaders(worksheet: ExcelJS.Worksheet, headerDepth: number): any {
    const headerRows: string[][] = [];

    // Collect all header rows
    for (let i = 1; i <= headerDepth; i++) {
      const row = worksheet.getRow(i);
      const headers: string[] = [];

      // Loop through all columns, ensuring empty cells are accounted for
      for (let col = 1; col <= worksheet.columnCount; col++) {
        const cell = row.getCell(col); // Get the cell at column `col`
        headers[col - 1] = cell.value?.toString() || ''; // Column index starts at 0
      }

      headerRows.push(headers);
    }

    // Recursively build a flattened header map
    let headerMap: string[] = this.mergeHeaders(headerRows);
    return headerMap;
  }

  /**
   * Merges multiple rows of headers into a single array with concatenated child headers.
   * For each column, it combines values from multiple rows, using a dot (.) to separate parent and child headers.
   *
   * @param headers - A 2D array where each row represents a set of headers. The first row contains the main headers,
   *                  and subsequent rows contain child headers. Empty values are ignored.
   * @returns A single array of merged headers, with child headers concatenated to their parent headers.
   */
  public mergeHeaders(headers: string[][]): string[] {
    if (!headers || headers.length === 0) {
      return [];
    }

    return headers[0].map((header, index) => {
      // Start with the header from the first row (parent header)
      let combinedHeader = header
        ?.trim()
        .replace(/\s+/g, '_')
        .replace(/\./g, '')
        .replace(/\(|\)/g, '')
        .replace(/%/g, 'persen')
        .toLowerCase();

      // Iterate over all rows (starting from the second row)
      for (let i = 1; i < headers.length; i++) {
        let chidlHeader = headers[i][index]
          ?.trim()
          .replace(/\s+/g, '_')
          .replace(/\./g, '')
          .replace(/\(|\)/g, '')
          .replace(/%/g, 'persen')
          .toLowerCase();
        if (chidlHeader !== '' && chidlHeader !== combinedHeader) {
          // If there's a non-empty value, concatenate it with the parent header
          combinedHeader += `.${chidlHeader}`;
        }
      }

      return combinedHeader;
    });
  }

  /**
   * Maps data rows to the flat header structure.
   * @param worksheet - The Excel worksheet.
   * @param headerMap - The flat header map.
   * @param headerDepth - The number of rows used for headers.
   */
  public parseData(
    worksheet: ExcelJS.Worksheet,
    headerMap: any,
    headerDepth: number,
  ): any[] {
    const data: object[][] = [];
    worksheet.eachRow((row, rowIndex) => {
      if (rowIndex <= headerDepth) return; // Skip header rows
      const rowData: any[] = this.buildRowData(row, headerMap, 1);
      data.push(rowData);
    });
    return data;
  }

  /**
   * Maps a row of data to the flat header structure.
   * @param row - The current worksheet row.
   * @param headerMap - The flat header map.
   * @param colIndex - The current column index.
   */
  public buildRowData(
    row: ExcelJS.Row,
    headerMap: any,
    colIndex: number,
  ): any[] {
    const rowData: any[] = [];
    for (const key in headerMap) {
      // Leaf node (no further children)
      rowData.push(row.getCell(colIndex).value);
      colIndex++;
    }
    return rowData;
  }
}
