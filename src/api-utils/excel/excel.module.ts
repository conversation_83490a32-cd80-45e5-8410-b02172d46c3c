import { forwardRef, Module } from '@nestjs/common';
import { ExcelService } from './service/excel.service';
import { ExcelController } from './controller/excel.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [ExcelController],
  providers: [ExcelService],
  exports: [ExcelService],
})
export class ExcelModule {}
