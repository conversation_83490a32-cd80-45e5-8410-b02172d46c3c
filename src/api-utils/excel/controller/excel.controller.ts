import {
  Controller,
  Logger,
  Post,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ExcelService } from '../service/excel.service';
import { FileInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('excel')
@UseGuards(JwtAuthGuard)
export class ExcelController {
  private readonly logger = new Logger(ExcelController.name);

  constructor(private readonly excelService: ExcelService) {}

  @Post('extract')
  @UseInterceptors(FileInterceptor('file'))
  async extractExcelToJson(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(`Entering ${this.extractExcelToJson.name}`);

    const tmpFilePath = file.originalname; // nama file sementara
    fs.writeFileSync(tmpFilePath, file.buffer);
    const response = await this.excelService.extractToJson(
      req,
      tmpFilePath,
      false,
    );
    fs.unlinkSync(tmpFilePath);

    this.logger.log(
      `Leaving ${this.extractExcelToJson.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('extract-lowercase')
  @UseInterceptors(FileInterceptor('file'))
  async extractExcelToJsonLowerCase(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(`Entering ${this.extractExcelToJsonLowerCase.name}`);

    const tmpFilePath = file.originalname; // nama file sementara
    fs.writeFileSync(tmpFilePath, file.buffer);
    const response =
      await this.excelService.extractToJsonLowercase(req, tmpFilePath);
    fs.unlinkSync(tmpFilePath);

    this.logger.log(
      `Leaving ${this.extractExcelToJsonLowerCase.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
