import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SmtpService {
  private readonly logger = new Logger(SmtpService.name);

  constructor(
    private readonly mailService: MailerService,
    private readonly config: ConfigService,
  ) {}

  async sendMail(payload: ISendMailOptions) {
    payload = {
      ...payload,
      priority: 'high',
      headers: {
        'X-Priority': '1',
        Importance: 'high',
      },
    };
    try {
      return await this.mailService.sendMail(payload);
    } catch (exception) {
      const errorMessage =
        exception instanceof Error ? exception.message : 'Unknown error';
      const stackTrace = exception.stack || 'No stack trace available';

      this.logger.error(
        `Mail sending failed: ${errorMessage}. Stack Trace: ${stackTrace}. Payload: ${JSON.stringify(payload)}.`,
      );

      throw new Error(`Failed to send mail. Error: ${errorMessage}`);
    }
  }
}
