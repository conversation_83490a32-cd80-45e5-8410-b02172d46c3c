// import { MailerModule } from '@nestjs-modules/mailer';
// import { Module } from '@nestjs/common';
// import { ConfigModule, ConfigService } from '@nestjs/config';
// import { SmtpService } from './smtp.service';
// import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
// import { join } from 'path';
//
// @Module({
//   imports: [
//     MailerModule.forRootAsync({
//       imports: [ConfigModule],
//       inject: [ConfigService],
//       useFactory: (config: ConfigService) => ({
//         transport: {
//           host: config.get<string>('SMTP_HOST'),
//           port: config.get<number>('SMTP_PORT'),
//           secure: true,
//           auth: {
//             user: config.get<string>('SMTP_USERNAME'),
//             pass: config.get<string>('SMTP_PASSWORD'),
//           },
//           logger: false,
//           debug: false,
//         },
//         defaults: {
//           from: '"Satu SDM" <<EMAIL>>',
//         },
//         template: {
//           dir: join(__dirname, '..', '..', '..', 'assets', 'templates'), // where email templates are stored
//           adapter: new HandlebarsAdapter(), // Use Handlebars for templating
//           options: {
//             strict: true,
//           },
//         },
//       }),
//     }),
//   ],
//   providers: [SmtpService],
// })
// export class SmtpModule {}

import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SmtpService } from './service/smtp.service';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { join } from 'path';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        transport: {
          service: 'gmail',
          auth: {
            user: config.get<string>('SMTP_USERNAME'),
            pass: config.get<string>('SMTP_PASSWORD'),
          },
          secure: true,
          logger: true,
          debug: true,
        },
        defaults: {
          from: '"Satu SDM" <<EMAIL>>', // Set your email as the sender
        },
        template: {
          dir: join(__dirname, '..', '..', '..', 'assets', 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
  ],
  providers: [SmtpService],
})
export class SmtpModule {}
