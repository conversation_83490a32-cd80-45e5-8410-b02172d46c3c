import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import * as Firebase from 'firebase-admin';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { wait } from '../../../core/utils/common.utils';
import { FirebaseEnum } from '../../../core/enums/firebase.enum';
import { ISendNotificationMultiple } from '../../../core/interfaces/firebase.interface';

@Injectable()
export class FirebaseService {
  private firebaseApp: Firebase.app.App;

  constructor(private readonly prisma: PrismaService) {
    if (!Firebase.apps.length) {
      this.firebaseApp = Firebase.initializeApp({
        credential: Firebase.credential.cert({
          clientEmail: process.env.FCM_CLIENT_EMAIL,
          privateKey: process.env.FCM_PRIVATE_KEY.replace(/\\n/g, '\n'),
          projectId: process.env.FCM_PROJECT_ID,
        }),
      });
    } else {
      this.firebaseApp = Firebase.app();
    }
  }

  @OnEvent(FirebaseEnum.SEND_SINGLE)
  async sendNotification(
    token: string,
    title: string,
    body: string,
    imageUrl?: string,
    data?: { [key: string]: string },
  ) {
    try {
      const response = await this.firebaseApp.messaging().send({
        token,
        notification: {
          title,
          body,
          imageUrl: imageUrl?.length ? imageUrl : undefined,
        },
        data: data || {},
      });

      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      if (
        error?.errorInfo?.message ===
        'The registration token is not a valid FCM registration token'
      ) {
        this.prisma.users_device_token
          .deleteMany({
            where: {
              token,
            },
          })
          .then((result) => {
            console.log('Successfully deleted tokens:', result);
          })
          .catch((error) => {
            console.error('Error deleting tokens:', error);
          });
      }
    }
  }

  @OnEvent(FirebaseEnum.SEND_MULTIPLE)
  async sendNotificationMultiple(payload: ISendNotificationMultiple) {
    try {
      const limit = 100000;
      const batch = Math.ceil(payload.tokens.length / limit);

      let i = -1;
      while (++i < batch) {
        const dataBatch = payload.tokens.splice(0, limit);

        await this._proceedBatch({ ...payload, tokens: dataBatch });
        await wait(1000);
      }
    } catch (error) {
      console.log('Error sending message:', error);
    }
  }

  async sendTopicNotification(
    topic: string,
    title: string,
    body: string,
    imageUrl?: string,
    data?: { [key: string]: string },
  ) {
    try {
      const response = await this.firebaseApp.messaging().send({
        notification: {
          title,
          body,
          imageUrl,
        },
        topic,
        data: data || {},
      });

      return response;
    } catch (error) {
      console.log('Error sending topic : ', error);
    }
  }

  private async _proceedBatch(payload: ISendNotificationMultiple) {
    const tokens = [];
    const limitProceed = 1000;
    const batchProceed = Math.ceil(payload.tokens.length / limitProceed);

    let i = -1;
    while (++i < batchProceed) {
      tokens.push(payload.tokens.splice(0, limitProceed));
    }

    await Promise.all(
      tokens.map(async (item) => {
        await this._sendMultiNotification({ ...payload, tokens: item });
      }),
    );
  }

  private async _sendMultiNotification({
    tokens,
    body,
    imageUrl,
    title,
    type,
    data,
  }: ISendNotificationMultiple) {
    const user_ids = {};
    tokens.map((d) => (user_ids[d.user_id + ''] = 1));

    await this.prisma.notifikasi.createMany({
      data: Object.keys(user_ids).map((d) => ({
        title,
        type,
        users_id: +d,
        description: body,
        is_read: false,
        target: data?.link,
      })),
    });

    const response = await this.firebaseApp.messaging().sendEachForMulticast({
      tokens: tokens?.map((d) => d.token),
      notification: {
        title,
        body,
        imageUrl: imageUrl?.length ? imageUrl : undefined,
      },
      data: data || {},
    });

    if (response.failureCount) {
      const invalidTokens = response.responses
        .map((item, index) =>
          item.success === false &&
          (item.error.message.toLowerCase() ==
            'The registration token is not a valid FCM registration token'.toLowerCase() ||
            item.error.message.toLowerCase() ==
              'Requested entity was not found.'.toLowerCase())
            ? tokens[index].token
            : -1,
        )
        .filter((item) => item != -1) as string[];
      await this.prisma.users_device_token.deleteMany({
        where: {
          token: { in: invalidTokens },
        },
      });
    }

    console.log(
      `Successfully sent message to multiple tokens: { successCount = ${response.successCount}, failureCount = ${response.failureCount} }`,
    );
    return response;
  }
}
