import { Injectable, InternalServerErrorException } from '@nestjs/common';
import * as Minio from 'minio';

@Injectable()
export class MinioService {
  private readonly s3Minio: Minio.Client;
  readonly defaultBucketName: string;
  readonly defaultPath: string;

  constructor() {
    this.s3Minio = new Minio.Client({
      endPoint: process.env.MINIO_ENDPOINT,
      useSSL: process.env.MINIO_PROTOCOL === 'https',
      accessKey: process.env.MINIO_ACCESS_KEY,
      secretKey: process.env.MINIO_SECRET_KEY,
    });

    this.defaultBucketName = process.env.MINIO_BUCKET_NAME;
    this.defaultPath = process.env.MINIO_PATH_FILE;
  }

  async uploadFiles(files: Express.Multer.File[]) {
    return await Promise.all(
      files.map(async (file) => {
        const uploaded = await this.uploadFile(file);
        delete file.buffer;
        delete file.fieldname;

        if (!uploaded.ETag) return { rawFile: file };

        return {
          rawFile: file,
          uploaded,
        };
      }),
    );
  }

  /**
   * upload file into storage service.
   * @param file - the file for submitted into service.
   */
  public async uploadFilesWithoutBufferAndFilename(file: Express.Multer.File) {
    try {
      const uploaded = await this.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };

      return {
        rawFile: file,
        uploaded,
      };
    } catch (err) {
      throw new InternalServerErrorException('Failed upload file : ' + err);
    }
  }

  async uploadFile(file: Express.Multer.File) {
    const { originalname, buffer } = file;

    const filename = `${Date.now().toString()}-${originalname}`;
    const pathFileS3 = `${process.env.MINIO_PATH_FILE}${filename}`;
    const upload = await this.s3Minio.putObject(
      process.env.MINIO_BUCKET_NAME,
      pathFileS3,
      buffer,
    );
    const url = `${process.env.MINIO_PROTOCOL}://${process.env.MINIO_ENDPOINT}/${process.env.MINIO_BUCKET_NAME}${pathFileS3}`;
    return {
      Key: pathFileS3,
      ETag: upload.etag,
      Location: url,
      filename: filename,
    };
  }

  async uploadFileWithNRP(file: Express.Multer.File, nrp: String) {
    const { originalname, buffer } = file;

    const filename = `${Date.now().toString()}-${originalname}`;
    const pathFileWithNRP = `${process.env.MINIO_PATH_FILE}${nrp}/${filename}`;
    const upload = await this.s3Minio.putObject(
      process.env.MINIO_BUCKET_NAME,
      pathFileWithNRP,
      buffer,
    );
    const url = `${process.env.MINIO_PROTOCOL}://${process.env.MINIO_ENDPOINT}/${process.env.MINIO_BUCKET_NAME}${pathFileWithNRP}`;

    return {
      Key: pathFileWithNRP,
      ETag: upload.etag,
      Location: url,
      filename: filename,
    };
  }

  async uploadToMinio(buffer: Buffer, originalname: string) {
    const filename = `${Date.now().toString()}-${originalname}`;
    const pathFileS3 = `${process.env.MINIO_PATH_FILE}${filename}`;
    const upload = await this.s3Minio.putObject(
      process.env.MINIO_BUCKET_NAME,
      pathFileS3,
      buffer,
    );
    const url = `${process.env.MINIO_PROTOCOL}://${process.env.MINIO_ENDPOINT}/${process.env.MINIO_BUCKET_NAME}${pathFileS3}`;
    return {
      Key: pathFileS3,
      ETag: upload.etag,
      Location: url,
      filename: filename,
    };
  }

  async checkFileExist(bucketName: string, filePath: string): Promise<string> {
    try {
      await this.s3Minio.statObject(bucketName, filePath);

      const normalizedFilePath = filePath.replace(/\/+/g, '/');
      const expires = 7200;
      return await this.s3Minio.presignedGetObject(
        bucketName,
        normalizedFilePath,
        expires,
      );
    } catch (err) {
      const defaultImagePath = 'personel/personel-bg.jpg';
      const expires = 7200;

      try {
        return await this.s3Minio.presignedGetObject(
          bucketName,
          defaultImagePath,
          expires,
        );
      } catch (defaultErr) {
        console.error(
          'Error generating signed URL for default image:',
          defaultErr,
        );

        return `${process.env.MINIO_PROTOCOL}://${process.env.MINIO_ENDPOINT}/${bucketName}${defaultImagePath}`;
      }
    }
  }

  async convertFileKeyToURL(bucketName, filePath) {
    if (!filePath) return null;

    try {
      await this.s3Minio.statObject(bucketName, filePath);

      const expires = 3600;
      const signedUrl = await this.s3Minio.presignedGetObject(
        bucketName,
        filePath,
        expires,
      );

      if (!filePath) {
        return null;
      }
      return signedUrl;
    } catch (e) {
      return null;
    }
  }

  getClient() {
    return this.s3Minio;
  }

  /**
   * upload file into storage service.
   * @param file - the file for submitted into service.
   */
  public async uploadFilesWithNRP(file: Express.Multer.File, nrp: string) {
    try {
      const uploaded = await this.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };

      return {
        rawFile: file,
        uploaded,
      };
    } catch (err) {
      throw new InternalServerErrorException('Failed upload file : ' + err);
    }
  }
}
