import {
  Injectable,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnModuleInit,
} from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService
  extends PrismaClient<Prisma.PrismaClientOptions, 'query'>
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(PrismaService.name);
  private static readonly ENV: string = process.env.APP_ENV;

  constructor() {
    super({
      log: getLogLevel(PrismaService.ENV),
      errorFormat: 'colorless',
    });

    this.$on('query' as any, (e: Prisma.QueryEvent) => {
      this.logger.log(`Params: ${e.params}`);
      this.logger.log(`Duration: ${e.duration} ms`);
    });
  }

  async onModuleInit() {
    this.logger.log('Prisma connection process starting...');

    const environment = process.env.APP_ENV || 'local';
    this.logger.log(`Environment: ${environment}`);

    try {
      // Attempt to connect to Prisma
      await this.$connect();
      this.logger.log('Prisma connected successfully');
    } catch (error) {
      this.logger.error('Prisma connection failed', error.stack);
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}

const getLogLevel = (env: string) => {
  const logLevel = [];
  switch (env) {
    case 'local':
      logLevel.push('query');
      logLevel.push('info');
      logLevel.push('warn');
      logLevel.push('error');
      break;
    case 'development':
    case 'staging':
    case 'production':
      logLevel.push('info');
    default:
      break;
  }
  return logLevel;
};
