import {
  IsBoolean,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>ptional,
  IsString,
} from 'class-validator';
import { PaginationDto } from '../../../core/dtos/pagination.dto';

export class GetAllNotifikasiDto extends PaginationDto {
  @IsOptional()
  is_read: string;
}

export class CreateNotifikasiDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsNumber()
  @IsNotEmpty()
  users_id: number;

  @IsString()
  @IsOptional()
  fcm_token?: string;

  @IsBoolean()
  @IsNotEmpty()
  is_read: boolean;

  @IsBoolean()
  @IsNotEmpty()
  with_push_notif: boolean;
}
