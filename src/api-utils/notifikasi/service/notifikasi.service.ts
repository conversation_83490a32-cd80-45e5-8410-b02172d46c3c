import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  ICreateNotifikas,
  IFindByUser,
} from '../../../core/interfaces/notifikasi.interface';
import { ISendNotification } from '../../../core/interfaces/firebase.interface';
import { FirebaseEnum } from '../../../core/enums/firebase.enum';
import { LogsActivityService } from '../../logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class NotifikasiService {
  constructor(
    private prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, params: ICreateNotifikas) {
    const {
      with_push_notif,
      users_id,
      title,
      description,
      type,
      is_read,
      fcm_token,
    } = params;

    if (with_push_notif) {
      const user = await this.prisma.users_device_token.findFirst({
        where: { user_id: users_id },
      });

      if (user?.token) {
        const mock: ISendNotification = {
          title,
          data: {},
          body: description,
          type: 'NOTIFIKASI',
          token: { token: user.token, user_id: user.user_id },
        };
        this.eventEmitter.emit(FirebaseEnum.SEND_SINGLE, mock);
        // await this.firebaseService.sendNotification(
        //   user.token,
        //   title,
        //   description,
        //   '',
        //   {},
        // );
      } else {
        console.warn(`Device token not found for user_id: ${users_id}`);
      }
    }

    const queryResult = await this.prisma.notifikasi.create({
      data: {
        title,
        type,
        description,
        is_read,
        users_id,
        fcm_token,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NOTIFIKASI_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async findByUser(req: any, params: IFindByUser) {
    const { user_id, is_read, take, page } = params;

    const where: any = {
      users_id: user_id,
    };

    if (is_read) {
      where.is_read = is_read === '1';
    }

    const [data, count] = await Promise.all([
      this.prisma.notifikasi.findMany({
        where,
        orderBy: {
          created_at: 'desc',
        },
        take: +take,
        skip: +take * (+page - 1),
      }),
      this.prisma.notifikasi.count({ where }),
    ]);

    const queryResult = { data, max_page: Math.ceil(count / +take) };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NOTIFIKASI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async countUnread(req: any, user_id: number) {
    const queryResult = await this.prisma.notifikasi.count({
      where: { users_id: user_id, is_read: false },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NOTIFIKASI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async markAsRead(req: any, id: bigint) {
    try {
      const queryResult = await this.prisma.notifikasi.update({
        where: { id: BigInt(id) },
        data: { is_read: true },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.NOTIFIKASI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.NOTIFIKASI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Notifikasi tidak ditemukan.`);
      }
      throw error;
    }
  }

  async readAll(req: any, user_id: number) {
    const queryResult = await this.prisma.notifikasi.updateMany({
      where: { users_id: user_id, is_read: false },
      data: { is_read: true },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NOTIFIKASI_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
