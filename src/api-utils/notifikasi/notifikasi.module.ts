import { forwardRef, Module } from '@nestjs/common';
import { NotifikasiService } from './service/notifikasi.service';
import { NotifikasiController } from './controller/notifikasi.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [NotifikasiController],
  providers: [NotifikasiService],
  exports: [NotifikasiService],
})
export class NotifikasiModule {}
