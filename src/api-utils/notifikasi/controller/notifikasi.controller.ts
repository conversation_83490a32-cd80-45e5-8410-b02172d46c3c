import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { NotifikasiService } from '../service/notifikasi.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import {
  CreateNotifikasiDto,
  GetAllNotifikasiDto,
} from '../dto/notifikasi.dto';

@Controller('notifikasi')
@UseGuards(JwtAuthGuard)
export class NotifikasiController {
  private readonly logger = new Logger(NotifikasiController.name);

  constructor(private readonly notifikasiService: NotifikasiService) {}

  @Get()
  @HttpCode(200)
  async findByUser(@Req() req: any, @Query() query: GetAllNotifikasiDto) {
    this.logger.log(
      `Entering ${this.findByUser.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.notifikasiService.findByUser(req, {
      user_id: req['user']['id'],
      is_read: query.is_read,
      take: query.limit,
      page: query.page,
    });
    this.logger.log(
      `Leaving ${this.findByUser.name} with query: ${JSON.stringify(query)} and response: ${response}`,
    );

    return response;
  }

  @Get('unread-count')
  @HttpCode(200)
  async countUnread(@Req() req: any) {
    this.logger.log(`Entering ${this.countUnread.name}`);
    const response = await this.notifikasiService.countUnread(
      req,
      req['user']['id'],
    );
    this.logger.log(
      `Leaving ${this.countUnread.name} with response: ${response}`,
    );
    return response;
  }

  @Put(':id/read')
  @HttpCode(200)
  async markAsRead(@Req() req: any, @Param('id') id: bigint) {
    this.logger.log(`Entering ${this.markAsRead.name} with id: ${id}`);
    const response = await this.notifikasiService.markAsRead(req, id);
    this.logger.log(
      `Leaving ${this.markAsRead.name} with id: ${id} and response: ${response}`,
    );
    return response;
  }

  @Post('read-all')
  @HttpCode(200)
  async readAll(@Req() req: any) {
    this.logger.log(`Entering ${this.readAll.name}`);
    const response = await this.notifikasiService.readAll(
      req,
      req['user']['id'],
    );
    this.logger.log(`Leaving ${this.readAll.name} with response: ${response}`);
    return response;
  }

  @Post()
  @HttpCode(200)
  async create(
    @Req() req: any,
    @Body() createNotifikasiDto: CreateNotifikasiDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createNotifikasiDto)}`,
    );
    const response = await this.notifikasiService.create(
      req,
      createNotifikasiDto,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createNotifikasiDto)} and response: ${response}`,
    );

    return response;
  }
}
