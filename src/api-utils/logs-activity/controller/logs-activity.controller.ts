import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  Logger,
  Param,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { GetAllLogsActivityDto } from '../dto/get-all-logs-activity.dto';
import { LogsActivityService } from '../service/logs-activity.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto } from 'src/core/dtos';
import { SearchAndSortLogDTO } from '../dto/logs-activity.dto';

@Controller('logs-activity')
@UseGuards(JwtAuthGuard)
export class LogsActivityController {
  private readonly logger = new Logger(LogsActivityController.name);

  constructor(private readonly logsActivityService: LogsActivityService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortLogDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.logsActivityService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response data: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getLogList(@Req() req: any, @Query() query: GetAllLogsActivityDto) {
    this.logger.log(
      `Entering ${this.getLogList.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.logsActivityService.getListLogs(req, query);
    this.logger.log(
      `Leaving ${this.getLogList.name} with query: ${JSON.stringify(query)} and response data: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDetail(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getDetail.name} with id: ${id}`);
    const response = await this.logsActivityService.getDetail(req, +id);
    this.logger.log(
      `Leaving ${this.getDetail.name} with id: ${id} and response data: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
