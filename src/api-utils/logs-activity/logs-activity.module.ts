import { forwardRef, Module } from '@nestjs/common';
import { LogsActivityService } from './service/logs-activity.service';
import { LogsActivityController } from './controller/logs-activity.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';

@Module({
  imports: [PrismaModule, forwardRef(() => UsersModule)],
  controllers: [LogsActivityController],
  providers: [LogsActivityService],
  exports: [LogsActivityService],
})
export class LogsActivityModule {}
