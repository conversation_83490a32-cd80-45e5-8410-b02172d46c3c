import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import * as dayjs from 'dayjs';
import { PaginationDto } from 'src/core/dtos';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { GetAllLogsActivityDto } from '../dto/get-all-logs-activity.dto';
import { SearchAndSortLogDTO } from '../dto/logs-activity.dto';
import { ILog } from '../../../core/interfaces/log.interface';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../../core/utils/search.utils';

@Injectable()
export class LogsActivityService {
  private readonly logger = new Logger(LogsActivityService.name);

  constructor(private prisma: PrismaService) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortLogDTO,
  ) {
    const { limit, page } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama_operator: { field: 'user.personel.nama_lengkap', type: 'string' },
      nrp: { field: 'user.personel.nrp', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, results] = await this.prisma.$transaction([
      this.prisma.users_role.count({ where }),
      this.prisma.users_role.findMany({
        select: {
          id: true,
          user: {
            select: {
              personel: {
                select: {
                  nama_lengkap: true,
                  nrp: true,
                  email: true,
                  uid: true,
                },
              },
              logs_activity: {
                select: { activity: true, created_at: true, id: true },
                orderBy: { created_at: 'desc' },
                take: 1,
              },
            },
          },
          role: {
            select: {
              nama: true,
              bagian: { select: { nama: true } },
              level: { select: { nama: true } },
            },
          },
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    // const [totalData, results] = await this.prisma.$transaction([
    //   this.prisma.logs_activity.count({ where }),
    //   this.prisma.logs_activity.findMany({
    //     select: { id: true, activity: true, created_at: true },
    //     take: limit,
    //     skip: limit * (page - 1),
    //     orderBy: orderBy,
    //     where: where,
    //   }),
    // ]);

    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: Array.from(Object.values(results)),
      page: page,
      totalData: totalData,
      totalPage: totalPage,
    };
  }

  // async getList(
  //   req,
  //   paginationData: PaginationDto,
  //   searchandsortData: SearchAndSortLogDTO,
  // ) {
  //   const { sort_column, sort_desc, search } = searchandsortData;
  //   const limit = paginationData.limit || 100;
  //   const page = paginationData.page || 1;
  //
  //   const where = { deleted_at: null } satisfies Prisma.users_roleWhereInput;
  //
  //   if (search) {
  //     where['OR'] = [
  //       {
  //         user: {
  //           personel: {
  //             nama_lengkap: { contains: search, mode: 'insensitive' },
  //           },
  //         },
  //       },
  //       {
  //         user: {
  //           personel: {
  //             nrp: { contains: search, mode: 'insensitive' },
  //           },
  //         },
  //       },
  //     ];
  //   }
  //
  //   if (searchandsortData.bagian) {
  //     where['role'] = {
  //       bagian: {
  //         id: +searchandsortData.bagian,
  //       },
  //     };
  //   }
  //
  //   if (searchandsortData.level) {
  //     where['level'] = { id: +searchandsortData.level };
  //   }
  //
  //   const orderBy = [];
  //   const sort = ['asc', 'desc'].includes(sort_desc) ? sort_desc : 'asc';
  //   switch (sort_column) {
  //     case 'nama_operator':
  //       orderBy.push({ user: { personel: { nama_lengkap: sort } } });
  //       break;
  //     case 'nrp':
  //       orderBy.push({ user: { personel: { nrp: sort } } });
  //       break;
  //     case 'role':
  //       orderBy.push({ role: { nama: sort } });
  //       break;
  //     case 'level':
  //       orderBy.push({ level: { nama: sort } });
  //       break;
  //     case 'bagian':
  //       orderBy.push({ role: { bagian: { nama: sort } } });
  //       break;
  //     case 'email':
  //       orderBy.push({ user: { personel: { email: sort } } });
  //       break;
  //     default:
  //       orderBy.push({ created_at: 'desc' });
  //       break;
  //   }
  //
  //   const [totalData, user] = await this.prisma.$transaction([
  //     this.prisma.users_role.count({
  //       where: where,
  //     }),
  //     this.prisma.users_role.findMany({
  //       select: {
  //         id: true,
  //         user: {
  //           select: {
  //             personel: {
  //               select: {
  //                 nama_lengkap: true,
  //                 nrp: true,
  //                 email: true,
  //                 uid: true,
  //               },
  //             },
  //             logs_activity: {
  //               select: { activity: true, created_at: true, id: true },
  //               orderBy: { created_at: 'desc' },
  //               take: 1,
  //             },
  //           },
  //         },
  //         role: { select: { nama: true, bagian: { select: { nama: true } } } },
  //         level: { select: { nama: true } },
  //       },
  //       take: +limit,
  //       skip: +limit * (+page - 1),
  //       orderBy: orderBy,
  //       where: where,
  //     }),
  //   ]);
  //
  //   const totalPage = Math.ceil(totalData / limit);
  //
  //   const queryResult = user.map((item) => {
  //     return {
  //       id: Number(item.user.logs_activity[0]?.id),
  //       uid: item.user?.personel?.uid,
  //       nama_operator: item.user?.personel?.nama_lengkap ?? '',
  //       nrp: item.user?.personel?.nrp ?? '',
  //       role: item.role?.nama ?? '',
  //       level: item.level?.nama ?? '',
  //       bagian: item.role?.bagian?.nama ?? '',
  //       email: item.user?.personel?.email ?? '',
  //       aktifitas: item.user.logs_activity[0]?.activity || '',
  //       created_at: item.user.logs_activity[0]?.created_at || '',
  //     };
  //   });
  //
  //   const message = convertToLogMessage(
  //     ConstantLogStatusEnum.SUCCESS,
  //     ConstantLogTypeEnum.READ_LOG_TYPE,
  //     ConstantLogDataTypeEnum.LIST,
  //     ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
  //   );
  //
  //   return {
  //     statusCode: HttpStatus.OK,
  //     message,
  //     data: queryResult,
  //     page: page,
  //     totalData: totalData,
  //     totalPage: totalPage,
  //   };
  // }

  async getListLogs(req: any, payload: GetAllLogsActivityDto) {
    const { keyword, startDate, endDate, page, limit } = payload;

    const where: Prisma.logs_activityWhereInput = {};

    if (startDate) {
      if (!dayjs(startDate).isValid()) {
        throw new BadRequestException('Format Start date tidak valid');
      }
      where.created_at ||= {};
      where.created_at = {
        ...(where.created_at as Record<string, any>),
        gte: new Date(startDate),
      };
    }
    if (endDate) {
      if (!dayjs(endDate).isValid()) {
        throw new BadRequestException('Format End date tidak valid');
      }
      where.created_at ||= {};
      where.created_at = {
        ...(where.created_at as Record<string, any>),
        lte: new Date(endDate),
      };
    }
    if (keyword) {
      where.OR = [
        { activity: { contains: keyword, mode: 'insensitive' } },
        {
          user: {
            personel: {
              nama_lengkap: { contains: keyword, mode: 'insensitive' },
            },
          },
        },
        { user: { personel: { nrp: { contains: keyword } } } },
      ];
    }

    const [totalData, data] = await Promise.all([
      this.prisma.logs_activity.count({ where }),
      this.prisma.logs_activity.findMany({
        where,
        select: {
          activity: true,
          created_at: true,
          id: true,
          user_id: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: { id: 'desc' },
      }),
    ]);

    const ids = data.map((item) => item.user_id);
    const users = await this.prisma.users_role.findMany({
      where: { users_id: { in: ids } },
      select: {
        users_id: true,
        user: {
          select: {
            personel: {
              select: { nama_lengkap: true, nrp: true, email: true },
            },
          },
        },
        role: { select: { nama: true, bagian: { select: { nama: true } } } },
        level: { select: { nama: true } },
      },
    });

    const userMap = new Map();
    users.map((d) => userMap.set(d.users_id, d));

    data.forEach((item) => {
      const userRole = userMap.get(item.user_id);

      item['nama_operator'] = userRole.user?.personel?.nama_lengkap ?? '';
      item['nrp'] = userRole.user?.personel?.nrp ?? '';
      item['role'] = userRole.role?.nama ?? '';
      item['level'] = userRole.level?.nama ?? '';
      item['bagian'] = userRole.role?.bagian?.nama ?? '';
      item['email'] = userRole.user?.personel?.email ?? '';
    });

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = { data, page, totalPage, totalData };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    );
    await this.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LOGS_ACTIVITY_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      ...queryResult,
    };
  }

  async getDetail(req: any, id: number) {
    const log = await this.prisma.logs_activity.findFirst({ where: { id } });

    const userRole = await this.prisma.users_role.findFirst({
      where: { users_id: log.user_id },
      select: {
        user: {
          select: {
            personel: {
              select: { nama_lengkap: true, nrp: true, email: true },
            },
          },
        },
        role: { select: { nama: true, bagian: { select: { nama: true } } } },
        level: { select: { nama: true } },
      },
    });

    const queryResult = {
      ...log,
      nama_operator: userRole.user?.personel?.nama_lengkap ?? '',
      nrp: userRole.user?.personel?.nrp ?? '',
      role: userRole.role?.nama ?? '',
      level: userRole.level?.nama ?? '',
      bagian: userRole.role?.bagian?.nama ?? '',
      email: userRole.user?.personel?.email ?? '',
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    );
    await this.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LOGS_ACTIVITY_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async addLogsActivity(logData: ILog) {
    const {
      user_id,
      activity,
      payload,
      message,
      headers,
      url,
      method,
      module,
    } = logData;

    const data = {
      user_id,
      activity,
      detail: JSON.stringify({
        message,
        url,
        method,
        module,
        headers,
        payload,
      }),
      created_at: new Date(),
    };

    try {
      await this.prisma.logs_activity.create({ data });
      this.logger.log(
        `Success ${this.addLogsActivity.name} dengan data ${JSON.stringify(data)}`,
      );
    } catch (error) {
      this.logger.error(
        `Error ${this.addLogsActivity.name} dengan data ${JSON.stringify(data)} karena ${error.toString()}`,
      );
    }
  }
}
