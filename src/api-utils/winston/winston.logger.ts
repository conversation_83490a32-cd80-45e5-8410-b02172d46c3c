import { WinstonModuleOptions } from 'nest-winston';
import * as winston from 'winston';
import * as path from 'path';
import 'winston-daily-rotate-file';
console.log(
  'logs directory : ',
  path.join(__dirname, '../../../logs/app-%DATE%.log'),
);
export const winstonConfig: WinstonModuleOptions = {
  transports: [
    // 🔁 Daily log rotation transport
    new winston.transports.DailyRotateFile({
      filename: path.join(__dirname, '../../../../logs/app-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m', // Rotate if larger than 20MB
      maxFiles: '14d', // Keep logs for 14 days
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DDTHH:mm:ss.SSSZ' }),
        winston.format.errors({ stack: true }),
        winston.format.metadata({
          fillExcept: ['message', 'level', 'timestamp'],
        }),
        winston.format.json(),
      ),
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
  ],
};
