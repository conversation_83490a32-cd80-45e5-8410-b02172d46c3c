import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

@Injectable()
export class AxiosService {
  private readonly axiosInstances: { [key: string]: AxiosInstance } = {};
  private readonly logger = new Logger(AxiosService.name);

  // Add a new base URL configuration or use an existing one
  private getAxiosInstance(baseURL: string): AxiosInstance {
    if (!this.axiosInstances[baseURL]) {
      this.axiosInstances[baseURL] = axios.create({
        baseURL: baseURL,
      });
    }
    return this.axiosInstances[baseURL];
  }

  // Reusable GET method with multiple base URL support
  async get<T>(
    url: string,
    baseURL: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const axiosInstance = this.getAxiosInstance(baseURL);
      const response = await axiosInstance.get<T>(url, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  // Reusable POST method with multiple base URL support
  async post<T>(
    url: string,
    data: any,
    baseURL: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const axiosInstance = this.getAxiosInstance(baseURL);
      const response = await axiosInstance.post<T>(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  // Reusable PUT method with multiple base URL support
  async put<T>(
    url: string,
    data: any,
    baseURL: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const axiosInstance = this.getAxiosInstance(baseURL);
      const response = await axiosInstance.put<T>(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error);
    }
  }

  // Handle errors from the request
  private handleError(error: any): void {
    if (error.response) {
      this.logger.error('Error response:', error.response.data);
    } else if (error.request) {
      this.logger.error('Error request:', error.request);
    } else {
      this.logger.error('Error message:', error.message);
    }
    throw new Error('HTTP Request failed');
  }
}
