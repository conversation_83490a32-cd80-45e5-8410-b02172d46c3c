import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  IPangkatInterface,
  IPangkatPersonelInterface,
} from '../../core/interfaces/kenaikan-pangkat.interface';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../core/utils/search.utils';
import { PaginationDto } from '../../core/dtos';
import { SearchAndSortRoleDto } from '../../access-management/roles/dto/roles.dto';

@Injectable()
export class PendataanPerumahanService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async findAll(
      req: any,
      paginationData: PaginationDto,
      searchAndSortData: SearchAndSortRoleDto,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
              searchAndSortData;

    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const columnMapping: IColumnMapping = {
      nama_personel: {
        field: 'nama_personel',
        type: 'string',
      },
      jenis_kelamin: { field: 'jenis_kelamin', type: 'string' },
      nama_pangkat: { field: 'nama_pangkat', type: 'string' },
      nama_satuan: { field: 'nama_satuan', type: 'date' },
      status_kepemilikan: { field: 'status_kepemilikan', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.pendataan_perumahan.count({ where }),
      this.prisma.pendataan_perumahan.findMany({
        select: {
          id: true,
          nama_personel: true,
          jenis_kelamin: true,
          nama_pangkat: true,
          nama_satuan: true,
          status_kepemilikan: true,
          lokasi_kepemilikan: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        where,
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_PANGKAT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findOne(req: any, id: number) {
    const personel = await this.prisma.personel.findFirst({
      where: { id, deleted_at: null },
      include: {
        status_aktif: true,
        jabatan_personel: {
          select: {
            tmt_jabatan: true,
            jabatans: {
              select: {
                nama: true
              },
              include: {
                satuan: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        pangkat_personel: {
          include: {
            pangkat: true,
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!personel) {
      throw new NotFoundException(`Personel tidak ditemukan`);
    }

    let kenaikanPangkatSelanjutnya = [];

    const tingkat_id = personel.pangkat_personel[0]?.pangkat?.tingkat_id;

    if (tingkat_id) {
      const nextPromotion = await this.prisma.pangkat.findMany({
        where: { tingkat_id },
        orderBy: { id: 'desc' },
        select: {
          id: true,
          nama: true,
          nama_singkat: true,
        },
      });

      if (nextPromotion) {
        kenaikanPangkatSelanjutnya = nextPromotion.reverse();
      }
    }

    const mappedPromotion = this.generatePromotionHirarcy(
      personel.pangkat_personel[0],
      kenaikanPangkatSelanjutnya,
    );

    const queryResult = {
      ...personel,
      kenaikan_pangkat_selanjutnya: mappedPromotion,
    };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.KENAIKAN_PANGKAT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  generatePromotionHirarcy(
    currentRank: IPangkatPersonelInterface,
    promotionRanks: IPangkatInterface[],
  ) {
    if (!currentRank || !promotionRanks.length) return [];

    const tmtDate = new Date(currentRank.tmt);
    const tmtYear = tmtDate.getFullYear();
    const tmtDayMonth = tmtDate.toISOString().slice(5);
    const currentRankIndex = promotionRanks.findIndex(
      (rank) => rank.nama === currentRank.pangkat.nama,
    );

    const result = promotionRanks.map((rank, index) => {
      const promotionYear = tmtYear + (index - currentRankIndex) * 4;
      return {
        ...rank,
        promotion_year: `${promotionYear}-${tmtDayMonth}`,
        status: index <= currentRankIndex ? 1 : 0, //
      };
    });

    return result.reverse();
  }
}
