import { forwardRef, Module } from '@nestjs/common';
import { PendataanPerumahanService } from './service/pendataan-perumahan.service';
import { PendataanPerumahanController } from './controller/pendataan-perumahan.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PendataanPerumahanController],
  providers: [PendataanPerumahanService],
})
export class PendataanPerumahanModule {}
