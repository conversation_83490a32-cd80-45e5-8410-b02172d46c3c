import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PendataanPerumahanService } from '../service/pendataan-perumahan.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Permission } from '../../core/decorators';

@Controller('pendataan-perumahan')
@UseGuards(JwtAuthGuard)
export class PendataanPerumahanController {
  private readonly logger = new Logger(PendataanPerumahanController.name);

  constructor(
    private readonly pendataanPerumahanService: PendataanPerumahanService,
  ) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndsortData: SearchAndSortDTO,
  ) {
    console.log("PEPEPEPEPEP")
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndsortData)}`,
    );
    const response = await this.pendataanPerumahanService.findAll(
      req,
      paginationData,
      searchAndsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
