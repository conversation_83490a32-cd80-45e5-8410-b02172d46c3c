import { Module } from '@nestjs/common';
import { PenghargaanService } from './service/penghargaan.service';
import { PenghargaanController } from './controller/penghargaan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [PenghargaanController],
  providers: [PenghargaanService],
})
export class PenghargaanModule {}
