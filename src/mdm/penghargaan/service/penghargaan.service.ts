import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePenghargaanDto,
  UpdatePenghargaanDto,
} from '../dto/penghargaan.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class PenghargaanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, createPenghargaanDto: CreatePenghargaanDto) {
    try {
      await this._validateSameName(createPenghargaanDto.nama);

      const queryResult = await this.prisma.penghargaan.create({
        data: {
          nama: createPenghargaanDto.nama,
          tingkat_id: Number(createPenghargaanDto.tingkat),
          is_aktif: Boolean(createPenghargaanDto.is_aktif) ?? true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'number';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        tingkat: { field: 'tingkat_id', type: 'number' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.penghargaan.count({
          where,
        }),
        this.prisma.penghargaan.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
            penghargaan_tingkat: { select: { id: true, nama: true } },
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.penghargaan.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
          is_aktif: true,
          penghargaan_tingkat: { select: { id: true, nama: true } },
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Data penghargaan tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updatePenghargaanDto: UpdatePenghargaanDto,
  ) {
    try {
      const penghargaan = await this.prisma.penghargaan.findFirst({
        where: { id, deleted_at: null },
      });
      if (!penghargaan) {
        throw new NotFoundException(`Data penghargaan tidak ditemukan`);
      }

      if (updatePenghargaanDto.nama !== penghargaan.nama) {
        await this._validateSameName(updatePenghargaanDto.nama);
      }

      const queryResult = await this.prisma.penghargaan.update({
        where: { id },
        data: {
          nama: updatePenghargaanDto.nama,
          tingkat_id:
            Number(updatePenghargaanDto.tingkat) || penghargaan.tingkat_id,
          updated_at: new Date(),
          is_aktif: Boolean(updatePenghargaanDto.is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(req: any, id: number) {
    try {
      const penghargaan = await this.prisma.penghargaan.findFirst({
        where: { id, deleted_at: null },
      });
      if (!penghargaan) {
        throw new NotFoundException(`Data penghargaan tidak ditemukan`);
      }

      const queryResult = await this.prisma.penghargaan.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async penghargaanPersonel(req: any, uid: string) {
    try {
      const getPenghargaanPersonel = await this.prisma.penghargaan_personel.findMany({
        select: {
          tgl_penghargaan: true,
          penghargaan: {
            select: {
              nama: true,
            },
          },
          penghargaan_tingkat: {
            select: {
              nama: true,
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tgl_penghargaan: 'desc',
        },
      });

      const queryResult = getPenghargaanPersonel.map((item) => {
        return {
          tmt: item?.tgl_penghargaan,
          penghargaan: item.penghargaan.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  private async _validateSameName(name: string) {
    try {
      const sameNameExist = await this.prisma.penghargaan.count({
        where: {
          nama: { startsWith: name, mode: 'insensitive' },
          deleted_at: null,
        },
      });
      if (sameNameExist) {
        throw new HttpException(
          `Penghargaan dengan nama ${name} sudah tersedia!`,
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (err) {
      throw err;
    }
  }
}
