import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PenghargaanService } from '../service/penghargaan.service';
import {
  CreatePenghargaanDto,
  UpdatePenghargaanDto,
} from '../dto/penghargaan.dto';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('penghargaan')
@UseGuards(JwtAuthGuard)
export class PenghargaanController {
  private readonly logger = new Logger(PenghargaanController.name);

  constructor(private readonly penghargaanService: PenghargaanService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(
    @Req() req: any,
    @Body() createPenghargaanDto: CreatePenghargaanDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createPenghargaanDto)}`,
    );
    const response = await this.penghargaanService.create(
      req,
      createPenghargaanDto,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createPenghargaanDto)} and response: ${response}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.penghargaanService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} search and sort data: ${JSON.stringify(searchandsortData)} and response: ${response}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.penghargaanService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${response}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updatePenghargaanDto: UpdatePenghargaanDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} body: ${JSON.stringify(updatePenghargaanDto)}`,
    );
    const response = await this.penghargaanService.update(
      req,
      +id,
      updatePenghargaanDto,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} body: ${JSON.stringify(updatePenghargaanDto)} and response: ${response}`,
    );

    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} with id: ${id}`);
    const response = await this.penghargaanService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} with id: ${id} and response: ${response}`,
    );
    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async penghargaanPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.penghargaanPersonel.name} with uid: ${uid}`);
    const response = await this.penghargaanService.penghargaanPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.penghargaanPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
