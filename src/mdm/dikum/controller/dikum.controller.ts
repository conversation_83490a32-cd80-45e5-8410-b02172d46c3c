import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  Logger,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DikumService } from '../service/dikum.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('dikum')
@UseGuards(JwtAuthGuard)
export class DikumController {
  private readonly logger = new Logger(DikumController.name);

  constructor(private readonly dikumService: DikumService) {}

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async dikbangumPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.dikbangumPersonel.name} with uid ${uid}`);

    const response = await this.dikumService.dikumPersonel(req, uid);

    this.logger.log(
      `Leaving ${this.dikbangumPersonel.name} with uid ${uid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
