import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateKonsentrasiDTO {
  @IsNotEmpty({ message: '<PERSON>a tidak boleh kosong' })
  @IsString({ message: 'Nama harus berupa string' })
  nama: string;

  @IsBoolean({ message: 'is_aktif harus berupa boolean' })
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateKonsentrasiDTO extends PartialType(CreateKonsentrasiDTO) {}
