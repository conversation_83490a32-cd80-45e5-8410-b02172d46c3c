import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';
import { KonsentrasiController } from './controller/konsentrasi.controller';
import { KonsentrasiService } from './service/konsentrasi.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KonsentrasiController],
  providers: [KonsentrasiService],
})
export class KonsentrasiModule {}
