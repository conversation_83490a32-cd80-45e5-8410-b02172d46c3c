import { forwardRef, Module } from '@nestjs/common';
import { DikumService } from './service/dikum.service';
import { DikumController } from './controller/dikum.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { TingkatModule } from './tingkat/tingkat.module';
import { InstitusiModule } from './institusi/institusi.module';
import { JurusanModule } from './jurusan/jurusan.module';
import { KonsentrasiModule } from './konsentrasi/konsentrasi.module';
import { GelarModule } from './gelar/gelar.module';
import { DikumDetailModule } from './dikum-detail/dikum-detail.module';

@Module({
  imports: [
    PrismaModule,
    LogsActivityModule,
    forwardRef(() => UsersModule),
    TingkatModule,
    InstitusiModule,
    JurusanModule,
    KonsentrasiModule,
    GelarModule,
    DikumDetailModule,
  ],
  controllers: [DikumController],
  providers: [DikumService],
})
export class DikumModule {}
