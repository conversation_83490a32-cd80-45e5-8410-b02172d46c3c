import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateDikumDTO {
  @IsNotEmpty({ message: 'Nama tidak boleh kosong' })
  @IsString({ message: 'Nama harus berupa string' })
  nama: string;

  @IsNotEmpty({ message: 'Tingkat pendidikan tidak boleh kosong' })
  @IsString({ message: 'Tingkat pendidikan harus berupa string' })
  tingkat_pendidikan: string;
}

export class UpdateDikumDTO extends PartialType(CreateDikumDTO) {}
