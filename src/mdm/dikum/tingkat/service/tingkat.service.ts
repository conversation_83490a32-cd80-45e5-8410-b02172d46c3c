import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import { CreateTingkatDTO, UpdateTingkatDTO } from '../dto/tingkat.dto';
import { is } from 'date-fns/locale';

@Injectable()
export class TingkatService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'boolean' | 'date' };
      } = {
        nama: { field: 'nama', type: 'string' },
        tingkat_pendidikan: { field: 'tingkat_pendidikan', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.dikum.count({
          where,
        }),
        this.prisma.dikum.findMany({
          select: {
            id: true,
            nama: true,
            tingkat_pendidikan: true,
            is_aktif: true,
            created_at: true,
          },
          where,
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const queryResult = await this.prisma.dikum.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('ID tingkat tidak ditemukan.');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreateTingkatDTO) {
    const { nama, tingkat_pendidikan, is_aktif } = body;

    try {
      const checkNameDikum = await this.prisma.dikum.findFirst({
        where: {
          nama,
          is_aktif: Boolean(is_aktif) ?? true,
          deleted_at: null,
        },
      });

      if (checkNameDikum) {
        throw new ConflictException('Nama tingkat sudah ada.');
      }

      const queryResult = await this.prisma.dikum.create({
        data: {
          nama,
          tingkat_pendidikan,
          created_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdateTingkatDTO) {
    const { nama, tingkat_pendidikan, is_aktif } = body;

    try {
      const dikum = await this.prisma.dikum.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!dikum) {
        throw new NotFoundException('ID tingkat tidak ditemukan.');
      }

      // Validasi nama hanya jika dikirim
      if (
        typeof nama !== 'undefined' &&
        (await this.prisma.dikum.findFirst({
          where: {
            nama,
            deleted_at: null,
            NOT: { id: +id },
          },
        }))
      ) {
        throw new ConflictException('Nama tingkat sudah ada.');
      }

      // Bangun data update secara dinamis
      const updateData: any = { updated_at: new Date() };
      if (typeof nama !== 'undefined') updateData.nama = nama;
      if (typeof tingkat_pendidikan !== 'undefined')
        updateData.tingkat_pendidikan = tingkat_pendidikan;
      if (typeof is_aktif !== 'undefined')
        updateData.is_aktif = Boolean(is_aktif);

      const queryResult = await this.prisma.dikum.update({
        where: { id: +id },
        data: updateData,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    try {
      const dikum = await this.prisma.dikum.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!dikum) {
        throw new NotFoundException('ID tingkat tidak ditemukan.');
      }

      const queryResult = await this.prisma.dikum.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
