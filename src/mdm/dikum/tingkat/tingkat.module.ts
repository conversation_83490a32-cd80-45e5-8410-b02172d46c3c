import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';
import { TingkatController } from './controller/tingkat.controller';
import { TingkatService } from './service/tingkat.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [TingkatController],
  providers: [TingkatService],
})
export class TingkatModule {}
