import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateTingkatDTO {
  @IsNotEmpty({ message: '<PERSON>a tidak boleh kosong' })
  @IsString({ message: 'Nama harus berupa string' })
  nama: string;

  @IsNotEmpty({ message: 'Tingkat pendidikan tidak boleh kosong' })
  @IsString({ message: 'Tingkat pendidikan harus berupa string' })
  tingkat_pendidikan: string;

  @IsBoolean({ message: 'is_aktif harus berupa boolean' })
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateTingkatDTO extends PartialType(CreateTingkatDTO) {}
