import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';
import { GelarController } from './controller/gelar.controller';
import { GelarService } from './service/gelar.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [GelarController],
  providers: [GelarService],
})
export class GelarModule {}
