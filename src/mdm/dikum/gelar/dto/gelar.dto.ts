import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateGelarDTO {
  @IsNotEmpty({ message: '<PERSON>a tidak boleh kosong' })
  @IsString({ message: 'Nama harus berupa string' })
  nama: string;

  @IsNotEmpty({ message: 'Gelar belakang tidak boleh kosong' })
  @IsBoolean({ message: 'Gelar belakang harus berupa boolean' })
  is_gelar_belakang: boolean;

  @IsBoolean()
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateGelarDTO extends PartialType(CreateGelarDTO) {}
