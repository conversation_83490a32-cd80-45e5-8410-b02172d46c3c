import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class DikumService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async dikumPersonel(req: any, uid: string) {
    try {
      const getDikumPersonel = await this.prisma.dikum_personel.findMany({
        select: {
          created_at: true,
          dikum_detail: {
            select: {
              institusi: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              jurusan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              dikum: {
                select: {
                  id: true,
                  nama: true,
                  tingkat_pendidikan: true,
                },
              },
              gelar: {
                select: {
                  id: true,
                  nama: true,
                  is_gelar_belakang: true,
                },
              },
              dikum_konsentrasi: {
                select: {
                  konsentrasi: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const queryResult = getDikumPersonel.map((item) => {
        const date = new Date(item.created_at);
        const year = date.getFullYear();
        let dikumDetail = {}
        if(item.dikum_detail){
          dikumDetail = {...item.dikum_detail}
        }

        return {
          ...item,
          ...dikumDetail,
          tingkat: item.dikum_detail?.dikum?.nama,
          jurusan: item.dikum_detail?.jurusan?.nama,
          institusi: item.dikum_detail?.institusi?.nama || null,
          tahun: year,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
