import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';
import { JurusanController } from './controller/jurusan.controller';
import { JurusanService } from './service/jurusan.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [JurusanController],
  providers: [JurusanService],
})
export class JurusanModule {}
