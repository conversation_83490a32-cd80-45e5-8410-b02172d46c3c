import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import {
  CreateDikumDetailDTO,
  UpdateDikumDetailDTO,
} from '../dto/dikum-detail.dto';

@Injectable()
export class DikumDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'number' | 'date';
        };
      } = {
        jurusan: { field: 'jurusan.nama', type: 'string' },
        tingkat: { field: 'dikum.nama', type: 'string' },
        institusi: { field: 'institusi.nama', type: 'string' },
        gelar: { field: 'gelar.nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.dikum_detail.count({
          where,
        }),
        this.prisma.dikum_detail.findMany({
          select: {
            id: true,
            is_aktif: true,
            institusi: {
              select: {
                id: true,
                nama: true,
              },
            },
            jurusan: {
              select: {
                id: true,
                nama: true,
              },
            },
            dikum: {
              select: {
                id: true,
                nama: true,
                tingkat_pendidikan: true,
              },
            },
            gelar: {
              select: {
                id: true,
                nama: true,
                is_gelar_belakang: true,
              },
            },
            dikum_konsentrasi: {
              select: {
                konsentrasi: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
            created_at: true,
          },
          where,
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const queryResult = await this.prisma.dikum_detail.findFirst({
        select: {
          id: true,
          is_aktif: true,
          institusi: {
            select: {
              id: true,
              nama: true,
            },
          },
          jurusan: {
            select: {
              id: true,
              nama: true,
            },
          },
          dikum: {
            select: {
              id: true,
              nama: true,
              tingkat_pendidikan: true,
            },
          },
          gelar: {
            select: {
              id: true,
              nama: true,
              is_gelar_belakang: true,
            },
          },
          dikum_konsentrasi: {
            select: {
              konsentrasi: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
          },
          created_at: true,
        },
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('ID dikum_detail tidak ditemukan.');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreateDikumDetailDTO) {
    const {
      institusi_id,
      jurusan_id,
      tingkat_id,
      gelar_id,
      konsentrasi_id,
      is_aktif,
    } = body;

    try {
      const checkDikumDetail = await this.prisma.dikum_detail.findFirst({
        where: {
          institusi_id,
          jurusan_id,
          deleted_at: null,
        },
      });

      if (checkDikumDetail) {
        throw new ConflictException(
          'Institusi dengan jurusan tersebut sudah ada.',
        );
      }

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const dikumDetail = await tx.dikum_detail.create({
          data: {
            institusi_id,
            jurusan_id,
            dikum_id: tingkat_id,
            gelar_id,
            is_aktif: Boolean(is_aktif) ?? true,
            created_at: new Date(),
          },
        });

        if (Array.isArray(konsentrasi_id) && konsentrasi_id.length > 0) {
          const konsentrasiData = konsentrasi_id.map((id) => ({
            konsentrasi_id: id,
            dikum_detail_id: dikumDetail.id,
            verified: false, // 🟢 tambahkan ini
          }));

          await tx.dikum_konsentrasi.createMany({
            data: konsentrasiData,
          });
        }

        return dikumDetail;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKUM_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdateDikumDetailDTO) {
    const {
      institusi_id,
      jurusan_id,
      tingkat_id,
      gelar_id,
      konsentrasi_id,
      is_aktif,
    } = body;

    try {
      const dikumDetail = await this.prisma.dikum_detail.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!dikumDetail) {
        throw new NotFoundException('ID dikum detail tidak ditemukan.');
      }

      const checkName = await this.prisma.dikum_detail.findFirst({
        where: {
          institusi_id,
          jurusan_id,
          deleted_at: null,
          NOT: {
            id: +id,
          },
        },
      });

      if (checkName) {
        throw new ConflictException(
          'Institusi dengan jurusan tersebut sudah ada.',
        );
      }

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const dikumDetail = await tx.dikum_detail.update({
          where: { id: +id },
          data: {
            institusi_id,
            jurusan_id,
            dikum_id: tingkat_id,
            gelar_id,
            is_aktif: Boolean(is_aktif) ?? true,
            created_at: new Date(),
          },
        });

        if (Array.isArray(konsentrasi_id) && konsentrasi_id.length > 0) {
          const konsentrasiData = konsentrasi_id.map((id) => ({
            konsentrasi_id: id,
            dikum_detail_id: dikumDetail.id,
            verified: false, // 🟢 tambahkan ini
          }));

          await tx.dikum_konsentrasi.deleteMany({
            where: {
              dikum_detail_id: +id,
            },
          });

          await tx.dikum_konsentrasi.createMany({
            data: konsentrasiData,
          });
        }

        return dikumDetail;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    try {
      const dikumDetail = await this.prisma.dikum_detail.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!dikumDetail) {
        throw new NotFoundException('ID dikum detail tidak ditemukan.');
      }

      const queryResult = await this.prisma.dikum_detail.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
