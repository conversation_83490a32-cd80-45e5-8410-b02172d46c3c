import { PartialType } from '@nestjs/mapped-types';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';

export class CreateDikumDetailDTO {
  @IsNotEmpty({ message: 'Institusi ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Institusi ID harus berupa angka' })
  institusi_id: number;

  @IsNotEmpty({ message: 'Tingkat ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Tingkat ID harus berupa angka' })
  tingkat_id: number;

  @IsNotEmpty({ message: 'Jurusan ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Jurusan ID harus berupa angka' })
  jurusan_id: number;

  @IsNotEmpty({ message: 'Gelar ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Gelar ID harus berupa angka' })
  gelar_id: number;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Number)
  @IsInt({ each: true })
  konsentrasi_id: number[];

  @IsBoolean({ message: 'is_aktif harus berupa boolean' })
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateDikumDetailDTO extends PartialType(CreateDikumDetailDTO) {}
