import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';
import { DikumDetailController } from './controller/dikum-detail.controller';
import { DikumDetailService } from './service/dikum-detail.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DikumDetailController],
  providers: [DikumDetailService],
})
export class DikumDetailModule {}
