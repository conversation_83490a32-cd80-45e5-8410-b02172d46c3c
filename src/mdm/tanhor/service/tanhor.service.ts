import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateTanhorDto, UpdateTanhorDto } from '../dto/tanhor.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class TanhorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, createTanhorDto: CreateTanhorDto) {
    try {
      await this._validateSameName(createTanhorDto.nama);
      const queryResult = await this.prisma.tanhor.create({
        data: {
          nama: createTanhorDto.nama,
          is_aktif: Boolean(createTanhorDto.is_aktif) ?? true,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'date';
        };
      } = {
        name: { field: 'nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.tanhor.count({
          where,
        }),
        this.prisma.tanhor.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.tanhor.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
          is_aktif: true,
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, updateTanhorDto: UpdateTanhorDto) {
    try {
      const tanhor = await this.prisma.tanhor.findFirst({
        where: { id, deleted_at: null },
      });
      if (!tanhor) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      const checkNameExists = await this._validateSameName(
        updateTanhorDto.nama,
        id,
      );

      console.log('checkNameExists = ', checkNameExists);

      const queryResult = await this.prisma.tanhor.update({
        where: { id },
        data: {
          nama: updateTanhorDto.nama,
          is_aktif: Boolean(updateTanhorDto.is_aktif) ?? true,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(req: any, id: number) {
    try {
      const tanhor = await this.prisma.tanhor.findFirst({
        where: { id, deleted_at: null },
      });
      if (!tanhor) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      const queryResult = await this.prisma.tanhor.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async tanhorPersonel(req: any, uid: string) {
    try {
      const getTanhorPersonel = await this.prisma.tanhor_personel.findMany({
        select: {
          tgl_tanhor: true,
          tanhor: {
            select: {
              nama: true,
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tgl_tanhor: 'desc',
        },
      });

      const queryResult = getTanhorPersonel.map((item) => {
        return {
          tmt: item?.tgl_tanhor,
          tanhor: item.tanhor.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TANHOR_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  private async _validateSameName(name: string, excludeId?: number) {
    try {
      const sameNameExist = await this.prisma.tanhor.count({
        where: {
          nama: { startsWith: name, mode: 'insensitive' },
          deleted_at: null,
          NOT: excludeId ? { id: excludeId } : undefined,
        },
      });

      if (sameNameExist) {
        throw new ConflictException(
          `Tanda Kehormatan dengan nama ${name} sudah tersedia!`,
        );
      }
    } catch (err) {
      throw err;
    }
  }
}
