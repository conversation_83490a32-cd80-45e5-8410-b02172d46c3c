import { forwardRef, Module } from '@nestjs/common';
import { TanhorService } from './service/tanhor.service';
import { TanhorController } from './controller/tanhor.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [TanhorController],
  providers: [TanhorService],
})
export class TanhorModule {}
