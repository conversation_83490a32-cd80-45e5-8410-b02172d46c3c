import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { TanhorService } from '../service/tanhor.service';
import { CreateTanhorDto, UpdateTanhorDto } from '../dto/tanhor.dto';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('tanhor')
@UseGuards(JwtAuthGuard)
export class TanhorController {
  private readonly logger = new Logger(TanhorController.name);

  constructor(private readonly tanhorService: TanhorService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(@Req() req: any, @Body() createTanhorDto: CreateTanhorDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createTanhorDto)}`,
    );
    const response = await this.tanhorService.create(req, createTanhorDto);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createTanhorDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.tanhorService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.tanhorService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateTanhorDto: UpdateTanhorDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(updateTanhorDto)}`,
    );
    const response = await this.tanhorService.update(req, +id, updateTanhorDto);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(updateTanhorDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} with id: ${id}`);
    const response = await this.tanhorService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async tanhorPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.tanhorPersonel.name} with uid: ${uid}`);
    const response = await this.tanhorService.tanhorPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.tanhorPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
