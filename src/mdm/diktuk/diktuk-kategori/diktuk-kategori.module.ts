import { forwardRef, Module } from '@nestjs/common';
import { DiktukKategoriService } from './service/diktuk-kategori.service';
import { DiktukKategoriController } from './controller/diktuk-kategori.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DiktukKategoriController],
  providers: [DiktukKategoriService],
})
export class DiktukKategoriModule {}
