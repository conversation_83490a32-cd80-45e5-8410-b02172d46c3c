import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateDiktukKategoriDto,
  UpdateDiktukKategoriDto,
} from '../dto/diktuk-kategori.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';

@Injectable()
export class DiktukKategoriService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateDiktukKategoriDto) {
    try {
      const { nama } = body;
      const dataDiktukKategori = await this.prisma.diktuk_kategori.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (dataDiktukKategori) {
        throw new BadRequestException('Nama diktuk kategori already exist!');
      }

      const queryResult = await this.prisma.diktuk_kategori.create({
        data: {
          nama: nama,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_KATEGORI_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async get(req: any, id) {
    try {
      const queryResult = await this.prisma.diktuk_kategori.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`diktuk kategori id ${id} not found`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_KATEGORI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'date' };
      } = {
        nama: { field: 'nama', type: 'string' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.diktuk_kategori.count({
          where: where,
        }),
        this.prisma.diktuk_kategori.findMany({
          select: {
            id: true,
            nama: true,
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_KATEGORI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id, body: UpdateDiktukKategoriDto) {
    try {
      const queryResult = await this.prisma.$transaction(async (tx) => {
        const { nama } = body;
        if (!nama) throw new BadRequestException('Nama cannot be empty!');
        if (!id) throw new BadRequestException('Id cannot be empty!');
        if (
          !(await tx.diktuk_kategori.findFirst({
            where: { id: +id, deleted_at: null },
          }))
        )
          throw new BadRequestException(`Diktuk kategori id ${id} not found!`);

        const updateDiktukKategori = await tx.diktuk_kategori.update({
          where: { id: +id },
          data: {
            nama: nama,
          },
        });

        return updateDiktukKategori;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_KATEGORI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id) {
    try {
      if (
        !(await this.prisma.diktuk_kategori.findFirst({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Diktuk kategori id ${id} not found!`);

      const queryResult = await this.prisma.diktuk_kategori.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_KATEGORI_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
