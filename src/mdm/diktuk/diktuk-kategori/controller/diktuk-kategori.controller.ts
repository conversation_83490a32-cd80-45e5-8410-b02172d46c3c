import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DiktukKategoriService } from '../service/diktuk-kategori.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  CreateDiktukKategoriDto,
  UpdateDiktukKategoriDto,
} from '../dto/diktuk-kategori.dto';

@Controller('diktuk-kategori')
export class DiktukKategoriController {
  private readonly logger = new Logger(DiktukKategoriController.name);

  constructor(private readonly diktukKategoriService: DiktukKategoriService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(201)
  async create(@Req() req: Request, @Body() body: CreateDiktukKategoriDto) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.diktukKategoriService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async get(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.diktukKategoriService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.diktukKategoriService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async update(
    @Req() req: Request,
    @Param('id') id: number,
    @Body() body: UpdateDiktukKategoriDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.diktukKategoriService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async delete(@Req() req: Request, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.diktukKategoriService.delete(req, id);

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
