import { PartialType } from '@nestjs/mapped-types';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateDiktukDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  @IsNotEmpty()
  kategori_id: number;

  @IsOptional()
  @IsBoolean()
  is_aktif?: boolean;
}

export class UpdateDiktukDto extends PartialType(CreateDiktukDto) {}
