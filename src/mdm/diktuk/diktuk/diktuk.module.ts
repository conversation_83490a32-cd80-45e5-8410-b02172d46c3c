import { forwardRef, Module } from '@nestjs/common';
import { DiktukService } from './service/diktuk.service';
import { DiktukController } from './controller/diktuk.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DiktukController],
  providers: [DiktukService],
})
export class DiktukModule {}
