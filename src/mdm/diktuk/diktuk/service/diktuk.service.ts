import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateDiktukDto, UpdateDiktukDto } from '../dto/diktuk.dto';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';
import { is } from 'date-fns/locale';

@Injectable()
export class DiktukService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateDiktukDto) {
    try {
      const { nama, kategori_id, is_aktif } = body;

      const dataDiktuk = await this.prisma.diktuk.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      const dataDiktukKategori = await this.prisma.diktuk_kategori.findFirst({
        where: {
          id: +kategori_id,
          deleted_at: null,
        },
      });

      if (dataDiktuk)
        throw new BadRequestException('Nama diktuk already exist!');

      if (!dataDiktukKategori)
        throw new BadRequestException('Kategori diktuk not found!');

      const queryResult = await this.prisma.diktuk.create({
        data: {
          nama: nama,
          is_aktif: Boolean(is_aktif) ?? true,
          kategori_id: kategori_id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKTUK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async get(req: any, id) {
    try {
      const queryResult = await this.prisma.diktuk.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
        include: { diktuk_kategori: true },
      });

      if (!queryResult) {
        throw new NotFoundException(`diktuk id ${id} not found`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getJenisDiktuk(req: any) {
    const results = await this.prisma.jenis_diktuk.findMany({
      where: { deleted_at: null },
      select: {
        id: true,
        nama: true,
        deskripsi: true,
        background_color: true,
      },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'number' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        kategori: { field: 'diktuk_kategori.nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.diktuk.count({
          where: where,
        }),
        this.prisma.diktuk.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
            created_at: true,
            diktuk_kategori: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where: where,
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKTUK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id, body: UpdateDiktukDto) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const existingDiktuk = await tx.diktuk.findFirst({
          where: { id: +id, deleted_at: null },
        });

        if (!existingDiktuk) {
          throw new BadRequestException(`Diktuk id ${id} not found!`);
        }

        // Validasi kategori_id hanya jika dikirim
        if (
          typeof body.kategori_id !== 'undefined' &&
          !(await tx.diktuk_kategori.findFirst({
            where: { id: +body.kategori_id, deleted_at: null },
          }))
        ) {
          throw new BadRequestException(`Diktuk kategori not found`);
        }

        // Bangun objek data update hanya dari field yang dikirim
        const updateData: any = {};
        if (typeof body.nama !== 'undefined') updateData.nama = body.nama;
        if (typeof body.kategori_id !== 'undefined')
          updateData.kategori_id = body.kategori_id;
        if (typeof body.is_aktif !== 'undefined')
          updateData.is_aktif = Boolean(body.is_aktif);
        updateData.updated_at = new Date();

        const updateDiktuk = await tx.diktuk.update({
          where: { id: +id },
          data: updateData,
        });

        return updateDiktuk;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id) {
    try {
      if (
        !(await this.prisma.diktuk.findFirst({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Diktuk id ${id} not found!`);

      const queryResult = await this.prisma.diktuk.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKTUK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKTUK_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
