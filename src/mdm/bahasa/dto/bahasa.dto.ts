import { bahasa_jenis_enum } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateBahasaDto {
  @IsString({ message: 'Nama harus berupa string' })
  @IsNotEmpty({ message: 'Nama tidak boleh kosong' })
  nama: string;

  @IsEnum(bahasa_jenis_enum, {
    message: 'Jenis harus berupa enum("LOKAL", "INTERNASIONAL")',
  })
  @IsNotEmpty({ message: '<PERSON>is tidak boleh kosong' })
  jenis: bahasa_jenis_enum;

  @IsOptional()
  @IsBoolean()
  is_aktif?: boolean;
}

export class UpdateBahasaDto extends CreateBahasaDto {}
