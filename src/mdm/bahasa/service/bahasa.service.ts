import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateBahasaDto, UpdateBahasaDto } from '../dto/bahasa.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';

@Injectable()
export class BahasaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateBahasaDto) {
    try {
      const { nama, jenis, is_aktif } = body;
      const dataBahasa = await this.prisma.bahasa.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (dataBahasa) {
        throw new BadRequestException('Nama bahasa already exist!');
      }

      const queryResult = await this.prisma.bahasa.create({
        data: {
          nama: nama,
          jenis: jenis,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAHASA_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async get(req: any, id) {
    try {
      const queryResult = await this.prisma.bahasa.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`bahasa id ${id} not found`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAHASA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(req: any, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'number' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.bahasa.count({
          where: where,
        }),
        this.prisma.bahasa.findMany({
          select: {
            id: true,
            nama: true,
            jenis: true,
            is_aktif: true,
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAHASA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id, body: UpdateBahasaDto) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const { nama, jenis, is_aktif } = body;
        if (!nama) throw new BadRequestException('Nama cannot be empty!');
        if (!id) throw new BadRequestException('Id cannot be empty!');
        if (
          !(await tx.bahasa.findFirst({ where: { id: +id, deleted_at: null } }))
        )
          throw new NotFoundException(`Bahasa id ${id} not found!`);

        const queryResult = await tx.bahasa.update({
          where: { id: +id },
          data: {
            nama: nama,
            jenis: jenis,
            is_aktif: Boolean(is_aktif) ?? true,
          },
        });

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.BAHASA_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.BAHASA_READ as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        };
      });
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id) {
    try {
      if (
        !(await this.prisma.bahasa.findFirst({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new NotFoundException(`Bahasa id ${id} not found!`);

      const queryResult = await this.prisma.bahasa.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAHASA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async bahasaPersonel(req: any, uid: string) {
    try {
      const getBahasaPersonel = await this.prisma.bahasa_personel.findMany({
        select: {
          bahasa: {
            select: {
              nama: true,
              jenis: true,
            },
          },
          is_aktif: true,
        },
        where: {
          deleted_at: null,
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const queryResult = getBahasaPersonel.map((item) => {
        return {
          bahasa: item.bahasa.nama,
          jenis: item.bahasa.jenis,
          status: item.is_aktif,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAHASA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
