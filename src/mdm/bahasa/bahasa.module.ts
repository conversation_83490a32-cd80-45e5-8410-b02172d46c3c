import { forwardRef, Module } from '@nestjs/common';
import { BahasaService } from './service/bahasa.service';
import { BahasaController } from './controller/bahasa.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [BahasaController],
  providers: [BahasaService],
})
export class BahasaModule {}
