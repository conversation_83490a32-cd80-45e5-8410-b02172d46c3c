import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { BahasaService } from '../service/bahasa.service';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { CreateBahasaDto, UpdateBahasaDto } from '../dto/bahasa.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';

@Controller('bahasa')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class BahasaController {
  private readonly logger = new Logger(BahasaController.name);

  constructor(private readonly bahasaService: BahasaService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: any,
    @Body(FieldValidatorPipe) body: CreateBahasaDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.bahasaService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.bahasaService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.bahasaService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body(FieldValidatorPipe) body: UpdateBahasaDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.bahasaService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.bahasaService.delete(req, id);

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async bahasaPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.bahasaPersonel.name} with uid ${uid}`);

    const response = await this.bahasaService.bahasaPersonel(req, uid);

    this.logger.log(
      `Leaving ${this.bahasaPersonel.name} with uid ${uid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
