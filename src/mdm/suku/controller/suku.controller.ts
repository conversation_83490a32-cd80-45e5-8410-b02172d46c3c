import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { SukuService } from '../service/suku.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { CreateSukuDto, UpdateSukuDto } from 'src/mdm/suku/dto/suku.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';

@Controller('suku')
@UseGuards(JwtAuthGuard)
export class SukuController {
  private readonly logger = new Logger(SukuController.name);

  constructor(private readonly sukuService: SukuService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: Request,
    @Body(FieldValidatorPipe) body: CreateSukuDto,
  ) {
    const suku = await this.sukuService.create(req, body);

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully create new suku',
      data: suku,
    };
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: Request, @Param('id') id: string) {
    const suku = await this.sukuService.get(req, id);

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get suku',
      data: suku,
    };
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    const { suku, page, totalData, totalPage } = await this.sukuService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get list suku',
      data: suku,
      totalPage,
      totalData,
      page,
    };
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: Request,
    @Param('id') id: number,
    @Body() body: UpdateSukuDto,
  ) {
    const suku = await this.sukuService.update(req, id, body);

    return {
      statusCode: HttpStatus.OK,
      message: 'Suku was successfully updated',
      data: suku,
    };
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: Request, @Param('id') id: string) {
    const suku = await this.sukuService.delete(req, Number(id));

    return {
      statusCode: HttpStatus.OK,
      message: 'Suku was successfully deleted',
      data: suku,
    };
  }
}
