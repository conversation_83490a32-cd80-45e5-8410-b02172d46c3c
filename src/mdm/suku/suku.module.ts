import { forwardRef, Module } from '@nestjs/common';
import { SukuService } from './service/suku.service';
import { SukuController } from './controller/suku.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SukuController],
  providers: [SukuService],
})
export class SukuModule {}
