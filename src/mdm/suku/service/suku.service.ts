import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateSukuDto, UpdateSukuDto } from '../dto/suku.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { is } from 'date-fns/locale';

@Injectable()
export class SukuService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateSukuDto) {
    const { nama, is_aktif } = body;

    try {
      const dataSuku = await this.prisma.suku.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (dataSuku) {
        throw new BadRequestException('Nama suku already exist!');
      }

      const createSuku = await this.prisma.suku.create({
        data: {
          nama: nama,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      return createSuku;
    } catch (error) {
      throw error;
    }
  }

  async get(req, id) {
    try {
      const suku = await this.prisma.suku.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!suku) {
        throw new NotFoundException(`suku id ${id} not found`);
      }
      return suku;
    } catch (error) {
      throw error;
    }
  }

  async getList(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'number' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, suku] = await this.prisma.$transaction([
        this.prisma.suku.count({
          where: where,
        }),
        this.prisma.suku.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      return { suku, page, totalPage, totalData };
    } catch (error) {
      throw error;
    }
  }

  async update(req, id, body: UpdateSukuDto) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const { nama, is_aktif } = body;
        if (!nama) throw new BadRequestException('Nama cannot be empty!');
        if (!id) throw new BadRequestException('Id cannot be empty!');
        if (
          !(await tx.suku.findFirst({ where: { id: +id, deleted_at: null } }))
        )
          throw new BadRequestException(`Suku id ${id} not found!`);

        const updateSuku = await tx.suku.update({
          where: { id: +id },
          data: {
            nama: nama,
            is_aktif: Boolean(is_aktif) ?? true,
          },
        });

        return updateSuku;
      });
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required!');

    const suku = await this.prisma.suku.findFirst({
      where: { id, deleted_at: null },
    });

    if (!suku) throw new BadRequestException(`Suku id ${id} not found!`);

    return await this.prisma.suku.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }
}
