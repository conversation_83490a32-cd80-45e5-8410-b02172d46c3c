import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { DuplikasiJabatanController } from './controller/duplikasi-jabatan.controller';
import { DuplikasiJabatanService } from './service/duplikasi-jabatan.service';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DuplikasiJabatanController],
  providers: [DuplikasiJabatanService, MinioService],
})
export class DuplikasiJabatanModule {}
