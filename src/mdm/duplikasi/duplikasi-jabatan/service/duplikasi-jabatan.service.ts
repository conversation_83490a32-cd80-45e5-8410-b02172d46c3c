import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateMultipleDuplikasiJabatanDTO } from '../dto/duplikasi-jabatan.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';

@Injectable()
export class DuplikasiJabatanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getFilter(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'number' | 'date';
      };
    } = {
      id: { field: 'id', type: 'number' },
      satuan_id: { field: 'satuan_id', type: 'number' },
      nama: { field: 'nama', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    if (where.AND && Array.isArray(where.AND)) {
      const satuanCondition = where.AND.find((condition) =>
        condition.hasOwnProperty('satuan_id'),
      );

      if (satuanCondition) {
        try {
          const satuanHirarki = await this.prisma.mv_satuan_hirarki.findFirst({
            where: {
              satuan_id: Number(satuanCondition.satuan_id.contains),
            },
          });

          const [totalData, dataHirarki] = await this.prisma.$transaction([
            this.prisma.mv_satuan_hirarki.count({
              where: {
                jenis_id: {
                  equals: satuanHirarki.jenis_id,
                },
              },
            }),
            this.prisma.mv_satuan_hirarki.findMany({
              select: {
                mv_satuan_hirarki_satuan: {
                  select: {
                    id: true,
                    nama: true,
                    atasan_satuan: {
                      select: {
                        id: true,
                        nama: true,
                      },
                    },
                  },
                },
              },
              take: +limit,
              skip: +limit * (+page - 1),
              where: {
                jenis_id: {
                  equals: satuanHirarki.jenis_id,
                },
              },
              orderBy: {
                mv_satuan_hirarki_satuan: orderBy[0],
              },
            }),
          ]);

          const totalPage = Math.ceil(totalData / limit);

          async function getHierarchy(prisma, startId) {
            let results = [];
            let currentId = startId;

            while (currentId) {
              const row = await prisma.satuan.findFirst({
                where: {
                  id: currentId,
                },
                select: {
                  id: true,
                  nama: true,
                  atasan_id: true,
                },
              });

              if (!row || row.length === 0) break;

              results.push({
                id: row.id.toString(),
                nama: row.nama,
              });

              currentId = row.atasan_id;
            }

            return results;
          }

          const queryResult = await Promise.all(
            dataHirarki.map(async ({ mv_satuan_hirarki_satuan }) => {
              const hierarchy = await getHierarchy(
                this.prisma,
                mv_satuan_hirarki_satuan.atasan_satuan.id,
              );

              return {
                id: mv_satuan_hirarki_satuan.id,
                nama: mv_satuan_hirarki_satuan.nama,
                group_name: [
                  ...hierarchy.map((h) => h.nama).reverse(),
                  mv_satuan_hirarki_satuan.nama,
                ].join(' - '),
              };
            }),
          );

          const message = convertToLogMessage(
            ConstantLogStatusEnum.SUCCESS,
            ConstantLogTypeEnum.READ_LOG_TYPE,
            ConstantLogDataTypeEnum.LIST,
            ConstantLogModuleEnum.MDM_DUPLIKASI_JABATAN_MODULE,
          );
          await this.logsActivityService.addLogsActivity(
            convertToILogData(
              req,
              CONSTANT_LOG.MDM_DUPLIKASI_JABATAN_READ as ConstantLogType,
              message,
              queryResult,
            ),
          );

          return {
            statusCode: HttpStatus.OK,
            message,
            page: page,
            totalPage: totalPage,
            totalData: totalData,
            data: queryResult,
          };
        } catch (error) {
          throw error;
        }
      }
    } else {
      throw new BadRequestException('Param Query Satuan ID is required');
    }
  }

  async create(req: any, body: CreateMultipleDuplikasiJabatanDTO) {
    const { data } = body;

    try {
      const queryResult: any[] = [];

      await this.prisma.$transaction(async (prisma) => {
        for (const item of data) {
          const { id_jabatan, id_satuan } = item;

          const jabatan = await prisma.jabatan.findUnique({
            select: {
              id: true,
              nama: true,
              dsp: true,
              nivellering_id: true,
              satuan_id: true,
              atasan_id: true,
              kategori_id: true,
              is_aktif: true,
              jabatan_pangkat: {
                select: {
                  pangkat_id: true,
                },
              },
            },
            where: { id: id_jabatan },
          });

          if (!jabatan) {
            throw new NotFoundException(`Jabatan '${id_jabatan}' not found`);
          }

          const satuans = await prisma.satuan.findMany({
            where: { id: { in: id_satuan } },
            select: {
              id: true,
              nama: true,
              atasan_satuan: {
                select: {
                  nama: true,
                },
              },
            },
          });

          if (satuans.length !== id_satuan.length) {
            throw new NotFoundException('Some satuan IDs not found');
          }

          for (const satuan of satuans) {
            const checkExistsJabatan = await prisma.jabatan.findFirst({
              where: {
                nama: jabatan.nama,
                satuan_id: satuan.id,
                deleted_at: null,
              },
            });

            if (checkExistsJabatan) {
              throw new ConflictException(
                `Jabatan '${jabatan.nama}' dengan satuan ID '${satuan.id}' sudah ada`,
              );
            }

            const newJabatan = await prisma.jabatan.create({
              data: {
                nama: jabatan.nama,
                dsp: jabatan.dsp,
                nivellering_id: jabatan.nivellering_id,
                satuan_id: satuan.id,
                atasan_id: jabatan.atasan_id,
                kategori_id: jabatan.kategori_id,
                is_aktif: jabatan.is_aktif,
              },
            });

            queryResult.push({ ...newJabatan, jabatan_pangkat: [] });

            const jabatanPangkatData = jabatan.jabatan_pangkat.map((jp) => ({
              jabatan_id: newJabatan.id,
              pangkat_id: jp.pangkat_id,
            }));

            if (jabatanPangkatData.length > 0) {
              const createdJabatanPangkat =
                await prisma.jabatan_pangkat.createMany({
                  data: jabatanPangkatData,
                });

              queryResult[queryResult.length - 1].jabatan_pangkat.push(
                createdJabatanPangkat,
              );
            }
          }
        }
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.MDM_DUPLIKASI_JABATAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MDM_DUPLIKASI_JABATAN_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
