import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsNumber,
  ValidateNested,
} from 'class-validator';

export class CreateDuplikasiJabatanDTO {
  @IsNotEmpty({ message: 'ID jabatan tidak boleh kosong' })
  @IsNumber({}, { message: 'ID jabatan harus berupa number' })
  id_jabatan: number;

  @IsArray({ message: 'ID satuan harus berupa array' })
  @ArrayNotEmpty({ message: 'ID satuan tidak boleh kosong' })
  @IsNumber({}, { each: true, message: 'Setiap ID satuan harus berupa number' })
  id_satuan: number[];
}

export class CreateMultipleDuplikasiJabatanDTO {
  @IsArray({ message: 'Data harus berupa array' })
  @ArrayNotEmpty({ message: 'Data tidak boleh kosong' })
  @ValidateNested({ each: true })
  @Type(() => CreateDuplikasiJabatanDTO)
  data: CreateDuplikasiJabatanDTO[];
}
