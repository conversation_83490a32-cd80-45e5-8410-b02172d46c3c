import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { DuplikasiJabatanService } from '../service/duplikasi-jabatan.service';
import { CreateMultipleDuplikasiJabatanDTO } from '../dto/duplikasi-jabatan.dto';
import { Module, Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../../core/constants/module.constant';

@Controller('duplikasi-jabatan')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class DuplikasiJabatanController {
  private readonly logger = new Logger(DuplikasiJabatanController.name);

  constructor(
    private readonly duplikasiJabatanService: DuplikasiJabatanService,
  ) {}

  @Get('/filter')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getFilter(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getFilter.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.duplikasiJabatanService.getFilter(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getFilter.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.OK)
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateMultipleDuplikasiJabatanDTO,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.duplikasiJabatanService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
