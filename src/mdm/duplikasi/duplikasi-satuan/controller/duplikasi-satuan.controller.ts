import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DuplikasiSatuanService } from '../service/duplikasi-satuan.service';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { CreateDuplikasiSatuanDTO } from '../dto/duplikasi-satuan.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../../core/constants/module.constant';

@Controller('duplikasi-satuan')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class DuplikasiSatuanController {
  private readonly logger = new Logger(DuplikasiSatuanController.name);

  constructor(
    private readonly duplikasiSatuanService: DuplikasiSatuanService,
  ) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.duplikasiSatuanService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Req() req: any,
    @Body(FieldValidatorPipe) body: CreateDuplikasiSatuanDTO,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.duplikasiSatuanService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
