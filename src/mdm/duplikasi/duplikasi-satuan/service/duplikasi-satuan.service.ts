import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateDuplikasiSatuanDTO } from '../dto/duplikasi-satuan.dto';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';

@Injectable()
export class DuplikasiSatuanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'date';
      };
    } = {
      nama: { field: 'nama', type: 'string' },
      is_active: { field: 'is_active', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    const [totalData, satuans] = await this.prisma.$transaction([
      this.prisma.satuan.count({
        where: where,
      }),
      this.prisma.satuan.findMany({
        select: {
          id: true,
          nama: true,
          atasan_satuan: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = satuans.map(({ atasan_satuan, ...data }) => ({
      ...data,
      atasan_id: atasan_satuan?.id || null,
      atasan_nama: atasan_satuan?.nama || null,
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.MDM_DUPLIKASI_SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MDM_DUPLIKASI_SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: +page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async create(req: any, body: CreateDuplikasiSatuanDTO) {
    const { satuan_id, atasan_id, satuan_baru } = body;

    try {
      const [
        existingSatuan,
        existingAtasan,
        existingSatuanBaru,
        lastSatuan,
        lastJabatan,
      ] = await Promise.all([
        this.prisma.satuan.findFirst({
          where: { id: satuan_id },
          include: { atasan_satuan: true },
        }),
        this.prisma.satuan.findFirst({ where: { id: atasan_id } }),
        this.prisma.satuan.findFirst({ where: { nama: satuan_baru } }),
        this.prisma.satuan.findFirst({
          orderBy: { id: 'desc' },
          select: { id: true },
        }),
        this.prisma.jabatan.findFirst({
          orderBy: { id: 'desc' },
          select: { id: true },
        }),
      ]);

      if (!existingSatuan)
        throw new NotFoundException('Satuan ID tidak ditemukan.');
      if (!existingAtasan)
        throw new NotFoundException('Atasan ID tidak ditemukan.');
      if (existingSatuanBaru)
        throw new ConflictException('Nama satuan baru sudah ada.');

      const replaceNamaWithSatuanBaru = (nama: string) => {
        const satuanRegex = new RegExp(existingSatuan.nama, 'g');
        const atasanRegex = existingSatuan.atasan_satuan?.nama
          ? new RegExp(existingSatuan.atasan_satuan.nama, 'g')
          : null;

        let updatedNama = nama.replace(satuanRegex, satuan_baru);
        if (atasanRegex)
          updatedNama = updatedNama.replace(atasanRegex, existingAtasan.nama);

        return updatedNama;
      };

      const createSatuanBaru = {
        id: Number(lastSatuan.id) + 1,
        nama: satuan_baru,
        alamat: existingSatuan.alamat,
        jenis_id: existingSatuan.jenis_id,
        is_aktif: existingSatuan.is_aktif,
        atasan_id: existingAtasan.id,
      };

      const jabatanFromSatuan = await this.prisma.jabatan.findMany({
        where: { satuan_id: existingSatuan.id },
        orderBy: {
          id: 'asc',
        },
      });

      const updateJabatanFromSatuan = jabatanFromSatuan.map((jabatan) => ({
        ...jabatan,
        nama: replaceNamaWithSatuanBaru(jabatan.nama),
      }));

      const createJabatanFromSatuan: any[] = [];
      let indexJabatan = Number(lastJabatan.id) + 1;

      // Query semua atasan yang diperlukan
      const checkAtasanJabatan = await this.prisma.jabatan.findMany({
        where: {
          id: {
            in: updateJabatanFromSatuan
              .map((data) => data.atasan_id)
              .filter((id) => id !== null),
          },
        },
      });

      // Buat mapping dari ID lama ke nama baru
      const namaJabatanBaruMap = new Map(
        checkAtasanJabatan.map((data) => [
          data.id,
          replaceNamaWithSatuanBaru(data.nama),
        ]),
      );

      // Query semua jabatan dengan nama yang telah diganti
      const newAtasanJabatan = await this.prisma.jabatan.findMany({
        where: {
          nama: { in: Array.from(namaJabatanBaruMap.values()) },
        },
      });

      // Buat mapping dari nama baru ke ID baru
      const namaToIdMap = new Map(
        newAtasanJabatan.map((data) => [data.nama, data.id]),
      );

      updateJabatanFromSatuan.forEach((data) => {
        delete data.satuan_id;

        let newAtasanId = null;
        if (data.atasan_id && namaJabatanBaruMap.has(data.atasan_id)) {
          const namaBaru = namaJabatanBaruMap.get(data.atasan_id);
          newAtasanId = namaToIdMap.get(namaBaru) ?? null;
        }

        let newIndex = indexJabatan;
        createJabatanFromSatuan.push({
          id: data.id,
          new_id: newIndex,
          ...data,
          satuan_id: createSatuanBaru.id,
          atasan_id: newAtasanId,
        });
        indexJabatan++;
      });

      const createSatuanTahap2: any[] = [];
      const idMappingSatuan: Map<number, number> = new Map();
      let lastSatuanId = createSatuanBaru.id + 1;

      // Ambil parent satuan dulu (hanya 1 level pertama)
      let queue = await this.prisma.satuan.findMany({
        where: { atasan_id: existingSatuan.id },
      });

      queue.forEach((satuan) => {
        const newId = lastSatuanId++;
        idMappingSatuan.set(Number(satuan.id), newId);
        createSatuanTahap2.push({
          id: satuan.id,
          new_id: newId,
          nama: satuan.nama,
          alamat: satuan.alamat,
          jenis_id: satuan.jenis_id,
          is_aktif: satuan.is_aktif,
          atasan_id: createSatuanBaru.id,
        });
      });

      while (queue.length > 0) {
        // Ambil ID satuan yang sudah diproses untuk mencari anaknya
        const parentIds = queue.map((s) => s.id);
        queue = []; // Reset queue untuk iterasi berikutnya

        // Ambil anak-anak dari parent yang baru saja diproses
        const childSatuanList = await this.prisma.satuan.findMany({
          where: { atasan_id: { in: parentIds } },
        });

        childSatuanList.forEach((childSatuan) => {
          const childNewId = lastSatuanId++;
          idMappingSatuan.set(Number(childSatuan.id), childNewId);
          createSatuanTahap2.push({
            id: childSatuan.id,
            new_id: childNewId,
            nama: childSatuan.nama,
            alamat: childSatuan.alamat,
            jenis_id: childSatuan.jenis_id,
            is_aktif: childSatuan.is_aktif,
            atasan_id: idMappingSatuan.get(Number(childSatuan.atasan_id))!,
          });

          queue.push(childSatuan); // Masukkan anak-anak ke dalam queue untuk iterasi berikutnya
        });
      }

      const createJabatanTahap2: any[] = [];
      const idMappingJabatan: Map<string, bigint> = new Map();
      let lastJabatanNewId =
        createJabatanFromSatuan.length > 0
          ? BigInt(
              Math.max(
                ...createJabatanFromSatuan.map((item) => Number(item.new_id)),
              ),
            )
          : BigInt(lastJabatan.id);

      // Ambil semua jabatan dalam satu batch query
      const allJabatan = await this.prisma.jabatan.findMany({
        where: {
          satuan_id: { in: createSatuanTahap2.map((s) => Number(s.id)) }, // Ubah BigInt ke Number
        },
        orderBy: { id: 'asc' },
      });

      // Buat mapping untuk jabatan berdasarkan satuan_id
      const jabatanBySatuan: Map<string, any[]> = new Map();
      allJabatan.forEach((jabatan) => {
        const satuanKey = jabatan.satuan_id.toString(); // Gunakan string sebagai key untuk konsistensi
        if (!jabatanBySatuan.has(satuanKey)) {
          jabatanBySatuan.set(satuanKey, []);
        }
        jabatanBySatuan.get(satuanKey)!.push(jabatan);
      });

      // Proses jabatan berdasarkan satuan yang baru dibuat
      for (const satuan of createSatuanTahap2) {
        const satuanKey = satuan.id.toString(); // Pastikan key cocok
        const jabatanList = jabatanBySatuan.get(satuanKey) || [];

        jabatanList.forEach((data) => {
          let atasanId = null;
          lastJabatanNewId++;

          idMappingJabatan.set(
            BigInt(data.id).toString(),
            BigInt(lastJabatanNewId),
          );

          // Cek apakah atasan_id ada di createJabatanFromSatuan
          const foundAtasan = createJabatanFromSatuan.find(
            (data2) => data.atasan_id === data2.id,
          );

          if (foundAtasan) {
            atasanId = foundAtasan.new_id;
          } else if (data.atasan_id) {
            // Jika atasan_id ada dalam idMappingJabatan
            const atasanIdString = BigInt(data.atasan_id).toString();
            if (idMappingJabatan.has(atasanIdString)) {
              atasanId = idMappingJabatan.get(atasanIdString);
            }
          }

          createJabatanTahap2.push({
            id: BigInt(lastJabatanNewId),
            nama: replaceNamaWithSatuanBaru(data.nama),
            dsp: data.dsp,
            nivellering_id: data.nivellering_id,
            satuan_id: satuan.new_id,
            atasan_id: atasanId,
            kategori_id: data.kategori_id,
            is_aktif: data.is_aktif,
            created_at: new Date(),
          });
        });
      }

      // Replace IDs with new IDs
      const updatedCreateJabatanFromSatuan = this.mapIdsToNewIds(
        createJabatanFromSatuan,
      );
      const updatedCreateSatuanTahap2 = this.mapIdsToNewIds(createSatuanTahap2);
      const updatedCreateJabatanTahap2 =
        this.mapIdsToNewIds(createJabatanTahap2);

      // Created Data
      await this.prisma.$transaction([
        this.prisma.satuan.create({ data: createSatuanBaru }),
        this.prisma.jabatan.createMany({
          data: updatedCreateJabatanFromSatuan,
        }),
        this.prisma.satuan.createMany({ data: updatedCreateSatuanTahap2 }),
        this.prisma.jabatan.createMany({ data: updatedCreateJabatanTahap2 }),
        this.prisma.$executeRaw`REFRESH MATERIALIZED VIEW mv_atasan_satuan`,
        this.prisma.$executeRaw`REFRESH MATERIALIZED VIEW mv_satuan_hirarki`,
        this.prisma
          .$executeRaw`SELECT setval('satuan_id_seq', COALESCE((SELECT MAX(id) FROM satuan), 0) + 1);`,
      ]);

      const queryResult = {
        satuan_parent: createSatuanBaru,
        jabatan_parent: updatedCreateJabatanFromSatuan,
        satuan_child: updatedCreateSatuanTahap2,
        jabatan_child: updatedCreateJabatanTahap2,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.MDM_DUPLIKASI_SATUAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MDM_DUPLIKASI_SATUAN_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  mapIdsToNewIds = (dataArray: any[]) => {
    return dataArray.map((data) => {
      if (data.new_id) {
        return {
          ...data,
          id: data.new_id,
          new_id: undefined,
        };
      }
      return data;
    });
  };
}
