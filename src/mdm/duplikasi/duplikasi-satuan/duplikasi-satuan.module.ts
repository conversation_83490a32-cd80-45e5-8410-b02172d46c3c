import { forwardRef, Module } from '@nestjs/common';
import { DuplikasiSatuanService } from './service/duplikasi-satuan.service';
import { DuplikasiSatuanController } from './controller/duplikasi-satuan.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DuplikasiSatuanController],
  providers: [DuplikasiSatuanService],
  exports: [DuplikasiSatuanService],
})
export class DuplikasiSatuanModule {}
