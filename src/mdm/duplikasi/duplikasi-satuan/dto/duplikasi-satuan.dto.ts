import { IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';

export class CreateDuplikasiSatuanDTO {
  @IsNotEmpty({ message: 'Satuan ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Satuan ID harus berupa number' })
  satuan_id: number;

  @IsNotEmpty({ message: 'Atasan ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Atasan ID harus berupa number' })
  atasan_id: number;

  @IsNotEmpty({ message: 'Satuan baru tidak boleh kosong' })
  @IsString({ message: 'Satuan baru harus berupa string' })
  satuan_baru: string;
}
