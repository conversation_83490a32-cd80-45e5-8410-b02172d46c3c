import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateSatuanJenisDto,
  UpdateSatuanJenisDto,
} from '../dto/satuan-jenis.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class SatuanJenisService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateSatuanJenisDto) {
    const { nama } = body;

    if (
      await this.prisma.satuan_jenis.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('nama satuan jenis already exists');
    }

    const satuanJenis = await this.prisma.satuan_jenis.create({
      data: {
        nama: nama,
        created_at: new Date(),
      },
    });

    const queryResult = {
      ...satuanJenis,
      id: Number(satuanJenis.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JENIS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const satuanJenis = await this.prisma.satuan_jenis.findFirst({
      select: {
        id: true,
        nama: true,
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!satuanJenis) {
      throw new NotFoundException(`satuan jenis id ${id} tidak di temukan`);
    }
    const queryResult = {
      ...satuanJenis,
      id: Number(satuanJenis.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JENIS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'date';
      };
    } = {
      nama: { field: 'nama', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, satuanJenis] = await this.prisma.$transaction([
      this.prisma.satuan_jenis.count({
        where: where,
      }),
      this.prisma.satuan_jenis.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = satuanJenis.map((satuanjeniss) => ({
      ...satuanjeniss,
      id: Number(satuanjeniss.id),
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JENIS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req, id, body: UpdateSatuanJenisDto) {
    const { nama } = body;

    const satuanJenis = await this.prisma.satuan_jenis.findFirst({
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!satuanJenis) {
      throw new NotFoundException(`satuan jenis id ${id} not found`);
    }

    if (
      await this.prisma.satuan_jenis.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
          id: {
            not: {
              in: [+id],
            },
          },
        },
      })
    ) {
      throw new BadRequestException('nama satuan jenis already exists');
    }

    const updatedSatuanJenis = await this.prisma.satuan_jenis.update({
      where: { id: +id },
      data: {
        nama,
        updated_at: new Date(),
      },
    });

    const queryResult = {
      ...updatedSatuanJenis,
      id: Number(updatedSatuanJenis.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JENIS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: updatedSatuanJenis,
    };
  }

  async delete(req, id) {
    const satuanJenis = await this.prisma.satuan_jenis.findFirst({
      where: { id: +id },
    });

    if (!satuanJenis) {
      throw new NotFoundException(`satuan jenis id ${id} not found`);
    }

    const updatedSatuanJenis = await this.prisma.satuan_jenis.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const queryResult = {
      ...updatedSatuanJenis,
      id: Number(updatedSatuanJenis.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JENIS_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: satuanJenis,
    };
  }
}
