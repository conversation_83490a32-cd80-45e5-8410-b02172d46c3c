import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { SatuanJenisService } from '../service/satuan-jenis.service';
import {
  CreateSatuanJenisDto,
  UpdateSatuanJenisDto,
} from '../dto/satuan-jenis.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { MODULES } from '../../../../core/constants/module.constant';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';

@Controller('satuan-jenis')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class SatuanJenisController {
  private readonly logger = new Logger(SatuanJenisController.name);

  constructor(private readonly satuanJenisService: SatuanJenisService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateSatuanJenisDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanJenisService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.satuanJenisService.get(req, id);
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.satuanJenisService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateSatuanJenisDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanJenisService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.satuanJenisService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
