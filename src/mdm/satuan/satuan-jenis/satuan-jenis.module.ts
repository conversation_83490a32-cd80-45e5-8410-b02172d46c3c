import { forwardRef, Module } from '@nestjs/common';
import { SatuanJenisService } from './service/satuan-jenis.service';
import { <PERSON>tuanJenisController } from './controller/satuan-jenis.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SatuanJenisController],
  providers: [SatuanJenisService],
})
export class SatuanJenisModule {}
