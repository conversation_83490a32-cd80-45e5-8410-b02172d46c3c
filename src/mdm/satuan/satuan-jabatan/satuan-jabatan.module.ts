import { forwardRef, Module } from '@nestjs/common';
import { SatuanJabatanService } from './service/satuan-jabatan.service';
import { SatuanJabatanController } from './controller/satuan-jabatan.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { ExcelModule } from '../../../api-utils/excel/excel.module';
import { FieldValidatorPipe } from '../../../core/validator/field.validator';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [
    PrismaModule,
    LogsActivityModule,
    ExcelModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [SatuanJabatanController],
  providers: [SatuanJabatanService, FieldValidatorPipe],
  exports: [SatuanJabatanService],
})
export class SatuanJabatanModule {}
