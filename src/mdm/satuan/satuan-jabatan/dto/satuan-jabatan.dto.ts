import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

const ToNumber = () =>
  Transform(({ value }) => {
    const val = Number(value);
    return isNaN(val) ? null : val;
  });

export class ValidateSatuanJabatanDto {
  @ToNumber()
  @IsNumber()
  @IsNotEmpty()
  jenis_satuan_tujuan: number;
}

export class CreateSatuanJabatanDto extends ValidateSatuanJabatanDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DetailSatuanJabatanDto)
  data: DetailSatuanJabatanDto[];
}

export class DetailSatuanJabatanDto {
  @ToNumber()
  @IsNumber()
  @IsOptional()
  no?: string;

  @IsString()
  @IsNotEmpty({ message: 'Jabatan tidak boleh kosong' })
  jabatan: string;

  @IsString()
  @IsNotEmpty({ message: 'Satuan tidak boleh kosong' })
  satuan: string;

  @IsString()
  @IsNotEmpty({ message: 'Pangkat tidak boleh kosong' })
  pangkat: string;

  @IsOptional()
  @ToNumber()
  @IsNumber()
  dsp?: number;

  @IsOptional()
  @IsString()
  nivellering?: string;

  @IsOptional()
  @IsString()
  pj?: string;

  @IsOptional()
  @IsString()
  status_akhir?: string;

  @IsOptional()
  @IsString()
  description?: string;
}
