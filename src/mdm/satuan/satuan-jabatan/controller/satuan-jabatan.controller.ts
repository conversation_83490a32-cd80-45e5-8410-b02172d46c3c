import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
} from '@nestjs/common';
import { SatuanJabatanService } from '../service/satuan-jabatan.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';
import { Module, Permission } from '../../../../core/decorators';
import {
  CreateSatuanJabatanDto,
  ValidateSatuanJabatanDto,
} from '../dto/satuan-jabatan.dto';
import { FieldValidatorPipe } from '../../../../core/validator/field.validator';
import { FileInterceptor } from '@nestjs/platform-express';
import { MODULES } from '../../../../core/constants/module.constant';

@Controller('satuan-jabatan')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class SatuanJabatanController {
  private readonly logger = new Logger(SatuanJabatanController.name);

  constructor(private readonly satuanJabatanService: SatuanJabatanService) {}

  @Post('/bulk/validate')
  @UseInterceptors(FileInterceptor('file', { limits: { files: 1 } }))
  @UsePipes(new FieldValidatorPipe())
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async validateProcess(
    @Req() req: any,
    @Body() body: ValidateSatuanJabatanDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.validateProcess.name} with body: ${JSON.stringify(body)} and file size: ${file.size}`,
    );
    const response = await this.satuanJabatanService.validateProcess(
      req,
      body,
      file,
    );
    this.logger.log(
      `Leaving ${this.validateProcess.name} with body: ${JSON.stringify(body)} and file size: ${file.size} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/bulk/create')
  @UsePipes(new FieldValidatorPipe())
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async bulkCreate(@Req() req: any, @Body() body: CreateSatuanJabatanDto) {
    this.logger.log(
      `Entering ${this.bulkCreate.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanJabatanService.bulkCreate(req, body);
    this.logger.log(
      `Leaving ${this.bulkCreate.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
