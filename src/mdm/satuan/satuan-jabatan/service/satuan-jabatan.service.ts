import * as fs from 'fs';
import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateSatuanJabatanDto,
  DetailSatuanJabatanDto,
  ValidateSatuanJabatanDto,
} from '../dto/satuan-jabatan.dto';
import { ExcelService } from '../../../../api-utils/excel/service/excel.service';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { FieldValidatorPipe } from '../../../../core/validator/field.validator';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class SatuanJabatanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly excelService: ExcelService,
    private readonly fieldValidatorPipe: FieldValidatorPipe,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async validateProcess(
    req: any,
    body: ValidateSatuanJabatanDto,
    file: Express.Multer.File,
  ) {
    if (!file) throw new BadRequestException('File is required');

    const filePath = file.originalname;
    fs.writeFileSync(filePath, file.buffer);
    const resultJson = await this.excelService.extractToJson(
      req,
      filePath,
      true,
    );
    fs.unlinkSync(filePath);

    const validatedData = await this.validateExcelData(resultJson);
    const mappings = await this.mapEntities(
      validatedData,
      BigInt(body.jenis_satuan_tujuan),
    );

    const queryResult = [];
    for (const dataItem of Object.values(validatedData)) {
      if (dataItem.description) {
        queryResult.push(dataItem);
        continue;
      }

      const satuanId = mappings.mappingSatuan[dataItem.satuan];
      if (!satuanId) {
        dataItem.description = 'Satuan tidak ditemukan';
        queryResult.push(dataItem);
        continue;
      }
      queryResult.push(dataItem);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async bulkCreate(req: any, body: CreateSatuanJabatanDto) {
    const { jenis_satuan_tujuan, data } = body;

    const mappings = await this.mapEntities(data, BigInt(jenis_satuan_tujuan));

    const results = [];
    for (const dataItem of Object.values(data)) {
      if (dataItem.description)
        throw new BadRequestException(dataItem.description);

      const satuanId = mappings.mappingSatuan[dataItem.satuan];
      if (!satuanId) throw new BadRequestException('Satuan tidak ditemukan');

      const jabatanExist = await this.prisma.jabatan.findFirst({
        where: {
          nama: dataItem.jabatan,
          satuan_id: satuanId,
          deleted_at: null,
        },
        select: { id: true },
      });

      if (!jabatanExist) {
        results.push({
          nama: dataItem.jabatan,
          dsp: Number(dataItem.dsp),
          nivellering_id:
            mappings.mappingNivellering[dataItem.nivellering] ?? null,
          satuan_id: satuanId,
          kategori_id: 1,
          is_aktif: true,
          jabatan_pangkat: {
            create: dataItem.pangkat
              .split(', ')
              .map((item) => {
                return { pangkat_id: mappings.mappingPangkat[item] };
              })
              .filter(Boolean),
          },
        });
      }
    }

    if (results.length) {
      await this.prisma.$transaction(
        results.map((result) => this.prisma.jabatan.create({ data: result })),
      );
    }

    const queryResult = { isCreated: true };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_JABATAN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  private async validateExcelData(
    data: any[],
  ): Promise<DetailSatuanJabatanDto[]> {
    const dtoInstances: any = plainToInstance(DetailSatuanJabatanDto, data);
    const results: DetailSatuanJabatanDto[] = [];

    for (const instance of dtoInstances?.data) {
      const errors = await validate(instance);
      results.push({
        ...instance,
        description: errors.length
          ? Object.values(this.fieldValidatorPipe.formatErrors(errors)).join(
              ', ',
            )
          : null,
      });
    }

    return results;
  }

  private async mapEntities(
    data: DetailSatuanJabatanDto[],
    jenis_satuan: bigint,
  ) {
    const [mappingSatuan, mappingPangkat, mappingNivellering] =
      await Promise.all([
        this.mapSatuan(data, jenis_satuan),
        this.mapPangkat(data),
        this.mapNivellering(data),
      ]);

    return { mappingSatuan, mappingPangkat, mappingNivellering };
  }

  private async mapSatuan(
    data: DetailSatuanJabatanDto[],
    jenis_satuan: bigint,
  ) {
    const mappingSatuan: Record<string, bigint> = {};
    await Promise.all(
      Object.values(data)
        .filter((item) => !item.description)
        .map(async (item) => {
          if (!mappingSatuan[item.satuan]) {
            mappingSatuan[item.satuan] = await this.getSatuanRealId(
              item.satuan,
              jenis_satuan,
            );
          }
        }),
    );
    return mappingSatuan;
  }

  private async mapPangkat(data: DetailSatuanJabatanDto[]) {
    const mappingPangkat: Record<string, bigint> = {};

    await Promise.all(
      Object.values(data)
        .filter((item) => item.pangkat && !item.description)
        .flatMap((item) => item.pangkat.split(', '))
        .map(async (value) => {
          if (!mappingPangkat[value]) {
            const resultPangkat = await this.prisma.pangkat.findFirst({
              where: {
                OR: [{ nama: value }, { nama_singkat: value }],
                deleted_at: null,
              },
              select: { id: true },
            });
            if (resultPangkat?.id) mappingPangkat[value] = resultPangkat.id;
          }
        }),
    );
    return mappingPangkat;
  }

  private async mapNivellering(data: DetailSatuanJabatanDto[]) {
    const mappingNivellering: Record<string, bigint> = {};

    await Promise.all(
      Object.values(data)
        .filter((item) => item.nivellering && !item.description)
        .map(async (item) => {
          if (!mappingNivellering[item.nivellering]) {
            const resultNivellering = await this.prisma.nivellering.findFirst({
              where: { nama: item.nivellering, deleted_at: null },
              select: { id: true },
            });
            if (resultNivellering?.id)
              mappingNivellering[item.nivellering] = resultNivellering.id;
          }
        }),
    );
    return mappingNivellering;
  }

  private async getSatuanRealId(
    satuan: string,
    jenis_satuan: bigint,
  ): Promise<bigint | null> {
    const splitSatuan = satuan.split(' - ');
    const name = splitSatuan[splitSatuan.length - 1];

    const result = await this.prisma.$queryRaw<
      { real_id: bigint; path: string }[]
    >`
        WITH RECURSIVE satuan_hierarchy AS (SELECT id AS real_id, id, nama, atasan_id, nama::TEXT AS path, 1 AS depth
                                            FROM satuan
                                            WHERE nama = ${name}
                                              AND jenis_id = ${jenis_satuan}
                                            UNION ALL
                                            SELECT sh.real_id,
                                                   s.id,
                                                   s.nama,
                                                   s.atasan_id,
                                                   s.nama || ' - ' || sh.path,
                                                   sh.depth + 1
                                            FROM satuan s
                                                     JOIN satuan_hierarchy sh ON sh.atasan_id = s.id
                                            WHERE sh.depth < ${splitSatuan.length})
        SELECT real_id
        FROM satuan_hierarchy
        WHERE path = ${satuan}
        ORDER BY depth DESC LIMIT 1;
    `;
    return result.length ? result[0].real_id : null;
  }
}
