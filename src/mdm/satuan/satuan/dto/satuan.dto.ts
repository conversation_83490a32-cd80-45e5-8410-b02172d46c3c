import { PartialType } from '@nestjs/mapped-types';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEmpty,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateSatuanDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNotEmpty()
  @IsString()
  alamat: string;

  @IsNumber()
  @IsNotEmpty()
  jenis_id: number;

  @IsNumber()
  @IsNotEmpty()
  atasan_id: number;

  @IsBoolean()
  is_aktif: boolean;

  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  fungsi_satuan_id: number[];
}

export class UpdateSatuanDto extends PartialType(CreateSatuanDto) {
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  fungsi_satuan_id?: number[];
}

export class CreateFungsiDto {
  @IsString({ message: '<PERSON>a harus berupa string' })
  @IsNotEmpty({ message: 'Nama tidak boleh kosong' })
  nama: string;

  @IsString()
  @IsOptional()
  kode: string;

  @IsOptional()
  @IsBoolean()
  is_aktif?: boolean;
}

export class UpdateFungsiDto extends PartialType(CreateFungsiDto) {}
