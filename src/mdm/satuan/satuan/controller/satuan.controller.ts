import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SatuanService } from '../service/satuan.service';
import {
  CreateFungsiDto,
  CreateSatuanDto,
  UpdateSatuanDto,
} from '../dto/satuan.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { MODULES } from '../../../../core/constants/module.constant';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';

@Controller('satuan')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class SatuanController {
  private readonly logger = new Logger(SatuanController.name);

  constructor(private readonly satuanService: SatuanService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateSatuanDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.satuanService.get(req, Number(id));
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.satuanService.getList(
      req,
      paginationData,
      searchAndSortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: UpdateSatuanDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanService.update(req, Number(id), body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.satuanService.delete(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/hirarki/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListSatuanHirarki(@Req() req: any) {
    this.logger.log(`Entering ${this.getListSatuanHirarki.name}`);
    const response = await this.satuanService.getListSatuanHirarki(req);
    this.logger.log(
      `Leaving ${this.getListSatuanHirarki.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/polda/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListSatuanPolda(@Req() req: any) {
    this.logger.log(`Entering ${this.getListSatuanPolda.name}`);
    const response = await this.satuanService.getListSatuanPolda(req);
    this.logger.log(
      `Leaving ${this.getListSatuanPolda.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/polres/list')
  @HttpCode(200)
  async getListSatuanPolres(@Req() req: any) {
    const response = await this.satuanService.getListSatuanPolres(req);
    return response;
  }

  @Get('/satker/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListSatker(@Req() req: any) {
    this.logger.log(`Entering ${this.getListSatker.name}`);
    const response = await this.satuanService.getListSatker(req);
    this.logger.log(
      `Leaving ${this.getListSatker.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/fungsi/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  @UsePipes(new ValidationPipe({ transform: true }))
  async getListFungsi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListFungsi.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.satuanService.getListFungsi(
      req,
      paginationData,
      searchAndSortData,
    );
    this.logger.log(
      `Leaving ${this.getListFungsi.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/fungsi/list/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getFungsiById(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getFungsiById.name} with id: ${id}`);
    const response = await this.satuanService.getFungsiById(req, Number(id));
    this.logger.log(
      `Leaving ${this.getFungsiById.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/fungsi/list')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createFungsi(
    @Req() req: any,
    @Body(FieldValidatorPipe) body: CreateFungsiDto,
  ) {
    this.logger.log(
      `Entering ${this.createFungsi.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanService.createFungsi(req, body);
    this.logger.log(
      `Leaving ${this.createFungsi.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/fungsi/list/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateFungsi(
    @Req() req: any,
    @Param('id') id: string,
    @Body(FieldValidatorPipe) body: CreateFungsiDto,
  ) {
    this.logger.log(
      `Entering ${this.updateFungsi.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.satuanService.updateFungsi(
      req,
      Number(id),
      body,
    );
    this.logger.log(
      `Leaving ${this.updateFungsi.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
