import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateFungsiDto,
  CreateSatuanDto,
  UpdateFungsiDto,
  UpdateSatuanDto,
} from '../dto/satuan.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PaginationDto, SearchAndSortDTO } from '../../../../core/dtos';
import { IColumnMapping } from '../../../../core/interfaces/db.interface';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class SatuanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateSatuanDto) {
    const { nama, alamat, jenis_id, is_aktif, atasan_id, fungsi_satuan_id } =
      body;

    const satuanName = await this.prisma.satuan.findFirst({
      where: {
        nama: nama,
        deleted_at: null,
      },
    });

    if (satuanName) throw new BadRequestException('Nama satuan sudah ada');

    const fungsiSatuan = await this.prisma.fungsi.findMany({
      where: {
        id: {
          in: fungsi_satuan_id,
        },
        deleted_at: null,
      },
    });

    if (fungsiSatuan.length !== fungsi_satuan_id.length) {
      throw new NotFoundException('Beberapa fungsi id tidak di temukan');
    }

    const satuanJenis = await this.prisma.satuan_jenis.findFirst({
      where: {
        id: jenis_id,
        deleted_at: null,
      },
    });

    if (!satuanJenis)
      throw new NotFoundException('Satuan jenis tidak terdaftar');

    const atasan = await this.prisma.satuan.findFirst({
      where: {
        id: atasan_id,
        deleted_at: null,
      },
    });

    if (!atasan) throw new NotFoundException(`Atasan tidak terdaftar`);

    const satuan = await this.prisma.$transaction(async (tx) => {
      await tx.$executeRaw`REFRESH MATERIALIZED VIEW mv_atasan_satuan`;
      await tx.$executeRaw`REFRESH MATERIALIZED VIEW mv_satuan_hirarki`;

      const newSatuan = await tx.satuan.create({
        data: {
          nama: nama,
          alamat: alamat,
          jenis_id: jenis_id,
          is_aktif: Boolean(is_aktif),
          atasan_id: atasan_id,
          created_at: new Date(),
        },
      });

      const createFungsiSatuan = await Promise.all(
        fungsi_satuan_id.map(async (fs) => {
          return tx.fungsi_satuan.create({
            data: {
              satuan_id: newSatuan.id,
              fungsi_id: fs,
              created_at: new Date(),
            },
          });
        }),
      );

      return { ...newSatuan, fungsi_satuan: createFungsiSatuan };
    });

    const queryResult = {
      ...satuan,
      id: Number(satuan.id),
      jenis_id: Number(satuan.jenis_id),
      atasan_id: satuan.atasan_id ? Number(satuan.atasan_id) : null,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const satuan = await this.prisma.satuan.findFirst({
      select: {
        id: true,
        nama: true,
        alamat: true,
        satuan_jenis: {
          select: {
            id: true,
            nama: true,
          },
        },
        atasan_satuan: {
          select: {
            id: true,
            nama: true,
            alamat: true,
          },
        },
        is_aktif: true,
      },
      where: {
        id: id,
        deleted_at: null,
      },
    });

    if (!satuan) throw new NotFoundException(`Satuan tidak terdaftar`);

    const queryResult = {
      ...satuan,
      id: Number(satuan.id),
      satuan_jenis: satuan.satuan_jenis
        ? {
            ...satuan.satuan_jenis,
            id: Number(satuan.satuan_jenis.id),
          }
        : null,
      atasan_satuan: satuan.atasan_satuan
        ? {
            ...satuan.atasan_satuan,
            id: Number(satuan.atasan_satuan.id),
          }
        : null,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: satuan,
    };
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      alamat: { field: 'alamat', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      atasan_satuan: { field: 'atasan_satuan.nama', type: 'string' },
      atasan_satuan_id: { field: 'atasan_satuan.id', type: 'bigint' },
      satuan_jenis_id: { field: 'satuan_jenis.id', type: 'bigint' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      id: { field: 'id', type: 'bigint' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    const [totalData, satuans] = await this.prisma.$transaction([
      this.prisma.satuan.count({
        where: where,
      }),
      this.prisma.satuan.findMany({
        select: {
          id: true,
          nama: true,
          alamat: true,
          satuan_jenis: {
            select: {
              id: true,
              nama: true,
            },
          },
          atasan_satuan: {
            select: {
              id: true,
              nama: true,
              alamat: true,
            },
          },
          is_aktif: true,
          fungsi_satuan: {
            select: {
              fungsi: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
            where: { deleted_at: null },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    async function getHierarchy(prisma, startId) {
      let results = [];
      let currentId = startId;

      while (currentId) {
        const row = await prisma.satuan.findFirst({
          where: {
            id: currentId,
          },
          select: {
            id: true,
            nama: true,
            atasan_id: true,
          },
        });

        if (!row || row.length === 0) break;

        results.push({
          id: row.id.toString(),
          nama: row.nama,
        });

        currentId = row.atasan_id;
      }

      return results;
    }

    const queryResult = await Promise.all(
      satuans.map(async (satuan) => {
        const hierarchy = satuan.atasan_satuan
          ? await getHierarchy(this.prisma, satuan.atasan_satuan.id)
          : [];

        return {
          ...satuan,
          id: Number(satuan.id),
          group_name: [
            ...hierarchy.map((h) => h.nama).reverse(),
            satuan.nama,
          ].join(' - '),
          satuan_jenis: satuan.satuan_jenis
            ? {
                ...satuan.satuan_jenis,
                id: Number(satuan.satuan_jenis.id),
              }
            : null,
          atasan_satuan: satuan.atasan_satuan
            ? {
                ...satuan.atasan_satuan,
                id: Number(satuan.atasan_satuan.id),
              }
            : null,
          fungsi_satuan: satuan.fungsi_satuan
            ? satuan.fungsi_satuan.map((fs) => fs.fungsi)
            : [],
        };
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req: any, id: number, body: UpdateSatuanDto) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const { nama, alamat, jenis_id, is_aktif, atasan_id, fungsi_satuan_id } =
      body;

    const satuan = await this.prisma.satuan.findFirst({
      where: {
        id,
        deleted_at: null,
      },
    });

    if (!satuan) {
      throw new NotFoundException(`satuan id ${id} not found`);
    }

    // Filter nilai 0
    const validFungsiIds = Array.isArray(fungsi_satuan_id)
      ? fungsi_satuan_id.filter((id) => id !== 0)
      : [];

    let fungsiSatuan = [];

    if (validFungsiIds.length > 0) {
      fungsiSatuan = await this.prisma.fungsi.findMany({
        where: {
          id: {
            in: validFungsiIds,
          },
          deleted_at: null,
        },
      });

      if (fungsiSatuan.length !== validFungsiIds.length) {
        throw new NotFoundException('beberapa fungsi id tidak di temukan');
      }
    }

    const satuanJenis = await this.prisma.satuan_jenis.findFirst({
      where: {
        id: jenis_id,
        deleted_at: null,
      },
    });

    if (!satuanJenis) throw new NotFoundException('Satuan jenis terdaftar');

    const atasan = await this.prisma.satuan.findFirst({
      where: {
        id: atasan_id,
        deleted_at: null,
      },
    });

    if (!atasan) throw new NotFoundException('Atasan tidak terdaftar');

    const updatedSatuan = await this.prisma.$transaction(async (tx) => {
      await tx.$executeRaw`REFRESH MATERIALIZED VIEW mv_atasan_satuan`;
      await tx.$executeRaw`REFRESH MATERIALIZED VIEW mv_satuan_hirarki`;

      const newUpdateSatuan = await this.prisma.satuan.update({
        where: { id },
        data: {
          nama,
          alamat,
          jenis_id,
          is_aktif: Boolean(is_aktif),
          atasan_id,
          updated_at: new Date(),
        },
      });

      const fungsi = await tx.fungsi_satuan.findMany({
        where: { satuan_id: newUpdateSatuan.id },
      });

      if (fungsi) {
        await tx.fungsi_satuan.updateMany({
          where: { satuan_id: newUpdateSatuan.id },
          data: { deleted_at: new Date() },
        });
      }

      if (validFungsiIds.length > 0) {
        // Hapus yang lama
        await tx.fungsi_satuan.updateMany({
          where: { satuan_id: newUpdateSatuan.id },
          data: { deleted_at: new Date() },
        });

        // Tambah yang baru
        fungsiSatuan = await Promise.all(
          fungsi_satuan_id.map(async (fs) => {
            return tx.fungsi_satuan.create({
              data: {
                satuan_id: newUpdateSatuan.id,
                fungsi_id: fs,
                created_at: new Date(),
              },
            });
          }),
        );
      } else {
        // Kalau kosong atau tidak dikirim, ambil data sebelumnya saja
        fungsiSatuan = await this.prisma.fungsi_satuan.findMany({
          where: {
            satuan_id: newUpdateSatuan.id,
            deleted_at: null,
          },
        });
      }

      return { ...newUpdateSatuan, fungsi_satuan: fungsiSatuan };
    });

    const queryResult = {
      ...updatedSatuan,
      id: Number(updatedSatuan.id),
      jenis_id: Number(updatedSatuan.jenis_id),
      atasan_id: updatedSatuan.atasan_id
        ? Number(updatedSatuan.atasan_id)
        : null,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const satuan = await this.prisma.satuan.findFirst({
      where: { id },
    });

    if (!satuan) throw new NotFoundException('Satuan tidak terdaftar');

    const updatedSatuan = await this.prisma.satuan.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });

    const queryResult = {
      ...updatedSatuan,
      id: Number(updatedSatuan.id),
      jenis_id: Number(updatedSatuan.jenis_id),
      atasan_id: updatedSatuan.atasan_id
        ? Number(updatedSatuan.atasan_id)
        : null,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListSatuanHirarki(req: any) {
    const user = req.user;
    if (!user.satuan_id)
      throw new BadRequestException('Personel tidak mempunyai satuan id');

    const hirarki = await this.prisma.satuan_hirarki.findFirst({
      where: { satuan_id: Number(user.satuan_id) },
    });

    const queryResult = await this.prisma.satuan.findMany({
      where: {
        id: {
          in: hirarki.atasan_id,
        },
      },
      select: {
        id: true,
        nama: true,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListSatuanPolda(req: any) {
    const queryResult = await this.prisma.satuan.findMany({
      select: { id: true, nama: true },
      where: {
        OR: [
          {
            nama: {
              startsWith: 'polda',
              mode: 'insensitive',
            },
          },
          {
            nama: {
              equals: 'polri', // apakah ini perlu?
              mode: 'insensitive',
            },
          },
        ],
      },
      orderBy: {
        nama: 'asc',
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListSatuanPolres(req: any) {
    const queryResult = await this.prisma.satuan.findMany({
      select: { id: true, nama: true },
      where: {
        OR: [
          {
            nama: {
              startsWith: 'polres',
              mode: 'insensitive',
            },
          },
        ],
      },
      orderBy: {
        nama: 'asc',
      },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: queryResult,
    };
  }

  async findByName(nama: string) {
    return await this.prisma.satuan.findFirst({
      where: { nama },
    });
  }

  async getListSatker(req: any) {
    const queryResult = await this.prisma.satuan.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        is_aktif: true,
        deleted_at: null,
      },
      orderBy: {
        nama: 'asc',
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListFungsi(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      kode: { field: 'kode', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.fungsi.count({
        where: where,
      }),
      this.prisma.fungsi.findMany({
        select: {
          id: true,
          nama: true,
          kode: true,
          is_aktif: true,
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getFungsiById(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const queryResult = await this.prisma.fungsi.findFirst({
      where: { id, deleted_at: null },
    });

    if (!queryResult) throw new NotFoundException('Fungsi tidak terdaftar');

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createFungsi(req: any, body: CreateFungsiDto) {
    const { nama, kode, is_aktif } = body;

    const fungsi = await this.prisma.fungsi.findFirst({
      where: { nama, deleted_at: null },
    });

    if (fungsi) throw new ConflictException('Nama fungsi sudah ada');

    const queryResult = await this.prisma.fungsi.create({
      data: { nama, kode: String(kode), is_aktif: Boolean(is_aktif) },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  async updateFungsi(req: any, id: number, body: UpdateFungsiDto) {
    const { nama, kode, is_aktif } = body;

    const fungsi = await this.prisma.fungsi.findFirst({
      where: { id, deleted_at: null },
    });

    if (!fungsi) {
      throw new NotFoundException('Fungsi tidak ditemukan');
    }

    const queryResult = await this.prisma.fungsi.update({
      where: { id },
      data: { nama, kode: String(kode), is_aktif: Boolean(is_aktif) },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
