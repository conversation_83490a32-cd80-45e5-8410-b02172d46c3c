import { forwardRef, Module } from '@nestjs/common';
import { SatuanService } from './service/satuan.service';
import { SatuanController } from './controller/satuan.controller';
import { IsUniqueConstraint } from 'src/core/validator/is-unique.validator';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module'; // Path ke custom validator
// import { IsUniqueConstraint } from 'src/core/helpers/IsUnique'; // Path ke custom validator

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SatuanController],
  providers: [SatuanService, IsUniqueConstraint],
  exports: [SatuanService],
})
export class SatuanModule {}
