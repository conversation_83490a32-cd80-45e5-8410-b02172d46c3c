import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateJabatanKategoriDto,
  UpdateJabatanKategoriDto,
} from '../dto/jabatan-kategori.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class JabatanKategoriService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateJabatanKategoriDto) {
    const { nama, is_aktif } = body;

    if (
      await this.prisma.jabatan_kategori.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('nama jabatan kategori already exists');
    }

    const queryResult = await this.prisma.jabatan_kategori.create({
      data: {
        nama: nama,
        is_aktif: is_aktif ?? true,
        created_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_KATEGORI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const queryResult = await this.prisma.jabatan_kategori.findFirst({
      select: {
        id: true,
        nama: true,
        is_aktif: true,
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(
        `jabatann kategori id ${id} tidak di temukan`,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_KATEGORI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    console.log(queryResult);

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'date';
      };
    } = {
      nama: { field: 'nama', type: 'string' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.jabatan_kategori.count({
        where: where,
      }),
      this.prisma.jabatan_kategori.findMany({
        select: {
          id: true,
          nama: true,
          is_aktif: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_KATEGORI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req, id, body: UpdateJabatanKategoriDto) {
    const { nama, is_aktif } = body;

    const jabatanKategori = await this.prisma.jabatan_kategori.findFirst({
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!jabatanKategori) {
      throw new NotFoundException(`jabatan kaetegori id ${id} not found`);
    }

    if (
      await this.prisma.jabatan_kategori.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
          id: {
            not: {
              in: [+id],
            },
          },
        },
      })
    ) {
      throw new BadRequestException('nama jabatan kategori already exists');
    }

    const queryResult = await this.prisma.jabatan_kategori.update({
      where: { id: +id },
      data: {
        nama: nama,
        is_aktif: is_aktif ?? true,
        updated_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_KATEGORI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req, id) {
    const jabatanKategori = await this.prisma.jabatan_kategori.findFirst({
      where: { id: +id },
    });

    if (!jabatanKategori) {
      throw new NotFoundException(`jabatan kategori id ${id} not found`);
    }

    const queryResult = await this.prisma.jabatan_kategori.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_KATEGORI_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
