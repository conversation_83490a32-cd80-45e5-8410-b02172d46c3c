import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JabatanKategoriService } from './service/jabatan-kategori.service';
import {
  CreateJabatanKategoriDto,
  UpdateJabatanKategoriDto,
} from './dto/jabatan-kategori.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('jabatan-kategori')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class JabatanKategoriController {
  private readonly logger = new Logger(JabatanKategoriController.name);

  constructor(
    private readonly jabatanKategoriService: JabatanKategoriService,
  ) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateJabatanKategoriDto) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.jabatanKategoriService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.jabatanKategoriService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with paginationData ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.jabatanKategoriService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with paginationData ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateJabatanKategoriDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );
    const response = await this.jabatanKategoriService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);
    const response = await this.jabatanKategoriService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
