import { forwardRef, Module } from '@nestjs/common';
import { JabatanKategoriService } from './service/jabatan-kategori.service';
import { JabatanKategoriController } from './jabatan-kategori.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [JabatanKategoriController],
  providers: [JabatanKategoriService],
})
export class JabatanKategoriModule {}
