import { forwardRef, Module } from '@nestjs/common';
import { JabatanService } from './service/jabatan.service';
import { JabatanController } from './controller/jabatan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [JabatanController],
  providers: [JabatanService],
})
export class JabatanModule {}
