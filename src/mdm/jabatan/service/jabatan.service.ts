import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateJabatanDto, UpdateJabatanDto } from '../dto/jabatan.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class JabatanService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateJabatanDto) {
    const {
      nama,
      dsp,
      nivellering_id,
      satuan_id,
      atasan_id,
      kategori_id,
      pangkat_id,
      is_aktif,
    } = body;

    if (
      await this.prisma.jabatan.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('nama jabatan sudah ada');
    }

    const pangkat = await this.prisma.pangkat.findMany({
      where: {
        id: {
          in: pangkat_id,
        },
        deleted_at: null,
      },
    });

    if (pangkat.length !== pangkat_id.length) {
      throw new NotFoundException('beberapa pangkat id tidak di temukan');
    }

    const satuan = await this.prisma.satuan.findFirst({
      where: {
        id: +satuan_id,
        deleted_at: null,
      },
    });

    if (!satuan) {
      throw new NotFoundException(`satuan id ${satuan_id} tidak di temukan`);
    }

    const nivellering = await this.prisma.nivellering.findFirst({
      where: {
        id: +nivellering_id,
        deleted_at: null,
      },
    });

    if (!nivellering) {
      throw new NotFoundException(
        `nivellering id ${nivellering_id} tidak di temukan`,
      );
    }

    const kategori = await this.prisma.jabatan_kategori.findFirst({
      where: {
        id: +kategori_id,
        deleted_at: null,
      },
    });

    if (!kategori) {
      throw new NotFoundException(
        `jabatan kategori id ${kategori_id} tidak di temukan`,
      );
    }

    const atasan = await this.prisma.jabatan.findFirst({
      where: {
        id: +atasan_id,
        deleted_at: null,
      },
    });

    if (!atasan) {
      throw new NotFoundException(`atasan id ${atasan_id} tidak di temukan`);
    }

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const createJabatan = await tx.jabatan.create({
        data: {
          nama: nama,
          dsp: dsp,
          nivellering_id: nivellering_id,
          satuan_id: satuan_id,
          atasan_id: atasan_id,
          kategori_id: kategori_id,
          created_at: new Date(),
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const createJabatanPangkat = await Promise.all(
        pangkat_id.map(async (pangkatId) => {
          return tx.jabatan_pangkat.create({
            data: {
              jabatan_id: createJabatan.id,
              pangkat_id: pangkatId,
            },
          });
        }),
      );

      return { jabatan: createJabatan, jabatan_pangkat: createJabatanPangkat };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const queryResult = await this.prisma.jabatan.findFirst({
      select: {
        id: true,
        nama: true,
        dsp: true,
        nivellering: {
          select: {
            id: true,
            nama: true,
          },
        },
        satuan: {
          select: {
            id: true,
            nama: true,
          },
        },
        atasan_jabatan: {
          select: {
            id: true,
            nama: true,
          },
        },
        jabatan_kategori: {
          select: {
            id: true,
            nama: true,
          },
        },
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(`jabatan id ${id} tidak di temukan`);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number' | 'date';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      dsp: { field: 'dsp', type: 'number' },
      nivellering_nama: { field: 'nivellering.nama', type: 'string' },
      // atasan_jabatan_nama: { field: 'atasan_jabatan.nama', type: 'string' },
      satuan_id: { field: 'satuan_id', type: 'bigint' },
      atasan_id: { field: 'atasan_id', type: 'bigint' },
      // pangkat: { field: 'jabatan_pangkat.pangkat.nama', type: 'string' },
      // jabatan_kategori_nama: { field: 'jabatan_kategori.nama', type: 'string' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, jabatans] = await this.prisma.$transaction([
      this.prisma.jabatan.count({
        where: where,
      }),
      this.prisma.jabatan.findMany({
        select: {
          id: true,
          nama: true,
          dsp: true,
          is_aktif: true,
          nivellering: {
            select: {
              id: true,
              nama: true,
            },
          },
          satuan: {
            select: {
              id: true,
              nama: true,
            },
          },
          atasan_jabatan: {
            select: {
              id: true,
              nama: true,
            },
          },
          jabatan_kategori: {
            select: {
              id: true,
              nama: true,
            },
          },
          jabatan_pangkat: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = jabatans.map(({ jabatan_pangkat, ...data }) => ({
      ...data,
      pangkat: jabatan_pangkat.map((jp) => jp.pangkat),
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_READ as ConstantLogType,
        message,
        jabatans,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req, id, body: UpdateJabatanDto) {
    const {
      nama,
      dsp,
      nivellering_id,
      satuan_id,
      atasan_id,
      kategori_id,
      pangkat_id,
      is_aktif,
    } = body;

    // Cek jika nama jabatan sudah ada dan bukan milik ID ini
    if (
      typeof nama !== 'undefined' &&
      (await this.prisma.jabatan.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
          id: {
            not: {
              in: [+id],
            },
          },
        },
      }))
    ) {
      throw new BadRequestException('nama jabatan sudah ada');
    }

    // Validasi pangkat jika dikirim
    if (pangkat_id && pangkat_id.length > 0) {
      const pangkat = await this.prisma.pangkat.findMany({
        where: {
          id: { in: pangkat_id },
          deleted_at: null,
        },
      });

      if (pangkat.length !== pangkat_id.length) {
        throw new NotFoundException('beberapa pangkat id tidak di temukan');
      }
    }

    // Validasi satuan jika dikirim
    if (typeof satuan_id !== 'undefined') {
      const satuan = await this.prisma.satuan.findFirst({
        where: {
          id: +satuan_id,
          deleted_at: null,
        },
      });

      if (!satuan) {
        throw new NotFoundException(`satuan id ${satuan_id} tidak di temukan`);
      }
    }

    // Validasi nivellering jika dikirim
    if (typeof nivellering_id !== 'undefined' && nivellering_id > 0) {
      const nivellering = await this.prisma.nivellering.findFirst({
        where: {
          id: +nivellering_id,
          deleted_at: null,
        },
      });

      if (!nivellering) {
        throw new NotFoundException(
          `nivellering id ${nivellering_id} tidak di temukan`,
        );
      }
    }

    // Validasi kategori jika dikirim
    if (typeof kategori_id !== 'undefined') {
      const kategori = await this.prisma.jabatan_kategori.findFirst({
        where: {
          id: +kategori_id,
          deleted_at: null,
        },
      });

      if (!kategori) {
        throw new NotFoundException(
          `jabatan kategori id ${kategori_id} tidak di temukan`,
        );
      }
    }

    // Validasi atasan jika dikirim
    if (typeof atasan_id !== 'undefined') {
      const atasan = await this.prisma.jabatan.findFirst({
        where: {
          id: +atasan_id,
          deleted_at: null,
        },
      });

      if (!atasan) {
        throw new NotFoundException(`atasan id ${atasan_id} tidak di temukan`);
      }
    }

    const queryResult = await this.prisma.$transaction(async (tx) => {
      // Bangun data update secara dinamis
      const data: any = {
        updated_at: new Date(),
      };
      if (typeof nama !== 'undefined') data.nama = nama;
      if (typeof dsp !== 'undefined') data.dsp = dsp;
      if (typeof nivellering_id !== 'undefined')
        data.nivellering_id = nivellering_id;
      if (typeof satuan_id !== 'undefined') data.satuan_id = satuan_id;
      if (typeof atasan_id !== 'undefined') data.atasan_id = atasan_id;
      if (typeof kategori_id !== 'undefined') data.kategori_id = kategori_id;
      if (typeof is_aktif !== 'undefined') data.is_aktif = Boolean(is_aktif);

      const updatedJabatan = await tx.jabatan.update({
        where: { id: +id },
        data,
      });

      let createJabatanPangkat = [];

      // Update pangkat jika dikirim
      if (pangkat_id && pangkat_id.length > 0) {
        await tx.jabatan_pangkat.deleteMany({
          where: { jabatan_id: updatedJabatan.id },
        });

        createJabatanPangkat = await Promise.all(
          pangkat_id.map((pangkatId) =>
            tx.jabatan_pangkat.create({
              data: {
                jabatan_id: updatedJabatan.id,
                pangkat_id: pangkatId,
              },
            }),
          ),
        );
      }

      return { jabatan: updatedJabatan, jabatan_pangkat: createJabatanPangkat };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JABATAN_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req, id) {
    const queryData = await this.prisma.jabatan.findFirst({
      where: { id: +id },
    });

    if (!queryData) {
      throw new NotFoundException(`jabatan id ${id} not found`);
    }

    const queryResult = await this.prisma.jabatan.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JABATAN_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async jabatanPersonel(req, uid: string) {
    try {
      const getJabatanPersonel = await this.prisma.jabatan_personel.findMany({
        select: {
          jabatans: true,
          tmt_jabatan: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tmt_jabatan: 'desc',
        },
      });

      const queryResult = getJabatanPersonel.map((item) => {
        const date = new Date(item.tmt_jabatan);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const tmt = item.tmt_jabatan == null ? null : `${month}-${day}-${year}`;

        return {
          jabatan: item.jabatans.nama,
          tmt: tmt,
        };
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.JABATAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.JABATAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
