import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JabatanService } from '../service/jabatan.service';
import { CreateJabatanDto, UpdateJabatanDto } from '../dto/jabatan.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';

@Controller('jabatan')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class JabatanController {
  private readonly logger = new Logger(JabatanController.name);

  constructor(private readonly jabatanService: JabatanService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateJabatanDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} jabatan with body: ${JSON.stringify(body)}`,
    );
    const response = await this.jabatanService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} jabatan with body: ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} jabatan with id: ${id}`);

    const response = await this.jabatanService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} jabatan with id: ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} jabatan with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.jabatanService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} jabatan with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body(new FieldValidatorPipe()) body: UpdateJabatanDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} jabatan with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.jabatanService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} jabatan with id: ${id} and body: ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} jabatan with id: ${id}`);
    const response = await this.jabatanService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} jabatan with id: ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async jabatanPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.jabatanPersonel.name} jabatan with uid: ${uid}`,
    );
    const response = await this.jabatanService.jabatanPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.jabatanPersonel.name} jabatan with uid: ${uid} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
