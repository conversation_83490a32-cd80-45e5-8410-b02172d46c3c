import { PartialType } from '@nestjs/mapped-types';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { extend } from 'lodash';

export class CreateJabatanDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  dsp: number;

  @IsNumber()
  nivellering_id: number;

  @IsNumber()
  @IsNotEmpty()
  satuan_id: number;

  @IsNumber()
  @IsNotEmpty()
  atasan_id: number;

  @IsNumber()
  @IsNotEmpty()
  kategori_id: number;

  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  pangkat_id: number[];

  @IsBoolean()
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateJabatanDto extends PartialType(CreateJabatanDto) {}
