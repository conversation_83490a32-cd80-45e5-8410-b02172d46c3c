import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PangkatService } from '../service/pangkat.service';
import { CreatePangkatDto, UpdatePangkatDto } from '../dto/pangkat.dto';
import { Module, Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { MODULES } from '../../../../core/constants/module.constant';
import { PermissionGuard } from '../../../../core/guards/permission-auth.guard';

@Controller('pangkat')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PangkatController {
  private readonly logger = new Logger(PangkatController.name);

  constructor(private readonly pangkatService: PangkatService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(@Req() req: any, @Body() createPangkatDto: CreatePangkatDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createPangkatDto)}`,
    );
    const response = await this.pangkatService.create(req, createPangkatDto);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createPangkatDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pangkatService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.pangkatService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updatePangkatDto: UpdatePangkatDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(updatePangkatDto)}`,
    );
    const response = await this.pangkatService.update(
      req,
      +id,
      updatePangkatDto,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(updatePangkatDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} with id: ${id}`);
    const response = await this.pangkatService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} with id: ${id} and response: ${response}`,
    );
    return response;
  }

  @Get('/personel/:uid')
  @HttpCode(200)
  async pangkatPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.pangkatPersonel.name} with uid: ${uid}`);
    const response = await this.pangkatService.pangkatPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.pangkatPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
