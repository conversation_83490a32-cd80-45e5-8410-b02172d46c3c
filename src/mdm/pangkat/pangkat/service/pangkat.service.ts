import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreatePangkatDto, UpdatePangkatDto } from '../dto/pangkat.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from 'src/core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class PangkatService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, createPangkatDto: CreatePangkatDto) {
    try {
      await this._validateSameName(createPangkatDto.nama);

      const queryResult = await this.prisma.pangkat.create({
        data: {
          nama: createPangkatDto.nama,
          nama_singkat: createPangkatDto.nama_singkat,
          kategori_id: Number(createPangkatDto.kategori_id),
          golongan_id: Number(createPangkatDto.golongan_id),
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'number';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        nama_singkat: { field: 'nama_singkat', type: 'string' },
        kategori: { field: 'kategori_id', type: 'number' },
        golongan: { field: 'golongan_id', type: 'number' },
        created_at: { field: 'created_at', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.pangkat.count({
          where,
        }),
        this.prisma.pangkat.findMany({
          select: {
            id: true,
            nama: true,
            nama_singkat: true,
            golongan: {
              select: {
                id: true,
                nama: true,
              },
            },
            pangkat_kategori: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.pangkat.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
          nama_singkat: true,
          pangkat_kategori: {
            select: {
              nama: true,
            },
          },
          golongan: {
            select: {
              nama: true,
            },
          },
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Pangkat tidak ditemukan`);
      }
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, updatePangkatDto: UpdatePangkatDto) {
    try {
      const pangkat = await this.prisma.pangkat.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
          nama_singkat: true,
          kategori_id: true,
          golongan_id: true,
        },
      });

      if (!pangkat) {
        throw new NotFoundException(`Pangkat tidak ditemukan`);
      }

      await this._validateSameName(updatePangkatDto.nama, id);

      const { nama, nama_singkat, kategori_id, golongan_id } = updatePangkatDto;
      const queryResult = await this.prisma.pangkat.update({
        where: { id },
        data: {
          nama: nama || pangkat.nama,
          nama_singkat: nama_singkat || pangkat.nama_singkat,
          kategori_id: Number(kategori_id) || pangkat.kategori_id,
          golongan_id: Number(golongan_id) || pangkat.golongan_id,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(req: any, id: number) {
    try {
      const pangkat = await this.prisma.pangkat.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
        },
      });

      if (!pangkat) {
        throw new NotFoundException(`Pangkat tidak ditemukan`);
      }

      const queryResult = await this.prisma.pangkat.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async pangkatPersonel(req, uid: string) {
    try {
      const getPangkatPersonel = await this.prisma.pangkat_personel.findMany({
        select: {
          tmt: true,
          pangkat: {
            select: {
              nama_singkat: true,
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tmt: 'desc',
        },
      });

      const queryResult = getPangkatPersonel.map((item) => {
        const date = new Date(item.tmt);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const tmt = item.tmt == null ? null : `${month}-${day}-${year}`;
        return {
          tmt: tmt,
          pangkat: item.pangkat.nama_singkat,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  private async _validateSameName(name: string, id?: number) {
    try {
      const sameNameExist = await this.prisma.pangkat.count({
        where: {
          id: id ? { not: id } : undefined,
          nama: {
            startsWith: name,
            mode: 'insensitive',
          },
          deleted_at: null,
        },
      });

      if (sameNameExist) {
        throw new HttpException(
          `Pangkat dengan nama ${name} sudah tersedia!`,
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (err) {
      throw err;
    }
  }
}
