import { forwardRef, Module } from '@nestjs/common';
import { PangkatService } from './service/pangkat.service';
import { PangkatController } from './controller/pangkat.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../../access-management/users/users.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PangkatController],
  providers: [PangkatService],
})
export class PangkatModule {}
