import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PangkatKategoriService } from './service/pangkat_kategori.service';
import {
  CreatePangkatKategoriDto,
  UpdatePangkatKategoriDto,
} from './dto/pangkat_kategori.dto';
import { Module, Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { MODULES } from '../../../core/constants/module.constant';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('pangkat-kategori')
@Module(MODULES.MASTER_DATA_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PangkatKategoriController {
  private readonly logger = new Logger(PangkatKategoriController.name);

  constructor(
    private readonly pangkatKategoriService: PangkatKategoriService,
  ) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(
    @Req() req: any,
    @Body() createPangkatKategoriDto: CreatePangkatKategoriDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createPangkatKategoriDto)}`,
    );
    const response = await this.pangkatKategoriService.create(
      req,
      createPangkatKategoriDto,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createPangkatKategoriDto)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get()
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pangkatKategoriService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.pangkatKategoriService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updatePangkatKategoriDto: UpdatePangkatKategoriDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(updatePangkatKategoriDto)}`,
    );
    const response = await this.pangkatKategoriService.update(
      req,
      +id,
      updatePangkatKategoriDto,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(updatePangkatKategoriDto)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} with id: ${id}`);
    const response = await this.pangkatKategoriService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} with id: ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
