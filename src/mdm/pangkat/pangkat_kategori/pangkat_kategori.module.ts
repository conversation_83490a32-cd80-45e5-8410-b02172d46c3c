import { Module } from '@nestjs/common';
import { PangkatKategoriService } from './service/pangkat_kategori.service';
import { PangkatKategoriController } from './pangkat_kategori.controller';
import { PrismaModule } from '../../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [PangkatKategoriController],
  providers: [PangkatKategoriService],
})
export class PangkatKategoriModule {}
