import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePangkatKategoriDto,
  UpdatePangkatKategoriDto,
} from '../dto/pangkat_kategori.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../../core/constants/log.constant';
import { ConstantLogType } from '../../../../core/interfaces/log.type';

@Injectable()
export class PangkatKategoriService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, createPangkatKategoriDto: CreatePangkatKategoriDto) {
    try {
      await this._validateSameName(createPangkatKategoriDto.nama);

      const queryResult = await this.prisma.pangkat_kategori.create({
        data: {
          nama: createPangkatKategoriDto.nama,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_KATEGORI_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        created_at: { field: 'created_at', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.pangkat_kategori.count({
          where,
        }),
        this.prisma.pangkat_kategori.findMany({
          select: {
            id: true,
            nama: true,
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_KATEGORI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.pangkat_kategori.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Pangkat kategori tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_KATEGORI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updatePangkatKategoriDto: UpdatePangkatKategoriDto,
  ) {
    try {
      const pangkatKategori = await this.prisma.pangkat_kategori.findFirst({
        where: { id, deleted_at: null },
      });
      if (!pangkatKategori) {
        throw new NotFoundException(`Pangkat kategori tidak ditemukan`);
      }

      await this._validateSameName(updatePangkatKategoriDto.nama);
      const queryResult = await this.prisma.pangkat_kategori.update({
        where: { id },
        data: {
          nama: updatePangkatKategoriDto.nama,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_KATEGORI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(req: any, id: number) {
    try {
      const pangkatKategori = await this.prisma.pangkat_kategori.findFirst({
        where: { id, deleted_at: null },
      });
      if (!pangkatKategori) {
        throw new NotFoundException(`Pangkat kategori tidak ditemukan`);
      }

      const queryResult = await this.prisma.pangkat_kategori.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PANGKAT_KATEGORI_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _validateSameName(name: string) {
    try {
      const sameNameExist = await this.prisma.pangkat_kategori.count({
        where: {
          nama: { startsWith: name, mode: 'insensitive' },
          deleted_at: null,
        },
      });
      if (sameNameExist) {
        throw new HttpException(
          `Pangkat Kategori dengan nama ${name} sudah tersedia!`,
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (err) {
      throw err;
    }
  }
}
