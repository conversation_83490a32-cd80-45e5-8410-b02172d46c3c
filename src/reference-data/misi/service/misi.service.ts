import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { CreateMisiDto } from '../dto/misi.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class MisiService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateMisiDto) {
    const { nama, kota, provinsi, negara, is_aktif } = body;

    try {
      const misi = await this.prisma.misi.findFirst({
        select: {
          id: true,
          nama: true,
        },
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (misi) {
        throw new ConflictException('Nama already exists');
      }

      const queryResult = await this.prisma.misi.create({
        data: {
          nama,
          kota,
          provinsi,
          negara,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.MISI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MISI_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, body: CreateMisiDto) {
    const { nama, kota, provinsi, negara, is_aktif } = body;
    try {
      const penugasan = await this.prisma.misi.findFirst({
        where: {
          id,
          deleted_at: null,
        },
      });

      if (!penugasan) {
        throw new NotFoundException('Misi not found');
      }

      const checkIfExists = await this.prisma.misi.findFirst({
        where: {
          nama: nama,
          NOT: {
            id,
          },
          deleted_at: null,
        },
      });

      if (checkIfExists) {
        throw new ConflictException('Nama already exists');
      }

      const queryResult = await this.prisma.misi.update({
        where: { id },
        data: {
          nama,
          kota,
          provinsi,
          negara,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.MISI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MISI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const misi = await this.prisma.misi.findFirst({
        where: {
          id,
          deleted_at: null,
        },
      });

      if (!misi) {
        throw new NotFoundException('Misi not found');
      }

      const queryResult = await this.prisma.misi.update({
        where: { id },
        data: {
          deleted_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.MISI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MISI_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.misi.findFirst({
        select: {
          id: true,
          nama: true,
          kota: true,
          provinsi: true,
          negara: true,
          created_at: true,
          updated_at: true,
          is_aktif: true,
        },
        where: {
          id: BigInt(id),
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('Misi not found');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.MISI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MISI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'number' | 'date';
      };
    } = {
      id: { field: 'id', type: 'number' },
      nama: { field: 'nama', type: 'string' },
      kota: { field: 'kota', type: 'string' },
      provinsi: { field: 'provinsi', type: 'string' },
      negara: { field: 'negara', type: 'string' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    try {
      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.misi.count({
          where,
        }),
        this.prisma.misi.findMany({
          select: {
            id: true,
            nama: true,
            kota: true,
            provinsi: true,
            negara: true,
            is_aktif: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          where,
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.MISI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MISI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: totalPage,
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }
}
