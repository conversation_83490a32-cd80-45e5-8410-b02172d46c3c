import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { MisiController } from './controller/misi.controller';
import { MisiService } from './service/misi.service';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [MisiController],
  providers: [MisiService],
})
export class MisiModule {}
