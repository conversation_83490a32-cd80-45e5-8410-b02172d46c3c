import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { bracketToObject } from '../../../core/utils/common.utils';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { MisiService } from '../service/misi.service';
import { CreateMisiDto } from '../dto/misi.dto';

@Controller('misi')
@UseGuards(JwtAuthGuard)
export class MisiController {
  private readonly logger = new Logger(MisiController.name);

  constructor(private readonly misiService: MisiService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateMisiDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.misiService.create(
      req,
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: CreateMisiDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.misiService.update(
      req,
      Number(id),
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.misiService.delete(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.misiService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getOne(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getOne.name} with id: ${id}`);
    const response = await this.misiService.getOne(req, Number(id));
    this.logger.log(
      `Leaving ${this.getOne.name} with id: ${id} and data: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
