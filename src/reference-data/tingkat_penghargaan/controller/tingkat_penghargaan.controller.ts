import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { TingkatPenghargaanService } from '../service/tingkat_penghargaan.service';
import {
  CreateTingkatPenghargaanDto,
  UpdateTingkatPenghargaanDto,
} from '../dto/tingkat_penghargaan.dto';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('tingkat-penghargaan')
@UseGuards(JwtAuthGuard)
export class TingkatPenghargaanController {
  private readonly logger = new Logger(TingkatPenghargaanController.name);

  constructor(
    private readonly tingkatPenghargaanService: TingkatPenghargaanService,
  ) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(
    @Req() req: any,
    @Body() createTingkatPenghargaanDto: CreateTingkatPenghargaanDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createTingkatPenghargaanDto)}`,
    );
    const response = await this.tingkatPenghargaanService.create(
      req,
      createTingkatPenghargaanDto,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createTingkatPenghargaanDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.tingkatPenghargaanService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.tingkatPenghargaanService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateTingkatPenghargaanDto: UpdateTingkatPenghargaanDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(updateTingkatPenghargaanDto)}`,
    );
    const response = await this.tingkatPenghargaanService.update(
      req,
      +id,
      updateTingkatPenghargaanDto,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(updateTingkatPenghargaanDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} with id: ${id}`);
    const response = await this.tingkatPenghargaanService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
