import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateTingkatPenghargaanDto,
  UpdateTingkatPenghargaanDto,
} from '../dto/tingkat_penghargaan.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class TingkatPenghargaanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(
    req: any,
    createTingkatPenghargaanDto: CreateTingkatPenghargaanDto,
  ) {
    try {
      await this._validateSameName(createTingkatPenghargaanDto.nama);
      const queryResult = await this.prisma.penghargaan_tingkat.create({
        data: {
          nama: createTingkatPenghargaanDto.nama,
          is_aktif: Boolean(createTingkatPenghargaanDto.is_aktif) ?? true,
          sipk_poin: Number(createTingkatPenghargaanDto.sipk_poin),
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TINGKAT_PENGHARGAAN_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'number';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        sipk_poin: { field: 'sipk_poin', type: 'number' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.penghargaan_tingkat.count({
          where,
        }),
        this.prisma.penghargaan_tingkat.findMany({
          select: {
            id: true,
            nama: true,
            sipk_poin: true,
            is_aktif: true,
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TINGKAT_PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.penghargaan_tingkat.findFirst({
        where: { id, deleted_at: null },
        select: {
          id: true,
          nama: true,
          sipk_poin: true,
          is_aktif: true,
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Data tingkat penghargaan tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TINGKAT_PENGHARGAAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updateTingkatPenghargaanDto: UpdateTingkatPenghargaanDto,
  ) {
    try {
      const tingkatPenghargaan =
        await this.prisma.penghargaan_tingkat.findFirst({
          where: { id, deleted_at: null },
        });

      if (!tingkatPenghargaan) {
        throw new NotFoundException(`Data penghargaan tidak ditemukan`);
      }

      if (updateTingkatPenghargaanDto.nama !== tingkatPenghargaan.nama) {
        await this._validateSameName(updateTingkatPenghargaanDto.nama);
      }

      const queryResult = await this.prisma.penghargaan_tingkat.update({
        where: { id },
        data: {
          nama: updateTingkatPenghargaanDto.nama,
          updated_at: new Date(),
          sipk_poin:
            Number(updateTingkatPenghargaanDto.sipk_poin) ||
            tingkatPenghargaan.sipk_poin,
          is_aktif: Boolean(updateTingkatPenghargaanDto.is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TINGKAT_PENGHARGAAN_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(req: any, id: number) {
    try {
      const tingkatPenghargaan =
        await this.prisma.penghargaan_tingkat.findFirst({
          where: { id, deleted_at: null },
        });
      if (!tingkatPenghargaan) {
        throw new NotFoundException(`Data tingkat penghargaan tidak ditemukan`);
      }

      const queryResult = await this.prisma.penghargaan_tingkat.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.TINGKAT_PENGHARGAAN_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _validateSameName(name: string) {
    try {
      const sameNameExist = await this.prisma.penghargaan_tingkat.count({
        where: {
          nama: { startsWith: name, mode: 'insensitive' },
          deleted_at: null,
        },
      });
      if (sameNameExist) {
        throw new HttpException(
          `Tingkat Penghargaan dengan nama ${name} sudah tersedia!`,
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (err) {
      throw err;
    }
  }
}
