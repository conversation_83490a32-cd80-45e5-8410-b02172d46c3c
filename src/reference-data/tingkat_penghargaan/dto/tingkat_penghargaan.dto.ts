import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateTingkatPenghargaanDto {
  @IsNotEmpty()
  @IsString()
  nama: string;

  @IsNotEmpty()
  sipk_poin: number;

  @IsBoolean()
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateTingkatPenghargaanDto extends PartialType(
  CreateTingkatPenghargaanDto,
) {}
