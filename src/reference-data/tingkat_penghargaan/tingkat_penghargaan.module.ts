import { forwardRef, Module } from '@nestjs/common';
import { TingkatPenghargaanService } from './service/tingkat_penghargaan.service';
import { TingkatPenghargaanController } from './controller/tingkat_penghargaan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [TingkatPenghargaanController],
  providers: [TingkatPenghargaanService],
})
export class TingkatPenghargaanModule {}
