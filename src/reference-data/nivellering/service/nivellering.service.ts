import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateNivelleringDto,
  UpdateNivelleringDto,
} from '../dto/nivellering.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class NivelleringService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateNivelleringDto) {
    const { nama, eselon_id } = body;

    if (
      await this.prisma.eselon.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('nama nivellering already exists');
    }

    const queryResult = await this.prisma.nivellering.create({
      data: {
        nama: nama,
        eselon_id: eselon_id,
        created_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NIVELLERING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NIVELLERING_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const queryResult = await this.prisma.nivellering.findFirst({
      select: {
        id: true,
        nama: true,
        eselon: {
          select: {
            id: true,
            nama: true,
          },
        },
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(`nivellering id ${id} tidak di temukan`);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NIVELLERING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NIVELLERING_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nama: { field: 'nama', type: 'string' },
      eselon_nama: { field: 'eselon.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.nivellering.count({
        where: where,
      }),
      this.prisma.nivellering.findMany({
        select: {
          id: true,
          nama: true,
          eselon: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.NIVELLERING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NIVELLERING_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get nivellering',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req, id, body: UpdateNivelleringDto) {
    const { nama, eselon_id } = body;

    const eselon = await this.prisma.eselon.findFirst({
      where: { id: +eselon_id },
    });

    if (!eselon) {
      throw new NotFoundException(`eselon id ${id} not found`);
    }
    // Memeriksa apakah nivellering id ada di database
    const nivellering = await this.prisma.nivellering.findFirst({
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!nivellering) {
      throw new NotFoundException(`nivellering id ${id} not found`);
    }

    if (
      await this.prisma.nivellering.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
          id: {
            not: {
              in: [+id],
            },
          },
        },
      })
    ) {
      throw new BadRequestException('nama nivellering already exists');
    }

    const queryResult = await this.prisma.nivellering.update({
      where: { id: +id },
      data: {
        nama: nama,
        eselon_id: eselon_id,
        updated_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NIVELLERING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NIVELLERING_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Nivellering was successfully updated',
      data: queryResult,
    };
  }

  async delete(req, id) {
    const nivellering = await this.prisma.nivellering.findFirst({
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!nivellering) {
      throw new NotFoundException(`nivellering id ${id} not found`);
    }

    const queryResult = await this.prisma.nivellering.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NIVELLERING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NIVELLERING_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Nivellering was successfully deleted',
      data: queryResult,
    };
  }
}
