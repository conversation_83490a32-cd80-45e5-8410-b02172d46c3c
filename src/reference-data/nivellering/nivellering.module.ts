import { forwardRef, Module } from '@nestjs/common';
import { NivelleringService } from './service/nivellering.service';
import { NivelleringController } from './controller/nivellering.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [NivelleringController],
  providers: [NivelleringService],
})
export class NivelleringModule {}
