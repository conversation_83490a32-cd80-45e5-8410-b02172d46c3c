import { forwardRef, Module } from '@nestjs/common';
import { StatusKawinService } from './service/status_kawin.service';
import { StatusKawinController } from './controller/status_kawin.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [StatusKawinController],
  providers: [StatusKawinService],
})
export class StatusKawinModule {}
