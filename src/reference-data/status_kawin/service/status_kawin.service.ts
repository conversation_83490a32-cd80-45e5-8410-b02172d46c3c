import { HttpStatus, Injectable } from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class StatusKawinService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'nama', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.status_kawin.count({
          where: where,
        }),
        this.prisma.status_kawin.findMany({
          select: {
            id: true,
            nama: true,
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.STATUS_KAWIN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.STATUS_KAWIN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }
}
