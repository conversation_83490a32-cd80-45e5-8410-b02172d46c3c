import { forwardRef, Module } from '@nestjs/common';
import { SertifikatService } from './service/sertifikat.service';
import { SertifikatController } from './controller/sertifikat.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SertifikatController],
  providers: [SertifikatService],
})
export class SertifikatModule {}
