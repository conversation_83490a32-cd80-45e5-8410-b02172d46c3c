import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  Lo<PERSON>,
  <PERSON>m,
  Req,
  UseGuards,
} from '@nestjs/common';
import { SertifikatService } from '../service/sertifikat.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('sertifikat')
@UseGuards(JwtAuthGuard)
export class SertifikatController {
  private readonly logger = new Logger(SertifikatController.name);

  constructor(private readonly sertifikatService: SertifikatService) {}

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async sertifikatPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.sertifikatPersonel.name} with uid: ${uid}`,
    );
    const response = await this.sertifikatService.getMany(req, uid);
    this.logger.log(
      `Leaving ${this.sertifikatPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
