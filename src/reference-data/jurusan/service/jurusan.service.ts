import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateJurusanDTO } from '../dto/jurusan.dto';
import { time } from 'console';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class JurusanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req: any, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'nama', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.jurusan.count({
          where: where,
        }),
        this.prisma.jurusan.findMany({
          select: {
            id: true,
            nama: true,
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.JURUSAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.JURUSAN_READ as ConstantLogType,
          message,
          null,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const queryResult = await this.prisma.jurusan.findUnique({
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('Data jurusan tidak ditemukan');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.JURUSAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          null,
          CONSTANT_LOG.JURUSAN_READ as ConstantLogType,
          message,
          null,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(body: CreateJurusanDTO) {
    const { nama } = body;

    try {
      const checkExist = await this.prisma.jurusan.findFirst({
        where: {
          nama: nama,
        },
      });

      if (checkExist) {
        throw new ConflictException('Nama jurusan sudah ada');
      }

      const queryResult = await this.prisma.jurusan.create({
        data: {
          nama: nama,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.JURUSAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          null,
          CONSTANT_LOG.JURUSAN_CREATE as ConstantLogType,
          message,
          null,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: CreateJurusanDTO) {
    const { nama } = body;

    try {
      const checkExist = await this.prisma.jurusan.findFirst({
        where: {
          nama: nama,
          NOT: {
            id: +id,
          },
        },
      });

      if (checkExist) {
        throw new ConflictException('Nama jurusan sudah ada');
      }

      const queryResult = await this.prisma.jurusan.update({
        where: { id: +id },
        data: {
          nama: nama,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.JURUSAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.JURUSAN_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    try {
      const checkExist = await this.prisma.jurusan.findFirst({
        where: {
          id: +id,
        },
      });

      if (checkExist) {
        throw new NotFoundException('Jurusan tidak ditemukan');
      }

      const queryResult = await this.prisma.jurusan.update({
        where: { id: +id },
        data: {
          deleted_at: new time(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.JURUSAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.JURUSAN_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
