import { forwardRef, Module } from '@nestjs/common';
import { JurusanService } from './service/jurusan.service';
import { JurusanController } from './controller/jurusan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [JurusanController],
  providers: [JurusanService],
})
export class JurusanModule {}
