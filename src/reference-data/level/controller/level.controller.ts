import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { LevelService } from '../service/level.service';
import {
  CreateLevelDto,
  SearchAndSortLevelDto,
  UpdateLevelDto,
} from '../dto/level.dto';
import { PaginationDto } from 'src/core/dtos';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('level')
@UseGuards(JwtAuthGuard)
export class LevelController {
  private readonly logger = new Logger(LevelController.name);

  constructor(private readonly levelService: LevelService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateLevelDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.levelService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const level = await this.levelService.get(req, id);
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(level)}`,
    );
    return;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortLevelDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.levelService.getListLevel(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateLevelDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.levelService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.levelService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
