import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateLevelDto,
  SearchAndSortLevelDto,
  UpdateLevelDto,
} from '../dto/level.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { PaginationDto } from '../../../core/dtos';

@Injectable()
export class LevelService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateLevelDto) {
    const { nama } = body;

    const queryResult = await this.prisma.level.create({
      data: {
        nama: nama,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.LEVEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LEVEL_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const queryResult = await this.prisma.level.findFirst({
      where: { id: +id },
    });

    if (!queryResult) {
      throw new NotFoundException(`level ${id} not found`);
    }
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.LEVEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LEVEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListLevel(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortLevelDto,
  ) {
    let response = {};
    if (searchandsortData.type?.toLowerCase() === 'operator') {
      response['data'] = await this.getListLevelOperator(req);
    } else {
      const { levels, page, totalPage, totalData } = await this.getList(
        req,
        paginationData,
      );

      response = { data: levels, page, totalPage, totalData };
    }
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.LEVEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LEVEL_READ as ConstantLogType,
        message,
        response,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      ...response,
    };
  }

  async getList(req, paginationData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, levels] = await this.prisma.$transaction([
      this.prisma.level.count({
        where: {
          deleted_at: null,
        },
      }),
      this.prisma.level.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          id: 'asc',
        },
        where: {
          deleted_at: null,
          NOT: {
            nama: { contains: 'Superadmin', mode: 'insensitive' },
          },
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    return { levels, page, totalPage, totalData };
  }

  async getListLevelOperator(req) {
    const levels = await this.prisma.level.findMany({
      where: { nama: { in: ['Level 2', 'Level 3'] }, deleted_at: null },
      select: {
        id: true,
        nama: true,
      },
    });

    return levels;
  }

  async update(req, id, body: UpdateLevelDto) {
    const { nama } = body;

    const level = await this.prisma.level.findFirst({
      where: { id: +id },
    });

    if (!level) {
      throw new NotFoundException(`level ${id} not found`);
    }

    level.nama = nama;

    const queryResult = await this.prisma.level.update({
      where: { id: +id },
      data: level,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.LEVEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LEVEL_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req, id) {
    const level = await this.prisma.level.findFirst({
      where: { id: +id },
    });

    if (!level) {
      throw new NotFoundException(`level ${id} not found`);
    }

    const queryResult = await this.prisma.level.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.LEVEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.LEVEL_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
