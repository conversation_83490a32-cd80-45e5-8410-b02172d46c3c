import { forwardRef, Module } from '@nestjs/common';
import { LevelService } from './service/level.service';
import { LevelController } from './controller/level.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [LevelController],
  providers: [LevelService],
})
export class LevelModule {}
