import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  Logger,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { BagianService } from '../service/bagian.service';
import { PaginationDto } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('bagian')
@UseGuards(JwtAuthGuard)
export class BagianController {
  private readonly logger = new Logger(BagianController.name);

  constructor(private readonly bagianService: BagianService) {}

  @Get('/:id')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.bagianService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @HttpCode(200)
  async getList(@Req() req: any, @Query() paginationData: PaginationDto) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.bagianService.getList(req, paginationData);

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
