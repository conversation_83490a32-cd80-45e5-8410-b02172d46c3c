import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';

@Injectable()
export class BagianService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async get(req, id) {
    const queryResult = await this.prisma.bagian.findFirst({
      where: { id: +id },
    });

    if (!queryResult) {
      throw new NotFoundException(`bagian id ${id} not found`);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGIAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGIAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get bagian',
      data: queryResult,
    };
  }

  async getList(req: any, paginationData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;
    const where = {
      deleted_at: null,
    } satisfies Prisma.bagianWhereInput;

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.bagian.count({
        where: {
          deleted_at: null,
        },
      }),
      this.prisma.bagian.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          id: 'desc',
        },
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }
}
