import { forwardRef, Module } from '@nestjs/common';
import { BagianService } from './service/bagian.service';
import { BagianController } from './controller/bagian.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [BagianController],
  providers: [BagianService],
})
export class BagianModule {}
