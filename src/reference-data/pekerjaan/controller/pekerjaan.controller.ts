import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PekerjaanService } from '../service/pekerjaan.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('pekerjaan')
@UseGuards(JwtAuthGuard)
export class PekerjaanController {
  private readonly logger = new Logger(PekerjaanController.name);

  constructor(private readonly pekerjaanService: PekerjaanService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pekerjaanService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
