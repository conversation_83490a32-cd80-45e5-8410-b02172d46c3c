import { forwardRef, Module } from '@nestjs/common';
import { PekerjaanService } from './service/pekerjaan.service';
import { PekerjaanController } from './controller/pekerjaan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PekerjaanController],
  providers: [PekerjaanService],
})
export class PekerjaanModule {}
