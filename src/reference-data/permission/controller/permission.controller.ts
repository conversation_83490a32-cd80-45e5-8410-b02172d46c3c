import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { PermissionService } from '../service/permission.service';
import { UpdatePermissionDto } from '../dto/permission.dto';
import { PaginationDto } from 'src/core/dtos';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('permissions')
@UseGuards(JwtAuthGuard)
export class PermissionController {
  private readonly logger = new Logger(PermissionController.name);

  constructor(private readonly permissionService: PermissionService) {}

  // @Post('/')
  // @Permission('PERMISSION_CREATE')
  // @HttpCode(201)
  // async create(@Req() req: any, @Body() body: CreatePermissionDto) {
  //   this.logger.log(
  //     `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
  //   );
  //
  //   const response = await this.permissionService.create(req, body);
  //   this.logger.log(
  //     `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
  //   );
  //
  //   return response;
  // }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(@Req() req: any, @Query() paginationData: PaginationDto) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response = await this.permissionService.getList(req, paginationData);
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/module/:module_id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @HttpCode(200)
  async getByModuleId(@Req() req: any, @Param('module_id') module_id: string) {
    this.logger.log(`Entering ${this.getList.name}`);
    const response = await this.permissionService.getByModuleId(
      req,
      Number(module_id),
    );
    this.logger.log(
      `Leaving ${this.getList.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.permissionService.get(req, id);
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdatePermissionDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.permissionService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  // @UseGuards(SiswaJwtAuthGuard)
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.permissionService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
