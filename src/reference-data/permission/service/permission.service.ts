import { BadRequestException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
} from '../dto/permission.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { PaginationDto } from "../../../core/dtos";

@Injectable()
export class PermissionService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  // async create(req, body: CreatePermissionDto) {
  //   const { nama, desc } = body;
  //
  //   const queryResult = await this.prisma.permission.create({
  //     data: {
  //       nama: nama,
  //       desc: desc,
  //     },
  //   });
  //
  //   const message = convertToLogMessage(
  //     ConstantLogStatusEnum.SUCCESS,
  //     ConstantLogTypeEnum.CREATE_LOG_TYPE,
  //     ConstantLogDataTypeEnum.OBJECT,
  //     ConstantLogModuleEnum.PERMISSION_MODULE,
  //   );
  //   await this.logsActivityService.addLogsActivity(
  //     convertToILogData(
  //       req,
  //       CONSTANT_LOG.PERMISSION_CREATE as ConstantLogType,
  //       message,
  //       queryResult,
  //     ),
  //   );
  //
  //   return {
  //     statusCode: HttpStatus.OK,
  //     message,
  //     data: queryResult,
  //   };
  // }

  async get(req, id) {
    const queryResult = await this.prisma.permission.findFirst({
      where: { id: +id },
    });

    if (!queryResult) {
      throw new NotFoundException(`permission ${id} not found`);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get permission',
      data: queryResult,
    };
  }

  async getList(req, paginationData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.permission.count({
        where: {
          deleted_at: null,
        },
      }),
      this.prisma.permission.findMany({
        select: {
          id: true,
          nama: true,
          desc: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          id: 'desc',
        },
        where: {
          deleted_at: null,
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERMISSION_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERMISSION_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async update(req, id, body: UpdatePermissionDto) {
    const { nama, desc } = body;

    const permission = await this.prisma.permission.findFirst({
      where: { id: +id },
    });

    if (!permission) {
      throw new NotFoundException(`permission ${id} not found`);
    }

    permission.nama = nama;
    permission.desc = desc;

    const queryResult = await this.prisma.permission.update({
      where: { id: +id },
      data: permission,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PERMISSION_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERMISSION_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req, id) {
    const permission = await this.prisma.permission.findFirst({
      where: { id: +id },
    });

    if (!permission) {
      throw new NotFoundException(`permission ${id} not found`);
    }

    const queryResult = await this.prisma.permission.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PERMISSION_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERMISSION_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getByModuleId(req: any, moduleId: number) {
    if (isNaN(moduleId)) throw new BadRequestException('Module ID harus berupa angka');
    const queryResult = await this.prisma.permission.findMany({
      where: { module_id: moduleId },
      select: {
        id: true,
        nama: true,
        desc: true,
        label: true,
      }
    });

    const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERMISSION_MODULE,
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
