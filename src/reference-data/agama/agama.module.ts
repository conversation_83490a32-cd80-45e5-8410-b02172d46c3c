import { forwardRef, Module } from '@nestjs/common';
import { AgamaService } from './service/agama.service';
import { AgamaController } from './controller/agama.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [AgamaController],
  providers: [AgamaService],
})
export class AgamaModule {}
