import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AgamaService } from '../service/agama.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('agama')
@UseGuards(JwtAuthGuard)
export class AgamaController {
  private readonly logger = new Logger(AgamaController.name);

  constructor(private readonly agamaService: AgamaService) {}

  @Get('/')
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with search ${searchandsortData.search}`,
    );
    const response = await this.agamaService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with search ${searchandsortData} data and data ${JSON.stringify(response)} `,
    );

    return response;
  }
}
