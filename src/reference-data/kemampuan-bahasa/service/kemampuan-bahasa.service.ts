import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class KemampuanBahasaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getMany(req: any, uid: string) {
    try {
      const queryResult = await this.prisma.personel_kemampuan_bahasa.findMany({
        select: {
          bahasa: true,
          status: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KEMAMPUAN_BAHASA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KEMAMPUAN_BAHASA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
