import { forwardRef, Module } from '@nestjs/common';
import { KemampuanBahasaService } from './service/kemampuan-bahasa.service';
import { KemampuanBahasaController } from './controller/kemampuan-bahasa.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KemampuanBahasaController],
  providers: [KemampuanBahasaService],
})
export class KemampuanBahasaModule {}
