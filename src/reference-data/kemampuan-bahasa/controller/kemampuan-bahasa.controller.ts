import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { KemampuanBahasaService } from '../service/kemampuan-bahasa.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('kemampuan-bahasa')
@UseGuards(JwtAuthGuard)
export class KemampuanBahasaController {
  private readonly logger = new Logger(KemampuanBahasaController.name);

  constructor(private readonly bahasaService: KemampuanBahasaService) {}

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async kemampuanBahasaPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.kemampuanBahasaPersonel.name} with uid: ${uid}`,
    );
    const response = await this.bahasaService.getMany(req, uid);
    this.logger.log(
      `Leaving ${this.kemampuanBahasaPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
