import { forwardRef, Module } from '@nestjs/common';
import { RoleTipeService } from './service/role-tipe.service';
import { RoleTipeController } from './controller/role-tipe.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [RoleTipeController],
  providers: [RoleTipeService],
})
export class RoleTipeModule {}
