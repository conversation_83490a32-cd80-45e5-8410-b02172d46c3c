import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { RoleTipeService } from '../service/role-tipe.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto } from 'src/core/dtos';

@Controller('role-tipe')
@UseGuards(JwtAuthGuard)
export class RoleTipeController {
  private readonly logger = new Logger(RoleTipeController.name);

  constructor(private readonly roleTipeService: RoleTipeService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(@Req() req: Request, @Query() paginationData: PaginationDto) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)}`,
    );
    const response = await this.roleTipeService.getList(req, paginationData);
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
