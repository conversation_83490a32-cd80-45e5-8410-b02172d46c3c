import { forwardRef, Module } from '@nestjs/common';
import { KabupatenService } from './service/kabupaten.service';
import { KabupatenController } from './controller/kabupaten.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KabupatenController],
  providers: [KabupatenService],
})
export class KabupatenModule {}
