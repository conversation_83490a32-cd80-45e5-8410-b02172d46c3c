import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateEselonDto, UpdateEselonDto } from '../dto/eselon.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class EselonService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateEselonDto) {
    const { nama } = body;

    if (
      await this.prisma.eselon.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('nama eselon already exists');
    }

    const eselon = await this.prisma.eselon.create({
      data: {
        nama: nama,
        created_at: new Date(),
      },
    });

    const queryResult = {
      ...eselon,
      id: Number(eselon.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ESELON_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ESELON_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req, id) {
    const eselon = await this.prisma.eselon.findFirst({
      select: {
        id: true,
        nama: true,
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!eselon) {
      throw new NotFoundException(`eselon id ${id} tidak di temukan`);
    }
    const queryResult = {
      ...eselon,
      id: Number(eselon.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ESELON_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ESELON_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, eselons] = await this.prisma.$transaction([
      this.prisma.eselon.count({
        where: where,
      }),
      this.prisma.eselon.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const convertedEselon = eselons.map((eselon) => ({
      ...eselon,
      id: Number(eselon.id),
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ESELON_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ESELON_READ as ConstantLogType,
        message,
        convertedEselon,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: convertedEselon,
    };
  }

  async update(req, id, body: UpdateEselonDto) {
    const { nama } = body;

    const eselon = await this.prisma.eselon.findFirst({
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!eselon) {
      throw new NotFoundException(`eselon id ${id} not found`);
    }

    if (
      await this.prisma.eselon.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
          id: {
            not: {
              in: [+id],
            },
          },
        },
      })
    ) {
      throw new BadRequestException('nama eselon already exists');
    }

    const updatedEselon = await this.prisma.eselon.update({
      where: { id: +id },
      data: {
        nama: nama,
        updated_at: new Date(),
      },
    });

    const queryResult = {
      ...updatedEselon,
      id: Number(updatedEselon.id),
    };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ESELON_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ESELON_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: updatedEselon,
    };
  }

  async delete(req, id) {
    const eselon = await this.prisma.eselon.findFirst({
      where: { id: +id },
    });

    if (!eselon) {
      throw new NotFoundException(`eselon id ${id} not found`);
    }

    const updatedEselon = await this.prisma.eselon.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const queryResult = {
      ...updatedEselon,
      id: Number(updatedEselon.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ESELON_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ESELON_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Eselon was successfully deleted',
      data: queryResult,
    };
  }
}
