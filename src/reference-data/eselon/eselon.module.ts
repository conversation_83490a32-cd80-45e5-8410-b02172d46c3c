import { forwardRef, Module } from '@nestjs/common';
import { EselonService } from './service/eselon.service';
import { EselonController } from './controller/eselon.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [EselonController],
  providers: [EselonService],
})
export class EselonModule {}
