import { Module } from '@nestjs/common';
import { GolonganService } from './service/golongan.service';
import { GolonganController } from './controller/golongan.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [GolonganController],
  providers: [GolonganService],
})
export class GolonganModule {}
