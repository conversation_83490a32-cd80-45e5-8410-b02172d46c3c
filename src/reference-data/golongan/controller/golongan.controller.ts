import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { GolonganService } from '../service/golongan.service';
import { CreateGolonganDto, UpdateGolonganDto } from '../dto/golongan.dto';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('golongan')
@UseGuards(JwtAuthGuard)
export class GolonganController {
  private readonly logger = new Logger(GolonganController.name);

  constructor(private readonly golonganService: GolonganService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  async create(@Req() req: any, @Body() createGolonganDto: CreateGolonganDto) {
    this.logger.log(
      `Entering ${this.create.name} golongan with body: ${JSON.stringify(createGolonganDto)}`,
    );

    const response = await this.golonganService.create(req, createGolonganDto);

    this.logger.log(
      `Leaving ${this.create.name} golongan with body: ${JSON.stringify(createGolonganDto)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} golongan with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.golonganService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} golongan with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} golongan with id: ${id}`);
    const response = await this.golonganService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} golongan with id: ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateGolonganDto: UpdateGolonganDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} golongan with id: ${id} and body: ${JSON.stringify(updateGolonganDto)}`,
    );
    const response = await this.golonganService.update(
      req,
      +id,
      updateGolonganDto,
    );
    this.logger.log(
      `Leaving ${this.update.name} golongan with id: ${id} and body: ${JSON.stringify(updateGolonganDto)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  async remove(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.remove.name} golongan with id: ${id}`);
    const response = await this.golonganService.remove(req, +id);
    this.logger.log(
      `Leaving ${this.remove.name} golongan with id: ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
