import { IsNotEmpty, IsString, <PERSON><PERSON>ength } from 'class-validator';
import { Match } from '../../../core/decorators';

export class LoginDto {
  @IsString()
  @IsNotEmpty()
  nrp: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @IsString()
  deviceToken?: string;
}

export class ResendOtpDto {
  @IsString()
  @IsNotEmpty()
  nrp: string;
}

export class LogoutDto {
  @IsString()
  deviceToken?: string;
}

export class VerifyOtpDto {
  @IsString()
  @IsNotEmpty()
  nrp: string;

  @IsString()
  @IsNotEmpty()
  otp: string;

}

export class ResetPasswordDto {
  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsNotEmpty()
  @Match('password')
  confirmPassword: string;
}

export class ForgotPasswordDto {
  @IsString()
  @IsNotEmpty()
  nrp: string;
}

export class DeviceTokenDto extends LogoutDto {}
