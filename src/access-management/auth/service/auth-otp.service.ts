import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ISendMailOptions } from '@nestjs-modules/mailer';
import { OtpException } from '../../../core/exceptions/internal-otp.exceptions';
import { NullValueException } from '../../../core/exceptions/null-value.exceptions';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { SmtpService } from '../../../api-utils/smtp/service/smtp.service';
import * as NodeCache from 'node-cache';
import * as moment from 'moment-timezone';
import { InvalidOtpException } from '../../../core/exceptions/invalid-otp.exceptions';
import { VerifyOtpDto } from '../dto/auth.dto';
import { UsersService } from '../../users/service/users.service';
import { AuthJwtService } from './auth-jwt.service';
import { ConfigService } from '@nestjs/config';
import { AuthRepository } from '../repository/auth.repository';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class AuthOtpService {
  private readonly cache: NodeCache;
  private readonly logger = new Logger(AuthOtpService.name);

  constructor(
    private readonly smtpService: SmtpService,
    private readonly logsActivityService: LogsActivityService,
    private readonly usersService: UsersService,
    private readonly authJwtService: AuthJwtService,
    private readonly configService: ConfigService,
    private readonly authRepository: AuthRepository,
  ) {
    this.cache = new NodeCache({
      stdTTL: this.configService.get<number>('OTP_EXPIRED_TIME_SEC'),
    });
  }

  resetFailedAttemptAndCooldown(
    nrp: string,
    countLeft: number,
    failedKey: string,
    prefixCooldownFailedKey: string,
  ): void {
    if (countLeft === 0) {
      this.deleteCachedFailedLogin(nrp, prefixCooldownFailedKey);
      this.deleteCachedFailedLogin(nrp, failedKey);
    }
  }

  async verifyOtp(req: any, body: VerifyOtpDto) {
    const failedCase = this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY');
    const { nrp, otp } = body;

    this.validateCachedFailedLogin(nrp, failedCase);

    const { user, user_role } = await this.usersService.validateAuthPersonelDetail(nrp);

    this.validateOtpCode(nrp, otp, failedCase);

    const restructuredUser = await this.usersService.getAuthRestructuredUsers(
      user,
      user_role,
      false,
    );

    const accessToken = this.authJwtService.signJwt(nrp);
    const decodedJwt = this.authJwtService.decodeJwt(accessToken);

    await this.authRepository.saveJwtToken(accessToken, user);

    const message = 'Berhasil verifikasi OTP';
    const queryResult = {
      user: restructuredUser,
      accessToken,
      expiry_date: this.authJwtService.getExpiryDateFromDecodedJwt(decodedJwt),
      expiry_timestamp:
        this.authJwtService.getExpiryTimestampFromDecodedJwt(decodedJwt),
    };

    const logMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.VERIFY_OTP_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );
    req.user = {};
    req.user.id = restructuredUser.id;
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_VERIFY_OTP as ConstantLogType,
        logMessage,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  private validateOtpCode(
    nrp: string,
    code: string,
    failedCase: string,
  ): { key: string; code: string } {
    const cachedOtp = this.getCachedOtp(nrp);
    const prefixCooldownFailedKey = this.getPrefixCooldownFailedKey(failedCase);
    if (!cachedOtp.code || cachedOtp.code !== code) {
      const failedAttempts = this.incrementCachedFailedLogin(nrp, failedCase);
      this.setCachedCooldown(
        nrp,
        failedAttempts,
        failedCase,
        prefixCooldownFailedKey,
      );

      const message = `Invalid or expired OTP with nrp ${nrp} and code ${code}`;
      this.logger.error(message);
      throw new BadRequestException(message);
    } else {
      this.deleteCachedFailedLogin(nrp, prefixCooldownFailedKey);
      this.deleteCachedFailedLogin(nrp, failedCase);
      this.deleteCachedFailedLogin(
        nrp,
        this.configService.get('PREFIX_OTP_RETRY_KEY'),
      );
      this.deleteCachedFailedLogin(
        nrp,
        this.configService.get('PREFIX_OTP_KEY'),
      );
    }
    return cachedOtp;
  }

  async resendOtp(req: any, nrp: string) {
    const failedCase = this.configService.get('PREFIX_OTP_RETRY_KEY');
    const prefixCooldownFailedKey = this.getPrefixCooldownFailedKey(failedCase);

    this.validateCachedFailedLogin(nrp, failedCase);

    const user = await this.usersService.validateAuthPersonelByNrp(nrp);

    const otp = this.resetCachedOtp(nrp);

    await this.sendOtpToUser(user, otp.code);

    const otpRetry = this.incrementCachedFailedLogin(nrp, failedCase);
    this.setCachedCooldown(nrp, otpRetry, failedCase, prefixCooldownFailedKey);

    this.logger.log({
      message: 'Resent OTP saved successfully',
      otpKey: otp.key,
      otpMasked: `${otp.code.substring(0, 4)}****`,
      timestamp: new Date().toISOString(),
    });

    const otpRetryInfo = this.getOtpRetryInfo(nrp);
    const cooldownTimeInfo = this.getCooldownTimeInfo(
      nrp,
      prefixCooldownFailedKey,
    );

    const queryResult = {
      message: `OTP has been resent to your email ${user.email}`,
      isCooldown: cooldownTimeInfo.isCooldown,
      cooldownTimeLeftSec: cooldownTimeInfo.cooldownTimeLeftSec ?? 0,
      cooldownDatetime: cooldownTimeInfo.cooldownDatetime,
      isMaxOtpRetry: otpRetryInfo.isMaxOtpRetry,
      otpRetryLeft: otpRetryInfo.otpRetryLeft,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.RESEND_OTP_PASSWORD_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );

    req.user = {};
    req.user.id = user.users.id;
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_RESEND_OTP as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully login',
      data: queryResult,
    };
  }

  async sendOtpToUser(user: any, code: string) {
    if (user.email) {
      const content: ISendMailOptions = {
        to: user.email,
        subject: '[SATU SDM] Kode Verifikasi',
        template: './otp-template',
        context: {
          fullName: user.nama_lengkap,
          code,
          otpExpiredTimeSec:
            this.configService.get<number>('OTP_EXPIRED_TIME_SEC') / 60,
        },
      };

      try {
        await this.smtpService.sendMail(content);
      } catch (exception) {
        const errorMessage =
          exception instanceof Error ? exception.message : 'Unknown error';
        const stackTrace = exception.stack || 'No stack trace available';

        this.logger.error(
          `Failed to send OTP email. Error: ${errorMessage}. Stack Trace: ${stackTrace}. Payload: ${JSON.stringify(content)}`,
        );
        throw new OtpException(
          `Failed to send OTP to email ${JSON.stringify(content)}. Error: ${errorMessage}`,
        );
      }
    } else {
      const message = `Failed to send OTP email with personel nrp ${user.nrp} due user doesn't has email. Please register your email to SSDM application.`;
      this.logger.error(message);
      throw new NullValueException(message);
    }
  }

  incrementCachedFailedLogin(nrp: string, failedCase: string) {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        return this.incrementOtpRetry(nrp);
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        return this.incrementFailedAttempts(nrp);
      default:
        throw new OtpException(
          `Failed to increment cached failed login with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  incrementOtpRetry(nrp: string) {
    let otpRetry = this.getCachedOtpRetry(nrp) || 0;
    otpRetry += 1;

    this.setCachedOtpRetry(nrp, otpRetry);

    return this.getCachedOtpRetry(nrp);
  }

  incrementFailedAttempts(nrp: string) {
    let failedAttempts = this.getCachedFailedAttempts(nrp) || 0;
    failedAttempts += 1;

    this.setCachedFailedAttempts(nrp, failedAttempts);

    return this.getCachedFailedAttempts(nrp);
  }

  getCooldownTime(failedCase: string) {
    switch (this.getPrefixCooldownFailedKey(failedCase)) {
      case this.configService.get('PREFIX_OTP_RETRY_COOLDOWN_KEY'):
        return (
          this.configService.get<number>('LOGIN_OTP_RETRY_COOLDOWN_TIME_SEC') *
          1000
        );
      case this.configService.get('PREFIX_FAILED_ATTEMPT_COOLDOWN_KEY'):
        return (
          this.configService.get<number>(
            'LOGIN_FAILED_ATTEMPT_COOLDOWN_TIME_SEC',
          ) * 1000
        );
      default:
        throw new OtpException(
          `Failed to get max failed login with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  setCachedCooldown(
    nrp: string,
    retry: number,
    failedCase: string,
    prefixCooldownFailedKey: string,
  ) {
    if (retry >= this.getMaxFailedLogin(failedCase)) {
      const cooldownTimestamp = moment()
        .add(this.getCooldownTime(failedCase), 'milliseconds')
        .valueOf();
      this.setCachedCooldownTime(
        nrp,
        cooldownTimestamp,
        prefixCooldownFailedKey,
      );
    }
  }

  getMaxFailedLogin(failedCase: string): number {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        return this.configService.get<number>('LOGIN_MAX_OTP_RETRY');
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        return this.configService.get<number>('LOGIN_MAX_FAILED_ATTEMPTS');
      default:
        throw new OtpException(
          `Failed to get max failed login with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  validateCachedFailedLogin(nrp: string, failedCase: string) {
    if (this.isGreaterThanMaxFailedLogin(nrp, failedCase)) {
      this.validateOtpCooldownTime(nrp, failedCase);
    }
  }

  debugCache() {
    const keys = this.cache.keys(); // Retrieve all keys in the cache
    const allData = {};

    keys.forEach((key) => {
      allData[key] = this.cache.get(key); // Retrieve value by key
    });

    console.log('Cache Contents:', allData);
  }

  isGreaterThanMaxFailedLogin(nrp: string, failedCase: string) {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        return (
          !!this.getCachedOtpRetry(nrp) &&
          this.getCachedOtpRetry(nrp) >= this.getMaxFailedLogin(failedCase)
        );
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        return (
          !!this.getCachedFailedAttempts(nrp) &&
          this.getCachedFailedAttempts(nrp) >=
            this.getMaxFailedLogin(failedCase)
        );
      default:
        throw new OtpException(
          `Failed to check is greater than max failed login with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  getPrefixCooldownFailedKey(failedCase: string) {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        return this.configService.get('PREFIX_OTP_RETRY_COOLDOWN_KEY');
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        return this.configService.get('PREFIX_FAILED_ATTEMPT_COOLDOWN_KEY');
      default:
        throw new OtpException(
          `Failed to get prefix cooldown failed key ${failedCase} due prefix failed case variable`,
        );
    }
  }

  validateOtpCooldownTime(nrp: string, failedCase: string): void {
    const prefixCooldownFailedKey: string =
      this.getPrefixCooldownFailedKey(failedCase);

    if (this.isCooldownTime(nrp, prefixCooldownFailedKey)) {
      this.throwErrorOtpCooldownTime(
        this.getCachedCooldownTime(nrp, prefixCooldownFailedKey),
        failedCase,
      );
    } else {
      this.deleteCachedFailedLogin(nrp, prefixCooldownFailedKey);
      this.deleteCachedFailedLogin(nrp, failedCase);
    }
  }

  throwErrorOtpCooldownTime(
    existingCooldownTimestampMillis: number,
    failedCase: string,
  ): void {
    const timeLeftSec = this.subtractTimestampToNowInSec(
      existingCooldownTimestampMillis,
    );

    let message: string;
    if (failedCase === this.configService.get('PREFIX_OTP_RETRY_KEY')) {
      message = `Too many resend otp. Please try again in ${timeLeftSec} seconds.`;
    } else {
      message = `Too many failed attempts. Please try again in ${timeLeftSec} seconds.`;
    }

    this.logger.error(message);
    throw new InvalidOtpException(message);
  }

  isCooldownTime(nrp: string, prefixCooldownFailedKey: string): boolean {
    const cooldownTimeInMillis = this.getCachedCooldownTime(
      nrp,
      prefixCooldownFailedKey,
    );
    return !!cooldownTimeInMillis && cooldownTimeInMillis > moment().valueOf();
  }

  subtractTimestampToNowInSec(cooldownTimeInMillis: number) {
    return Math.round((cooldownTimeInMillis - moment().valueOf()) / 1000);
  }

  resetCachedOtp(nrp: string) {
    this.deleteCachedFailedLogin(nrp, this.configService.get('PREFIX_OTP_KEY'));

    return this.setCachedOtp(nrp);
  }

  deleteCachedFailedLogin(nrp: string, failedCase: string): void {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_KEY'):
        if (this.getCachedOtp(nrp)) {
          this.cache.del(this.getOtpKey(nrp));
        }
        break;
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        if (this.getCachedOtpRetry(nrp)) {
          this.cache.del(this.getOtpRetryKey(nrp));
        }
        break;
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        if (this.getCachedFailedAttempts(nrp)) {
          this.cache.del(this.getFailedAttemptKey(nrp));
        }
        break;
      case this.configService.get('PREFIX_OTP_RETRY_COOLDOWN_KEY'):
        if (
          this.getCachedCooldownTime(
            nrp,
            this.configService.get('PREFIX_OTP_RETRY_COOLDOWN_KEY'),
          )
        ) {
          this.cache.del(
            this.getCooldownKey(
              nrp,
              this.configService.get('PREFIX_OTP_RETRY_COOLDOWN_KEY'),
            ),
          );
        }
        break;
      case this.configService.get('PREFIX_FAILED_ATTEMPT_COOLDOWN_KEY'):
        if (
          this.getCachedCooldownTime(
            nrp,
            this.configService.get('PREFIX_FAILED_ATTEMPT_COOLDOWN_KEY'),
          )
        ) {
          this.cache.del(
            this.getCooldownKey(
              nrp,
              this.configService.get('PREFIX_FAILED_ATTEMPT_COOLDOWN_KEY'),
            ),
          );
        }
        break;
      default:
        throw new OtpException(
          `Failed to delete cached OTP with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  getOtpRetryInfo(nrp: string): {
    isMaxOtpRetry: boolean;
    otpRetryLeft: number;
  } {
    const failedCase = this.configService.get('PREFIX_OTP_RETRY_KEY');

    return {
      isMaxOtpRetry: this.isGreaterThanMaxFailedLogin(nrp, failedCase),
      otpRetryLeft: this.getFailedLoginLeft(nrp, failedCase),
    };
  }

  getFailedAttemptsInfo(nrp: string): {
    isMaxFailedAttempts: boolean;
    failedAttemptsLeft: number;
  } {
    const failedCase = this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY');

    return {
      isMaxFailedAttempts: this.isGreaterThanMaxFailedLogin(nrp, failedCase),
      failedAttemptsLeft: this.getFailedLoginLeft(nrp, failedCase),
    };
  }

  getFailedLoginLeft(nrp: string, failedCase: string) {
    switch (failedCase) {
      case this.configService.get('PREFIX_OTP_RETRY_KEY'):
        return this.calculateFailedLoginLeft(
          this.getCachedOtpRetry(nrp),
          this.configService.get<number>('LOGIN_MAX_OTP_RETRY'),
        );
      case this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY'):
        return this.calculateFailedLoginLeft(
          this.getCachedFailedAttempts(nrp),
          this.configService.get<number>('LOGIN_MAX_FAILED_ATTEMPTS'),
        );
      default:
        throw new OtpException(
          `Failed to calculate failed login left with prefix failed case ${failedCase} due prefix failed case variable`,
        );
    }
  }

  calculateFailedLoginLeft(
    existingFailedLogin: number,
    maxFailedLogin: number,
  ) {
    if (existingFailedLogin) {
      const calculateOtpRetryLeft: number =
        maxFailedLogin - existingFailedLogin;
      return calculateOtpRetryLeft < 0 ? 0 : calculateOtpRetryLeft;
    } else {
      return maxFailedLogin;
    }
  }

  getCooldownTimeInfo(
    nrp: string,
    prefixCooldownFailedKey: string,
  ): {
    isCooldown: boolean;
    cooldownTimeLeftSec: number;
    cooldownDatetime: string;
  } {
    return {
      isCooldown: this.isCooldownTime(nrp, prefixCooldownFailedKey),
      cooldownTimeLeftSec: this.calculateCooldownLeft(
        nrp,
        prefixCooldownFailedKey,
      ),
      cooldownDatetime: this.getCooldownDatetime(nrp, prefixCooldownFailedKey),
    };
  }

  getCooldownDatetime(nrp: string, prefixCooldownFailedKey: string) {
    const existingCooldownTimestampMillis = this.getCachedCooldownTime(
      nrp,
      prefixCooldownFailedKey,
    );

    if (existingCooldownTimestampMillis) {
      return moment(existingCooldownTimestampMillis)
        .tz('Asia/Jakarta')
        .format('YYYY-MM-DD HH:mm:ss');
    } else {
      return null;
    }
  }

  calculateCooldownLeft(nrp: string, prefixCooldownFailedKey: string) {
    const existingCooldownTimestampMillis = this.getCachedCooldownTime(
      nrp,
      prefixCooldownFailedKey,
    );

    if (existingCooldownTimestampMillis) {
      return this.subtractTimestampToNowInSec(existingCooldownTimestampMillis) <
        0
        ? 0
        : this.subtractTimestampToNowInSec(existingCooldownTimestampMillis);
    } else {
      return 0;
    }
  }

  getOtpKey(nrp: string) {
    return `${this.configService.get('PREFIX_OTP_KEY')}${nrp}`;
  }

  setCachedOtp(nrp: string): { key: string; code: string } {
    const data = {
      key: this.getOtpKey(nrp),
      code: this.getOtpCode(),
    };
    this.cache.set(data.key, data.code);

    return data;
  }

  getCachedOtp(nrp: string): { key: string; code: string } {
    const key = this.getOtpKey(nrp);
    return {
      key,
      code: this.cache.get<string>(key),
    };
  }

  getOtpCode(): string {
    switch (this.configService.get('APP_ENV')) {
      case 'local':
      case 'development':
      case 'staging':
        return '000000';
      case 'production':
        return Math.floor(100000 + Math.random() * 900000).toString();
      default:
        throw new OtpException(
          `Failed to retrieve OTP with environment ${this.configService.get('APP_ENV')} due unknown environment variable`,
        );
    }
  }

  getCooldownKey(nrp: string, prefixCooldownFailedKey: string): string {
    return `${prefixCooldownFailedKey}${nrp}`;
  }

  setCachedCooldownTime(
    nrp: string,
    cooldownTimeInMillis: number,
    prefixCooldownFailedKey: string,
  ): void {
    this.cache.set(
      this.getCooldownKey(nrp, prefixCooldownFailedKey),
      cooldownTimeInMillis,
    );
  }

  getCachedCooldownTime(
    nrp: string,
    prefixCooldownFailedKey: string,
  ): number | undefined {
    return this.cache.get<number>(
      this.getCooldownKey(nrp, prefixCooldownFailedKey),
    );
  }

  getFailedAttemptKey(nrp: string): string {
    return `${this.configService.get('PREFIX_FAILED_ATTEMPTS_KEY')}${nrp}`;
  }

  getCachedFailedAttempts(nrp: string): number | undefined {
    return this.cache.get<number>(this.getFailedAttemptKey(nrp));
  }

  setCachedFailedAttempts(nrp: string, failedAttempts: number): void {
    this.cache.set(this.getFailedAttemptKey(nrp), failedAttempts);
  }

  getOtpRetryKey(nrp: string) {
    return `${this.configService.get('PREFIX_OTP_RETRY_KEY')}${nrp}`;
  }

  getCachedOtpRetry(nrp: string): number | undefined {
    return this.cache.get<number>(this.getOtpRetryKey(nrp));
  }

  setCachedOtpRetry(nrp: string, otpRetryCount: number): void {
    this.cache.set(this.getOtpRetryKey(nrp), otpRetryCount);
  }
}
