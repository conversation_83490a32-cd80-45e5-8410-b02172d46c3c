import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthRepository } from '../repository/auth.repository';

@Injectable()
export class AuthJwtService {
  constructor(
    private jwtService: JwtService,
    private readonly authRepository: AuthRepository,
  ) {}

  async findToken(token: string) {
    return await this.authRepository.findUsersJwtTokenByToken(token);
  }

  signJwt(nrp: string) {
    const payload = { nrp };
    return this.jwtService.sign(payload, { expiresIn: '2d' });
  }

  decodeJwt(accessToken: string) {
    return JSON.parse(atob(accessToken.split('.')[1]));
  }

  verifyJwt(accessToken: string) {
    return this.jwtService.verify(accessToken);
  }

  getExpiryTimestampFromDecodedJwt(decodedJwt: any) {
    return decodedJwt?.exp * 1000;
  }

  getExpiryDateFromDecodedJwt(decodedJwt: any) {
    const date = new Date(decodedJwt?.exp * 1000);
    return date.toLocaleString('en-US', {
      timeZone: 'Asia/Jakarta',
      dateStyle: 'short',
      timeStyle: 'medium',
    });
  }
}
