import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import {
  DeviceTokenDto,
  LoginDto,
  LogoutDto,
  ResetPasswordDto,
} from '../dto/auth.dto';
import { SmtpService } from 'src/api-utils/smtp/service/smtp.service';
import { ConfigService } from '@nestjs/config';
import { ISendMailOptions } from '@nestjs-modules/mailer';
import { AuthOtpService } from './auth-otp.service';
import { UsersService } from '../../users/service/users.service';
import { maskEmail } from '../../../core/utils/common.utils';
import { validateNewPassword } from '../../../core/utils/validation.utils';
import * as NodeCache from 'node-cache';
import * as moment from 'moment';
import * as cryptoJs from 'crypto-js';
import { TooManyRequestsException } from '../../../core/exceptions/too-many-request.exception';
import { IValidateAuthByPersonelNRP } from '../../../core/interfaces/users.interface';
import { LoginException } from '../../../core/exceptions/internal-login.exceptions';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { AuthRepository } from '../repository/auth.repository';

@Injectable()
export class AuthService {
  private readonly cache: NodeCache = new NodeCache();
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly smtpService: SmtpService,
    private readonly config: ConfigService,
    private readonly otpService: AuthOtpService,
    private readonly authRepository: AuthRepository,
    private readonly usersService: UsersService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async login(req: any, body: LoginDto) {
    const { nrp, password } = body;

    const user: IValidateAuthByPersonelNRP =
      await this.usersService.validateAuthPersonelByNrp(nrp);
    await this.validatePassword(nrp, password, user);

    const failedKey = this.config.get('PREFIX_FAILED_ATTEMPTS_KEY');
    const prefixCooldownFailedKey =
      this.otpService.getPrefixCooldownFailedKey(failedKey);

    let cooldownTimeInfo = this.otpService.getCooldownTimeInfo(
      nrp,
      prefixCooldownFailedKey,
    );
    let failedAttemptsInfo = this.otpService.getFailedAttemptsInfo(nrp);

    if (cooldownTimeInfo.isCooldown) {
      return {
        message: `OTP tidak dikirim ke email ${user.email} karena dalam cooldown time`,
        isCooldown: cooldownTimeInfo.isCooldown,
        cooldownTimeLeftSec: cooldownTimeInfo.cooldownTimeLeftSec ?? 0,
        cooldownDatetime: cooldownTimeInfo.cooldownDatetime,
        isMaxFailedAttempts: failedAttemptsInfo.isMaxFailedAttempts,
        failedAttemptsLeft: failedAttemptsInfo.failedAttemptsLeft ?? 0,
        email: user.email,
      };
    } else {
      this.otpService.resetFailedAttemptAndCooldown(
        nrp,
        failedAttemptsInfo.failedAttemptsLeft,
        failedKey,
        prefixCooldownFailedKey,
      );
      cooldownTimeInfo = this.otpService.getCooldownTimeInfo(
        nrp,
        prefixCooldownFailedKey,
      );
      failedAttemptsInfo = this.otpService.getFailedAttemptsInfo(nrp);
    }

    const otp = this.otpService.setCachedOtp(nrp);
    // await this.otpService.sendOtpToUser(user, otp.code);

    this.logger.log({
      message: 'OTP sudah sukses disimpan cache',
      otpKey: otp.key,
      otpMasked: `${otp.code.substring(0, 4)}****`,
      timestamp: new Date().toISOString(),
    });

    const queryResult = {
      message: `OTP telah dikirim ke email ${maskEmail(user.email)}`,
      isCooldown: cooldownTimeInfo.isCooldown,
      cooldownTimeLeftSec: cooldownTimeInfo.cooldownTimeLeftSec ?? 0,
      cooldownDatetime: cooldownTimeInfo.cooldownDatetime,
      isMaxFailedAttempts: failedAttemptsInfo.isMaxFailedAttempts,
      failedAttemptsLeft: failedAttemptsInfo.failedAttemptsLeft,
      email: maskEmail(user.email),
      nrp: user.nrp,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.LOGIN_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );
    req.user = {};
    req.user.id = user.users.id;
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_LOGIN as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async validatePassword(
    nrp: string,
    password: string,
    user: IValidateAuthByPersonelNRP,
  ) {
    const attemptKey = `${this.config.get<string>('PREFIX_LOGIN_ATTEMPT_KEY')}${nrp}`;
    const lockKey = `${this.config.get<string>('PREFIX_LOCK_LOGIN_KEY')}${nrp}`;

    if (this.cache.get<boolean>(lockKey))
      throw new TooManyRequestsException(
        `Akun terkunci sementara. Coba lagi nanti.`,
      );

    const attemptCount = this.cache.get<number>(attemptKey) || 0;
    const newAttempt = attemptCount + 1;

    const checkPassword = await this.switchPassword(password, user);

    if (!checkPassword) {
      this.cache.set(attemptKey, newAttempt);

      if (newAttempt % 3 === 0) {
        const waitTime = this.getMaxWaitTime(newAttempt);
        this.cache.set(lockKey, true, waitTime);
        throw new TooManyRequestsException(
          `Terlalu banyak percobaan gagal. Coba lagi dalam ${waitTime / 60} menit.`,
        );
      }

      const message = `Kombinasi username dan password tidak sesuai`;
      this.logger.error(message);
      throw new UnauthorizedException(message);
    }

    this.cache.del(attemptKey);
  }

  async validatePasswordClient(
    nrp: string,
    password: string,
    user: IValidateAuthByPersonelNRP,
  ) {
    const attemptKey = `${this.config.get<string>('PREFIX_LOGIN_ATTEMPT_KEY')}${nrp}`;
    const lockKey = `${this.config.get<string>('PREFIX_LOCK_LOGIN_KEY')}${nrp}`;

    if (this.cache.get<boolean>(lockKey))
      throw new TooManyRequestsException(
        `Akun terkunci sementara. Coba lagi nanti.`,
      );

    const attemptCount = this.cache.get<number>(attemptKey) || 0;
    const newAttempt = attemptCount + 1;

    const checkPassword = await this.switchPassword(password, user);

    if (!checkPassword) {
      this.cache.set(attemptKey, newAttempt);

      if (newAttempt % 3 === 0) {
        const waitTime = this.getMaxWaitTime(newAttempt);
        this.cache.set(lockKey, true, waitTime);
        throw new TooManyRequestsException(
          `Terlalu banyak percobaan gagal. Coba lagi dalam ${waitTime / 60} menit.`,
        );
      }

      const message = `Kombinasi username dan password tidak sesuai`;
      this.logger.error(message);
      throw new BadRequestException(message);
    }

    this.cache.del(attemptKey);
  }

  private async switchPassword(
    password: string,
    user: IValidateAuthByPersonelNRP,
  ) {
    const env = this.config.get('APP_ENV');
    switch (env) {
      case 'local':
      case 'development':
      case 'staging':
        return await bcrypt.compare(
          password,
          '$2a$10$s4pki1zBVfFikcQ5dPjmbOszQ7lMuN57TASHan6v9/rIEJqdR/dxC',
        );
      case 'production':
        return await bcrypt.compare(password, user.users.password);
      default:
        throw new LoginException(`Tidak ditemukan login environment ${env}`);
    }
  }

  private getMaxWaitTime(attempts: number): number {
    const level = Math.floor(attempts / 3);
    return 60 * Math.pow(10, level - 1);
  }

  async logout(req: any, body: LogoutDto) {
    try {
      if (body.deviceToken) {
        const user_id = req['user']['id'];
        const token = body.deviceToken;
        await this.prisma.$transaction(async (tx) => {
          await tx.users_device_token.deleteMany({
            where: {
              user_id,
              token,
            },
          });
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.LOGOUT_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.AUTHENTICATION_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.AUTHENTICATION_LOGOUT as ConstantLogType,
          message,
          body,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully logout',
      };
    } catch (error) {
      this.logger.error('Error in logout = ', error);
      throw error;
    }
  }

  async forgotPassword(req: any, nrp: string) {
    const currentDate = moment.utc().millisecond(0);

    const validityDuration: {
      amount: moment.DurationInputArg1;
      unit: moment.DurationInputArg2;
    } = {
      amount: 1,
      unit: 'days',
    };

    const unitTranslation = {
      minutes: 'Menit',
      hours: 'Jam',
      days: 'x 24 Jam',
      months: 'Bulan',
      years: 'Tahun',
    };

    const personel = await this.prisma.personel.findFirst({
      where: { nrp },
      select: {
        id: true,
        nama_lengkap: true,
        nrp: true,
        email: true,
        uid: true,
        reset_password: true,
        users: {
          select: {
            id: true,
            password: true,
            users_jwt_token: {
              select: {
                token: true,
              },
            },
          },
        },
      },
    });

    if (!personel) throw new NotFoundException('Email tidak terdaftar.');

    let resetToken = cryptoJs
      .MD5(`${personel.id}${currentDate.format('YYYY-MM-DD HH:mm:ss')}`)
      .toString();
    let expiredAt = currentDate
      .clone()
      .add(validityDuration.amount, validityDuration.unit)
      .millisecond(0)
      .toISOString();

    if (personel.reset_password.length) {
      const resetPassword = personel.reset_password[0];

      const expToken = moment.utc(resetPassword.expired_at).millisecond(0);
      const isTokenActive = resetPassword.is_aktif;

      if (currentDate.isSameOrBefore(expToken) && isTokenActive) {
        resetToken = resetPassword.kode_unique;
        expiredAt = expToken.toISOString();
      }
    }

    await this.prisma.reset_password.upsert({
      where: { personel_id: personel.id },
      create: {
        kode_unique: resetToken,
        personel_id: personel.id,
        expired_at: expiredAt,
      },
      update: {
        kode_unique: resetToken,
        expired_at: expiredAt,
        is_aktif: true,
      },
    });

    const contextMail = {
      name: personel.nama_lengkap,
      resetLink: `${this.config.get('URL_PAGE_RESET_PASSWORD')}${resetToken}`,
      linkValidity: `${Number(validityDuration.amount)} ${unitTranslation[validityDuration.unit]}`,
    };

    const emailTo = this.setEmail(personel.email, personel.nrp);
    const payloadMail: ISendMailOptions = {
      to: emailTo,
      subject: 'Reset Password Satu SDM',
      template: './forgot-password-template',
      context: contextMail,
    };

    await this.smtpService.sendMail(payloadMail);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.RESET_PASSWORD_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_FORGOT_PASSWORD as ConstantLogType,
        message,
        payloadMail,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: `Tautan telah dikirim ke email Anda ${maskEmail(emailTo)}`,
    };
  }

  async resetPassword(req: any, tokenId: string, body: ResetPasswordDto) {
    const currentDate = moment().utc();
    const { password, confirmPassword } = body;

    const resetPassword = await this.prisma.reset_password.findFirst({
      where: { kode_unique: tokenId },
      include: {
        personel: {
          select: {
            users: { select: { id: true, password: true } },
          },
        },
      },
    });

    if (!resetPassword || !resetPassword.personel.users)
      throw new BadRequestException('Token Anda tidak dikenali');

    const expToken = moment(resetPassword.expired_at);
    const isTokenActive = resetPassword.is_aktif;
    if (!isTokenActive || currentDate.isAfter(expToken))
      throw new BadRequestException('Token Anda sudah tidak berlaku');

    if (password !== confirmPassword)
      throw new BadRequestException(
        'Password dan confirmPassword tidak cocok. Silakan pastikan kedua kata sandi sesuai.',
      );

    const userId = resetPassword.personel.users.id;
    const currentPassword = resetPassword.personel.users.password;

    const comparePassword = await bcrypt.compare(password, currentPassword);
    if (comparePassword)
      throw new BadRequestException(
        'Password tidak boleh sama seperti yang pernah anda gunakan',
      );

    validateNewPassword(password);

    const newPassword = await bcrypt.hash(password, 10);

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const users = await tx.users.update({
        where: { id: userId },
        data: { password: newPassword },
      });

      await tx.reset_password.update({
        where: { id: resetPassword.id },
        data: { is_aktif: false },
      });

      return users;
    });

    const logMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.RESET_PASSWORD_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_RESET_PASSWORD as ConstantLogType,
        logMessage,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Password Anda berhasil diperbarui.',
    };
  }

  async getModules(req: any) {
    const user = req.user;
    const result = [...user.modules.flat()];

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_READ as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: result,
    };
  }

  private setEmail(email: string, nrp: string) {
    if (!email)
      throw new NotFoundException(`Email tidak terisi dengan nrp ${nrp}`);
    return email;
  }

  async addDeviceToken(req: any, body: DeviceTokenDto) {
    const queryResult = await this.authRepository.saveDeviceToken(
      body.deviceToken,
      req.user,
    );

    const message = 'Berhasil menambah FCM device token';
    const logMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.ADD_DEVICE_TOKEN,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.AUTHENTICATION_ADD_DEVICE_TOKEN as ConstantLogType,
        logMessage,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
