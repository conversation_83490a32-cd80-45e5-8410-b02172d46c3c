import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';

@Injectable()
export class AuthRepository {
  private readonly logger = new Logger(AuthRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async saveDeviceToken(token: string, user) {
    if (!token) {
      this.logger.log(
        `Device token tidak disediakan oleh user dengan nrp ${user.nrp}`,
      );
      return;
    }
    return this.prisma.users_device_token.upsert({
      where: {
        user_id: user.id,
      },
      update: {
        token,
      },
      create: {
        user_id: user.id,
        token,
      },
    });
  }

  async saveJwtToken(token: string, user) {
    this.prisma.$transaction(async (tx) => {
      await tx.users_jwt_token.upsert({
        where: {
          user_id: user.users.id,
        },
        update: {
          token,
        },
        create: {
          user_id: user.users.id,
          token,
        },
      });
    });
  }

  async findUsersJwtTokenByToken(token: string) {
    return await this.prisma.users_jwt_token.findFirst({
      where: {
        token,
      },
    });
  }
}
