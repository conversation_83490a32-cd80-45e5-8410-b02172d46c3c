import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { MODULES } from '../../../core/constants/module.constant';
import {
  CheckModulePermission,
  LogActivityText,
  MaskResponseLog,
  Module,
  UseLogActivity,
} from '../../../core/decorators';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import {
  DeviceTokenDto,
  ForgotPasswordDto,
  LoginDto,
  LogoutDto,
  ResendOtpDto,
  ResetPasswordDto,
  VerifyOtpDto,
} from '../dto/auth.dto';
import { AuthOtpService } from '../service/auth-otp.service';
import { AuthService } from '../service/auth.service';

@Module(MODULES.AUTH)
@CheckModulePermission(false)
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly otpService: AuthOtpService,
  ) {}

  @LogActivityText('Masuk aplikasi lewat autentikasi service')
  @MaskResponseLog('email')
  @Post('/login')
  @HttpCode(200)
  async login(@Req() req: any, @Body() body: LoginDto) {
    this.logger.log(
      `Entering ${this.login.name} with body ${JSON.stringify(body)}`,
    );
    const response = await this.authService.login(req, body);
    this.logger.log(
      `Leaving ${this.login.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @LogActivityText('User melakukan permintaan OTP ulang')
  @Post('/resend-otp')
  @HttpCode(200)
  async resendOtp(@Req() req: any, @Body() body: ResendOtpDto) {
    this.logger.log(
      `Entering ${this.resendOtp.name} with body ${JSON.stringify(body)}`,
    );
    const response = await this.otpService.resendOtp(req, body.nrp);
    this.logger.log(
      `Leaving ${this.resendOtp.name} with body ${JSON.stringify(body)} and response ${response}`,
    );

    return response;
  }

  @LogActivityText('User keluar')
  @Post('/logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async logout(@Req() req: Request, @Body() body: LogoutDto) {
    this.logger.log(
      `Entering ${this.logout.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.authService.logout(req, body);

    this.logger.log(
      `Leaving ${this.logout.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @UseLogActivity(false)
  // @LogActivityText('User melakukan lupa password')
  @Post('/forgot-password')
  @HttpCode(200)
  async forgotPassword(@Req() req: any, @Body() body: ForgotPasswordDto) {
    this.logger.log(
      `Entering ${this.forgotPassword.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.authService.forgotPassword(req, body.nrp);

    this.logger.log(
      `Leaving ${this.forgotPassword.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @UseLogActivity(false)
  // @LogActivityText('User melakukan reset password')
  @Patch('/reset-password/:tokenId')
  @HttpCode(200)
  @UsePipes(new ValidationPipe())
  async resetPassword(
    @Req() req: any,
    @Param('tokenId') tokenId: string,
    @Body() body: ResetPasswordDto,
  ) {
    this.logger.log(
      `Entering ${this.resetPassword.name} with token id ${tokenId} and body ${JSON.stringify(body)}`,
    );

    const response = await this.authService.resetPassword(req, tokenId, body);

    this.logger.log(
      `Leaving ${this.resetPassword.name} with token id ${tokenId} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @UseLogActivity(false)
  @Post('/modules')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getModules(@Req() req: Request) {
    this.logger.log(`Entering ${this.getModules.name}`);

    const response = await this.authService.getModules(req);

    this.logger.log(
      `Leaving ${this.getModules.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @LogActivityText('User melakukan verifikasi OTP')
  @MaskResponseLog('user.email', 'accessToken', 'user.roles')
  @Post('/verify-otp')
  @HttpCode(200)
  async verifyOtp(@Req() req: any, @Body() body: VerifyOtpDto) {
    this.logger.log(
      `Entering ${this.verifyOtp.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.otpService.verifyOtp(req, body);

    this.logger.log(
      `Leaving ${this.verifyOtp.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/device-token')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async addDeviceToken(@Req() req: Request, @Body() body: DeviceTokenDto) {
    this.logger.log(
      `Entering ${this.addDeviceToken.name} with body ${JSON.stringify(body)}`,
    );
    const response = await this.authService.addDeviceToken(req, body);
    this.logger.log(
      `Leaving ${this.addDeviceToken.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
