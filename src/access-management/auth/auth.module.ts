//src/auth/auth.module.ts
import { Global, Module } from '@nestjs/common';
import { AuthService } from './service/auth.service';
import { AuthController } from './controller/auth.controller';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from 'src/api-utils/prisma/prisma.module';
import { UsersModule } from 'src/access-management/users/users.module';
import { JwtStrategy } from '../../core/configs/jwt.strategy';
import { UsersService } from 'src/access-management/users/service/users.service';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { SmtpService } from 'src/api-utils/smtp/service/smtp.service';
import { AuthOtpService } from './service/auth-otp.service';
import { AuthJwtService } from './service/auth-jwt.service';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import { AuthRepository } from './repository/auth.repository';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Global()
@Module({
  imports: [
    PrismaModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET_KEY'),
        signOptions: {
          expiresIn: config.get<string>('JWT_EXPIRED_TIME'),
        },
      }),
    }),
    UsersModule,
    LogsActivityModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    UsersService,
    LogsActivityService,
    MinioService,
    SmtpService,
    AuthOtpService,
    AuthJwtService,
    ExcelService,
    AuthRepository,
  ],
  exports: [AuthJwtService, AuthService],
})
export class AuthModule {}
