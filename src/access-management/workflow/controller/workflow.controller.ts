import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpCode,
  Logger,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  AnyFilesInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import { Request } from 'express';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PermissionGuard } from 'src/core/guards/permission-auth.guard';
import { BracketToObject } from '../../../core/utils/common.utils';
import { WorkflowPersonelSignService } from '../service/workflow-personel-sign.service';
import { WorkflowTransactionService } from '../service/workflow-transaction.service';
import {
  <PERSON>reateArchiveDto,
  CreateLetterReportDto,
  CreatePersonelSignDto,
  CreateTransactionDto,
  CreateWorkflowDto,
  GetListDto,
  GetListTransactionDto,
  GetQuotaDto,
  RejectTransactionDto,
  SignTransactionDto,
  UpdateTransactionDto,
  UpdateWorkflowDto,
} from '../dto/workflow.dto';
import { WorkflowService } from '../service/workflow.service';
import { ArchiveAndLetterReportService } from '../service/archive-and-letter-report.service';

@Controller('workflow')
@UseGuards(JwtAuthGuard, PermissionGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class WorkflowController {
  private readonly logger = new Logger(WorkflowController.name);

  constructor(
    private readonly workflowService: WorkflowService,
    private readonly workflowTransactionService: WorkflowTransactionService,
    private readonly workflowPersonelSignService: WorkflowPersonelSignService,
    private readonly archiveAndLetterReportService: ArchiveAndLetterReportService,
  ) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: Request, @Body() body: CreateWorkflowDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const workflow = await this.workflowService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('/transaction')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createTransaction(
    @Req() req: Request,
    @Body() body: CreateTransactionDto,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.createTransaction.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    const workflow = await this.workflowTransactionService.create(
      req,
      BracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.createTransaction.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(FilesInterceptor('file', 1))
  @Post('/personel-sign')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createPersonelSign(
    @Req() req: Request,
    @Body() body: CreatePersonelSignDto,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 500 }),
          new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.createPersonelSign.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    body.file = files;

    const workflow = await this.workflowPersonelSignService.create(req, body);
    this.logger.log(
      `Leaving ${this.createPersonelSign.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  // Create Letter Report
  @UseInterceptors(FilesInterceptor('file', 1))
  @Post('/letter-report')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createLetterReport(
    @Req() req: Request,
    @Body() body: CreateLetterReportDto,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.createLetterReport.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    body.file = files;
    const letterReport = await this.archiveAndLetterReportService.create(
      req,
      body,
    );
    this.logger.log(
      `Leaving ${this.createLetterReport.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(letterReport)}`,
    );

    return letterReport;
  }

  // Create Archive
  @UseInterceptors(FilesInterceptor('file', 1))
  @Post('/archive')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createArchive(
    @Req() req: Request,
    @Body() body: CreateArchiveDto,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.createArchive.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    body.file = files;
    const workflow = await this.archiveAndLetterReportService.createArchive(
      req,
      body,
    );
    this.logger.log(
      `Leaving ${this.createArchive.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  // Get Letter Report
  @Get('/letter-report')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getLetterReportList(
    @Req() req: Request,
    @Query() query: GetListDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getLetterReportList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const result = await this.archiveAndLetterReportService.getList(
      req,
      query,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getLetterReportList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(result)}`,
    );

    return result;
  }

  // Get Letter Report By Id
  @Get('/letter-report/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getLetterReportDetail(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(
      `Entering ${this.getLetterReportDetail.name} with id: ${id}`,
    );
    const data = await this.archiveAndLetterReportService.getLetterReportDetail(
      req,
      id,
    );
    this.logger.log(
      `Leaving ${this.getLetterReportDetail.name} with id: ${id} and response: ${JSON.stringify(data)}`,
    );

    return data;
  }

  // Get Archive
  @Get('/archive')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getArchiveList(
    @Req() req: Request,
    @Query() query: GetListDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getArchiveList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const result = await this.archiveAndLetterReportService.getArchiveList(
      req,
      query,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getArchiveList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(result)}`,
    );

    return result;
  }

  // Get Quota
  @Get('/quota')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getQuota(@Req() req: Request, @Query() query: GetQuotaDto) {
    this.logger.log(
      `Entering ${this.getQuota.name} with query: ${JSON.stringify(query)}`,
    );
    const data = await this.archiveAndLetterReportService.getQuota(req, query);
    this.logger.log(
      `Leaving ${this.getQuota.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(data)}`,
    );

    return data;
  }

  @Get('/transaction')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getTransactionList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() query: GetListTransactionDto,
  ) {
    this.logger.log(
      `Entering ${this.getTransactionList.name} with pagination data: ${JSON.stringify(paginationData)} and query: ${JSON.stringify(query)}`,
    );
    const data = await this.workflowTransactionService.getList(
      req,
      paginationData,
      query,
    );
    this.logger.log(
      `Leaving ${this.getTransactionList.name} with pagination data: ${JSON.stringify(paginationData)} and query: ${JSON.stringify(query)} and response: ${JSON.stringify(data)}`,
    );

    return data;
  }

  @Get('/transaction/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getTransaction(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getTransaction.name} with id: ${id}`);
    const workflow = await this.workflowTransactionService.get(req, +id);
    this.logger.log(
      `Leaving ${this.getTransaction.name} with id: ${id} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @Get('/personel-sign')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getPersonelSign(@Req() req: Request) {
    this.logger.log(`Entering ${this.getPersonelSign.name}`);
    const workflow = await this.workflowPersonelSignService.getList(req);
    this.logger.log(
      `Leaving ${this.getPersonelSign.name} with response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const workflow = await this.workflowService.get(req, +id);
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() query: GetListDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const data = await this.workflowService.getList(
      req,
      query,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with query: ${JSON.stringify(query)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(data)}`,
    );

    return data;
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('/sign/:id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async sign(
    @Req() req: Request,
    @Body() body: SignTransactionDto,
    @Param('id') id: string,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.sign.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    const workflow = await this.workflowTransactionService.sign(
      req,
      +id,
      BracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.sign.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('/paraf/:id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async paraf(
    @Req() req: Request,
    @Body() body: SignTransactionDto,
    @Param('id') id: string,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.paraf.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    const workflow = await this.workflowTransactionService.paraf(
      req,
      +id,
      BracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.paraf.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('/reject/:id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async reject(
    @Req() req: Request,
    @Body() body: RejectTransactionDto,
    @Param('id') id: string,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.reject.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files?.length}`,
    );
    files?.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    const workflow = await this.workflowTransactionService.reject(
      req,
      +id,
      BracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.reject.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files?.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Put('/transaction/:id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async updateTransaction(
    @Req() req: Request,
    @Body() body: UpdateTransactionDto,
    @Param('id') id: string,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.updateTransaction.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    const workflow = await this.workflowTransactionService.update(
      req,
      +id,
      BracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.updateTransaction.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @UseInterceptors(FilesInterceptor('file', 1))
  @Put('/personel-sign/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updatePersonelSign(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: CreatePersonelSignDto,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 500 }),
          new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePersonelSign.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    body.file = files;

    const workflow = await this.workflowPersonelSignService.update(
      req,
      +id,
      body,
    );
    this.logger.log(
      `Leaving ${this.updatePersonelSign.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  // Edit Letter Report
  @UseInterceptors(FilesInterceptor('file', 1))
  @Put('/letter-report/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(201)
  async updateLetterReport(
    @Req() req: Request,
    @Body() body: CreateLetterReportDto,
    @Param('id') id: string,
    @UploadedFiles(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    this.logger.log(
      `Entering ${this.updateLetterReport.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    body.file = files;
    const result = await this.archiveAndLetterReportService.update(
      req,
      +id,
      body,
    );
    this.logger.log(
      `Leaving ${this.updateLetterReport.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(result)}`,
    );

    return result;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: Request,
    @Param('id') id: string,
    @Body() body: UpdateWorkflowDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const workflow = await this.workflowService.update(req, +id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @Delete('/transaction/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteTransaction(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.deleteTransaction.name} with id: ${id}`);
    const workflow = await this.workflowTransactionService.delete(req, +id);
    this.logger.log(
      `Leaving ${this.deleteTransaction.name} with id: ${id} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  @Delete('/personel-sign/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deletePersonelSign(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.deletePersonelSign.name} with id: ${id}`);
    const workflow = await this.workflowPersonelSignService.delete(req, +id);
    this.logger.log(
      `Leaving ${this.deletePersonelSign.name} with id: ${id} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }

  // Delete Letter Report
  @Delete('/letter-report/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteLetterReport(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.deleteLetterReport.name} with id: ${id}`);
    const letterReport = await this.archiveAndLetterReportService.delete(
      req,
      +id,
    );
    this.logger.log(
      `Leaving ${this.deleteLetterReport.name} with id: ${id} and response: ${JSON.stringify(letterReport)}`,
    );

    return letterReport;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const workflow = await this.workflowService.delete(req, +id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(workflow)}`,
    );

    return workflow;
  }
}
