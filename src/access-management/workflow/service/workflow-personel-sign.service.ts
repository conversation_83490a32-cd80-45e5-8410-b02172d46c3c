import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';
import { error } from 'console';
import * as crypto from 'crypto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreatePersonelSignDto } from '../dto/workflow.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class WorkflowPersonelSignService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly _secret = this.configService.get('SIGN_SECRET');

  async getList(req: any) {
    try {
      const queryResult = await this.prisma.personel_sign.findMany({
        where: { personel_id: +req.user['personel_id'] },
        select: {
          id: true,
          filename: true,
          url: true,
          qr_data: true,
        },
      });

      queryResult.forEach((item) => {
        if (item.qr_data) {
          const [res, key] = item.qr_data.split('.');
          const sec = crypto
            .createHash('sha256')
            .update(String(this._secret))
            .digest('base64')
            .substr(0, 32);
          const iv = Buffer.from(key, 'base64');

          const decipher = crypto.createDecipheriv('aes-256-cbc', sec, iv);
          item.qr_data = decipher.update(res, 'hex', 'utf8');
          item.qr_data += decipher.final('utf8');
        }
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw error;
    }
  }

  async create(req: any, payload: CreatePersonelSignDto) {
    try {
      if (!payload.qr_data && !payload.file?.length) {
        throw new BadRequestException('Data qr atau file harus terisi');
      }

      let mock: Prisma.personel_signUncheckedCreateInput = {
        qr_data: payload.qr_data,
        personel_id: +req.user['personel_id'],
      };

      if (payload.qr_data) {
        const sec = crypto
          .createHash('sha256')
          .update(String(this._secret))
          .digest('base64')
          .substr(0, 32);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv('aes-256-cbc', sec, iv);

        mock.qr_data = cipher.update(payload.qr_data, 'utf8', 'hex');
        mock.qr_data += cipher.final('hex');
        mock.qr_data += '.';
        mock.qr_data += iv.toString('base64');
      }

      if (payload.file?.length) {
        const [upload] = await this.minioService.uploadFiles(payload.file);

        mock = {
          ...mock,
          ...upload.rawFile,
          key: upload.uploaded.Key,
          url: upload.uploaded.Location,
          filename: upload.uploaded.filename,
        };
      }

      const queryResult = await this.prisma.personel_sign.create({
        data: mock,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, payload: CreatePersonelSignDto) {
    try {
      const check = await this.prisma.personel_sign.findFirst({
        where: { id },
      });

      if (!check) {
        throw new BadRequestException(
          `Tanda tangan dengan ${id} tidak ditemukan`,
        );
      }
      if (check.personel_id != req.user['personel_id']) {
        throw new BadRequestException('Data tanda tangan ini bukan milik Anda');
      }

      let mock: Record<string, any> = {};
      if (payload.file?.length) {
        const [upload] = await this.minioService.uploadFiles(payload.file);

        mock = {
          ...mock,
          ...upload.rawFile,
          key: upload.uploaded.Key,
          url: upload.uploaded.Location,
          filename: upload.uploaded.filename,
        };
      }
      if (payload.qr_data) {
        const sec = crypto
          .createHash('sha256')
          .update(String(this._secret))
          .digest('base64')
          .substr(0, 32);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv('aes-256-cbc', sec, iv);

        mock.qr_data = cipher.update(payload.qr_data, 'utf8', 'hex');
        mock.qr_data += cipher.final('hex');
        mock.qr_data += '.';
        mock.qr_data += iv.toString('base64');
      }

      const queryResult = await this.prisma.personel_sign.update({
        where: { id },
        data: mock,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const check = await this.prisma.personel_sign.findFirst({
        where: { id },
      });

      if (!check) {
        throw new BadRequestException(
          `Tanda tangan dengan ${id} tidak ditemukan`,
        );
      }
      if (check.personel_id != req.user['personel_id']) {
        throw new BadRequestException('Data tanda tangan ini bukan milik Anda');
      }

      const queryResult = await this.prisma.personel_sign.delete({
        where: { id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }
}
