import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { SearchAndSortDTO } from '../../../core/dtos';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import {
  CreateArchiveDto,
  CreateLetterReportDto,
  GetListDto,
  GetQuotaDto,
} from '../dto/workflow.dto';

@Injectable()
export class ArchiveAndLetterReportService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  // create letter report
  async create(req: Request, body: CreateLetterReportDto) {
    try {
      // check kuota
      const checkKuota = await this.prisma.workflow_letter_report.count({
        where: {
          letter_date: {
            gte: new Date(body.letter_date),
            lte: new Date(body.letter_date),
          },
        },
      });

      if (checkKuota >= 50) {
        throw new BadRequestException('Kuota tidak mencukupi');
      }

      // upload file
      const [upload] = await this.minioService.uploadFiles(body.file as any);

      // create to db
      const letterReport = await this.prisma.workflow_letter_report.create({
        data: {
          letter_number: body.letter_number,
          letter_type: body.letter_type as any,
          letter_date: new Date(body.letter_date),
          to: body.to,
          subject: body.subject,
          tembusan: body.tembusan,
          participant: String(body.participant),
          created_by: req.user?.['personel_id'],
        },
      });

      await this.prisma.workflow_archive_letter_document.create({
        data: {
          ...upload.rawFile,
          key: upload.uploaded.Key,
          url: upload.uploaded.Location,
          filename: upload.uploaded.filename,
          type: 'LETTER_REPORT',
          letter_report_id: letterReport.id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
          message,
          {},
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: {},
      };
    } catch (err) {
      throw err;
    }
  }

  // create archive
  async createArchive(req: Request, body: CreateArchiveDto) {
    try {
      // upload file
      const [upload] = await this.minioService.uploadFiles(body.file as any);

      // create to db
      const archive = await this.prisma.workflow_archive_letter.create({
        data: {
          letter_number: body.letter_number,
          letter_date: new Date(body.letter_date),
          from: body.from,
          to: body.to,
          subject: body.subject,
          type: body.type as any,
          information: body.information,
          keyword: body.keyword,
          created_by: req.user?.['personel_id'],
        },
      });

      await this.prisma.workflow_archive_letter_document.create({
        data: {
          ...upload.rawFile,
          key: upload.uploaded.Key,
          url: upload.uploaded.Location,
          filename: upload.uploaded.filename,
          type: 'ARCHIVE',
          archive_id: archive.id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
          message,
          {},
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: {},
      };
    } catch (err) {
      throw err;
    }
  }

  // get letter report list
  async getList(
    req: Request,
    paginationData: GetListDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        letter_number: { field: 'letter_number', type: 'string' },
        letter_date: { field: 'letter_date', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      // get from db
      const [totalData, letterReports] = await this.prisma.$transaction([
        this.prisma.workflow_letter_report.count({ where: where }),
        this.prisma.workflow_letter_report.findMany({
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: {
            ...where,
            letter_date: paginationData.date
              ? {
                  gte: paginationData.date,
                  lte: paginationData.date,
                }
              : undefined,
          },
          include: {
            document: true,
          },
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      // get rekap surat
      const rekapSurat = await this.prisma.workflow_transaction.findMany({
        select: {
          workflow_id: true, // ID dari workflow_transaction
          workflow_transaction_workflow: {
            select: {
              jenis_surat: true, // Ambil jenis_surat dari tabel workflow
            },
          },
        },
      });

      // Agregasi Manual untuk Menghitung Jumlah Berdasarkan jenis_surat
      const hasilRekap = rekapSurat.reduce((acc, curr) => {
        const jenisSurat = curr.workflow_transaction_workflow.jenis_surat;

        // Inisialisasi jika jenis_surat belum ada dalam objek accumulator
        if (!acc[jenisSurat]) {
          acc[jenisSurat] = 0;
        }

        // Tambahkan jumlah untuk jenis_surat yang sesuai
        acc[jenisSurat] += 1;

        return acc;
      }, {});

      const queryResult = {
        data: letterReports,
        totalWorkflows: hasilRekap,
        page,
        totalPage,
        totalData,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  // get letter report detail
  async getLetterReportDetail(req: Request, id: string) {
    try {
      const data = await this.prisma.workflow_letter_report.findFirstOrThrow({
        where: {
          id: Number(id),
        },
        include: {
          document: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: data,
      };
    } catch (error) {
      throw error;
    }
  }

  // get archive list
  async getArchiveList(
    req: Request,
    paginationData: GetListDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        letter_number: { field: 'letter_number', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      // get from db
      const [totalData, archiveLetters] = await this.prisma.$transaction([
        this.prisma.workflow_archive_letter.count({ where: where }),
        this.prisma.workflow_archive_letter.findMany({
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: {
            ...where,
            letter_date: paginationData.date
              ? {
                  gte: paginationData.date,
                  lte: paginationData.date,
                }
              : undefined,
          },
          include: {
            document: true,
          },
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      // get rekap surat
      const rekapSurat = await this.prisma.workflow_transaction.findMany({
        select: {
          workflow_id: true, // ID dari workflow_transaction
          workflow_transaction_workflow: {
            select: {
              jenis_surat: true, // Ambil jenis_surat dari tabel workflow
            },
          },
        },
      });

      // Agregasi Manual untuk Menghitung Jumlah Berdasarkan jenis_surat
      const hasilRekap = rekapSurat.reduce((acc, curr) => {
        const jenisSurat = curr.workflow_transaction_workflow.jenis_surat;

        // Inisialisasi jika jenis_surat belum ada dalam objek accumulator
        if (!acc[jenisSurat]) {
          acc[jenisSurat] = 0;
        }

        // Tambahkan jumlah untuk jenis_surat yang sesuai
        acc[jenisSurat] += 1;

        return acc;
      }, {});

      const queryResult = {
        data: archiveLetters,
        totalWorkflows: hasilRekap,
        page,
        totalPage,
        totalData,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  // edit letter report
  async update(req: Request, id: number, body: CreateLetterReportDto) {
    try {
      // find in db
      const letterReport =
        await this.prisma.workflow_letter_report.findFirstOrThrow({
          where: { id },
          include: { document: true },
        });

      // check kuota
      const checkKuota = await this.prisma.workflow_letter_report.count({
        where: {
          letter_date: {
            gte: new Date(body.letter_date),
            lte: new Date(body.letter_date),
          },
        },
      });

      if (
        checkKuota >= 50 &&
        new Date(body.letter_date).getDate() !==
          new Date(letterReport.letter_date).getDate() &&
        new Date(body.letter_date).getMonth() !==
          new Date(letterReport.letter_date).getMonth() &&
        new Date(body.letter_date).getFullYear() !==
          new Date(letterReport.letter_date).getFullYear()
      ) {
        throw new BadRequestException('Kuota tidak mencukupi');
      }

      // upload file
      const [upload] = await this.minioService.uploadFiles(body.file as any);

      // update in db
      await this.prisma.workflow_letter_report.update({
        where: {
          id,
        },
        data: {
          letter_number: body.letter_number,
          letter_type: body.letter_type as any,
          letter_date: new Date(body.letter_date),
          to: body.to,
          subject: body.subject,
          tembusan: body.tembusan,
          participant: String(body.participant),
          updated_at: new Date(),
        },
      });

      await this.prisma.workflow_archive_letter_document.update({
        where: { id: letterReport.document[0].id },
        data: {
          ...upload.rawFile,
          key: upload.uploaded.Key,
          url: upload.uploaded.Location,
          filename: upload.uploaded.filename,
          type: 'LETTER_REPORT',
          letter_report_id: letterReport.id,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_UPDATE as ConstantLogType,
          message,
          {},
        ),
      );
    } catch (err) {
      throw err;
    }
  }

  // delete letter report
  async delete(req: Request, id: number) {
    try {
      // find in db
      const letterReport =
        await this.prisma.workflow_letter_report.findFirstOrThrow({
          where: { id },
          include: {
            document: true,
          },
        });

      // delete letter report
      await this.prisma.workflow_letter_report.update({
        where: {
          id,
        },
        data: {
          deleted_at: new Date(),
          document: {
            update: {
              where: {
                id: letterReport.document[0].id,
              },
              data: {
                deleted_at: new Date(),
              },
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_DELETE as ConstantLogType,
          message,
          {},
        ),
      );
    } catch (err) {
      throw err;
    }
  }

  // get quota
  async getQuota(req: Request, query: GetQuotaDto) {
    try {
      const startOfMonth = new Date(query.date);
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date(startOfMonth);
      endOfMonth.setMonth(endOfMonth.getMonth() + 1);
      endOfMonth.setDate(0); // Go back one day to get the last day of the current month

      const data = await this.prisma.workflow_letter_report.findMany({
        where: {
          letter_date: {
            gte: startOfMonth,
            lt: endOfMonth,
          },
        },
      });

      const daysInMonth = endOfMonth.getDate();
      const quota = Array.from({ length: daysInMonth }, () => 0);

      data.forEach((item) => {
        const date = new Date(item.letter_date).getDate();
        quota[date - 1]++;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          quota,
        ),
      );

      return quota;
    } catch (error) {
      throw error;
    }
  }
}
