import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { workflow_status_enum } from '@prisma/client';
import { PaginationDto } from 'src/core/dtos';
import { BatchingProcess } from 'src/core/utils/batch.utils';
import { CustomErrorException } from 'src/core/exceptions/custom-error.exception';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { IColumnMapping } from 'src/core/interfaces/db.interface';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateTransactionDto,
  GetListTransactionDto,
  RejectTransactionDto,
  SignTransactionDto,
  UpdateTransactionDto,
} from '../dto/workflow.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class WorkflowTransactionService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async get(req: any, id: number) {
    try {
      const queryResult = await this.prisma.workflow_transaction.findFirst({
        where: { id: id },
        select: {
          id: true,
          status: true,
          note: true,
          workflow_transaction_workflow: {
            select: {
              nama: true,
              id: true,
            },
          },
          created_by_personel: {
            select: {
              id: true,
              nama_lengkap: true,
            },
          },
          workflow_dokumen: {
            select: {
              filename: true,
              url: true,
              id: true,
              used_in_step: true,
              current_step: true,
              workflow_validation_log: {
                select: {
                  signed_by_personel: {
                    select: { nama_lengkap: true, foto_file: true },
                  },
                  created_at: true,
                },
              },
            },
          },
          workflow_transaction_flow: {
            orderBy: { step: 'asc' },
            select: {
              id: true,
              step: true,
              status: true,
              note: true,
              jabatan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              satuan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              workflow_transaction_personel: {
                select: {
                  id: true,
                  paraf: true,
                  tanda_tangan: true,
                  has_paraf: true,
                  has_tanda_tangan: true,
                  personel: {
                    select: {
                      id: true,
                      nrp: true,
                      nama_lengkap: true,
                    },
                  },
                },
                orderBy: { order: 'asc' },
              },
            },
          },
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Workflow id ${id} not found`);
      }

      queryResult.workflow_dokumen.map((item) => {
        queryResult.workflow_transaction_flow[item.current_step - 1][
          'document'
        ] ||= [];
        queryResult.workflow_transaction_flow[item.current_step - 1][
          'document'
        ].push(item);
      });

      delete queryResult.workflow_dokumen;

      queryResult.workflow_transaction_flow.forEach((item) => {
        item.workflow_transaction_personel =
          item.workflow_transaction_personel.map((personel) => {
            return {
              id: personel.personel.id,
              paraf: personel.paraf,
              tanda_tangan: personel.tanda_tangan,
              has_paraf: personel.has_paraf,
              has_tanda_tangan: personel.has_tanda_tangan,
              name: personel.personel.nama_lengkap,
              nrp: personel.personel.nrp,
            };
          }) as any;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    query: GetListTransactionDto,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const {
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        jenis_surat,
      } = query;

      const columnMapping: IColumnMapping = {
        nama: { field: 'workflow.nama', type: 'string' },
        dibuat_oleh: { field: 'dibuat_oleh.nama_lengkap', type: 'string' },
        status: {
          field: 'status',
          type: 'enum',
          enums: workflow_status_enum,
        },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, workflow] = await this.prisma.$transaction([
        this.prisma.workflow_transaction.count({
          where: {
            ...where,
            workflow_transaction_workflow: {
              jenis_surat: { equals: jenis_surat },
            },
          },
        }),
        this.prisma.workflow_transaction.findMany({
          select: {
            id: true,
            workflow_transaction_workflow: {
              select: { nama: true },
            },
            current_step: true,
            status: true,
            created_at: true,
            created_by_personel: {
              select: {
                nama_lengkap: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: {
            ...where,
            workflow_transaction_workflow: {
              jenis_surat: { equals: jenis_surat },
            },
          },
        }),
      ]);

      const queryResult = await BatchingProcess({
        data: workflow,
        limit: 20,
        callback: async (data) => {
          await Promise.all(
            data.map(async (item) => {
              const docs = await this.prisma.workflow_dokumen.findMany({
                where: {
                  workflow_transaction_id: item.id,
                  current_step: item.current_step,
                },
                select: {
                  filename: true,
                  url: true,
                },
              });

              item.document = docs;
            }),
          );

          return data;
        },
      });

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      if (error instanceof CustomErrorException) {
        throw new BadRequestException(
          `Status hanya bisa ${Object.keys(workflow_status_enum).join(', ')}`,
        );
      }

      throw error;
    }
  }

  async create(req: any, body: CreateTransactionDto) {
    const check = await this.prisma.workflow.findFirst({
      where: { id: body.workflow_id },
    });

    if (!check) {
      throw new BadRequestException(
        `Workflow dengan id ${body.workflow_id} tidak ditemukan`,
      );
    }

    try {
      const queryResult = await this.prisma.$transaction(async (trx) => {
        const transaction = await trx.workflow_transaction.create({
          data: {
            personel: {
              connect: { id: req.user['personel_id'] },
            },
            workflow_transaction_workflow: {
              connect: { id: body.workflow_id },
            },
            created_by_personel: {
              connect: { id: req.user['personel_id'] },
            },
          },
        });

        for (const [i, flow] of body.flow.entries()) {
          const insert = await trx.workflow_transaction_flow.create({
            data: {
              step: flow.step,
              jabatan_id: flow.jabatan,
              // satuan_id: flow.satuan,
              workflow_transaction_id: transaction.id,
              note: flow.notes,
              status: flow.step === 1 ? 'ON_PROCESS' : 'WAITING',
              must_sign_length: flow.personel.length,
              next_step: body.flow[i + 1]?.step,
            },
          });

          await trx.workflow_transaction_personel.createMany({
            data: flow.personel.map((p, i) => ({
              personel_id: p.personel_id,
              workflow_transaction_flow_id: insert.id,
              paraf: p.paraf,
              order: i,
              tanda_tangan: p.tanda_tangan,
            })),
          });

          const upload = await this.minioService.uploadFiles(
            flow.document as any,
          );
          return await trx.workflow_dokumen.createMany({
            data: upload.map((d) => ({
              ...d.rawFile,
              key: d.uploaded.Key,
              url: d.uploaded.Location,
              filename: d.uploaded.filename,
              workflow_transaction_id: transaction.id,
              current_step: d.rawFile['used_in_step'][0],
            })),
          });
        }
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async sign(req: any, id: number, body: SignTransactionDto) {
    const check = await this.prisma.workflow_transaction_flow.findFirst({
      where: { id },
      include: {
        workflow_transaction_personel: {
          select: {
            id: true,
            personel_id: true,
            has_tanda_tangan: true,
            order: true,
          },
          where: {
            OR: [
              { has_tanda_tangan: false },
              { personel_id: req.user['personel_id'] },
            ],
          },
          orderBy: { order: 'asc' },
        },
      },
    });

    const personel = check.workflow_transaction_personel.filter(
      (d) => d.personel_id == req.user['personel_id'],
    );

    if (!check) {
      throw new BadRequestException(`Workflow dengan id ${id} tidak ditemukan`);
    }

    if (!personel?.length) {
      throw new BadRequestException(
        `Anda tidak memiliki akses untuk menandatangani dokumen ini`,
      );
    }

    if (personel[0].order !== check.sign_length) {
      throw new BadRequestException(
        'Saat ini dto membutuhkan tanda tangan dari personel lain terlebih dahulu',
      );
    }

    if (personel[0].has_tanda_tangan) {
      throw new BadRequestException(`Anda sudah menandatangani dokumen ini`);
    }

    const upload = await this.minioService.uploadFiles(body.document as any);

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const inc = await trx.workflow_transaction_flow.update({
        where: { id },
        data: { sign_length: { increment: 1 } },
      });
      await Promise.all(
        upload.map(async (item) => {
          const mock = {
            key: item.uploaded.Key,
            url: item.uploaded.Location,
            filename: item.uploaded.filename,
          };

          if (inc.must_sign_length === inc.sign_length) {
            await trx.workflow_transaction_flow.update({
              where: { id },
              data: { status: 'DONE' },
            });
            if (inc.next_step) {
              mock['current_step'] = inc.next_step;
              await trx.workflow_transaction.update({
                where: { id: inc.workflow_transaction_id },
                data: { current_step: inc.next_step, note: '' },
              });
            } else {
              await trx.workflow_transaction.update({
                where: { id: inc.workflow_transaction_id },
                data: { status: 'DONE', note: '' },
              });
            }
          }

          await trx.workflow_dokumen.update({
            where: { id: item.rawFile['id'] },
            data: mock,
          });

          await trx.workflow_validation_log.create({
            data: {
              signed_by: req.user['personel_id'],
              workflow_dokumen_id: item.rawFile['id'],
            },
          });

          await trx.workflow_transaction_personel.update({
            where: { id: personel[0].id },
            data: { has_tanda_tangan: true },
          });
        }),
      );
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.WORKFLOW_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async paraf(req: any, id: number, body: SignTransactionDto) {
    const check = await this.prisma.workflow_transaction_flow.findFirst({
      where: { id },
      include: {
        workflow_transaction_personel: {
          select: { id: true, personel_id: true, has_paraf: true, order: true },
          where: {
            OR: [
              { has_paraf: false },
              { personel_id: req.user['personel_id'] },
            ],
          },
          orderBy: { order: 'asc' },
        },
      },
    });
    const personel = check.workflow_transaction_personel.filter(
      (d) => d.personel_id == req.user['personel_id'],
    );

    if (!check) {
      throw new BadRequestException(`Workflow dengan id ${id} tidak ditemukan`);
    }

    if (!personel?.length) {
      throw new BadRequestException(
        `Anda tidak memiliki akses untuk memparaf dokumen ini`,
      );
    }

    if (personel[0].order !== check.sign_length) {
      throw new BadRequestException(
        'Saat ini dto membutuhkan paraf dari personel terlebih dahulu',
      );
    }

    if (personel[0].has_paraf) {
      throw new BadRequestException(`Anda sudah memparaf dokumen ini`);
    }

    const upload = await this.minioService.uploadFiles(body.document as any);

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const inc = await trx.workflow_transaction_flow.update({
        where: { id },
        data: { sign_length: { increment: 1 } },
      });
      await Promise.all(
        upload.map(async (item) => {
          const mock = {
            key: item.uploaded.Key,
            url: item.uploaded.Location,
            filename: item.uploaded.filename,
          };

          if (inc.must_sign_length === inc.sign_length) {
            await trx.workflow_transaction_flow.update({
              where: { id },
              data: { status: 'DONE' },
            });
            if (inc.next_step) {
              mock['current_step'] = inc.next_step;
              await trx.workflow_transaction.update({
                where: { id: inc.workflow_transaction_id },
                data: { current_step: inc.next_step, note: '' },
              });
            } else {
              await trx.workflow_transaction.update({
                where: { id: inc.workflow_transaction_id },
                data: { status: 'DONE', note: '' },
              });
            }
          }

          await trx.workflow_dokumen.update({
            where: { id: item.rawFile['id'] },
            data: mock,
          });

          await trx.workflow_transaction_personel.update({
            where: { id: personel[0].id },
            data: { has_paraf: true },
          });
        }),
      );
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.WORKFLOW_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async reject(req: any, id: number, body: RejectTransactionDto) {
    const check = await this.prisma.workflow_transaction_flow.findFirst({
      where: { id },
      include: { workflow_transaction: { select: { current_step: true } } },
    });

    if (!check) {
      throw new BadRequestException(`Workflow dengan id ${id} tidak ditemukan`);
    }

    if (check.status !== 'ON_PROCESS') {
      throw new BadRequestException('Workflow tidak membutuhkan approval');
    }

    if (check.step !== check.workflow_transaction.current_step) {
      throw new BadRequestException(
        'Workflow saat ini sedang di tahap lain, silahkan coba kembali saat telah sampai pada tahap Anda',
      );
    }

    try {
      const queryResult = await this.prisma.$transaction(async (trx) => {
        if (body.document?.length) {
          const upload = await this.minioService.uploadFiles(
            body.document as any,
          );
          await Promise.all(
            upload.map(async (item) => {
              await trx.workflow_dokumen.update({
                where: { id: item.rawFile['id'] },
                data: {
                  key: item.uploaded.Key,
                  url: item.uploaded.Location,
                  filename: item.uploaded.filename,
                },
              });
            }),
          );
        }

        const transaction = await trx.workflow_transaction.update({
          where: { id: check.workflow_transaction_id },
          data: { status: 'REJECTED', current_step: 1, note: body.note },
        });

        const flows = await trx.workflow_transaction_flow.findMany({
          where: { workflow_transaction_id: transaction.id },
        });

        await Promise.all(
          flows.map(async (flow) => {
            await trx.workflow_transaction_personel.updateMany({
              where: { workflow_transaction_flow_id: flow.id },
              data: { has_paraf: false },
            });
          }),
        );

        return await trx.workflow_transaction_flow.update({
          where: { id },
          data: { status: 'REJECTED', note: body.note },
        });
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async update(req: any, id: number, body: UpdateTransactionDto) {
    const check = await this.prisma.workflow_transaction.findFirst({
      where: { id },
    });

    if (!check) {
      throw new BadRequestException(`Workflow dengan id ${id} tidak ditemukan`);
    }

    if (check.status !== 'REJECTED') {
      throw new BadRequestException(
        'Workflow tidak bisa diubah sebelum ditolak',
      );
    }

    try {
      const queryResult = await this.prisma.$transaction(async (trx) => {
        const flows = await trx.workflow_transaction_flow.findMany({
          where: { workflow_transaction_id: id },
          orderBy: { step: 'asc' },
          include: { workflow_transaction_personel: true },
        });

        await trx.workflow_transaction.update({
          where: { id },
          data: { current_step: flows[0].step, note: '', status: 'ON_PROCESS' },
        });

        const flowDict: Record<string, any> = {};
        flows.map((f) => (flowDict[f.step] = f));

        for (const [i, flow] of body.flow.entries()) {
          const exist = flowDict[flow.step];

          if (!exist) continue;

          const personelDict: Record<string, any> = {};
          exist.personel.map((p) => (personelDict[p.personel_id] = p));

          await trx.workflow_transaction_flow.update({
            where: { id: exist.id },
            data: {
              // satuan_id: flow.satuan,
              status: flow.step === 1 ? 'ON_PROCESS' : 'WAITING',
              step: flow.step,
              note: flow.notes,
              sign_length: 0,
              must_sign_length: flow.personel.length,
              next_step: body.flow[i + 1]?.step,
              updated_at: new Date(),
            },
          });

          await Promise.all(
            flow.personel.map(async (personel, i) => {
              const check = personelDict[personel.personel_id];

              if (check) {
                await trx.workflow_transaction_personel.update({
                  where: { id: check.id },
                  data: {
                    has_paraf: false,
                    paraf: personel.paraf,
                    order: i,
                    personel_id: personel.personel_id,
                    tanda_tangan: personel.tanda_tangan,
                    updated_at: new Date(),
                  },
                });
              } else {
                await trx.workflow_transaction_personel.create({
                  data: {
                    order: i,
                    has_paraf: false,
                    has_tanda_tangan: false,
                    paraf: personel.paraf,
                    personel_id: personel.personel_id,
                    tanda_tangan: personel.tanda_tangan,
                    workflow_transaction_flow_id: exist.id,
                  },
                });
              }
            }),
          );

          if (flow.document?.length) {
            const upload = await this.minioService.uploadFiles(
              flow.document as any,
            );
            await trx.workflow_dokumen.createMany({
              data: upload.map((d) => ({
                ...d.rawFile,
                key: d.uploaded.Key,
                url: d.uploaded.Location,
                filename: d.uploaded.filename,
                workflow_transaction_id: id,
                current_step: d.rawFile['used_in_step'][0],
              })),
            });
          }
        }
        return flowDict;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      if (
        !(await this.prisma.workflow_transaction.findFirst({ where: { id } }))
      ) {
        throw new BadRequestException(`Workflow id ${id} not found!`);
      }

      const queryResult = await this.prisma.$transaction(async (trx) => {
        await trx.workflow_transaction.updateMany({
          where: { id },
          data: { deleted_at: new Date() },
        });

        const deletedFlow = await trx.workflow_transaction_flow.findMany({
          where: { workflow_transaction_id: id },
          select: { id: true },
        });
        await trx.workflow_transaction_flow.updateMany({
          where: { workflow_transaction_id: id },
          data: { deleted_at: new Date() },
        });

        await Promise.all(
          deletedFlow.map(async (flow) => {
            await trx.workflow_transaction_personel.updateMany({
              where: { workflow_transaction_flow_id: flow.id },
              data: { deleted_at: new Date() },
            });

            await trx.workflow_dokumen.updateMany({
              where: { workflow_transaction_id: flow.id },
              data: { deleted_at: new Date() },
            });
          }),
        );
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
