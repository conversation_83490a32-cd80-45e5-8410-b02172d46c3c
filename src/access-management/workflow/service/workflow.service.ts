import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { IColumnMapping } from 'src/core/interfaces/db.interface';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateWorkflowDto, UpdateWorkflowDto } from '../dto/workflow.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class WorkflowService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateWorkflowDto) {
    try {
      const { nama, jenis_surat, klasifikasi, personel_ids } = body;

      const queryResult = await this.prisma.workflow.create({
        data: {
          nama: nama,
          jenis_surat: jenis_surat,
          klasifikasi: klasifikasi,
          created_by: req.user?.['personel_id'],
          workflow_flow: {
            create: {
              step: 1,
              jabatan_id: Number(body.jabatan),
              // satuan_id: Number(body.satuan),
              note: body.note,
              workflow_flow_personel: {
                createMany: {
                  data: personel_ids.map((personel) => {
                    return {
                      personel_id: Number(personel),
                      tanda_tangan: true,
                    };
                  }),
                },
              },
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async get(req: any, id: number) {
    try {
      const queryResult = await this.prisma.workflow.findFirst({
        where: {
          id: id,
          deleted_at: null,
        },
        select: {
          id: true,
          nama: true,
          created_by_personel: {
            select: {
              id: true,
              nama_lengkap: true,
            },
          },
          workflow_flow: {
            where: { deleted_at: null },
            select: {
              id: true,
              step: true,
              note: true,
              workflow_flow_satuan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              workflow_flow_jabatan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              workflow_flow_personel: {
                select: {
                  id: true,
                  paraf: true,
                  tanda_tangan: true,
                  workflow_personel: {
                    select: {
                      id: true,
                      nrp: true,
                      nama_lengkap: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!queryResult) {
        throw new NotFoundException(`Workflow id ${id} not found`);
      }

      queryResult.workflow_flow.forEach((item) => {
        item.workflow_flow_personel = item.workflow_flow_personel.map(
          (personel) => {
            return {
              id: personel.workflow_personel.id,
              paraf: personel.paraf,
              tanda_tangan: personel.tanda_tangan,
              name: personel.workflow_personel.nama_lengkap,
              nrp: personel.workflow_personel.nrp,
            };
          },
        ) as any;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        nama: { field: 'nama', type: 'string' },
        dibuat_oleh: { field: 'dibuat_oleh.nama_lengkap', type: 'string' },
        jenis_surat: { field: 'jenis_surat', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.workflow.count({ where: where }),
        this.prisma.workflow.findMany({
          select: {
            id: true,
            nama: true,
            created_at: true,
            jenis_surat: true,
            created_by_personel: {
              select: {
                nama_lengkap: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: number, body: UpdateWorkflowDto) {
    const check = await this.prisma.workflow.findFirst({ where: { id: id } });

    if (!check) {
      throw new BadRequestException(`Workflow id ${id} not found`);
    }

    try {
      const queryResult = await this.prisma.$transaction(async (trx) => {
        return await Promise.all(
          body.data.map(async (data) => {
            const personel: Record<string, any> = {};
            if (data.id) {
              console.log({
                id: data.id,
                jabatan_id: +data.jabatan,
                note: data.note,
                updated_at: new Date(),
              });
              await trx.workflow_flow.update({
                where: { id: data.id },
                data: {
                  // satuan_id: +data.satuan,
                  jabatan_id: +data.jabatan,
                  note: data.note,
                  updated_at: new Date(),
                },
              });

              const check = await trx.workflow_personel.findMany({
                where: { workflow_flow_id: data.id },
              });
              check.map((d) => (personel[d.personel_id + ''] = d as any));
            } else {
              const create = await trx.workflow_flow.create({
                data: {
                  // satuan_id: +data.satuan,
                  jabatan_id: +data.jabatan,
                  step: +data.tahap,
                  workflow_id: id,
                  note: data.note,
                },
              });
              data.id = create.id as any;
            }

            for (const item of data.personel) {
              if (check[item.personel_id]) {
                await trx.workflow_personel.update({
                  where: { id: check[item.personel_id].id },
                  data: {
                    paraf: item.paraf,
                    personel_id: +item.personel_id,
                    tanda_tangan: item.tanda_tangan,
                    updated_at: new Date(),
                  },
                });
                delete check[item.personel_id];
              } else {
                await trx.workflow_personel.create({
                  data: {
                    paraf: item.paraf,
                    personel_id: +item.personel_id,
                    tanda_tangan: item.tanda_tangan,
                    workflow_flow_id: data.id,
                  },
                });
              }
            }

            if (Object.values(personel)?.length) {
              await trx.workflow_personel.deleteMany({
                where: {
                  id: {
                    in: Object.values(personel).map((d) => d.id) as any,
                  },
                },
              });
            }
          }),
        );
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      console.log(err);

      if (err.code === 'P2002') {
        throw new BadRequestException('Terdapat tahap yang duplikat.');
      }

      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      if (
        !(await this.prisma.workflow.findFirst({
          where: { id, deleted_at: null },
        }))
      ) {
        throw new BadRequestException(`Workflow id ${id} not found!`);
      }

      const queryResult = await this.prisma.workflow.update({
        where: { id: id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.WORKFLOW_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.WORKFLOW_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
