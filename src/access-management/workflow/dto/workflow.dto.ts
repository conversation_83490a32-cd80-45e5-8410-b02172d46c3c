import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { TransformBoolean } from 'src/core/decorators';

export class CreateWorkflowDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsString()
  @IsNotEmpty()
  klasifikasi: string;

  @IsString()
  @IsNotEmpty()
  jenis_surat: string;

  // @IsNumber()
  // @IsNotEmpty()
  // satuan: string;

  @IsNumber()
  @IsNotEmpty()
  jabatan: string;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  personel_ids: Array<string>;

  @IsString()
  note: string;
}

class PersonelDto {
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  personel_id: number;

  @IsBoolean()
  @IsNotEmpty()
  @TransformBoolean()
  paraf: boolean;

  @IsBoolean()
  @IsNotEmpty()
  @TransformBoolean()
  tanda_tangan: boolean;
}

class UpdateBodyDto {
  @ValidateIf((_, val) => !!val)
  @IsNumber()
  id: number;

  // @IsNumber()
  // @IsNotEmpty()
  // satuan: string;

  @IsNumber()
  @IsNotEmpty()
  jabatan: string;

  @IsNumber()
  @IsNotEmpty()
  tahap: number;

  @IsString()
  note: string;

  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => PersonelDto)
  personel: Array<PersonelDto>;
}

export class UpdateWorkflowDto {
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  @ValidateNested({ each: true })
  @Type(() => UpdateBodyDto)
  data: Array<UpdateBodyDto>;
}

class TransactionDocumentDto {
  @IsArray()
  @ValidateIf((_, val) => val?.length)
  @IsNumber({}, { each: true })
  @Type(() => Number)
  used_in_step: Array<number>;
}

class CreateTransactionFlowDto {
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  step: number;

  // @IsNumber()
  // @IsNotEmpty()
  // @Type(() => Number)
  // satuan: number;

  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  jabatan: number;

  @IsString()
  @IsNotEmpty()
  notes: string;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => PersonelDto)
  personel: Array<PersonelDto>;

  @IsArray()
  @ValidateIf((_, val) => val?.length)
  @ValidateNested({ each: true })
  @Type(() => TransactionDocumentDto)
  document: Array<TransactionDocumentDto>;
}

export class CreateTransactionDto {
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  workflow_id: number;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateTransactionFlowDto)
  flow: Array<CreateTransactionFlowDto>;

  files?: Array<any>;
}

export class UpdateTransactionDto {
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  @ValidateNested({ each: true })
  @Type(() => CreateTransactionFlowDto)
  flow: Array<CreateTransactionFlowDto>;

  files?: Array<any>;
}

export class GetListTransactionDto extends SearchAndSortDTO {
  @IsNotEmpty()
  @IsString()
  jenis_surat: string;
}

class DocumentBody {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  id: number;
}

export class SignTransactionDto {
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => DocumentBody)
  document: Array<DocumentBody>;
}

export class RejectTransactionDto {
  @IsNotEmpty()
  @IsString()
  note: string;

  @ValidateIf((_, val) => val?.length)
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => DocumentBody)
  document: Array<DocumentBody>;
}

export class GetListDto extends PaginationDto {
  @IsOptional()
  nama: string;

  @IsOptional()
  dibuat_oleh: string;

  @IsOptional()
  jenis_surat: string;

  @IsOptional()
  @Type(() => Date)
  created_at: string;

  @IsOptional()
  @Type(() => Date)
  date: string;
}

export class CreatePersonelSignDto {
  @IsOptional()
  qr_data: string;

  file?: any;
}

enum LetterType {
  'NOTA_DINAS',
  'SURAT_TELEGRAM',
  'KEPUTUSAN',
}

export class CreateLetterReportDto {
  @IsString()
  @IsNotEmpty()
  letter_number: string;

  @IsEnum(LetterType)
  letter_type: LetterType;

  @Type(() => Date)
  letter_date: string;

  @IsString()
  @IsNotEmpty()
  to: string;

  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsArray()
  @ArrayMinSize(1)
  @Type(() => String)
  tembusan: Array<string>;

  @IsNumberString()
  @IsNotEmpty()
  participant: number;

  file: any;
}

enum ArchiveType {
  'BIASA',
  'RAHASIA',
}

export class CreateArchiveDto {
  @IsString()
  @IsNotEmpty()
  letter_number: string;

  @Type(() => Date)
  letter_date: string;

  @IsString()
  @IsNotEmpty()
  from: string;

  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsString()
  @IsOptional()
  to: string;

  @IsEnum(ArchiveType)
  type: ArchiveType;

  @IsString()
  @IsOptional()
  information: string;

  @IsString()
  @IsOptional()
  keyword: string;

  file: any;
}

export class GetQuotaDto {
  @IsNotEmpty()
  @Type(() => Date)
  date: string;
}
