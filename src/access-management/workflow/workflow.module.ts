import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { ArchiveAndLetterReportService } from './service/archive-and-letter-report.service';
import { WorkflowPersonelSignService } from './service/workflow-personel-sign.service';
import { WorkflowTransactionService } from './service/workflow-transaction.service';
import { WorkflowController } from './controller/workflow.controller';
import { WorkflowService } from './service/workflow.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [WorkflowController],
  providers: [
    WorkflowService,
    WorkflowTransactionService,
    MinioService,
    WorkflowPersonelSignService,
    ArchiveAndLetterReportService,
  ],
})
export class WorkflowModule {}
