import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { RolesService } from '../service/roles.service';
import {
  CreateRoleDto,
  CreateRolePermissionDto,
  CreateRoleV4Dto,
  CreateRoleV5Dto,
  SearchAndSortRoleDto,
  UpdateRoleDto,
  UpdateRolePermissionDto,
} from '../dto/roles.dto';
import { PaginationDto } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';

@Controller('roles')
@Module(MODULES.ROLE_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class RolesController {
  private readonly logger = new Logger(RolesController.name);

  constructor(private readonly roleService: RolesService) {}

  @Post('/')
  @Permission('PERMISSION_READ')
  @HttpCode(201)
  @UsePipes(new ValidationPipe())
  async create(@Req() req: any, @Body() body: CreateRolePermissionDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.createRoleWithPermissions(
      req,
      body,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/v2')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UsePipes(new ValidationPipe())
  async createV2(@Req() req: any, @Body() body: CreateRoleDto) {
    this.logger.log(
      `Entering ${this.createV2.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.createV2(req, body);
    this.logger.log(
      `Leaving ${this.createV2.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/v3')
  @Permission('PERMISSION_CREATE')
  @UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
  @HttpCode(201)
  async createV3(@Req() req: any, @Body() body: CreateRoleDto) {
    this.logger.log(
      `Entering ${this.createV3.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.createV3(req, body);
    this.logger.log(
      `Leaving ${this.createV3.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/v4')
  @Permission('PERMISSION_CREATE')
  @UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
  @HttpCode(201)
  async createV4(@Req() req: any, @Body() body: CreateRoleV4Dto) {
    this.logger.log(
      `Entering ${this.createV4.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.createV4(req, body);
    this.logger.log(
      `Leaving ${this.createV4.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  // Note: if send root module without permission, set access role into root module
  @Post('/v5')
  @UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
  @HttpCode(201)
  async createV5(@Req() req: any, @Body() body: CreateRoleV5Dto) {
    this.logger.log(
      `Entering ${this.createV5.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.createV5(req, body);
    this.logger.log(
      `Leaving ${this.createV5.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v1/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.roleService.get(req, Number(id));
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/v2/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getV2(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getV2.name} with id: ${id}`);
    const response = await this.roleService.getV2(req, Number(id));
    this.logger.log(
      `Leaving ${this.getV2.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v3/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getV3(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getV3.name} with id: ${id}`);
    const response = await this.roleService.getV3(req, Number(id));
    this.logger.log(
      `Leaving ${this.getV3.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v4/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getV4(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getV4.name} with id: ${id}`);
    const response = await this.roleService.getV4(req, Number(id));
    this.logger.log(
      `Leaving ${this.getV4.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortRoleDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.roleService.processGetList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v2')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListV2(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortRoleDto,
  ) {
    this.logger.log(
      `Entering ${this.getListV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.roleService.processGetListV2(
      req,
      paginationData,
      searchAndSortData,
    );
    this.logger.log(
      `Leaving ${this.getListV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v3')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async getListV3(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortRoleDto,
  ) {
    this.logger.log(
      `Entering ${this.getListV3.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.roleService.getListV3(
      req,
      paginationData,
      searchAndSortData,
    );
    this.logger.log(
      `Leaving ${this.getListV3.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v4')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async getListV4(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortRoleDto,
  ) {
    const response = await this.roleService.getListV4(
      req,
      paginationData,
      searchAndSortData,
    );
    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdateRolePermissionDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.update(req, Number(id), body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/v2/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateV2(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdateRoleDto,
  ) {
    this.logger.log(
      `Entering ${this.updateV2.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.updateV2(req, Number(id), body);
    this.logger.log(
      `Leaving ${this.updateV2.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/v3/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateV3(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: CreateRoleV4Dto,
  ) {
    this.logger.log(
      `Entering ${this.updateV3.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.updateV3(req, Number(id), body);
    this.logger.log(
      `Leaving ${this.updateV3.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/v4/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateV4(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: CreateRoleV5Dto,
  ) {
    this.logger.log(
      `Entering ${this.updateV4.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.roleService.updateV4(req, Number(id), body);
    this.logger.log(
      `Leaving ${this.updateV4.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.roleService.delete(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('v2/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteV2(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.deleteV2.name} with id: ${id}`);
    const response = await this.roleService.deleteV2(req, Number(id));
    this.logger.log(
      `Leaving ${this.deleteV2.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/portal')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListPortal(@Req() req: any, @Query('module_id') module_id: string) {
    this.logger.log(
      `Entering ${this.getListPortal.name} with module_id: ${module_id}`,
    );
    const response = await this.roleService.getListPortal(req, module_id);
    this.logger.log(
      `Leaving ${this.getListPortal.name} with module_id: ${module_id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/modules')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async listModules(@Req() req: any) {
    this.logger.log(`Entering ${this.listModules.name}`);
    const response = await this.roleService.getListModule(req);
    this.logger.log(
      `Leaving ${this.listModules.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/modules/v2')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async listModulesV2(@Req() req: any) {
    this.logger.log(`Entering ${this.listModulesV2.name}`);
    const response = await this.roleService.getListModuleV2(req);
    this.logger.log(
      `Leaving ${this.listModulesV2.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/atasan')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async atasanRole(@Req() req: any, @Query('tipe_role') tipe_role: string) {
    this.logger.log(
      `Entering ${this.atasanRole.name} with tipe_role: ${tipe_role}`,
    );
    const response = await this.roleService.getListAtasanRole(req, tipe_role);
    this.logger.log(
      `Leaving ${this.atasanRole.name} with tipe_role: ${tipe_role} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/bawahan')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async bawahanRole(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortRoleDto,
  ) {
    this.logger.log(`Entering ${this.bawahanRole.name}`);
    const response = await this.roleService.getListBawahanRole(req, paginationData, searchAndSortData);
    this.logger.log(
      `Leaving ${this.bawahanRole.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
