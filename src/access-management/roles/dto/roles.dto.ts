import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber, IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { SearchAndSortDTO } from 'src/core/dtos';

export class CreateRolePermissionDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  @IsNotEmpty()
  bagian_id: number;

  @IsBoolean()
  @IsNotEmpty()
  is_admin: boolean;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  permissions: number[];

  @IsNumber()
  @IsNotEmpty()
  satuan_id: number;

  @IsNumber()
  role_type_id: number;
}

export class UpdateRolePermissionDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  @IsNotEmpty()
  bagian_id: number;

  @IsBoolean()
  @IsNotEmpty()
  is_admin: boolean;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  permissions: number[];

  @IsNumber()
  @IsOptional()
  satuan_id?: number;

  @IsNumber()
  role_type_id: number;
}

export class SearchAndSortRoleDto extends SearchAndSortDTO {
  @IsString()
  @IsOptional()
  type?: string;
}

export class CreateRoleDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  role_tipe_id: number;

  @IsOptional()
  @IsNumber()
  atasan_id?: number;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  modules: number[];

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  permissions: number[];

  @IsOptional()
  @IsNumber()
  level_id?: number;
}

export class UpdateRoleDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  role_tipe_id: number;

  @IsNumber()
  atasan_id: number;

  @IsNumber()
  level_id: number;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  modules: number[];

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  permissions: number[];
}

export class CreateRoleDataV4Dto {
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  module_id: number;

  @IsArray()
  @Transform(({ value }) => value.map((v: string) => parseInt(v, 10)))
  @IsNumber({}, { each: true })
  permission_id: number[];

  @IsArray()
  @Transform(({ value }) => value.map((v: string) => parseInt(v, 10)))
  @IsNumber({}, { each: true })
  portal_id: number[];
}

export class CreateRoleV4Dto {
  @IsString()
  nama: string;

  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  role_tipe_id: number;

  @IsOptional()
  @IsNumber()
  level_id?: number;

  @Transform(({ value }) => (value !== null ? parseInt(value, 10) : null))
  @IsNumber()
  @IsOptional()
  atasan_id?: number | null;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRoleDataV4Dto)
  data: CreateRoleDataV4Dto[];
}

export class CreateRoleV5Dto {
  @IsString()
  nama: string;

  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  role_tipe_id: number;

  @IsOptional()
  @IsNumber()
  level_id: number | null;

  @IsNotEmpty()
  @IsBoolean()
  is_fungsi: boolean;

  @Transform(({ value }) => (value !== null ? parseInt(value, 10) : null))
  @IsNumber()
  @IsOptional()
  atasan_id?: number | null;

  @IsNotEmpty()
  @IsObject()
  @ValidateNested({ each: true })
  data: Record<string, number[]>;
}
