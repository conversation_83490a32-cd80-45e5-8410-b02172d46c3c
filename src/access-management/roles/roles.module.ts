import { forwardRef, Module } from '@nestjs/common';
import { RolesService } from './service/roles.service';
import { RolesController } from './controller/roles.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { RolesRepository } from './repository/roles.repository';
import { RolesValidator } from '../../core/validator/roles.validator';
import { UsersModule } from '../users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [RolesController],
  providers: [RolesService, RolesRepository, RolesValidator],
  exports: [RolesRepository],
})
export class RolesModule {}
