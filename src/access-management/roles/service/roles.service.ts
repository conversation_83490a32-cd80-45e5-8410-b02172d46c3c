import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateRoleDto,
  CreateRolePermissionDto,
  CreateRoleV4Dto,
  CreateRoleV5Dto,
  SearchAndSortRoleDto,
  UpdateRoleDto,
  UpdateRolePermissionDto,
} from '../dto/roles.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { RolesRepository } from '../repository/roles.repository';
import { RolesValidator } from '../../../core/validator/roles.validator';
import {
  convertRoleAccess,
  convertRoleAccessV2,
  convertToFormatCreate,
} from '../../../core/utils/roles.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { IModuleV2 } from '../../../core/interfaces/roles.interface';
import * as moment from 'moment/moment';

@Injectable()
export class RolesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly rolesValidator: RolesValidator,
    private readonly rolesRepository: RolesRepository,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async createRoleWithPermissions(req: any, body: CreateRolePermissionDto) {
    const { nama, bagian_id, permissions, satuan_id, role_type_id } = body;

    if (role_type_id == null)
      throw new BadRequestException('role type cannot be null');

    await this.rolesRepository.validateNotFoundEntity(
      'role_type',
      {
        id: role_type_id,
        deleted_at: null,
      },
      'Role type not found',
    );

    const role = await this.prisma.role.findFirst({
      where: {
        nama: nama,
        deleted_at: null,
      },
    });

    if (role) throw new BadRequestException('Role name already exists');

    await this.rolesRepository.validateNotFoundEntity(
      'role_type',
      {
        id: bagian_id,
        deleted_at: null,
      },
      `Bagian with ID ${bagian_id} not found`,
    );

    await this.rolesRepository.validateNotFoundEntity(
      'role_type',
      {
        id: satuan_id,
        deleted_at: null,
      },
      'Satuan not found',
    );
    const queryResult = await this.prisma.$transaction(async (tx) => {
      const role = await tx.role.create({
        data: {
          nama,
          bagian_id,
          is_admin: false,
          satuan_id,
          role_tipe_id: role_type_id,
        },
      });

      if (permissions?.length) {
        await tx.role_permission.createMany({
          data: permissions.map((permissionId) => ({
            permission_id: permissionId,
            role_id: role.id,
          })),
        });
      }

      return role;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const queryResult = await this.prisma.role.findFirst({
      where: {
        id: id,
        deleted_at: null,
      },
      select: {
        id: true,
        nama: true,
        is_admin: true,
        satuan: {
          select: {
            id: true,
            nama: true,
          },
        },
        bagian: {
          select: {
            id: true,
            nama: true,
          },
        },
        role_permission: {
          select: {
            id: true,
            permission: {
              select: {
                id: true,
                nama: true,
                desc: true,
              },
            },
          },
        },
        role_tipe: {
          select: {
            id: true,
            nama: true,
          },
        },
      },
    });

    if (!queryResult) throw new NotFoundException(`Role ID ${id} not found`);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      id: { field: 'id', type: 'bigint' },
      nama: { field: 'nama', type: 'string' },
      bagian_nama: { field: 'bagian.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, roles] = await this.prisma.$transaction([
      this.prisma.role.count({
        where: where,
      }),
      this.prisma.role.findMany({
        select: {
          id: true,
          nama: true,
          is_admin: true,
          created_at: true,
          satuan: {
            select: {
              id: true,
              nama: true,
            },
          },
          bagian: {
            select: {
              id: true,
              nama: true,
            },
          },
          role_permission: {
            select: {
              id: true,
              permission: {
                select: {
                  id: true,
                  nama: true,
                  desc: true,
                },
              },
            },
          },
          role_tipe: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const restructureRole = roles.map((item) => {
      return {
        id: item.id,
        nama: item.nama,
        is_admin: false,
        bagian: item.bagian,
        created_at: item.created_at,
        permission: item.role_permission.map(
          (permission) => permission.permission,
        ),
        role_tipe_id: item.role_tipe.id,
        role_tipe: item.role_tipe.nama,
        satuan: item.satuan,
      };
    });

    return { roles: restructureRole, page, totalPage, totalData };
  }

  async getListAdmin(req: any) {
    const { user } = req;
    return await this.prisma.role.findMany({
      where: {
        deleted_at: null,
        bagian: {
          id: { not: user.bagian_id },
          nama: { not: user.bagian_name, mode: 'insensitive' },
        },
        role_tipe: {
          nama: { equals: 'admin', mode: 'insensitive' },
        },
      },
      select: {
        id: true,
        nama: true,
      },
      orderBy: { nama: 'desc' },
    });
  }

  async getListByBagian(req: any) {
    const { user } = req;
    const where = {
      deleted_at: null,
      bagian: { id: user.bagian_id, deleted_at: null },
    };

    if (
      user.level_name.toLowerCase() === 'superadmin' &&
      user.bagian_name.toLowerCase() === 'baginfopers'
    ) {
      where['role_tipe'] = {
        nama: { equals: 'admin', mode: 'insensitive' },
      };
    } else {
      where['role_tipe'] = { nama: { not: 'admin', mode: 'insensitive' } };
    }

    return await this.prisma.role.findMany({
      where: where,
      select: {
        id: true,
        nama: true,
      },
      orderBy: { nama: 'asc' },
    });
  }

  async update(req: any, id: number, body: UpdateRolePermissionDto) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const { nama, bagian_id, permissions, satuan_id, role_type_id } = body;

    await this.rolesRepository.validateNotFoundEntity(
      'role',
      {
        id: id,
        deleted_at: null,
      },
      `Role ID ${id} not found`,
    );

    const roleName = await this.prisma.role.findFirst({
      where: {
        nama: nama,
        deleted_at: null,
        id: {
          not: {
            in: [id],
          },
        },
      },
    });

    if (roleName) throw new BadRequestException('Role name already exists');

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const updatedRole = await tx.role.update({
        where: { id },
        data: {
          nama,
          bagian_id,
          is_admin: false,
          satuan_id,
          role_tipe_id: role_type_id,
        },
      });

      await tx.role_permission.deleteMany({
        where: { role_id: id },
      });

      if (permissions?.length) {
        await tx.role_permission.createMany({
          data: permissions.map((permissionId) => ({
            permission_id: permissionId,
            role_id: id,
          })),
        });
      }

      return updatedRole;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateV2(req: any, id: number, body: UpdateRoleDto) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const { modules, permissions, atasan_id, level_id, nama, role_tipe_id } =
      body;

    await this.rolesRepository.validateNotFoundEntity(
      'role',
      {
        id: id,
        deleted_at: null,
      },
      `Role ID ${id} not found`,
    );

    const roleName = await this.prisma.role.findFirst({
      where: {
        nama: nama,
        deleted_at: null,
        id: {
          not: {
            in: [id],
          },
        },
      },
    });

    if (roleName) throw new BadRequestException('Role name already exists');

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const updateRole = await tx.role.update({
        where: { id },
        data: {
          nama,
          role_tipe_id,
          atasan_id,
          level_id,
          updated_at: new Date(),
        },
      });

      if (permissions?.length) {
        await tx.role_permission.deleteMany({
          where: { role_id: updateRole.id },
        });

        await tx.role_permission.createMany({
          data: permissions.map((permissionId) => ({
            permission_id: permissionId,
            role_id: updateRole.id,
          })),
        });
      }

      if (modules?.length) {
        await tx.role_module.deleteMany({
          where: { role_id: updateRole.id },
        });

        await tx.role_module.createMany({
          data: modules.map((moduleId) => ({
            module_id: moduleId,
            role_id: updateRole.id,
          })),
        });
      }

      return updateRole;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateV3(req: any, id: number, body: CreateRoleV4Dto) {
    const { user } = req;
    const { nama, role_tipe_id, atasan_id, level_id, data } = body;

    const existingRole = await this.rolesRepository.getRoleAccess(id);
    if (!existingRole.length)
      throw new NotFoundException(`Role ID ${id} not found`);

    const { validatedDataLs } = await this.rolesValidator.validateRoleAccess(
      true,
      nama,
      role_tipe_id,
      atasan_id,
      level_id,
      data,
      user,
    );

    const queryResult = await this.rolesRepository.updateRoleAccess(
      id,
      nama,
      atasan_id,
      level_id,
      role_tipe_id,
      convertToFormatCreate(existingRole),
      validatedDataLs,
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateV4(req: any, id: number, body: CreateRoleV5Dto) {
    const currentDate = moment().toISOString();

    const { nama, role_tipe_id, atasan_id, level_id, is_fungsi, data } = body || {};

    const [_, validateRoleType] = await Promise.all([
      this.rolesValidator.checkRoleOrThrowError(nama, id),
      this.rolesValidator.validateRoleTipeOrThrowError(role_tipe_id),
    ]);

    if (!validateRoleType.isAdmin) {
      await Promise.all([
        this.rolesValidator.validateRequestForAtasanId(true, atasan_id),
        this.rolesValidator.validateRequestForLevelId(true, level_id),
      ]);
    }

    const allModules = await this.rolesRepository.queryRecursiveModules();
    const currentRole = await this.rolesRepository.queryRecursiveRoles({}, id);

    const allModulePermissions: Record<string, number[]> = {};
    this.mapModulePermissions(allModules, allModulePermissions);

    const currentModulePermissions: Record<string, number[]> = {};
    this.mapModulePermissions(
      convertRoleAccessV2(currentRole)[0].modules,
      currentModulePermissions,
    );

    const { toInsert, toDelete } = this.getInsertDeleteSets(
      currentModulePermissions,
      allModulePermissions,
      data,
    );

    return this.prisma.$transaction(async (tx) => {
      const roleUpdate = await tx.role.update({
        where: { id },
        data: {
          nama,
          atasan_id,
          level_id,
          role_tipe_id,
          is_fungsi,
        },
      });

      if (toDelete.length) {
        await tx.role_access.updateMany({
          where: {
            OR: toDelete.map((item) => {
              const [moduleId, permissionId] = item.split('-');
              return {
                role_id: id,
                module_id: Number(moduleId),
                permission_id: Number(permissionId),
              };
            }),
          },
          data: {
            deleted_at: currentDate,
            updated_at: currentDate,
          },
        });
      }

      if (toInsert.length) {
        await tx.role_access.createMany({
          data: toInsert.map((item) => {
            const [moduleId, permissionId] = item.split('-');
            return {
              module_id: Number(moduleId),
              permission_id: Number(permissionId),
              role_id: id,
            };
          }),
        });
      }

      return roleUpdate;
    });
  }

  async validateDeleteRole(id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    await this.rolesRepository.validateNotFoundEntity(
      'role',
      {
        id: id,
        deleted_at: null,
      },
      `Role ID ${id} not found`,
    );

    const roleAtasan = await this.prisma.role.findFirst({
      where: { atasan_id: id, deleted_at: null },
    });

    if (roleAtasan)
      throw new BadRequestException(
        'Role cannot be deleted because it still has operators/subordinates',
      );
  }

  async delete(req: any, id: number) {
    await this.validateDeleteRole(id);

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const data = { deleted_at: new Date() };
      await tx.role_access.updateMany({
        where: { role_id: id },
        data,
      });

      return await tx.role.update({
        where: { id: id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteV2(req: any, id: number) {
    await this.validateDeleteRole(id);

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const data = { deleted_at: new Date() };
      await tx.role_access.updateMany({
        where: { role_id: id },
        data,
      });

      return await tx.role.update({
        where: { id: id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createV2(req: any, body: CreateRoleDto) {
    const { level_id, modules, nama, permissions, role_tipe_id, atasan_id } =
      body;
    const queryResult = await this.prisma.$transaction(async (tx) => {
      await this.rolesRepository.validateNotFoundEntity(
        'role',
        { id: atasan_id },
        'Atasan role not found',
      );

      await this.rolesRepository.validateNotFoundEntity(
        'role_tipe',
        {
          id: role_tipe_id,
          deleted_at: null,
        },
        'Role type not found',
      );

      const roleName = await this.prisma.role.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (roleName) throw new BadRequestException('Role name already exists');

      const createRole = await tx.role.create({
        data: {
          nama,
          atasan_id,
          level_id,
          role_tipe_id,
        },
      });

      if (permissions?.length) {
        await tx.role_permission.createMany({
          data: permissions.map((permissionId) => ({
            permission_id: permissionId,
            role_id: createRole.id,
          })),
        });
      }

      const additionalModules = [
        5, 6, 7, 9, 17, 19, 21, 22, 23, 24, 25, 27, 28,
      ];
      const distinctModules = Array.from(
        new Set([...modules, ...additionalModules]),
      );

      const roleModules = distinctModules.map((moduleId) => ({
        module_id: moduleId,
        role_id: createRole.id,
      }));

      await tx.role_module.createMany({ data: roleModules });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createV3(req: any, body: CreateRoleDto) {
    const { user } = req;
    const { level_id, modules, nama, permissions, role_tipe_id, atasan_id } =
      body;

    const { isAdmin, level } = await this.rolesValidator.validateRoleV3(
      nama,
      atasan_id,
      role_tipe_id,
      level_id,
      user,
    );
    const queryResult = await this.saveRoleV3(
      nama,
      atasan_id,
      role_tipe_id,
      modules,
      permissions,
      user,
      level,
      isAdmin,
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createV4(req: any, body: CreateRoleV4Dto) {
    const { user } = req;
    const { nama, role_tipe_id, atasan_id, level_id, data } = body;

    const {
      validatedNama,
      validatedAtasanId,
      validatedLevelId,
      validatedRoleTipeId,
      validatedDataLs,
    } = await this.rolesValidator.validateRoleAccess(
      false,
      nama,
      role_tipe_id,
      atasan_id,
      level_id,
      data,
      user,
    );

    const result = await this.rolesRepository.createRoleAccess(
      validatedNama,
      validatedAtasanId,
      validatedLevelId,
      validatedRoleTipeId,
      validatedDataLs,
    );

    const queryResult = convertRoleAccess(result);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createV5(req: any, body: CreateRoleV5Dto) {
    const { nama, role_tipe_id, atasan_id, level_id, is_fungsi, data } = body || {};

    const [, validateRoleType] = await Promise.all([
      this.rolesValidator.checkRoleOrThrowError(nama),
      this.rolesValidator.validateRoleTipeOrThrowError(role_tipe_id),
    ]);

    if (!validateRoleType.isAdmin) {
      await Promise.all([
        this.rolesValidator.validateRequestForAtasanId(true, atasan_id),
        this.rolesValidator.validateRequestForLevelId(true, level_id),
      ]);
    }

    const allModules = await this.rolesRepository.queryRecursiveModules();
    const allModulePermissions: Record<string, number[]> = {};
    this.mapModulePermissions(allModules, allModulePermissions);

    const payloadRoleAccess = this.getPayloadRoleAccess(
      allModulePermissions,
      data,
    );

    return this.prisma.$transaction(async (tx) => {
      const role = await tx.role.create({
        data: { nama, atasan_id, level_id, role_tipe_id, is_fungsi },
      });

      await tx.role_access.createMany({
        data: payloadRoleAccess.map((item) => {
          const [module_id, permission_id] = item.split('-');
          return {
            module_id: Number(module_id),
            permission_id: Number(permission_id),
            role_id: role.id,
          };
        }),
      });

      return role;
    });
  }

  async processGetList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortRoleDto,
  ) {
    let queryResult = {};
    if (searchandsortData.type?.toLowerCase() === 'bagian') {
      queryResult['data'] = await this.getListByBagian(req);
    } else if (searchandsortData.type?.toLowerCase() === 'admin') {
      queryResult['data'] = await this.getListAdmin(req);
    } else {
      const { roles, page, totalPage, totalData } = await this.getList(
        paginationData,
        searchandsortData,
      );

      queryResult = {
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: roles,
      };
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get roles',
      ...queryResult,
    };
  }

  async processGetListV2(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    let queryResult = {};
    if (searchAndSortData.type?.toLowerCase() === 'bagian') {
      queryResult['data'] = await this.getListByBagianV2(req);
    } else if (searchAndSortData.type?.toLowerCase() === 'admin') {
      queryResult['data'] = await this.getListAdminV2(req);
    } else {
      const { roles, page, totalPage, totalData } = await this.getListV2(
        paginationData,
        searchAndSortData,
      );
      queryResult = {
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: roles,
      };
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      ...queryResult,
    };
  }

  async getListV2(
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      id: { field: 'id', type: 'bigint' },
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    where['NOT'] = { atasan_id: null };

    const [totalData, roles] = await this.prisma.$transaction([
      this.prisma.role.count({
        where: where,
      }),
      this.prisma.role.findMany({
        select: {
          id: true,
          nama: true,
          created_at: true,
          role_permission: {
            select: {
              id: true,
              permission: {
                select: {
                  id: true,
                  nama: true,
                  desc: true,
                },
              },
            },
          },
          role_module: {
            select: {
              id: true,
              module: {
                select: {
                  id: true,
                  nama: true,
                  name_long: true,
                },
              },
            },
            where: {
              deleted_at: null,
              module: {
                deleted_at: null,
              },
            },
          },
          role_tipe: {
            select: {
              id: true,
              nama: true,
            },
          },
          level: {
            select: {
              id: true,
              nama: true,
            },
          },
          atasan_role: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const restructureRole = roles.map((item) => {
      return {
        id: item.id,
        nama: item.nama,
        created_at: item.created_at,
        permission: item.role_permission.map((permission) => ({
          nama: permission.permission.nama,
          desc: permission.permission.desc,
        })),
        module: item.role_module.map((roleModule) => ({
          name: roleModule.module.nama,
          name_long: roleModule.module.name_long,
        })),
        role_tipe_id: item.role_tipe.id,
        role_tipe: item.role_tipe.nama,
        level: item.level,
        atasan_role: item.atasan_role,
      };
    });

    return { roles: restructureRole, page, totalPage, totalData };
  }

  async getListV3(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
      // modules: { field: 'role_access.modules.nama', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    where['NOT'] = { atasan_id: null };

    const [totalData, roles] = await this.prisma.$transaction([
      this.prisma.role.count({ where }),
      this.prisma.role.findMany({
        where,
        select: {
          role_access: {
            where: { deleted_at: null },
            select: {
              role: {
                select: {
                  id: true,
                  nama: true,
                  role_tipe: { select: { id: true, nama: true } },
                  atasan_role: { select: { id: true, nama: true } },
                  level: { select: { id: true, nama: true } },
                  created_at: true,
                },
              },
              modules: {
                select: { id: true, nama: true, name_long: true },
              },
              permission: {
                select: { id: true, nama: true, desc: true },
              },
              portal: {
                select: { id: true, nama: true, desc: true },
              },
            },
          },
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = roles.map((role) =>
      convertRoleAccess(role.role_access),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListV4(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const { limit, page } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      role_name: { field: 'pu|role_name', type: 'string' },
      role_tipe: { field: 'pu|role_tipe::text', type: 'string' },
      level: { field: 'pu|level::text', type: 'string' },
      created_at: { field: 'pu|created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const roles = await this.rolesRepository.queryRecursiveRoles({
      take: limit,
      skip: limit * (page - 1),
      where,
      orderQuery: orderBy,
    });

    const queryResult = convertRoleAccessV2(roles);
    const totalData = queryResult?.length ? Number(roles[0].total_data) : 0;
    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getV2(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const queryResult = await this.prisma.role.findFirst({
      where: {
        id: id,
        deleted_at: null,
      },
      select: {
        id: true,
        nama: true,
        role_permission: {
          select: {
            permission: {
              select: {
                id: true,
                nama: true,
                desc: true,
              },
            },
          },
        },
        role_module: {
          select: {
            module: {
              select: {
                id: true,
                nama: true,
                name_long: true,
              },
            },
          },
          where: {
            deleted_at: null,
            module: {
              deleted_at: null,
            },
          },
        },
        role_tipe: {
          select: {
            id: true,
            nama: true,
          },
        },
        level: {
          select: {
            id: true,
            nama: true,
          },
        },
        atasan_role: {
          select: {
            id: true,
            nama: true,
          },
        },
      },
    });

    if (!queryResult) throw new NotFoundException(`Role ID ${id} not found`);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getV3(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const rolesAccess = await this.rolesRepository.getRoleAccess(id);
    if (!rolesAccess.length)
      throw new NotFoundException(`Role ID ${id} not found`);

    const queryResult = convertRoleAccess(rolesAccess);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getV4(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const roleAccess = await this.rolesRepository.queryRecursiveRoles({}, id);

    const queryResult = convertRoleAccessV2(roleAccess)[0];

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListByBagianV2(req: any) {
    const { user } = req;
    const where = {
      deleted_at: null,
      atasan_role: { id: user.role_id },
    };

    return await this.prisma.role.findMany({
      where: where,
      select: {
        id: true,
        nama: true,
      },
      orderBy: { nama: 'asc' },
    });
  }

  async getListAdminV2(req: any) {
    const { user } = req;
    return await this.prisma.role.findMany({
      where: {
        deleted_at: null,
        atasan_id: user.role_id,
        role_tipe: {
          nama: { equals: 'admin', mode: 'insensitive' },
        },
      },
      select: {
        id: true,
        nama: true,
      },
      orderBy: { nama: 'asc' },
    });
  }

  async getListModule(req: any) {
    const queryResult = await this.prisma.modules.findMany({
      where: {
        deleted_at: null,
        NOT: { id: { in: [2] } },
      },
      select: {
        id: true,
        nama: true,
        name_long: true,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListModuleV2(req: any) {
    const queryResult = await this.rolesRepository.queryRecursiveModules();

    const results: IModuleV2[] = [];

    for (const item of queryResult) {
      if (results.some((module) => module.id === item.id)) continue;

      const module: IModuleV2 = {
        id: item.id,
        nama: item.nama,
        name_long: item.name_long,
        chain_parent: item.chain_parent,
        permissions: item.permissions,
        modules: [],
      };

      if (item.parent_id) {
        const parent = this.findParentModule(results, item.parent_id);
        if (parent) {
          parent.modules.push(module);
          continue;
        }

        results.push(module);
        continue;
      }

      results.push(module);
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async getListAtasanRole(req: any, roleType: string) {
    const queryResult = await this.prisma.role.findMany({
      where: {
        deleted_at: null,
        level_id: null,
        role_tipe_id: 1,
      },
      select: {
        id: true,
        nama: true,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListBawahanRole(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const { user } = req;
    const isSuperAdmin =
      user.roles?.[0].level?.nama.toLowerCase() === 'superadmin';

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
    };

    const { where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const queryResult = await this.prisma.role.findMany({
      where: {
        ...where,
        atasan_id: {
          in: isSuperAdmin ? undefined : user.roles.map((item: any) => item.id),
        },
        deleted_at: null,
      },
      select: {
        id: true,
        nama: true,
        atasan_id: true,
      },
      orderBy: [{ nama: 'asc' }],
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListPortal(req: any, module_id?: string) {
    if (module_id === undefined || module_id === null)
      return this.rolesRepository.getListPortal();

    const moduleId = Number(module_id);
    if (isNaN(moduleId) || moduleId <= 0)
      throw new BadRequestException(`Module ID tidak sesuai`);

    const queryResult = this.rolesRepository.getListPortal({
      module_id: moduleId,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ROLES_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ROLES_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  private findParentModule(
    modules: IModuleV2[],
    parentId: number,
  ): IModuleV2 | null {
    for (const module of modules) {
      if (module.id === parentId) return module;
      const found = this.findParentModule(module.modules, parentId);
      if (found) return found;
    }
    return null;
  }

  private async saveRoleV3(
    nama: string,
    atasanId: number,
    roleTipeId: number,
    moduleIdLs: number[],
    permissionIdLs: number[],
    user: any,
    level: any,
    isAdmin: boolean,
  ) {
    return await this.prisma.$transaction(async (tx) => {
      const createRole = await tx.role.create({
        data: {
          nama,
          atasan_id: isAdmin ? user.role_id : atasanId,
          level_id: isAdmin ? null : level.id,
          role_tipe_id: roleTipeId,
        },
      });

      await tx.role_permission.createMany({
        data: permissionIdLs.map((permissionId) => ({
          permission_id: permissionId,
          role_id: createRole.id,
        })),
      });

      const distinctModules = Array.from(new Set([...moduleIdLs]));
      const roleModules = distinctModules.map((moduleId) => ({
        module_id: moduleId,
        role_id: createRole.id,
      }));

      await tx.role_module.createMany({ data: roleModules });
    });
  }

  private mapModulePermissions(
    modules: any[],
    currentModules: Record<string, number[]>,
  ) {
    return modules.forEach((module) => {
      currentModules[module.id] = module.permissions.map((perm) => perm.id);

      if (module.modules?.length) {
        this.mapModulePermissions(module.modules, currentModules);
      }
    });
  }

  private getPayloadRoleAccess(
    mappingModules: Record<string, number[]>,
    data: Record<string, number[]>,
  ): string[] {
    const set = new Set<string>();

    Object.entries(data).forEach(([moduleIds, permissionIds]) => {
      const [moduleId] = moduleIds.split(' - ');
      this.validateModulePermissions(mappingModules, moduleId, permissionIds);

      permissionIds.forEach((permissionId) => {
        set.add(`${moduleId}-${permissionId}`);
      });

      set.add(`${moduleId}-null`);
    });

    return Array.from(set);
  }

  private getInsertDeleteSets(
    currentModules: Record<string, number[]>,
    mappingModules: Record<string, number[]>,
    data: Record<string, number[]>,
  ): { toInsert: string[]; toDelete: string[] } {
    const newSet = new Set<string>();

    Object.entries(data).forEach(([moduleIds, permissionIds]) => {
      const [moduleId] = moduleIds.split(' - ');
      this.validateModulePermissions(mappingModules, moduleId, permissionIds);

      permissionIds.forEach((permissionId) => {
        newSet.add(`${moduleId}-${permissionId}`);
      });

      newSet.add(`${moduleId}-null`);
    });

    const currentSet = new Set<string>();
    Object.entries(currentModules).forEach(([moduleId, permissionIds]) => {
      permissionIds.forEach((permissionId) => {
        currentSet.add(`${moduleId}-${permissionId}`);
      });
      currentSet.add(`${moduleId}-null`);
    });

    const toInsert = Array.from(newSet).filter((x) => !currentSet.has(x));
    const toDelete = Array.from(currentSet).filter((x) => !newSet.has(x));

    return { toInsert, toDelete };
  }

  private validateModulePermissions(
    mappingModules: Record<string, number[]>,
    moduleId: string,
    permissionIds: number[],
  ): void {
    if (!mappingModules[moduleId]) {
      throw new BadRequestException(
        `Modul dengan ID ${moduleId} tidak ditemukan`,
      );
    }

    permissionIds.forEach((pid) => {
      if (!mappingModules[moduleId].includes(pid)) {
        throw new BadRequestException(
          `Permission dengan ID ${pid} tidak ditemukan pada modul dengan ID ${moduleId}`,
        );
      }
    });
  }
}
