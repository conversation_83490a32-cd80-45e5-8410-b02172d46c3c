import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import {
  mapValidatedDataForGeneralModule,
  mapValidatedDataForPortalModule,
} from '../../../core/utils/roles.utils';
import {
  IRecursiveModule,
  IRecursiveRoles,
  IRoleAccess,
} from '../../../core/interfaces/roles.interface';
import * as moment from 'moment';
import { IOrderBy, IWhere } from '../../../core/interfaces/db.interface';
import { Prisma } from '@prisma/client';

@Injectable()
export class RolesRepository {
  constructor(private readonly prismaService: PrismaService) {}

  async createRoleAccess(
    nama: string,
    atasan_id: number,
    level_id: number,
    role_tipe_id: number,
    validatedDataLs: {
      validatedAtasan: number;
      validatedModuleId: number;
      validatedPortalIdLs: number[];
      validatedLevel: number;
      validatedPermissionIdLs: number[];
    }[],
  ): Promise<IRoleAccess[]> {
    const filteredValidatedPortalModule = validatedDataLs.filter(
      (validatedData) =>
        validatedData.validatedPortalIdLs &&
        validatedData.validatedPortalIdLs.length > 0,
    );
    const filteredValidatedGeneralModuleLs = validatedDataLs.filter(
      (validatedData) =>
        validatedData.validatedPermissionIdLs &&
        validatedData.validatedPermissionIdLs.length > 0,
    );

    const create = await this.prismaService.$transaction(async (tx) => {
      const createdRole = await tx.role.create({
        data: {
          nama,
          atasan_id,
          level_id,
          role_tipe_id,
        },
      });

      const mapRoleAccessForGeneralModule = mapValidatedDataForGeneralModule(
        filteredValidatedGeneralModuleLs,
        createdRole.id,
      );
      const mapRoleAccessForPortalModule = mapValidatedDataForPortalModule(
        filteredValidatedPortalModule,
        createdRole.id,
      );

      const mapValidatedData = [
        ...mapRoleAccessForGeneralModule,
        ...mapRoleAccessForPortalModule,
      ];
      await tx.role_access.createMany({
        data: mapValidatedData,
      });

      return createdRole;
    });

    return await this.getRoleAccess(create.id);
  }

  async updateRoleAccess(
    role_id: number,
    nama: string,
    atasan_id: number,
    level_id: number,
    role_tipe_id: number,
    existingData: any,
    validatedDataLs: {
      validatedAtasan: number;
      validatedModuleId: number;
      validatedPortalIdLs: number[];
      validatedLevel: number;
      validatedPermissionIdLs: number[];
    }[],
  ): Promise<any> {
    const currentDate = moment().toISOString();

    const filteredValidatedPortalModule = validatedDataLs.filter(
      (validatedData) =>
        validatedData.validatedPortalIdLs &&
        validatedData.validatedPortalIdLs.length > 0,
    );

    const filteredValidatedGeneralModuleLs = validatedDataLs.filter(
      (validatedData) =>
        validatedData.validatedPermissionIdLs &&
        validatedData.validatedPermissionIdLs.length > 0,
    );

    return await this.prismaService.$transaction(async (tx) => {
      const mapRoleAccessForGeneralModule = mapValidatedDataForGeneralModule(
        filteredValidatedGeneralModuleLs,
        role_id,
      );
      const mapRoleAccessForPortalModule = mapValidatedDataForPortalModule(
        filteredValidatedPortalModule,
        role_id,
      );

      const mapValidatedData = [
        ...mapRoleAccessForGeneralModule,
        ...mapRoleAccessForPortalModule,
      ];

      const existingSet = new Set(
        existingData.map(
          (item) =>
            `${item.role_id}-${item.module_id}-${item.permission_id}-${item.portal_id}`,
        ),
      );

      const newSet = new Set(
        mapValidatedData.map(
          (item) =>
            `${item.role_id}-${item.module_id}-${item.permission_id}-${item.portal_id}`,
        ),
      );

      const toDelete = existingData.filter(
        (item) =>
          !newSet.has(
            `${item.role_id}-${item.module_id}-${item.permission_id}-${item.portal_id}`,
          ),
      );

      const toInsert = mapValidatedData.filter(
        (item) =>
          !existingSet.has(
            `${item.role_id}-${item.module_id}-${item.permission_id}-${item.portal_id}`,
          ),
      );

      await tx.role.update({
        where: { id: role_id },
        data: {
          nama,
          atasan_id,
          level_id,
          role_tipe_id,
        },
      });

      if (toDelete.length > 0) {
        await tx.role_access.updateMany({
          where: {
            OR: toDelete.map((item) => ({
              role_id: item.role_id,
              module_id: item.module_id,
              permission_id: item.permission_id ?? null,
              portal_id: item.portal_id ?? null,
            })),
          },
          data: {
            deleted_at: currentDate,
            updated_at: currentDate,
          },
        });
      }

      if (toInsert.length > 0) {
        await tx.role_access.createMany({
          data: toInsert,
        });
      }
    });
  }

  async getRoleAccess(role_id: number) {
    return await this.prismaService.role_access.findMany({
      where: {
        role_id: role_id,
        deleted_at: null,
      },
      select: {
        role: {
          select: {
            id: true,
            nama: true,
            level: {
              select: {
                id: true,
                nama: true,
              },
            },
            atasan_role: {
              select: {
                id: true,
                nama: true,
              },
            },
            role_tipe: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
        },
        modules: {
          where: { deleted_at: null },
          select: {
            id: true,
            nama: true,
            name_long: true,
          },
        },
        permission: {
          where: { deleted_at: null },
          select: {
            id: true,
            nama: true,
            desc: true,
          },
        },
        portal: {
          where: { deleted_at: null },
          select: {
            id: true,
            nama: true,
          },
        },
      },
    });
  }

  async getListPortal(where?: any) {
    return await this.prismaService.portal.findMany({
      where: {
        ...where,
      },
      select: {
        id: true,
        nama: true,
        desc: true,
      },
      orderBy: {
        id: 'asc',
      },
    });
  }

  async queryRecursiveModules(): Promise<IRecursiveModule[]> {
    return await this.prismaService.$queryRaw<IRecursiveModule[]>`
      WITH RECURSIVE
        menu_tree AS (SELECT id,
                             nama,
                             name_long,
                             parent_id,
                             id::TEXT AS chain_parent,
                             1        AS level
                      FROM modules
                      WHERE parent_id IS NULL
                        AND deleted_at IS NULL

                      UNION ALL

                      SELECT m.id,
                             m.nama,
                             m.name_long,
                             m.parent_id,
                             m.id::TEXT || ' - ' || mt.chain_parent AS chain_parent,
                             mt.level + 1
                      FROM modules m
                             INNER JOIN menu_tree mt ON m.parent_id = mt.id
                      WHERE m.deleted_at IS NULL),
        permissions_ordered AS (SELECT *
                                FROM permission
                                WHERE deleted_at IS NULL
                                ORDER BY label ASC)
      SELECT mt.id        AS id,
             mt.nama      AS nama,
             mt.name_long AS name_long,
             mt.parent_id AS parent_id,
             mt.chain_parent,
             COALESCE(
                 JSON_AGG(
                 JSON_BUILD_OBJECT(
                   'id', p.id,
                   'nama', p.nama,
                   'label', p.label
                 )
                         ) FILTER (WHERE p.id IS NOT NULL), '[]'
             )            AS permissions
      FROM menu_tree mt
             LEFT JOIN permissions_ordered p ON p.module_id = mt.id
      GROUP BY mt.id, mt.nama, mt.name_long, mt.level, mt.parent_id, mt.chain_parent
      ORDER BY mt.level, mt.parent_id, mt.id;
    `;
  }

  async queryRecursiveRoles(
    params: {
      take?: number;
      skip?: number;
      where?: IWhere | null;
      orderQuery?: IOrderBy[];
    } = {},
    roleId?: number,
  ): Promise<IRecursiveRoles[]> {
    const { take = 10, skip = 0, where = null, orderQuery = [] } = params;
    let orderByClauses = '';
    let whereClauseRoleData = 'WHERE 1=1';

    if (orderQuery?.length) {
      const orderConditions = orderQuery.map((order) => {
        const [rawKey, sortDirection] = Object.entries(order)[0];
        const columnName =
          rawKey === 'id' ? 'pu.role_id' : rawKey.replace('|', '.');
        const direction =
          sortDirection.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
        return `${columnName} ${direction}`;
      });

      orderByClauses = orderConditions.length
        ? `ORDER BY ${orderConditions.join(', ')}`
        : 'ORDER BY pu.created_at DESC';
    }

    if (where && where['OR']?.length) {
      const conditions = where['OR'].map((condition) => {
        const [key, value] = Object.entries(condition)[0];
        const columnName = key.replace('|', '.');
        const contains = (value as any)?.contains || '';
        return `${columnName} ILIKE '%${contains}%'`;
      });

      whereClauseRoleData += ` AND (${conditions.join(' OR ')})`;
    }

    const whereClauseFiltered = roleId ? ` AND pu.role_id = ${roleId}` : '';

    const query = await this.prismaService.$queryRaw<IRecursiveRoles[]>`
      WITH RECURSIVE
        menu_tree AS (SELECT id, nama, name_long, parent_id, id::TEXT AS chain_parent, 1 AS level
                      FROM modules
                      WHERE parent_id IS NULL
                        AND deleted_at IS NULL
                      UNION ALL
                      SELECT m.id,
                             m.nama,
                             m.name_long,
                             m.parent_id,
                             m.id::TEXT || ' - ' || mt.chain_parent,
                             mt.level + 1
                      FROM modules m
                             INNER JOIN menu_tree mt ON m.parent_id = mt.id
                      WHERE m.deleted_at IS NULL),
        role_data AS (SELECT r.id                                                           AS role_id,
                             r.nama                                                         AS role_name,
                             r.created_at                                                   AS created_at,
                             CASE
                               WHEN r2.id IS NULL THEN NULL
                               ELSE JSON_BUILD_OBJECT('id', r2.id, 'nama', r2.nama, 'atasan_id',
                                                      r2.atasan_id) END                     AS atasan_role,
                             CASE
                               WHEN r.level_id IS NULL THEN NULL
                               ELSE JSON_BUILD_OBJECT('id', r.level_id, 'nama', l.nama) END AS level,
                             JSON_BUILD_OBJECT('id', r.role_tipe_id, 'nama', rt.nama)       AS role_tipe,
                             r.is_fungsi                                                    AS is_fungsi,
                             mt.name_long                                                   AS module_name_long,
                             mt.id                                                          AS module_id,
                             mt.nama                                                        AS module_name,
                             p.label                                                        AS permission_label,
                             p.id                                                           AS permission_id,
                             p.nama                                                         AS permission_name,
                             mt.chain_parent,
                             mt.level                                                       AS level_child
                      FROM role r
                             LEFT JOIN role_access ra ON ra.role_id = r.id
                             LEFT JOIN menu_tree mt ON ra.module_id = mt.id
                             LEFT JOIN permission p ON ra.permission_id = p.id
                             LEFT JOIN role_tipe rt ON r.role_tipe_id = rt.id
                             LEFT JOIN level l ON r.level_id = l.id
                             LEFT JOIN role r2 ON r.atasan_id = r2.id
                      WHERE ra.deleted_at IS NULL
                        AND p.deleted_at IS NULL
                        AND rt.deleted_at IS NULL
                        AND l.deleted_at IS NULL
                        AND r2.deleted_at IS NULL
                      ORDER BY r.created_at DESC),
        grouped_user AS (SELECT rd.role_id, rd.role_tipe::text, rd.role_name, rd.level::text, rd.created_at
                         FROM role_data rd
                                LEFT JOIN menu_tree mt ON rd.module_id = mt.id
                         GROUP BY rd.role_id, rd.role_tipe::text, rd.role_name, rd.level::text, rd.created_at),
        searched_user AS (SELECT pu.*
                          FROM grouped_user pu
                            ${Prisma.raw(whereClauseRoleData)} ${Prisma.raw(whereClauseFiltered)}),
        ordered_user AS (SELECT *
                         FROM searched_user pu
                           ${Prisma.raw(orderByClauses)}),
        paged_user AS (SELECT *
                       FROM ordered_user
                       LIMIT ${take} OFFSET ${skip}),
        distinct_roles AS (SELECT COUNT(DISTINCT role_id) AS total_data
                           FROM ordered_user)
      SELECT (SELECT total_data FROM distinct_roles) AS total_data,
             rd.*
      FROM role_data rd
             JOIN paged_user pu ON rd.role_id = pu.role_id
        ${Prisma.raw(orderByClauses)};
    `;

    return this.sortingData(query);
  }

  sortingData(data: IRecursiveRoles[]): IRecursiveRoles[] {
    const grouped = new Map<number, typeof data>();

    data.forEach((item) => {
      if (!grouped.has(item.role_id)) {
        grouped.set(item.role_id, []);
      }
      grouped.get(item.role_id)?.push(item);
    });

    return Array.from(grouped.values()).flatMap((group) =>
      group.sort((a, b) => {
        if (a.level_child !== b.level_child) {
          return a.level_child - b.level_child;
        }

        const labelA = a.permission_label ?? '';
        const labelB = b.permission_label ?? '';
        return labelA.localeCompare(labelB);
      }),
    );
  }

  async validateNotFoundEntity(tableName: string, where: any, message: string) {
    const entity = await this.prismaService[tableName].findFirst({ where });
    if (!entity) throw new NotFoundException(message);
    return entity;
  }

  async validateBadRequestEntity(
    tableName: string,
    where: any,
    message: string,
  ) {
    const entity = await this.prismaService[tableName].findFirst({ where });
    if (entity) throw new BadRequestException(message);
  }
}
