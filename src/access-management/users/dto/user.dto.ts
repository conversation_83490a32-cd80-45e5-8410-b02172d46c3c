import {
  ArrayMinSize,
  IsArray,
  IsIn,
  IsNotEmpty,
  IsNumber, IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { SearchAndSortDTO } from 'src/core/dtos';

export class CreateUserDto {
  @IsNumber()
  @IsNotEmpty()
  personel_id: number;

  @IsNumber()
  @IsNotEmpty()
  role_id: number;

  @IsNumber()
  @IsNotEmpty()
  level_id: number;

  @IsIn(['bagian_saya', 'bagian_lain'], {
    message: 'type must be "bagian_saya" or "bagian_lain"',
  })
  @ValidateIf((_, value) => value !== undefined) // Validate only if sort_desc is provided
  type: string;
}

export class UpdateUserDto {
  @IsNumber()
  @IsNotEmpty()
  role_id: number;

  @IsNumber()
  @IsNotEmpty()
  level_id: number;
}

export class SearchAndSortUserDto extends SearchAndSortDTO {
  @IsNumber()
  bagian?: number;

  @IsNumber()
  level?: number;
}

export class CreateUserV2Dto {
  @IsString()
  @IsNotEmpty()
  uid_personel: string;

  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  roles: number[];
}

export class UpdateUserV2Dto extends CreateUserV2Dto {}

export class BulkCreateUserDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsString()
  @IsNotEmpty()
  nrp: string;

  @IsNumber()
  @IsNotEmpty()
  role: number;

  @IsString()
  @IsOptional()
  desc?: string;
}
