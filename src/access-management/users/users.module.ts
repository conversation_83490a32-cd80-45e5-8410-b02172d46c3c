import { forwardRef, Module } from '@nestjs/common';
import { UsersService } from './service/users.service';
import { UsersController } from './controller/users.controller';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { ExcelModule } from '../../api-utils/excel/excel.module';
import { PermissionModule } from '../../reference-data/permission/permission.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { MinioService } from '../../api-utils/minio/service/minio.service';

@Module({
  imports: [
    PrismaModule,
    LogsActivityModule,
    forwardRef(() => ExcelModule),
    forwardRef(() => PermissionModule),
  ],
  controllers: [UsersController],
  providers: [UsersService, LogsActivityService, MinioService],
  exports: [UsersService],
})
export class UsersModule {}
