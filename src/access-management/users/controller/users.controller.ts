// src/users/users.controller.ts
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { UsersService } from '../service/users.service';
import { Permission } from 'src/core/decorators';
import {
  BulkCreateUserDto,
  CreateUserDto,
  CreateUserV2Dto,
  SearchAndSortUserDto,
  UpdateUserDto,
  UpdateUserV2Dto,
} from '../dto/user.dto';
import { PaginationDto } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PermissionGuard } from 'src/core/guards/permission-auth.guard';
import { Module } from 'src/core/decorators/module.decorator';
import { MODULES } from 'src/core/constants/module.constant';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { FieldValidatorPipe } from '../../../core/validator/field.validator';

@Controller('users')
@Module(MODULES.USER_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class UsersController {
  private readonly logger = new Logger(UsersController.name);

  constructor(private readonly usersService: UsersService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateUserDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.usersService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortUserDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    return await this.usersService.getListV2(
      req,
      paginationData,
      searchandsortData,
    );
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.usersService.get(req, Number(id));
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateUserDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.usersService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.usersService.deleteV2(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/v2/create')
  @Permission('PERMISSION_CREATE')
  @UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
  @HttpCode(201)
  async createV3(@Req() req: any, @Body() body: CreateUserV2Dto) {
    this.logger.log(
      `Entering ${this.createV3.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.usersService.createV2(req, body);
    this.logger.log(
      `Leaving ${this.createV3.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v3/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListV3(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortUserDto,
  ) {
    this.logger.log(
      `Entering ${this.getListV3.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.usersService.getListV3(
      req,
      paginationData,
      searchandsortData,
    );
    return response;
  }

  @Put('/v2/update')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateV2(@Req() req: any, @Body() body: UpdateUserV2Dto) {
    this.logger.log(
      `Entering ${this.updateV2.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.usersService.updateV2(req, body);
    this.logger.log(
      `Leaving ${this.updateV2.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/v3/update')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateV3(@Req() req: any, @Body() body: UpdateUserV2Dto) {
    return await this.usersService.updateV3(req, body);
  }

  @Delete('/v3/:uid')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteV3(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.deleteV3.name} with uid: ${uid}`);
    const response = await this.usersService.deleteV3(req, uid);
    this.logger.log(
      `Leaving ${this.deleteV3.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/bulk/validate')
  @UseInterceptors(FileInterceptor('file', { limits: { files: 1 } }))
  @UsePipes(new FieldValidatorPipe())
  @HttpCode(200)
  async validateProcess(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return await this.usersService.validateProcessBulk(req, file);
  }

  @Post('/bulk/create')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async bulkCreate(@Req() req: any, @Body() body: BulkCreateUserDto[]) {
    this.logger.log(
      `Entering ${this.bulkCreate.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.usersService.createBulk(req, body);
    this.logger.log(
      `Leaving ${this.bulkCreate.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/download-template/excel')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async downloadTemplate(@Req() req: any, @Res() res: Response) {
    const { buffer, filename } =
      await this.usersService.generateExcelTemplateForUser(req);
    this.logger.log(`Leaving ${this.downloadTemplate.name}`);
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    res.end(buffer);
  }

  @Get('/v2/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getV2(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.getV2.name} with uid: ${uid}`);
    return await this.usersService.getByUid(req, uid);
  }
}
