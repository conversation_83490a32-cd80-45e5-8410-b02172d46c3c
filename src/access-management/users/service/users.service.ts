import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  BulkCreateUserDto,
  CreateUserDto,
  CreateUserV2Dto,
  SearchAndSortUserDto,
  UpdateUserDto,
  UpdateUserV2Dto,
} from '../dto/user.dto';
import { PaginationDto } from 'src/core/dtos';
import { Prisma } from '@prisma/client';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import {
  convertUserToJabatan,
  convertUserToPangkatShortName,
  convertUserToSatuanId,
  convertUserToSatuanName,
  convertUserToUserManagement,
} from '../../../core/utils/auth.utils';
import {
  I<PERSON>olumnMapping,
  IOrderBy,
  IWhere,
} from '../../../core/interfaces/db.interface';
import * as _ from 'lodash';
import * as moment from 'moment';
import { IValidateAuthByPersonelNRP } from '../../../core/interfaces/users.interface';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import * as ExcelJS from 'exceljs';
import Worksheet from 'exceljs/index';
import {
  IConvertRoleAccessModules,
  IRecursiveRoles,
  IRoleAccessV2,
} from '../../../core/interfaces/roles.interface';
import { mappingModule } from '../../../core/utils/roles.utils';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import {
  getCellStyleByType,
  getColumnLetterByKey,
} from '../../../core/utils/excel.utils';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import { buildWhereClause } from '../../../core/utils/db.utils';
import * as fs from 'fs';
import { ExcelService } from '../../../api-utils/excel/service/excel.service';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  public static readonly OPERATOR = 'operator';
  public static readonly SUPERADMIN = 'superadmin';
  public static readonly USER = 'user';
  public static readonly ADMIN = 'admin';

  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
    private readonly excelService: ExcelService,
  ) {}

  async getAuthRestructuredUsers(
    user: any,
    user_role: IRecursiveRoles[],
    isJwt: boolean,
  ) {
    const mappingRoleModules = this.mappingRoleModules(user_role);

    const result = {
      id: Number(user.users?.id),
      uid: user.uid,
      personel_id: user.id,
      nama_operator: user.nama_lengkap,
      pangkat: convertUserToPangkatShortName(user),
      nrp: user.nrp,
      role_id: user_role?.[0]?.role_id ?? null,
      role: user_role?.[0]?.role_name ?? null,
      role_tipe: user_role?.[0]?.role_tipe?.nama ?? null,
      level_id: user_role?.[0]?.level?.id ?? null,
      level: user_role?.[0]?.level?.nama ?? null,
      bagian_id: null,
      bagian: null,
      satuan_id: convertUserToSatuanId(user),
      satuan: convertUserToSatuanName(user),
      jabatan: convertUserToJabatan(user),
      email: user.email || null,
      user_mgt: convertUserToUserManagement(user),
      image: user.foto_file,
      roles: mappingRoleModules.roles,
      foto_file: user.foto_file,
      is_siswa: false,
    };

    return isJwt
      ? {
          ...result,
          modules: mappingRoleModules.modules,
          raw_roles: mappingRoleModules.raw_roles,
        }
      : result;
  }

  async validateAuthPersonelDetail(nrp: string) {
    const user = await this.prisma.personel.findFirst({
      where: {
        nrp,
        deleted_at: null,
      },
      select: {
        id: true,
        nrp: true,
        foto_file: true,
        uid: true,
        email: true,
        nama_lengkap: true,
        pangkat_personel: {
          select: {
            pangkat: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
          },
          where: {
            is_aktif: true,
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                id: true,
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
          },
          where: {
            is_aktif: true,
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        users: {
          select: {
            id: true,
            password: true,
            // user_role: {
            //   select: {
            //     id: true,
            //     role: {
            //       select: {
            //         id: true,
            //         nama: true,
            //         atasan_id: true,
            //         level: { select: { id: true, nama: true } },
            //         bagian: { select: { id: true, nama: true } },
            //         role_access: {
            //           where: { deleted_at: null },
            //           select: {
            //             role: {
            //               select: {
            //                 id: true,
            //                 nama: true,
            //                 level: { select: { id: true, nama: true } },
            //                 satuan: { select: { id: true, nama: true } },
            //                 bagian: { select: { id: true, nama: true } },
            //                 atasan_role: { select: { id: true, nama: true } },
            //                 role_tipe: { select: { id: true, nama: true } },
            //               },
            //             },
            //             modules: {
            //               select: { id: true, nama: true, name_long: true },
            //             },
            //             permission: {
            //               select: { id: true, nama: true, desc: true },
            //             },
            //             portal: { select: { id: true, nama: true } },
            //           },
            //         },
            //         role_tipe: {
            //           select: { id: true, nama: true },
            //         },
            //         atasan_role: {
            //           select: { id: true, atasan_id: true },
            //         },
            //       },
            //     },
            //     level: { select: { id: true, nama: true } },
            //   },
            //   orderBy: { created_at: 'asc' },
            //   where: {
            //     deleted_at: null,
            //     role: {
            //       deleted_at: null,
            //     },
            //   },
            // },
          },
        },
      },
    });

    if (!user?.users) {
      const message = `User not found with ${nrp}`;
      this.logger.error(message);
      throw new UnauthorizedException(message);
    }

    user.foto_file = user?.foto_file
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${user.nrp}/${user.foto_file}`,
        )
      : null;

    const userRoles = await this.queryRecursiveRoles(user.users.id);

    return { user, user_role: userRoles };
  }

  async validateAuthPersonelByNrp(
    nrp: string,
  ): Promise<IValidateAuthByPersonelNRP> {
    const user = await this.prisma.personel.findFirst({
      where: {
        nrp,
        deleted_at: null,
      },
      select: {
        id: true,
        nrp: true,
        nama_lengkap: true,
        email: true,
        users: {
          select: {
            id: true,
            password: true,
            users_jwt_token: {
              select: {
                token: true,
              },
            },
          },
        },
      },
    });

    if (!user?.users) {
      const message = 'Kombinasi username dan password tidak sesuai';
      this.logger.error(message);
      throw new UnauthorizedException(message);
    }
    return user;
  }

  async getByUid(req: any, uid: string) {
    const user = await this.prisma.personel.findFirst({
      where: {
        uid,
        deleted_at: null,
      },
      select: {
        id: true,
        nrp: true,
        foto_file: true,
        uid: true,
        email: true,
        nama_lengkap: true,
        pangkat_personel: {
          select: {
            pangkat: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
          },
          where: {
            is_aktif: true,
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                id: true,
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
          },
          where: {
            is_aktif: true,
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        users: {
          select: {
            id: true,
            password: true,
            logs_activity: {
              select: { activity: true, detail: true, created_at: true },
              orderBy: { created_at: 'desc' },
            },
          },
        },
      },
    });

    if (!user?.users) {
      const message = `User dengan UID ${uid} tidak ditemukan`;
      throw new UnauthorizedException(message);
    }

    user.foto_file = user?.foto_file
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${user.nrp}/${user.foto_file}`,
        )
      : null;

    const userRoles = await this.queryRecursiveRoles(user.users.id);
    const users = await this.getAuthRestructuredUsers(user, userRoles, false);
    return { user: users, log_activity: user?.users?.logs_activity };
  }

  async generateExcelTemplateForUser(req: any) {
    const { user } = req;
    const isSuperAdmin =
      user.roles?.[0].level?.nama.toLowerCase() === 'superadmin';
    const roleIds = Array.from(
      new Set<number>(user.roles.map((item: any) => item.id)),
    );

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet();
    const dropdownSheet = workbook.addWorksheet('Master Data');

    worksheet.columns = [
      {
        header: 'NRP',
        key: 'nrp',
        style: getCellStyleByType('string'),
        width: 20,
      },
      {
        header: 'ROLE',
        key: 'role',
        width: 35,
      },
    ];

    const defaultFirstRow = new Map<string, string>();
    defaultFirstRow.set('nrp', ' ');
    defaultFirstRow.set('role', ' ');

    worksheet.addRow(Object.fromEntries(defaultFirstRow));

    const roles: { nama: string }[] = await this.prisma.role.findMany({
      where: {
        atasan_id: { in: isSuperAdmin ? undefined : roleIds },
      },
      select: { nama: true },
      orderBy: { nama: 'asc' },
    });

    const roleNames = Array.from(
      new Set<string>(roles.map((role) => role.nama)),
    );
    const roleColumnLetter = getColumnLetterByKey(worksheet, 'role');

    this.addDataToDropdownSheet(
      roleColumnLetter,
      'Master Data Role',
      roleNames,
      dropdownSheet,
    );

    this.addDataValidation(
      roleColumnLetter,
      'Role',
      'Master Data',
      'role',
      roleNames.length,
      worksheet,
    );

    const buffer = await workbook.xlsx.writeBuffer();
    const filename = 'TEMPLATE_USER_MANAGEMENT';
    return { buffer, filename };
  }

  async getByUserId(nrp: string) {
    const users = await this.validateAuthPersonelDetail(nrp);
    if (!users) throw new NotFoundException(`No user found for nrp: ${nrp}`);

    const { user, user_role } = users;

    return await this.getAuthRestructuredUsers(user, user_role, true);
  }

  async get(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const user = await this.prisma.users_role.findFirst({
      where: { users_id: id },
      select: {
        id: true,
        user: {
          select: {
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                email: true,
                id: true,
                uid: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
              },
            },
            logs_activity: {
              select: { activity: true, detail: true, created_at: true },
              orderBy: { created_at: 'desc' },
            },
          },
        },
        role: {
          select: {
            id: true,
            nama: true,
            role_permission: {
              select: { permission: { select: { nama: true } } },
            },
            bagian: { select: { id: true, nama: true } },
          },
        },
        level: { select: { id: true, nama: true } },
      },
    });

    if (!user) throw new NotFoundException('User not found');
    const queryResult = {
      id: Number(user.id),
      nama_operator: user.user.personel.nama_lengkap,
      nrp: user.user.personel.nrp,
      pangkat: user.user.personel.pangkat_personel[0]?.pangkat || null,
      role: { id: user.role.id, nama: user.role.nama },
      personel: user.user.personel,
      level: user.level,
      bagian: user.role.bagian,
      hak_akses: user.role.role_permission.map((item) => item.permission.nama),
      logs_activity: user.user.logs_activity,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V1_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V1_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListV2(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortUserDto,
  ) {
    const { user } = req;
    const { sort_column, sort_desc, search } = searchAndSortData;
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const where = {
      deleted_at: null,
      role: {
        atasan_id: Number(user.role_id),
      },
      AND: [
        {
          OR: [
            {
              user: {
                personel: {
                  nama_lengkap: { contains: search, mode: 'insensitive' },
                },
              },
            },
            {
              user: {
                personel: {
                  nrp: { contains: search, mode: 'insensitive' },
                },
              },
            },
          ],
        },
      ],
    } satisfies Prisma.users_roleWhereInput;

    const orderBy = [];
    const sort = ['asc', 'desc'].includes(sort_desc) ? sort_desc : 'asc';
    switch (sort_column) {
      case 'nama_operator':
        orderBy.push({ user: { personel: { nama_lengkap: sort } } });
        break;
      case 'nrp':
        orderBy.push({ user: { personel: { nrp: sort } } });
        break;
      case 'role':
        orderBy.push({ role: { nama: sort } });
        break;
      case 'level':
        orderBy.push({ level: { nama: sort } });
        break;
      case 'bagian':
        orderBy.push({ role: { bagian: { nama: sort } } });
        break;
      case 'created_at':
        orderBy.push({ created_at: sort });
        break;
      default:
        orderBy.push({ created_at: 'desc' });
        break;
    }

    const [totalData, users] = await this.prisma.$transaction([
      this.prisma.users_role.count({
        where: where,
      }),
      this.prisma.users_role.findMany({
        select: {
          id: true,
          user: {
            select: {
              personel: {
                select: {
                  id: true,
                  uid: true,
                  nama_lengkap: true,
                  nrp: true,
                  foto_file: true,
                },
              },
            },
          },
          role: {
            select: {
              id: true,
              nama: true,
              bagian: { select: { id: true, nama: true } },
              level: { select: { id: true, nama: true } },
            },
          },
          level: { select: { id: true, nama: true } },
          created_at: true,
        },
        take: limit,
        skip: limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = users.map((item) => {
      const foto_file = item.user?.personel?.foto_file
        ? this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${item.user.personel.nrp}/${item.user.personel.foto_file}`,
          )
        : null;

      return {
        id: Number(item.id),
        nama_operator: item.user?.personel?.nama_lengkap ?? '',
        foto_file: foto_file,
        nrp: item.user?.personel?.nrp ?? '',
        role: { id: item.role?.id ?? '', nama: item.role?.nama ?? '' },
        personel: {
          id: item.user?.personel?.id ?? '',
          uid: item.user?.personel?.uid ?? '',
          nama: item.user?.personel?.nama_lengkap ?? '',
        },
        level: item.role?.level?.nama ?? '',
        bagian: item.role?.bagian ?? '',
        created_at: item.created_at,
      };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V2_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V2_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalData: totalData,
      totalPage: totalPage,
    };
  }

  async getListV3(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortUserDto,
  ) {
    const { user } = req;
    const isSuperAdmin =
      user.roles?.[0].level?.nama.toLowerCase() === 'superadmin';

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama_operator: { field: 'pu|nama_lengkap', type: 'string' },
      nama_role: { field: 'pu|nama_role', type: 'string' },
      nrp: { field: 'pu|nrp', type: 'string' },
      created_at: { field: 'pu|last_role_created_at', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const newUsers = await this.findUsers(
      {
        take: limit,
        skip: limit * (+page - 1),
        where,
        orderQuery: orderBy,
      },
      isSuperAdmin ? undefined : user.roles.map((item) => item.id),
    );
    const results = newUsers.reduce((acc, item) => {
      if (!acc[item.uid]) {
        acc[item.uid] = {
          id: item.uid,
          nama_operator: item.nama_lengkap,
          nrp: item.nrp,
          role: [],
          created_at: item.created_at,
        };
      }

      acc[item.uid].role.push({
        id: item.id,
        name: item.nama,
        atasan_id: item.atasan_id,
        created_at: item.created_at,
      });

      return acc;
    }, {} as any);

    const totalData = Number(newUsers?.[0]?.total_data) ?? 0;
    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V3_READ as ConstantLogType,
        message,
        Array.from(Object.values(results)),
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: Array.from(Object.values(results)),
      page: page,
      totalData: totalData,
      totalPage: totalPage,
    };
  }

  async create(req: any, body: CreateUserDto) {
    const { user } = req;
    const { personel_id, role_id, type } = body;

    const users = await this.validateEntity(
      'users',
      { personel_id: personel_id },
      {
        id: true,
        personel: { select: { nama_lengkap: true, nrp: true } },
        users_role: {
          select: {
            role_id: true,
            role: {
              select: {
                nama: true,
              },
            },
          },
          where: { deleted_at: null },
        },
      },
      'User not found',
      null,
    );

    if (users.users_role.length)
      throw new BadRequestException(
        `User already assigned to role ${users.users_role[0].role.nama}`,
      );

    const isLevelSuperAdmin =
      user.level_name_new.toLowerCase() === 'superadmin';
    const isUserLevel1 = user.level_name_new.toLowerCase() === 'level 1';
    const isUserRoleAdmin = user.role_tipe_nama.toLowerCase() === 'admin';

    const roles = await this.validateEntity(
      'role',
      { id: role_id },
      {
        id: true,
        nama: true,
        is_admin: true,
        bagian: { select: { id: true, nama: true } },
        role_tipe: { select: { id: true, nama: true } },
        atasan_id: true,
      },
      'Role not found',
      null,
    );

    const isRoleAdmin = roles.role_tipe.nama.toLowerCase() === 'admin';
    const payload = this.buildPayload({
      user,
      roles,
      type,
      isLevelSuperAdmin,
      isUserLevel1,
      isUserRoleAdmin,
      isRoleAdmin,
      role_id,
    });

    const getUser = await this.prisma.$transaction(async (tx) => {
      return await tx.users_role.create({
        data: payload,
        select: {
          id: true,
          user: {
            select: {
              personel: { select: { nama_lengkap: true, nrp: true } },
            },
          },
          role: {
            select: {
              nama: true,
              bagian: { select: { nama: true } },
              role_tipe: { select: { nama: true } },
              level: { select: { nama: true } },
            },
          },
          level: { select: { nama: true } },
          created_at: true,
        },
      });
    });

    const queryResult = {
      ...getUser,
      id: Number(getUser.id),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V1_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V1_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createV2(req: any, body: CreateUserV2Dto) {
    const currentDate = moment().toDate();
    const { uid_personel, roles } = body;

    const user = await this.prisma.users.findFirst({
      where: { personel: { uid: uid_personel } },
      select: {
        id: true,
        personel: { select: { nama_lengkap: true, nrp: true } },
        users_role: {
          select: {
            role_id: true,
            deleted_at: true,
            role: { select: { id: true, nama: true, role_tipe_id: true } },
          },
          // where: { deleted_at: null },
        },
      },
    });

    if (!user) throw new NotFoundException('Personel tidak ditemukan!');

    const idRolesUniqueFromBody = [...new Set(roles)];

    const rolesOnBody = await this.prisma.role.findMany({
      where: {
        id: { in: idRolesUniqueFromBody },
        role_tipe_id: { not: null },
        deleted_at: null,
      },
      include: { role_tipe: true },
    });

    if (rolesOnBody.length !== idRolesUniqueFromBody.length)
      throw new NotFoundException('Role tidak terdaftar');

    const existingRole = user.users_role.find(
      (r) => idRolesUniqueFromBody.includes(r.role.id) && !r.deleted_at,
    );

    if (existingRole)
      throw new ConflictException(
        `Personel sudah memiliki role ${existingRole.role.nama}`,
      );

    const validRoleIds = new Set(rolesOnBody.map((r) => r.id));
    const userRoleStates = new Map(
      user.users_role.map((r) => [r.role_id, Boolean(r.deleted_at)]),
    );

    const toInsert: { role_id: number; users_id: bigint }[] = [];
    const toReactivate: number[] = [];

    for (const roleId of validRoleIds) {
      if (!userRoleStates.has(roleId)) {
        toInsert.push({ role_id: roleId, users_id: user.id });
      } else if (userRoleStates.get(roleId) === true) {
        toReactivate.push(roleId);
      }
    }

    await this.prisma.$transaction(async (tx) => {
      if (toInsert.length) {
        await tx.users_role.createMany({ data: toInsert });
      }

      if (toReactivate.length) {
        await tx.users_role.updateMany({
          where: {
            users_id: user.id,
            role_id: { in: toReactivate },
          },
          data: { deleted_at: null },
        });
      }
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  async update(req: any, id: number, body: UpdateUserDto) {
    const reqUser = req.user;
    if (
      reqUser.role_tipe_nama.toLowerCase() !== 'admin' &&
      !['superadmin', 'level 1'].includes(reqUser.level_name_new.toLowerCase())
    )
      throw new ForbiddenException('Unauthorized');

    const { role_id } = body;

    const user = await this.prisma.users_role.findFirst({
      where: { id: +id, deleted_at: null },
      select: {
        id: true,
        user: { select: { personel: { select: { nama_lengkap: true } } } },
        role: {
          select: {
            id: true,
            atasan_id: true,
            bagian: { select: { id: true, nama: true } },
          },
        },
        level: { select: { id: true, nama: true } },
      },
    });

    if (!user) throw new NotFoundException(`User not found`);
    if (reqUser.role_id != user.role.atasan_id)
      throw new ForbiddenException(
        `No access to update this user ${user.user.personel.nama_lengkap}`,
      );

    const queryResult = await this.prisma.users_role.update({
      where: { id: +id },
      data: {
        // level_id: level_id,
        role_id: role_id,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V2_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V2_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateV2(req: any, body: UpdateUserV2Dto) {
    const user = await this.prisma.users.findFirst({
      where: {
        personel: {
          uid: body.uid_personel,
        },
      },
      select: {
        id: true,
        personel: { select: { nama_lengkap: true, nrp: true } },
        users_role: {
          select: {
            role: {
              select: {
                id: true,
                nama: true,
                role_tipe_id: true,
              },
            },
          },
          where: { deleted_at: null },
        },
      },
    });

    if (!user) throw new NotFoundException('Personel tidak ditemukan!');

    const roles = await this.prisma.role.findMany({
      where: {
        id: { in: body.roles },
        role_tipe_id: { not: null },
        deleted_at: null,
      },
      include: {
        role_tipe: true,
      },
    });

    //Distinct role tipe id from roles
    const uniqueTypeRole = roles.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.role_tipe_id === item.role_tipe_id),
    );

    if (uniqueTypeRole.length > 1 || uniqueTypeRole.length < 1)
      throw new BadRequestException('Invalid data!');

    return await this.prisma.$transaction(async (tx) => {
      await tx.users_role.updateMany({
        where: {
          user: {
            personel: {
              uid: body.uid_personel,
            },
          },
        },
        data: { deleted_at: new Date() },
      });

      await tx.users_role.createMany({
        data: roles.map((item) => ({
          users_id: user.id,
          role_id: item.id,
          created_at: new Date(),
        })),
      });

      const getUser = await tx.users.findFirst({
        select: {
          id: true,
          personel: {
            select: { uid: true, nama_lengkap: true, nrp: true },
          },
          users_role: {
            select: {
              role: {
                select: {
                  nama: true,
                  role_tipe: { select: { nama: true } },
                },
              },
              created_at: true,
            },
          },
        },
        where: {
          id: user.id,
          deleted_at: null,
        },
      });

      // throw new Error('Hello');

      return {
        uid: getUser.personel.uid,
        nama_lengkap: getUser.personel.nama_lengkap,
        nrp: getUser.personel.nrp,
        roles: getUser.users_role.map((item) => ({
          name: item.role?.nama ?? '',
          role_tipe: item.role?.role_tipe?.nama ?? '',
          created_at: item.created_at,
        })),
      };
    });
  }

  async updateV3(req: any, body: UpdateUserV2Dto) {
    const { uid_personel, roles: requestedRoles } = body;

    const user = await this.prisma.users.findFirst({
      where: {
        personel: { uid: uid_personel, deleted_at: null },
      },
      select: {
        id: true,
        users_role: {
          select: { role_id: true, deleted_at: true },
        },
      },
    });

    if (!user) throw new NotFoundException('Personel tidak ditemukan!');

    const roles = await this.prisma.role.findMany({
      where: {
        id: { in: requestedRoles },
        // role_tipe_id: { not: null },
        deleted_at: null,
      },
      select: { id: true },
    });

    if (roles.length !== requestedRoles.length)
      throw new NotFoundException('Role yang Anda pilih tidak ditemukan');

    const validRoleIds = new Set(roles.map((r) => r.id));
    const userRoleStates = new Map(
      user.users_role.map((r) => [r.role_id, Boolean(r.deleted_at)]),
    );

    const toInsert: { role_id: number; users_id: bigint }[] = [];
    const toReactivate: number[] = [];
    const toDeactivate: number[] = [];

    for (const roleId of validRoleIds) {
      if (!userRoleStates.has(roleId)) {
        toInsert.push({ role_id: roleId, users_id: user.id });
      } else if (userRoleStates.get(roleId) === true) {
        toReactivate.push(roleId);
      }
    }

    for (const [roleId, isDeleted] of userRoleStates) {
      if (!validRoleIds.has(roleId) && isDeleted === false)
        toDeactivate.push(roleId);
    }

    await this.prisma.$transaction(async (tx) => {
      if (toInsert.length) {
        await tx.users_role.createMany({ data: toInsert });
      }
      if (toReactivate.length) {
        await tx.users_role.updateMany({
          where: {
            users_id: user.id,
            role_id: { in: toReactivate },
          },
          data: { deleted_at: null },
        });
      }
      if (toDeactivate.length) {
        await tx.users_role.updateMany({
          where: {
            users_id: user.id,
            role_id: { in: toDeactivate },
          },
          data: { deleted_at: new Date() },
        });
      }
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  async deleteV2(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const reqUser = req.user;
    if (
      reqUser.role_tipe_nama.toLowerCase() !== 'admin' &&
      !['superadmin', 'level 1'].includes(reqUser.level_name.toLowerCase())
    )
      throw new ForbiddenException('Unauthorized');

    const user = await this.prisma.users_role.findFirst({
      where: { id: +id, deleted_at: null },
      select: {
        id: true,
        user: { select: { personel: { select: { nama_lengkap: true } } } },
        role: {
          select: {
            id: true,
            bagian: { select: { id: true, nama: true } },
            atasan_id: true,
          },
        },
        level: { select: { id: true, nama: true } },
      },
    });

    if (!user) throw new NotFoundException(`User not found`);

    if (user.role.atasan_id !== reqUser.role_id)
      throw new ForbiddenException(
        `No access to delete this user ${user.user.personel.nama_lengkap}`,
      );

    await this.prisma.users_role.update({
      where: { id: +id },
      data: {
        deleted_at: new Date(),
      },
    });

    const queryResult = { ...user, id: Number(user.id) };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V2_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V2_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteV3(req: any, uid: string) {
    const user = await this.prisma.users_role.findMany({
      where: {
        user: {
          personel: {
            uid: uid,
          },
        },
      },
    });

    if (!user.length) throw new NotFoundException('User not found!');

    const queryResult = await this.prisma.users_role.updateMany({
      where: {
        user: {
          personel: {
            uid: uid,
          },
        },
      },
      data: {
        deleted_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.USERS_V3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.USER_V3_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async validateProcessBulk(req: any, file: Express.Multer.File) {
    const { user } = req;
    const isSuperAdmin =
      user.roles?.[0].level?.nama.toLowerCase() === 'superadmin';

    if (!file)
      throw new BadRequestException(
        'Anda belum upload file. Silakan upload ulang!',
      );

    const tempFilePath = file.originalname;
    await fs.promises.writeFile(tempFilePath, file.buffer);

    try {
      const resultJson = await this.excelService.extractToJson(
        req,
        tempFilePath,
        true,
      );
      const responseData =
        (resultJson?.data as { nrp: string; role: string }[]) || [];

      if (responseData.length === 0)
        throw new BadRequestException('File yang Anda kirim kosong!');

      const uniqueNrps = [...new Set(responseData.map((d) => d.nrp))];
      const uniqueRoles = [...new Set(responseData.map((d) => d.role))];

      const [personels, roles, whitelistRoles] = await Promise.all([
        this.prisma.personel.findMany({
          where: { nrp: { in: uniqueNrps } },
          select: { nrp: true, nama_lengkap: true },
        }),
        this.prisma.role.findMany({
          where: { nama: { in: uniqueRoles } },
          select: { id: true, nama: true },
        }),
        this.prisma.role.findMany({
          where: {
            atasan_id: {
              in: isSuperAdmin
                ? undefined
                : user.roles.map((item: any) => item.id),
            },
            deleted_at: null,
          },
          select: {
            id: true,
            nama: true,
            atasan_id: true,
          },
          orderBy: [{ nama: 'asc' }],
        }),
      ]);

      const personelMap = new Map(
        personels.map((p) => [p.nrp, p.nama_lengkap]),
      );
      const roleMap = new Map(roles.map((r) => [r.nama, r.id]));
      const whitelistRoleMap = new Set(whitelistRoles.map((r) => r.id));

      const results = responseData.map(({ nrp, role }) => {
        const errors: string[] = [];

        const nama = personelMap.get(nrp) ?? '';
        const roleValue = roleMap.get(role);
        let roleID: number | undefined;

        if (!personelMap.has(nrp)) {
          errors.push('NRP yang Anda masukkan tidak terdaftar dalam personel');
        }

        if (!roleValue) {
          errors.push(`Role ${role} tidak ditemukan`);
        } else if (!whitelistRoleMap.has(roleValue)) {
          errors.push(`Role ${role} tidak diperbolehkan untuk Anda`);
        } else {
          roleID = roleValue;
        }

        return {
          nrp,
          role: roleID,
          nama,
          desc: errors.join(', '),
        };
      });

      return {
        statusCode: HttpStatus.OK,
        message: 'Success',
        data: results,
      };
    } finally {
      fs.promises.unlink(tempFilePath).catch(() => {});
    }
  }

  async createBulk(req, body: BulkCreateUserDto[]) {
    const { user } = req;
    const isSuperAdmin =
      user.roles?.[0]?.level?.nama?.toLowerCase() === 'superadmin';

    if (!body.length)
      throw new BadRequestException('Data user tidak boleh kosong!');

    const hasInvalidData = body.some((d) => d.desc?.trim());
    if (hasInvalidData)
      throw new BadRequestException(
        'Beberapa data yang anda masukkan masih ada yang salah. Silahkan periksa kembali.',
      );

    const uniqueNrps = [...new Set(body.map((d) => d.nrp))];
    const personels = await this.prisma.users.findMany({
      where: { personel: { nrp: { in: uniqueNrps } } },
      select: { id: true, personel: { select: { nrp: true } } },
    });

    const personelMap = new Map(personels.map((p) => [p.personel.nrp, p.id]));

    const rolePairs = body
      .map((d) => {
        const userId = personelMap.get(d.nrp);
        if (!userId) return null;
        return { users_id: userId, role_id: d.role };
      })
      .filter(Boolean) as { users_id: bigint; role_id: number }[];

    const existingRoles = await this.prisma.users_role.findMany({
      where: {
        OR: rolePairs.map(({ users_id, role_id }) => ({ users_id, role_id })),
      },
      select: { users_id: true, role_id: true },
    });

    const existingSet = new Set(
      existingRoles.map((r) => `${r.users_id}-${r.role_id}`),
    );

    const newRoles = rolePairs.filter(
      ({ users_id, role_id }) => !existingSet.has(`${users_id}-${role_id}`),
    );

    if (newRoles.length) {
      await this.prisma.users_role.createMany({
        data: newRoles,
        skipDuplicates: true,
      });
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private async validateEntity(
    tableName: string,
    where: any,
    select: any,
    notFoundMessage: string | null,
    foundMessage: string | null,
  ) {
    const entity = await this.prisma[tableName].findFirst({ where, select });
    if (notFoundMessage && !entity)
      throw new NotFoundException(notFoundMessage);
    if (foundMessage && entity) throw new NotFoundException(foundMessage);
    return entity;
  }

  private buildPayload(params: {
    user: any;
    roles: any;
    type: string;
    isLevelSuperAdmin: boolean;
    isUserLevel1: boolean;
    isUserRoleAdmin: boolean;
    isRoleAdmin: boolean;
    role_id: number;
  }) {
    const {
      user,
      roles,
      type,
      isLevelSuperAdmin,
      isUserLevel1,
      isUserRoleAdmin,
      isRoleAdmin,
      role_id,
    } = params;

    if (
      this.isSuperAdminCase(
        user,
        roles,
        isLevelSuperAdmin,
        isUserRoleAdmin,
        isRoleAdmin,
      )
    ) {
      return this.createPayload(user, roles.id);
    } else if (
      this.isBagianSayaCase(type, isUserLevel1, isUserRoleAdmin, roles, role_id)
    ) {
      return this.createPayload(user, role_id);
    } else if (
      this.isBagianLainCase(type, isUserLevel1, isUserRoleAdmin, roles, user)
    ) {
      return this.createPayload(user, role_id);
    } else {
      throw new BadRequestException('Bad Request!');
    }
  }

  private isSuperAdminCase(
    user: any,
    roles: any,
    isLevelSuperAdmin: boolean,
    isUserRoleAdmin: boolean,
    isRoleAdmin: boolean,
  ) {
    return (
      isLevelSuperAdmin &&
      user.atasan_role === null &&
      isUserRoleAdmin &&
      (!isRoleAdmin || roles.atasan_id === user.role_id)
    );
  }

  private isBagianSayaCase(
    type: string,
    isUserLevel1: boolean,
    isUserRoleAdmin: boolean,
    roles: any,
    role_id: number,
  ) {
    if (type !== 'bagian_saya' || !isUserLevel1 || !isUserRoleAdmin)
      return false;
    if (
      roles.atasan_id !== role_id ||
      roles.role_tipe.nama.toLowerCase() === 'admin'
    ) {
      throw new BadRequestException('Role invalid!');
    }
    return true;
  }

  private isBagianLainCase(
    type: string,
    isUserLevel1: boolean,
    isUserRoleAdmin: boolean,
    roles: any,
    user: any,
  ) {
    if (type !== 'bagian_lain' || !isUserLevel1 || !isUserRoleAdmin)
      return false;
    if (
      roles.role_tipe.nama.toLowerCase() === 'admin' ||
      roles.atasan_id === user.atasan_role.id
    ) {
      throw new BadRequestException('Role invalid!');
    }
    return true;
  }

  private async queryRecursiveRoles(userId: bigint) {
    return await this.prisma.$queryRaw<IRecursiveRoles[]>`
      WITH RECURSIVE
        menu_tree AS (SELECT id, nama, name_long, parent_id, id::TEXT AS chain_parent, 1 AS level
                      FROM modules
                      WHERE parent_id IS NULL
                        AND deleted_at IS NULL
                      UNION ALL
                      SELECT m.id,
                             m.nama,
                             m.name_long,
                             m.parent_id,
                             m.id::TEXT || ' - ' || mt.chain_parent,
                             mt.level + 1
                      FROM modules m
                             INNER JOIN menu_tree mt ON m.parent_id = mt.id
                      WHERE m.deleted_at IS NULL),
        role_data AS (SELECT r.id                                                           AS role_id,
                             r.nama                                                         AS role_name,
                             r.created_at                                                   AS created_at,
                             r.is_fungsi                                                    AS is_fungsi,
                             CASE
                               WHEN r2.id IS NULL THEN NULL
                               ELSE JSON_BUILD_OBJECT('id', r2.id, 'nama', r2.nama, 'atasan_id',
                                                      r2.atasan_id) END                     AS atasan_role,
                             CASE
                               WHEN r.level_id IS NULL THEN NULL
                               ELSE JSON_BUILD_OBJECT('id', r.level_id, 'nama', l.nama) END AS level,
                             JSON_BUILD_OBJECT('id', r.role_tipe_id, 'nama', rt.nama)       AS role_tipe,
                             mt.name_long                                                   AS module_name_long,
                             mt.nama                                                        AS module_name,
                             mt.id                                                          AS module_id,
                             p.label                                                        AS permission_label,
                             p.id                                                           AS permission_id,
                             p.nama                                                         AS permission_name,
                             mt.chain_parent
                      FROM role r
                             LEFT JOIN role_access ra ON ra.role_id = r.id
                             LEFT JOIN menu_tree mt ON ra.module_id = mt.id
                             LEFT JOIN permission p ON ra.permission_id = p.id
                             LEFT JOIN role_tipe rt ON r.role_tipe_id = rt.id
                             LEFT JOIN level l ON r.level_id = l.id
                             LEFT JOIN role r2 ON r.atasan_id = r2.id
                             JOIN users_role ur ON ur.role_id = r.id
                      WHERE ra.deleted_at IS NULL
                        AND p.deleted_at IS NULL
                        AND l.deleted_at IS NULL
                        AND r2.deleted_at IS NULL
                        AND r.deleted_at IS NULL
                        AND ur.deleted_at IS NULL
                        AND ur.users_id = ${userId}
                      GROUP BY r2.id, r.id, mt.id, ur.users_id, r.nama, mt.nama, p.label, p.nama, p.id, l.nama,
                               rt.nama,
                               mt.level, mt.name_long,
                               mt.chain_parent, r.created_at
                      ORDER BY mt.level)
      SELECT rd.*
      FROM role_data rd;
    `;
  }

  private addDataToDropdownSheet(
    columnLetter: string,
    masterDataHeader: string,
    dataLs: string[],
    dropdownSheet: Worksheet,
  ) {
    const initRow = 1;
    dropdownSheet.getCell(`${columnLetter}${initRow}`).value = masterDataHeader;
    dataLs.unshift(' ');
    dataLs.forEach((data, index) => {
      dropdownSheet.getCell(`${columnLetter}${index + 1}`).value = data;
    });

    dropdownSheet.getRow(2).hidden = true;
    dropdownSheet.state = 'hidden';
  }

  private addDataValidation(
    columnLetter: string,
    dataHeader: string,
    dropdownSheetname: string,
    dataKey: string,
    countData: number,
    worksheet: Worksheet,
  ) {
    worksheet.dataValidations.add(`${columnLetter}2:${columnLetter}1048576`, {
      type: 'list',
      allowBlank: true,
      formulae: [
        `'${dropdownSheetname}'!${columnLetter}$2:$${columnLetter}$${countData}`,
      ],
      showErrorMessage: true,
      errorTitle: `Invalid ${dataHeader}`,
      error: `The ${dataKey} selected is not valid. Please select a valid ${dataKey} from the dropdown.`,
    });
  }

  private createPayload(user: any, roleId: number) {
    return {
      user_id: user.id,
      role_id: roleId,
      create_at: new Date(),
    };
  }

  private mappingRoleModules(data: IRecursiveRoles[]) {
    if (!Array.isArray(data) || data.length === 0) {
      return { roles: [], modules: [], raw_roles: [] };
    }

    const roles = _.uniqBy(
      _.map(data, (role) => ({
        id: role.role_id,
        nama: role.role_name,
        level: role.level ?? null,
        role_tipe: role.role_tipe?.nama ?? null,
        atasan_role: role.atasan_role?.nama ?? null,
        is_fungsi: role.is_fungsi ?? false,
      })),
      'id',
    );

    const modules = this.mappingAllModules(data);

    return { roles, modules, raw_roles: data };
  }

  private mappingAllModules(data: IRecursiveRoles[]) {
    const results: Record<
      number,
      IRoleAccessV2 & { modules: IConvertRoleAccessModules[] }
    > = {};

    data.forEach((item) => {
      if (!item.module_id) return;

      if (!results['data']) {
        results['data'] = {
          id: item.module_id,
          nama: item.module_name,
          name_long: item.module_name_long,
          chain_parent: item.chain_parent,
          permissions: [],
          modules: [],
        };
      }

      mappingModule(results['data'].modules!, item);
    });

    return results['data']?.modules ?? [];
  }

  private buildOrderByClause(orderQuery: IOrderBy[] = []): string {
    const orders = orderQuery
      .map((order) => {
        const [rawKey, sortDir] = Object.entries(order)[0];
        const column = rawKey.replace('|', '.');
        const direction = sortDir.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
        return rawKey !== 'id' ? `${column} ${direction}` : null;
      })
      .filter(Boolean);

    return orders.length
      ? `ORDER BY ${orders.join(', ')}`
      : 'ORDER BY pu.last_role_created_at DESC';
  }

  private async findUsers(
    params: {
      take?: number;
      skip?: number;
      where?: IWhere | null;
      orderQuery?: IOrderBy[];
    } = {},
    atasanIds?: any[] | undefined,
  ): Promise<any[]> {
    const { take = 10, skip = 0, where, orderQuery } = params;

    const whereClause = buildWhereClause(where);
    const orderClause = this.buildOrderByClause(orderQuery);
    const pagination = `LIMIT ${take} OFFSET ${skip}`;
    const atasan = atasanIds
      ? `AND r.atasan_id IN (${atasanIds.join(',')})`
      : '';

    return this.prisma.$queryRaw`
      WITH user_ranks AS (SELECT ps.uid,
                                 ps.nama_lengkap,
                                 ps.nrp,
                                 MAX(ur.created_at) AS last_role_created_at
                          FROM users us
                                 JOIN personel ps ON us.personel_id = ps.id
                                 LEFT JOIN users_role ur ON us.id = ur.users_id
                                 JOIN role r ON ur.role_id = r.id
                          WHERE us.deleted_at IS NULL
                            AND ur.deleted_at IS NULL
                            AND r.deleted_at IS NULL
                            AND r.role_tipe_id IS NOT NULL
                          GROUP BY ps.uid, ps.nama_lengkap, ps.nrp),
           user_data AS (SELECT ps.uid,
                                ps.nama_lengkap,
                                ps.nrp,
                                r.id,
                                r.nama,
                                r.atasan_id,
                                ur.created_at
                         FROM users us
                                JOIN personel ps ON us.personel_id = ps.id
                                LEFT JOIN users_role ur ON us.id = ur.users_id
                                JOIN role r ON ur.role_id = r.id
                         WHERE us.deleted_at IS NULL
                           AND ur.deleted_at IS NULL
                           AND r.deleted_at IS NULL
                           ${Prisma.raw(atasan)}),
           grouped_user AS (SELECT ud.uid,
                                   ud.nama_lengkap,
                                   ud.nrp,
                                   MAX(ur.last_role_created_at) AS last_role_created_at,
                                   MAX(ud.nama)                 AS nama_role
                            FROM user_data ud
                                   JOIN user_ranks ur ON ur.uid = ud.uid
                            GROUP BY ud.uid, ud.nama_lengkap, ud.nrp),
           searched_user AS (SELECT pu.*
                             FROM grouped_user pu
                             WHERE 1 = 1
                               ${Prisma.raw(whereClause)}),
           ordered_user AS (SELECT *
                            FROM searched_user pu
                              ${Prisma.raw(orderClause)}),
           paged_user AS (SELECT *
                          FROM ordered_user ${Prisma.raw(pagination)}),
           distinct_data AS (SELECT COUNT(DISTINCT uid) AS total_data
                             FROM ordered_user)
      SELECT (SELECT total_data FROM distinct_data) AS total_data,
             ud.*
      FROM user_data ud
             JOIN paged_user pu ON ud.uid = pu.uid
        ${Prisma.raw(orderClause)},
           ud.created_at DESC;
    `;
  }
}
