import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DikbangumService } from '../service/dikbangum.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { CreateDikbangumDto, UpdateDikbangumDto } from '../dto/dikbangum.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('dikbangum')
@UseGuards(JwtAuthGuard)
export class DikbangumController {
  private readonly logger = new Logger(DikbangumController.name);

  constructor(private readonly dikbangumService: DikbangumService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateDikbangumDto) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangumService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.dikbangumService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.dikbangumService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateDikbangumDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangumService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.dikbangumService.delete(req, id);

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async dikbangumPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.delete.name} with uid ${uid}`);

    const response = await this.dikbangumService.dikbangumPersonel(req, uid);

    this.logger.log(
      `Leaving ${this.delete.name} with uid ${uid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
