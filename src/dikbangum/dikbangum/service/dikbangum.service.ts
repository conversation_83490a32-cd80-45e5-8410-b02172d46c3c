import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateDikbangumDto, UpdateDikbangumDto } from '../dto/dikbangum.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class DikbangumService {
  constructor(
    private readonly logsActivityService: LogsActivityService,
    private readonly prisma: PrismaService,
  ) {}

  async create(req: any, body: CreateDikbangumDto) {
    try {
      const { nama, kategori_id, nama_alternatif, is_aktif } = body;

      const dataDikbangum = await this.prisma.dikbangum.findFirst({
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      const dataDikbangumKategori =
        await this.prisma.dikbangum_kategori.findFirst({
          where: {
            id: +kategori_id,
            deleted_at: null,
          },
        });

      if (dataDikbangum)
        throw new BadRequestException('Nama dikbangum already exist!');

      if (!dataDikbangumKategori)
        throw new BadRequestException('Kategori dikbangum not found!');

      const queryResult = await this.prisma.dikbangum.create({
        data: {
          nama: nama,
          dikbangum_kategori_id: +kategori_id,
          nama_alternatif: nama_alternatif,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully create new dikbangum',
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async get(req: any, id) {
    try {
      const queryResult = await this.prisma.dikbangum.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
        include: { dikbangum_kategori: true },
      });

      if (!queryResult) {
        throw new NotFoundException(`dikbangum id ${id} not found`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(req: any, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'number' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        kategori: { field: 'dikbangum_kategori.nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.dikbangum.count({
          where: where,
        }),
        this.prisma.dikbangum.findMany({
          select: {
            id: true,
            nama: true,
            nama_alternatif: true,
            is_aktif: true,
            created_at: true,
            dikbangum_kategori: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where: where,
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get list dikbangum',
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id, body: UpdateDikbangumDto) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const existing = await tx.dikbangum.findFirst({
          where: { id: +id, deleted_at: null },
        });

        if (!existing) {
          throw new BadRequestException(`Dikbangum id ${id} not found!`);
        }

        // Validasi kategori hanya jika dikirim
        if (
          typeof body.kategori_id !== 'undefined' &&
          !(await tx.dikbangum_kategori.findFirst({
            where: { id: +body.kategori_id, deleted_at: null },
          }))
        ) {
          throw new BadRequestException(`Dikbangum kategori not found`);
        }

        // Bangun data update secara dinamis
        const updateData: any = {};
        if (typeof body.nama !== 'undefined') updateData.nama = body.nama;
        if (typeof body.nama_alternatif !== 'undefined')
          updateData.nama_alternatif = body.nama_alternatif;
        if (typeof body.kategori_id !== 'undefined')
          updateData.dikbangum_kategori_id = body.kategori_id;
        if (typeof body.is_aktif !== 'undefined')
          updateData.is_aktif = Boolean(body.is_aktif);
        updateData.updated_at = new Date();

        const updateDikbangum = await tx.dikbangum.update({
          where: { id: +id },
          data: updateData,
        });

        return updateDikbangum;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id) {
    try {
      if (
        !(await this.prisma.dikbangum.findFirst({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Dikbangum id ${id} not found!`);

      const queryResult = await this.prisma.dikbangum.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async dikbangumPersonel(req: any, uid: string) {
    try {
      const getDikbangumPersonel =
        await this.prisma.dikbangum_personel.findMany({
          select: {
            dikbangum: {
              select: {
                nama: true,
              },
            },
            tanggal_selesai: true,
            ranking: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_selesai: 'asc',
          },
        });

      const queryResult = getDikbangumPersonel.map((item) => {
        const date = new Date(item.tanggal_selesai);
        const year = date.getFullYear();
        return {
          tingkat: item.dikbangum.nama,
          tahun: year,
          ranking: item.ranking,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKBANGUM_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGUM_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
