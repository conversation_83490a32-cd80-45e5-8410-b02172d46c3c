import { forwardRef, Module } from '@nestjs/common';
import { DikbangumService } from './service/dikbangum.service';
import { DikbangumController } from './controller/dikbangum.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DikbangumController],
  providers: [DikbangumService],
})
export class DikbangumModule {}
