import { PartialType } from '@nestjs/mapped-types';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateDikbangumDto {
  @IsString()
  @IsNotEmpty()
  nama: string;

  @IsNumber()
  @IsNotEmpty()
  kategori_id: number;

  @IsString()
  @IsNotEmpty()
  nama_alternatif: string;

  @IsBoolean()
  @IsOptional()
  is_aktif?: boolean;
}

export class UpdateDikbangumDto extends PartialType(CreateDikbangumDto) {}
