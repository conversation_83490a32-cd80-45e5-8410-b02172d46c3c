import { forwardRef, Module } from '@nestjs/common';
import { DikbangumKategoriService } from './service/dikbangum-kategori.service';
import { DikbangumKategoriController } from './controller/dikbangum-kategori.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DikbangumKategoriController],
  providers: [DikbangumKategoriService],
})
export class DikbangumKategoriModule {}
