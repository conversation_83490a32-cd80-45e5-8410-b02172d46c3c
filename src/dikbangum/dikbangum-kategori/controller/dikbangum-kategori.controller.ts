import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DikbangumKategoriService } from '../service/dikbangum-kategori.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import {
  CreateDikbangumKategoriDto,
  UpdateDikbangumKategoriDto,
} from '../dto/dikbangum-kategori.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('dikbangum-kategori')
export class DikbangumKategoriController {
  private readonly logger = new Logger(DikbangumKategoriController.name);

  constructor(
    private readonly dikbangumKategoriService: DikbangumKategoriService,
  ) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateDikbangumKategoriDto) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangumKategoriService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.dikbangumKategoriService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.dikbangumKategoriService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateDikbangumKategoriDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangumKategoriService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.dikbangumKategoriService.delete(req, id);

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
