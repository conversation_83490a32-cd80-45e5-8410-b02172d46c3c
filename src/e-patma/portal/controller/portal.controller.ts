import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PortalService } from '../service/portal.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('portal')
@UseGuards(JwtAuthGuard)
export class PortalController {
  private readonly logger = new Logger(PortalController.name);

  constructor(private readonly portalService: PortalService) {}

  @Get('/filter')
  // @Permission('PORTAL_GET_FILTER')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFilter(@Req() req: any) {
    this.logger.log(`Entering ${this.getFilter.name}`);
    const response = await this.portalService.getFilter(req.query);
    this.logger.log(
      `Leaving ${this.getFilter.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
