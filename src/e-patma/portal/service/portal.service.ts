import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as lodash from 'lodash';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class PortalService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getFilter(req: any) {
    const jenisDiktuk = await this.getJenisDiktuk();
    const jenis_diktuk = jenisDiktuk.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});
    const jenisDiktukFilter =
      await this.getSearchFilterByColumnName('jenis_diktuk_id');

    const kompetensiDiktuk = await this.getKompetensiDiktuk();
    const kompetensi_diktuk = kompetensiDiktuk.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const subKompetensiDiktuk = await this.getSubKompetensiDiktuk();
    const sub_kompetensi_diktuk = subKompetensiDiktuk.reduce(function (
      map,
      obj,
    ) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const subSubKompetensiDiktuk = await this.getSubSubKompetensiDiktuk();
    const sub_sub_kompetensi_diktuk = subSubKompetensiDiktuk.reduce(function (
      map,
      obj,
    ) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const tmpDik = await this.getTmpdik();
    const tmp_dik = tmpDik.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const statusKawin = await this.getStatusKawin();
    const status_kawin = statusKawin.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const agm = await this.getAgama();
    const agama = agm.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const prov = await this.getProvinsi();
    const provinsi = prov.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const kab = await this.getKabupaten();
    const kabupaten = kab.reduce(function (map, obj) {
      map[Number(obj.id)] = obj.nama;
      return map;
    }, {});

    const tahun: number[] = [];
    let year = await this.getLastTahunPenerimaan();
    for (let i = 0; i < 10; i++) {
      tahun.push(Number(year));
      year--;
    }
    const tahunFilter = await this.getSearchFilterByColumnName('ta_rim_diktuk');

    const tahun_penempatan: number[] = [];
    let year_pat = await this.getLastTahunPenempatan();
    for (let i = 0; i < 10; i++) {
      tahun_penempatan.push(Number(year_pat));
      year_pat--;
    }
    const tahunPatFilter =
      await this.getSearchFilterByColumnName('ta_pat_diktuk');

    const glbRim = await this.getGelombangPenerimaan();
    const gelombang = [];
    glbRim.forEach((glb) => {
      gelombang.push(glb.gelombang_rim_masuk_diktuk);
    });
    const gelombangFilter = await this.getSearchFilterByColumnName(
      'gelombang_rim_masuk_diktuk',
    );

    const glbPat = await this.getGelombangPenempatan();
    const gelombang_penempatan = [];
    glbPat.forEach((glb) => {
      gelombang_penempatan.push(glb.gelombang_pat_diktuk);
    });
    const gelombangPatFilter = await this.getSearchFilterByColumnName(
      'gelombang_pat_diktuk',
    );

    const jenis_kelamins = await this.getJenisKelamin();
    const jenis_kelamin = [];
    jenis_kelamins.forEach((jk) => {
      jenis_kelamin.push(jk.jenis_kelamin);
    });

    const kolom_siswa = new Map<number, string>();
    const kolom_rekrutmen = new Map<number, string>();
    const kolom_diktuk = new Map<number, string>();
    const filterSiswa = await this.getFilterSiswa();
    filterSiswa.forEach((filter) => {
      if (filter.is_siswa) {
        kolom_siswa[filter.id] = filter.nama;
        return;
      }

      if (filter.is_rekrutmen) {
        kolom_rekrutmen[filter.id] = filter.nama;
        return;
      }

      kolom_diktuk[filter.id] = filter.nama;
    });

    const searchFilter = await this.getFilterSearch();
    const filterSearch = searchFilter.reduce(function (map, obj) {
      const pushObj: any = obj;
      if (pushObj.column_name.includes('_id')) {
        switch (pushObj.column_name) {
          case 'asal_rim_polda_id':
          case 'asal_rim_polres_id':
          case 'unique_id':
          case 'pers_id':
          case 'kecamatan_id':
          case 'kelurahan_id':
            pushObj.type = 'text';
            break;
          default:
            pushObj.type = 'option';
            break;
        }
      } else {
        pushObj.type = 'text';
      }

      const category = lodash.snakeCase(pushObj.category_name);
      (map[category] = map[category] || []).push(pushObj);
      return map;
    }, {});

    const queryResult = {
      jenis_diktuk,
      kompetensi_diktuk,
      sub_kompetensi_diktuk,
      sub_sub_kompetensi_diktuk,
      tahun,
      tahun_penempatan,
      gelombang,
      gelombang_penempatan,
      jenis_kelamin,
      kolom_siswa,
      kolom_rekrutmen,
      kolom_diktuk,
      filter_search: filterSearch,
      tmp_dik,
      status_kawin,
      agama,
      provinsi,
      kabupaten,
      jenis_diktuk_filter_id: jenisDiktukFilter.id,
      tahun_filter: tahunFilter.id,
      tahun_penempatan_filter: tahunPatFilter.id,
      gelombang_filter: gelombangFilter.id,
      gelombang_penempatan_filter: gelombangPatFilter.id,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getJenisDiktuk() {
    return this.prisma.jenis_diktuk.findMany({
      where: {
        deleted_at: null,
      },
    });
  }

  async getTmpdik() {
    return this.prisma.tmpdik.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        deleted_at: null,
      },
    });
  }

  async getStatusKawin() {
    return this.prisma.status_kawin.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        deleted_at: null,
      },
    });
  }

  async getGelombangPenempatan() {
    return this.prisma.siswa.findMany({
      select: {
        gelombang_pat_diktuk: true,
      },
      distinct: ['gelombang_pat_diktuk'],
      where: {
        deleted_at: null,
      },
      orderBy: {
        gelombang_pat_diktuk: Prisma.SortOrder.asc,
      },
    });
  }

  async getJenisKelamin() {
    return this.prisma.siswa.findMany({
      select: {
        jenis_kelamin: true,
      },
      distinct: ['jenis_kelamin'],
      where: {
        deleted_at: null,
      },
    });
  }

  async getFilterSiswa() {
    return this.prisma.filter_siswa.findMany({
      select: {
        id: true,
        nama: true,
        is_siswa: true,
        is_rekrutmen: true,
      },
    });
  }

  async getFilterkelolaData(ids: number[]) {
    try {
      return await this.prisma.filter_siswa.findMany({
        where: {
          id: {
            in: ids,
          },
        },
      });
    } catch (e) {
      throw e;
    }
  }

  getDiktukInitial() {
    return [
      {
        title: 'AKPOL',
        label: 'Akademi Kepolisian',
      },
      {
        title: 'SIPSS',
        label: 'Sekolah Inspektur Polisi Sumber Sarjana',
      },
      {
        title: 'BINTARA',
        label: 'Brigadir Polisi',
      },
      {
        title: 'TAMTAMA',
        label: 'Bhayangkara Polisi',
      },
    ];
  }

  async getLastTahunPenerimaan() {
    try {
      const siswa = await this.prisma.siswa.findFirst({
        distinct: ['ta_rim_diktuk'],
        orderBy: {
          ta_rim_diktuk: Prisma.SortOrder.desc,
        },
        take: 1,
      });
      return siswa.ta_rim_diktuk;
    } catch (e) {
      throw e;
    }
  }

  async getLastTahunPenempatan() {
    try {
      const siswa = await this.prisma.siswa.findFirst({
        distinct: ['ta_pat_diktuk'],
        orderBy: {
          ta_pat_diktuk: Prisma.SortOrder.desc,
        },
        take: 1,
      });
      return Number(siswa.ta_pat_diktuk);
    } catch (e) {
      throw e;
    }
  }

  async getFilterSearch() {
    try {
      return await this.prisma.search_filter_siswa.findMany();
    } catch (e) {
      throw e;
    }
  }

  async getFilterSearchByIds(ids: number[]) {
    try {
      return await this.prisma.search_filter_siswa.findMany({
        where: {
          id: {
            in: ids,
          },
        },
        orderBy: {
          table_name: Prisma.SortOrder.asc,
        },
      });
    } catch (e) {
      throw e;
    }
  }

  async findSatuanByNamas(keywords: string[], isContain: boolean = false) {
    const where: Prisma.satuanWhereInput = {
      deleted_at: null,
    };

    switch (isContain) {
      case true:
        where.nama = {
          contains: keywords[0],
          mode: 'insensitive',
        };
        break;
      default:
        where.nama = {
          in: keywords,
        };
        break;
    }

    return this.prisma.satuan.findMany({
      select: {
        id: true,
        nama: true,
      },
      distinct: ['nama'],
      where: where,
    });
  }

  async getKompetensiDiktuk() {
    return this.prisma.kompetensi_diktuk.findMany();
  }

  async getSubKompetensiDiktuk() {
    return this.prisma.sub_kompetensi_diktuk.findMany();
  }

  async getSubSubKompetensiDiktuk() {
    return this.prisma.sub_sub_kompetensi_diktuk.findMany();
  }

  async getGelombangPenerimaan() {
    return this.prisma.siswa.findMany({
      select: {
        gelombang_rim_masuk_diktuk: true,
      },
      distinct: ['gelombang_rim_masuk_diktuk'],
      where: {
        deleted_at: null,
      },
      orderBy: {
        gelombang_rim_masuk_diktuk: Prisma.SortOrder.asc,
      },
    });
  }

  async getAgama() {
    return this.prisma.agama.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        deleted_at: null,
      },
    });
  }

  async getProvinsi() {
    return this.prisma.provinsi.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        deleted_at: null,
      },
    });
  }

  async getKabupaten() {
    return this.prisma.kabupaten.findMany({
      select: {
        id: true,
        nama: true,
      },
      where: {
        deleted_at: null,
      },
    });
  }

  async getSearchFilterByColumnName(columnName: string) {
    return this.prisma.search_filter_siswa.findFirst({
      where: {
        column_name: columnName,
      },
    });
  }
}
