import { Module } from '@nestjs/common';
import { PortalService } from './service/portal.service';
import { PortalController } from './controller/portal.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [PortalController],
  providers: [PortalService],
})
export class PortalModule {}
