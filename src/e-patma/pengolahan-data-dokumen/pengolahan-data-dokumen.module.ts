import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { StatistikController } from './controller/statistik.controller';
import { UploadDataController } from './controller/upload-data.controller';
import { KelolaDataController } from './controller/kelola-data.controller';
import { FileManagerController } from './controller/file-manager.controller';
import { StatistikService } from './service/statistik.service';
import { KelolaDataService } from './service/kelola-data.service';
import { PortalService } from '../portal/service/portal.service';
import { UploadDataService } from './service/upload-data.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { FileManagerService } from './service/file-manager.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [
    StatistikController,
    UploadDataController,
    KelolaDataController,
    FileManagerController,
  ],
  providers: [
    StatistikService,
    PortalService,
    KelolaDataService,
    UploadDataService,
    MinioService,
    FileManagerService,
  ],
})
export class PengolahanDataDokumenModule {}
