import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileManagerService } from '../service/file-manager.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('pengolahan-data-dto')
@UseGuards(JwtAuthGuard)
export class FileManagerController {
  private readonly logger = new Logger(FileManagerController.name);

  constructor(private readonly fileManagerService: FileManagerService) {}

  @Post('/file-manager')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_FILE_MANAGER')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFolders(@Req() req: any, @Body('path') path: string) {
    this.logger.log(`Entering ${this.getFolders.name} with path: ${path}`);
    const response = await this.fileManagerService.getFolders(req, path);
    this.logger.log(
      `Leaving ${this.getFolders.name} with path: ${path} and response: ${response}`,
    );
    return response;
  }

  @Post('/file-manager/download')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_FILE_MANAGER_CONTENT_DOWNLOAD')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async downloadFile(
    @Req() req: any,
    @Res() res: Response,
    @Body('path') path: string,
  ) {
    this.logger.log(`Entering ${this.downloadFile.name} with path: ${path}`);

    const [stat, stream] = await this.fileManagerService.getFileByPath(
      req,
      path,
    );

    res.setHeader(
      'Content-Type',
      stat.metaData['content-type'] || 'application/octet-stream',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename="${path.split('/').pop()}"`,
    );
    this.logger.log(`Leaving ${this.downloadFile.name} with path: ${path}`);

    stream.pipe(res);
  }

  @Post('/file-manager/upload')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_FILE_MANAGER_UPLOAD')
  // @UseGuards(SiswaJwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(200)
  async upload(
    @Req() req: any,
    @Body('path') path: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(`Entering ${this.upload.name} with path: ${path}`);
    const response = await this.fileManagerService.uploadToPath(
      req,
      path,
      file,
    );
    this.logger.log(
      `Leaving ${this.upload.name} with path: ${path} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/file-manager/delete')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_FILE_MANAGER_DELETE')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async delete(@Req() req: any, @Body('path') path: string) {
    this.logger.log(`Entering ${this.delete.name} with path: ${path}`);
    const response = await this.fileManagerService.deleteFromPath(req, path);
    this.logger.log(
      `Leaving ${this.delete.name} with path: ${path} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
