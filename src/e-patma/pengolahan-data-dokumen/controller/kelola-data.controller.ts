import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  FilterKelolaDto,
  FilterKelolaV2Dto,
} from '../dto/pengolahan-data-dokumen.dto';
import { Response } from 'express';
import { PortalService } from '../../portal/service/portal.service';
import { KelolaDataService } from '../service/kelola-data.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('pengolahan-data-dto')
@UseGuards(JwtAuthGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
export class KelolaDataController {
  private readonly logger = new Logger(KelolaDataController.name);

  constructor(
    private readonly portalService: PortalService,
    private readonly kelolaDataService: KelolaDataService,
  ) {}

  @Post('/kelola-data')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_KELOLA_DATA')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFilteredData(@Req() req: any, @Body() filter: FilterKelolaDto) {
    this.logger.log(
      `Entering ${this.getFilteredData.name} with filter: ${JSON.stringify(filter)}`,
    );
    const response = await this.kelolaDataService.getFilteredData(req, filter);
    this.logger.log(
      `Leaving ${this.getFilteredData.name} with filter: ${JSON.stringify(filter)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/kelola-data/download')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_KELOLA_DATA_DOWNLOAD')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFilteredDataDownload(
    @Req() req: any,
    @Res() res: Response,
    @Body() filter: FilterKelolaDto,
  ) {
    this.logger.log(
      `Entering ${this.getFilteredDataDownload.name} with filter: ${JSON.stringify(filter)}`,
    );
    const buffer = await this.kelolaDataService.getFilteredDataDownload(
      req,
      filter,
    );

    const filename = `Kelola Data Pengolahan Dokumen`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename="${filename}.xlsx"`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.getFilteredDataDownload.name} with filter: ${JSON.stringify(filter)} and buffer length: ${buffer.byteLength}`,
    );

    res.end(buffer);
  }

  @Post('/kelola-data-v2')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_KELOLA_DATA')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFilteredDataV2(@Req() req: any, @Body() filter: FilterKelolaV2Dto) {
    this.logger.log(
      `Entering ${this.getFilteredDataV2.name} with filter: ${JSON.stringify(filter)}`,
    );
    const response = await this.kelolaDataService.getFilteredDataV2(
      req,
      filter,
    );
    this.logger.log(
      `Leaving ${this.getFilteredDataV2.name} with filter: ${JSON.stringify(filter)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
