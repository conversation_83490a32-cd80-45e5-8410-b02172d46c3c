import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import {
  SiswaParamDto,
  UploadDataTemplateQueryDto,
} from '../dto/pengolahan-data-dokumen.dto';
import { PortalService } from '../../portal/service/portal.service';
import { UploadDataService } from '../service/upload-data.service';
import { PortalPipe } from '../../../core/pipe/portal.pipe';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('pengolahan-data-dto')
@UseGuards(JwtAuthGuard)
@UsePipes(new PortalPipe())
export class UploadDataController {
  private readonly logger = new Logger(UploadDataController.name);

  constructor(
    private readonly portalService: PortalService,
    private readonly uploadDataService: UploadDataService,
  ) {}

  @Get('/upload-data/template')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_GET_UPLOAD_DATA_TEMPLATE')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getUploadDataTemplate(
    @Req() req: any,
    @Res() res: Response,
    @Query(new ValidationPipe({ stopAtFirstError: true, transform: true }))
    query: UploadDataTemplateQueryDto,
  ) {
    this.logger.log(
      `Entering ${this.getUploadDataTemplate.name} with query ${JSON.stringify(query)}`,
    );

    const { buffer, filename } =
      await this.uploadDataService.getUploadDataTemplate(req, query);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename="${filename}.xlsx"`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    this.logger.log(
      `Leaving ${this.getUploadDataTemplate.name} with query: ${JSON.stringify(query)} and buffer length: ${buffer.byteLength}`,
    );

    res.end(buffer);
  }

  @Post('/upload-data/verify')
  // @Permission('PENGOLAHAN_DATA_DOKUMEN_POST_UPLOAD_DATA_VERIFY')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async submitDataSiswa(@Req() req: any, @Body() students: SiswaParamDto) {
    this.logger.log(
      `Entering ${this.submitDataSiswa.name} with body: ${JSON.stringify(students)}`,
    );
    const response = await this.uploadDataService.submitDataSiswa(
      req,
      students,
    );
    this.logger.log(
      `Leaving ${this.submitDataSiswa.name} with body: ${JSON.stringify(students)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
