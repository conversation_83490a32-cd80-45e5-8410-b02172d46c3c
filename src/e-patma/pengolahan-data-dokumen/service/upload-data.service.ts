import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import {
  Siswa,
  SiswaDB,
  SiswaDto,
  SiswaParamDto,
  UploadDataTemplateQueryDto,
} from '../dto/pengolahan-data-dokumen.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as exceljs from 'exceljs';
import { PortalService } from '../../portal/service/portal.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class UploadDataService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly portalService: PortalService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async submitDataSiswa(req: any, students: SiswaParamDto) {
    const queryResult: SiswaDB[] = [];
    const jenisDiktuk = await this.portalService.getJenisDiktuk();
    const kompetensiDiktuk = await this.portalService.getKompetensiDiktuk();
    const subKompetensiDiktuk =
      await this.portalService.getSubKompetensiDiktuk();
    const subSubKompetensiDiktuk =
      await this.portalService.getSubSubKompetensiDiktuk();
    const tmpdik = await this.portalService.getTmpdik();
    for (const student of students.data) {
      if (
        !jenisDiktuk.find(
          (jd) => jd.nama == student.jenis_diktuk?.toUpperCase(),
        )
      ) {
        throw new BadRequestException(
          `jenis_diktuk ${student.jenis_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findJenisDiktuk = jenisDiktuk.find(
        (jd) => jd.nama == student.jenis_diktuk.toUpperCase(),
      );

      if (
        !kompetensiDiktuk.find(
          (kd) =>
            kd.nama == student.kompetensi_diktuk?.toUpperCase() &&
            kd.diktuk_id == findJenisDiktuk.id,
        )
      ) {
        return new BadRequestException(
          `kompetensi_diktuk ${student.kompetensi_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findKompetensi = kompetensiDiktuk.find(
        (kd) =>
          kd.nama == student.kompetensi_diktuk.toUpperCase() &&
          kd.diktuk_id == findJenisDiktuk.id,
      );

      if (
        !subKompetensiDiktuk.find(
          (skd) =>
            skd.nama == student.sub_kompetensi_diktuk?.toUpperCase() &&
            skd.kompetensi_id == findKompetensi.id,
        )
      ) {
        throw new BadRequestException(
          `sub_kompetensi_diktuk ${student.sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findSubKompetensi = subKompetensiDiktuk.find(
        (skd) =>
          skd.nama == student.sub_kompetensi_diktuk.toUpperCase() &&
          skd.kompetensi_id == findKompetensi.id,
      );

      if (
        !subSubKompetensiDiktuk.find(
          (sskd) =>
            sskd.nama == student.sub_sub_kompetensi_diktuk?.toUpperCase() &&
            sskd.sub_kompetensi_id == findSubKompetensi.id,
        )
      ) {
        throw new BadRequestException(
          `sub_sub_kompetensi_diktuk ${student.sub_sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} dengan sub kompetensi diktuk ${findSubKompetensi.nama}, pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findSubSubKompetensi = subSubKompetensiDiktuk.find(
        (sskd) =>
          sskd.nama == student.sub_sub_kompetensi_diktuk.toUpperCase() &&
          sskd.sub_kompetensi_id == findSubKompetensi.id,
      );

      if (!tmpdik.find((t) => t.nama == student.tmp_dik?.toUpperCase())) {
        throw new BadRequestException(
          `tmp_dik ${student.tmp_dik} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }

      const satuanNamas = [student.asal_rim_polda];
      if (student.angket_1) {
        satuanNamas.push(student.angket_1.toUpperCase());
      }
      if (student.angket_2) {
        satuanNamas.push(student.angket_2.toUpperCase());
      }
      if (student.angket_3) {
        satuanNamas.push(student.angket_3.toUpperCase());
      }
      if (student.angket_4) {
        satuanNamas.push(student.angket_4.toUpperCase());
      }
      if (student.angket_5) {
        satuanNamas.push(student.angket_5.toUpperCase());
      }
      const satuan = await this.portalService.findSatuanByNamas(satuanNamas);
      if (
        !satuan.find((s) => s.nama == student.asal_rim_polda?.toUpperCase())
      ) {
        throw new BadRequestException(
          `asal_rim_polda ${student.asal_rim_polda} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }

      const update: SiswaDB = this.populateSiswa(student);
      update.tmp_dik_id = tmpdik.find(
        (t) => t.nama == student.tmp_dik.toUpperCase(),
      ).id;
      update.asal_rim_polda_id = satuan.find(
        (s) => s.nama == student.asal_rim_polda.toUpperCase(),
      ).id;
      update.nama_lengkap = student.nama?.toUpperCase();
      update.jenis_kelamin = student.jk?.toUpperCase();
      update.tempat_lahir = student.tmp_lahir?.toUpperCase();
      update.tanggal_lahir = student.tgl_lahir;
      update.jenis_diktuk_id = findJenisDiktuk.id;
      update.kompetensi_diktuk_id = findKompetensi.id;
      update.sub_kompetensi_diktuk_id = findSubKompetensi.id;
      update.sub_sub_kompetensi_diktuk_id = findSubSubKompetensi.id;
      update.angket_1 = satuan.find(
        (s) => s.nama == student.angket_1?.toUpperCase(),
      )?.id;
      update.angket_2 = satuan.find(
        (s) => s.nama == student.angket_2?.toUpperCase(),
      )?.id;
      update.angket_3 = satuan.find(
        (s) => s.nama == student.angket_3?.toUpperCase(),
      )?.id;
      update.angket_4 = satuan.find(
        (s) => s.nama == student.angket_4?.toUpperCase(),
      )?.id;
      update.angket_5 = satuan.find(
        (s) => s.nama == student.angket_5?.toUpperCase(),
      )?.id;
      update.satker_patma = satuan.find(
        (s) => s.nama == student.satker_patma?.toUpperCase(),
      )?.id;
      queryResult.push(update);
    }

    await this.uploadDataBulkUpdate(queryResult);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_PENGOLAHAN_DATA_DOKUMEN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getUploadDataTemplate(req: any, query: UploadDataTemplateQueryDto) {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Pengolahan Data Dokumen');
    const header = [
      'NO_URUT_ASALRIM',
      'PERS_ID',
      'UNIQUE_ID',
      'NO_REGISTRASI_ONLINE',
      'NO_UJIAN_POLDA',
      'NIK',
      'NAMA',
      'GELAR',
      'NAMA_DENGAN_GELAR',
      'JK',
      'TMP_LHR',
      'TGL_LHR',
      'NRP',
      'TA_RIM_DIKTUK',
      'GELOMBANG_RIM_MASUK_DIKTUK',
      'JENIS_DIKTUK',
      'KOMPETENSI_DIKTUK',
      'SUB_KOMPETENSI_DIKTUK',
      'SUB_SUB_KOMPETENSI_DIKTUK',
      'KET_JALUR_REKPRO',
      'TMP_DIK',
      'ASAL_RIM_POLDA',
      'TA_PAT_DIKTUK',
      'GELOMBANG_PAT_DIKTUK',
      'REKOM_PSI_1',
      'REKOM_PSI_2',
      'CATATAN_KHUSUS_REKOMPSI',
      'RIKKESWA_1',
      'RIKKESWA_2',
      'CATATAN_KHUSUS_RIKKESWA',
      'ANGKET_1',
      'ANGKET_2',
      'ANGKET_3',
      'ANGKET_4',
      'ANGKET_5',
      'URUT_NO_PATMA_GBG',
      'URUT_NO_PATMA_PER_SPN',
      'PATMA',
      'SUB_PATMA',
      'SUB_SUB_PATMA',
      'SATKER_PATMA',
      'JAB_PATMA',
      'PATMA_MABES_POLDA',
      'DATA_PENARIKAN_SATKER',
      'CATATAN_KHUSUS_PATMA',
      'NAMA_KASUBBAG',
      'NAMA_KABAG',
      'NRP_KABAG',
      'NAMA_KARO',
      'NAMA_AS_SDM',
      'NAMA_KAPOLRI',
      'PANGKAT',
      'KD_LAMP_PATMA',
      'NO_KEP_PATMA',
      'TMT_KEP_PATMA',
      'TMT_KEP_PANGKAT',
      'KESARJANAAN_MDS',
      'TH_MDS',
      'TH_NAIK_GAJI_MDS',
      'TMT_MDS',
      'URUT_NO_MDS',
      'MSK_GOL_GAJI_5',
      'GOL_GAJI_8',
      'MSK_GOL_GAJI_9',
      'GAJI',
      'TERBILANG_GAJI',
      'KET_KEP_A',
      'KET_KEP_B',
      'KET_KEP_C',
      'KET_KEP_D',
      'URUT_NO_KEPPRES',
      'NO_KEPPRES',
      'TMT_KEPPRES',
      'FILE_KEPPRES',
      'WIL_TMP_DIK',
      'WIL_PATMA',
      'WIL_ASAL_TUJUAN',
      'RENBUT_JALDIS',
    ];

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 13,
      };
    });

    const jenisDiktuk = await this.portalService.getJenisDiktuk();
    const kompetensiDiktuk = await this.portalService.getKompetensiDiktuk();
    const subKompetensiDiktuk =
      await this.portalService.getSubKompetensiDiktuk();
    const subSubKompetensiDiktuk =
      await this.portalService.getSubSubKompetensiDiktuk();
    const students = await this.uploadDataGetList(
      query.jenis_diktuk,
      query.tahun?.toString(),
      query.gelombang,
    );

    if (students.length > 0) {
      students.forEach((student) => {
        const findJenisDiktuk = jenisDiktuk.find(
          (jd) => jd.id == student.jenis_diktuk_id,
        );
        const findKompetensi = kompetensiDiktuk.find(
          (kd) =>
            kd.id == student.kompetensi_diktuk_id &&
            kd.diktuk_id == findJenisDiktuk.id,
        );
        const findSubKompetensi = subKompetensiDiktuk.find(
          (skd) =>
            skd.id == student.sub_kompetensi_diktuk_id &&
            skd.kompetensi_id == findKompetensi.id,
        );
        const findSubSubKompetensi = subSubKompetensiDiktuk.find(
          (sskd) =>
            sskd.id == student.sub_sub_kompetensi_diktuk_id &&
            sskd.sub_kompetensi_id == findSubKompetensi.id,
        );

        const row: any[] = [
          student.no_urut_asalrim,
          student.pers_id,
          student.unique_id,
          student.no_registrasi_online,
          student.no_ujian_polda,
          student.nik,
          student.nama_lengkap,
          student.gelar,
          student.nama_dengan_gelar,
          student.jenis_kelamin,
          student.tempat_lahir,
          student.tanggal_lahir,
          student.siswa_patma?.nrp,
          student.ta_rim_diktuk,
          student.gelombang_rim_masuk_diktuk,
          findJenisDiktuk.nama,
          findKompetensi.nama,
          findSubKompetensi.nama,
          findSubSubKompetensi.nama,
          student.ket_jalur_rekpro,
          student.tmpdik?.nama,
          student.siswa_asal_rim_polda?.nama,
          student.ta_pat_diktuk,
          student.gelombang_pat_diktuk,
          student.siswa_keswa_angket?.rekom_psi_1,
          student.siswa_keswa_angket?.rekom_psi_2,
          student.siswa_keswa_angket?.catatan_khusus_rekompsi,
          student.siswa_keswa_angket?.rikkeswa_1,
          student.siswa_keswa_angket?.rikkeswa_2,
          student.siswa_keswa_angket?.catatan_khusus_rikkeswa,
          student.siswa_keswa_angket?.siswa_keswa_angket_satuan_angket_1?.nama,
          student.siswa_keswa_angket?.siswa_keswa_angket_satuan_angket_2?.nama,
          student.siswa_keswa_angket?.siswa_keswa_angket_satuan_angket_3?.nama,
          student.siswa_keswa_angket?.siswa_keswa_angket_satuan_angket_4?.nama,
          student.siswa_keswa_angket?.siswa_keswa_angket_satuan_angket_5?.nama,
          student.siswa_patma?.urut_no_patma_gbg,
          student.siswa_patma?.urut_no_patma_per_spn,
          student.siswa_patma?.patma,
          student.siswa_patma?.sub_patma,
          student.siswa_patma?.sub_sub_patma,
          student.siswa_patma?.satker_patma_ref?.nama,
          student.siswa_patma?.jab_patma,
          student.siswa_patma?.patma_mabes_polda,
          student.siswa_patma?.data_penarikan_satker,
          student.siswa_patma?.catatan_khusus_patma,
          student.siswa_patma?.nama_kasubbag,
          student.siswa_patma?.nama_kabag,
          student.siswa_patma?.nrp_kabag,
          student.siswa_patma?.nama_karo,
          student.siswa_patma?.nama_as_sdm,
          student.siswa_patma?.nama_kapolri,
          student.siswa_patma?.pangkat,
          student.siswa_patma?.kd_lamp_patma,
          student.siswa_patma?.no_kep_patma,
          student.siswa_patma?.tmt_kep_patma,
          student.siswa_patma?.tmt_kep_pangkat,
          student.siswa_patma?.kesarjanaan_mds,
          student.siswa_patma?.th_mds,
          student.siswa_patma?.th_naik_gaji_mds,
          student.siswa_patma?.tmt_mds,
          student.siswa_patma?.urut_no_mds,
          student.siswa_patma?.msk_gol_gaji_5,
          student.siswa_patma?.gol_gaji_8,
          student.siswa_patma?.msk_gol_gaji_9,
          Number(student.siswa_patma?.gaji),
          student.siswa_patma?.terbilang_gaji,
          student.siswa_patma?.ket_kep_a,
          student.siswa_patma?.ket_kep_b,
          student.siswa_patma?.ket_kep_c,
          student.siswa_patma?.ket_kep_d,
          student.siswa_patma?.urut_no_keppres,
          student.siswa_patma?.no_keppres,
          student.siswa_patma?.tmt_keppres,
          student.siswa_patma?.file_keppres,
          student.siswa_patma?.wil_tmp_dik,
          student.siswa_patma?.wil_patma,
          student.siswa_patma?.wil_asal_tujuan,
          student.siswa_patma?.renbut_jaldis,
        ];

        worksheet.addRow(row);
      });
    }

    const buffer = await workbook.xlsx.writeBuffer();
    const filename = `Template Pengolahan Data Dokumen - ${jenisDiktuk.find((jd) => Number(jd.id) == query.jenis_diktuk)?.nama} - ${query?.tahun} - ${query?.gelombang}`;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_PENGOLAHAN_DATA_DOKUMEN_WRITE_EXCEL as ConstantLogType,
        message,
        header,
      ),
    );

    return { buffer, filename };
  }

  async uploadDataGetList(
    jenisDiktuk: number,
    tahun: string,
    gelombang: string,
  ) {
    let where = {};
    if (jenisDiktuk && jenisDiktuk > 0) {
      where = {
        ...where,
        jenis_diktuk_id: jenisDiktuk,
      };
    }

    if (tahun && tahun != '0') {
      where = {
        ...where,
        ta_pat_diktuk: tahun,
      };
    }

    if (gelombang && gelombang != '') {
      where = {
        ...where,
        gelombang_pat_diktuk: gelombang,
      };
    }

    return this.prisma.siswa.findMany({
      include: {
        siswa_asal_rim_polda: true,
        tmpdik: true,
        siswa_patma: {
          include: {
            satker_patma_ref: true,
          },
        },
        siswa_keswa_angket: {
          include: {
            siswa_keswa_angket_satuan_angket_1: true,
            siswa_keswa_angket_satuan_angket_2: true,
            siswa_keswa_angket_satuan_angket_3: true,
            siswa_keswa_angket_satuan_angket_4: true,
            siswa_keswa_angket_satuan_angket_5: true,
          },
        },
      },
      where: where,
      orderBy: {
        no_urut_asalrim: Prisma.SortOrder.asc,
      },
    });
  }

  async uploadDataBulkUpdate(students: SiswaDB[]) {
    return await this.prisma.$transaction(
      async (tx) => {
        for (const student of students) {
          const siswa = await tx.siswa.findFirst({
            where: {
              unique_id: student.unique_id,
            },
          });

          await tx.siswa.update({
            where: {
              id: siswa.id,
            },
            data: {
              no_urut_asalrim: student.no_urut_asalrim,
              no_registrasi_online: student.no_registrasi_online,
              no_ujian_polda: student.no_ujian_polda,
              nik: student.nik,
              nama_lengkap: student.nama_lengkap,
              gelar: student.gelar,
              nama_dengan_gelar: student.nama_dengan_gelar,
              jenis_kelamin: student.jenis_kelamin,
              tempat_lahir: student.tempat_lahir,
              tanggal_lahir: student.tanggal_lahir,
              ta_rim_diktuk: student.ta_rim_diktuk,
              gelombang_rim_masuk_diktuk: student.gelombang_rim_masuk_diktuk,
              jenis_diktuk_id: student.jenis_diktuk_id,
              kompetensi_diktuk_id: student.kompetensi_diktuk_id,
              sub_kompetensi_diktuk_id: student.sub_kompetensi_diktuk_id,
              sub_sub_kompetensi_diktuk_id:
                student.sub_sub_kompetensi_diktuk_id,
              tmp_dik_id: student.tmp_dik_id,
              asal_rim_polda_id: student.asal_rim_polda_id,
              ta_pat_diktuk: student.ta_pat_diktuk,
              gelombang_pat_diktuk: student.gelombang_pat_diktuk,
              ket_jalur_rekpro: student.ket_jalur_rekro,
              updated_at: new Date(),
            },
          });

          await tx.siswa_patma.update({
            where: {
              siswa_id: siswa.id,
            },
            data: {
              nrp: student.nrp,
              urut_no_patma_gbg: student.urut_no_patma_gbg,
              urut_no_patma_per_spn: student.urut_no_patma_per_spn,
              patma: student.patma,
              sub_patma: student.sub_patma,
              sub_sub_patma: student.sub_sub_patma,
              satker_patma: student.satker_patma,
              jab_patma: student.jab_patma,
              patma_mabes_polda: student.patma_mabes_polda,
              data_penarikan_satker: student.data_penarikan_satker,
              catatan_khusus_patma: student.catatan_khusus_patma,
              nama_kasubbag: student.nama_kasubbag,
              nama_kabag: student.nama_kabag,
              nrp_kabag: student.nrp_kabag,
              nama_karo: student.nama_karo,
              nama_as_sdm: student.nama_as_sdm,
              nama_kapolri: student.nama_kapolri,
              pangkat: student.pangkat,
              kd_lamp_patma: student.kd_lamp_patma,
              no_kep_patma: student.no_kep_patma,
              tmt_kep_patma: student.tmt_kep_patma,
              tmt_kep_pangkat: student.tmt_kep_pangkat,
              kesarjanaan_mds: student.kesarjanaan_mds,
              th_mds: student.th_mds,
              th_naik_gaji_mds: student.th_naik_gaji_mds,
              tmt_mds: student.tmt_mds,
              urut_no_mds: student.urut_no_mds,
              msk_gol_gaji_5: student.msk_gol_gaji_5,
              gol_gaji_8: student.gol_gaji_8,
              msk_gol_gaji_9: student.msk_gol_gaji_9,
              gaji: student.gaji,
              terbilang_gaji: student.terbilang_gaji,
              ket_kep_a: student.ket_kep_a,
              ket_kep_b: student.ket_kep_b,
              ket_kep_c: student.ket_kep_c,
              ket_kep_d: student.ket_kep_d,
              urut_no_keppres: student.urut_no_keppres,
              no_keppres: student.no_keppres,
              tmt_keppres: student.tmt_keppres,
              file_keppres: student.file_keppres,
              wil_tmp_dik: student.wil_tmp_dik,
              wil_patma: student.wil_patma,
              wil_asal_tujuan: student.wil_asal_tujuan,
              renbut_jaldis: student.renbut_jaldis,
              updated_at: new Date(),
            },
          });

          await tx.siswa_keswa_angket.create({
            data: {
              siswa_id: siswa.id,
              rekom_psi_1: student.rekom_psi_1,
              rekom_psi_2: student.rekom_psi_2,
              catatan_khusus_rekompsi: student.catatan_khusus_rekompsi,
              rikkeswa_1: student.rikkeswa_1,
              rikkeswa_2: student.rikkeswa_2,
              angket_1: student.angket_1,
              angket_2: student.angket_2,
              angket_3: student.angket_3,
              angket_4: student.angket_4,
              angket_5: student.angket_5,
            },
          });
        }

        return;
      },
      {
        timeout: 120000,
      },
    );
  }

  populateSiswa(siswa: SiswaDto): Siswa {
    return {
      no_urut_asalrim: siswa.no_urut_asalrim,
      unique_id: siswa.unique_id,
      no_registrasi_online: siswa.no_registrasi_online,
      no_ujian_polda: siswa.no_ujian_polda,
      nik: siswa.nik,
      nama_dengan_gelar: siswa.nama_dengan_gelar,
      ta_rim_diktuk: siswa.ta_rim_diktuk,
      gelombang_rim_masuk_diktuk:
        siswa.gelombang_rim_masuk_diktuk?.toUpperCase(),
      ta_pat_diktuk: siswa.ta_pat_diktuk,
      gelombang_pat_diktuk: siswa.gelombang_pat_diktuk?.toUpperCase(),
      rekom_psi_1: siswa.rekom_psi_1?.toUpperCase(),
      rekom_psi_2: siswa.rekom_psi_2?.toUpperCase(),
      catatan_khusus_rekompsi: siswa.catatan_khusus_rekompsi?.toUpperCase(),
      rikkeswa_1: siswa.rikkeswa_1,
      rikkeswa_2: siswa.rikkeswa_2,
      catatan_khusus_rikkeswa: siswa.catatan_khusus_rikkeswa?.toUpperCase(),
      urut_no_patma_gbg: siswa.urut_no_patma_gbg,
      urut_no_patma_per_spn: siswa.urut_no_patma_per_spn,
      patma: siswa.patma?.toUpperCase(),
      sub_patma: siswa.sub_patma?.toUpperCase(),
      sub_sub_patma: siswa.sub_sub_patma?.toUpperCase(),
      jab_patma: siswa.jab_patma,
      patma_mabes_polda: siswa.patma_mabes_polda,
      data_penarikan_satker: siswa.data_penarikan_satker,
      catatan_khusus_patma: siswa.catatan_khusus_patma,
      nama_kasubbag: siswa.nama_kasubbag,
      nama_kabag: siswa.nama_kabag,
      nrp_kabag: siswa.nrp_kabag,
      nama_karo: siswa.nama_karo,
      nama_as_sdm: siswa.nama_as_sdm,
      nama_kapolri: siswa.nama_kapolri,
      pangkat: siswa.pangkat,
      kd_lamp_patma: siswa.kd_lamp_patma,
      no_kep_patma: siswa.no_kep_patma,
      tmt_kep_patma: siswa.tmt_kep_patma,
      tmt_kep_pangkat: siswa.tmt_kep_pangkat,
      kesarjanaan_mds: siswa.kesarjanaan_mds,
      th_mds: siswa.th_mds,
      th_naik_gaji_mds: siswa.th_naik_gaji_mds,
      tmt_mds: siswa.tmt_mds,
      urut_no_mds: siswa.urut_no_mds,
      msk_gol_gaji_5: siswa.msk_gol_gaji_5,
      gol_gaji_8: siswa.gol_gaji_8,
      msk_gol_gaji_9: siswa.msk_gol_gaji_9,
      gaji: siswa.gaji,
      terbilang_gaji: siswa.terbilang_gaji,
      ket_kep_a: siswa.ket_kep_a,
      ket_kep_b: siswa.ket_kep_b,
      ket_kep_c: siswa.ket_kep_c,
      ket_kep_d: siswa.ket_kep_d,
      urut_no_keppres: siswa.urut_no_keppres,
      no_keppres: siswa.no_keppres,
      tmt_keppres: siswa.tmt_keppres,
      file_keppres: siswa.file_keppres,
      wil_tmp_dik: siswa.wil_tmp_dik,
      wil_patma: siswa.wil_patma,
      wil_asal_tujuan: siswa.wil_asal_tujuan,
      renbut_jaldis: siswa.renbut_jaldis,
    };
  }
}
