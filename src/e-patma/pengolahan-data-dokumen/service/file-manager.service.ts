import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import * as Minio from 'minio';
import stream from 'node:stream';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class FileManagerService {
  constructor(
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getFolders(req: any, path: string) {
    try {
      const folders = await this.getFolderByPath(path);
      const queryResult = [];
      folders.forEach((f) => {
        let path = f.name;
        let type = 'file';
        let title = f.name?.split('/').pop();
        if (f.prefix) {
          path = f.prefix;
          type = 'folder';
          const prefix = f.prefix?.split('/');
          title = prefix[prefix.length - 2];
        }

        const d = {
          title: title,
          type: type,
          path: path,
          ...f,
        };

        queryResult.push(d);
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_PENGOLAHAN_DATA_DOKUMEN_READ_MINIO as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      Logger.error('Error listing folder contents:', error);
      throw new InternalServerErrorException(error);
    }
  }

  async getFolderByPath(path: string): Promise<Minio.BucketItem[]> {
    return new Promise((resolve, reject) => {
      const items: Minio.BucketItem[] = [];
      const fullPath =
        path && path != ''
          ? path
          : `${this.minioService.defaultPath.slice(1)}e-patma/`;
      const stream = this.minioService
        .getClient()
        .listObjectsV2(this.minioService.defaultBucketName, fullPath);

      stream.on('data', (obj) => {
        if (!obj.name?.toLowerCase().includes('.keep')) {
          items.push(obj);
        }
      });
      stream.on('end', () => resolve(items));
      stream.on('error', (error) => reject(error));
    });
  }

  async getFileByPath(
    req: any,
    path: string,
  ): Promise<[Minio.BucketItemStat, stream.Readable]> {
    const fileStat = await this.minioService
      .getClient()
      .statObject(this.minioService.defaultBucketName, path);

    const fileStream = await this.minioService
      .getClient()
      .getObject(this.minioService.defaultBucketName, path);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_PENGOLAHAN_DATA_DOKUMEN_READ_MINIO as ConstantLogType,
        message,
        fileStat,
      ),
    );

    return [fileStat, fileStream];
  }

  async uploadToPath(req: any, path: string, file: Express.Multer.File) {
    const queryResult = await this.minioService
      .getClient()
      .putObject(
        this.minioService.defaultBucketName,
        `${path}${file.originalname}`,
        file.buffer,
        file.size,
        {
          'Content-Type': file.mimetype,
        },
      );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DISTRIBUSI_DATA_DOKUMEN_WRITE_MINIO as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: {
        versionId: queryResult.versionId,
      },
    };
  }

  async deleteFromPath(req: any, path: string) {
    try {
      const isFolder = path.endsWith('/');
      if (isFolder) {
        const objectsToDelete: string[] = [];
        const stream = this.minioService
          .getClient()
          .listObjectsV2(this.minioService.defaultBucketName, path, true);
        for await (const obj of stream) {
          objectsToDelete.push(obj.name);
        }

        if (objectsToDelete.length > 0) {
          await this.minioService
            .getClient()
            .removeObjects(
              this.minioService.defaultBucketName,
              objectsToDelete,
            );
        }

        return;
      } else {
        await this.minioService
          .getClient()
          .removeObject(this.minioService.defaultBucketName, path);
      }

      const queryResult = {
        deletedPath: path,
      };
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_PENGOLAHAN_DATA_DOKUMEN_WRITE_MINIO as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      Logger.error('Error delete file manager:', error);
      throw new InternalServerErrorException(error);
    }
  }
}
