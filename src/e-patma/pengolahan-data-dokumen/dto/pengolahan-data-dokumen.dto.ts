import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { PaginationDto } from '../../../core/dtos';

export class FilterDto {
  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  start_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  start_gelombang?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  end_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  end_gelombang?: string;
}

export class UploadDataTemplateQueryDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  jenis_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  tahun?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  gelombang?: string;
}

export class SiswaParamDto {
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => SiswaDto)
  data: SiswaDto[];
}

export class SiswaDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  no_urut_asalrim?: number;

  @Type(() => String)
  @IsOptional()
  pers_id?: string;

  @Type(() => String)
  @IsOptional()
  unique_id?: string;

  @Type(() => String)
  @IsOptional()
  no_registrasi_online?: string;

  @Type(() => String)
  @IsOptional()
  no_ujian_polda?: string;

  @Type(() => String)
  @IsOptional()
  nik?: string;

  @Type(() => String)
  @IsOptional()
  nama?: string;

  @Type(() => String)
  @IsOptional()
  gelar?: string;

  @Type(() => String)
  @IsOptional()
  nama_dengan_gelar?: string;

  @Type(() => String)
  @IsOptional()
  jk?: string;

  @Type(() => String)
  @IsOptional()
  tmp_lahir?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tgl_lahir?: Date;

  @Type(() => String)
  @IsOptional()
  nrp?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  ta_rim_diktuk?: number;

  @Type(() => String)
  @IsOptional()
  gelombang_rim_masuk_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  jenis_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  sub_sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  ket_jalur_rekpro?: string;

  @Type(() => String)
  @IsOptional()
  tmp_dik?: string;

  @Type(() => String)
  @IsOptional()
  asal_rim_polda?: string;

  @Type(() => String)
  @IsOptional()
  ta_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  gelombang_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  rekom_psi_1?: string;

  @Type(() => String)
  @IsOptional()
  rekom_psi_2?: string;

  @Type(() => String)
  @IsOptional()
  catatan_khusus_rekompsi?: string;

  @Type(() => String)
  @IsOptional()
  rikkeswa_1?: string;

  @Type(() => String)
  @IsOptional()
  rikkeswa_2?: string;

  @Type(() => String)
  @IsOptional()
  catatan_khusus_rikkeswa?: string;

  @Type(() => String)
  @IsOptional()
  angket_1?: string;

  @Type(() => String)
  @IsOptional()
  angket_2?: string;

  @Type(() => String)
  @IsOptional()
  angket_3?: string;

  @Type(() => String)
  @IsOptional()
  angket_4?: string;

  @Type(() => String)
  @IsOptional()
  angket_5?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  urut_no_patma_gbg?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  urut_no_patma_per_spn?: number;

  @Type(() => String)
  @IsOptional()
  patma?: string;

  @Type(() => String)
  @IsOptional()
  sub_patma?: string;

  @Type(() => String)
  @IsOptional()
  sub_sub_patma?: string;

  @Type(() => String)
  @IsOptional()
  satker_patma?: string;

  @Type(() => String)
  @IsOptional()
  jab_patma?: string;

  @Type(() => String)
  @IsOptional()
  patma_mabes_polda?: string;

  @Type(() => String)
  @IsOptional()
  data_penarikan_satker?: string;

  @Type(() => String)
  @IsOptional()
  catatan_khusus_patma?: string;

  @Type(() => String)
  @IsOptional()
  nama_kasubbag?: string;

  @Type(() => String)
  @IsOptional()
  nama_kabag?: string;

  @Type(() => String)
  @IsOptional()
  nrp_kabag?: string;

  @Type(() => String)
  @IsOptional()
  nama_karo?: string;

  @Type(() => String)
  @IsOptional()
  nama_as_sdm?: string;

  @Type(() => String)
  @IsOptional()
  nama_kapolri?: string;

  @Type(() => String)
  @IsOptional()
  pangkat?: string;

  @Type(() => String)
  @IsOptional()
  kd_lamp_patma?: string;

  @Type(() => String)
  @IsOptional()
  no_kep_patma?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tmt_kep_patma?: Date;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tmt_kep_pangkat?: Date;

  @Type(() => String)
  @IsOptional()
  kesarjanaan_mds?: string;

  @Type(() => String)
  @IsOptional()
  th_mds?: string;

  @Type(() => String)
  @IsOptional()
  th_naik_gaji_mds?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tmt_mds?: Date;

  @Type(() => String)
  @IsOptional()
  urut_no_mds?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  msk_gol_gaji_5?: Date;

  @Type(() => String)
  @IsOptional()
  gol_gaji_8?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  msk_gol_gaji_9?: Date;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  gaji?: number;

  @Type(() => String)
  @IsOptional()
  terbilang_gaji?: string;

  @Type(() => String)
  @IsOptional()
  ket_kep_a?: string;

  @Type(() => String)
  @IsOptional()
  ket_kep_b?: string;

  @Type(() => String)
  @IsOptional()
  ket_kep_c?: string;

  @Type(() => String)
  @IsOptional()
  ket_kep_d?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  urut_no_keppres?: number;

  @Type(() => String)
  @IsOptional()
  no_keppres?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tmt_keppres?: Date;

  @Type(() => String)
  @IsOptional()
  file_keppres?: string;

  @Type(() => String)
  @IsOptional()
  wil_tmp_dik?: string;

  @Type(() => String)
  @IsOptional()
  wil_patma?: string;

  @Type(() => String)
  @IsOptional()
  wil_asal_tujuan?: string;

  @Type(() => String)
  @IsOptional()
  renbut_jaldis?: string;
}

export interface Siswa {
  no_urut_asalrim?: number;
  pers_id?: string;
  unique_id?: string;
  no_registrasi_online?: string;
  no_ujian_polda?: string;
  nik?: string;
  gelar?: string;
  nama_dengan_gelar?: string;
  nrp?: string;
  ta_rim_diktuk?: number;
  gelombang_rim_masuk_diktuk?: string;
  ket_jalur_rekro?: string;
  ta_pat_diktuk?: string;
  gelombang_pat_diktuk?: string;
  rekom_psi_1?: string;
  rekom_psi_2?: string;
  catatan_khusus_rekompsi?: string;
  rikkeswa_1?: string;
  rikkeswa_2?: string;
  catatan_khusus_rikkeswa?: string;
  urut_no_patma_gbg?: number;
  urut_no_patma_per_spn?: number;
  patma?: string;
  sub_patma?: string;
  sub_sub_patma?: string;
  jab_patma?: string;
  patma_mabes_polda?: string;
  data_penarikan_satker?: string;
  catatan_khusus_patma?: string;
  nama_kasubbag?: string;
  nama_kabag?: string;
  nrp_kabag?: string;
  nama_karo?: string;
  nama_as_sdm?: string;
  nama_kapolri?: string;
  pangkat?: string;
  kd_lamp_patma?: string;
  no_kep_patma?: string;
  tmt_kep_patma?: Date;
  tmt_kep_pangkat?: Date;
  kesarjanaan_mds?: string;
  th_mds?: string;
  th_naik_gaji_mds?: string;
  tmt_mds?: Date;
  urut_no_mds?: string;
  msk_gol_gaji_5?: Date;
  gol_gaji_8?: string;
  msk_gol_gaji_9?: Date;
  gaji?: number;
  terbilang_gaji?: string;
  ket_kep_a?: string;
  ket_kep_b?: string;
  ket_kep_c?: string;
  ket_kep_d?: string;
  urut_no_keppres?: number;
  no_keppres?: string;
  tmt_keppres?: Date;
  file_keppres?: string;
  wil_tmp_dik?: string;
  wil_patma?: string;
  wil_asal_tujuan?: string;
  renbut_jaldis?: string;
}

export interface SiswaDB extends Siswa {
  nama_lengkap?: string;
  jenis_kelamin?: string;
  tempat_lahir?: string;
  tanggal_lahir?: Date;
  jenis_diktuk_id?: bigint;
  kompetensi_diktuk_id?: bigint;
  sub_kompetensi_diktuk_id?: bigint;
  sub_sub_kompetensi_diktuk_id?: bigint;
  tmp_dik_id?: bigint;
  asal_rim_polda_id?: bigint;
  angket_1?: bigint;
  angket_2?: bigint;
  angket_3?: bigint;
  angket_4?: bigint;
  angket_5?: bigint;
  satker_patma?: bigint;
}

export class FilterKelolaDto extends PaginationDto {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  jenis_diktuk?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_personel?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_rekrutmen?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_diktuk?: string[];

  @Type(() => String)
  @IsOptional()
  tahun?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang?: string;
}

export class FilterKelolaV2Dto extends PaginationDto {
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  data_column?: number[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FilterKelolaSearchDto)
  data_search?: FilterKelolaSearchDto[];
}

export class FilterKelolaSearchDto {
  @IsNumber()
  @Expose()
  id: number;

  @IsString()
  @IsNotEmpty()
  value: string;
}
