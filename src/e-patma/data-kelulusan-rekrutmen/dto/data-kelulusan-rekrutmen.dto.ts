import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Expose, Type } from 'class-transformer';
import { PaginationDto } from '../../../core/dtos';

export class FilterDto {
  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  start_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  start_gelombang?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  end_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  end_gelombang?: string;
}

export class SiswaParamDto {
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => SiswaDto)
  data: SiswaDto[];
}

export class SiswaDto {
  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  no_urut_asalrim?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  unique_id?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  no_registrasi_online?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  no_ujian_polda?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  nik?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelar?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  nama_dengan_gelar?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  ta_rim_diktuk?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang_rim_masuk_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_jalur_rekpro?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ta_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  jenis_pekerjaan?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  oap_orang_asli_papua?: string;

  @Type(() => String)
  @IsString()
  @IsOptional()
  n_kualitatif_rikmin_awal?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_rikmin_awal?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_rikmin_akhir?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_rikmin_akhir?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_rikkes_1?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_kuantitatif_rikkes_1?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  diagnosa_rikkes_1?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_rikkes_2?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_kuantitatif_rikkes_2?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  diagnosa_rikkes_2?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_rikpsi_1?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_kuantitatif_rikpsi_1?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_rikpsi_1?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_rikpsi_2?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  temuan_rikpsi_2?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_rikpsi_2?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_peng_u_akademik?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_wwsn_k_akademik?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_mtk_akademik?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_b_ing_akademik?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_gbgakhir_akademik?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_akademik?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  hasil_lari_12_m_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_a_lari_12_menit_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  hasil_pull_up_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_pull_up_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  hasil_situp_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_situp_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  hasil_pushup_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_pushup_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  hasil_shuttlerun_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  rata2_n_b_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_ab_ukj?: number;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  jarak_renang_ukj?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  waktu_renang_ukj?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_renang_ukj?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_antro_ukj?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_kuantitatif_antro_ukj?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  kelainan_antro_ukj?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_kuantitatif_akhir_ukj?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  n_kualitatif_akhir_ukj?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_ukj?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kualitatif_pmk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  n_kuantitatif_pmk: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  temuan_pmk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ket_pmk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  data_deteksi_dini_densus?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  prestasi_seleksi_rim?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  catatan_khusus_seleksi_rim?: string;

  @Type(() => String)
  @IsString()
  @IsNotEmpty()
  user_name?: string;

  @Type(() => String)
  @IsString()
  @IsNotEmpty()
  password?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  nama?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  jk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  tmp_lahir?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tgl_lahir?: Date;

  @Type(() => String)
  @IsOptional()
  @IsString()
  jenis_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  sub_sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  tmp_dik?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  asal_rim_polda?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  asal_rim_polres?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  status_kawin?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  ijazah_dikum_gun_seleksi_rim?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  n_shuttle_run_ukj?: number;
}

export interface Siswa {
  n_kuantitatif_rikkes_1: number;
  n_kuantitatif_akhir_ukj: number;
  n_mtk_akademik: number;
  ket_pmk: string;
  ket_ukj: string;
  nik: string;
  temuan_rikpsi_2: string;
  rata2_n_b_ukj: number;
  n_kualitatif_antro_ukj: string;
  n_kuantitatif_rikkes_2: number;
  waktu_renang_ukj: string;
  unique_id: string;
  ket_rikmin_awal: string;
  n_kuantitatif_antro_ukj: number;
  gelar: string;
  diagnosa_rikkes_2: string;
  diagnosa_rikkes_1: string;
  no_urut_asalrim: number;
  no_ujian_polda: string;
  n_kualitatif_rikmin_awal: string;
  n_ab_ukj: number;
  catatan_khusus_seleksi_rim: string;
  n_pushup_ukj: number;
  temuan_pmk: string;
  n_kualitatif_rikpsi_1: string;
  ket_akademik: string;
  n_kualitatif_rikpsi_2: string;
  n_a_lari_12_menit_ukj: number;
  gelombang_rim_masuk_diktuk: string;
  n_peng_u_akademik: number;
  n_kuantitatif_pmk: string;
  n_kualitatif_rikkes_1: string;
  n_kualitatif_rikkes_2: string;
  n_wwsn_k_akademik: number;
  hasil_situp_ukj: number;
  ta_pat_diktuk: string;
  hasil_shuttlerun_ukj: number;
  kelainan_antro_ukj: string;
  hasil_pull_up_ukj: number;
  prestasi_seleksi_rim: string;
  data_deteksi_dini_densus: string;
  n_situp_ukj: number;
  n_renang_ukj: number;
  hasil_lari_12_m_ukj: number;
  nama_dengan_gelar: string;
  ta_rim_diktuk: number;
  n_kuantitatif_rikpsi_1: number;
  no_registrasi_online: string;
  n_kualitatif_rikmin_akhir: string;
  hasil_pushup_ukj: number;
  ket_jalur_rekpro: string;
  n_kualitatif_pmk: string;
  oap_orang_asli_papua: string;
  gelombang_pat_diktuk: string;
  jenis_pekerjaan: string;
  n_b_ing_akademik: number;
  ket_rikpsi_2: string;
  n_gbgakhir_akademik: number;
  n_kualitatif_akhir_ukj: string;
  ket_rikpsi_1: string;
  n_pull_up_ukj: number;
  jarak_renang_ukj: number;
  ket_rikmin_akhir: string;
  password: string;
}

export interface SiswaDB extends Siswa {
  nama_lengkap?: string;
  jenis_kelamin?: string;
  tempat_lahir?: string;
  tanggal_lahir?: Date;
  jenis_diktuk_id?: bigint;
  kompetensi_diktuk_id?: bigint;
  sub_kompetensi_diktuk_id?: bigint;
  sub_sub_kompetensi_diktuk_id?: bigint;
  tmp_dik_id?: bigint;
  asal_rim_polda_id?: bigint;
  asal_rim_polres_id?: bigint;
  status_kawin_id?: bigint;
  ijazah_dikum_seleksi_rim?: string;
  n_shuttelerun_ukj?: number;
  username?: string;
}

export class FilterKelolaDto extends PaginationDto {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  jenis_diktuk?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_personel?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_rekrutmen?: string[];

  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Expose()
  tahun?: number;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang?: string;
}

export class FilterKelolaV2Dto extends PaginationDto {
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  data_column?: number[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FilterKelolaSearchDto)
  data_search?: FilterKelolaSearchDto[];
}

export class FilterKelolaSearchDto {
  @IsNumber()
  @Expose()
  id: number;

  @IsString()
  @IsNotEmpty()
  value: string;
}
