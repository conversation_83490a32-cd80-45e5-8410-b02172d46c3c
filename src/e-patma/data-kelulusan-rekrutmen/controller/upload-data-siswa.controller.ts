import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { SiswaParamDto } from '../dto/data-kelulusan-rekrutmen.dto';
import { PortalService } from '../../portal/service/portal.service';
import { UploadDataSiswaService } from '../service/upload-data-siswa.service';
import { PortalPipe } from '../../../core/pipe/portal.pipe';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('data-kelulusan-rekrutmen')
@UseGuards(JwtAuthGuard)
@UsePipes(new PortalPipe())
export class UploadDataSiswaController {
  private readonly logger = new Logger(UploadDataSiswaController.name);

  constructor(
    private readonly portalService: PortalService,
    private readonly uploadDataSiswaService: UploadDataSiswaService,
  ) {}

  @Get('/upload-data-siswa/template/:diktuk')
  // @Permission('DATA_KELULUSAN_REKRUTMEN_GET_UPLOAD_DATA_SISWA_TEMPLATE')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getRekapUploadData(
    @Req() req: any,
    @Res() res: Response,
    @Param('diktuk') diktuk: number,
  ) {
    this.logger.log(
      `Entering ${this.getRekapUploadData.name} with diktuk ${diktuk}`,
    );
    const { buffer, filename } =
      await this.uploadDataSiswaService.getRekapUploadData(req, diktuk);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename="${filename}.xlsx"`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    this.logger.log(
      `Leaving ${this.getRekapUploadData.name} with diktuk ${diktuk} and buffer length ${buffer.byteLength}`,
    );
    res.end(buffer);
  }

  @Post('/upload-data-siswa/submit')
  // @Permission('DATA_KELULUSAN_REKRUTMEN_POST_UPLOAD_DATA_SISWA_SUBMIT')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async submitDataSiswa(
    @Req() req: any,
    @Body(new ValidationPipe({ stopAtFirstError: true, transform: true }))
    students: SiswaParamDto,
  ) {
    this.logger.log(
      `Entering ${this.submitDataSiswa.name} with students ${JSON.stringify(students)}`,
    );
    const response = await this.uploadDataSiswaService.submitDataSiswa(
      req,
      students,
    );
    this.logger.log(
      `Leaving ${this.submitDataSiswa.name} with students ${JSON.stringify(students)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
