import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { FilterDto } from '../dto/data-kelulusan-rekrutmen.dto';
import { RekapDataSiswaService } from '../service/rekap-data-siswa.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('data-kelulusan-rekrutmen')
@UseGuards(JwtAuthGuard)
export class RekapDataSiswaController {
  private readonly logger = new Logger(RekapDataSiswaController.name);

  constructor(private readonly rekapDataSiswaService: RekapDataSiswaService) {}

  @Get('/rekap-data-siswa/rekap-upload-data')
  // @Permission('DATA_KELULUSAN_REKRUTMEN_GET_REKAP_DATA_SISWA_REKAP_UPLOAD_DATA')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getRekapUploadData(@Req() req: any, @Query() param: FilterDto) {
    this.logger.log(
      `Entering ${this.getRekapUploadData.name} with param ${JSON.stringify(param)}`,
    );
    const response = await this.rekapDataSiswaService.getRekapUploadData(
      req,
      param,
    );
    this.logger.log(
      `Leaving ${this.getRekapUploadData.name} with param ${JSON.stringify(param)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
