import { Modu<PERSON> } from '@nestjs/common';
import { UploadDataSiswaController } from './controller/upload-data-siswa.controller';
import { StatistikController } from './controller/statistik.controller';
import { RekapDataSiswaController } from './controller/rekap-data-siswa.controller';
import { KelolaDataController } from './controller/kelola-data.controller';
import { FileManagerController } from './controller/file-manager.controller';
import { PortalService } from '../portal/service/portal.service';
import { StatistikService } from './service/statistik.service';
import { RekapDataSiswaService } from './service/rekap-data-siswa.service';
import { UploadDataSiswaService } from './service/upload-data-siswa.service';
import { KelolaDataService } from './service/kelola-data.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { FileManagerService } from './service/file-manager.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [
    StatistikController,
    RekapDataSiswaController,
    UploadDataSiswaController,
    KelolaDataController,
    FileManagerController,
  ],
  providers: [
    PortalService,
    StatistikService,
    RekapDataSiswaService,
    UploadDataSiswaService,
    KelolaDataService,
    MinioService,
    FileManagerService,
  ],
})
export class DataKelulusanRekrutmenModule {}
