import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { PortalService } from '../../portal/service/portal.service';

@Injectable()
export class StatistikService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly portalService: PortalService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getRekapDataSeleksi(req: any, param: any) {
    if (param.start_year > param.end_year) {
      throw new BadRequestException(
        'tahun mulai tidak boleh lebih besar dari tahun akhir',
      );
    }

    const queryParam: {
      startYear: string;
      startGel: string;
      endYear: string;
      endGel: string;
    } = {
      startYear: param.start_year?.toString(),
      startGel: param.start_gelombang,
      endYear: param.end_year?.toString(),
      endGel: param.end_gelombang,
    };
    const queryData =
      await this.statistikGetRekapDataSeleksiWithFilter(queryParam);

    const queryResult: any[] = this.portalService.getDiktukInitial();
    let indexData: number;
    queryData.map((item) => {
      indexData = queryResult.findIndex((d) => d.title == item.nama);
      queryResult[indexData].pria = item.pria;
      queryResult[indexData].wanita = item.wanita;
      queryResult[indexData].total = item.total;
      queryResult[indexData].persen_pria =
        `${((Number(item.pria) / Number(item.total)) * 100).toFixed(2)} %`;
      queryResult[indexData].persen_wanita =
        `${((Number(item.wanita) / Number(item.total)) * 100).toFixed(2)} %`;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_KELULUSAN_REKRUTMEN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getStatistik(req: any) {
    const year = await this.portalService.getLastTahunPenerimaan();
    const queryResult: any[] = this.portalService.getDiktukInitial();
    queryResult.forEach((_d, idx) => {
      queryResult[idx].kelulusan = [];
      for (let tahun = Number(year) - 4; tahun <= year; tahun++) {
        queryResult[idx].kelulusan.push({
          tahun: tahun,
          total_gelombang: 0,
          gelombang: [],
        });
      }
    });

    const getData = await this.statistikGetLast5Year(Number(year));
    let gelombangBundle = [];
    let total = 0;
    let title = '';
    let tahun = '';
    let indexData: number;
    let indexKelulusan: number;
    getData.map((item) => {
      if (title == '') {
        title = item.nama;
        tahun = item.ta_rim_diktuk;

        const gelombang = {
          title: item.gelombang_rim_masuk_diktuk,
          pria: item.pria,
          wanita: item.wanita,
          total: item.pria + item.wanita,
        };
        total = Number(gelombang.total);
        gelombangBundle.push(gelombang);
        return;
      }

      if (title == item.nama) {
        if (tahun == item.ta_rim_diktuk) {
          const gelombang = {
            title: item.gelombang_rim_masuk_diktuk,
            pria: item.pria,
            wanita: item.wanita,
            total: item.pria + item.wanita,
          };
          total += Number(gelombang.total);
          gelombangBundle.push(gelombang);
          return;
        }

        indexData = queryResult.findIndex((d) => d.title == title);
        indexKelulusan = queryResult[indexData].kelulusan.findIndex(
          (k) => k.tahun == tahun,
        );
        queryResult[indexData].kelulusan[indexKelulusan].total_gelombang =
          total;
        queryResult[indexData].kelulusan[indexKelulusan].gelombang =
          gelombangBundle;

        tahun = item.ta_rim_diktuk;
        const gelombang = {
          title: item.gelombang_rim_masuk_diktuk,
          pria: item.pria,
          wanita: item.wanita,
          total: item.pria + item.wanita,
        };
        total = Number(gelombang.total);
        gelombangBundle = [gelombang];
        return;
      }

      indexData = queryResult.findIndex((d) => d.title == title);
      indexKelulusan = queryResult[indexData].kelulusan.findIndex(
        (k) => k.tahun == tahun,
      );
      queryResult[indexData].kelulusan[indexKelulusan].total_gelombang = total;
      queryResult[indexData].kelulusan[indexKelulusan].gelombang =
        gelombangBundle;

      title = item.nama;
      tahun = item.ta_rim_diktuk;
      const gelombang = {
        title: item.gelombang_rim_masuk_diktuk,
        pria: item.pria,
        wanita: item.wanita,
        total: item.pria + item.wanita,
      };
      total = Number(gelombang.total);
      gelombangBundle = [gelombang];
    });

    indexData = queryResult.findIndex((d) => d.title == title);
    indexKelulusan = queryResult[indexData].kelulusan.findIndex(
      (k) => k.tahun == tahun,
    );
    queryResult[indexData].kelulusan[indexKelulusan].total_gelombang = total;
    queryResult[indexData].kelulusan[indexKelulusan].gelombang =
      gelombangBundle;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_KELULUSAN_REKRUTMEN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async statistikGetLast5Year(year: number) {
    try {
      const q = Prisma.sql`
          select j.nama,
                 s.ta_rim_diktuk,
                 s.gelombang_rim_masuk_diktuk,
                 count(case when s.jenis_kelamin = 'PRIA' then 1 end)   as pria,
                 count(case when s.jenis_kelamin = 'WANITA' then 1 end) as wanita,
                 count(s.jenis_kelamin)                                 as total
          from siswa s
                   inner join jenis_diktuk j on s.jenis_diktuk_id = j.id
          where s.ta_rim_diktuk >= ${year - 4}
            and s.deleted_at is null
          group by j.nama, s.ta_rim_diktuk, s.gelombang_rim_masuk_diktuk
          order by j.nama, s.ta_rim_diktuk, s.gelombang_rim_masuk_diktuk
      `;

      return await this.prisma.$queryRaw<
        {
          nama: string;
          gelombang_rim_masuk_diktuk: string;
          ta_rim_diktuk: string;
          pria: number;
          wanita: number;
          total: number;
        }[]
      >(q);
    } catch (e) {
      throw e;
    }
  }

  async statistikGetRekapDataSeleksiWithFilter(param: {
    startYear: string;
    startGel: string;
    endYear: string;
    endGel: string;
  }) {
    try {
      let where = 'where s.deleted_at is null';
      let whereYear: string;
      if (
        param.startYear &&
        param.startYear != '' &&
        param.endYear &&
        param.endYear != ''
      ) {
        whereYear = `s.ta_rim_diktuk between '${param.startYear}' and '${param.endYear}'`;
      } else {
        whereYear =
          param.startYear && param.startYear != ''
            ? `s.ta_rim_diktuk >= '${param.startYear}'`
            : param.endYear && param.endYear != ''
              ? `s.ta_rim_diktuk <= '${param.endYear}'`
              : '';
      }

      if (whereYear != '') {
        where += ` and ${whereYear}`;
      }

      let whereGel: string;
      if (
        param.startGel &&
        param.startGel != '' &&
        param.endGel &&
        param.endGel != ''
      ) {
        whereGel = `s.gelombang_rim_masuk_diktuk between '${param.startGel}' and '${param.endGel}'`;
      } else {
        whereGel =
          param.startGel && param.startGel != ''
            ? `s.gelombang_rim_masuk_diktuk >= '${param.startGel}'`
            : param.endGel && param.endGel != ''
              ? `s.gelombang_rim_masuk_diktuk <= '${param.endGel}'`
              : '';
      }

      if (whereGel != '') {
        where += ` and ${whereGel}`;
      }

      const query = `
          select j.nama,
                 count(case when s.jenis_kelamin = 'PRIA' then 1 end)   as pria,
                 count(case when s.jenis_kelamin = 'WANITA' then 1 end) as wanita,
                 count(s.jenis_kelamin)                                 as total
          from siswa s
                   inner join jenis_diktuk j on s.jenis_diktuk_id = j.id
              ${where}
          group by j.nama
          order by j.nama
      `;
      return await this.prisma.$queryRawUnsafe<
        {
          nama: string;
          pria: number;
          wanita: number;
          total: number;
        }[]
      >(query);
    } catch (e) {
      throw e;
    }
  }
}
