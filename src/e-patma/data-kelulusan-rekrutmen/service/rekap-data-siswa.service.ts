import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { FilterDto } from '../dto/data-kelulusan-rekrutmen.dto';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class RekapDataSiswaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getRekapUploadData(req: any, param: FilterDto) {
    if (param.start_year > param.end_year) {
      throw new BadRequestException(
        'tahun mulai tidak boleh lebih besar dari tahun akhir',
      );
    }

    const queryParam: {
      startYear: string;
      startGel: string;
      endYear: string;
      endGel: string;
    } = {
      startYear: param.start_year?.toString(),
      startGel: param.start_gelombang,
      endYear: param.end_year?.toString(),
      endGel: param.end_gelombang,
    };
    const getData = await this.rekapDataSiswaGetRekapUploadData(queryParam);

    const queryResult = [];
    let details = [];
    let total = 0;
    let totalPria = 0;
    let totalWanita = 0;
    let title = '';
    let no = 1;
    getData.map((item) => {
      if (title == '') {
        title = item.nama;
        total += Number(item.total);
        totalPria += Number(item.pria);
        totalWanita += Number(item.wanita);
        details.push({
          no: no,
          asal_rim_polda: item.nama_rim_polda,
          pria: item.pria,
          wanita: item.wanita,
          total: item.total,
        });
        no++;
        return;
      }

      if (title == item.nama) {
        total += Number(item.total);
        totalPria += Number(item.pria);
        totalWanita += Number(item.wanita);
        details.push({
          no: no,
          asal_rim_polda: item.nama_rim_polda,
          pria: item.pria,
          wanita: item.wanita,
          total: item.total,
        });
        no++;
        return;
      }

      queryResult.push({
        title: title,
        pria: totalPria,
        wanita: totalWanita,
        total: total,
        persen_pria: `${((totalPria / total) * 100).toFixed(2)} %`,
        persen_wanita: `${((totalWanita / total) * 100).toFixed(2)} %`,
        details: details,
      });

      no = 1;
      title = item.nama;
      details = [
        {
          no: no,
          asal_rim_polda: item.nama_rim_polda,
          pria: item.pria,
          wanita: item.wanita,
          total: item.total,
        },
      ];
      total = Number(item.total);
      totalPria = Number(item.pria);
      totalWanita = Number(item.wanita);
      no++;
    });

    queryResult.push({
      title: title,
      pria: totalPria,
      wanita: totalWanita,
      total: total,
      persen_pria: `${((totalPria / total) * 100).toFixed(2)} %`,
      persen_wanita: `${((totalWanita / total) * 100).toFixed(2)} %`,
      details: details,
    });
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_KELULUSAN_REKRUTMEN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async rekapDataSiswaGetRekapUploadData(param: {
    startYear: string;
    startGel: string;
    endYear: string;
    endGel: string;
  }) {
    try {
      let where = 'where s.deleted_at is null';
      let whereYear: string;
      if (
        param.startYear &&
        param.startYear != '' &&
        param.endYear &&
        param.endYear != ''
      ) {
        whereYear = `s.ta_rim_diktuk between '${param.startYear}' and '${param.endYear}'`;
      } else {
        whereYear =
          param.startYear && param.startYear != ''
            ? `s.ta_rim_diktuk >= '${param.startYear}'`
            : param.endYear && param.endYear != ''
              ? `s.ta_rim_diktuk <= '${param.endYear}'`
              : '';
      }

      if (whereYear != '') {
        where += ` and ${whereYear}`;
      }

      let whereGel: string;
      if (
        param.startGel &&
        param.startGel != '' &&
        param.endGel &&
        param.endGel != ''
      ) {
        whereGel = `s.gelombang_rim_masuk_diktuk between '${param.startGel}' and '${param.endGel}'`;
      } else {
        whereGel =
          param.startGel && param.startGel != ''
            ? `s.gelombang_rim_masuk_diktuk >= '${param.startGel}'`
            : param.endGel && param.endGel != ''
              ? `s.gelombang_rim_masuk_diktuk <= '${param.endGel}'`
              : '';
      }

      if (whereGel != '') {
        where += ` and ${whereGel}`;
      }

      const query = `
          select j.nama,
                 st.nama                                                   nama_rim_polda,
                 count(case when s.jenis_kelamin = 'PRIA' then 1 end)   as pria,
                 count(case when s.jenis_kelamin = 'WANITA' then 1 end) as wanita,
                 count(s.jenis_kelamin)                                 as total
          from siswa s
                   inner join jenis_diktuk j on s.jenis_diktuk_id = j.id
                   inner join satuan st on s.asal_rim_polda_id = st.id
              ${where}
          group by j.nama, st.nama
          order by j.nama, st.nama
      `;
      return await this.prisma.$queryRawUnsafe<
        {
          nama: string;
          nama_rim_polda: string;
          pria: number;
          wanita: number;
          total: number;
        }[]
      >(query);
    } catch (e) {
      throw e;
    }
  }
}
