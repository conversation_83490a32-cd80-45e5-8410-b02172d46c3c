import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import {
  Siswa,
  SiswaDB,
  SiswaDto,
  SiswaParamDto,
} from '../dto/data-kelulusan-rekrutmen.dto';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as exceljs from 'exceljs';
import { PortalService } from '../../portal/service/portal.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UploadDataSiswaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly portalService: PortalService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async submitDataSiswa(req: any, students: SiswaParamDto) {
    try {
      const queryResult: SiswaDB[] = [];
      const jenisDiktuk = await this.portalService.getJenisDiktuk();
      const kompetensiDiktuk = await this.portalService.getKompetensiDiktuk();
      const subKompetensiDiktuk =
        await this.portalService.getSubKompetensiDiktuk();
      const subSubKompetensiDiktuk =
        await this.portalService.getSubSubKompetensiDiktuk();
      const tmpdik = await this.portalService.getTmpdik();
      const statusKawin = await this.portalService.getStatusKawin();
      const folders = [];
      for (const student of students.data) {
        if (
          !jenisDiktuk.find(
            (jd) => jd.nama == student.jenis_diktuk?.toUpperCase(),
          )
        ) {
          return new BadRequestException(
            `jenis_diktuk ${student.jenis_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }
        const findJenisDiktuk = jenisDiktuk.find(
          (jd) => jd.nama == student.jenis_diktuk.toUpperCase(),
        );

        if (
          !kompetensiDiktuk.find(
            (kd) =>
              kd.nama == student.kompetensi_diktuk?.toUpperCase() &&
              kd.diktuk_id == findJenisDiktuk.id,
          )
        ) {
          return new BadRequestException(
            `kompetensi_diktuk ${student.kompetensi_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }
        const findKompetensi = kompetensiDiktuk.find(
          (kd) =>
            kd.nama == student.kompetensi_diktuk.toUpperCase() &&
            kd.diktuk_id == findJenisDiktuk.id,
        );

        if (
          !subKompetensiDiktuk.find(
            (skd) =>
              skd.nama == student.sub_kompetensi_diktuk?.toUpperCase() &&
              skd.kompetensi_id == findKompetensi.id,
          )
        ) {
          return new BadRequestException(
            `sub_kompetensi_diktuk ${student.sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }
        const findSubKompetensi = subKompetensiDiktuk.find(
          (skd) =>
            skd.nama == student.sub_kompetensi_diktuk.toUpperCase() &&
            skd.kompetensi_id == findKompetensi.id,
        );

        if (
          !subSubKompetensiDiktuk.find(
            (sskd) =>
              sskd.nama == student.sub_sub_kompetensi_diktuk?.toUpperCase() &&
              sskd.sub_kompetensi_id == findSubKompetensi.id,
          )
        ) {
          return new BadRequestException(
            `sub_sub_kompetensi_diktuk ${student.sub_sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} dengan sub kompetensi diktuk ${findSubKompetensi.nama}, pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }
        const findSubSubKompetensi = subSubKompetensiDiktuk.find(
          (sskd) =>
            sskd.nama == student.sub_sub_kompetensi_diktuk.toUpperCase() &&
            sskd.sub_kompetensi_id == findSubKompetensi.id,
        );

        if (!tmpdik.find((t) => t.nama == student.tmp_dik?.toUpperCase())) {
          return new BadRequestException(
            `tmp_dik ${student.tmp_dik} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }

        if (
          !statusKawin.find(
            (s) => s.nama == student.status_kawin?.toUpperCase(),
          )
        ) {
          return new BadRequestException(
            `status_kawin ${student.status_kawin} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
          );
        }

        let satuan: {
          id: bigint;
          nama: string;
        }[];
        if (student.asal_rim_polda || student.asal_rim_polres) {
          const satuanNamas: string[] = [];
          if (student.asal_rim_polda) {
            satuanNamas.push(student.asal_rim_polda.toUpperCase());
          }
          if (student.asal_rim_polres) {
            satuanNamas.push(student.asal_rim_polres.toUpperCase());
          }

          satuan = await this.portalService.findSatuanByNamas(satuanNamas);

          if (student.asal_rim_polda) {
            if (
              !satuan.find(
                (s) => s.nama == student.asal_rim_polda.toUpperCase(),
              )
            ) {
              return new BadRequestException(
                `asal_rim_polda ${student.asal_rim_polda} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
              );
            }
          }
          if (student.asal_rim_polres) {
            if (
              !satuan.find(
                (s) => s.nama == student.asal_rim_polres.toUpperCase(),
              )
            ) {
              return new BadRequestException(
                `asal_rim_polres ${student.asal_rim_polres} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
              );
            }
          }
        }

        const insert: SiswaDB = this.populateSiswa(student);
        insert.password = await bcrypt.hash(insert.password, process.env.SALT);
        insert.tmp_dik_id = tmpdik.find(
          (t) => t.nama == student.tmp_dik.toUpperCase(),
        ).id;
        insert.asal_rim_polda_id = satuan.find(
          (s) => s.nama == student.asal_rim_polda.toUpperCase(),
        ).id;
        insert.asal_rim_polres_id = satuan.find(
          (s) => s.nama == student.asal_rim_polres.toUpperCase(),
        ).id;
        insert.status_kawin_id = statusKawin.find(
          (s) => s.nama == student.status_kawin.toUpperCase(),
        ).id;
        insert.nama_lengkap = student.nama?.toUpperCase();
        insert.jenis_kelamin = student.jk?.toUpperCase();
        insert.tempat_lahir = student.tmp_lahir?.toUpperCase();
        insert.tanggal_lahir = new Date(student.tgl_lahir);
        insert.jenis_diktuk_id = findJenisDiktuk.id;
        insert.kompetensi_diktuk_id = findKompetensi.id;
        insert.sub_kompetensi_diktuk_id = findSubKompetensi.id;
        insert.sub_sub_kompetensi_diktuk_id = findSubSubKompetensi.id;
        insert.ijazah_dikum_seleksi_rim =
          student.ijazah_dikum_gun_seleksi_rim?.toUpperCase();
        insert.n_shuttelerun_ukj = student.n_shuttle_run_ukj;
        insert.username = student.user_name;
        queryResult.push(insert);

        let path = `portal 1/${student.ta_rim_diktuk}`;
        let pathGel = `/${student.jenis_diktuk} ${student.gelombang_rim_masuk_diktuk}`;
        if (student.jenis_diktuk == 'AKPOL') {
          pathGel = `${student.jenis_diktuk}`;
        }
        folders.push({
          path: `${path}${pathGel}/${student.asal_rim_polda}/Personel`,
          unique_id: student.unique_id,
        });

        path = `portal 2/${student.ta_pat_diktuk}`;
        pathGel = `/${student.jenis_diktuk} ${student.gelombang_pat_diktuk}`;
        if (student.jenis_diktuk == 'AKPOL') {
          pathGel = `${student.jenis_diktuk}`;
        }
        folders.push({
          path: `${path}${pathGel}/${student.asal_rim_polda}/Personel`,
          unique_id: student.unique_id,
        });
      }

      await this.uploadDataSiswaBulkInsert(queryResult);
      this.createFolders(folders);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_DATA_KELULUSAN_REKRUTMEN_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      Logger.error(error, error.message);
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: error.message,
      };
    }
  }

  async getRekapUploadData(req: any, diktuk: number) {
    const jenisDiktuk = await this.portalService.getJenisDiktuk();
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Data Kelulusan Rekrutmen');
    const header = [
      'NO_URUT_ASALRIM',
      'UNIQUE_ID',
      'NO_REGISTRASI_ONLINE',
      'NO_UJIAN_POLDA',
      'NIK',
      'NAMA',
      'GELAR',
      'NAMA_DENGAN_GELAR',
      'JK',
      'TMP_LHR',
      'TGL_LHR',
      'TA_RIM_DIKTUK',
      'GELOMBANG_RIM_MASUK_DIKTUK',
      'JENIS_DIKTUK',
      'KOMPETENSI_DIKTUK',
      'SUB_KOMPETENSI_DIKTUK',
      'SUB_SUB_KOMPETENSI_DIKTUK',
      'KET_JALUR_REKPRO',
      'TMP_DIK',
      'ASAL_RIM_POLDA',
      'ASAL_RIM_POLRES',
      'TA_PAT_DIKTUK',
      'GELOMBANG_PAT_DIKTUK',
      'JENIS_PEKERJAAN',
      'STATUS_KAWIN',
      'OAP_ORANG_ASLI_PAPUA',
      'IJAZAH_DIKUM_GUN_SELEKSI_RIM',
      'N_KUALITATIF_RIKMIN_AWAL',
      'KET_RIKMIN_AWAL',
      'N_KUALITATIF_RIKMIN_AKHIR',
      'KET_RIKMIN_AKHIR',
      'N_KUALITATIF_RIKKES_1',
      'N_KUANTITATIF_RIKKES_1',
      'DIAGNOSA_RIKKES_1',
      'N_KUALITATIF_RIKKES_2',
      'N_KUANTITATIF_RIKKES_2',
      'DIAGNOSA_RIKKES_2',
      'N_KUALITATIF_RIKPSI_1',
      'N_KUANTITATIF_RIKPSI_1',
      'KET_RIKPSI_1',
      'N_KUALITATIF_RIKPSI_2',
      'TEMUAN_RIKPSI_2',
      'KET_RIKPSI_2',
      'N_PENG_U_AKADEMIK',
      'N_WWSN_K_AKADEMIK',
      'N_MTK_AKADEMIK',
      'N_B_ING_AKADEMIK',
      'N_GBGAKHIR_AKADEMIK',
      'KET_AKADEMIK',
      'HASIL_LARI_12_M_UKJ',
      'N_A_LARI_12_MENIT_UKJ',
      'HASIL_PULL_UP_UKJ',
      'N_PULL_UP_UKJ',
      'HASIL_SITUP_UKJ',
      'N_SITUP_UKJ',
      'HASIL_PUSHUP_UKJ',
      'N_PUSHUP_UKJ',
      'HASIL_SHUTTLERUN_UKJ',
      'N_SHUTTLE_RUN_UKJ',
      'RATA2_N_B_UKJ',
      'N_AB_UKJ',
      'JARAK_RENANG_UKJ',
      'WAKTU_RENANG_UKJ',
      'N_RENANG_UKJ',
      'N_KUALITATIF_ANTRO_UKJ',
      'N_KUANTITATIF_ANTRO_UKJ',
      'KELAINAN_ANTRO_UKJ',
      'N_KUANTITATIF_AKHIR_UKJ',
      'N_KUALITATIF_AKHIR_UKJ',
      'KET_UKJ',
      'N_KUALITATIF_PMK',
      'TEMUAN_PMK',
      'KET_PMK',
      'DATA_DETEKSI_DINI_DENSUS',
      'PRESTASI_SELEKSI_RIM',
      'CATATAN_KHUSUS_SELEKSI_RIM',
    ];

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 13,
      };
    });

    const buffer = await workbook.xlsx.writeBuffer();
    const filename = `Template Data Kelulusan Rekrutmen - ${jenisDiktuk.find((jd) => Number(jd.id) == diktuk).nama}`;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_KELULUSAN_REKRUTMEN_WRITE_EXCEL as ConstantLogType,
        message,
        header,
      ),
    );

    return { buffer, filename };
  }

  async uploadDataSiswaBulkInsert(students: SiswaDB[]) {
    return await this.prisma.$transaction(async (tx) => {
      for (const student of students) {
        await tx.siswa.create({
          data: {
            no_urut_asalrim: student.no_urut_asalrim,
            unique_id: student.unique_id,
            no_registrasi_online: student.no_registrasi_online,
            no_ujian_polda: student.no_ujian_polda,
            nik: student.nik,
            nama_lengkap: student.nama_lengkap,
            gelar: student.gelar,
            nama_dengan_gelar: student.nama_dengan_gelar,
            jenis_kelamin: student.jenis_kelamin,
            tempat_lahir: student.tempat_lahir,
            tanggal_lahir: student.tanggal_lahir,
            ta_rim_diktuk: student.ta_rim_diktuk,
            gelombang_rim_masuk_diktuk: student.gelombang_rim_masuk_diktuk,
            jenis_diktuk_id: student.jenis_diktuk_id,
            kompetensi_diktuk_id: student.kompetensi_diktuk_id,
            sub_kompetensi_diktuk_id: student.sub_kompetensi_diktuk_id,
            sub_sub_kompetensi_diktuk_id: student.sub_sub_kompetensi_diktuk_id,
            ket_jalur_rekpro: student.ket_jalur_rekpro,
            tmp_dik_id: student.tmp_dik_id,
            asal_rim_polda_id: student.asal_rim_polda_id,
            asal_rim_polres_id: student.asal_rim_polres_id,
            ta_pat_diktuk: student.ta_pat_diktuk,
            gelombang_pat_diktuk: student.gelombang_pat_diktuk,
            jenis_pekerjaan: student.jenis_pekerjaan,
            status_kawin_id: student.status_kawin_id,
            oap_orang_asli_papua: student.oap_orang_asli_papua,
            ijazah_dikum_seleksi_rim: student.ijazah_dikum_seleksi_rim,
            password: student.password,
            username: student.username,
            created_at: new Date(),
            siswa_penerimaan_ref: {
              create: {
                n_kualitatif_rikmin_awal: student.n_kualitatif_rikmin_awal,
                ket_rikmin_awal: student.ket_rikmin_awal,
                n_kualitatif_rikmin_akhir: student.n_kualitatif_rikmin_akhir,
                ket_rikmin_akhir: student.ket_rikmin_akhir,
                n_kualitatif_rikkes_1: student.n_kualitatif_rikkes_1,
                n_kuantitatif_rikkes_1: student.n_kuantitatif_rikkes_1,
                diagnosa_rikkes_1: student.diagnosa_rikkes_1,
                n_kualitatif_rikkes_2: student.n_kualitatif_rikkes_2,
                n_kuantitatif_rikkes_2: student.n_kuantitatif_rikkes_2,
                diagnosa_rikkes_2: student.diagnosa_rikkes_2,
                n_kualitatif_rikpsi_1: student.n_kualitatif_rikpsi_1,
                n_kuantitatif_rikpsi_1:
                  student.n_kuantitatif_rikpsi_1?.toString(),
                ket_rikpsi_1: student.ket_rikpsi_1,
                n_kualitatif_rikpsi_2: student.n_kualitatif_rikpsi_2,
                temuan_rikpsi_2: student.temuan_rikpsi_2,
                ket_rikpsi_2: student.ket_rikpsi_2,
                n_peng_u_akademik: student.n_peng_u_akademik,
                n_wwsn_k_akademik: student.n_wwsn_k_akademik,
                n_mtk_akademik: student.n_mtk_akademik,
                n_b_ing_akademik: student.n_b_ing_akademik,
                n_gbgakhir_akademik: student.n_gbgakhir_akademik,
                ket_akademik: student.ket_akademik,
                hasil_lari_12_m_ukj: student.hasil_lari_12_m_ukj,
                n_a_lari_12_menit_ukj: student.n_a_lari_12_menit_ukj,
                hasil_pull_up_ukj: student.hasil_pull_up_ukj,
                n_pull_up_ukj: student.hasil_pull_up_ukj,
                hasil_situp_ukj: student.hasil_situp_ukj,
                n_situp_ukj: student.hasil_situp_ukj,
                hasil_pushup_ukj: student.hasil_pushup_ukj,
                n_pushup_ukj: student.hasil_pushup_ukj,
                hasil_shuttlerun_ukj: student.hasil_shuttlerun_ukj,
                rata2_n_b_ukj: student.rata2_n_b_ukj,
                n_ab_ukj: student.n_ab_ukj,
                jarak_renang_ukj: student.jarak_renang_ukj,
                waktu_renang_ukj: student.waktu_renang_ukj.toString(),
                n_renang_ukj: student.n_renang_ukj,
                n_kualitatif_antro_ukj: student.n_kualitatif_antro_ukj,
                n_kuantitatif_antro_ukj: student.n_kuantitatif_antro_ukj,
                kelainan_antro_ukj: student.kelainan_antro_ukj,
                n_kuantitatif_akhir_ukj: student.n_kuantitatif_akhir_ukj,
                n_kualitatif_akhir_ukj: student.n_kualitatif_akhir_ukj,
                ket_ukj: student.ket_ukj,
                n_kualitatif_pmk: student.n_kualitatif_pmk,
                n_kuantitatif_pmk: student.n_kuantitatif_pmk,
                temuan_pmk: student.temuan_pmk,
                ket_pmk: student.ket_pmk,
                data_deteksi_dini_densus: student.data_deteksi_dini_densus,
                prestasi_seleksi_rim: student.prestasi_seleksi_rim,
                catatan_khusus_seleksi_rim: student.catatan_khusus_seleksi_rim,
                created_at: new Date(),
              },
            },
          },
        });
      }

      return;
    });
  }

  async createFolders(folders: { path: string; unique_id: string }[]) {
    const defaultPath = `${this.minioService.defaultPath}e-patma/`;
    const defaultBucket = this.minioService.defaultBucketName;

    for (const folder of folders) {
      const folderNames = [
        'Data Rekam Jejak',
        'Dokumen Catatan Khusus',
        'Dokumen Pribadi',
      ];

      if (folder.path.includes('e-patma 2')) {
        folderNames.push('Dokumen Patma');
      }

      const normalizedPath = `${defaultPath}${
        folder.path.endsWith('/') ? folder.path : `${folder.path}/`
      }${folder.unique_id}/`;

      try {
        for (const folderName of folderNames) {
          const path = `${normalizedPath}${folderName}`;
          const folderLevels = path.split('/').filter(Boolean);
          let currentPath = '';

          for (const level of folderLevels) {
            currentPath += `${level}/`;

            // Upload an empty file to represent the folder
            await this.minioService
              .getClient()
              .putObject(defaultBucket, `${currentPath}.keep`, Buffer.from(''));
          }
        }
      } catch (error) {
        Logger.error(`Error creating nested folders ${normalizedPath}:`, error);
      }
    }
  }

  populateSiswa(siswa: SiswaDto): Siswa {
    return {
      no_urut_asalrim: siswa.no_urut_asalrim,
      unique_id: siswa.unique_id,
      no_registrasi_online: siswa.no_registrasi_online,
      no_ujian_polda: siswa.no_ujian_polda,
      nik: siswa.nik,
      gelar: siswa.gelar?.toUpperCase(),
      nama_dengan_gelar: siswa.nama_dengan_gelar,
      ta_rim_diktuk: siswa.ta_rim_diktuk,
      gelombang_rim_masuk_diktuk:
        siswa.gelombang_rim_masuk_diktuk?.toUpperCase(),
      ket_jalur_rekpro: siswa.ket_jalur_rekpro?.toUpperCase(),
      ta_pat_diktuk: siswa.ta_pat_diktuk,
      gelombang_pat_diktuk: siswa.gelombang_pat_diktuk?.toUpperCase(),
      jenis_pekerjaan: siswa.jenis_pekerjaan?.toUpperCase(),
      oap_orang_asli_papua: siswa.oap_orang_asli_papua?.toUpperCase(),
      n_kualitatif_rikmin_awal: siswa.n_kualitatif_rikmin_awal?.toString(),
      ket_rikmin_awal: siswa.ket_rikmin_awal?.toUpperCase(),
      n_kualitatif_rikmin_akhir: siswa.n_kualitatif_rikmin_akhir?.toUpperCase(),
      ket_rikmin_akhir: siswa.ket_rikmin_akhir?.toUpperCase(),
      n_kualitatif_rikkes_1: siswa.n_kualitatif_rikkes_1?.toUpperCase(),
      n_kuantitatif_rikkes_1: siswa.n_kuantitatif_rikkes_1,
      diagnosa_rikkes_1: siswa.diagnosa_rikkes_1,
      n_kualitatif_rikkes_2: siswa.n_kualitatif_rikkes_2?.toUpperCase(),
      n_kuantitatif_rikkes_2: siswa.n_kuantitatif_rikkes_2,
      diagnosa_rikkes_2: siswa.diagnosa_rikkes_2,
      n_kualitatif_rikpsi_1: siswa.n_kualitatif_rikpsi_1?.toUpperCase(),
      n_kuantitatif_rikpsi_1: siswa.n_kuantitatif_rikpsi_1,
      ket_rikpsi_1: siswa.ket_rikpsi_1?.toUpperCase(),
      n_kualitatif_rikpsi_2: siswa.n_kualitatif_rikpsi_2?.toUpperCase(),
      temuan_rikpsi_2: siswa.temuan_rikpsi_2,
      ket_rikpsi_2: siswa.ket_rikpsi_2?.toUpperCase(),
      n_peng_u_akademik: siswa.n_peng_u_akademik,
      n_wwsn_k_akademik: siswa.n_wwsn_k_akademik,
      n_mtk_akademik: siswa.n_mtk_akademik,
      n_b_ing_akademik: siswa.n_b_ing_akademik,
      n_gbgakhir_akademik: siswa.n_gbgakhir_akademik,
      ket_akademik: siswa.ket_akademik,
      hasil_lari_12_m_ukj: siswa.hasil_lari_12_m_ukj,
      n_a_lari_12_menit_ukj: siswa.n_a_lari_12_menit_ukj,
      hasil_pull_up_ukj: siswa.hasil_pull_up_ukj,
      n_pull_up_ukj: siswa.n_pull_up_ukj,
      hasil_situp_ukj: siswa.hasil_situp_ukj,
      n_situp_ukj: siswa.n_situp_ukj,
      hasil_pushup_ukj: siswa.hasil_pushup_ukj,
      n_pushup_ukj: siswa.hasil_pushup_ukj,
      hasil_shuttlerun_ukj: siswa.hasil_shuttlerun_ukj,
      rata2_n_b_ukj: siswa.rata2_n_b_ukj,
      n_ab_ukj: siswa.n_ab_ukj,
      jarak_renang_ukj: siswa.jarak_renang_ukj,
      waktu_renang_ukj: siswa.waktu_renang_ukj?.toString().toUpperCase(),
      n_renang_ukj: siswa.n_renang_ukj,
      n_kualitatif_antro_ukj: siswa.n_kualitatif_antro_ukj?.toUpperCase(),
      n_kuantitatif_antro_ukj: siswa.n_kuantitatif_antro_ukj,
      kelainan_antro_ukj: siswa.kelainan_antro_ukj,
      n_kuantitatif_akhir_ukj: siswa.n_kuantitatif_akhir_ukj,
      n_kualitatif_akhir_ukj: siswa.n_kualitatif_akhir_ukj?.toString(),
      ket_ukj: siswa.ket_ukj,
      n_kualitatif_pmk: siswa.n_kualitatif_pmk?.toString().toUpperCase(),
      n_kuantitatif_pmk: siswa.n_kuantitatif_pmk,
      temuan_pmk: siswa.temuan_pmk?.toUpperCase(),
      ket_pmk: siswa.ket_pmk?.toUpperCase(),
      data_deteksi_dini_densus: siswa.data_deteksi_dini_densus?.toUpperCase(),
      prestasi_seleksi_rim: siswa.prestasi_seleksi_rim,
      catatan_khusus_seleksi_rim: siswa.catatan_khusus_seleksi_rim,
      password: siswa.password,
    };
  }
}
