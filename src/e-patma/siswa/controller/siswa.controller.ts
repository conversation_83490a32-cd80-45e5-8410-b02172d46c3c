import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { LoginDto } from '../dto/siswa.dto';
import { SiswaService } from '../service/siswa.service';
import { SiswaJwtAuthGuard } from '../../../core/guards/siswa-jwt-auth.guard';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('e-patma/siswa')
@UseGuards(JwtAuthGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class SiswaController {
  private readonly logger = new Logger(SiswaController.name);

  constructor(private readonly siswaService: SiswaService) {}

  @Post('/login')
  @HttpCode(200)
  async login(@Req() req: any, @Body() login: LoginDto) {
    this.logger.log(
      `Entering ${this.login.name} with body: ${JSON.stringify(login)}`,
    );
    const response = this.siswaService.siswaLogin(req, login);
    this.logger.log(
      `Leaving ${this.login.name} with body: ${JSON.stringify(login)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dashboard/:id')
  @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async dashboard(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.dashboard.name} with id: ${id}`);
    const response = this.siswaService.dashboard(req, id);
    this.logger.log(
      `Leaving ${this.dashboard.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/verify/:id')
  @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async verify(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.verify.name} with id: ${id}`);
    const response = this.siswaService.verify(req, id);
    this.logger.log(
      `Leaving ${this.verify.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
