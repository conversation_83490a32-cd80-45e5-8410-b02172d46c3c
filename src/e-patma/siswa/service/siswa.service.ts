import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import * as bcrypt from 'bcrypt';
import { LoginDto } from '../dto/siswa.dto';
import { JwtService } from '@nestjs/jwt';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as dayjs from 'dayjs';

@Injectable()
export class SiswaService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly logsActivityService: LogsActivityService,
    private readonly prisma: PrismaService,
  ) {}

  async siswaLogin(req: any, login: LoginDto) {
    try {
      const siswa = await this.getSiswaByUsername(login.username);
      if (!siswa) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          message: ['Invalid username / password', 'LC-L-01'],
        };
      }

      const checkPassword = await bcrypt.compare(
        login.password,
        siswa.password,
      );
      if (!checkPassword) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          message: ['Invalid username / password', 'LC-L-02'],
        };
      }

      const user = {
        id: siswa.id,
        uid: siswa.uid,
        nama: siswa.nama_lengkap,
        unique_id: siswa.unique_id,
      };

      const accessToken = this.jwtService.sign({
        user,
      });
      const decodeJwt = JSON.parse(atob(accessToken.split('.')[1]));
      const date = new Date(decodeJwt?.exp * 1000);

      const queryResult = {
        user,
        access_token: accessToken,
        expiry_date: date.toLocaleString('en-US', {
          timeZone: 'Asia/Jakarta',
          dateStyle: 'short',
          timeStyle: 'medium',
        }),
        expiry_timestamp: decodeJwt?.exp * 1000,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_SISWA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (e) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: [e.message, 'LC-L-03'],
      };
    }
  }

  async getSiswaByUsername(username: string) {
    return this.prisma.siswa.findFirst({
      where: {
        username,
      },
    });
  }

  async dashboard(req: any, id: number) {
    try {
      const queryResult: any = await this.getSiswaById(id, false);
      queryResult.tanggal_lahir_format = dayjs(
        queryResult.tanggal_lahir,
      ).format('YYYY-MM-DD');

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_SISWA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (e) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: [e.message, 'HC-D-01'],
      };
    }
  }

  async verify(req: any, id: number) {
    try {
      const queryResult = await this.verifySiswaById(id);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.EPATMA_SISWA_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (e) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: [e.message, 'HC-V-01'],
      };
    }
  }

  async getSiswaById(id: number, isAuth: boolean) {
    let selectQ: any = {
      select: {
        id: true,
        uid: true,
        unique_id: true,
      },
    };
    if (!isAuth) {
      selectQ = {
        include: {
          agama: true,
          jenis_diktuk: true,
          kompetensi_diktuk: true,
          sub_kompetensi_diktuk: true,
          sub_sub_kompetensi_diktuk: true,
          siswa_penerimaan_ref: true,
          siswa_diktuk: true,
          statusKawin: true,
          tmpdik: true,
          asal_rim_polda: true,
          asal_rim_polres: true,
        },
      };
    }

    return this.prisma.siswa.findFirst({
      ...selectQ,
      where: {
        id,
      },
    });
  }

  async verifySiswaById(id: number) {
    return this.prisma.siswa.update({
      where: { id: id },
      data: { status_validation: true },
    });
  }
}
