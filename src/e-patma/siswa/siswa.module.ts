import { Module } from '@nestjs/common';
import { SiswaController } from './controller/siswa.controller';
import { SiswaService } from './service/siswa.service';
import { JwtModule } from '@nestjs/jwt';
import { SiswaJwtStrategy } from '../../core/configs/siswa-jwt.strategy';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

export const jwtSecret = 'b5b3246a35948f14';

@Module({
  imports: [
    PrismaModule,
    JwtModule.register({
      secret: jwtSecret,
      signOptions: { expiresIn: '24h' }, // e.g. 7d, 24h
    }),
    LogsActivityModule,
  ],
  controllers: [SiswaController],
  providers: [SiswaService],
})
export class PortalSiswaModule {}
