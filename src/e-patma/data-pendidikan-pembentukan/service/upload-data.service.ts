import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import {
  Siswa,
  SiswaDB,
  SiswaDto,
  SiswaParamDto,
  UploadDataTemplateQueryDto,
} from '../dto/data-pendidikan-pembentukan.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as exceljs from 'exceljs';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { PortalService } from '../../portal/service/portal.service';

@Injectable()
export class UploadDataService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
    private readonly portalService: PortalService,
  ) {}

  async submitDataSiswa(req: any, students: SiswaParamDto) {
    const queryResult: SiswaDB[] = [];
    const jenisDiktuk = await this.portalService.getJenisDiktuk();
    const kompetensiDiktuk = await this.portalService.getKompetensiDiktuk();
    const subKompetensiDiktuk =
      await this.portalService.getSubKompetensiDiktuk();
    const subSubKompetensiDiktuk =
      await this.portalService.getSubSubKompetensiDiktuk();
    const tmpdik = await this.portalService.getTmpdik();
    const statusKawin = await this.portalService.getStatusKawin();
    for (const student of students.data) {
      if (
        !jenisDiktuk.find(
          (jd) => jd.nama == student.jenis_diktuk?.toUpperCase(),
        )
      ) {
        throw new BadRequestException(
          `jenis_diktuk ${student.jenis_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findJenisDiktuk = jenisDiktuk.find(
        (jd) => jd.nama == student.jenis_diktuk.toUpperCase(),
      );

      if (
        !kompetensiDiktuk.find(
          (kd) =>
            kd.nama == student.kompetensi_diktuk?.toUpperCase() &&
            kd.diktuk_id == findJenisDiktuk.id,
        )
      ) {
        return new BadRequestException(
          `kompetensi_diktuk ${student.kompetensi_diktuk} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findKompetensi = kompetensiDiktuk.find(
        (kd) =>
          kd.nama == student.kompetensi_diktuk.toUpperCase() &&
          kd.diktuk_id == findJenisDiktuk.id,
      );

      if (
        !subKompetensiDiktuk.find(
          (skd) =>
            skd.nama == student.sub_kompetensi_diktuk?.toUpperCase() &&
            skd.kompetensi_id == findKompetensi.id,
        )
      ) {
        throw new BadRequestException(
          `sub_kompetensi_diktuk ${student.sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findSubKompetensi = subKompetensiDiktuk.find(
        (skd) =>
          skd.nama == student.sub_kompetensi_diktuk.toUpperCase() &&
          skd.kompetensi_id == findKompetensi.id,
      );

      if (
        !subSubKompetensiDiktuk.find(
          (sskd) =>
            sskd.nama == student.sub_sub_kompetensi_diktuk?.toUpperCase() &&
            sskd.sub_kompetensi_id == findSubKompetensi.id,
        )
      ) {
        throw new BadRequestException(
          `sub_sub_kompetensi_diktuk ${student.sub_sub_kompetensi_diktuk} tidak ditemukan di dalam kompetensi diktuk ${findKompetensi.nama} dengan sub kompetensi diktuk ${findSubKompetensi.nama}, pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }
      const findSubSubKompetensi = subSubKompetensiDiktuk.find(
        (sskd) =>
          sskd.nama == student.sub_sub_kompetensi_diktuk.toUpperCase() &&
          sskd.sub_kompetensi_id == findSubKompetensi.id,
      );

      if (!tmpdik.find((t) => t.nama == student.tmp_dik?.toUpperCase())) {
        throw new BadRequestException(
          `tmp_dik ${student.tmp_dik} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }

      if (
        !statusKawin.find((s) => s.nama == student.status_kawin?.toUpperCase())
      ) {
        throw new BadRequestException(
          `status_kawin ${student.status_kawin} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }

      const satuan = await this.portalService.findSatuanByNamas([
        student.asal_rim_polda,
      ]);
      if (
        !satuan.find((s) => s.nama == student.asal_rim_polda?.toUpperCase())
      ) {
        throw new BadRequestException(
          `asal_rim_polda ${student.asal_rim_polda} tidak ditemukan pada no_urut_asalrim ${student.no_urut_asalrim}`,
        );
      }

      const update: SiswaDB = this.populateSiswa(student);
      update.tmp_dik_id = tmpdik.find(
        (t) => t.nama == student.tmp_dik.toUpperCase(),
      ).id;
      update.asal_rim_polda_id = satuan.find(
        (s) => s.nama == student.asal_rim_polda.toUpperCase(),
      ).id;
      update.status_kawin_id = statusKawin.find(
        (s) => s.nama == student.status_kawin.toUpperCase(),
      ).id;
      update.nama_lengkap = student.nama?.toUpperCase();
      update.jenis_kelamin = student.jk?.toUpperCase();
      update.tempat_lahir = student.tmp_lahir?.toUpperCase();
      update.tanggal_lahir = new Date(student.tgl_lahir);
      update.jenis_diktuk_id = findJenisDiktuk.id;
      update.kompetensi_diktuk_id = findKompetensi.id;
      update.sub_kompetensi_diktuk_id = findSubKompetensi.id;
      update.sub_sub_kompetensi_diktuk_id = findSubSubKompetensi.id;
      update.ijazah_dikum_seleksi_rim =
        student.ijazah_dikum_gun_seleksi_rim?.toUpperCase();
      update.n_lari_12_m_jasdiktuk = student.n_a_lari_12_menit_jasdiktuk;
      update.n_shuttlerun_jasdiktuk = student.n_shuttle_run_jasdiktuk;
      queryResult.push(update);
    }

    await this.uploadDataBulkUpdate(queryResult);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getUploadDataTemplate(req: any, query: UploadDataTemplateQueryDto) {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Data Pendidikan Pembentukan');
    const header = [
      'NO_URUT_ASALRIM',
      'UNIQUE_ID',
      'NAMA',
      'NAMA_DENGAN_GELAR',
      'JK',
      'TMP_LHR',
      'TGL_LHR',
      'NRP',
      'NIK',
      'JENIS_PEKERJAAN',
      'STATUS_KAWIN',
      'NO_UJIAN_POLDA',
      'NO_REGISTRASI_ONLINE',
      'JENIS_DIKTUK',
      'TA_RIM_DIKTUK',
      'GELOMBANG_RIM_MASUK_DIKTUK',
      'KOMPETENSI_DIKTUK',
      'SUB_KOMPETENSI_DIKTUK',
      'SUB_SUB_KOMPETENSI_DIKTUK',
      'TMP_DIK',
      'ASAL_RIM_POLDA',
      'TA_PAT_DIKTUK',
      'GELOMBANG_PAT_DIKTUK',
      'IJAZAH_DIKUM_GUN_SELEKSI_RIM',
      'USERNAME',
      'PASSWORD',
      'NO_AK_NOSIS',
      'NO_URUT_TMP_DIK',
      'HASIL_LARI_12_M_JASDIKTUK',
      'N_A_LARI_12_MENIT_JASDIKTUK',
      'HASIL_PULL_UP_JASDIKTUK',
      'N_PULL_UP_JASDIKTUK',
      'HASIL_SITUP_JASDIKTUK',
      'N_SITUP_JASDIKTUK',
      'HASIL_PUSHUP_JASDIKTUK',
      'N_PUSHUP_JASDIKTUK',
      'HASIL_SHUTTLERUN_JASDIKTUK',
      'N_SHUTTLE_RUN_JASDIKTUK',
      'RATA2_N_B_JASDIKTUK',
      'N_AB_JASDIKTUK',
      'N_JASMANI_DIKTUK',
      'N_MENTALKEP_KAR_DIKTUK',
      'N_AKADEMIK_PENG_DIKTUK',
      'N_GBGAKHIR_DIKTUK',
      'RANKING_DIKTUK',
      'CATATAN_KESEHATAN_DIKTUK',
      'CATATAN_PELANGGARAN_DIKTUK',
      'CATATAN_PSIKOLOGI_DIKTUK',
      'PRESTASI_DIKTUK',
      'CATATAN_KHUSUS_DIKTUK',
      'STATUS_DIKTUK',
      'KET_STATUS_DIKTUK',
    ];

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 13,
      };
    });

    const jenisDiktuk = await this.portalService.getJenisDiktuk();
    const kompetensiDiktuk = await this.portalService.getKompetensiDiktuk();
    const subKompetensiDiktuk =
      await this.portalService.getSubKompetensiDiktuk();
    const subSubKompetensiDiktuk =
      await this.portalService.getSubSubKompetensiDiktuk();
    const statusKawin = await this.portalService.getStatusKawin();
    const students = await this.uploadDataGetList(
      query.jenis_diktuk,
      query.tahun?.toString(),
      query.gelombang,
    );

    if (students.length > 0) {
      students.forEach((student) => {
        const findJenisDiktuk = jenisDiktuk.find(
          (jd) => jd.id == student.jenis_diktuk_id,
        );
        const findKompetensi = kompetensiDiktuk.find(
          (kd) =>
            kd.id == student.kompetensi_diktuk_id &&
            kd.diktuk_id == findJenisDiktuk.id,
        );
        const findSubKompetensi = subKompetensiDiktuk.find(
          (skd) =>
            skd.id == student.sub_kompetensi_diktuk_id &&
            skd.kompetensi_id == findKompetensi.id,
        );
        const findSubSubKompetensi = subSubKompetensiDiktuk.find(
          (sskd) =>
            sskd.id == student.sub_sub_kompetensi_diktuk_id &&
            sskd.sub_kompetensi_id == findSubKompetensi.id,
        );

        const row: any[] = [
          student.no_urut_asalrim,
          student.unique_id,
          student.nama_lengkap,
          student.nama_dengan_gelar,
          student.jenis_kelamin,
          student.tempat_lahir,
          student.tanggal_lahir,
          student.siswa_patma?.nrp,
          student.nik,
          student.jenis_pekerjaan,
          statusKawin.find((sk) => sk.id == student.status_kawin_id).nama,
          student.no_ujian_polda,
          student.no_registrasi_online,
          findJenisDiktuk.nama,
          student.ta_rim_diktuk,
          student.gelombang_rim_masuk_diktuk,
          findKompetensi.nama,
          findSubKompetensi.nama,
          findSubSubKompetensi.nama,
          student.tmpdik?.nama,
          student.siswa_asal_rim_polda?.nama,
          student.ta_pat_diktuk,
          student.gelombang_pat_diktuk,
          student.ijazah_dikum_seleksi_rim,
          student.username,
          student.password,
          student.no_ak_nosis,
          student.siswa_diktuk?.no_urut_tmp_dik,
          Number(student.siswa_diktuk?.hasil_lari_12_m_jasdiktuk),
          Number(student.siswa_diktuk?.n_lari_12_m_jasdiktuk),
          Number(student.siswa_diktuk?.hasil_pull_up_jasdiktuk),
          Number(student.siswa_diktuk?.n_pull_up_jasdiktuk),
          Number(student.siswa_diktuk?.hasil_situp_jasdiktuk),
          Number(student.siswa_diktuk?.n_situp_jasdiktuk),
          Number(student.siswa_diktuk?.hasil_pushup_jasdiktuk),
          Number(student.siswa_diktuk?.n_pushup_jasdiktuk),
          Number(student.siswa_diktuk?.hasil_shuttlerun_jasdiktuk),
          Number(student.siswa_diktuk?.n_shuttlerun_jasdiktuk),
          Number(student.siswa_diktuk?.rata2_n_b_jasdiktuk),
          Number(student.siswa_diktuk?.n_ab_jasdiktuk),
          Number(student.siswa_diktuk?.n_jasmani_diktuk),
          Number(student.siswa_diktuk?.n_mentalkep_kar_diktuk),
          Number(student.siswa_diktuk?.n_akademik_peng_diktuk),
          Number(student.siswa_diktuk?.n_gbgakhir_diktuk),
          Number(student.siswa_diktuk?.ranking_diktuk),
          student.siswa_diktuk?.catatan_kesehatan_diktuk,
          student.siswa_diktuk?.catatan_pelanggaran_diktuk,
          student.siswa_diktuk?.catatan_psikologi_diktuk,
          student.siswa_diktuk?.prestasi_diktuk,
          student.siswa_diktuk?.catatan_khusus_diktuk,
          student.siswa_diktuk?.status_diktuk,
          student.siswa_diktuk?.ket_status_diktuk,
        ];

        worksheet.addRow(row);
      });
    }

    const buffer = await workbook.xlsx.writeBuffer();
    const filename = `Template Data Pendidikan Pembentukan - ${jenisDiktuk.find((jd) => Number(jd.id) == query.jenis_diktuk)?.nama} - ${query?.tahun} - ${query?.gelombang}`;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_WRITE_EXCEL as ConstantLogType,
        message,
        header,
      ),
    );

    return { buffer, filename };
  }

  async uploadDataGetList(
    jenisDiktuk: number,
    tahun: string,
    gelombang: string,
  ) {
    let where = {};
    if (jenisDiktuk && jenisDiktuk > 0) {
      where = {
        ...where,
        jenis_diktuk_id: jenisDiktuk,
      };
    }

    if (tahun && tahun != '0') {
      where = {
        ...where,
        ta_pat_diktuk: tahun,
      };
    }

    if (gelombang && gelombang != '') {
      where = {
        ...where,
        gelombang_pat_diktuk: gelombang,
      };
    }

    return this.prisma.siswa.findMany({
      include: {
        siswa_asal_rim_polda: true,
        tmpdik: true,
        siswa_patma: true,
        siswa_diktuk: true,
      },
      where: where,
      orderBy: {
        no_urut_asalrim: Prisma.SortOrder.asc,
      },
    });
  }

  async uploadDataBulkUpdate(students: SiswaDB[]) {
    return await this.prisma.$transaction(
      async (tx) => {
        for (const student of students) {
          const siswa = await tx.siswa.findFirst({
            where: {
              unique_id: student.unique_id,
            },
          });

          await tx.siswa.update({
            where: {
              id: siswa.id,
            },
            data: {
              no_urut_asalrim: student.no_urut_asalrim,
              no_registrasi_online: student.no_registrasi_online,
              no_ujian_polda: student.no_ujian_polda,
              nik: student.nik,
              nama_lengkap: student.nama_lengkap,
              nama_dengan_gelar: student.nama_dengan_gelar,
              jenis_kelamin: student.jenis_kelamin,
              tempat_lahir: student.tempat_lahir,
              tanggal_lahir: student.tanggal_lahir,
              ta_rim_diktuk: student.ta_rim_diktuk,
              gelombang_rim_masuk_diktuk: student.gelombang_rim_masuk_diktuk,
              jenis_diktuk_id: student.jenis_diktuk_id,
              kompetensi_diktuk_id: student.kompetensi_diktuk_id,
              sub_kompetensi_diktuk_id: student.sub_kompetensi_diktuk_id,
              sub_sub_kompetensi_diktuk_id:
                student.sub_sub_kompetensi_diktuk_id,
              tmp_dik_id: student.tmp_dik_id,
              asal_rim_polda_id: student.asal_rim_polda_id,
              ta_pat_diktuk: student.ta_pat_diktuk,
              gelombang_pat_diktuk: student.gelombang_pat_diktuk,
              jenis_pekerjaan: student.jenis_pekerjaan,
              status_kawin_id: student.status_kawin_id,
              ijazah_dikum_seleksi_rim: student.ijazah_dikum_seleksi_rim,
              password: student.password,
              username: student.username,
              no_ak_nosis: student.no_ak_nosis,
              updated_at: new Date(),
            },
          });

          await tx.siswa_patma.create({
            data: {
              siswa_id: siswa.id,
              nrp: student.nrp,
              created_at: new Date(),
            },
          });

          await tx.siswa_diktuk.create({
            data: {
              siswa_id: siswa.id,
              no_urut_tmp_dik: student.no_urut_tmp_dik,
              hasil_lari_12_m_jasdiktuk: student.hasil_lari_12_m_jasdiktuk,
              n_lari_12_m_jasdiktuk: student.n_lari_12_m_jasdiktuk,
              hasil_pull_up_jasdiktuk: student.hasil_pull_up_jasdiktuk,
              n_pull_up_jasdiktuk: student.n_pull_up_jasdiktuk,
              hasil_situp_jasdiktuk: student.hasil_situp_jasdiktuk,
              n_situp_jasdiktuk: student.n_situp_jasdiktuk,
              hasil_pushup_jasdiktuk: student.hasil_pushup_jasdiktuk,
              n_pushup_jasdiktuk: student.n_pushup_jasdiktuk,
              hasil_shuttlerun_jasdiktuk: student.hasil_shuttlerun_jasdiktuk,
              n_shuttlerun_jasdiktuk: student.n_shuttlerun_jasdiktuk,
              rata2_n_b_jasdiktuk: student.rata2_n_b_jasdiktuk,
              n_ab_jasdiktuk: student.n_ab_jasdiktuk,
              n_jasmani_diktuk: student.n_jasmani_diktuk,
              n_mentalkep_kar_diktuk: student.n_mentalkep_kar_diktuk,
              n_akademik_peng_diktuk: student.n_akademik_peng_diktuk,
              n_gbgakhir_diktuk: student.n_gbgakhir_diktuk,
              ranking_diktuk: student.ranking_diktuk,
              catatan_kesehatan_diktuk: student.catatan_kesehatan_diktuk,
              catatan_pelanggaran_diktuk: student.catatan_pelanggaran_diktuk,
              catatan_psikologi_diktuk: student.catatan_psikologi_diktuk,
              prestasi_diktuk: student.prestasi_diktuk,
              catatan_khusus_diktuk: student.catatan_khusus_diktuk,
              status_diktuk: student.status_diktuk,
              ket_status_diktuk: student.ket_status_diktuk,
              created_at: new Date(),
            },
          });
        }

        return;
      },
      {
        timeout: 120000,
      },
    );
  }

  populateSiswa(siswa: SiswaDto): Siswa {
    return {
      no_urut_asalrim: siswa.no_urut_asalrim,
      unique_id: siswa.unique_id,
      no_registrasi_online: siswa.no_registrasi_online,
      no_ujian_polda: siswa.no_ujian_polda,
      nik: siswa.nik,
      nama_dengan_gelar: siswa.nama_dengan_gelar,
      ta_rim_diktuk: siswa.ta_rim_diktuk,
      gelombang_rim_masuk_diktuk:
        siswa.gelombang_rim_masuk_diktuk?.toUpperCase(),
      ta_pat_diktuk: siswa.ta_pat_diktuk,
      gelombang_pat_diktuk: siswa.gelombang_pat_diktuk?.toUpperCase(),
      jenis_pekerjaan: siswa.jenis_pekerjaan?.toUpperCase(),
      username: siswa.username,
      password: siswa.password,
      nrp: siswa.nrp,
      no_ak_nosis: siswa.no_ak_nosis,
      no_urut_tmp_dik: siswa.no_urut_tmp_dik,
      hasil_lari_12_m_jasdiktuk: siswa.hasil_lari_12_m_jasdiktuk,
      hasil_pull_up_jasdiktuk: siswa.hasil_pull_up_jasdiktuk,
      n_pull_up_jasdiktuk: siswa.n_pull_up_jasdiktuk,
      hasil_situp_jasdiktuk: siswa.hasil_situp_jasdiktuk,
      n_situp_jasdiktuk: siswa.n_situp_jasdiktuk,
      hasil_pushup_jasdiktuk: siswa.hasil_pushup_jasdiktuk,
      n_pushup_jasdiktuk: siswa.n_pushup_jasdiktuk,
      hasil_shuttlerun_jasdiktuk: siswa.hasil_shuttlerun_jasdiktuk,
      rata2_n_b_jasdiktuk: siswa.rata2_n_b_jasdiktuk,
      n_ab_jasdiktuk: siswa.n_ab_jasdiktuk,
      n_jasmani_diktuk: siswa.n_jasmani_diktuk,
      n_mentalkep_kar_diktuk: siswa.n_mentalkep_kar_diktuk,
      n_akademik_peng_diktuk: siswa.n_akademik_peng_diktuk,
      n_gbgakhir_diktuk: siswa.n_gbgakhir_diktuk,
      ranking_diktuk: siswa.ranking_diktuk,
      catatan_kesehatan_diktuk: siswa.catatan_kesehatan_diktuk?.toUpperCase(),
      catatan_pelanggaran_diktuk:
        siswa.catatan_pelanggaran_diktuk?.toUpperCase(),
      catatan_psikologi_diktuk: siswa.catatan_psikologi_diktuk?.toUpperCase(),
      prestasi_diktuk: siswa.prestasi_diktuk?.toUpperCase(),
      catatan_khusus_diktuk: siswa.catatan_khusus_diktuk?.toUpperCase(),
      status_diktuk: siswa.status_diktuk?.toUpperCase(),
      ket_status_diktuk: siswa.ket_status_diktuk?.toUpperCase(),
    };
  }
}
