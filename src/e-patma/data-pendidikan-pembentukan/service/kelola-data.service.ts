import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { Prisma } from '@prisma/client';
import { HttpStatus, Injectable } from '@nestjs/common';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import * as dayjs from 'dayjs';
import { PortalService } from '../../portal/service/portal.service';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { CONSTANT_LOG } from 'src/core/constants/log.constant';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import * as exceljs from 'exceljs';
import * as lodash from 'lodash';
import {
  FilterKelolaDto,
  FilterKelolaV2Dto,
} from '../dto/data-pendidikan-pembentukan.dto';

@Injectable()
export class KelolaDataService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly portalService: PortalService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getFilteredDataDownload(req: any, filter: FilterKelolaDto) {
    const paramFilter: number[] = [];
    filter.data_personel?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    filter.data_rekrutmen?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    filter.data_diktuk?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    const filters = await this.portalService.getFilterkelolaData(paramFilter);

    let isNik: boolean;
    let isNama: boolean;
    let isJk: boolean;
    let isNoRegis: boolean;
    let isSosKel: boolean;
    let isNilai: boolean;
    let isGlb: boolean;
    let isKomDik: boolean;
    let isGenPuan: boolean;
    let isTmpdik: boolean;
    let isPullUp: boolean;
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('Kelola Data');
    const header = ['No Urut', 'Jenis Diktuk', 'Tahun', 'NRP', 'No Ak Nosis'];
    filters.forEach((item) => {
      switch (true) {
        case item.nama.toLowerCase().includes('nik'):
          isNik = true;
          header.push('NIK');
          break;
        case item.nama.toLowerCase().includes('nama'):
          isNama = true;
          header.push('Nama Lengkap');
          break;
        case item.nama.toLowerCase().includes('kelamin'):
          isJk = true;
          header.push('Jenis Kelamin');
          break;
        case item.nama.toLowerCase().includes('regis'):
          isNoRegis = true;
          header.push('No Registrasi');
          break;
        case item.nama.toLowerCase().includes('keluarga'):
          isSosKel = true;
          header.push(
            'Agama',
            'Suku',
            'Email',
            'No HP WA',
            'Facebook',
            'Twitter',
            'Instagram',
            'Tiktok',
            'Anak Ke',
            'Jumlah Saudara',
            'Nama Ayah',
            'Pekerjaan Ayah',
            'Status Ayah',
            'Umur Ayah',
            'Golongan Pangkat Ayah',
            'Jabatan Ayah',
            'Nama Ibu',
            'Pekerjaan Ibu',
            'Status Ibu',
            'Umur Ibu',
            'Golongan Pangkat Ibu',
            'Jabatan Ibu',
            'No HP Orang Tua',
            'Provinsi',
            'Kabupaten/Kotamadya',
            'Kecamatan',
            'Kelurahan',
            'Alamat Lengkap',
          );
          break;
        case item.nama.toLowerCase() == 'nilai':
          isNilai = true;
          header.push(
            'Hasil Lari 12 Menit Jasdiktuk',
            'Nilai Lari 12 Menit Jasdiktuk',
            'Hasil Pull Up Jasdiktuk',
            'Hasil Situp Jasdiktuk',
            'Nilai Situp Jasdiktuk',
            'Hasil Pushup Jasdiktuk',
            'Nilai Pushup Jasdiktuk',
            'Hasil Shuttle Run Jasdiktuk',
            'Nilai Shuttle Run Jasdiktuk',
            'Rata2 Nilai B Jasdiktuk',
            'Nilai Ab Jasdiktuk',
            'Nilai Jasmani Diktuk',
            'Nilai Mentalkep Kar Diktuk',
            'Nilai Akademik Peng Diktuk',
            'Nilai Gabungan Akhir Diktuk',
            'Ranking Diktuk',
            'Catatan Kesehatan Diktuk',
            'Catatan Pelanggaran Diktuk',
            'Catatan Psikologi Diktuk',
            'Prestasi Diktuk',
            'Catatan Khusus Diktuk',
            'Status Diktuk',
            'Keterangan Status Diktuk',
          );
          break;
        case item.nama.toLowerCase().includes('gelombang'):
          isGlb = true;
          header.push('Gelombang');
          break;
        case item.nama.toLowerCase().includes('kompetensi'):
          isKomDik = true;
          header.push('Kompetensi Diktuk');
          break;
        case item.nama.toLowerCase().includes('kemampuan'):
          isGenPuan = true;
          header.push(
            'Golongan Darah',
            'Hobi',
            'Tinggi Badan',
            'Berat Badan',
            'Warna Kulit',
            'Warna Mata',
            'Warna Rambut',
            'Jenis Rambut',
            'Ukuran Topi',
            'Ukuran Baju',
            'Ukuran Celana',
            'Ukuran Sepatu',
            'Kemampuan Bahasa Daerah',
            'Keterangan Kemampuan Bahasa Daerah',
            'Kemampuan Bahasa Asing',
            'Keterangan Kemampuan Bahasa Asing',
            'Keahlian Aplikasi Komputer',
            'Keahlian Mengemudi',
            'Kepemilikan Sim',
            'Keahlian Beladiri',
            'Sabuk Tingkatan Beladiri',
          );
          break;
        case item.nama.toLowerCase().includes('tmpt'):
          isTmpdik = true;
          header.push('No Urut Tempat Pendidikan');
          break;
        case item.nama.toLowerCase().includes('pull'):
          isPullUp = true;
          header.push('Nilai Pull Up Jasdiktuk');
          break;
      }
    });

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 13,
      };
    });
    const paramjenis_diktuk: number[] = [];
    filter.jenis_diktuk.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramjenis_diktuk.push(parseInt(data));
      }
    });

    const siswas = await this.kelolaDataGetByjenis_diktuk(
      filter.page,
      filter.limit,
      paramjenis_diktuk,
      filter.tahun,
      filter.gelombang,
    );
    siswas.forEach((siswa) => {
      const row: any[] = [
        siswa.no_urut_asalrim,
        siswa.jenis_diktuk?.nama,
        siswa.ta_pat_diktuk,
        siswa.siswa_patma?.nrp,
        siswa.no_ak_nosis,
      ];

      if (isNik) {
        row.push(siswa.nik);
      }

      if (isNama) {
        row.push(siswa.nama_lengkap);
      }

      if (isJk) {
        row.push(siswa.jenis_kelamin);
      }

      if (isNoRegis) {
        row.push(siswa.no_registrasi_online);
      }

      if (isSosKel) {
        row.push(
          siswa.agama?.nama,
          siswa.suku,
          siswa.email,
          siswa.no_hp,
          siswa.medsos_facebook,
          siswa.medsos_twitter,
          siswa.medsos_instagram,
          siswa.medsos_tiktok,
          siswa.siswa_keluarga_ref?.anak_ke,
          siswa.siswa_keluarga_ref?.jumlah_saudara,
          siswa.siswa_keluarga_ref?.nama_ayah,
          siswa.siswa_keluarga_ref?.pek_ayah,
          siswa.siswa_keluarga_ref?.status_ayah,
          siswa.siswa_keluarga_ref?.umur_ayah,
          siswa.siswa_keluarga_ref?.gol_pangkat_ayah,
          siswa.siswa_keluarga_ref?.jabatan_ayah,
          siswa.siswa_keluarga_ref?.nama_ibu,
          siswa.siswa_keluarga_ref?.pek_ibu,
          siswa.siswa_keluarga_ref?.status_ibu,
          siswa.siswa_keluarga_ref?.umur_ibu,
          siswa.siswa_keluarga_ref?.gol_pangkat_ibu,
          siswa.siswa_keluarga_ref?.jabatan_ibu,
          siswa.siswa_keluarga_ref?.no_hp_ortu,
          siswa.siswa_alamat_ref?.provinsi.nama,
          siswa.siswa_alamat_ref?.kabupaten.nama,
          siswa.siswa_alamat_ref?.kecamatan.nama,
          siswa.siswa_alamat_ref?.kelurahan.nama,
          siswa.siswa_alamat_ref?.alamat,
        );
      }

      if (isNilai) {
        row.push(
          Number(siswa.siswa_diktuk?.hasil_lari_12_m_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_lari_12_m_jasdiktuk),
          Number(siswa.siswa_diktuk?.hasil_pull_up_jasdiktuk),
          Number(siswa.siswa_diktuk?.hasil_situp_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_situp_jasdiktuk),
          Number(siswa.siswa_diktuk?.hasil_pushup_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_pushup_jasdiktuk),
          Number(siswa.siswa_diktuk?.hasil_shuttlerun_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_shuttlerun_jasdiktuk),
          Number(siswa.siswa_diktuk?.rata2_n_b_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_ab_jasdiktuk),
          Number(siswa.siswa_diktuk?.n_jasmani_diktuk),
          Number(siswa.siswa_diktuk?.n_mentalkep_kar_diktuk),
          Number(siswa.siswa_diktuk?.n_akademik_peng_diktuk),
          Number(siswa.siswa_diktuk?.n_gbgakhir_diktuk),
          Number(siswa.siswa_diktuk?.ranking_diktuk),
          siswa.siswa_diktuk?.catatan_kesehatan_diktuk,
          siswa.siswa_diktuk?.catatan_pelanggaran_diktuk,
          siswa.siswa_diktuk?.catatan_psikologi_diktuk,
          siswa.siswa_diktuk?.prestasi_diktuk,
          siswa.siswa_diktuk?.catatan_khusus_diktuk,
          siswa.siswa_diktuk?.status_diktuk,
          siswa.siswa_diktuk?.ket_status_diktuk,
        );
      }

      if (isGlb) {
        row.push(siswa.gelombang_pat_diktuk);
      }

      if (isKomDik) {
        row.push(siswa.kompetensi_diktuk.nama);
      }

      if (isGenPuan) {
        row.push(
          siswa.golongan_darah,
          siswa.hobi,
          siswa.siswa_fisik_ref?.tb_cm,
          siswa.siswa_fisik_ref?.bb_kg,
          siswa.siswa_fisik_ref?.warna_kulit,
          siswa.siswa_fisik_ref?.warna_mata,
          siswa.siswa_fisik_ref?.warna_rambut,
          siswa.siswa_fisik_ref?.jenis_rambut,
          siswa.siswa_fisik_ref?.ukuran_topi,
          siswa.siswa_fisik_ref?.ukuran_baju,
          siswa.siswa_fisik_ref?.ukuran_celana,
          siswa.siswa_fisik_ref?.ukuran_sepatu,
          siswa.siswa_keahlian_ref?.puan_bhs_daerah,
          siswa.siswa_keahlian_ref?.ket_puan_bhs_daerah,
          siswa.siswa_keahlian_ref?.puan_bhs_asing,
          siswa.siswa_keahlian_ref?.ket_puan_bhs_asing,
          siswa.siswa_keahlian_ref?.keahlian_aplikasi_komputer,
          siswa.siswa_keahlian_ref?.keahlian_mengemudi,
          siswa.siswa_keahlian_ref?.kepemilikan_sim,
          siswa.siswa_keahlian_ref?.keahlian_beladiri,
          siswa.siswa_keahlian_ref?.sabuk_tingkatan_beladiri,
        );
      }

      if (isTmpdik) {
        row.push(siswa.siswa_diktuk?.no_urut_tmp_dik);
      }

      if (isPullUp) {
        row.push(Number(siswa.siswa_diktuk?.n_pull_up_jasdiktuk));
      }

      worksheet.addRow(row);
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_WRITE_EXCEL as ConstantLogType,
        message,
        siswas,
      ),
    );

    return await workbook.xlsx.writeBuffer();
  }

  async getFilteredDataV2(req: any, filter: FilterKelolaV2Dto) {
    const searchIds: number[] = [];
    filter.data_search.forEach((data) => {
      searchIds.push(data.id);
    });
    const [column, search] = await Promise.all([
      this.portalService.getFilterSearchByIds(filter.data_column),
      this.portalService.getFilterSearchByIds(searchIds),
    ]);
    const filterColumn = column.reduce(function (map, obj) {
      (map[obj.table_name] = map[obj.table_name] || []).push(obj.column_name);
      return map;
    }, {});
    const filterSearch = search.reduce(function (map, obj) {
      const searchIdx = filter.data_search.findIndex((ds) => ds.id == obj.id);
      const searchObj = {
        column: obj.column_name,
        value: filter.data_search[searchIdx].value,
      };
      (map[obj.table_name] = map[obj.table_name] || []).push(searchObj);
      return map;
    }, {});
    const kelolaData: any[] = await this.KelolaDataGetList({
      column: filterColumn,
      search: filterSearch,
      page: filter.page,
      limit: filter.limit,
    });
    const queryResult: any[] = [];
    if (kelolaData.length > 0) {
      kelolaData.forEach((d) => {
        if (d.tanggal_lahir) {
          d.tanggal_lahir_format = dayjs(d.tanggal_lahir).format('YYYY-MM-DD');
        }

        if (d.siswa_patma?.tmt_kep_patma) {
          d.siswa_patma.tmt_kep_patma_format = dayjs(
            d.siswa_patma.tmt_kep_patma,
          ).format('YYYY-MM-DD');
        }

        Object.keys(d).forEach((key) => {
          switch (key) {
            case 'kompetensi_diktuk':
            case 'sub_kompetensi_diktuk':
            case 'sub_sub_kompetensi_diktuk':
            case 'siswa_asal_rim_polda':
            case 'siswa_asal_rim_polres':
            case 'status_kawin':
            case 'jenis_diktuk':
              d[`${lodash.snakeCase(key)}`] = d[key].nama;
              delete d[key];
              delete d[`${lodash.snakeCase(key)}_id`];
              break;
            case 'tmpdik':
              d[`${lodash.snakeCase(key)}`] = d[key].nama;
              delete d[`tmp_dik_id`];
              break;
            case 'agama':
              d[`${lodash.snakeCase(key)}`] = d[key].nama;
              delete d[`agama_id`];
              break;
            case 'siswa_hobi':
              d.hobi = d[key].map((h) => h.hobi.nama).join(',');
              delete d[key];
              break;
            case 'siswa_patma':
              if (d[key]) {
                Object.keys(d[key]).forEach((k) => {
                  switch (k) {
                    case 'satker_patma_ref':
                      if (d[key][k]) {
                        d.satker_patma = d[key][k].nama;
                      }
                      break;
                    default:
                      d[k] = d[key][k];
                      break;
                  }
                });
              }
              delete d[key];
              break;
            case 'siswa_alamat_ref':
              if (d[key]) {
                Object.keys(d[key]).forEach((k) => {
                  switch (k) {
                    case 'kecamatan':
                    case 'kelurahan':
                    case 'kabupaten':
                    case 'provinsi':
                      d[`alamat_${k}`] = d[key][k].nama;
                      break;
                    default:
                      if (k.includes('_id')) {
                        delete d[key][k];
                      } else {
                        d[k] = d[key][k];
                      }
                      break;
                  }
                });
              }
              delete d[key];
              break;
            case 'siswa_keswa_angket':
              if (d[key]) {
                Object.keys(d[key]).forEach((k) => {
                  switch (k.includes('satuan_angket')) {
                    case true:
                      d[k.replace('satuan_', '')] = d[key][k].nama;
                      break;
                    default:
                      if (k.includes('angket_')) {
                        delete d[key][k];
                      } else {
                        d[k] = d[key][k];
                      }
                      break;
                  }
                });
              }
              delete d[key];
              break;
            default:
              if (key.includes('siswa_')) {
                if (d[key]) {
                  Object.keys(d[key]).forEach((k) => {
                    d[k] = d[key][k];
                  });
                }
                delete d[key];
              }
              break;
          }
        });

        queryResult.push(d);
      });
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getFilteredData(req: any, filter: FilterKelolaDto) {
    const paramFilter: number[] = [];
    filter.data_personel?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    filter.data_rekrutmen?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    filter.data_diktuk?.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramFilter.push(parseInt(data));
      }
    });
    const filters = await this.portalService.getFilterkelolaData(paramFilter);

    let isNik: boolean;
    let isNama: boolean;
    let isJk: boolean;
    let isNoRegis: boolean;
    let isSosKel: boolean;
    let isNilai: boolean;
    let isGlb: boolean;
    let isKomDik: boolean;
    let isGenPuan: boolean;
    let isTmpdik: boolean;
    let isPullUp: boolean;

    filters.forEach((item) => {
      switch (true) {
        case item.nama.toLowerCase().includes('nik'):
          isNik = true;
          break;
        case item.nama.toLowerCase().includes('nama'):
          isNama = true;
          break;
        case item.nama.toLowerCase().includes('kelamin'):
          isJk = true;
          break;
        case item.nama.toLowerCase().includes('regis'):
          isNoRegis = true;
          break;
        case item.nama.toLowerCase().includes('keluarga'):
          isSosKel = true;
          break;
        case item.nama.toLowerCase() == 'nilai':
          isNilai = true;
          break;
        case item.nama.toLowerCase().includes('gelombang'):
          isGlb = true;
          break;
        case item.nama.toLowerCase().includes('kompetensi'):
          isKomDik = true;
          break;
        case item.nama.toLowerCase().includes('kemampuan'):
          isGenPuan = true;
          break;
        case item.nama.toLowerCase().includes('tmpt'):
          isTmpdik = true;
          break;
        case item.nama.toLowerCase().includes('pull'):
          isPullUp = true;
          break;
      }
    });

    const paramjenis_diktuk: number[] = [];
    filter.jenis_diktuk.forEach((data) => {
      if (!isNaN(parseInt(data))) {
        paramjenis_diktuk.push(parseInt(data));
      }
    });

    const siswas: any = await this.kelolaDataGetByjenis_diktuk(
      filter.page,
      filter.limit,
      paramjenis_diktuk,
      filter.tahun,
      filter.gelombang,
    );
    const queryResult = [];
    siswas.forEach((siswa, index) => {
      siswas[index].tgl_lahir = dayjs(siswa.tanggal_lahir).format('YYYY-MM-DD');

      const d: any = {
        no_urut: siswa.no_urut_asalrim,
        jenis_diktuk: siswa.jenis_diktuk?.nama,
        tahun: siswa.ta_pat_diktuk,
        nrp: siswa.siswa_patma?.nrp,
        no_ak_nosis: siswa.no_ak_nosis,
        all: {
          siswa: {
            no_urut: siswa.no_urut_asalrim,
            jenis_diktuk: siswa.jenis_diktuk?.nama,
            tahun: siswa.ta_pat_diktuk,
            nrp: siswa.siswa_patma?.nrp,
            no_ak_nosis: siswa.no_ak_nosis,
            nik: siswa.nik,
            nama: siswa.nama_lengkap,
            jenis_kelamin: siswa.jenis_kelamin,
            no_registrasi: siswa.no_registrasi_online,
            gelombang: siswa.gelombang_pat_diktuk,
            kompetensi_diktuk: siswa.kompetensi_diktuk.nama,
          },
          sosial_dan_keluarga: {
            agama: siswa.agama?.nama,
            suku: siswa.suku,
            email: siswa.email,
            nomor_hp_wa: siswa.no_hp,
            media_sosial_facebook: siswa.medsos_facebook,
            media_sosial_twitter: siswa.medsos_twitter,
            media_sosial_instagram: siswa.medsos_instagram,
            media_sosial_tiktok: siswa.medsos_tiktok,
            anak_ke: siswa.siswa_keluarga_ref?.anak_ke,
            jumlah_saudara: siswa.siswa_keluarga_ref?.jumlah_saudara,
            nama_ayah: siswa.siswa_keluarga_ref?.nama_ayah,
            pekerjaan_ayah: siswa.siswa_keluarga_ref?.pek_ayah,
            status_ayah: siswa.siswa_keluarga_ref?.status_ayah,
            umur_ayah: siswa.siswa_keluarga_ref?.umur_ayah,
            golongan_pangkat_ayah: siswa.siswa_keluarga_ref?.gol_pangkat_ayah,
            jabatan_ayah: siswa.siswa_keluarga_ref?.jabatan_ayah,
            nama_ibu: siswa.siswa_keluarga_ref?.nama_ibu,
            pekerjaan_ibu: siswa.siswa_keluarga_ref?.pek_ibu,
            status_ibu: siswa.siswa_keluarga_ref?.status_ibu,
            umur_ibu: siswa.siswa_keluarga_ref?.umur_ibu,
            golongan_pangkat_ibu: siswa.siswa_keluarga_ref?.gol_pangkat_ibu,
            jabatan_ibu: siswa.siswa_keluarga_ref?.jabatan_ibu,
            nomor_hp_orang_tua: siswa.siswa_keluarga_ref?.no_hp_ortu,
            provinsi_alamat_rumah: siswa.siswa_alamat_ref?.provinsi.nama,
            kotamadya_alamat_rumah: siswa.siswa_alamat_ref?.kabupaten.nama,
            kecamatan_alamat_rumah: siswa.siswa_alamat_ref?.kecamatan.nama,
            kelurahan_alamat_rumah: siswa.siswa_alamat_ref?.kelurahan.nama,
            alamat_rumah_lengkap: siswa.siswa_alamat_ref?.alamat,
          },
          nilai: {
            hasil_lari_12_menit_jasdiktuk:
              siswa.siswa_diktuk?.hasil_lari_12_m_jasdiktuk,
            nilai_lari_12_menit_jasdiktuk:
              siswa.siswa_diktuk?.n_lari_12_m_jasdiktuk,
            hasil_pull_up_jasdiktuk:
              siswa.siswa_diktuk?.hasil_pull_up_jasdiktuk,
            hasil_situp_jasdiktuk: siswa.siswa_diktuk?.hasil_situp_jasdiktuk,
            nilai_situp_jasdiktuk: siswa.siswa_diktuk?.n_situp_jasdiktuk,
            hasil_pushup_jasdiktuk: siswa.siswa_diktuk?.hasil_pushup_jasdiktuk,
            nilai_pushup_jasdiktuk: siswa.siswa_diktuk?.n_pushup_jasdiktuk,
            hasil_shuttle_run_jasdiktuk:
              siswa.siswa_diktuk?.hasil_shuttlerun_jasdiktuk,
            nilai_shuttle_run_jasdiktuk:
              siswa.siswa_diktuk?.n_shuttlerun_jasdiktuk,
            rata2_nilai_b_jasdiktuk: siswa.siswa_diktuk?.rata2_n_b_jasdiktuk,
            nilai_ab_jasdiktuk: siswa.siswa_diktuk?.n_ab_jasdiktuk,
            nilai_jasmani_diktuk: siswa.siswa_diktuk?.n_jasmani_diktuk,
            nilai_mentalkep_kar_diktuk:
              siswa.siswa_diktuk?.n_mentalkep_kar_diktuk,
            nilai_akademik_peng_diktuk:
              siswa.siswa_diktuk?.n_akademik_peng_diktuk,
            nilai_gabungan_akhir_diktuk: siswa.siswa_diktuk?.n_gbgakhir_diktuk,
            ranking_diktuk: siswa.siswa_diktuk?.ranking_diktuk,
            catatan_kesehatan_diktuk:
              siswa.siswa_diktuk?.catatan_kesehatan_diktuk,
            catatan_pelanggaran_diktuk:
              siswa.siswa_diktuk?.catatan_pelanggaran_diktuk,
            catatan_psikologi_diktuk:
              siswa.siswa_diktuk?.catatan_psikologi_diktuk,
            prestasi_diktuk: siswa.siswa_diktuk?.prestasi_diktuk,
            catatan_khusus_diktuk: siswa.siswa_diktuk?.catatan_khusus_diktuk,
            status_diktuk: siswa.siswa_diktuk?.status_diktuk,
            keterangan_status_diktuk: siswa.siswa_diktuk?.ket_status_diktuk,
          },
          genetik_dan_kemampuan: {
            golongan_darah: siswa.golongan_darah,
            hobi: siswa.hobi,
            tinggi_badan: siswa.siswa_fisik_ref?.tb_cm,
            berat_badan: siswa.siswa_fisik_ref?.bb_kg,
            warna_kulit: siswa.siswa_fisik_ref?.warna_kulit,
            warna_mata: siswa.siswa_fisik_ref?.warna_mata,
            warna_rambut: siswa.siswa_fisik_ref?.warna_rambut,
            jenis_rambut: siswa.siswa_fisik_ref?.jenis_rambut,
            ukuran_topi: siswa.siswa_fisik_ref?.ukuran_topi,
            ukuran_baju: siswa.siswa_fisik_ref?.ukuran_baju,
            ukuran_celana: siswa.siswa_fisik_ref?.ukuran_celana,
            ukuran_sepatu: siswa.siswa_fisik_ref?.ukuran_sepatu,
            kemampuan_bahasa_daerah: siswa.siswa_keahlian_ref?.puan_bhs_daerah,
            keterangan_kemampuan_bahasa_daerah:
              siswa.siswa_keahlian_ref?.ket_puan_bhs_daerah,
            kemampuan_bahasa_asing: siswa.siswa_keahlian_ref?.puan_bhs_asing,
            keterangan_kemampuan_bhs_asing:
              siswa.siswa_keahlian_ref?.ket_puan_bhs_asing,
            keahlian_aplikasi_komputer:
              siswa.siswa_keahlian_ref?.keahlian_aplikasi_komputer,
            keahlian_mengemudi: siswa.siswa_keahlian_ref?.keahlian_mengemudi,
            kepemilikan_sim: siswa.siswa_keahlian_ref?.kepemilikan_sim,
            keahlian_beladiri: siswa.siswa_keahlian_ref?.keahlian_beladiri,
            sabuk_tingkatan_beladiri:
              siswa.siswa_keahlian_ref?.sabuk_tingkatan_beladiri,
          },
        },
      };

      if (isNik) {
        d.nik = siswa.nik;
      }

      if (isNama) {
        d.nik = siswa.nama_lengkap;
      }

      if (isJk) {
        d.jenis_kelamin = siswa.jenis_kelamin;
      }

      if (isNoRegis) {
        d.no_registrasi = siswa.no_registrasi_online;
      }

      if (isSosKel) {
        d.agama = siswa.agama?.nama;
        d.suku = siswa.suku;
        d.email = siswa.email;
        d.nomor_hp_wa = siswa.no_hp;
        d.media_sosial_facebook = siswa.medsos_facebook;
        d.media_sosial_twitter = siswa.medsos_twitter;
        d.media_sosial_instagram = siswa.medsos_instagram;
        d.media_sosial_tiktok = siswa.medsos_tiktok;

        d.anak_ke = siswa.siswa_keluarga_ref?.anak_ke;
        d.jumlah_saudara = siswa.siswa_keluarga_ref?.jumlah_saudara;
        d.nama_ayah = siswa.siswa_keluarga_ref?.nama_ayah;
        d.pekerjaan_ayah = siswa.siswa_keluarga_ref?.pek_ayah;
        d.status_ayah = siswa.siswa_keluarga_ref?.status_ayah;
        d.umur_ayah = siswa.siswa_keluarga_ref?.umur_ayah;
        d.golongan_pangkat_ayah = siswa.siswa_keluarga_ref?.gol_pangkat_ayah;
        d.jabatan_ayah = siswa.siswa_keluarga_ref?.jabatan_ayah;
        d.nama_ibu = siswa.siswa_keluarga_ref?.nama_ibu;
        d.pekerjaan_ibu = siswa.siswa_keluarga_ref?.pek_ibu;
        d.status_ibu = siswa.siswa_keluarga_ref?.status_ibu;
        d.umur_ibu = siswa.siswa_keluarga_ref?.umur_ibu;
        d.golongan_pangkat_ibu = siswa.siswa_keluarga_ref?.gol_pangkat_ibu;
        d.jabatan_ibu = siswa.siswa_keluarga_ref?.jabatan_ibu;
        d.nomor_hp_orang_tua = siswa.siswa_keluarga_ref?.no_hp_ortu;

        d.provinsi_alamat_rumah = siswa.siswa_alamat_ref?.provinsi.nama;
        d.kotamadya_alamat_rumah = siswa.siswa_alamat_ref?.kabupaten.nama;
        d.kecamatan_alamat_rumah = siswa.siswa_alamat_ref?.kecamatan.nama;
        d.kelurahan_alamat_rumah = siswa.siswa_alamat_ref?.kelurahan.nama;
        d.alamat_rumah_lengkap = siswa.siswa_alamat_ref?.alamat;
      }

      if (isNilai) {
        d.hasil_lari_12_menit_jasdiktuk =
          siswa.siswa_diktuk?.hasil_lari_12_m_jasdiktuk;
        d.nilai_lari_12_menit_jasdiktuk =
          siswa.siswa_diktuk?.n_lari_12_m_jasdiktuk;
        d.hasil_pull_up_jasdiktuk = siswa.siswa_diktuk?.hasil_pull_up_jasdiktuk;
        d.hasil_situp_jasdiktuk = siswa.siswa_diktuk?.hasil_situp_jasdiktuk;
        d.nilai_situp_jasdiktuk = siswa.siswa_diktuk?.n_situp_jasdiktuk;
        d.hasil_pushup_jasdiktuk = siswa.siswa_diktuk?.hasil_pushup_jasdiktuk;
        d.nilai_pushup_jasdiktuk = siswa.siswa_diktuk?.n_pushup_jasdiktuk;
        d.hasil_shuttle_run_jasdiktuk =
          siswa.siswa_diktuk?.hasil_shuttlerun_jasdiktuk;
        d.nilai_shuttle_run_jasdiktuk =
          siswa.siswa_diktuk?.n_shuttlerun_jasdiktuk;
        d.rata2_nilai_b_jasdiktuk = siswa.siswa_diktuk?.rata2_n_b_jasdiktuk;
        d.nilai_ab_jasdiktuk = siswa.siswa_diktuk?.n_ab_jasdiktuk;
        d.nilai_jasmani_diktuk = siswa.siswa_diktuk?.n_jasmani_diktuk;
        d.nilai_mentalkep_kar_diktuk =
          siswa.siswa_diktuk?.n_mentalkep_kar_diktuk;
        d.nilai_akademik_peng_diktuk =
          siswa.siswa_diktuk?.n_akademik_peng_diktuk;
        d.nilai_gabungan_akhir_diktuk = siswa.siswa_diktuk?.n_gbgakhir_diktuk;
        d.ranking_diktuk = siswa.siswa_diktuk?.ranking_diktuk;
        d.catatan_kesehatan_diktuk =
          siswa.siswa_diktuk?.catatan_kesehatan_diktuk;
        d.catatan_pelanggaran_diktuk =
          siswa.siswa_diktuk?.catatan_pelanggaran_diktuk;
        d.catatan_psikologi_diktuk =
          siswa.siswa_diktuk?.catatan_psikologi_diktuk;
        d.prestasi_diktuk = siswa.siswa_diktuk?.prestasi_diktuk;
        d.catatan_khusus_diktuk = siswa.siswa_diktuk?.catatan_khusus_diktuk;
        d.status_diktuk = siswa.siswa_diktuk?.status_diktuk;
        d.keterangan_status_diktuk = siswa.siswa_diktuk?.ket_status_diktuk;
      }

      if (isGlb) {
        d.gelombang = siswa.gelombang_pat_diktuk;
      }

      if (isKomDik) {
        d.kompetensi_diktuk = siswa.kompetensi_diktuk.nama;
      }

      if (isGenPuan) {
        d.golongan_darah = siswa.golongan_darah;
        d.hobi = siswa.hobi;

        d.tinggi_badan = siswa.siswa_fisik_ref?.tb_cm;
        d.berat_badan = siswa.siswa_fisik_ref?.bb_kg;
        d.warna_kulit = siswa.siswa_fisik_ref?.warna_kulit;
        d.warna_mata = siswa.siswa_fisik_ref?.warna_mata;
        d.warna_rambut = siswa.siswa_fisik_ref?.warna_rambut;
        d.jenis_rambut = siswa.siswa_fisik_ref?.jenis_rambut;
        d.ukuran_topi = siswa.siswa_fisik_ref?.ukuran_topi;
        d.ukuran_baju = siswa.siswa_fisik_ref?.ukuran_baju;
        d.ukuran_celana = siswa.siswa_fisik_ref?.ukuran_celana;
        d.ukuran_sepatu = siswa.siswa_fisik_ref?.ukuran_sepatu;

        d.kemampuan_bahasa_daerah = siswa.siswa_keahlian_ref?.puan_bhs_daerah;
        d.keterangan_kemampuan_bahasa_daerah =
          siswa.siswa_keahlian_ref?.ket_puan_bhs_daerah;
        d.kemampuan_bahasa_asing = siswa.siswa_keahlian_ref?.puan_bhs_asing;
        d.keterangan_kemampuan_bhs_asing =
          siswa.siswa_keahlian_ref?.ket_puan_bhs_asing;
        d.keahlian_aplikasi_komputer =
          siswa.siswa_keahlian_ref?.keahlian_aplikasi_komputer;
        d.keahlian_mengemudi = siswa.siswa_keahlian_ref?.keahlian_mengemudi;
        d.kepemilikan_sim = siswa.siswa_keahlian_ref?.kepemilikan_sim;
        d.keahlian_beladiri = siswa.siswa_keahlian_ref?.keahlian_beladiri;
        d.sabuk_tingkatan_beladiri =
          siswa.siswa_keahlian_ref?.sabuk_tingkatan_beladiri;
      }

      if (isTmpdik) {
        d.no_urut_tempat_pendidikan = siswa.siswa_diktuk?.no_urut_tmp_dik;
      }

      if (isPullUp) {
        d.nilai_pull_up_jasdiktuk = siswa.siswa_diktuk?.n_pull_up_jasdiktuk;
      }

      queryResult.push(d);
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async kelolaDataGetByjenis_diktuk(
    page: number,
    limit: number,
    jenis_diktuks: number[],
    tahun: string,
    gelombang: string,
  ) {
    try {
      const where: any = {
        jenis_diktuk_id: {
          in: jenis_diktuks,
        },
      };

      if (tahun != '') {
        where.ta_pat_diktuk = tahun;
      }

      if (gelombang != '') {
        where.gelombang_pat_diktuk = gelombang;
      }

      return await this.prisma.siswa.findMany({
        include: {
          jenis_diktuk: true,
          agama: true,
          siswa_patma: true,
          siswa_diktuk: true,
          siswa_keluarga_ref: true,
          siswa_keahlian_ref: true,
          siswa_alamat_ref: {
            include: {
              provinsi: true,
              kabupaten: true,
              kecamatan: true,
              kelurahan: true,
            },
          },
          kompetensi_diktuk: true,
          sub_kompetensi_diktuk: true,
          sub_sub_kompetensi_diktuk: true,
          tmpdik: true,
          siswa_asal_rim_polda: true,
          siswa_asal_rim_polres: true,
          siswa_fisik_ref: true,
        },
        where: where,
        orderBy: {
          no_urut_asalrim: Prisma.SortOrder.asc,
        },
        take: +limit,
        skip: +limit * (+page - 1),
      });
    } catch (e) {
      throw e;
    }
  }

  async KelolaDataGetList(param: {
    column: { [key: string]: string[] };
    search: any;
    page: number;
    limit: number;
  }) {
    const select: Prisma.siswaSelect = {};
    const selectPatma: Prisma.siswa_patmaSelect = {};
    const selectRiwayatPendidikan: Prisma.siswa_riwayat_pendidikanSelect = {};
    const selectFisik: Prisma.siswa_fisikSelect = {};
    const selectKeahlian: Prisma.siswa_keahlianSelect = {};
    const selectHobi: Prisma.siswa_hobiSelect = {};
    const selectKeluarga: Prisma.siswa_keluargaSelect = {};
    const selectAlamat: Prisma.siswa_alamatSelect = {};
    const selectPenerimaan: Prisma.siswa_penerimaanSelect = {};
    const selectDiktuk: Prisma.siswa_diktukSelect = {};
    const selectKeswaAngket: Prisma.siswa_keswa_angketSelect = {};
    Object.keys(param.column).forEach((column) => {
      param.column[column].forEach((value) => {
        switch (column) {
          case 'siswa':
            switch (value) {
              case 'kompetensi_diktuk_id':
                select.kompetensi_diktuk = true;
                break;
              case 'sub_kompetensi_diktuk_id':
                select.sub_kompetensi_diktuk = true;
                break;
              case 'sub_sub_kompetensi_diktuk_id':
                select.sub_sub_kompetensi_diktuk = true;
                break;
              case 'tmp_dik_id':
                select.tmpdik = true;
                break;
              case 'asal_rim_polda_id':
                select.siswa_asal_rim_polda = true;
                break;
              case 'asal_rim_polres_id':
                select.siswa_asal_rim_polres = true;
                break;
              case 'status_kawin_id':
                select.status_kawin = true;
                break;
              case 'jenis_diktuk_id':
                select.jenis_diktuk = true;
                break;
              case 'agama_id':
                select.agama = true;
                break;
            }
            select[value] = true;
            break;
          case 'siswa_patma':
            switch (value) {
              case 'satker_patma':
                selectPatma.satker_patma_ref = true;
                break;
            }
            selectPatma[value] = true;
            break;
          case 'siswa_riwayat_pendidikan':
            selectRiwayatPendidikan[value] = true;
            break;
          case 'siswa_fisik':
            selectFisik[value] = true;
            break;
          case 'siswa_keahlian':
            selectKeahlian[value] = true;
            break;
          case 'siswa_hobi':
            selectHobi[value] = true;
            break;
          case 'siswa_keluarga':
            selectKeluarga[value] = true;
            break;
          case 'siswa_alamat':
            switch (value) {
              case 'provinsi_id':
                selectAlamat.provinsi = true;
                break;
              case 'kabupaten_id':
                selectAlamat.kabupaten = true;
                break;
              case 'kecamatan_id':
                selectAlamat.kecamatan = true;
                break;
              case 'kelurahan_id':
                selectAlamat.kelurahan = true;
                break;
            }
            selectAlamat[value] = true;
            break;
          case 'siswa_penerimaan':
            selectPenerimaan[value] = true;
            break;
          case 'siswa_diktuk':
            selectDiktuk[value] = true;
            break;
          case 'siswa_keswa_angket':
            switch (value) {
              case 'angket_1':
                selectKeswaAngket.siswa_keswa_angket_satuan_angket_1 = true;
                break;
              case 'angket_2':
                selectKeswaAngket.siswa_keswa_angket_satuan_angket_2 = true;
                break;
              case 'angket_3':
                selectKeswaAngket.siswa_keswa_angket_satuan_angket_3 = true;
                break;
              case 'angket_4':
                selectKeswaAngket.siswa_keswa_angket_satuan_angket_4 = true;
                break;
              case 'angket_5':
                selectKeswaAngket.siswa_keswa_angket_satuan_angket_5 = true;
                break;
            }
            selectKeswaAngket[value] = true;
            break;
        }
      });
    });

    const selectQ: Prisma.siswaSelect = {
      ...select,
    };

    if (Object.keys(selectPatma).length > 0) {
      selectQ.siswa_patma = { select: selectPatma };
    }
    if (Object.keys(selectRiwayatPendidikan).length > 0) {
      selectQ.siswa_riwayat_pendidikan = { select: selectRiwayatPendidikan };
    }
    if (Object.keys(selectFisik).length > 0) {
      selectQ.siswa_fisik_ref = { select: selectFisik };
    }
    if (Object.keys(selectKeahlian).length > 0) {
      selectQ.siswa_keahlian_ref = { select: selectKeahlian };
    }
    if (Object.keys(selectHobi).length > 0) {
      selectQ.siswa_hobi = { select: selectHobi };
    }
    if (Object.keys(selectKeluarga).length > 0) {
      selectQ.siswa_keluarga_ref = { select: selectKeluarga };
    }
    if (Object.keys(selectAlamat).length > 0) {
      selectQ.siswa_alamat_ref = { select: selectAlamat };
    }
    if (Object.keys(selectPenerimaan).length > 0) {
      selectQ.siswa_penerimaan_ref = { select: selectPenerimaan };
    }
    if (Object.keys(selectDiktuk).length > 0) {
      selectQ.siswa_diktuk = { select: selectDiktuk };
    }
    if (Object.keys(selectKeswaAngket).length > 0) {
      selectQ.siswa_keswa_angket = { select: selectKeswaAngket };
    }

    const where: Prisma.siswaWhereInput = {};
    const wherePatma: Prisma.siswa_patmaWhereInput = {};
    const whereRiwayatPendidikan: Prisma.siswa_riwayat_pendidikanWhereInput =
      {};
    const whereFisik: Prisma.siswa_fisikWhereInput = {};
    const whereKeahlian: Prisma.siswa_keahlianWhereInput = {};
    const whereKeluarga: Prisma.siswa_keluargaWhereInput = {};
    const whereAlamat: Prisma.siswa_alamatWhereInput = {};
    const wherePenerimaan: Prisma.siswa_penerimaanWhereInput = {};
    const whereDiktuk: Prisma.siswa_diktukWhereInput = {};
    const whereKeswaAngket: Prisma.siswa_keswa_angketWhereInput = {};
    Object.keys(param.search).forEach((k) => {
      param.search[k].forEach((s) => {
        const columnType = Prisma.dmmf.datamodel.models
          .find((m) => m.name == k)
          .fields.find((f) => f.name == s.column).type;
        switch (columnType.toLowerCase()) {
          case 'int':
          case 'bigint':
          case 'decimal':
            switch (k) {
              case 'siswa':
                switch (s.column) {
                  case 'asal_rim_polda_id':
                    where.siswa_asal_rim_polda = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'asal_rim_polres_id':
                    where.siswa_asal_rim_polres = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  default:
                    where[s.column] = Number(s.value);
                    break;
                }
                break;
              case 'siswa_patma':
                switch (s.column) {
                  case 'kecamatan_id':
                    wherePatma.satker_patma_ref = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  default:
                    wherePatma[s.column] = Number(s.value);
                    break;
                }
                break;
              case 'siswa_riwayat_pendidikan':
                whereRiwayatPendidikan[s.column] = Number(s.value);
                break;
              case 'siswa_fisik':
                whereFisik[s.column] = Number(s.value);
                break;
              case 'siswa_keahlian':
                whereKeahlian[s.column] = Number(s.value);
                break;
              case 'siswa_keluarga':
                whereKeluarga[s.column] = Number(s.value);
                break;
              case 'siswa_alamat':
                switch (s.column) {
                  case 'kecamatan_id':
                    whereAlamat.kecamatan = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'kelurahan_id':
                    whereAlamat.kelurahan = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  default:
                    whereAlamat[s.column] = Number(s.value);
                    break;
                }
                break;
              case 'siswa_penerimaan':
                wherePenerimaan[s.column] = Number(s.value);
                break;
              case 'siswa_diktuk':
                whereDiktuk[s.column] = Number(s.value);
                break;
              case 'siswa_keswa_angket':
                switch (s.column) {
                  case 'angket_1':
                    whereKeswaAngket.siswa_keswa_angket_satuan_angket_1 = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'angket_2':
                    whereKeswaAngket.siswa_keswa_angket_satuan_angket_2 = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'angket_3':
                    whereKeswaAngket.siswa_keswa_angket_satuan_angket_3 = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'angket_4':
                    whereKeswaAngket.siswa_keswa_angket_satuan_angket_4 = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  case 'angket_5':
                    whereKeswaAngket.siswa_keswa_angket_satuan_angket_5 = {
                      nama: {
                        contains: s.value,
                        mode: 'insensitive',
                      },
                    };
                    break;
                  default:
                    whereKeswaAngket[s.column] = Number(s.value);
                    break;
                }
                break;
            }
            break;
          case 'string':
            switch (k) {
              case 'siswa':
                where[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_patma':
                wherePatma[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_riwayat_pendidikan':
                whereRiwayatPendidikan[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_fisik':
                whereFisik[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_keahlian':
                whereKeahlian[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_keluarga':
                whereKeluarga[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_alamat':
                whereAlamat[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_penerimaan':
                wherePenerimaan[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_diktuk':
                whereDiktuk[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
              case 'siswa_keswa_angket':
                whereKeswaAngket[s.column] = {
                  contains: s.value,
                  mode: 'insensitive',
                };
                break;
            }
            break;
          case 'datetime':
            switch (k) {
              case 'siswa':
                where[s.column] = {
                  equals: new Date(s.value),
                };
                break;
              case 'siswa_patma':
                wherePatma[s.column] = {
                  equals: new Date(s.value),
                };
                break;
            }
            break;
          case 'hobi':
            where.siswa_hobi = {
              some: {
                hobi: {
                  nama: {
                    contains: s.value,
                    mode: 'insensitive',
                  },
                },
              },
            };
            break;
        }
      });
    });

    const whereQ: Prisma.siswaWhereInput = {
      ...where,
    };

    if (Object.keys(wherePatma).length > 0) {
      whereQ.siswa_patma = wherePatma;
    }

    if (Object.keys(whereRiwayatPendidikan).length > 0) {
      whereQ.siswa_riwayat_pendidikan = whereRiwayatPendidikan;
    }

    if (Object.keys(whereFisik).length > 0) {
      whereQ.siswa_fisik_ref = whereFisik;
    }

    if (Object.keys(whereKeahlian).length > 0) {
      whereQ.siswa_keahlian_ref = whereKeahlian;
    }

    if (Object.keys(whereKeluarga).length > 0) {
      whereQ.siswa_keluarga_ref = whereKeluarga;
    }

    if (Object.keys(whereAlamat).length > 0) {
      whereQ.siswa_alamat_ref = whereAlamat;
    }

    if (Object.keys(wherePenerimaan).length > 0) {
      whereQ.siswa_penerimaan_ref = wherePenerimaan;
    }

    if (Object.keys(whereDiktuk).length > 0) {
      whereQ.siswa_diktuk = whereDiktuk;
    }

    if (Object.keys(whereKeswaAngket).length > 0) {
      whereQ.siswa_keswa_angket = whereKeswaAngket;
    }

    return this.prisma.siswa.findMany({
      select: selectQ,
      where: whereQ,
      orderBy: {
        no_urut_asalrim: Prisma.SortOrder.asc,
      },
      take: +param.limit,
      skip: +param.limit * (+param.page - 1),
    });
  }
}
