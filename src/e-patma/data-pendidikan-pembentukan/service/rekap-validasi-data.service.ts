import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { FilterRekapDto } from '../dto/data-pendidikan-pembentukan.dto';

@Injectable()
export class RekapValidasiDataService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getRekapValidasiData(req: any, filter: FilterRekapDto) {
    const getData = await this.rekapValidasiDataGetWithFilter(
      filter.tahun,
      filter.gelombang,
    );
    if (getData.length == 0) {
      return {
        statusCode: HttpStatus.OK,
        message: 'Success',
      };
    }

    const queryResult = [];
    let group = [];
    let total = 0;
    let totalValidasiSudah = 0;
    let totalValidasiBelum = 0;
    let totalAngketSudah = 0;
    let totalAngketBelum = 0;
    let totalUploadSudah = 0;
    let totalUploadBelum = 0;
    let totalSiswaAktif = 0;
    let totalSiswaNonAktif = 0;
    let totalSiswaMeninggal = 0;
    let totalSiswaTdkLulus = 0;
    let title = '';
    let no = 1;
    for (const item of getData) {
      if (title == '') {
        title = item.nama;
        total += Number(item.jml_siswa);
        totalValidasiSudah += Number(item.sudah);
        totalValidasiBelum += Number(item.belum);
        totalAngketSudah += Number(item.sudah_isi);
        totalAngketBelum += Number(item.belum_isi);
        totalUploadSudah += Number(item.sudah_upload);
        totalUploadBelum += Number(item.belum_upload);
        totalSiswaAktif += Number(item.aktif);
        totalSiswaNonAktif += Number(item.non_aktif);
        totalSiswaMeninggal += Number(item.meninggal);
        totalSiswaTdkLulus += Number(item.tdk_lulus);
        group.push({
          no: no,
          spn: item.nama_rim_polda,
          jumlah_siswa: item.jml_siswa,
          verifikasi_nrp: item.nrp,
          pengisian_angket: item.sudah_isi + item.belum_isi,
          upload_dokumen: item.sudah_upload + item.belum_upload,
          presentase: '',
        });
        no++;
        continue;
      }

      if (title == item.nama) {
        total += Number(item.jml_siswa);
        totalValidasiSudah += Number(item.sudah);
        totalValidasiBelum += Number(item.belum);
        totalAngketSudah += Number(item.sudah_isi);
        totalAngketBelum += Number(item.belum_isi);
        totalUploadSudah += Number(item.sudah_upload);
        totalUploadBelum += Number(item.belum_upload);
        totalSiswaAktif += Number(item.aktif);
        totalSiswaNonAktif += Number(item.non_aktif);
        totalSiswaMeninggal += Number(item.meninggal);
        totalSiswaTdkLulus += Number(item.tdk_lulus);
        group.push({
          no: no,
          spn: item.nama_rim_polda,
          jumlah_siswa: item.jml_siswa,
          verifikasi_nrp: item.nrp,
          pengisian_angket: item.sudah_isi + item.belum_isi,
          upload_dokumen: item.sudah_upload + item.belum_upload,
          presentase: '',
        });
        no++;
        continue;
      }

      group.forEach((item, index) => {
        group[index].presentase =
          `${((Number(item.jumlah_siswa) / total) * 100).toFixed(2)} %`;
      });

      const getStatusSiswa = await this.rekapValidasiDataGetStatusSiswaDetail(
        filter.tahun,
        filter.gelombang,
        title,
      );
      const aktif_detail = [];
      const non_aktif_detail = [];
      const meninggal_dunia_detail = [];
      const tidak_lulus_detail = [];
      getStatusSiswa.forEach((item) => {
        switch (item.siswa_diktuk.status_diktuk) {
          case 'AKTIF':
            aktif_detail.push({ nama: item.nama_lengkap });
            break;
          case 'DIKELUARKAN':
            non_aktif_detail.push({ nama: item.nama_lengkap });
            break;
          case 'MD':
            meninggal_dunia_detail.push({ nama: item.nama_lengkap });
            break;
          case 'TIDAK LULUS':
            tidak_lulus_detail.push({ nama: item.nama_lengkap });
            break;
        }
      });

      queryResult.push({
        title: title,
        jumlah_siswa: {
          serdik: total,
          persen: '100%',
        },
        validasi_data: {
          sudah: totalValidasiSudah,
          belum: totalValidasiBelum,
          total: totalValidasiSudah + totalValidasiBelum,
          sudah_persen: `${((totalValidasiSudah / (totalValidasiSudah + totalValidasiBelum)) * 100).toFixed(2)} %`,
          belum_persen: `${((totalValidasiBelum / (totalValidasiSudah + totalValidasiBelum)) * 100).toFixed(2)} %`,
        },
        pengisian_angket: {
          sudah_isi: totalAngketSudah,
          belum_isi: totalAngketBelum,
          total: totalAngketSudah + totalAngketBelum,
          sudah_isi_persen: `${((totalAngketSudah / (totalAngketSudah + totalAngketBelum)) * 100).toFixed(2)} %`,
          belum_isi_persen: `${((totalAngketBelum / (totalAngketSudah + totalAngketBelum)) * 100).toFixed(2)} %`,
        },
        upload_dokumen: {
          sudah_upload: totalUploadSudah,
          belum_upload: totalUploadBelum,
          total: totalUploadSudah + totalUploadBelum,
          sudah_upload_persen: `${((totalUploadSudah / (totalUploadSudah + totalUploadBelum)) * 100).toFixed(2)} %`,
          belum_upload_persen: `${((totalUploadBelum / (totalUploadSudah + totalUploadBelum)) * 100).toFixed(2)} %`,
        },
        status_siswa: {
          total:
            totalSiswaAktif +
            totalSiswaNonAktif +
            totalSiswaMeninggal +
            totalSiswaTdkLulus,
          aktif: totalSiswaAktif,
          aktif_detail,
          non_aktif: totalSiswaNonAktif,
          non_aktif_detail,
          meninggal_dunia: totalSiswaMeninggal,
          meninggal_dunia_detail,
          tidak_lulus: totalSiswaTdkLulus,
          tidak_lulus_detail,
          aktif_persen: `${(
            (totalSiswaAktif /
              (totalSiswaAktif +
                totalSiswaNonAktif +
                totalSiswaMeninggal +
                totalSiswaTdkLulus)) *
            100
          ).toFixed(2)} %`,
          non_aktif_persen: `${(
            (totalSiswaNonAktif /
              (totalSiswaAktif +
                totalSiswaNonAktif +
                totalSiswaMeninggal +
                totalSiswaTdkLulus)) *
            100
          ).toFixed(2)} %`,
          meninggal_dunia_persen: `${(
            (totalSiswaMeninggal /
              (totalSiswaAktif +
                totalSiswaNonAktif +
                totalSiswaMeninggal +
                totalSiswaTdkLulus)) *
            100
          ).toFixed(2)} %`,
          tidak_lulus_persen: `${(
            (totalSiswaTdkLulus /
              (totalSiswaAktif +
                totalSiswaNonAktif +
                totalSiswaMeninggal +
                totalSiswaTdkLulus)) *
            100
          ).toFixed(2)} %`,
        },
        group: group,
      });

      no = 1;
      title = item.nama;
      group = [
        {
          no: no,
          spn: item.nama_rim_polda,
          jumlah_siswa: item.jml_siswa,
          verifikasi_nrp: item.nrp,
          pengisian_angket: item.sudah_isi + item.belum_isi,
          upload_dokumen: item.sudah_upload + item.belum_upload,
          presentase: '',
        },
      ];
      total = Number(item.jml_siswa);
      totalValidasiSudah = Number(item.sudah);
      totalValidasiBelum = Number(item.belum);
      totalAngketSudah = Number(item.sudah_isi);
      totalAngketBelum = Number(item.belum_isi);
      totalUploadSudah = Number(item.sudah_upload);
      totalUploadBelum = Number(item.belum_upload);
      totalSiswaAktif = Number(item.aktif);
      totalSiswaNonAktif = Number(item.non_aktif);
      totalSiswaMeninggal = Number(item.meninggal);
      totalSiswaTdkLulus = Number(item.tdk_lulus);
    }

    group.forEach((item, index) => {
      group[index].presentase =
        `${((Number(item.jumlah_siswa) / total) * 100).toFixed(2)} %`;
    });

    queryResult.push({
      title: title,
      jumlah_siswa: {
        serdik: total,
        persen: '100%',
      },
      validasi_data: {
        sudah: totalValidasiSudah,
        belum: totalValidasiBelum,
        total: totalValidasiSudah + totalValidasiBelum,
        sudah_persen: `${((totalValidasiSudah / (totalValidasiSudah + totalValidasiBelum)) * 100).toFixed(2)} %`,
        belum_persen: `${((totalValidasiBelum / (totalValidasiSudah + totalValidasiBelum)) * 100).toFixed(2)} %`,
      },
      pengisian_angket: {
        sudah_isi: totalAngketSudah,
        belum_isi: totalAngketBelum,
        total: totalAngketSudah + totalAngketBelum,
        sudah_isi_persen: `${((totalAngketSudah / (totalAngketSudah + totalAngketBelum)) * 100).toFixed(2)} %`,
        belum_isi_persen: `${((totalAngketBelum / (totalAngketSudah + totalAngketBelum)) * 100).toFixed(2)} %`,
      },
      upload_dokumen: {
        sudah_upload: totalUploadSudah,
        belum_upload: totalUploadBelum,
        total: totalUploadSudah + totalUploadBelum,
        sudah_upload_persen: `${((totalUploadSudah / (totalUploadSudah + totalUploadBelum)) * 100).toFixed(2)} %`,
        belum_upload_persen: `${((totalUploadBelum / (totalUploadSudah + totalUploadBelum)) * 100).toFixed(2)} %`,
      },
      status_siswa: {
        total:
          totalSiswaAktif +
          totalSiswaNonAktif +
          totalSiswaMeninggal +
          totalSiswaTdkLulus,
        aktif: totalSiswaAktif,
        non_aktif: totalSiswaNonAktif,
        meninggal_dunia: totalSiswaMeninggal,
        tidak_lulus: totalSiswaTdkLulus,
        aktif_persen: `${(
          (totalSiswaAktif /
            (totalSiswaAktif +
              totalSiswaNonAktif +
              totalSiswaMeninggal +
              totalSiswaTdkLulus)) *
          100
        ).toFixed(2)} %`,
        non_aktif_persen: `${(
          (totalSiswaNonAktif /
            (totalSiswaAktif +
              totalSiswaNonAktif +
              totalSiswaMeninggal +
              totalSiswaTdkLulus)) *
          100
        ).toFixed(2)} %`,
        meninggal_dunia_persen: `${(
          (totalSiswaMeninggal /
            (totalSiswaAktif +
              totalSiswaNonAktif +
              totalSiswaMeninggal +
              totalSiswaTdkLulus)) *
          100
        ).toFixed(2)} %`,
        tidak_lulus_persen: `${(
          (totalSiswaTdkLulus /
            (totalSiswaAktif +
              totalSiswaNonAktif +
              totalSiswaMeninggal +
              totalSiswaTdkLulus)) *
          100
        ).toFixed(2)} %`,
      },
      group: group,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async rekapValidasiDataGetWithFilter(tahun: number, gelombang: string) {
    try {
      let where = 'where s.deleted_at is null';

      if (tahun) {
        where += ` and s.ta_pat_diktuk = '${tahun}'`;
      }

      if (gelombang) {
        where += ` and s.gelombang_pat_diktuk = '${gelombang}'`;
      }

      const query = `
          select j.nama,
                 st.nama                                                         nama_rim_polda,
                 count(s.id)                                                  as jml_siswa,
                 count(case when s.status_validation = true then 1 end)       as sudah,
                 count(case when s.status_validation = false then 1 end)      as belum,
                 count(case when ska.id is not null then 1 end)               as sudah_isi,
                 count(case when ska.id is null then 0 end)                   as belum_isi,
                 count(case when s.status_upload = true then 1 end)           as sudah_upload,
                 count(case when s.status_upload = false then 1 end)          as belum_upload,
                 count(case when sd.status_diktuk = 'AKTIF' then 1 end)       as aktif,
                 count(case when sd.status_diktuk = 'DIKELUARKAN' then 1 end) as non_aktif,
                 count(case when sd.status_diktuk = 'MD' then 1 end)          as meninggal,
                 count(case when sd.status_diktuk = 'TIDAK LULUS' then 1 end) as tdk_lulus,
                 count(case when sp.nrp is not null then 1 end)               as nrp
          from siswa s
                   inner join jenis_diktuk j on s.jenis_diktuk_id = j.id
                   inner join satuan st on s.asal_rim_polda_id = st.id
                   inner join siswa_diktuk sd on s.id = sd.siswa_id
                   left join siswa_keswa_angket ska on s.id = ska.siswa_id
                   left join siswa_patma sp on s.id = sp.siswa_id
              ${where}
          group by j.nama, st.nama
          order by j.nama, st.nama

      `;
      return await this.prisma.$queryRawUnsafe<
        {
          nama: string;
          nama_rim_polda: string;
          jml_siswa: number;
          sudah: number;
          belum: number;
          sudah_isi: number;
          belum_isi: number;
          sudah_upload: number;
          belum_upload: number;
          aktif: number;
          non_aktif: number;
          meninggal: number;
          tdk_lulus: number;
          nrp: number;
        }[]
      >(query);
    } catch (e) {
      throw e;
    }
  }

  async rekapValidasiDataGetStatusSiswaDetail(
    tahun: number,
    gelombang: string,
    namaDiktuk: string,
  ) {
    return this.prisma.siswa.findMany({
      select: {
        nama_lengkap: true,
        siswa_diktuk: {
          select: {
            status_diktuk: true,
          },
        },
      },
      where: {
        deleted_at: null,
        ta_pat_diktuk: tahun.toString(),
        gelombang_pat_diktuk: gelombang,
        jenis_diktuk: {
          nama: namaDiktuk,
        },
      },
    });
  }
}
