import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { PaginationDto } from '../../../core/dtos';

export class FilterRekapDto {
  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  tahun?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  gelombang?: string;
}

export class FilterDto {
  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  start_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  start_gelombang?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  end_year?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  end_gelombang?: string;
}

export class UploadDataTemplateQueryDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  jenis_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  tahun?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  gelombang?: string;
}

export class SiswaParamDto {
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @IsArray()
  @Type(() => SiswaDto)
  data: SiswaDto[];
}

export class SiswaDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  no_urut_asalrim?: number;

  @Type(() => String)
  @IsOptional()
  unique_id?: string;

  @Type(() => String)
  @IsOptional()
  nama?: string;

  @Type(() => String)
  @IsOptional()
  nama_dengan_gelar?: string;

  @Type(() => String)
  @IsOptional()
  jk?: string;

  @Type(() => String)
  @IsOptional()
  tmp_lahir?: string;

  @ValidateIf(
    (o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  tgl_lahir?: Date;

  @Type(() => String)
  @IsOptional()
  nrp?: string;

  @Type(() => String)
  @IsOptional()
  nik?: string;

  @Type(() => String)
  @IsOptional()
  jenis_pekerjaan?: string;

  @Type(() => String)
  @IsOptional()
  status_kawin?: string;

  @Type(() => String)
  @IsOptional()
  no_ujian_polda?: string;

  @Type(() => String)
  @IsOptional()
  no_registrasi_online?: string;

  @Type(() => String)
  @IsOptional()
  jenis_diktuk?: string;

  @Type(() => Number)
  @IsNumber()
  @Expose()
  @IsOptional()
  ta_rim_diktuk?: number;

  @Type(() => String)
  @IsOptional()
  gelombang_rim_masuk_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  sub_sub_kompetensi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  tmp_dik?: string;

  @Type(() => String)
  @IsOptional()
  asal_rim_polda?: string;

  @Type(() => String)
  @IsOptional()
  ta_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  gelombang_pat_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  ijazah_dikum_gun_seleksi_rim?: string;

  @Type(() => String)
  @IsOptional()
  username?: string;

  @Type(() => String)
  @IsOptional()
  password?: string;

  @Type(() => String)
  @IsOptional()
  no_ak_nosis?: string;

  @Type(() => String)
  @IsOptional()
  no_urut_tmp_dik?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  hasil_lari_12_m_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_a_lari_12_menit_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  hasil_pull_up_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_pull_up_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  hasil_situp_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_situp_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  hasil_pushup_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_pushup_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  hasil_shuttlerun_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_shuttle_run_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  rata2_n_b_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_ab_jasdiktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_jasmani_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_mentalkep_kar_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_akademik_peng_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  n_gbgakhir_diktuk?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  ranking_diktuk?: number;

  @Type(() => String)
  @IsOptional()
  catatan_kesehatan_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  catatan_pelanggaran_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  catatan_psikologi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  prestasi_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  catatan_khusus_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  status_diktuk?: string;

  @Type(() => String)
  @IsOptional()
  ket_status_diktuk?: string;
}

export interface Siswa {
  no_urut_asalrim?: number;
  unique_id?: string;
  nama_dengan_gelar?: string;
  nrp?: string;
  nik?: string;
  jenis_pekerjaan?: string;
  no_ujian_polda?: string;
  no_registrasi_online?: string;
  ta_rim_diktuk?: number;
  gelombang_rim_masuk_diktuk?: string;
  ta_pat_diktuk?: string;
  gelombang_pat_diktuk?: string;
  username?: string;
  password?: string;
  no_ak_nosis?: string;
  no_urut_tmp_dik?: string;
  hasil_lari_12_m_jasdiktuk?: number;
  hasil_pull_up_jasdiktuk?: number;
  n_pull_up_jasdiktuk?: number;
  hasil_situp_jasdiktuk?: number;
  n_situp_jasdiktuk?: number;
  hasil_pushup_jasdiktuk?: number;
  n_pushup_jasdiktuk?: number;
  hasil_shuttlerun_jasdiktuk?: number;
  rata2_n_b_jasdiktuk?: number;
  n_ab_jasdiktuk?: number;
  n_jasmani_diktuk?: number;
  n_mentalkep_kar_diktuk?: number;
  n_akademik_peng_diktuk?: number;
  n_gbgakhir_diktuk?: number;
  ranking_diktuk?: number;
  catatan_kesehatan_diktuk?: string;
  catatan_pelanggaran_diktuk?: string;
  catatan_psikologi_diktuk?: string;
  prestasi_diktuk?: string;
  catatan_khusus_diktuk?: string;
  status_diktuk?: string;
  ket_status_diktuk?: string;
}

export interface SiswaDB extends Siswa {
  nama_lengkap?: string;
  jenis_kelamin?: string;
  tempat_lahir?: string;
  tanggal_lahir?: Date;
  status_kawin_id?: bigint;
  jenis_diktuk_id?: bigint;
  kompetensi_diktuk_id?: bigint;
  sub_kompetensi_diktuk_id?: bigint;
  sub_sub_kompetensi_diktuk_id?: bigint;
  tmp_dik_id?: bigint;
  asal_rim_polda_id?: bigint;
  ijazah_dikum_seleksi_rim?: string;
  n_lari_12_m_jasdiktuk?: number;
  n_shuttlerun_jasdiktuk?: number;
}

export class FilterKelolaDto extends PaginationDto {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  jenis_diktuk?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_personel?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_rekrutmen?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_diktuk?: string[];

  @Type(() => String)
  @IsOptional()
  tahun?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang?: string;
}

export class FilterKelolaV2Dto extends PaginationDto {
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  data_column?: number[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FilterKelolaSearchDto)
  data_search?: FilterKelolaSearchDto[];
}

export class FilterKelolaSearchDto {
  @IsNumber()
  @Expose()
  id: number;

  @IsString()
  @IsNotEmpty()
  value: string;
}
