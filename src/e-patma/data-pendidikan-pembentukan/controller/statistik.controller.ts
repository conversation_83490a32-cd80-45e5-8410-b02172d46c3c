import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { StatistikService } from '../service/statistik.service';
import { FilterDto } from '../dto/data-pendidikan-pembentukan.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
// import { SiswaJwtAuthGuard } from '../auth/jwt-auth.guard';
// import { Permission } from '../core/decorators';

@Controller('data-pendidikan-pembentukan')
@UseGuards(JwtAuthGuard)
export class StatistikController {
  private readonly logger = new Logger(StatistikController.name);

  constructor(private readonly statistikService: StatistikService) {}

  @Get('/statistik')
  // @Permission('DATA_PENDIDIKAN_PEMBENTUKAN_GET_STATISTIK')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getStatistik(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatistik.name}`);
    const response = await this.statistikService.getStatistik(req);
    this.logger.log(
      `Leaving ${this.getStatistik.name} with response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/statistik/rekap-data-seleksi')
  // @Permission('DATA_PENDIDIKAN_PEMBENTUKAN_GET_STATISTIK_REKAP_DATA_SELEKSI')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getRekapDataSeleksi(@Req() req: any, @Query() param: FilterDto) {
    this.logger.log(
      `Entering ${this.getRekapDataSeleksi.name} with param ${JSON.stringify(param)}`,
    );
    const response = await this.statistikService.getRekapDataSeleksi(
      req,
      param,
    );
    this.logger.log(
      `Leaving ${this.getRekapDataSeleksi.name} with param ${JSON.stringify(param)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
