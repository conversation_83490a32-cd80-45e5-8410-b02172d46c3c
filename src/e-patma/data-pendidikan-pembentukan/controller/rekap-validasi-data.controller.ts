import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { FilterRekapDto } from '../dto/data-pendidikan-pembentukan.dto';
import { RekapValidasiDataService } from '../service/rekap-validasi-data.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('data-pendidikan-pembentukan')
@UseGuards(JwtAuthGuard)
export class RekapValidasiDataController {
  private readonly logger = new Logger(RekapValidasiDataController.name);

  constructor(
    private readonly rekapValidasiDataService: RekapValidasiDataService,
  ) {}

  @Get('/rekap-validasi-data')
  // @Permission('DATA_PENDIDIKAN_PEMBENTUKAN_GET_REKAP_VALIDASI_DATA')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getRekapValidasiData(@Req() req: any, @Query() filter: FilterRekapDto) {
    this.logger.log(
      `Entering ${this.getRekapValidasiData.name} with filter ${JSON.stringify(filter)}`,
    );
    const response = await this.rekapValidasiDataService.getRekapValidasiData(
      req,
      filter,
    );
    this.logger.log(
      `Leaving ${this.getRekapValidasiData.name} with filter ${JSON.stringify(filter)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
