import { <PERSON>du<PERSON> } from '@nestjs/common';
import { StatistikController } from './controller/statistik.controller';
import { RekapValidasiDataController } from './controller/rekap-validasi-data.controller';
import { UploadDataController } from './controller/upload-data.controller';
import { KelolaDataController } from './controller/kelola-data.controller';
import { FileManagerController } from './controller/file-manager.controller';
import { StatistikService } from './service/statistik.service';
import { RekapValidasiDataService } from './service/rekap-validasi-data.service';
import { UploadDataService } from './service/upload-data.service';
import { PortalService } from '../portal/service/portal.service';
import { KelolaDataService } from './service/kelola-data.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { FileManagerService } from './service/file-manager.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [
    StatistikController,
    RekapValidasiDataController,
    UploadDataController,
    KelolaDataController,
    FileManagerController,
  ],
  providers: [
    StatistikService,
    RekapValidasiDataService,
    UploadDataService,
    PortalService,
    KelolaDataService,
    MinioService,
    FileManagerService,
  ],
})
export class DataPendidikanPembentukanModule {}
