import { <PERSON>du<PERSON> } from '@nestjs/common';
import { StatistikController } from './controller/statistik.controller';
import { DownloadDataController } from './controller/download-data.controller';
import { KelolaDataController } from './controller/kelola-data.controller';
import { FileManagerController } from './controller/file-manager.controller';
import { StatistikService } from './service/statistik.service';
import { PortalService } from '../portal/service/portal.service';
import { KelolaDataService } from './service/kelola-data.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { FileManagerService } from './service/file-manager.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { DownloadDataService } from './service/download-data.service';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [
    StatistikController,
    DownloadDataController,
    KelolaDataController,
    FileManagerController,
  ],
  providers: [
    StatistikService,
    PortalService,
    KelolaDataService,
    MinioService,
    FileManagerService,
    DownloadDataService,
  ],
})
export class DistribusiDataDokumenModule {}
