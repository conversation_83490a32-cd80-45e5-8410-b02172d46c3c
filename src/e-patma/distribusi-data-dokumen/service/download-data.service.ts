import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { filterDokumenDto } from '../dto/distribusi-data-dokumen.dto';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { join } from 'path';
import fs from 'fs';
import * as archiver from 'archiver';
import { Response } from 'express';
import PDFDocument from 'pdfkit';

@Injectable()
export class DownloadDataService {
  constructor(
    private readonly minioService: MinioService,
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getDocument(req: any, filter: filterDokumenDto) {
    let countdown: Date;
    let status = true;
    let queryResult = {
      title: `BINTARA ${filter.tahun} Gelombang ${filter.gelombang}`,
      password: 123456,
      dokumen_patma: [
        {
          id: 123,
          name: 'S.Keputusan.pdf',
        },
        {
          id: 234,
          name: 'S.Cuti.pdf',
        },
        {
          id: 345,
          name: 'S.Nominasi-Jalsis.pdf',
        },
        {
          id: 456,
          name: 'S.Penghadapan.pdf',
        },
        {
          id: 567,
          name: 'S.Jugrah-Patma.pdf',
        },
      ],
      dokumen_bintara: [
        {
          id: 112,
          name: 'Kep.Pengangkatan.zip',
        },
        {
          id: 223,
          name: 'Kep.Pangkat.zip',
        },
        {
          id: 334,
          name: 'Kep.Jabatan.zip',
        },
        {
          id: 445,
          name: 'Kep.Presiden.zip',
        },
        {
          id: 556,
          name: 'Data-SPN-Polda-Aceh_Bintara_2020_Gel.2.zip',
        },
      ],
    };

    if (filter.tahun == 2024) {
      queryResult = null;
      status = false;
      countdown = new Date();
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DISTRIBUSI_DATA_DOKUMEN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      status,
      countdown,
    };
  }

  async downloadAll(req: any, res: Response) {
    const uploadDir = join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const zipFileName = `random-files-${Date.now()}.zip`;
    const output = fs.createWriteStream(join(uploadDir, zipFileName));

    const archive = archiver('zip', {
      zlib: { level: 9 },
    });

    output.on('close', () => {
      res.download(join(__dirname, '../../uploads', zipFileName), (err) => {
        if (err) {
          Logger.error('Error in file download:', err);
          res.status(500).send('Could not download the file');
        }

        fs.unlinkSync(join(__dirname, '../../uploads', zipFileName));
      });
    });

    archive.on('error', (err) => {
      throw err;
    });
    archive.pipe(output);

    const queryResult = [
      { name: 'file1.txt', content: 'This is the content of file 1.' },
      { name: 'file2.txt', content: 'This is the content of file 2.' },
      { name: 'file3.txt', content: 'This is the content of file 3.' },
    ];

    queryResult.forEach((file) => {
      archive.append(file.content, { name: file.name });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DISTRIBUSI_DATA_DOKUMEN_READ_MINIO as ConstantLogType,
        message,
        queryResult,
      ),
    );

    await archive.finalize();
  }

  async downloadFile(req: any, res: Response) {
    const doc = new PDFDocument();
    const queryResult = [
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      'Vivamus luctus urna sed urna ultricies ac tempor dui sagittis.',
      'In condimentum facilisis porta.',
      'Sed nec diam eu diam mattis viverra.',
      'Nulla fringilla, orci ac euismod semper.',
    ];
    const randomIndex = Math.floor(Math.random() * queryResult.length);
    const randomLine = queryResult[randomIndex];
    const tmpDir = join(__dirname, '../../tmp');
    if (!fs.existsSync(tmpDir)) {
      fs.mkdirSync(tmpDir, { recursive: true });
    }

    const fileName = `random-pdf-${Date.now()}.pdf`;
    const filePath = join(`${tmpDir}/${fileName}`);
    const writeStream = fs.createWriteStream(filePath);
    doc.pipe(writeStream);
    doc.fontSize(25).text('Random PDF', { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(randomLine);
    doc.end();

    // When the PDF is finished writing, send it as a response

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.EPATMA_DISTRIBUSI_DATA_DOKUMEN_WRITE_PDF as ConstantLogType,
        message,
        queryResult,
      ),
    );
    writeStream.on('finish', () => {
      res.download(filePath, fileName, (err) => {
        if (err) {
          res.status(500).send('Could not download the file.');
        }
        // Optionally delete the file after sending it
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            Logger.error('Error deleting file:', unlinkErr);
          }
        });
      });
    });

    // Handle errors
    writeStream.on('error', () => {
      res.status(500).json({ message: 'Error creating PDF' });
    });
  }
}
