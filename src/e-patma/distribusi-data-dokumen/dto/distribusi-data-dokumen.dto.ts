import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { PaginationDto } from '../../../core/dtos';

export class FilterDto {
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  start_year?: number;

  @Type(() => String)
  @IsOptional()
  start_gelombang?: string;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  end_year?: number;

  @Type(() => String)
  @IsNumber()
  @IsOptional()
  end_gelombang?: string;
}

export class filterDokumenDto {
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  @IsOptional()
  tahun?: number;

  @Type(() => String)
  @IsString()
  @IsOptional()
  gelombang?: string;

  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  jenis_diktuk: number;
}

export class FilterKelolaDto extends PaginationDto {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  jenis_diktuk?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_personel?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_rekrutmen?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  data_diktuk?: string[];

  @Type(() => String)
  @IsOptional()
  tahun?: string;

  @Type(() => String)
  @IsOptional()
  @IsString()
  gelombang?: string;
}

export class lockFolderDto {
  @Type(() => String)
  @IsNotEmpty()
  @IsString()
  path?: string;

  @Type(() => String)
  @IsNotEmpty()
  @IsString()
  password?: string;

  @ValidateIf(
    (_o, value) => value !== null && value !== undefined && value !== '',
  )
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  active_date?: Date;
}

export class FilterKelolaV2Dto extends PaginationDto {
  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  data_column?: number[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FilterKelolaSearchDto)
  data_search?: FilterKelolaSearchDto[];
}

export class FilterKelolaSearchDto {
  @IsNumber()
  @Expose()
  id: number;

  @IsString()
  @IsNotEmpty()
  value: string;
}
