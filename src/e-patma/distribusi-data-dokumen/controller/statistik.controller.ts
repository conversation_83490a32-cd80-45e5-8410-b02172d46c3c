import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { FilterDto } from '../dto/distribusi-data-dokumen.dto';
import { StatistikService } from '../service/statistik.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
// import { SiswaJwtAuthGuard } from '../auth/jwt-auth.guard';
// import { Permission } from '../core/decorators';

@Controller('distribusi-data-dto')
@UseGuards(JwtAuthGuard)
export class StatistikController {
  private readonly logger = new Logger(StatistikController.name);

  constructor(private readonly statistikService: StatistikService) {}

  @Get('/statistik')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_GET_STATISTIK')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getStatistik(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatistik.name}`);
    const response = await this.statistikService.getStatistik(req);
    this.logger.log(
      `Leaving ${this.getStatistik.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/statistik/rekap-data-seleksi-rekrutmen')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_GET_REKAP_DATA_SELEKSI_REKRUTMEN')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFilteredStatistik(@Req() req: any, @Query() param: FilterDto) {
    this.logger.log(
      `Entering ${this.getFilteredStatistik.name} with param: ${JSON.stringify(param)}`,
    );
    const response = await this.statistikService.getFilteredStatistik(
      req,
      param,
    );
    this.logger.log(
      `Leaving ${this.getFilteredStatistik.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
