import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { filterDokumenDto } from '../dto/distribusi-data-dokumen.dto';

import { DownloadDataService } from '../service/download-data.service';
import { Response } from 'express';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('distribusi-data-dto')
@UseGuards(JwtAuthGuard)
export class DownloadDataController {
  private readonly logger = new Logger(DownloadDataController.name);

  constructor(private readonly downloadDataService: DownloadDataService) {}

  @Get('/download-data')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_GET_DOWNLOAD_DATA')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getDokumen(@Req() req: any, @Query() filter: filterDokumenDto) {
    this.logger.log(`Entering ${this.getDokumen.name} with filter: ${filter}`);
    const response = await this.downloadDataService.getDocument(req, filter);
    this.logger.log(
      `Leaving ${this.getDokumen.name} with filter: ${filter} and response: ${response}`,
    );
    return response;
  }

  @Get('download-data/all')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_GET_DOWNLOAD_DATA_ALL')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async downloadAll(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.downloadAll.name}`);
    await this.downloadDataService.downloadAll(req, res);
    this.logger.log(`Leaving ${this.downloadAll.name}`);
  }

  @Get('download-data/file/:id')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_GET_DOWNLOAD_DATA_FILE')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async downloadFile(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.downloadFile.name}`);
    await this.downloadDataService.downloadFile(req, res);
    this.logger.log(`Leaving ${this.downloadFile.name}`);
  }
}
