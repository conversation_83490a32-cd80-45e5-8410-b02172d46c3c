import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Post,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileManagerService } from '../service/file-manager.service';
import { lockFolderDto } from '../dto/distribusi-data-dokumen.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('distribusi-data-dto')
@UseGuards(JwtAuthGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
export class FileManagerController {
  private readonly logger = new Logger(FileManagerController.name);

  constructor(private readonly fileManagerService: FileManagerService) {}

  @Post('/file-manager')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async getFolders(@Req() req: any, @Body('path') path: string) {
    this.logger.log(`Entering ${this.getFolders.name} with path: ${path}`);
    const response = await this.fileManagerService.getFolders(req, path);
    this.logger.log(
      `Leaving ${this.getFolders.name} with path: ${path} and response: ${response}`,
    );
    return response;
  }

  @Post('/file-manager/download')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER_CONTENT_DOWNLOAD')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async downloadFile(
    @Req() req: any,
    @Res() res: Response,
    @Body('path') path: string,
  ) {
    this.logger.log(`Entering ${this.downloadFile.name} with path: ${path}`);

    const [stat, stream] = await this.fileManagerService.getFileByPath(
      req,
      path,
    );

    res.setHeader(
      'Content-Type',
      stat.metaData['content-type'] || 'application/octet-stream',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename="${path.split('/').pop()}"`,
    );
    this.logger.log(`Leaving ${this.downloadFile.name} with path: ${path}`);

    stream.pipe(res);
  }

  @Post('/file-manager/submit')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER_SUBMIT')
  // @UseGuards(SiswaJwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(200)
  async submit(
    @Req() req: any,
    @Body() param: lockFolderDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.submit.name} with param: ${JSON.stringify(param)}`,
    );
    const response = await this.fileManagerService.uploadToPath(
      req,
      param.path,
      file,
    );

    const path = param.path.split('/');
    await this.fileManagerService.lockFolder(
      req,
      path[path.length - 2],
      param.path,
      param.password,
      param.active_date,
    );
    this.logger.log(
      `Leaving ${this.submit.name} with param: ${JSON.stringify(param)} and response: ${response}`,
    );
    return response;
  }

  @Post('/file-manager/upload')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER_UPLOAD')
  // @UseGuards(SiswaJwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(200)
  async upload(
    @Req() req: any,
    @Body('path') path: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(`Entering ${this.submit.name} with path: ${path}`);

    const response = await this.fileManagerService.uploadToPath(
      req,
      path,
      file,
    );
    this.logger.log(
      `Leaving ${this.submit.name} with path: ${path} and response: ${response}`,
    );
    return response;
  }

  @Post('/file-manager/lock')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER_LOCK')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async lock(@Req() req: any, @Body() param: lockFolderDto) {
    this.logger.log(
      `Entering ${this.lock.name} with param: ${JSON.stringify(param)}`,
    );
    const path = param.path.split('/');
    const response = await this.fileManagerService.lockFolder(
      req,
      path[path.length - 2],
      param.path,
      param.password,
      param.active_date,
    );
    this.logger.log(
      `Leaving ${this.lock.name} with param: ${JSON.stringify(param)} and response: ${response}`,
    );

    return response;
  }

  @Post('/file-manager/delete')
  // @Permission('DISTRIBUSI_DATA_DOKUMEN_POST_FILE_MANAGER_DELETE')
  // @UseGuards(SiswaJwtAuthGuard)
  @HttpCode(200)
  async delete(@Req() req: any, @Body('path') path: string) {
    this.logger.log(`Entering ${this.delete.name} with path: ${path}`);
    const response = await this.fileManagerService.deleteFromPath(req, path);
    this.logger.log(
      `Leaving ${this.delete.name} with path: ${path} and response: ${response}`,
    );
    return response;
  }
}
