import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreatePelatihanKategoriDTO,
  UpdatePelatihanKategoriDTO,
} from '../dto/pelatihan-kategori.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class PelatihanKategoriService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req: any, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'nama', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.pelatihan_kategori.count({
          where: where,
        }),
        this.prisma.pelatihan_kategori.findMany({
          select: {
            id: true,
            nama: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_KATEGORI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page: +page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const pelatihanKategori = await this.prisma.pelatihan_kategori.findFirst({
        select: {
          id: true,
          nama: true,
          created_at: true,
          updated_at: true,
        },
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!pelatihanKategori) {
        throw new NotFoundException('ID pelatihan kategori tidak ditemukan');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_KATEGORI_READ as ConstantLogType,
          message,
          pelatihanKategori,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: pelatihanKategori,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreatePelatihanKategoriDTO) {
    const { nama } = body;

    try {
      const pelatihanKategori = await this.prisma.pelatihan_kategori.findFirst({
        where: {
          nama: nama,
        },
      });

      if (pelatihanKategori) {
        throw new ConflictException('Pelatihan kategori already exists');
      }

      const createPelatihanKategori =
        await this.prisma.pelatihan_kategori.create({
          data: {
            nama: nama,
            created_at: new Date(),
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_KATEGORI_CREATE as ConstantLogType,
          message,
          createPelatihanKategori,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: createPelatihanKategori,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdatePelatihanKategoriDTO) {
    const { nama } = body;

    try {
      const pelatihanKategori = await this.prisma.pelatihan_kategori.findFirst({
        where: {
          id: +id,
        },
      });

      if (!pelatihanKategori) {
        throw new NotFoundException('Pelatihan kategori ID not found');
      }
      const checkExists = await this.prisma.pelatihan_kategori.findFirst({
        where: {
          nama: nama,
          NOT: {
            id: +id,
          },
        },
      });

      if (checkExists) {
        throw new ConflictException('Pelatihan kategori already exists');
      }

      const updatePelatihanKategori =
        await this.prisma.pelatihan_kategori.update({
          where: { id: +id },
          data: {
            nama: nama,
            updated_at: new Date(),
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_KATEGORI_UPDATE as ConstantLogType,
          message,
          updatePelatihanKategori,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: updatePelatihanKategori,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    try {
      const pelatihanKategori = await this.prisma.pelatihan_kategori.findFirst({
        where: {
          id: +id,
        },
      });

      if (!pelatihanKategori) {
        throw new NotFoundException('Pelatihan kategori ID not found');
      }

      const updatePelatihanKategori =
        await this.prisma.pelatihan_kategori.update({
          where: { id: +id },
          data: {
            deleted_at: new Date(),
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_KATEGORI_DELETE as ConstantLogType,
          message,
          updatePelatihanKategori,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: updatePelatihanKategori,
      };
    } catch (error) {
      throw error;
    }
  }
}
