import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { <PERSON><PERSON>tihanKategoriController } from './controller/pelatihan-kategori.controller';
import { PelatihanKategoriService } from './service/pelatihan-kategori.service';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [PelatihanKategoriController],
  providers: [PelatihanKategoriService],
})
export class PelatihanKategoriModule {}
