import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreatePelatihanDto, UpdatePelatihanDto } from '../dto/pelatihan.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class PelatihanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async get(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'number' | 'boolean' | 'date';
      };
    } = {
      nama: { field: 'nama', type: 'string' },
      lokasi: { field: 'dikbangspes_lokasi.nama', type: 'string' },
      tingkat: { field: 'dikbangspes_tingkat.nama', type: 'string' },
      id: { field: 'id', type: 'number' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    try {
      const [totalData, pelatihan] = await this.prisma.$transaction([
        this.prisma.pelatihan.count({
          where: where,
        }),
        this.prisma.pelatihan.findMany({
          select: {
            id: true,
            nama: true,
            created_at: true,
            is_aktif: true,
            dikbangspes_lokasi: {
              select: {
                id: true,
                nama: true,
              },
            },
            dikbangspes_tingkat: {
              select: {
                id: true,
                nama: true,
              },
            },
            pelatihan_kategori: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const queryResult = pelatihan.map(
        ({
          dikbangspes_lokasi,
          dikbangspes_tingkat,
          pelatihan_kategori,
          ...item
        }) => ({
          ...item,
          lokasi_id: dikbangspes_lokasi?.id || null,
          lokasi: dikbangspes_lokasi?.nama || null,
          tingkat_id: dikbangspes_tingkat?.id || null,
          tingkat: dikbangspes_tingkat?.nama || null,
          kategori_id: pelatihan_kategori?.id || null,
          kategori: pelatihan_kategori?.nama || null,
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PELATIHAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page: +page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const queryResult = await this.prisma.pelatihan.findFirst({
        where: {
          id: +id,
          deleted_at: null,
        },
        select: {
          id: true,
          nama: true,
          is_aktif: true,
          created_at: true,
          dikbangspes_lokasi: {
            select: {
              id: true,
              nama: true,
            },
          },
          dikbangspes_tingkat: {
            select: {
              id: true,
              nama: true,
            },
          },
          pelatihan_kategori: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
      });

      if (!queryResult) {
        throw new NotFoundException('Pelatihan tidak ditemukan');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreatePelatihanDto) {
    const { nama, lokasi_id, tingkat_id, kategori_id, is_aktif } = body;

    const lokasi = await this.prisma.dikbangspes_lokasi.findFirst({
      where: {
        id: lokasi_id,
      },
    });

    if (!lokasi) {
      throw new NotFoundException('Lokasi tidak ditemukan');
    }

    const tingkat = await this.prisma.dikbangspes_tingkat.findFirst({
      where: { id: tingkat_id },
    });

    if (!tingkat) {
      throw new NotFoundException('Tingkat tidak ditemukan');
    }

    const kategori = await this.prisma.pelatihan_kategori.findFirst({
      where: { id: kategori_id },
    });

    if (!kategori) {
      throw new NotFoundException('Kategori tidak ditemukan');
    }

    const pelatihan = await this.prisma.pelatihan.findFirst({
      where: {
        nama: nama,
      },
    });

    if (pelatihan) {
      throw new ConflictException('Pelatihan already exists');
    }

    try {
      const queryResult = await this.prisma.pelatihan.create({
        data: {
          nama: nama,
          lokasi_id: lokasi.id,
          tingkat_id: tingkat.id,
          kategori_id: kategori.id,
          is_aktif: Boolean(is_aktif) ?? true,
          created_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdatePelatihanDto) {
    const { nama, lokasi_id, tingkat_id, kategori_id, is_aktif } = body;

    const checkIdPelatihan = await this.prisma.pelatihan.findFirst({
      where: {
        id: BigInt(id),
      },
    });

    if (!checkIdPelatihan) {
      throw new NotFoundException('Pelatihan tidak ditemukan');
    }

    const lokasi = await this.prisma.dikbangspes_lokasi.findFirst({
      where: {
        id: lokasi_id,
      },
    });

    if (!lokasi) {
      throw new NotFoundException('Lokasi tidak ditemukan');
    }

    const tingkat = await this.prisma.dikbangspes_tingkat.findFirst({
      where: { id: tingkat_id },
    });

    if (!tingkat) {
      throw new NotFoundException('Tingkat tidak ditemukan');
    }

    const kategori = await this.prisma.pelatihan_kategori.findFirst({
      where: { id: kategori_id },
    });

    if (!kategori) {
      throw new NotFoundException('Kategori tidak ditemukan');
    }

    const checkExists = await this.prisma.pelatihan.findFirst({
      where: {
        nama: nama,
        NOT: {
          id: +id,
        },
      },
    });

    if (checkExists) {
      throw new ConflictException('Pelatihan already exists');
    }

    try {
      const queryResult = await this.prisma.pelatihan.update({
        where: { id: +id },
        data: {
          nama: nama,
          lokasi_id: lokasi.id,
          tingkat_id: tingkat.id,
          kategori_id: kategori.id,
          is_aktif: Boolean(is_aktif) ?? true,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    const checkIdPelatihan = await this.prisma.pelatihan.findFirst({
      where: {
        id: +id,
      },
    });

    if (!checkIdPelatihan) {
      throw new NotFoundException('Pelatihan tidak ditemukan');
    }

    try {
      const queryResult = await this.prisma.pelatihan.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PELATIHAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PELATIHAN_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
