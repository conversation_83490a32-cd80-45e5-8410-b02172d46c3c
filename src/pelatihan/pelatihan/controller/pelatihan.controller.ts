import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PelatihanService } from '../service/pelatihan.service';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { CreatePelatihanDto, UpdatePelatihanDto } from '../dto/pelatihan.dto';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('pelatihan')
@UseGuards(JwtAuthGuard)
export class PelatihanController {
  private readonly logger = new Logger(PelatihanController.name);

  constructor(private readonly pelatihanService: PelatihanService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async get(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.get.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pelatihanService.get(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.get.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getById(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getById.name} with id: ${id}`);
    const response = await this.pelatihanService.getById(req, id);
    this.logger.log(
      `Leaving ${this.getById.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreatePelatihanDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.pelatihanService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: UpdatePelatihanDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.pelatihanService.update(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(HttpStatus.OK)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.pelatihanService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
