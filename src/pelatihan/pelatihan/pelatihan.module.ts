import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { PelatihanController } from './controller/pelatihan.controller';
import { PelatihanService } from './service/pelatihan.service';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PelatihanController],
  providers: [PelatihanService],
})
export class PelatihanModule {}
