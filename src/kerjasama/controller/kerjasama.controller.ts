import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { KerjasamaService } from '../service/kerjasama.service';
import { Permission } from 'src/core/decorators';
import {
  CreateKerjasamaDto,
  SearchAndSortKerjasamaDto,
  UpdateKerjasamaDto,
} from '../dto/kerjasama.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto } from 'src/core/dtos';

import { Response } from 'express';

@Controller('kerjasama')
@UseGuards(JwtAuthGuard)
export class KerjasamaController {
  private readonly logger = new Logger(KerjasamaController.name);

  constructor(private readonly kerjasamaService: KerjasamaService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'file_pks' },
      { name: 'file_mou', maxCount: 1 },
    ]),
  )
  async create(
    @Req() req: any,
    @Body() body: CreateKerjasamaDto,
    @UploadedFiles()
    files: {
      file_pks?: Express.Multer.File[];
      file_mou?: Express.Multer.File[];
    },
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );

    const response = await this.kerjasamaService.create(
      req,
      body,
      files.file_pks,
      files.file_mou?.[0],
    );

    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'file_pks' },
      { name: 'file_mou', maxCount: 1 },
    ]),
  )
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdateKerjasamaDto,
    @UploadedFiles()
    files: {
      file_pks?: Express.Multer.File[];
      file_mou?: Express.Multer.File[];
    },
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.kerjasamaService.update(
      req,
      Number(id),
      body,
      files?.file_pks,
      files?.file_mou?.[0],
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.kerjasamaService.delete(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortKerjasamaDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kerjasamaService.getMany(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getOne(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getOne.name} with id: ${id}`);
    const response = await this.kerjasamaService.getOne(req, Number(id));
    this.logger.log(
      `Leaving ${this.getOne.name} with id: ${id} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/download/excel')
  @Permission('PERMISSION_READ')
  async exportExcel(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.exportExcel.name}`);
    await this.kerjasamaService.exportExcel(req, res);
    this.logger.log(`Leaving ${this.exportExcel.name}`);
  }

  @Get('/download/pdf')
  @Permission('PERMISSION_READ')
  async exportPdf(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.exportPdf.name}`);
    await this.kerjasamaService.exportPdf(req, res);
    this.logger.log(`Leaving ${this.exportPdf.name}`);
  }
}
