import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Req,
} from '@nestjs/common';
import { Permission } from 'src/core/decorators';
import { DurasiKerjasamaService } from '../service/kerjasama-durasi.service';
import {
  CreateMasterDurasiKerjasamaDto,
  UpdateMasterDurasiKerjasamaDto,
} from '../dto/kerjasama-durasi.dto';

@Controller('kerjasama-durasi')
export class DurasiKerjasamaController {
  private readonly logger = new Logger(DurasiKerjasamaController.name);

  constructor(
    private readonly durasiKerjasamaService: DurasiKerjasamaService,
  ) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: any,
    @Body() createDurasiDto: CreateMasterDurasiKerjasamaDto,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createDurasiDto)}`,
    );
    const response = await this.durasiKerjasamaService.create(
      req,
      createDurasiDto,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createDurasiDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() updateDurasidto: UpdateMasterDurasiKerjasamaDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with body: ${JSON.stringify(updateDurasidto)}`,
    );

    const response = await this.durasiKerjasamaService.update(
      req,
      id,
      updateDurasidto,
    );
    this.logger.log(
      `Leaving ${this.update.name} with body: ${JSON.stringify(updateDurasidto)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete()
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.durasiKerjasamaService.delete(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getOne(@Req() req: any) {
    this.logger.log(`Entering ${this.getOne.name}`);

    const response = await this.durasiKerjasamaService.getMany(req);

    this.logger.log(
      `Leaving ${this.getOne.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getMany(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getMany.name} with id: ${id}`);
    const response = await this.durasiKerjasamaService.getOne(req, Number(id));
    this.logger.log(
      `Leaving ${this.getMany.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
