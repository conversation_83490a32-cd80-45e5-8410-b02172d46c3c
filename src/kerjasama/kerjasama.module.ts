import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { DurasiKerjasamaController } from './controller/kerjasama-durasi.controller';
import { DurasiKerjasamaService } from './service/kerjasama-durasi.service';
import { KerjasamaController } from './controller/kerjasama.controller';
import { KerjasamaService } from './service/kerjasama.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KerjasamaController, DurasiKerjasamaController],
  providers: [KerjasamaService, MinioService, DurasiKerjasamaService],
})
export class KerjasamaModule {}
