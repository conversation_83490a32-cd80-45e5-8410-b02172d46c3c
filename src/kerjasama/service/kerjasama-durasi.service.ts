import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateMasterDurasiKerjasamaDto,
  UpdateMasterDurasiKerjasamaDto,
} from '../dto/kerjasama-durasi.dto';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class DurasiKerjasamaService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, createDurasiDto: CreateMasterDurasiKerjasamaDto) {
    try {
      const queryResult = await this.prisma.kerjasama_durasi_bulan.create({
        data: {
          ...createDurasiDto,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updateDurasiKerjasamaDto: UpdateMasterDurasiKerjasamaDto,
  ) {
    try {
      const durasiKerjasama =
        await this.prisma.kerjasama_durasi_bulan.findFirst({
          where: { id },
        });

      if (!durasiKerjasama)
        throw new NotFoundException(`master durasi kerjasama not found`);

      durasiKerjasama.durasi = updateDurasiKerjasamaDto.durasi;
      durasiKerjasama.display = updateDurasiKerjasamaDto.display;

      const queryResult = await this.prisma.kerjasama_durasi_bulan.update({
        where: { id },
        data: {
          ...durasiKerjasama,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const queryResult = await this.prisma.kerjasama_durasi_bulan.findFirst({
        where: { id },
      });

      if (!queryResult)
        throw new NotFoundException(`master durasi kerjasama not found`);

      await this.prisma.kerjasama_durasi_bulan.update({
        where: { id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.kerjasama_durasi_bulan.findFirst({
        where: { id },
        select: {
          id: true,
          durasi: true,
          display: true,
        },
      });

      if (!queryResult)
        throw new NotFoundException(`master durasi kerjasama not found`);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get durasi kerjasama',
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getMany(req: any) {
    try {
      const queryResult = await this.prisma.kerjasama_durasi_bulan.findMany({
        select: { id: true, durasi: true, display: true },
        where: { deleted_at: null },
        orderBy: { durasi: 'asc' },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }
}
