import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { TDocumentDefinitions } from 'pdfmake/interfaces';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateKerjasamaDto,
  SearchAndSortKerjasamaDto,
  Tanggal_PKS,
  UpdateKerjasamaDto,
} from '../dto/kerjasama.dto';
import { PaginationDto } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { Workbook } from 'exceljs';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import * as PdfPrinter from 'pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts.js';
import { format } from 'date-fns';
import { getDayDifference } from '../../core/utils/common.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { Writable } from 'stream';

@Injectable()
export class KerjasamaService {
  constructor(
    private prisma: PrismaService,
    private minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(
    req: any,
    createKerjasamaDto: CreateKerjasamaDto,
    filePks?: Express.Multer.File[],
    filesMou?: Express.Multer.File,
  ) {
    try {
      const [uploadedFileMou] = await this.minioService.uploadFiles([filesMou]);
      const uploadedFilePks = await this.minioService.uploadFiles(filePks);

      const preInsertMou = {
        create: {
          ...uploadedFileMou.rawFile,
          key: uploadedFileMou.uploaded.Key,
          url: uploadedFileMou.uploaded.Location,
          filename: uploadedFileMou.uploaded.filename,
          created_at: new Date(),
          updated_at: new Date(),
        },
      };

      const preInsertPks = this.bindPksDates(
        createKerjasamaDto.tanggal_pks,
        uploadedFilePks,
      );

      const tanggalMulai = new Date(createKerjasamaDto.tanggal_mulai);
      const tanggalSelesai = new Date(createKerjasamaDto.tanggal_selesai);
      const queryResult = await this.prisma.kerjasama.create({
        data: {
          institusi: createKerjasamaDto.institusi,
          tanggal_mulai: tanggalMulai,
          tanggal_selesai: tanggalSelesai,
          durasi_hari: getDayDifference(tanggalMulai, tanggalSelesai),
          status_id: 1,
          kerjasama_file_pks: preInsertPks,
          kerjasama_file_mou: preInsertMou,
          jenis_institusi_id: 1,
          kategori_file: 'PKS',
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updateKerjasamaDto: UpdateKerjasamaDto,
    filesPks?: Express.Multer.File[],
    fileMou?: Express.Multer.File,
  ) {
    try {
      const kerjasama = await this.prisma.kerjasama.findFirst({
        where: { id },
      });
      if (!kerjasama) {
        throw new NotFoundException(`kerjasama ${id} not found`);
      }

      const { institusi, tanggal_mulai, tanggal_selesai, tanggal_pks } =
        updateKerjasamaDto;
      if (institusi) kerjasama.institusi = institusi;
      if (tanggal_mulai) kerjasama.tanggal_mulai = new Date(tanggal_mulai);
      if (tanggal_selesai)
        kerjasama.tanggal_selesai = new Date(tanggal_selesai);

      kerjasama.durasi_hari = BigInt(
        getDayDifference(kerjasama.tanggal_mulai, kerjasama.tanggal_selesai),
      );

      await this.prisma.kerjasama.update({
        where: { id },
        data: {
          ...kerjasama,
          updated_at: new Date(),
        },
      });

      // update file mou, if any
      if (fileMou) {
        const [uploadedFileMou] = await this.minioService.uploadFiles([
          fileMou,
        ]);
        const activeMouFile = await this.prisma.kerjasama_file_mou.findFirst({
          where: { id_kerjasama: id, deleted_at: null },
          select: { id: true },
        });
        await Promise.all([
          // unbind old mou file from kerjasama
          this.prisma.kerjasama_file_mou.update({
            where: { id: Number(activeMouFile.id) },
            data: { deleted_at: new Date() },
          }),
          // bind new mou file to kerjasama
          this.prisma.kerjasama_file_mou.create({
            data: {
              ...uploadedFileMou.rawFile,
              id_kerjasama: id,
              key: uploadedFileMou.uploaded.Key,
              url: uploadedFileMou.uploaded.Location,
              filename: uploadedFileMou.uploaded.filename,
              created_at: new Date(),
              updated_at: new Date(),
            },
          }),
        ]);
      }

      // remove to delete pks files
      if (updateKerjasamaDto.deleted_pks_id) {
        const toDeletePksFiles = updateKerjasamaDto.deleted_pks_id
          .split(',')
          .map(Number);

        await this.prisma.kerjasama_file_mou.updateMany({
          where: { id: { in: toDeletePksFiles } },
          data: {
            updated_at: new Date(),
            deleted_at: new Date(),
          },
        });
      }

      // update file pks, if any
      if (tanggal_pks.length) {
        const uploadedFilePks = filesPks?.length
          ? await this.minioService.uploadFiles(filesPks)
          : [];

        for (const idx in tanggal_pks) {
          const {
            id: id_pks,
            tanggal_mulai,
            tanggal_selesai,
          } = tanggal_pks[idx];

          const workingFile = uploadedFilePks[idx];
          // if id is not present, file is handled as new
          if (!id_pks) {
            await this.prisma.kerjasama_file_pks.create({
              data: {
                ...workingFile.rawFile,
                id_kerjasama: id,
                key: workingFile.uploaded.Key,
                url: workingFile.uploaded.Location,
                filename: workingFile.uploaded.filename,
                tanggal_mulai: new Date(tanggal_mulai),
                tanggal_selesai: new Date(tanggal_selesai),
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
            continue;
          }

          // if there are no new pks file available, only update pks dates
          await this.prisma.kerjasama_file_pks.update({
            where: { id: Number(id_pks) },
            data: {
              tanggal_mulai: new Date(tanggal_mulai),
              tanggal_selesai: new Date(tanggal_selesai),
              updated_at: new Date(),
            },
          });
        }
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_UPDATE as ConstantLogType,
          message,
          kerjasama,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: {
          isUpdate: true,
        },
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const queryResult = await this.prisma.kerjasama.findFirst({
        where: { id },
      });
      if (!queryResult) {
        throw new NotFoundException(`kerjasama ID ${id} tidak ditemukan`);
      }

      await Promise.all([
        // delete kerjasama
        this.prisma.kerjasama.update({
          where: { id },
          data: {
            deleted_at: new Date(),
          },
        }),
        // delete file pks
        this.prisma.kerjasama_file_pks.updateMany({
          where: { id_kerjasama: id },
          data: { deleted_at: new Date() },
        }),
        // delete file mou
        this.prisma.kerjasama_file_pks.updateMany({
          where: { id_kerjasama: id },
          data: { deleted_at: new Date() },
        }),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOne(req: any, id: number) {
    try {
      const _kerjasama = await this.prisma.kerjasama.findFirst({
        select: {
          id: true,
          institusi: true,
          tanggal_mulai: true,
          tanggal_selesai: true,
          durasi_hari: true,
          kerjasama_status: {
            select: {
              nama: true,
            },
          },
          kerjasama_file_pks: {
            select: {
              id: true,
              key: true,
              url: true,
              filename: true,
              tanggal_mulai: true,
              tanggal_selesai: true,
            },
            where: { deleted_at: null },
          },
          kerjasama_file_mou: {
            select: {
              id: true,
              key: true,
              url: true,
              filename: true,
            },
            where: { deleted_at: null },
          },
          created_at: true,
        },
        where: { id, deleted_at: null },
      });

      if (!_kerjasama) {
        throw new NotFoundException(`kerjasama ID ${id} tidak ditemukan`);
      }
      const queryResult = await this._calculateSisaWaktu(
        this._isNewLabel(_kerjasama),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getMany(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortKerjasamaDto,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'number';
        };
      } = {
        institusi: { field: 'institusi', type: 'string' },
        tanggal_mulai: { field: 'tanggal_mulai', type: 'date' },
        tanggal_selesai: { field: 'tanggal_selesai', type: 'date' },
        status: { field: 'status_id', type: 'string' },
        durasi_hari: { field: 'durasi_hari', type: 'number' },
      };

      // Current date
      const currentDate = new Date();

      // Calculate dates for 3 years ago and 5 years ago
      const threeYearsAgo = new Date();
      threeYearsAgo.setFullYear(currentDate.getFullYear() - 3);

      const fiveYearsAgo = new Date();
      fiveYearsAgo.setFullYear(currentDate.getFullYear() - 5);

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      if (searchandsortData.filterLastYears) {
        const filterYears = searchandsortData.filterLastYears;

        if (filterYears === 3) {
          where.tanggal_mulai = {
            gte: threeYearsAgo,
          };
        } else if (filterYears === 5) {
          where.tanggal_mulai = {
            gte: fiveYearsAgo,
          };
        }
      }

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      // update status kerjasama in background
      this.updateStatusKerjasama();

      const [totalData, _kerjasama] = await this.prisma.$transaction([
        this.prisma.kerjasama.count({
          where,
        }),
        this.prisma.kerjasama.findMany({
          select: {
            id: true,
            institusi: true,
            tanggal_mulai: true,
            tanggal_selesai: true,
            durasi_hari: true,
            kerjasama_status: {
              select: {
                nama: true,
              },
            },
            kerjasama_file_pks: {
              select: {
                filename: true,
                url: true,
                tanggal_mulai: true,
                tanggal_selesai: true,
              },
              where: { deleted_at: null },
            },
            kerjasama_file_mou: {
              select: {
                filename: true,
                url: true,
              },
              where: { deleted_at: null },
            },
            created_at: true,
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);
      const queryResult = _kerjasama
        .map(this._isNewLabel)
        .map(this._calculateSisaWaktu)
        .map(this._countPksFile);
      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async exportPdf(req: any, res: any) {
    const _kerjasama = await this.prisma.kerjasama.findMany({
      select: {
        id: true,
        institusi: true,
        tanggal_mulai: true,
        tanggal_selesai: true,
        durasi_hari: true,
        kerjasama_status: {
          select: {
            nama: true,
          },
        },
        kerjasama_file_pks: {
          select: {
            filename: true,
            url: true,
            tanggal_mulai: true,
            tanggal_selesai: true,
          },
          where: { deleted_at: null },
        },
        kerjasama_file_mou: {
          select: {
            filename: true,
            url: true,
          },
          where: { deleted_at: null },
        },
        created_at: true,
      },
      where: {
        deleted_at: null,
      },
      orderBy: {
        created_at: 'desc',
      },
    });
    const queryResult = _kerjasama
      .map(this._isNewLabel)
      .map(this._calculateSisaWaktu)
      .map(this._countPksFile);

    const fonts = {
      Roboto: {
        normal: Buffer.from(pdfFonts['Roboto-Regular.ttf'], 'base64'),
        bold: Buffer.from(pdfFonts['Roboto-Medium.ttf'], 'base64'),
        italics: Buffer.from(pdfFonts['Roboto-Italic.ttf'], 'base64'),
        bolditalics: Buffer.from(pdfFonts['Roboto-Italic.ttf'], 'base64'),
      },
    };
    const printer = new PdfPrinter(fonts);

    const docDefinition: TDocumentDefinitions = {
      defaultStyle: {
        fontSize: 8,
      },
      content: [
        { text: 'Data Kerjasama', style: 'header' },
        {
          table: {
            body: [
              [
                { text: 'No', fontSize: 10, bold: true },
                { text: 'Nama', fontSize: 10, bold: true },
                { text: 'Jangka Waktu PKS', fontSize: 10, bold: true },
                { text: 'Tanggal Mulai', fontSize: 10, bold: true },
                { text: 'Tanggal Berakhir', fontSize: 10, bold: true },
                { text: 'Sisa Waktu', fontSize: 10, bold: true },
                { text: 'Status', fontSize: 10, bold: true },
                { text: 'MoU', fontSize: 10, bold: true },
                { text: 'PKS', fontSize: 10, bold: true },
              ], // Header tabel
              ...queryResult.map((data, index) => {
                const jangka_waktu_pks = this.formatDayDiffInString(
                  Number(data.durasi_hari),
                );
                const sisa_waktu = this.formatDayDiffInString(
                  data.sisa_waktu_hari,
                );
                return [
                  index + 1,
                  data.institusi,
                  jangka_waktu_pks,
                  format(data.tanggal_mulai, 'd MMMM yyyy'),
                  format(data.tanggal_selesai, 'd MMMM yyyy'),
                  sisa_waktu,
                  data.kerjasama_status.nama,
                  data?.kerjasama_file_mou[0]?.filename || '-',
                  data.kerjasama_file_pks_count,
                ];
              }),
            ],
          },
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10],
        },
      },
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);

    const chunks = [];
    const bufferStream = new Writable({
      write(chunk, encoding, callback) {
        chunks.push(chunk);
        callback();
      },
    });
    pdfDoc.pipe(bufferStream);
    pdfDoc.end();
    await new Promise((resolve) => bufferStream.on('finish', resolve));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KERJASAMA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KERJASAMA_WRITE_PDF as ConstantLogType,
        message,
        queryResult,
      ),
    );

    const buffer = Buffer.concat(chunks);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=Data Kerjasama.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    res.end(buffer);

    return new Promise((resolve, reject) => {
      pdfDoc.on('end', () => {
        const result = Buffer.concat(chunks);
        resolve(result);
      });

      pdfDoc.on('error', (error) => {
        reject(error);
      });
    });
  }

  async exportExcel(req: any, res: any) {
    try {
      const _kerjasama = await this.prisma.kerjasama.findMany({
        select: {
          id: true,
          institusi: true,
          tanggal_mulai: true,
          tanggal_selesai: true,
          durasi_hari: true,
          kerjasama_status: {
            select: {
              nama: true,
            },
          },
          kerjasama_file_pks: {
            select: {
              filename: true,
              url: true,
              tanggal_mulai: true,
              tanggal_selesai: true,
            },
            where: { deleted_at: null },
          },
          kerjasama_file_mou: {
            select: {
              filename: true,
              url: true,
            },
            where: { deleted_at: null },
          },
          created_at: true,
        },
        where: {
          deleted_at: null,
        },
        orderBy: {
          created_at: 'desc',
        },
      });
      const kerjasama = _kerjasama
        .map(this._isNewLabel)
        .map(this._calculateSisaWaktu)
        .map(this._countPksFile);

      const workbook = new Workbook();
      const worksheet = workbook.addWorksheet('Kerjasama');
      const header = [
        'No',
        'Nama',
        'Jangka Waktu PKS',
        'Tanggal Mulai',
        'Tanggal Berakhir',
        'Sisa Waktu',
        'Status',
        'MoU',
        'PKS',
      ];
      const headerRow = worksheet.addRow(header);
      headerRow.eachCell((cell, colNumber) => {
        cell.font = {
          bold: true,
          size: 13,
        };
      });

      kerjasama.forEach((data, index) => {
        const jangka_waktu_pks = this.formatDayDiffInString(
          Number(data.durasi_hari),
        );
        const sisa_waktu = this.formatDayDiffInString(data.sisa_waktu_hari);
        worksheet.addRow([
          index + 1,
          data.institusi,
          jangka_waktu_pks,
          data.tanggal_mulai,
          data.tanggal_selesai,
          sisa_waktu,
          data.kerjasama_status.nama,
          data?.kerjasama_file_mou[0]?.filename || '-',
          data.kerjasama_file_pks_count,
        ]);
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KERJASAMA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KERJASAMA_READ as ConstantLogType,
          message,
          kerjasama,
        ),
      );

      const buffer = await workbook.xlsx.writeBuffer();
      const filename = 'Data Kerjasama';

      // Atur header respons untuk file Excel
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        `Content-Disposition`,
        `attachment; filename=${filename}.xlsx`,
      );
      res.setHeader('Content-Length', buffer.byteLength);

      // Kirim buffer sebagai respons
      res.end(buffer);
    } catch (error) {
      throw error;
    }
  }

  private formatDayDiffInString(days: number) {
    const year = Math.floor(days / 365);
    const month = Math.floor((days - year * 365) / 30);
    const day = days - (year * 365 + month * 30);

    const res = [];
    if (year > 0) res.push(`${year} Tahun`);
    if (month > 0) res.push(`${month} Bulan`);
    if (day >= 0) res.push(`${day} Hari`);

    return res.join(' ');
  }

  private async updateStatusKerjasama() {
    try {
      const today = new Date();
      await this.prisma.kerjasama.updateMany({
        where: {
          status_id: 1,
          tanggal_selesai: { lte: today },
        },
        data: {
          updated_at: today,
          status_id: 2,
        },
      });
    } catch (err) {
      throw err;
    }
  }

  private _isNewLabel(kerjasama) {
    const now = Date.now();
    const createdAt = new Date(kerjasama.created_at).getTime();
    const oneDay = 60 * 60 * 24 * 1000;

    const isLessThanADay = now - createdAt < oneDay;
    kerjasama.is_new = isLessThanADay;

    return kerjasama;
  }

  private _calculateSisaWaktu(kerjasama) {
    const now = new Date(new Date().setHours(0, 0, 0, 0)).getTime();
    const endTime = new Date(kerjasama.tanggal_selesai).getTime();
    const diffTime = Math.max(0, endTime - now);
    const daysDifference = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    kerjasama.sisa_waktu_hari = daysDifference;

    return kerjasama;
  }

  private _countPksFile(kerjasama) {
    kerjasama.kerjasama_file_pks_count = kerjasama.kerjasama_file_pks.length;
    delete kerjasama.kerjasama_file_pks;

    return kerjasama;
  }

  private bindPksDates(
    pksDates: Tanggal_PKS[],
    uploadedFilePks: (
      | {
          rawFile: Express.Multer.File;
          uploaded?: undefined;
        }
      | {
          rawFile: Express.Multer.File;
          uploaded: {
            Key: string;
            ETag: string;
            Location: string;
            filename: string;
          };
        }
    )[],
  ) {
    const payloads = pksDates.map((dt, idx) => {
      const file = uploadedFilePks[idx];
      return {
        ...file.rawFile,
        key: file.uploaded.Key,
        url: file.uploaded.Location,
        filename: file.uploaded.filename,
        tanggal_mulai: new Date(dt.tanggal_mulai),
        tanggal_selesai: new Date(dt.tanggal_selesai),
        created_at: new Date(),
        updated_at: new Date(),
      };
    });

    return { create: payloads };
  }
}
