import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { SearchAndSortDTO } from 'src/core/dtos';

export class CreateKerjasamaDto {
  @ApiProperty({
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  institusi: string;

  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  jenis_institusi_id: number;

  @ApiProperty({
    type: Date,
    required: true,
  })
  @IsNotEmpty()
  @Type(() => Date)
  tanggal_mulai: Date;

  @ApiProperty({
    type: Date,
    required: true,
  })
  @IsNotEmpty()
  @Type(() => Date)
  tanggal_selesai: Date;

  @IsNotEmpty()
  @IsArray()
  tanggal_pks: Tanggal_PKS[];
}

export class UpdateKerjasamaDto extends PartialType(CreateKerjasamaDto) {
  @IsOptional()
  @IsString()
  deleted_pks_id: string;
}

export class KerjasamaFileDto {
  originalname: string;
  encoding: string;
  mimetype: string;
  destination: string;
  filename: string;
  path: string;
  size: number;
}

export interface Tanggal_PKS {
  tanggal_mulai: string;
  tanggal_selesai: string;
  id?: number;
}

export class SearchAndSortKerjasamaDto extends SearchAndSortDTO {
  @IsNumber()
  @IsOptional()
  filterLastYears?: number;
}
