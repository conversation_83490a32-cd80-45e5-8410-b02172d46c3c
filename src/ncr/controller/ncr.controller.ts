import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import {
  ApprovalDocumentNcr,
  ApprovalNcr,
  CreatePermohonanCeraiRujuk,
  CreatePermohonan<PERSON>,
  DetailNcr,
  GenerateSuratIzin,
  UploadAkta,
} from '../dto/ncr.dto';
import { NcrService } from '../service/ncr.service';
import { FieldValidatorPipe } from '../../core/validator/field.validator';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { jenis_pengajuan_enum } from '@prisma/client';
import { Module, Permission } from '../../core/decorators';
import { MODULES } from '../../core/constants/module.constant';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';

@Controller('ncr')
@Module(MODULES.MARRIAGE_DIVORCE_RECONCILIATION)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class NcrController {
  private readonly logger = new Logger(NcrController.name);

  constructor(private readonly ncrService: NcrService) {}

  //PERSONEL
  @Get('/status')
  @HttpCode(200)
  async getStatus(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatus.name}`);
    const response = await this.ncrService.getStatus(req);
    this.logger.log(
      `Leaving ${this.getStatus.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/nikah')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'surat_pengantar_kesatker', maxCount: 1 },
      { name: 'surat_personalia', maxCount: 1 },
      { name: 'surat_n1_pemohon', maxCount: 1 },
      { name: 'surat_n1_pasangan', maxCount: 1 },
      { name: 'surat_n2_pemohon', maxCount: 1 },
      { name: 'surat_n2_pasangan', maxCount: 1 },
      { name: 'surat_n4_pemohon', maxCount: 1 },
      { name: 'surat_n4_pasangan', maxCount: 1 },
      { name: 'surat_kesanggupan_pemohon', maxCount: 1 },
      { name: 'surat_kesanggupan_pasangan', maxCount: 1 },
      { name: 'surat_persetujuan_orang_tua_pemohon', maxCount: 1 },
      { name: 'surat_persetujuan_orang_tua_pasangan', maxCount: 1 },
      { name: 'surat_cerai_mati_pemohon', maxCount: 1 },
      { name: 'surat_cerai_mati_pasangan', maxCount: 1 },
      { name: 'surat_sehat_pemohon', maxCount: 1 },
      { name: 'surat_sehat_pasangan', maxCount: 1 },
      { name: 'foto_4_x_6_pemohon', maxCount: 1 },
      { name: 'foto_4_x_6_pasangan', maxCount: 1 },
      { name: 'surat_pernyataan_bersama', maxCount: 1 },
      { name: 'skck', maxCount: 1 },
      { name: 'dokumen_khusus', maxCount: 1 },
      { name: 'surat_izin_cerai', maxCount: 1 },
      { name: 'akta_nikah', maxCount: 1 },
      { name: 'kartu_tanda_anggota', maxCount: 1 },
      { name: 'surat_izin_rujuk', maxCount: 1 },
      { name: 'akta_cerai', maxCount: 1 },
      { name: 'surat_persetujuan_rujuk', maxCount: 1 },
    ]),
  )
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreatePermohonanNikah,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.ncrService.create(req, body, files);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/cerai')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'surat_pengantar_kesatker', maxCount: 1 },
      { name: 'surat_izin_cerai', maxCount: 1 },
      { name: 'akta_nikah', maxCount: 1 },
      { name: 'kartu_tanda_anggota', maxCount: 1 },
    ]),
  )
  async createCerai(
    @Req() req: any,
    @Body() body: CreatePermohonanCeraiRujuk,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.createCerai.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.ncrService.createCeraiRujuk(
      req,
      body,
      jenis_pengajuan_enum.CERAI,
      files,
    );
    this.logger.log(
      `Leaving ${this.createCerai.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/rujuk')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'surat_pengantar_kesatker', maxCount: 1 },
      { name: 'surat_izin_rujuk', maxCount: 1 },
      { name: 'akta_cerai', maxCount: 1 },
      { name: 'surat_persetujuan_rujuk', maxCount: 1 },
    ]),
  )
  async createRujuk(
    @Req() req: any,
    @Body() body: CreatePermohonanCeraiRujuk,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.createRujuk.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.ncrService.createCeraiRujuk(
      req,
      body,
      jenis_pengajuan_enum.RUJUK,
      files,
    );
    this.logger.log(
      `Leaving ${this.createRujuk.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('upload-akta')
  @HttpCode(201)
  @UseInterceptors(FileInterceptor('surat_akta_ncr'))
  async uploadAkta(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: UploadAkta,
    @UploadedFile(new FieldValidatorPipe()) file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.uploadAkta.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.ncrService.uploadAkta(req, body, file);
    this.logger.log(
      `Leaving ${this.uploadAkta.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  //OPERATOR
  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ncrService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    return response;
  }

  @Post('/generate-surat-izin')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async generateSuratIzin(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: GenerateSuratIzin,
  ) {
    this.logger.log(
      `Entering ${this.generateSuratIzin.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.ncrService.generateSuratIzin(req, body);
    this.logger.log(
      `Leaving ${this.generateSuratIzin.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/approval')
  @Permission('PERMISSION_APPROVAL')
  @HttpCode(200)
  async approvalNcr(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) approval: ApprovalNcr,
  ) {
    this.logger.log(
      `Entering ${this.approvalNcr.name} with body: ${JSON.stringify(approval)}`,
    );
    const response = await this.ncrService.approvalNcr(req, approval);
    this.logger.log(
      `Leaving ${this.approvalNcr.name} with body: ${JSON.stringify(approval)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/approval-document')
  @Permission('PERMISSION_APPROVAL')
  @HttpCode(200)
  async approvalDocumentNCR(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) approval: ApprovalDocumentNcr,
  ) {
    this.logger.log(
      `Entering ${this.approvalDocumentNCR.name} with body: ${JSON.stringify(approval)}`,
    );
    const response = await this.ncrService.approvalDocumentNCR(req, approval);
    this.logger.log(
      `Leaving ${this.approvalDocumentNCR.name} with body: ${JSON.stringify(approval)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/edit-document/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  async editDocumentNCR(
    @Req() req: any,
    @Param('id') id: string,
    @UploadedFiles()
    files: {
      [key: string]: Express.Multer.File[];
    },
  ) {
    this.logger.log(`Entering ${this.editDocumentNCR.name} with id: ${id}`);
    const response = await this.ncrService.editDocumentNCR(req, id, files);
    this.logger.log(
      `Leaving ${this.editDocumentNCR.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  //GENERAL
  @Get('/detail')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDetail(@Req() req: any, @Query() query: DetailNcr) {
    this.logger.log(
      `Entering ${this.getDetail.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.ncrService.getDetail(req, query);
    this.logger.log(
      `Leaving ${this.getDetail.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
