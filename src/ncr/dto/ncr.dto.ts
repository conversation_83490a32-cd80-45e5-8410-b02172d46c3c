import { jenis_pengajuan_enum, status_dokumen_ncr_enum } from '@prisma/client';
import { Exclude, Transform } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Length,
} from 'class-validator';

export class DetailNcr {
  @IsOptional()
  @IsString()
  jenis_pengajuan: jenis_pengajuan_enum;

  @Transform(({ value }) => (!value ? null : Number(value)))
  @IsNumber()
  @IsOptional()
  pengajuan_id: number;
}

export class CreatePermohonanCeraiRujuk {
  @IsNotEmpty({ message: 'Satuan ID tidak boleh kosong' })
  @IsString({ message: 'Satuan ID harus berupa string' })
  satuan_id: number;
}

export class ApprovalNcr {
  @IsNotEmpty({ message: 'pengajuan ID tidak boleh kosong' })
  @IsNumber({}, { message: 'pengajuan ID harus berupa number' })
  pengajuan_id: number;

  @IsNotEmpty({ message: 'Tanggal pernikahan tidak boleh kosong' })
  @IsDateString()
  tanggal_sidang: string;

  @IsBoolean()
  @IsNotEmpty()
  approval: boolean;
}

export class ApprovalDocumentNcr {
  @IsNotEmpty({ message: 'Dokumen ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Dokumen ID harus berupa number' })
  id: number;

  @IsNotEmpty({ message: 'Tanggal pernikahan tidak boleh kosong' })
  @IsEnum(status_dokumen_ncr_enum)
  status: status_dokumen_ncr_enum;

  @IsOptional()
  @IsString({ message: 'Alasan penolakan harus berupa string' })
  alasan_penolakan: string;
}

export class CreatePermohonanNikah {
  @Exclude()
  surat_cerai_mati_pemohon: string;
  @Exclude()
  surat_cerai_mati_pasangan: string;
  @Exclude()
  dokumen_khusus: string;

  @IsNotEmpty({ message: 'Satuan ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Satuan ID harus berupa string format number' })
  satuan_id: number;

  @IsNotEmpty({ message: 'Alamat Personel ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Alamat Personel ID harus berupa string format number' },
  )
  alamat_personel_id: number;

  @IsNotEmpty({ message: 'Tempat pernikahan tidak boleh kosong' })
  @IsString({ message: 'Tempat pernikahan harus berupa string' })
  tempat_pernikah: string;

  @IsNotEmpty({ message: 'Tanggal pernikahan tidak boleh kosong' })
  @IsDateString()
  tanggal_pernikah: string;

  @IsNotEmpty({ message: 'Nama pasangan tidak boleh kosong' })
  @IsString({ message: 'Nama pasangan harus berupa string' })
  nama_pasangan: string;

  @IsNotEmpty({ message: 'NIK pasangan tidak boleh kosong' })
  @IsString({ message: 'NIK pasangan harus berupa string' })
  @Length(16, 16, { message: 'NIK pasangan harus terdiri dari 16 karakter' })
  nik_pasangan: string;

  @IsNotEmpty({ message: 'Tempat lahir pasangan tidak boleh kosong' })
  @IsString({ message: 'Tempat lahir pasangan harus berupa string' })
  tempat_lahir_pasangan: string;

  @IsNotEmpty({ message: 'Tanggal lahir pasangan tidak boleh kosong' })
  @IsDateString()
  tanggal_lahir_pasangan: string;

  @IsNotEmpty({ message: 'Pekerjaan pasangan ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Pekerjaan pasangan ID harus berupa string format number' },
  )
  pekerjaan_pasangan_id: number;

  @IsNotEmpty({ message: 'Agama pasangan ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Agama pasangan ID harus berupa string format number' },
  )
  agama_pasangan_id: number;

  @IsNotEmpty({ message: 'Status personel kawin ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Status personel kawin ID harus berupa string format number' },
  )
  status_personel_kawin_id: number;

  @IsNotEmpty({ message: 'Status pasangan kawin ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Status pasangan kawin ID harus berupa string format number' },
  )
  status_pasangan_kawin_id: number;

  @IsNotEmpty({ message: 'Email pasangan tidak boleh kosong' })
  @IsEmail({}, { message: 'Email pasangan harus berupa email yang valid' })
  email_pasangan: string;

  @IsNotEmpty({ message: 'Nomor HP pasangan tidak boleh kosong' })
  @IsPhoneNumber('ID', {
    message: 'Nomor HP pasangan harus berupa nomor telepon yang valid',
  })
  no_hp_pasangan: string;

  @IsNotEmpty({ message: 'Alamat pasangan tidak boleh kosong' })
  @IsString({ message: 'Alamat pasangan harus berupa string' })
  alamat_pasangan: string;

  @IsNotEmpty({ message: 'Nama lengkap bapak tidak boleh kosong' })
  @IsString({ message: 'Nama lengkap bapak harus berupa string' })
  nama_lengkap_bapak: string;

  @IsNotEmpty({ message: 'Pekerjaan bapak ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Pekerjaan bapak ID harus berupa string format number' },
  )
  pekerjaan_bapak_id: number;

  @IsNotEmpty({ message: 'Agama bapak ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Agama bapak ID harus berupa string format number' })
  agama_bapak_id: number;

  @IsNotEmpty({ message: 'Alamat bapak tidak boleh kosong' })
  @IsString({ message: 'Alamat bapak harus berupa string' })
  alamat_bapak: string;

  @IsNotEmpty({ message: 'Nama lengkap ibu tidak boleh kosong' })
  @IsString({ message: 'Nama lengkap ibu harus berupa string' })
  nama_lengkap_ibu: string;

  @IsNotEmpty({ message: 'Pekerjaan ibu ID tidak boleh kosong' })
  @IsNumber(
    {},
    { message: 'Pekerjaan ibu ID harus berupa string format number' },
  )
  pekerjaan_ibu_id: number;

  @IsNotEmpty({ message: 'Agama ibu ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Agama ibu ID harus berupa string format number' })
  agama_ibu_id: number;

  @IsNotEmpty({ message: 'Alamat ibu tidak boleh kosong' })
  @IsString({ message: 'Alamat ibu harus berupa string' })
  alamat_ibu: string;
}

export class UploadAkta {
  @Transform((value) => Number(value.value))
  @IsNotEmpty({ message: 'Pengajuan NCR ID tidak boleh kosong' })
  @IsNumber()
  pengajuan_ncr_id: number;

  @IsNotEmpty({ message: 'Jenis pengajuan tidak boleh kosong' })
  @IsEnum(jenis_pengajuan_enum, { message: 'Jenis pengajuan tidak valid' })
  @Transform(
    ({ value }) =>
      jenis_pengajuan_enum[value as keyof typeof jenis_pengajuan_enum],
  )
  jenis_pengajuan: jenis_pengajuan_enum;
}

export class GenerateSuratIzin {
  @IsNotEmpty({ message: 'Pengajuan NCR ID tidak boleh kosong' })
  @IsString({ message: 'Pengajuan NCR ID harus berupa string' })
  pengajuan_ncr_id: number;

  @IsNotEmpty({ message: 'Jenis pengajuan tidak boleh kosong' })
  @IsEnum(jenis_pengajuan_enum, { message: 'Jenis pengajuan tidak valid' })
  jenis_pengajuan: jenis_pengajuan_enum;

  @IsNotEmpty({ message: 'Jenis pengajuan tidak boleh kosong' })
  @IsString({ message: 'Nomor harus berupa string' })
  nomor: string;

  @IsNotEmpty({ message: 'Jenis pengajuan tidak boleh kosong' })
  @IsString({ message: 'Content harus berupa string' })
  content: string;
}
