import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { jenis_pengajuan_enum } from '@prisma/client';

export const RequiredFiles = {
  NIKAH: [
    'surat_pengantar_kesatker',
    'surat_personalia',
    'surat_n1_pemohon',
    'surat_n1_pasangan',
    'surat_n2_pemohon',
    'surat_n2_pasangan',
    'surat_n4_pemohon',
    'surat_n4_pasangan',
    'surat_kesanggupan_pemohon',
    'surat_kesanggupan_pasangan',
    'surat_persetujuan_orang_tua_pemohon',
    'surat_persetujuan_orang_tua_pasangan',
    'surat_sehat_pemohon',
    'surat_sehat_pasangan',
    'foto_4_x_6_pemohon',
    'foto_4_x_6_pasangan',
    'surat_pernyataan_bersama',
    'skck',
  ],
  CERAI: [
    'surat_pengantar_kesatker',
    'surat_izin_cerai',
    'akta_nikah',
    'kartu_tanda_anggota',
  ],
  RUJUK: [
    'surat_pengantar_kesatker',
    'surat_izin_rujuk',
    'akta_cerai',
    'surat_persetujuan_rujuk',
  ],
};

export class CreateDokumenNCR {
  @IsNotEmpty({
    message: 'Jenis pengajuan wajib diisi (ex: NIKAH, CERAI, RUJUK)',
  })
  @IsEnum(jenis_pengajuan_enum, {
    message: 'Jenis pengajuan harus salah satu dari: NIKAH, CERAI, RUJUK',
  })
  @IsString({ message: 'Jenis pengajuan harus berupa string' })
  jenis_pengajuan: string;

  @IsNotEmpty({
    message: 'Pengajuan NCR ID wajib di isi',
  })
  pengajuan_ncr_id: number;
}
