import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { formatDate, toCamelCase } from '../../core/utils/common.utils';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  ApprovalDocumentNcr,
  ApprovalNcr,
  CreatePermohonanCeraiRujuk,
  CreatePermohonan<PERSON>ikah,
  DetailNcr,
  GenerateSuratIzin,
  UploadAkta,
} from '../dto/ncr.dto';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { SortSearchColumn } from '../../core/utils/search.utils';
import { RequiredFiles } from '../dto/dokumen.dto';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import puppeteer from 'puppeteer';
import {
  gender_enum,
  jenis_pengajuan_enum,
  status_dokumen_ncr_enum,
  status_enum,
  status_pengajuan_enum,
} from '@prisma/client';
import { createMapHubunganKeluarga } from '../../core/utils/map.utils';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import * as moment from 'moment';
import { ISendNotificationMultiple } from '../../core/interfaces/firebase.interface';
import { FirebaseEnum } from '../../core/enums/firebase.enum';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class NcrService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly feUrl = this.configService.get<string>('FE_URL');

  // START SERVICE PERSONEL
  async getStatus(req: any) {
    const jenis_pengajuan_value = Object.values(jenis_pengajuan_enum);

    const data = await this.prisma.pengajuan_ncr.findMany({
      select: { status_pengajuan: true, jenis_pengajuan: true },
      where: {
        personel_id: req.user.personel_id,
        jenis_pengajuan: { in: jenis_pengajuan_value },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const queryResult = jenis_pengajuan_value.reduce((acc, jenis) => {
      const pengajuan = data.find((item) => item.jenis_pengajuan === jenis);
      acc[jenis] = pengajuan ? pengajuan.status_pengajuan : null;
      return acc;
    }, {});

    queryResult['kip_ksp'] = null;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async create(
    req: any,
    body: CreatePermohonanNikah,
    files: {
      [key: string]: Express.Multer.File[];
    },
  ) {
    const resultFiles = await this.validatieFiles(
      jenis_pengajuan_enum.NIKAH,
      files,
    );

    const personel = await this.prisma.personel.findFirst({
      where: { id: req.user.personel_id },
      select: {
        id: true,
        status_kawin_id: true,
        pengajuan_ncr: {
          where: {
            status_pengajuan: status_pengajuan_enum.PROSES,
            deleted_at: null,
          },
        },
      },
    });

    if (!personel)
      throw new ForbiddenException(
        `User dengan ID Personel '${req.user.personel_id}' tidak diizinkan untuk mengakses sumber daya ini`,
      );

    if (Number(personel.status_kawin_id) == 4)
      throw new ConflictException(
        'Pengajuan pernikahan baru tidak dapat diproses karena status Anda saat ini masih menikah.',
      );

    if (personel.pengajuan_ncr.length)
      throw new ConflictException(
        'Pengajuan nikah masih dalam proses. Tidak dapat mengajukan lagi sebelum pengajuan sebelumnya selesai.',
      );

    // START VALIDATE EXISTENCE OF IDs
    const [satuan, statusKawinPasangan] = await Promise.all([
      this.prisma.satuan.findFirst({ where: { id: body.satuan_id } }),
      this.prisma.status_kawin.findFirst({
        where: { id: body.status_pasangan_kawin_id },
      }),
      this.validateJob(body.pekerjaan_pasangan_id, 'Pekerjaan Pasangan ID'),
      this.validateJob(body.pekerjaan_bapak_id, 'Pekerjaan Bapak ID'),
      this.validateJob(body.pekerjaan_ibu_id, 'Pekerjaan Ibu ID'),
      this.validateAgama(body.agama_pasangan_id, 'Agama Pasangan ID'),
      this.validateAgama(body.agama_bapak_id, 'Agama Bapak ID'),
      this.validateAgama(body.agama_ibu_id, 'Agama Ibu ID'),
    ]);
    // END VALIDATE EXISTENCE OF IDs

    if (!satuan)
      throw new NotFoundException(`Satuan ID '${body.satuan_id}' tidak ada`);

    if (!statusKawinPasangan)
      throw new NotFoundException(
        `Status Kawin Pasangan ID '${body.status_pasangan_kawin_id}' tidak ada`,
      );

    const dataCalonPasangan = {
      pasangan: {
        nama: body.nama_pasangan,
        nik: body.nik_pasangan,
        tempat_lahir: body.tempat_lahir_pasangan,
        tanggal_lahir: body.tanggal_lahir_pasangan,
        pekerjaan_id: body.pekerjaan_pasangan_id,
        agama_id: body.agama_pasangan_id,
        status_kawin_id: body.status_pasangan_kawin_id,
        email: body.email_pasangan,
        no_hp: body.no_hp_pasangan,
        alamat: body.alamat_pasangan,
      },
      bapak: {
        nama: body.nama_lengkap_bapak,
        pekerjaan_id: body.pekerjaan_bapak_id,
        agama_id: body.agama_bapak_id,
        alamat: body.alamat_bapak,
      },
      ibu: {
        nama: body.nama_lengkap_ibu,
        pekerjaan_id: body.pekerjaan_ibu_id,
        agama_id: body.agama_ibu_id,
        alamat: body.alamat_ibu,
      },
    };

    const uploadResults = await this.createDokumen(resultFiles, files);

    const queryResult = await this.prisma.$transaction(async (prismaClient) => {
      const pengajuanNikah = await prismaClient.pengajuan_ncr.create({
        data: {
          satuan_id: body.satuan_id,
          tempat_pernikah: body.tempat_pernikah,
          tanggal_pernikah: new Date(body.tanggal_pernikah),
          status_pengajuan: status_pengajuan_enum.PROSES,
          alamat_personel_id: body.alamat_personel_id,
          status_kawin_pemohon_id: body.status_personel_kawin_id,
          status_kawin_pasangan_id: body.status_pasangan_kawin_id,
          jenis_pengajuan: jenis_pengajuan_enum.NIKAH,
          personel_id: personel.id,
          data_calon_pasangan: dataCalonPasangan,
        },
      });

      const dokumenPromises = await this.handleDokumen(
        prismaClient,
        resultFiles,
        files,
        Number(pengajuanNikah.id),
      );

      const historyNCR = prismaClient.history_status_ncr.create({
        data: {
          pengajuan_ncr_id: pengajuanNikah.id,
          status: status_pengajuan_enum.PROSES,
          created_by_id: req.user.id,
        },
      });

      await Promise.all([...dokumenPromises, historyNCR]);

      const cleanedPengajuanNikah = (({
        tanggal_sidang,
        nomor_surat_izin_nikah,
        surat_izin_nikah,
        pasangan_personel_id,
        updated_at,
        deleted_at,
        ...rest
      }) => rest)(pengajuanNikah);

      return [cleanedPengajuanNikah, uploadResults, historyNCR];
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_UPLOAD as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  async createCeraiRujuk(
    req: any,
    body: CreatePermohonanCeraiRujuk,
    jenis_pengajuan: jenis_pengajuan_enum,
    files: {
      [key: string]: Express.Multer.File[];
    },
  ) {
    const isValidPengajuan = [
      jenis_pengajuan_enum.CERAI as jenis_pengajuan_enum,
      jenis_pengajuan_enum.RUJUK as jenis_pengajuan_enum,
    ].includes(jenis_pengajuan);

    if (!isValidPengajuan)
      throw new InternalServerErrorException('Jenis Pengajuan Tidak Valid!');

    const resultFiles = await this.validatieFiles(jenis_pengajuan, files);

    const personel = await this.prisma.personel.findFirst({
      where: { id: req.user.personel_id },
      select: {
        id: true,
        status_kawin_id: true,
        jenis_kelamin: true,
        pengajuan_ncr: {
          select: {
            id: true,
            status_pengajuan: true,
            jenis_pengajuan: true,
            tempat_pernikah: true,
            alamat_personel_id: true,
            data_calon_pasangan: true,
            dokumen_ncr: {
              select: {
                created_at: true,
              },
              where: {
                jenis_dokumen: 'surat_akta_ncr',
              },
              orderBy: { created_at: 'desc' },
            },
          },
          orderBy: { created_at: 'desc' },
        },
        keluarga_personel: {
          where: {
            hubungan_keluarga_id: { in: [101, 102, 114, 115] },
          },
          orderBy: { created_at: 'desc' },
        },
      },
    });

    if (!personel)
      throw new ForbiddenException(
        `User dengan ID Personel '${req.user.personel_id}' tidak diizinkan untuk mengakses sumber daya ini.`,
      );

    const statusKawin = Number(personel.status_kawin_id);
    if (
      (jenis_pengajuan === jenis_pengajuan_enum.CERAI && statusKawin !== 4) ||
      (jenis_pengajuan === jenis_pengajuan_enum.RUJUK &&
        ![2, 3].includes(statusKawin))
    )
      throw new ConflictException(
        `Pengajuan ${jenis_pengajuan.toLowerCase()} tidak dapat diproses karena status Anda saat ini tidak sesuai.`,
      );

    await this.validatePengajuanNCR(personel, jenis_pengajuan);

    const uploadResults = await this.createDokumen(resultFiles, files);

    const dataPasangan = createMapHubunganKeluarga(
      personel.keluarga_personel,
      'createCeraiRujuk',
    );

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const pengajuanNikah = await this.createPengajuanNikah(
        tx,
        body,
        personel,
        jenis_pengajuan,
        dataPasangan,
      );

      const dokumenPromises = await this.handleDokumen(
        tx,
        resultFiles,
        files,
        Number(pengajuanNikah.id),
      );

      const historyNCR = tx.history_status_ncr.create({
        data: {
          pengajuan_ncr_id: pengajuanNikah.id,
          status: status_pengajuan_enum.PROSES,
          created_by_id: req.user.id,
        },
      });

      await Promise.all([...dokumenPromises, historyNCR]);

      const cleanedPengajuanNikah = (({
        tanggal_sidang,
        nomor_surat_izin_nikah,
        surat_izin_nikah,
        pasangan_personel_id,
        updated_at,
        deleted_at,
        ...rest
      }) => rest)(pengajuanNikah);

      return [cleanedPengajuanNikah, uploadResults, historyNCR];
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_UPLOAD as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  async uploadAktaV2(req: any, body: UploadAkta, file: Express.Multer.File) {
    const currentDate = moment().toDate();
    const { user } = req;
    const { pengajuan_ncr_id, jenis_pengajuan } = body;

    const pengajuanNcr = await this.prisma.pengajuan_ncr.findFirst({
      where: { id: pengajuan_ncr_id },
      include: {
        dokumen_ncr: {
          where: { deleted_at: null, jenis_dokumen: file.filename },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    if (!pengajuanNcr)
      throw new NotFoundException(
        `Pengajuan NCR ID '${pengajuan_ncr_id}' tidak ada`,
      );

    if (pengajuanNcr.jenis_pengajuan !== jenis_pengajuan)
      throw new BadRequestException(
        `Status pengajuan ini untuk ${pengajuanNcr.jenis_pengajuan}`,
      );

    if (
      pengajuanNcr.status_pengajuan != status_pengajuan_enum.SELESAI ||
      !pengajuanNcr.tanggal_sidang
    )
      throw new BadRequestException(`Status pengajuan anda belum selesai`);

    if (pengajuanNcr.dokumen_ncr.length)
      throw new ConflictException(`Dokumen ini sudah ada`);

    const personel = await this.prisma.personel.findUnique({
      where: { id: user.personel_id },
    });

    let hubunganPasangan = BigInt(
      personel.jenis_kelamin === gender_enum.LAKI_LAKI ? 101 : 102,
    );

    if (
      [
        jenis_pengajuan_enum.NIKAH as jenis_pengajuan_enum,
        jenis_pengajuan_enum.CERAI as jenis_pengajuan_enum,
      ].includes(jenis_pengajuan)
    ) {
      hubunganPasangan = BigInt(
        personel.jenis_kelamin === gender_enum.LAKI_LAKI ? 121 : 122,
      );
    }

    const uploadResult = await this.minioService.uploadFile(file);

    return await this.prisma.$transaction(async (tx) => {
      const createDokumen = await this.createDokumenRecord(
        tx,
        pengajuan_ncr_id,
        file,
        uploadResult,
      );

      const updateNCR = await tx.pengajuan_ncr.update({
        where: {
          id: pengajuan_ncr_id,
        },
        data: {
          surat_akta_ncr: createDokumen.id,
          updated_at: currentDate,
        },
      });

      if (jenis_pengajuan === jenis_pengajuan_enum.NIKAH) {
        const dataKeluargaPersonel = [];
        const keluarga = ['ibu', 'bapak', 'pasangan'];

        keluarga.forEach((keluarga) => {
          dataKeluargaPersonel.push(
            this.createDataKeluargaPersonel(
              pengajuanNcr,
              keluarga,
              user.personel_id,
            ),
          );
        });

        await tx.keluarga_personel.createMany({
          data: dataKeluargaPersonel,
        });

        return [createDokumen, updateNCR];
      }

      await this.updateKeluargaPersonel(
        tx,
        user.personel_id,
        hubunganPasangan,
        {
          hubunganPasangan:
            hubunganPasangan === (BigInt(101) || BigInt(121))
              ? BigInt(121)
              : BigInt(122),
          currentDate,
        },
      );

      return [createDokumen, updateNCR];
    });
  }

  //already refactor with func name uploadAktaV2
  async uploadAkta(req: any, body: UploadAkta, file: Express.Multer.File) {
    const currentDate = moment().toDate();

    const dataNCR = await this.prisma.pengajuan_ncr.findFirst({
      where: {
        id: +body.pengajuan_ncr_id,
      },
    });

    if (!dataNCR) {
      throw new NotFoundException(
        `Pengajuan NCR ID '${body.pengajuan_ncr_id}' tidak ada`,
      );
    }

    if (dataNCR.jenis_pengajuan != body.jenis_pengajuan) {
      throw new BadRequestException(
        `Status pengajuan ini untuk ${dataNCR.jenis_pengajuan}`,
      );
    }

    if (
      dataNCR.status_pengajuan != status_pengajuan_enum.SELESAI ||
      !dataNCR.tanggal_sidang
    ) {
      throw new BadRequestException(`Status pengajuan anda belum selesai`);
    }

    const dokumenNCR = await this.prisma.dokumen_ncr.findFirst({
      where: {
        jenis_dokumen: file.fieldname,
        pengajuan_ncr_id: +body.pengajuan_ncr_id,
      },
    });

    if (dokumenNCR) {
      throw new ConflictException(`Dokumen ini sudah ada`);
    }

    const personel = await this.prisma.personel.findFirst({
      where: {
        id: req.user.personel_id,
      },
    });

    const gender_enumPasangan =
      personel.jenis_kelamin === gender_enum.LAKI_LAKI
        ? gender_enum.PEREMPUAN
        : gender_enum.LAKI_LAKI;

    // 101 ISTRI, 102 SUAMI
    const hubunganPasangan =
      personel.jenis_kelamin === gender_enum.LAKI_LAKI
        ? BigInt(101)
        : BigInt(102);

    const uploadResult = await this.minioService.uploadFile(file);

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const createDokumen = await this.createDokumenRecord(
        tx,
        body.pengajuan_ncr_id,
        file,
        uploadResult,
      );

      const updateNCR = await tx.pengajuan_ncr.update({
        where: {
          id: body.pengajuan_ncr_id,
        },
        data: {
          surat_akta_ncr: createDokumen.id,
          updated_at: currentDate,
        },
      });

      if (body.jenis_pengajuan === jenis_pengajuan_enum.NIKAH) {
        const createIbuMertua = await tx.keluarga_personel.create({
          data: {
            nama_keluarga: dataNCR.data_calon_pasangan['ibu'].nama || '',
            alamat: dataNCR.data_calon_pasangan['ibu'].alamat || '',
            agama_id: BigInt(dataNCR.data_calon_pasangan['ibu'].agama_id),
            jenis_kelamin: gender_enum.PEREMPUAN,
            status: status_enum.HIDUP,
            hubungan_keluarga_id: BigInt(115), //IBU MERTUA
            personel_id: req.user.personel_id,
          },
        });

        const createAyahMertua = await tx.keluarga_personel.create({
          data: {
            nama_keluarga: dataNCR.data_calon_pasangan['bapak'].nama || '',
            alamat: dataNCR.data_calon_pasangan['bapak'].alamat || '',
            agama_id: BigInt(dataNCR.data_calon_pasangan['bapak'].agama_id),
            jenis_kelamin: gender_enum.LAKI_LAKI,
            status: status_enum.HIDUP,
            hubungan_keluarga_id: BigInt(114), //AYAH MERTUA
            personel_id: req.user.personel_id,
          },
        });

        const createPasangan = await tx.keluarga_personel.create({
          data: {
            nama_keluarga: dataNCR.data_calon_pasangan['pasangan'].nama || '',
            alamat: dataNCR.data_calon_pasangan['pasangan'].alamat || '',
            agama_id: BigInt(dataNCR.data_calon_pasangan['pasangan'].agama_id),
            no_hp: dataNCR.data_calon_pasangan['pasangan'].no_hp || '',
            tempat_lahir:
              dataNCR.data_calon_pasangan['pasangan'].tempat_lahir || '',
            tanggal_lahir: dataNCR.data_calon_pasangan['pasangan'].tanggal_lahir
              ? new Date(dataNCR.data_calon_pasangan['pasangan'].tanggal_lahir)
              : null,
            jenis_kelamin: gender_enumPasangan,
            status: status_enum.HIDUP,
            hubungan_keluarga_id: BigInt(hubunganPasangan),
            personel_id: req.user.personel_id,
            tanggal_nikah: new Date(dataNCR.tanggal_pernikah),
            status_nikah: true,
            status_pernikahan: true,
          },
        });

        await tx.pekerjaan_keluarga.create({
          data: {
            status_terakhir: true,
            jenis_pekerjaan_id: BigInt(
              dataNCR.data_calon_pasangan['ibu'].pekerjaan_id,
            ),
            keluarga_personel_id: createIbuMertua.id,
            tanggal_perubahan: currentDate,
          },
        });

        await tx.pekerjaan_keluarga.create({
          data: {
            status_terakhir: true,
            jenis_pekerjaan_id: BigInt(
              dataNCR.data_calon_pasangan['bapak'].pekerjaan_id,
            ),
            keluarga_personel_id: createAyahMertua.id,
            tanggal_perubahan: currentDate,
          },
        });

        await tx.pekerjaan_keluarga.create({
          data: {
            status_terakhir: true,
            jenis_pekerjaan_id: BigInt(
              dataNCR.data_calon_pasangan['pasangan'].pekerjaan_id,
            ),
            keluarga_personel_id: createPasangan.id,
            tanggal_perubahan: currentDate,
          },
        });
      } else if (body.jenis_pengajuan === jenis_pengajuan_enum.CERAI) {
        const recordToUpdate = await tx.keluarga_personel.findFirst({
          where: {
            personel_id: req.user.personel_id,
            hubungan_keluarga_id: BigInt(hubunganPasangan),
          },
        });

        if (recordToUpdate) {
          await tx.keluarga_personel.update({
            where: {
              id: recordToUpdate.id,
            },
            data: {
              hubungan_keluarga_id:
                hubunganPasangan === BigInt(101) ? BigInt(121) : BigInt(122),
              updated_at: currentDate,
            },
          });
        } else {
          throw new Error('Record tidak ditemukan');
        }
      } else {
        const hubunganSebelumnya =
          personel.jenis_kelamin === gender_enum.LAKI_LAKI
            ? BigInt(121)
            : BigInt(122);

        const recordToUpdate = await tx.keluarga_personel.findFirst({
          where: {
            personel_id: req.user.personel_id,
            hubungan_keluarga_id: BigInt(hubunganPasangan),
          },
        });

        if (recordToUpdate) {
          await tx.keluarga_personel.update({
            where: {
              id: recordToUpdate.id,
            },
            data: {
              hubungan_keluarga_id:
                hubunganSebelumnya === BigInt(121) ? BigInt(121) : BigInt(122),
              updated_at: currentDate,
            },
          });
        } else {
          throw new Error('Record tidak ditemukan');
        }
      }

      return [createDokumen, updateNCR];
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_UPLOAD as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  // END SERVICE PERSONEL

  // START SERVICE GENERAL
  async getDetail(req: any, data: DetailNcr) {
    const { jenis_pengajuan, pengajuan_id } = data;
    const { user } = req;

    const wherePersonel: Record<string, any> = {};

    if (pengajuan_id) {
      wherePersonel.pengajuan_ncr = { some: { id: +pengajuan_id } };
    } else if (!user.role_name) {
      wherePersonel.id = user.personel_id;
    }

    const wherePengajuanNcr: Record<string, any> = jenis_pengajuan
      ? { jenis_pengajuan }
      : {};

    const personel = await this.prisma.personel.findFirst({
      where: wherePersonel,
      select: {
        nama_lengkap: true,
        ktp_nomor: true,
        tanggal_lahir: true,
        nrp: true,
        email: true,
        no_hp: true,
        jenis_kelamin: true,
        tempat_lahir: true,
        pengajuan_ncr: {
          select: {
            tanggal_pernikah: true,
            tanggal_sidang: true,
            tempat_pernikah: true,
            data_calon_pasangan: true,
            satuan: {
              select: {
                id: true,
                nama: true,
              },
            },
            dokumen_ncr: {
              select: {
                id: true,
                jenis_dokumen: true,
                url: true,
                filename: true,
                status: true,
                alasan_penolakan: true,
              },
              where: {
                deleted_at: null,
              },
            },
          },
          where: {
            deleted_at: null,
            ...wherePengajuanNcr,
          },
          orderBy: {
            created_at: 'desc',
          },
        },
        alamat: {
          select: {
            id: true,
            alamat: true,
          },
        },
        status_kawin: {
          select: {
            id: true,
            nama: true,
          },
        },
        agama: {
          select: {
            nama: true,
          },
        },
        keluarga_personel: {
          select: {
            nama_keluarga: true,
            ktp_nomor: true,
            tempat_lahir: true,
            tanggal_lahir: true,
            no_hp: true,
            alamat: true,
            hubungan_keluarga_id: true,
            status: true,
            agama: {
              select: {
                nama: true,
              },
            },
            pekerjaan_keluarga: {
              select: {
                jenis_pekerjaan: {
                  select: {
                    jenis: true,
                  },
                },
              },
            },
          },
          where: {
            tanggal_cerai: null,
          },
        },
      },
    });

    if (!personel)
      throw new NotFoundException('Data pengajuan tidak ditemukan');

    const hubunganKeluarga = createMapHubunganKeluarga(
      personel.keluarga_personel,
      'getDetailV2',
    );

    const pengajuanNcr = personel.pengajuan_ncr[0] ?? null;

    const calonPasangan = pengajuanNcr.data_calon_pasangan ?? null;
    const dataPasangan = {
      ...(hubunganKeluarga['pasangan'] || calonPasangan['pasangan'] || {}),
      ibu: calonPasangan['ibu'] ?? null,
      bapak: calonPasangan['bapak'] ?? null,
    };

    const dataPersonel = {
      nama_pemohon: personel.nama_lengkap,
      nik: personel.ktp_nomor,
      tanggal_sidang: pengajuanNcr.tanggal_sidang ?? null,
      tanggal_pernikah: pengajuanNcr.tanggal_pernikah ?? null,
      tempat_pernikah: pengajuanNcr.tempat_pernikah ?? null,
      satuan_id: pengajuanNcr.satuan?.id ?? null,
      satuan: pengajuanNcr.satuan?.nama ?? null,
      tempat_lahir: personel.tempat_lahir,
      tanggal_lahir: personel.tanggal_lahir,
      pekerjaan: 'POLRI',
      pangkat: req.user.pangkat_nama_singkat,
      nrp: personel.nrp,
      jabatan: req.user.jabatan_nama,
      agama: personel.agama?.nama,
      status_id: personel.status_kawin?.id,
      status: personel.status_kawin?.nama,
      email: personel.email,
      no_hp: personel.no_hp,
      alamat_id: personel.alamat[0]?.id ?? null,
      alamat: personel.alamat[0]?.alamat ?? null,
      ibu: hubunganKeluarga['ibu'] ?? null,
      bapak: hubunganKeluarga['bapak'] ?? null,
    };

    const document = pengajuanNcr.dokumen_ncr ?? [];
    const documentResult = await this.convertUrlPhoto(document);

    const queryResult = {
      dataPersonel,
      dataPasangan,
      dokumen: documentResult,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NCR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  // END SERVICE GENERAL

  // START SERVICE OPERATOR
  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama_personel: { field: 'personel.nama_lengkap', type: 'string' },
      tempat_pernikah: { field: 'tempat_pernikah', type: 'string' },
      status_pengajuan: { field: 'status_pengajuan', type: 'enum' },
      jenis_pengajuan: { field: 'jenis_pengajuan', type: 'enum' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, listNikah] = await this.prisma.$transaction([
      this.prisma.pengajuan_ncr.count({
        where: where,
      }),
      this.prisma.pengajuan_ncr.findMany({
        select: {
          id: true,
          tempat_pernikah: true,
          tanggal_pernikah: true,
          tanggal_sidang: true,
          status_pengajuan: true,
          jenis_pengajuan: true,
          surat_izin_nikah: true,
          data_calon_pasangan: true,
          personel: {
            select: { nama_lengkap: true, nrp: true },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = listNikah.map((data) => ({
      id: data.id,
      nama_personel: data.personel['nama_lengkap'],
      nrp_personel: data.personel['nrp'],
      nama_pasangan: data.data_calon_pasangan['pasangan'].nama,
      status_pengajuan: data.status_pengajuan,
      tanggal_pernikah: formatDate(data.tanggal_pernikah),
      tanggal_sidang: data.tanggal_sidang
        ? formatDate(data.tanggal_sidang)
        : null,
      tempat_pernikah: data.tempat_pernikah,
      surat_izin_nikah: data.surat_izin_nikah,
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NCR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async generateSuratIzin(req: any, body: GenerateSuratIzin) {
    const jenisDokumen = 'surat-izin';

    const pengajuanNCR = await this.prisma.pengajuan_ncr.findFirst({
      where: {
        id: +body.pengajuan_ncr_id,
      },
    });

    if (!pengajuanNCR) {
      throw new NotFoundException(
        `Pengajuan ncr id '${body.pengajuan_ncr_id}' tidak ada`,
      );
    }

    const dokumenIsExist = await this.prisma.dokumen_ncr.findFirst({
      where: {
        pengajuan_ncr_id: +body.pengajuan_ncr_id,
        jenis_dokumen: jenisDokumen,
      },
    });

    if (dokumenIsExist) {
      throw new ConflictException(`Dokumen ini sudah ada`);
    }

    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    await page.setContent(body.content);

    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '2cm',
        bottom: '2cm',
        left: '2cm',
        right: '2cm',
      },
    });
    const buffer = Buffer.from(pdfBuffer);

    const originalName = `${jenisDokumen}-${body.jenis_pengajuan.toLowerCase()}.pdf`;

    const resultFile = await this.minioService.uploadToMinio(
      buffer,
      originalName,
    );

    const queryResult = await this.prisma.$transaction(async (prismaClient) => {
      const dokumenNCR = await prismaClient.dokumen_ncr.create({
        data: {
          pengajuan_ncr_id: +body.pengajuan_ncr_id,
          jenis_dokumen: jenisDokumen,
          originalname: originalName,
          encoding: 'binary', // Set encoding
          mimetype: 'application/pdf', // Set mimetype
          size: buffer.length, // Set size
          key: resultFile.Key || '',
          url: resultFile.Location || '',
          filename: resultFile.filename || '',
        },
      });

      const pengajuanNCR = await prismaClient.pengajuan_ncr.update({
        where: {
          id: +body.pengajuan_ncr_id,
        },
        data: {
          nomor_surat_izin_nikah: body.nomor,
          surat_izin_nikah: dokumenNCR.id,
          updated_at: new Date(),
        },
      });

      return [dokumenNCR, pengajuanNCR];
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  //END SERVICE OPERATOR

  async approvalNcr(req: any, approval: ApprovalNcr) {
    const pengajuanNcr = await this.prisma.pengajuan_ncr.findFirst({
      where: {
        id: approval.pengajuan_id,
      },
    });

    if (!pengajuanNcr) throw new NotFoundException('Pengajuan NCR tidak ada!');

    const queryResult = await this.prisma.pengajuan_ncr.update({
      where: {
        id: approval.pengajuan_id,
      },
      data: {
        tanggal_sidang: new Date(approval.tanggal_sidang),
        status_pengajuan: approval.approval
          ? status_pengajuan_enum.DISETUJUI
          : status_pengajuan_enum.DITOLAK,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async approvalDocumentNCR(req: any, approval: ApprovalDocumentNcr) {
    const { id, status, alasan_penolakan } = approval;

    const dokumenNcr = await this.prisma.dokumen_ncr.findFirst({
      where: { id },
      select: {
        id: true,
        jenis_dokumen: true,
        pengajuan_ncr: { select: { jenis_pengajuan: true, personel_id: true } },
      },
    });

    if (!dokumenNcr) throw new NotFoundException('Dokumen NCR tidak ada!');

    const user = await this.prisma.users.findFirst({
      where: { personel_id: dokumenNcr.pengajuan_ncr.personel_id },
    });
    const target = await this.prisma.users_device_token.findMany({
      where: { user_id: user.id },
    });

    if (target) {
      let mock: ISendNotificationMultiple;
      if (approval.status === status_dokumen_ncr_enum.REJECTED) {
        mock = {
          title: `Pengajuan ${dokumenNcr.pengajuan_ncr.jenis_pengajuan}`,
          data: { link: `${this.feUrl}/pengajuan-ncr` },
          type: 'NCR',
          body: `Dokumen ${toCamelCase(dokumenNcr.jenis_dokumen)} anda ditolak`,
          tokens: target,
        };
      }
      if (approval.status === status_dokumen_ncr_enum.ACCEPTED) {
        mock = {
          title: `Pengajuan ${dokumenNcr.pengajuan_ncr.jenis_pengajuan}`,
          data: { link: `${this.feUrl}/pengajuan-ncr` },
          type: 'NCR',
          body: `Dokumen ${toCamelCase(dokumenNcr.jenis_dokumen)} anda diterima`,
          tokens: target,
        };
      }

      if (mock) {
        this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
      }
    }

    const queryResult = await this.prisma.dokumen_ncr.update({
      where: { id },
      data: {
        status,
        alasan_penolakan,
        updated_at: new Date(),
      },
    });
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.NCR_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async editDocumentNCR(req: any, id: string, files: any) {
    if (Object.keys(files).length === 0) {
      throw new UnprocessableEntityException('File tidak boleh kosong');
    }

    this.validateFileSize(files);

    const dokumenNcr = await this.prisma.dokumen_ncr.findFirst({
      where: { id: +id },
    });

    if (!dokumenNcr)
      throw new NotFoundException(`Pengajuan NCR ID ${id} tidak ada`);

    const uploadResults = await this.createDokumen(['file'], files);

    const queryResult = [];

    await this.prisma.$transaction(async (prismaClient) => {
      let uploadIndex = 0;

      for (const key in files) {
        const fileArray = files[key];
        for (const file of fileArray) {
          const uploadResult = uploadResults[uploadIndex];

          const updateResult = await prismaClient.dokumen_ncr.update({
            where: { id: +id },
            data: {
              originalname: file.originalname,
              encoding: file.encoding,
              mimetype: file.mimetype,
              size: file.size,
              key: uploadResult.Key || '',
              url: uploadResult.Location || '',
              filename: uploadResult.filename || '',
              status: status_dokumen_ncr_enum.PROCESS,
              alasan_penolakan: null,
              updated_at: new Date(),
            },
          });

          queryResult.push(updateResult);
          uploadIndex++;
        }
      }
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.NCR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.NCR_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  // START HELPER
  private async validateJob(jobId: number, jobDescription: string) {
    const job = await this.prisma.jenis_pekerjaan.findFirst({
      where: {
        id: jobId,
      },
    });

    if (!job)
      throw new NotFoundException(`${jobDescription} '${jobId}' tidak ada`);
  }

  private async validateAgama(agamaId: number, agamaDescription: string) {
    const agama = await this.prisma.jenis_pekerjaan.findFirst({
      where: {
        id: +agamaId,
      },
    });

    if (!agama) {
      throw new NotFoundException(`${agamaDescription} '${agamaId}' tidak ada`);
    }
  }

  async validatieFiles(jenis_pengajuan: jenis_pengajuan_enum, files: any) {
    let requiredFiles: string[] = [];

    switch (jenis_pengajuan) {
      case jenis_pengajuan_enum.NIKAH:
        requiredFiles = RequiredFiles.NIKAH;
        break;
      case jenis_pengajuan_enum.CERAI:
        requiredFiles = RequiredFiles.CERAI;
        break;
      case jenis_pengajuan_enum.RUJUK:
        requiredFiles = RequiredFiles.RUJUK;
        break;
    }

    const errors = {};
    for (const file of requiredFiles) {
      if (!files[file]) {
        errors[file] = `${file.replace(/_/g, ' ')} tidak boleh kosong`;
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new UnprocessableEntityException({
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        error: 'Unprocessable Entity',
        message: errors,
      });
    }

    this.validateFileSize(files);

    return requiredFiles;
  }

  private validateFileSize(files: any) {
    const maxFileSize = 4 * 1024 * 1024; // 4MB
    const errors = {};

    for (const fieldName in files) {
      const fieldFiles = files[fieldName];
      fieldFiles.forEach((file: Express.Multer.File) => {
        if (file.size > maxFileSize) {
          errors[fieldName] = 'File terlalu besar';
        }
      });
    }

    if (Object.keys(errors).length > 0) {
      throw new UnprocessableEntityException({
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        error: 'Unprocessable Entity',
        message: errors,
      });
    }
  }

  private async createDokumenRecord(
    tx: any,
    pengajuanId: number,
    file: Express.Multer.File,
    uploadResult: any,
  ) {
    return tx.dokumen_ncr.create({
      data: {
        pengajuan_ncr_id: pengajuanId,
        jenis_dokumen: file.fieldname,
        originalname: file.originalname,
        encoding: file.buffer.toString('latin1'),
        mimetype: file.mimetype,
        size: file.size,
        key: uploadResult.Key || '',
        url: uploadResult.Location || '',
        filename: uploadResult.filename || '',
      },
    });
  }

  private isPengajuanCeraiValid(pengajuanNcr: any) {
    const isPengajuanCerai = (item: any) =>
      item.jenis_pengajuan === jenis_pengajuan_enum.CERAI;
    const hasDokumenNcr = (item: any) => item.dokumen_ncr[0] !== undefined;
    const isWithinThreeMonths = (item: any) => {
      const dokumenDate = new Date(item.dokumen_ncr[0].created_at);
      const threeMonthsLater = new Date(
        dokumenDate.setMonth(dokumenDate.getMonth() + 3),
      );
      return new Date() <= threeMonthsLater;
    };

    return pengajuanNcr.some(
      (item) =>
        isPengajuanCerai(item) &&
        hasDokumenNcr(item) &&
        isWithinThreeMonths(item),
    );
  }

  private async validatePengajuanNCR(
    personel: any,
    jenis_pengajuan: jenis_pengajuan_enum,
  ) {
    if (personel.pengajuan_ncr.length) {
      const isInProses = personel.pengajuan_ncr.some(
        (item) => item.status_pengajuan === status_pengajuan_enum.PROSES,
      );
      if (isInProses) {
        throw new ConflictException(
          'Pengajuan masih dalam proses, harap tunggu hingga selesai.',
        );
      }

      const isCeraiValid = this.isPengajuanCeraiValid(personel.pengajuan_ncr);
      if (jenis_pengajuan === jenis_pengajuan_enum.RUJUK && !isCeraiValid) {
        throw new ConflictException(
          'Pengajuan rujuk tidak dapat diproses karena sudah lebih dari 3 bulan sejak bercerai.',
        );
      }
    }
  }

  private async handleDokumen(
    tx: any,
    resultFiles: any,
    files: any,
    idPengajuanNikah: number,
  ) {
    const uploadResults = await this.createDokumen(resultFiles, files);
    return resultFiles
      .map((fieldname, index) => {
        const file: any = Object.values(files)
          .flat()
          .find((f: any) => f.fieldname === fieldname);
        const uploadResult = uploadResults[index];
        return file && uploadResult
          ? this.createDokumenRecord(tx, idPengajuanNikah, file, uploadResult)
          : null;
      })
      .filter(Boolean);
  }

  private async createPengajuanNikah(
    tx: any,
    body: CreatePermohonanCeraiRujuk,
    personel: any,
    jenis_pengajuan: jenis_pengajuan_enum,
    dataPasangan: any,
  ) {
    const isLakiLaki = personel.jenis_kelamin === gender_enum.LAKI_LAKI;
    const statusPemohon = isLakiLaki ? 2 : 3;
    const statusPasangan = isLakiLaki ? 3 : 2;
    const statusKawinPemohonId =
      jenis_pengajuan === jenis_pengajuan_enum.CERAI ? 4 : statusPemohon;
    const statusKawinPasanganId =
      jenis_pengajuan === jenis_pengajuan_enum.CERAI ? 4 : statusPasangan;

    return tx.pengajuan_ncr.create({
      data: {
        satuan_id: body.satuan_id,
        tempat_pernikah: personel.pengajuan_ncr[0]?.tempat_pernikah || '',
        status_pengajuan: status_pengajuan_enum.PROSES,
        alamat_personel_id: personel.pengajuan_ncr[0]?.alamat_personel_id || 1,
        status_kawin_pemohon_id: statusKawinPemohonId,
        status_kawin_pasangan_id: statusKawinPasanganId,
        jenis_pengajuan: jenis_pengajuan,
        personel_id: personel.id,
        data_calon_pasangan: dataPasangan,
      },
    });
  }

  private async convertUrlPhoto(document: any) {
    for (const doc of document) {
      if (doc.filename) {
        doc.url = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}/${doc.filename}`,
        );
      }
    }

    return document;
  }

  private createDataKeluargaPersonel(data: any, key: string, personel: any) {
    const currentDate = moment().toDate();

    const relationsMap = {
      ibu: {
        jenis_kelamin: gender_enum.PEREMPUAN,
        hubungan_keluarga_id: BigInt(115),
      },
      bapak: {
        jenis_kelamin: gender_enum.LAKI_LAKI,
        hubungan_keluarga_id: BigInt(114),
      },
      pasangan: {
        jenis_kelamin:
          personel.jenis_kelamin === gender_enum.LAKI_LAKI
            ? gender_enum.PEREMPUAN
            : gender_enum.LAKI_LAKI,
        hubungan_keluarga_id:
          personel.jenis_kelamin === gender_enum.LAKI_LAKI
            ? BigInt(101)
            : BigInt(102),
      },
    };

    const result = {
      nama_keluarga: data[key]?.nama || '',
      alamat: data[key]?.alamat || '',
      agama_id: BigInt(data[key]?.agama_id || 0),
      jenis_kelamin: relationsMap[key].jenis_kelamin,
      status: status_enum.HIDUP,
      hubungan_keluarga_id: relationsMap[key].hubungan_keluarga_id,
      personel_id: personel.personel_id,
      pekerjaan_keluarga: {
        create: {
          status_terakhir: true,
          jenis_pekerjaan_id: BigInt(data[key]?.pekerjaan_id || 0),
          tanggal_perubahan: currentDate,
        },
      },
    };

    if (key === 'pasangan') {
      Object.assign(result, {
        no_hp: data[key]?.no_hp || '',
        tempat_lahir: data[key]?.tempat_lahir || '',
        status_nikah: true,
        status_pernikahan: true,
        tanggal_nikah: data[key]?.tanggal_pernikah
          ? new Date(data[key]?.tanggal_pernikah)
          : null,
        tanggal_lahir: data[key]?.tanggal_lahir
          ? new Date(data[key]?.tanggal_lahir)
          : null,
      });
    }

    return result;
  }

  private async updateKeluargaPersonel(
    tx: any,
    personel_id: bigint,
    hubungan_keluarga_id: bigint,
    data: any,
  ) {
    const recordToUpdate = await tx.keluarga_personel.findFirst({
      where: { personel_id, hubungan_keluarga_id },
    });

    if (!recordToUpdate) throw new Error('Record tidak ditemukan');

    await tx.keluarga_personel.update({
      where: {
        id: recordToUpdate.id,
      },
      data: {
        hubungan_keluarga_id: data.hubunganPasangan,
        updated_at: data.currentDate,
      },
    });
  }

  async createDokumen(requiredFiles: string[], files: any): Promise<any[]> {
    const uploadPromises = [];

    for (const key in files) {
      const fileArray = files[key];
      for (const file of fileArray) {
        if (requiredFiles.includes(file.fieldname)) {
          uploadPromises.push(this.minioService.uploadFile(file));
        }
      }
    }

    return await Promise.all(uploadPromises);
  }

  // END HELPER
}
