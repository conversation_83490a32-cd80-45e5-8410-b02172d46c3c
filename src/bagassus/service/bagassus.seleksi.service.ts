import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { CreateSeleksiDto, GetManySeleksiDto } from '../dto/bagassus.dto';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { CONSTANT_LOG } from '../../core/constants/log.constant';

@Injectable()
export class BagassusSeleksiService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getRequirement(req: any) {
    try {
      const queryResult = await this.prisma.bagassus_seleksi_syarat.findMany({
        select: {
          id: true,
          nama: true,
        },
        orderBy: { id: 'asc' },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getRequirementValue(req: any, requirementId: number) {
    try {
      const queryResult =
        await this.prisma.bagassus_seleksi_syarat_value.findMany({
          where: { syarat_id: requirementId },
          select: {
            id: true,
            nama: true,
          },
          orderBy: { id: 'asc' },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get all requirement values',
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async createSeleksi(req: any, body: CreateSeleksiDto) {
    try {
      const queryResult = await this.prisma.bagassus_seleksi.create({
        data: {
          title: body.title,
          description: body.description,
          location_type_id: body.location_type_id,
          penugasan_id: body.penugasan,
          tanggal_mulai: new Date(body.start_date),
          tanggal_selesai: new Date(body.end_date),
          submission_start: new Date(body.submission_start),
          submission_end: new Date(body.submission_end),
          tahapan_seleksi: body.round,
          logo_file_id: body.logo_file_id,
          document_file_id: body.document_file_id,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const tahapan = body.round_selection_dates.map((date, idx) => ({
        seleksi_id: queryResult.id,
        tahap: idx + 1,
        tanggal_mulai: new Date(date.start_date),
        tanggal_selesai: new Date(date.end_date),
      }));

      await this.prisma.bagassus_tahap_seleksi.createMany({ data: tahapan });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async endRegistrationPeriod(req: any, seleksiId: number) {
    try {
      const seleksi = await this.prisma.bagassus_seleksi.findFirst({
        where: { id: seleksiId },
      });

      if (!seleksi) {
        throw new BadRequestException(`Data seleksi tidak ditemukan`);
      }

      const queryResult = await this.prisma.bagassus_seleksi.update({
        where: { id: seleksiId },
        data: {
          has_been_ended: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getManySeleksi(req: any, queries: GetManySeleksiDto) {
    try {
      const queryResult = await this.prisma.bagassus_seleksi.findMany({
        where: { location_type_id: queries.loc_type },
        select: {
          id: true,
          title: true,
          description: true,
          bagassus_seleksi_file_logo: { select: { filename: true, url: true } },
          submission_end: true,
          tanggal_tahapan_seleksi: {
            select: {
              tahap: true,
              tanggal_mulai: true,
              tanggal_selesai: true,
            },
          },
          has_been_ended: true,
        },
        orderBy: { created_at: 'desc' },
      });

      const now = new Date();
      for (const data of queryResult) {
        if (data.has_been_ended != null && data.has_been_ended) {
          data['status_terbaru'] = 'Ditutup';
          continue;
        }

        const { submission_end, tanggal_tahapan_seleksi: steps } = data;
        delete data.tanggal_tahapan_seleksi;
        const withinRegistrationPeriod = now <= new Date(submission_end);
        if (withinRegistrationPeriod) {
          data['status_terbaru'] = 'Dibuka';
          continue;
        }

        const withinSelectionPeriod = new Date(
          steps[steps.length - 1].tanggal_selesai,
        );

        if (withinSelectionPeriod) {
          const currentStep = this._getCurrentSelectionStep(steps);
          data['status_terbaru'] = `Seleksi Tahap ${currentStep}`;
          continue;
        }

        data['status_terbaru'] = 'Ditutup';
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  // async getOneSeleksi(seleksiId: number) {
  //   try {
  //     const seleksi = await this.prisma.bagassus_seleksi.findFirst({
  //       where: { id: seleksiId },
  //       select: {
  //         id: true,
  //         title: true,
  //         description: true,
  //         logo: { select: { filename: true, url: true } },
  //         submission_end: true,
  //         tanggal_tahapan_seleksi: {
  //           select: {
  //             tahap: true,
  //             tanggal_mulai: true,
  //             tanggal_selesai: true,
  //           },
  //         },
  //         has_been_ended: true,
  //         created_at: true,
  //       },
  //     });
  //
  //     return seleksi;
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  private _getCurrentSelectionStep(arr: any[]) {
    const withinDates = function (target: Date, dtStart: Date, dtEnd: Date) {
      return dtStart <= target && target <= dtEnd;
    };

    let result: number;
    const now = new Date();

    for (const data of arr) {
      const start = new Date(data.tanggal_mulai);
      const end = new Date(data.tanggal_selesai);
      if (withinDates(now, start, end)) {
        result = data.tahap;

      }
    }

    return result;
  }
}
