import { BadRequestException, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { translateDotNotation } from 'src/core/utils/common.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { ConstantLogType } from '../../core/interfaces/log.type';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  buildPaginatedResponse,
  buildResponse,
} from '../../core/utils/response.util';
import {
  BagassusDepositAssignDto,
  BagassusDepositGetListDto,
} from '../dto/bagassus-deposit.dto';

@Injectable()
export class BagassusDepositService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
    private readonly minioService: MinioService,
  ) {}

  async getInstansi(req: any) {
    const queryResult = await this.prisma.bagassus_instansi.findMany({
      select: {
        id: true,
        nama: true,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
    query: BagassusDepositGetListDto,
  ) {
    const { instansi_id, q, pangkat_id } = query;
    const { sort_column, sort_desc } = searchAndSortData;

    const limit = Number(paginationData.limit || 100);
    const page = Number(paginationData.page || 1);

    const where = ['bd.deleted_at ISNULL'];
    if (instansi_id) where.push(`bd.instansi_id = ${instansi_id}`);
    if (q) where.push(`p.nama_lengkap ILIKE '%${q}%'`);
    if (pangkat_id) where.push(`pt.pangkat_id = ${pangkat_id}`);

    let sort = 'bd.id ASC';
    if (sort_column) {
      sort = `bd.${sort_column} ${sort_desc || 'ASC'}`;
    }

    const [total, data] = await this.prisma.$transaction([
      this.prisma.$executeRawUnsafe(`
        SELECT COUNT(*) FROM bagassus_deposit bd
        INNER JOIN "personel" p ON p.id = bd.personel_id
        INNER JOIN "mv_pangkat_terakhir" pt ON pt.personel_id = p.id
        WHERE ${where.join(' AND ')}
      `),
      this.prisma.$queryRawUnsafe<Array<Record<string, any>>>(`
        SELECT 
          bd.id,
          bd.type,
          bd.no_pendaftaran_seleksi,
          bd.no_ujian_seleksi,
          i.nama AS "instansi.nama",
          p.nama_lengkap AS "personel.nama",
          p.nrp AS "personel.nrp",
          p.jenis_kelamin AS "personel.jenis_kelamin",
          sa.nama AS "personel.status_aktif",
          ja.nama AS "personel.jabatan",
          pa.id AS "personel.pangkat_id",
          pa.nama AS "personel.pangkat",
          pa.nama_singkat AS "personel.pangkat_singkat",
          sat.nama AS "personel.satuan"
        FROM bagassus_deposit bd
          INNER JOIN "bagassus_instansi" i ON i.id = bd.instansi_id
          INNER JOIN "personel" p ON p.id = bd.personel_id
          INNER JOIN "status_aktif" sa ON sa.id = p.status_aktif_id
          INNER JOIN "mv_jabatan_terakhir" jt ON jt.personel_id = p.id
          INNER JOIN "jabatan" ja ON ja.id = jt.jabatan_id
          INNER JOIN "satuan" sat ON sat.id = jt.satuan_id
          INNER JOIN "mv_pangkat_terakhir" pt ON pt.personel_id = p.id
          INNER JOIN "pangkat" pa ON pa.id = pt.pangkat_id
        WHERE ${where.join(' AND ')}
        ORDER BY ${sort}
        LIMIT ${limit} OFFSET ${limit * (page - 1)}
      `),
    ]);

    const queryResult = data.map((item) => translateDotNotation(item));
    const totalData = Number(total?.[0]?.count || 0);
    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(
      { result: queryResult, page, totalData, totalPage },
      message,
    );
  }

  async assign(
    req: any,
    payload: BagassusDepositAssignDto,
    files: Express.Multer.File[],
  ) {
    payload.start_date = new Date(payload.start_date);
    payload.end_date = new Date(payload.end_date);

    const fileSprin = files.find((d) => d.fieldname === 'file_sprin');
    const fileKep = files.find((d) => d.fieldname === 'file_kep');

    if (!fileSprin) throw new BadRequestException('File sprin wajib diunggah!');
    if (!fileKep) throw new BadRequestException('File kep wajib diunggah!');

    const check = await this.prisma.bagassus_deposit.findFirst({
      where: { id: payload.deposit_id },
    });

    if (!check) throw new BadRequestException('Data tidak ditemukan!');

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const [uploadedSprin, uploadedKep] = await Promise.all([
        this.uploadFile(fileSprin),
        this.uploadFile(fileKep),
      ]);

      await tx.bagassus_deposit.update({
        where: { id: payload.deposit_id },
        data: { deleted_at: new Date() },
      });

      console.log(check);

      return await tx.bagassus_personel.create({
        data: {
          personel_id: check.personel_id,
          instansi_id: payload.instansi_id,
          type: check.type,
          tanggal_mulai: payload.start_date,
          tanggal_selesai: payload.end_date,
          jabatan: payload.jabatan,
          durasi_hari: dayjs(payload.end_date).diff(payload.start_date, 'day'),
          bagassus_file: {
            createMany: {
              data: [
                { ...uploadedSprin, type: 'sprin', updated_at: new Date() },
                { ...uploadedKep, type: 'kep', updated_at: new Date() },
              ],
            },
          },
        },
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };
      console.log(file);

      return {
        ...file,
        key: uploaded.Key,
        url: uploaded.Location,
        filename: uploaded.filename,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (err) {
      throw err;
    }
  }
}
