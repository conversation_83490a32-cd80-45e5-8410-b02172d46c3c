import { BadRequestException, Injectable } from '@nestjs/common';
import { bagassus_report_status_enum } from '@prisma/client';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CONSTANT_LOG } from 'src/core/constants/log.constant';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from 'src/core/enums/log.enum';
import { ConstantLogType } from 'src/core/interfaces/log.type';
import { translateDotNotation } from 'src/core/utils/common.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import {
  buildPaginatedResponse,
  buildResponse,
} from 'src/core/utils/response.util';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  BagassusPersonelCreateDto,
  BagassusPersonelGetListDto,
} from '../dto/bagassus-personel.dto';

@Injectable()
export class BagassusPersonelService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
    private readonly minioService: MinioService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
    query: BagassusPersonelGetListDto,
  ) {
    const { q, end_date, start_date } = query;
    const { sort_column, sort_desc } = searchandsortData;

    const limit = Number(paginationData.limit || 100);
    const page = Number(paginationData.page || 1);

    const where = [`bpr.personel_id = ${req.user?.personel_id}`];
    if (start_date) where.push(`bpr.created_at >= ${start_date}`);
    if (end_date) where.push(`bpr.created_at >= ${end_date}`);
    if (q) where.push(`bprf.filename ILIKE '%${q}%'`);

    let sort = 'bpr.id ASC';
    if (sort_column) {
      sort = `bpr.${sort_column} ${sort_desc || 'ASC'}`;
    }

    const [total, data] = await this.prisma.$transaction([
      this.prisma.$executeRawUnsafe<any>(`
            SELECT COUNT(*) FROM bagassus_personel_report bpr
            INNER JOIN "bagassus_personel_report_file" bprf ON bpr.id = bprf.bagassus_personel_report_id
            WHERE ${where.join(' AND ')}
          `),
      this.prisma.$queryRawUnsafe<Array<Record<string, any>>>(`
            SELECT 
              bpr.id,
              bpr.triwulan,
              bpr.tahun,
              bpr.status,
              bpr.created_at,
              bprf.filename,
              i.nama AS "instansi.nama"
            FROM bagassus_personel_report bpr
              INNER JOIN "bagassus_personel_report_file" bprf ON bpr.id = bprf.bagassus_personel_report_id
              INNER JOIN "bagassus_personel" bp ON bp.id = bpr.bagassus_personel_id
              INNER JOIN "penugasan_instansi" i ON i.id = bp.instansi_id
            WHERE ${where.join(' AND ')}
            ORDER BY ${sort}
            LIMIT ${limit} OFFSET ${limit * (page - 1)}
          `),
    ]);

    await Promise.all(
      data.map(async (d) => {
        d.report_file = {
          filename: d.filename,
          url: await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}/${d.filename}`,
          ),
        };
      }),
    );
    console.dir(
      {
        total,
        query: await this.prisma.$executeRawUnsafe(`
            SELECT COUNT(*) FROM bagassus_personel_report bpr
            INNER JOIN "bagassus_personel_report_file" bprf ON bpr.id = bprf.bagassus_personel_report_id
            WHERE ${where.join(' AND ')}
          `),
      },
      { depth: null },
    );

    const queryResult = data.map((item) => translateDotNotation(item));
    const totalData = Number(total || 0);
    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(
      { result: queryResult, page, totalData, totalPage },
      message,
    );
  }

  async createReport(
    req: any,
    payload: BagassusPersonelCreateDto,
    files: Express.Multer.File[],
  ) {
    try {
      const fileReport = files.find((d) => d.fieldname === 'file_report');
      if (!fileReport) {
        throw new BadRequestException('File report wajib diunggah!');
      }

      const ext = fileReport.originalname.split('.').pop();
      if (ext !== 'xlsx') {
        throw new BadRequestException('File report harus berupa Xlsx!');
      }

      const checkBagassusPersonel =
        await this.prisma.bagassus_personel.findFirst({
          where: { id: payload.bagassus_personel_id },
        });
      if (!checkBagassusPersonel) {
        throw new BadRequestException('Data personel tidak ditemukan!');
      }
      // if (checkBagassusPersonel.personel_id !== req.user.personel_id) {
      //   throw new BadRequestException('Data personel ini bukan milik Anda!');
      // }

      const uploadedFile = await this.uploadFile(fileReport);

      const queryResult = await this.prisma.bagassus_personel_report.create({
        data: {
          bagassus_personel_id: checkBagassusPersonel.id,
          personel_id: checkBagassusPersonel.personel_id,
          triwulan: payload.triwulan,
          tahun: String(payload.tahun),
          status: bagassus_report_status_enum.WAITING_APPROVAL,
          bagassus_personel_report_file: {
            createMany: {
              data: [{ ...uploadedFile, updated_at: new Date() }],
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return buildResponse(queryResult, message);
    } catch (err) {
      if (err?.code === 'P2002') {
        throw new BadRequestException(
          'Data triwulan dan tahun sudah dilaporkan!',
        );
      }
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };

      return {
        ...file,
        key: uploaded.Key,
        url: uploaded.Location,
        filename: uploaded.filename,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (err) {
      throw err;
    }
  }

  // async getAllSeleksi(req: any, queries: GetManySeleksiDto) {
  //   try {
  //     const selections = await this.operatorService.getManySeleksi(
  //       req,
  //       queries,
  //     );

  //     for (const selection of selections.data) {
  //       selection['is_qualified'] = true;
  //     }

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.READ_LOG_TYPE,
  //       ConstantLogDataTypeEnum.LIST,
  //       ConstantLogModuleEnum.BAGASSUS_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
  //         message,
  //         selections,
  //       ),
  //     );

  //     return {
  //       statusCode: HttpStatus.OK,
  //       message,
  //       data: selections,
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  // async getOneSeleksi(req: any, seleksiId: number) {
  //   try {
  //     const queryResult = await this.prisma.bagassus_seleksi.findFirst({
  //       where: { id: seleksiId },
  //       select: {
  //         id: true,
  //         title: true,
  //         description: true,
  //         bagassus_seleksi_file_logo: { select: { filename: true, url: true } },
  //         submission_end: true,
  //         tanggal_tahapan_seleksi: {
  //           select: {
  //             tahap: true,
  //             tanggal_mulai: true,
  //             tanggal_selesai: true,
  //           },
  //         },
  //         has_been_ended: true,
  //         created_at: true,
  //       },
  //     });

  //     queryResult['is_qualified'] = true;

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.READ_LOG_TYPE,
  //       ConstantLogDataTypeEnum.LIST,
  //       ConstantLogModuleEnum.BAGASSUS_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
  //         message,
  //         queryResult,
  //       ),
  //     );

  //     return {
  //       statusCode: HttpStatus.OK,
  //       message,
  //       data: queryResult,
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  // async applySeleksi(req, body: ApplyToSeleksiDto) {
  //   try {
  //     const seleksi = await this.prisma.bagassus_seleksi.findFirst({
  //       where: { id: body.seleksi_id },
  //     });

  //     if (!seleksi) {
  //       throw new BadRequestException('Seleksi Tidak Ditemukan!');
  //     }

  //     const application = await this.prisma.bagassus_seleksi_personel.findFirst(
  //       {
  //         where: {
  //           personel_id: req.user.personel_id,
  //           seleksi_id: body.seleksi_id,
  //         },
  //         select: { id: true },
  //       },
  //     );

  //     if (application) {
  //       throw new BadRequestException('Anda Sudah Mendaftar di seleksi ini');
  //     }

  //     const queryResult = await this.prisma.bagassus_seleksi_personel.create({
  //       data: {
  //         personel_id: req.user.personel_id,
  //         seleksi_id: body.seleksi_id,
  //       },
  //     });

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.CREATE_LOG_TYPE,
  //       ConstantLogDataTypeEnum.OBJECT,
  //       ConstantLogModuleEnum.BAGASSUS_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
  //         message,
  //         queryResult,
  //       ),
  //     );

  //     return {
  //       statusCode: HttpStatus.OK,
  //       message,
  //       data: queryResult,
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }
}
