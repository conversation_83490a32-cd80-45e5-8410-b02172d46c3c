import { HttpStatus, Injectable } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { ConstantLogType } from '../../core/interfaces/log.type';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';

@Injectable()
export class FileBagassusService {
  constructor(
    private readonly logsActivityService: LogsActivityService,
    private readonly minioService: MinioService,
    private prisma: PrismaService,
  ) {}

  // async uploadFileSprin(req: any, fileSprin: Express.Multer.File) {
  //   try {
  //     const [uploadedFile] = await this.uploadFiles([fileSprin]);
  //     const insertToDb = await this.prisma.bagassus_sprin_file.create({
  //       data: {
  //         ...uploadedFile.rawFile,
  //         key: uploadedFile.uploaded.Key,
  //         url: uploadedFile.uploaded.Location,
  //         filename: uploadedFile.uploaded.filename,
  //         created_at: new Date(),
  //         updated_at: new Date(),
  //       },
  //     });

  //     const data = {
  //       id: insertToDb.id,
  //       key: uploadedFile.uploaded.Key,
  //       url: uploadedFile.uploaded.Location,
  //       filename: uploadedFile.uploaded.filename,
  //     };

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  //       ConstantLogDataTypeEnum.OBJECT,
  //       ConstantLogModuleEnum.BAGASSUS_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.BAGASSUS_UPLOAD as ConstantLogType,
  //         message,
  //         data,
  //       ),
  //     );

  //     return {
  //       statusCode: HttpStatus.OK,
  //       message: 'Successfully upload sprin file',
  //       data,
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  // async uploadFileKep(req: any, fileKep: Express.Multer.File) {
  //   try {
  //     const [uploadedFile] = await this.uploadFiles([fileKep]);
  //     const insertToDb = await this.prisma.bagassus_kep_file.create({
  //       data: {
  //         ...uploadedFile.rawFile,
  //         key: uploadedFile.uploaded.Key,
  //         url: uploadedFile.uploaded.Location,
  //         filename: uploadedFile.uploaded.filename,
  //         created_at: new Date(),
  //         updated_at: new Date(),
  //       },
  //     });

  //     const data = {
  //       id: insertToDb.id,
  //       key: uploadedFile.uploaded.Key,
  //       url: uploadedFile.uploaded.Location,
  //       filename: uploadedFile.uploaded.filename,
  //     };

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  //       ConstantLogDataTypeEnum.OBJECT,
  //       ConstantLogModuleEnum.BAGASSUS_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.BAGASSUS_UPLOAD as ConstantLogType,
  //         message,
  //         data,
  //       ),
  //     );

  //     return {
  //       statusCode: HttpStatus.OK,
  //       message,
  //       data,
  //     };
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  async uploadFileSeleksi(req: any, file: Express.Multer.File) {
    try {
      const [uploadedFile] = await this.uploadFiles([file]);
      const insertToDb = await this.prisma.bagassus_seleksi_file.create({
        data: {
          ...uploadedFile.rawFile,
          key: uploadedFile.uploaded.Key,
          url: uploadedFile.uploaded.Location,
          filename: uploadedFile.uploaded.filename,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const queryResult = {
        id: insertToDb.id,
        key: uploadedFile.uploaded.Key,
        url: uploadedFile.uploaded.Location,
        filename: uploadedFile.uploaded.filename,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async uploadFiles(files: Express.Multer.File[]) {
    try {
      const uploadedFiles = await Promise.all(
        files.map(async (file) => {
          const uploaded = await this.minioService.uploadFile(file);
          delete file.buffer;
          delete file.fieldname;

          if (!uploaded.ETag) return { rawFile: file };

          return {
            rawFile: file,
            uploaded,
          };
        }),
      );

      return uploadedFiles;
    } catch (err) {
      throw err;
    }
  }
}
