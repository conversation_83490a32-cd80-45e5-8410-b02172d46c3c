import { BadRequestException, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CONSTANT_LOG } from 'src/core/constants/log.constant';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from 'src/core/enums/log.enum';
import { ConstantLogType } from 'src/core/interfaces/log.type';
import { translateDotNotation } from 'src/core/utils/common.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import {
  buildPaginatedResponse,
  buildResponse,
} from 'src/core/utils/response.util';
import {
  BagassusPenugasanCreateDto,
  BagassusPenugasanExtendDto,
  BagassusPenugasanGetListDto,
} from '../dto/bagassus-penugasan.dto';

@Injectable()
export class BagassusPenugasanService {
  constructor(
    private readonly logsActivityService: LogsActivityService,
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
    query: BagassusPenugasanGetListDto,
  ) {
    const { q, pangkat_id, instansi_id, type } = query;
    const { sort_column, sort_desc } = searchandsortData;

    const limit = Number(paginationData.limit || 100);
    const page = Number(paginationData.page || 1);

    const where = [`bp.type = '${type}'`];
    if (instansi_id) where.push(`bp.instansi_id = ${instansi_id}`);
    if (q) where.push(`p.nama_lengkap ILIKE '%${q}%'`);
    if (pangkat_id) where.push(`pt.pangkat_id = ${pangkat_id}`);

    let sort = 'bp.id ASC';
    if (sort_column) {
      sort = `bp.${sort_column} ${sort_desc || 'ASC'}`;
    }

    const [total, data] = await this.prisma.$transaction([
      this.prisma.$executeRawUnsafe(`
        SELECT COUNT(*) FROM bagassus_personel bp
        INNER JOIN "personel" p ON p.id = bp.personel_id
        INNER JOIN "mv_pangkat_terakhir" pt ON pt.personel_id = p.id
        WHERE ${where.join(' AND ')}
      `),
      this.prisma.$queryRawUnsafe<Array<Record<string, any>>>(`
        SELECT 
          bp.id,
          bp.type,
          bp.jabatan,
          bp.tanggal_mulai,
          bp.tanggal_selesai,
          bp.durasi_hari,
          i.nama AS "instansi.nama",
          p.nama_lengkap AS "personel.nama",
          p.nrp AS "personel.nrp",
          p.jenis_kelamin AS "personel.jenis_kelamin",
          sa.nama AS "personel.status_aktif",
          ja.nama AS "personel.jabatan",
          pa.id AS "personel.pangkat_id",
          pa.nama AS "personel.pangkat",
          pa.nama_singkat AS "personel.pangkat_singkat"
        FROM bagassus_personel bp
          INNER JOIN "penugasan_instansi" i ON i.id = bp.instansi_id
          INNER JOIN "personel" p ON p.id = bp.personel_id
          INNER JOIN "status_aktif" sa ON sa.id = p.status_aktif_id
          INNER JOIN "mv_jabatan_terakhir" jt ON jt.personel_id = p.id
          INNER JOIN "jabatan" ja ON ja.id = jt.jabatan_id
          INNER JOIN "mv_pangkat_terakhir" pt ON pt.personel_id = p.id
          INNER JOIN "pangkat" pa ON pa.id = pt.pangkat_id
        WHERE ${where.join(' AND ')}
        ORDER BY ${sort}
        LIMIT ${limit} OFFSET ${limit * (page - 1)}
      `),
    ]);

    data.forEach((d) => {
      d.remaining_days = Math.min(
        Math.max(dayjs(d.tanggal_selesai).diff(new Date(), 'day'), 0),
        d.durasi_hari,
      );
    });

    const queryResult = data.map((item) => translateDotNotation(item));
    const totalData = Number(total || 0);
    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(
      { result: queryResult, page, totalData, totalPage },
      message,
    );
  }

  async getListPersonel(req: any) {
    const queryResult = await this.prisma.$queryRawUnsafe<
      Array<Record<string, any>>
    >(`
      SELECT
        bp.id,
        bp.tanggal_mulai,
        bp.tanggal_selesai,
        i.nama AS "instansi_nama"
      FROM bagassus_personel bp
        INNER JOIN "penugasan_instansi" i ON i.id = bp.instansi_id
      WHERE bp.personel_id = ${519418}
      ORDER BY i.nama
    `);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async getDetail(req: any, id: number) {
    const active = await this.prisma.bagassus_personel.findFirst({
      where: { id },
      include: {
        penugasan_instansi: true,
        personel: {
          select: {
            jenis_kelamin: true,
            nrp: true,
            foto_file: true,
            mv_pangkat_terakhir: {
              select: {
                pangkat: true,
              },
            },
          },
        },
      },
    });

    if (!active) {
      throw new BadRequestException('Data tidak ditemukan!');
    }

    active['status_penugasan'] = 'Aktif';
    if (dayjs(active.tanggal_selesai).isBefore(new Date())) {
      active['status_penugasan'] = 'Selesai';
    }

    if (active.personel.foto_file) {
      active.personel.foto_file = await this.minioService.checkFileExist(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${active.personel.nrp}/${active.personel.foto_file}`,
      );
    }

    const history = await this.prisma.bagassus_personel.findMany({
      where: { personel_id: active.personel_id, id: { not: active.id } },
      select: {
        id: true,
        tanggal_mulai: true,
        tanggal_selesai: true,
        durasi_hari: true,
        jabatan: true,
        bagassus_file: {
          select: { filename: true, type: true, url: true },
        },
        bagassus_personel_report: {
          include: {
            bagassus_personel_report_file: {
              select: { filename: true, url: true },
            },
          },
        },
        penugasan_instansi: true,
      },
      orderBy: { tanggal_selesai: 'desc' },
    });

    await Promise.all(
      history.map(async (d) => {
        await Promise.all(
          d.bagassus_file.map(async (f) => {
            f.url = await this.minioService.checkFileExist(
              process.env.MINIO_BUCKET_NAME!,
              `${process.env.MINIO_PATH_FILE}/${f.filename}`,
            );
          }),
        );

        for (const report of d.bagassus_personel_report) {
          await Promise.all(
            report.bagassus_personel_report_file.map(async (f) => {
              f.url = await this.minioService.checkFileExist(
                process.env.MINIO_BUCKET_NAME!,
                `${process.env.MINIO_PATH_FILE}/${f.filename}`,
              );
            }),
          );
        }
      }),
    );

    const queryResult = {
      ...active,
      history,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async extendPenugasan(
    req: any,
    id: number,
    payload: BagassusPenugasanExtendDto,
    files: Express.Multer.File[],
  ) {
    payload.start_date = new Date(payload.start_date);
    payload.end_date = new Date(payload.end_date);

    const fileSprin = files.find((d) => d.fieldname === 'file_sprin');
    const fileKep = files.find((d) => d.fieldname === 'file_kep');

    if (!fileSprin) throw new BadRequestException('File sprin wajib diunggah!');
    if (!fileKep) throw new BadRequestException('File kep wajib diunggah!');

    const check = await this.prisma.bagassus_personel.findFirst({
      where: { id },
    });

    if (!check) throw new BadRequestException('Data tidak ditemukan!');

    if (dayjs(payload.start_date).isBefore(check.tanggal_selesai)) {
      throw new BadRequestException(
        'Tanggal mulai perpanjangan harus sama dengan atau lebih besar dari Tanggal Berakhir penugasan saat ini',
      );
    }

    const [uploadedSprin, uploadedKep] = await Promise.all([
      this.uploadFile(fileSprin),
      this.uploadFile(fileKep),
    ]);

    const queryResult = await this.prisma.bagassus_personel.create({
      data: {
        personel_id: check.personel_id,
        instansi_id: payload.instansi_id,
        type: check.type,
        tanggal_mulai: payload.start_date,
        tanggal_selesai: payload.end_date,
        jabatan: payload.jabatan,
        durasi_hari: dayjs(payload.end_date).diff(payload.start_date, 'day'),
        bagassus_file: {
          createMany: {
            data: [
              { ...uploadedSprin, type: 'sprin', updated_at: new Date() },
              { ...uploadedKep, type: 'kep', updated_at: new Date() },
            ],
          },
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async createPenugasan(
    req: any,
    payload: BagassusPenugasanCreateDto,
    files: Express.Multer.File[],
  ) {
    payload.start_date = new Date(payload.start_date);
    payload.end_date = new Date(payload.end_date);

    const fileSprin = files.find((d) => d.fieldname === 'file_sprin');
    const fileKep = files.find((d) => d.fieldname === 'file_kep');

    if (!fileSprin) throw new BadRequestException('File sprin wajib diunggah!');
    if (!fileKep) throw new BadRequestException('File kep wajib diunggah!');

    const check = await this.prisma.bagassus_personel.findFirst({
      where: {
        personel_id: payload.personel_id,
        tanggal_selesai: { gte: new Date() },
      },
    });

    if (check) {
      throw new BadRequestException('Personel sedang dalam penugasan lainnya!');
    }
    const [uploadedSprin, uploadedKep] = await Promise.all([
      this.uploadFile(fileSprin),
      this.uploadFile(fileKep),
    ]);

    const queryResult = await this.prisma.bagassus_personel.create({
      data: {
        personel_id: payload.personel_id,
        instansi_id: payload.instansi_id,
        type: payload.type,
        tanggal_mulai: payload.start_date,
        tanggal_selesai: payload.end_date,
        jabatan: payload.jabatan,
        durasi_hari: dayjs(payload.end_date).diff(payload.start_date, 'day'),
        bagassus_file: {
          createMany: {
            data: [
              { ...uploadedSprin, type: 'sprin', updated_at: new Date() },
              { ...uploadedKep, type: 'kep', updated_at: new Date() },
            ],
          },
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };
      console.log(file);

      return {
        ...file,
        key: uploaded.Key,
        url: uploaded.Location,
        filename: uploaded.filename,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (err) {
      throw err;
    }
  }
}
