import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { StatusPenugasanEnum } from '../../core/enums/bagassus.enum';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { ConstantLogType } from '../../core/interfaces/log.type';
import {
  _calculateSisaWaktu,
  getDayDifference,
} from '../../core/utils/common.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  CreateDraftPenugasanDto,
  CreatePenugasanDto,
  CreateSinglePenugasanDto,
  DraftBulkPenugasanDto,
  GetManyDraftBulkPenugasanDto,
  GetManyPenugasan,
  KeywordSearchDto,
  SearchPersonelDto,
  UpdateDraftPersonelDto,
} from '../dto/bagassus.dto';

@Injectable()
export class BagassusOperatorService {
  constructor(
    private readonly logsActivityService: LogsActivityService,
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
  ) {}

  async createPenugasan(req: any, body: CreatePenugasanDto) {
    try {
      const preInsertData = await Promise.all(
        body.data.map(async (data) => {
          const tanggalMulai = new Date(data.start_date);
          const tanggalSelesai = new Date(data.end_date);
          const personelID = Number(data.personel_id);
          await this._validateIsActivePenugasan(personelID);

          const [fileSprint, fileKep] = await Promise.all([
            this.uploadFile(data.file_sprint),
            this.uploadFile(data.file_kep),
          ]);

          return {
            personel_id: personelID,
            instansi_id: data.instansi_id,
            type: data.type,
            tanggal_mulai: tanggalMulai,
            tanggal_selesai: tanggalSelesai,
            durasi_hari: getDayDifference(tanggalMulai, tanggalSelesai),
            jabatan: data.jabatan,
            sprin_file: { create: { data: { ...fileSprint, type: 'sprin' } } },
            kep_file: { create: { data: { ...fileKep, type: 'kep' } } },
            created_at: new Date(),
            updated_at: new Date(),
          };
        }),
      );

      const queryResult = await this.prisma.bagassus_personel.createMany({
        data: preInsertData,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async createDraftBulkPenugasan(
    jsonData: DraftBulkPenugasanDto[],
    payload: CreateDraftPenugasanDto,
    req: any,
  ) {
    try {
      this._checkUniqueNRP(jsonData);

      const [personelDict, instansiDict] = await Promise.all([
        this._createPersonelDictionary(jsonData),
        this._createInstansiDictionary(jsonData),
      ]);

      const dataWithPersonelDetails = jsonData.map((data) => {
        const personelData = personelDict[data.NRP];
        const { id: instansiID, nama: instansiNama } =
          instansiDict[data['ID Instansi']];

        const finalData = {
          no_excel: +data.No,
          nama_personel: personelData.nama_lengkap,
          personel_id: Number(personelData.id),
          nrp: personelData.nrp,
          gender: personelData.jenis_kelamin,
          instansi_id: Number(instansiID),
          instansi_name: instansiNama,
          location_type_id: payload.location_type,
          nama_jabatan: data['Nama Jabatan'],
          tanggal_mulai: data['Tanggal Mulai'],
          tanggal_selesai: data['Tanggal Selesai'],
          created_by_id: req.user.personel_id,
          created_at: new Date(),
          updated_at: new Date(),
        };

        return finalData;
      });

      const data = await this.prisma.bagassus_personel_draft.createMany({
        data: dataWithPersonelDetails,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data,
      };
    } catch (err) {
      throw err;
    }
  }

  async findDraftBulkPenugasan(
    req: any,
    queries: GetManyDraftBulkPenugasanDto,
  ) {
    const creator_id = Number(req['user']['personel_id']);

    try {
      const { page = 1, limit = 10 } = queries;

      const filterByKeyword = {};
      if (queries.q) {
        filterByKeyword['OR'] = [
          { nrp: { startsWith: queries.q } },
          { nama_personel: { startsWith: queries.q } },
        ];
      }

      const [draft, count] = await Promise.all([
        this.prisma.bagassus_personel_draft.findMany({
          where: { created_by_id: creator_id, ...filterByKeyword },
          select: {
            id: true,
            no_excel: true,
            nama_personel: true,
            nrp: true,
            gender: true,
            instansi_name: true,
            tanggal_mulai: true,
            tanggal_selesai: true,
          },
          orderBy: { no_excel: 'asc' },
          take: limit,
          skip: (page - 1) * limit,
        }),
        this.prisma.bagassus_personel_draft.count({
          where: { created_by_id: creator_id, ...filterByKeyword },
        }),
      ]);

      const queryResult = {
        drafts: draft,
        page,
        totalPage: Math.ceil(count / limit),
        totalData: count,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page,
        totalData: queryResult.totalData,
        totalPage: queryResult.totalPage,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateDraftPersonel(req: any, payload: UpdateDraftPersonelDto) {
    const creator_id = req['user'].personel_id;

    try {
      const draft = await this.prisma.bagassus_personel_draft.findFirst({
        where: { id: payload.id, created_by_id: creator_id },
        select: { id: true },
      });

      if (!draft) {
        throw new NotFoundException(`Draft tidak ditemukan!`);
      }

      const data = await this.prisma.bagassus_personel_draft.update({
        where: { id: draft.id },
        data: {
          sprin_file_id: payload?.file_sprin_id,
          kep_file_id: payload?.file_kep_id,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_UPDATE as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data,
      };
    } catch (err) {
      throw err;
    }
  }

  async removeDraftPersonel(req: any, payload: UpdateDraftPersonelDto) {
    const creator_id: number = req['user'].personel_id;
    try {
      const draft = await this.prisma.bagassus_personel_draft.findFirst({
        where: { id: payload.id, created_by_id: creator_id },
        select: { id: true },
      });

      if (!draft) {
        throw new NotFoundException(`Draft tidak ditemukan!`);
      }

      const data = await this.prisma.bagassus_personel_draft.delete({
        where: { id: payload.id, created_by_id: creator_id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_DELETE as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data,
      };
    } catch (err) {
      throw err;
    }
  }

  async publishDraftPenugasan(req: any) {
    // const creator_id = req['user'].personel_id;

    try {
      // const drafts = await this.prisma.bagassus_personel_draft.findMany({
      //   where: { created_by_id: creator_id },
      // });

      // const penugasanPayload: CreateSinglePenugasanDto[] = drafts.map(
      //   (draft) => {
      //     const fileSprinExist = draft?.sprin_file_id;
      //     if (!fileSprinExist) {
      //       const errMsg = `Publish draft gagal! Personel NRP ${draft.nrp} belum upload file Sprin!`;
      //       throw new BadRequestException(errMsg);
      //     }

      //     return {
      //       personel_id: draft.personel_id,
      //       jabatan_name: draft.nama_jabatan,
      //       location_type: draft.location_type_id,
      //       instansi_id: draft.instansi_id,
      //       start_date: draft.tanggal_mulai,
      //       end_date: draft.tanggal_selesai,
      //       file_sprin_id: Number(draft.sprin_file_id),
      //       file_kep_id: Number(draft?.kep_file_id) || undefined,
      //     };
      //   },
      // );

      // const data = await this.createPenugasan(req, { data: penugasanPayload });
      const data = {};

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          data,
        ),
      );

      return { message, data };
    } catch (err) {
      throw err;
    }
  }

  async removeAllDraftPenugasan(req: any) {
    const creator_id = req['user'].personel_id;

    try {
      const data = await this.prisma.bagassus_personel_draft.deleteMany({
        where: { created_by_id: creator_id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_DELETE as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllActivePenugasan(
    req: any,
    queries: GetManyPenugasan,
    paginationData: PaginationDto,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const filter = { location_type_id: queries.loc_type };
      if (queries.instansi_id) filter['instansi_id'] = queries.instansi_id;
      if (queries.pangkat_id) {
        filter['personel'] = {
          pangkat_personel: { some: { pangkat_id: queries.pangkat_id } },
        };
      }

      const findManyQuery = this._getManyPenugasanQueryBuilder(
        queries,
        paginationData,
      );

      const countQuery = this._countDataPenugasanQueryBuilder(queries);
      const [[{ count: totalData }], data] = await this.prisma.$transaction([
        // this.prisma.bagassus_personel.count({ where: filter }),
        this.prisma.$queryRaw<any[]>(countQuery),
        this.prisma.$queryRaw<any[]>(findManyQuery),
      ]);

      const totalPage = Math.ceil(Number(totalData) / limit);
      const result = data.map((data) => ({
        personel_id: Number(data.personel_id),
        uid: data.uid,
        fullname: data.nama_lengkap,
        nrp: data.nrp,
        gender: data.jenis_kelamin,
        // status: data.status,
        status: this._getStatusPenugasan(new Date(data.tanggal_selesai)),
        location: data.lokasi,
        instansi: data.instansi,
        start_dt: new Date(data.tanggal_mulai),
        end_dt: new Date(data.tanggal_selesai),
        duration_days: Number(data.durasi_hari),
        remaining_days: _calculateSisaWaktu(new Date(data.tanggal_selesai)),
      }));

      const queryResult = { penugasan: result, page, totalPage, totalData };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult.penugasan,
        totalPage,
        totalData,
        page,
      };
    } catch (err) {
      throw err;
    }
  }

  async findPersonelHistory(req: any, personelId: number) {
    try {
      const history = await this.prisma.bagassus_personel.findMany({
        where: {
          personel_id: personelId,
        },
        select: {
          durasi_hari: true,
          tanggal_mulai: true,
          tanggal_selesai: true,
          bagassus_file: true,
          penugasan_instansi: true,
        },
        orderBy: { id: 'desc' },
      });

      const result = history.map((data) => ({
        instansi: data.penugasan_instansi.nama,
        durasi_hari: data.durasi_hari,
        tanggal_mulai: new Date(data.tanggal_mulai),
        tanggal_selesai: new Date(data.tanggal_selesai),
        bagassus_sprin_file: data.bagassus_file.find((d) => d.type === 'sprin'),
        bagassus_kep_file: data.bagassus_file.find((d) => d.type === 'kep'),
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          result,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: result,
      };
    } catch (err) {
      throw err;
    }
  }

  async getListInstansi(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.penugasan_instansi.count({
        where: where,
      }),
      this.prisma.penugasan_instansi.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get list instansi',
      data: queryResult,
      totalPage,
      totalData,
      page,
    };
  }

  async findPersonelHistoryByUid(req: any, personelUid: string) {
    try {
      const data = await this.prisma.bagassus_personel.findMany({
        where: {
          personel: {
            uid: personelUid,
          },
        },
        select: {
          personel: {
            select: {
              mv_jabatan_terakhir: {
                select: {
                  jabatan: true,
                },
              },
            },
          },
          penugasan_instansi: true,
        },
        orderBy: { id: 'desc' },
      });

      const queryResult = data.map((item) => {
        return {
          nama_jabatan:
            item.personel.mv_jabatan_terakhir?.[0]?.jabatan?.[0]?.['nama'],
          instansi: item.penugasan_instansi.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  // async extendPenugasan(body: CreateSinglePenugasanDto) {
  //   try {
  //     const [latest] = await this.prisma.bagassus_personel.findMany({
  //       where: {
  //         personel_id: +body.personel_id,
  //       },
  //       orderBy: { id: 'desc' },
  //       take: 1,
  //     });
  //
  //     if (new Date(body.start_date) < new Date(latest.tanggal_selesai)) {
  //       throw new BadRequestException(
  //         `Perpanjangan Penugasan gagal! Tanggal mulai perpanjangan harus sama dengan atau lebih besar dari Tanggal Berakhir penugasan saat ini`,
  //       );
  //     }
  //
  //     const tanggalMulai = new Date(body.start_date);
  //     const tanggalSelesai = new Date(body.end_date);
  //     const extendedPenugasan = await this.prisma.bagassus_personel.create({
  //       data: {
  //         nama_jabatan: latest.nama_jabatan,
  //         personel_id: Number(latest.personel_id),
  //         instansi_id: latest.instansi_id,
  //         location_type_id: latest.location_type_id,
  //         tanggal_mulai: tanggalMulai,
  //         tanggal_selesai: tanggalSelesai,
  //         durasi_hari: getDayDifference(tanggalMulai, tanggalSelesai),
  //         sprin_file_id: body.file_sprin_id,
  //         kep_file_id: body.file_kep_id,
  //         created_at: new Date(),
  //         updated_at: new Date(),
  //       },
  //     });
  //
  //     return extendedPenugasan;
  //   } catch (err) {
  //     throw err;
  //   }
  // }

  async extendPenugasanV2(req: any, body: CreateSinglePenugasanDto) {
    try {
      const [latest] = await this.prisma.bagassus_personel.findMany({
        where: {
          personel_id: +body.personel_id,
        },
        select: { tanggal_selesai: true, personel_id: true },
        orderBy: { id: 'desc' },
        take: 1,
      });

      if (new Date(body.start_date) < new Date(latest.tanggal_selesai)) {
        throw new BadRequestException(
          `Perpanjangan Penugasan gagal! Tanggal mulai perpanjangan harus sama dengan atau lebih besar dari Tanggal Berakhir penugasan saat ini`,
        );
      }

      const tanggalMulai = new Date(body.start_date);
      const tanggalSelesai = new Date(body.end_date);
      const [fileSprint, fileKep] = await Promise.all([
        this.uploadFile(body.file_sprint),
        this.uploadFile(body.file_kep),
      ]);

      const data = await this.prisma.bagassus_personel.create({
        data: {
          personel_id: Number(latest.personel_id),
          instansi_id: body.instansi_id,
          type: body.type,
          tanggal_mulai: tanggalMulai,
          tanggal_selesai: tanggalSelesai,
          durasi_hari: getDayDifference(tanggalMulai, tanggalSelesai),
          jabatan: body.jabatan,
          bagassus_file: {
            createMany: {
              data: [
                { ...fileSprint, type: 'sprin', updated_at: new Date() },
                { ...fileKep, type: 'kep', updated_at: new Date() },
              ],
            },
          },
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_CREATE as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data,
      };
    } catch (err) {
      throw err;
    }
  }

  async findInstansiKeywordSuggestion(req: any, queries: KeywordSearchDto) {
    try {
      const { q: keyword, limit } = queries;

      const filter = {};
      if (keyword) {
        const uppercase = keyword.toUpperCase();
        const lowercase = keyword.toLowerCase();
        filter['where'] = {
          OR: [
            { nama: { gte: lowercase, lte: lowercase + 'ÿ' } },
            { nama: { gte: uppercase, lte: uppercase + 'ÿ' } },
          ],
        };
      }

      const queryResult = await this.prisma.penugasan_instansi.findMany({
        ...filter,
        select: { nama: true, id: true },
        orderBy: { nama: 'asc' },
        take: +limit || 5,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findSingleActivePenugasan(req: any, personelId: number) {
    try {
      const personelData = await this.prisma.personel.findFirst({
        where: { id: personelId },
        select: {
          nama_lengkap: true,
          jenis_kelamin: true,
          nrp: true,
        },
      });

      const pangkatTerkini = await this.prisma.pangkat_personel.findFirst({
        where: { personel_id: personelId },
        select: { pangkat: { select: { nama_singkat: true } } },
        orderBy: { tmt: 'desc' },
        take: 1,
      });

      const penugasan = await this.prisma.bagassus_personel.findFirst({
        where: { personel_id: personelId },
        orderBy: { id: 'desc' },
        select: {
          tanggal_mulai: true,
          tanggal_selesai: true,
          durasi_hari: true,
          penugasan_instansi: true,
          personel: {
            select: {
              mv_jabatan_terakhir: {
                select: {
                  jabatan: true,
                },
              },
              status_aktif: {
                select: {
                  nama: true,
                },
              },
            },
          },
        },
      });

      const sisaWaktuPenugasan = getDayDifference(
        new Date(),
        new Date(penugasan.tanggal_selesai),
      );

      const queryResult = {
        personel_id: personelId,
        nama_lengkap: personelData.nama_lengkap,
        nrp: personelData.nrp,
        pangkat: pangkatTerkini.pangkat.nama_singkat,
        gender: personelData.jenis_kelamin,
        instansi: penugasan.penugasan_instansi.nama,
        jabatan:
          penugasan.personel?.mv_jabatan_terakhir?.[0]?.jabatan?.['nama'],
        status: penugasan.personel?.status_aktif?.nama,
        tanggal_mulai: penugasan.tanggal_mulai,
        tanggal_selesai: penugasan.tanggal_selesai,
        durasi_penugasan_hari: penugasan.durasi_hari,
        sisa_durasi_penugasan_hari: sisaWaktuPenugasan,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async searchPersonel(req: any, queries: SearchPersonelDto) {
    try {
      const personel = await this.prisma.personel.findFirst({
        where: { nrp: queries.nrp },
      });

      if (!personel) {
        throw new NotFoundException(
          `Personel NRP ${queries.nrp} tidak ditemukan`,
        );
      }

      const latestPangkatPersonel =
        await this.prisma.pangkat_personel.findFirst({
          where: { personel_id: personel.id },
          select: {
            pangkat: { select: { nama_singkat: true } },
          },
          orderBy: { tmt: 'desc' },
        });

      const queryResult = Object.assign(personel, {
        pangkat_terkini: latestPangkatPersonel.pangkat.nama_singkat,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findPangkatKeywordSuggestion(req: any, queries: KeywordSearchDto) {
    try {
      const { q: keyword, limit } = queries;

      const filter = {};
      if (keyword) {
        const uppercase = keyword.toUpperCase();
        const lowercase = keyword.toLowerCase();
        filter['where'] = {
          OR: [
            { nama: { gte: lowercase, lte: lowercase + 'ÿ' } },
            { nama: { gte: uppercase, lte: uppercase + 'ÿ' } },
          ],
        };
      }

      const queryResult = await this.prisma.pangkat.findMany({
        ...filter,
        select: { nama: true, id: true },
        orderBy: { nama: 'asc' },
        take: +limit || 5,
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGASSUS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGASSUS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private _getManyPenugasanQueryBuilder(
    queries: GetManyPenugasan,
    paginationData: PaginationDto,
  ) {
    const { loc_type, instansi_id, pangkat_id } = queries;
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const filters = {
      instansi: Prisma.sql`WHERE id = ${instansi_id}`,
      pangkat: {
        cte: Prisma.sql`
            WITH pangkat_personel AS (SELECT p.nama_singkat,
                                             pp.personel_id,
                                             pp.pangkat_id,
                                             ROW_NUMBER() OVER(PARTITION BY pp.personel_id ORDER BY pp.tmt DESC) AS pangkat_ranking
                                      FROM (SELECT personel_id, pangkat_id, tmt FROM "pangkat_personel") pp
                                               JOIN "pangkat" p ON p.id = pp.pangkat_id)`,
        cteV2: Prisma.sql`
        ,pangkat_personel AS (
          SELECT 
            p.nama_singkat,
            pp.personel_id,
            pp.pangkat_id,
            ROW_NUMBER() OVER(PARTITION BY pp.personel_id ORDER BY pp.tmt DESC) AS pangkat_ranking
          FROM ( SELECT personel_id, pangkat_id, tmt FROM "pangkat_personel" ) pp
          JOIN "pangkat" p ON p.id = pp.pangkat_id
        )`,
        filter: Prisma.sql`JOIN ( SELECT * FROM "pangkat_personel" WHERE pangkat_ranking = 1 AND pangkat_id = ${pangkat_id}) pp ON pp.personel_id = pe.id`,
      },
      personel: Prisma.sql`WHERE pe.nama_lengkap ilike ${queries.q + '%'} OR pe.nrp = ${queries.q} `,
    };

    const queryTemplate: any = Prisma.sql`
        WITH latest_penugasan AS (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY personel_id ORDER BY tanggal_selesai DESC) AS rn
                                  FROM bagassus_personel)
            ${pangkat_id ? filters.pangkat.cteV2 : Prisma.empty}
        SELECT p.*,
               bp.*,
               i.nama as instansi,
               l.nama as lokasi
        FROM (SELECT *
              FROM "latest_penugasan"
              WHERE rn = 1) AS bp
                 JOIN (SELECT pe.id as personel_id, pe.nama_lengkap, pe.nrp, pe.jenis_kelamin, pe.uid
                       FROM "personel" pe
                           ${queries.q ? filters.personel : Prisma.empty} ${pangkat_id ? filters.pangkat.filter : Prisma.empty}) p
                      on p.personel_id = bp.personel_id
                 JOIN (SELECT id, nama
                       from "penugasan_instansi" ${instansi_id ? filters.instansi : Prisma.empty}) i
                      ON i.id = bp.instansi_id
                 JOIN (SELECT id, nama
                       from "bagassus_location_type"
                       WHERE id = ${loc_type}) l ON l.id = bp.location_type_id
        ORDER BY bp.updated_at DESC
            LIMIT ${limit}
        OFFSET ${limit * (page - 1)}
    `;

    return queryTemplate;
  }

  private _countDataPenugasanQueryBuilder(queries: GetManyPenugasan) {
    const { loc_type, instansi_id, pangkat_id } = queries;

    const filters = {
      instansi: Prisma.sql`WHERE id = ${instansi_id}`,
      pangkat: {
        cte: Prisma.sql`
            WITH pangkat_personel AS (SELECT p.nama_singkat,
                                             pp.personel_id,
                                             pp.pangkat_id,
                                             ROW_NUMBER() OVER(PARTITION BY pp.personel_id ORDER BY pp.tmt DESC) AS pangkat_ranking
                                      FROM (SELECT personel_id, pangkat_id, tmt FROM "pangkat_personel") pp
                                               JOIN "pangkat" p ON p.id = pp.pangkat_id)`,
        cteV2: Prisma.sql`
        ,pangkat_personel AS (
          SELECT 
            p.nama_singkat,
            pp.personel_id,
            pp.pangkat_id,
            ROW_NUMBER() OVER(PARTITION BY pp.personel_id ORDER BY pp.tmt DESC) AS pangkat_ranking
          FROM ( SELECT personel_id, pangkat_id, tmt FROM "pangkat_personel" ) pp
          JOIN "pangkat" p ON p.id = pp.pangkat_id
        )`,
        filter: Prisma.sql`JOIN ( SELECT * FROM "pangkat_personel" WHERE pangkat_ranking = 1 AND pangkat_id = ${pangkat_id}) pp ON pp.personel_id = pe.id`,
      },
      personel: Prisma.sql`WHERE pe.nama_lengkap ilike ${queries.q + '%'} OR pe.nrp = ${queries.q} `,
    };

    const queryTemplate: any = Prisma.sql`
        WITH latest_penugasan AS (SELECT *,
                                         ROW_NUMBER() OVER (PARTITION BY personel_id ORDER BY tanggal_selesai DESC) AS rn
                                  FROM bagassus_personel)
            ${pangkat_id ? filters.pangkat.cteV2 : Prisma.empty}
        SELECT COUNT(bp.personel_id)
        FROM (SELECT *
              FROM "latest_penugasan"
              WHERE rn = 1) AS bp
                 JOIN (SELECT pe.id as personel_id, pe.nama_lengkap, pe.nrp, pe.jenis_kelamin, pe.uid
                       FROM "personel" pe
                           ${queries.q ? filters.personel : Prisma.empty} ${pangkat_id ? filters.pangkat.filter : Prisma.empty}) p
                      on p.personel_id = bp.personel_id
                 JOIN (SELECT id, nama
                       from "penugasan_instansi" ${instansi_id ? filters.instansi : Prisma.empty}) i
                      ON i.id = bp.instansi_id
                 JOIN (SELECT id, nama
                       from "bagassus_location_type"
                       WHERE id = ${loc_type}) l ON l.id = bp.location_type_id
    `;

    return queryTemplate;
  }

  // private _countDataPenugasanQueryBuilder(queries: GetManyPenugasan) {
  //   const { loc_type, instansi_id, pangkat_id } = queries;
  //   const filters = {
  //     instansi: Prisma.sql`WHERE id = ${instansi_id}`,
  //     pangkat: {
  //       cte: Prisma.sql`
  //       WITH pangkat_personel AS (
  //         SELECT
  //           p.nama_singkat,
  //           pp.personel_id,
  //           pp.pangkat_id,
  //           ROW_NUMBER() OVER(PARTITION BY pp.personel_id ORDER BY pp.tmt DESC) AS pangkat_ranking
  //         FROM ( SELECT personel_id, pangkat_id, tmt FROM "pangkat_personel" ) pp
  //         JOIN "pangkat" p ON p.id = pp.pangkat_id
  //       )`,
  //       filter: Prisma.sql`JOIN ( SELECT * FROM "pangkat_personel" WHERE pangkat_ranking = 1 AND pangkat_id = ${pangkat_id}) pp ON pp.personel_id = pe.id`,
  //     },
  //     personel: Prisma.sql`WHERE pe.nama_lengkap ilike ${queries.q + '%'} OR pe.nrp = ${queries.q} `,
  //   };
  //   const queryTemplate: any = Prisma.sql`
  //     ${pangkat_id ? filters.pangkat.cte : Prisma.empty}
  //     SELECT
  //       COUNT(DISTINCT(p.personel_id))
  //     FROM (
  //       SELECT * FROM "bagassus_personel" WHERE tanggal_selesai >= NOW()
  //     ) AS bp
  //     JOIN (
  //       SELECT pe.id as personel_id, pe.nama_lengkap, pe.nrp, pe.jenis_kelamin, s.nama AS status, pe.uid
  //       FROM "personel" pe
  //       JOIN "status_aktif" s ON s.id = pe.status_aktif_id
  //       ${queries.q ? filters.personel : Prisma.empty}
  //       ${pangkat_id ? filters.pangkat.filter : Prisma.empty}
  //     ) p on p.personel_id = bp.personel_id
  //     JOIN (
  //       SELECT id, nama from "penugasan_instansi" ${instansi_id ? filters.instansi : Prisma.empty}
  //     ) i ON i.id = bp.instansi_id
  //     JOIN (
  //       SELECT id, nama from "bagassus_location_type" WHERE id = ${loc_type}
  //     ) l ON l.id = bp.location_type_id
  //   `;

  //   return queryTemplate;
  // }

  private _getStatusPenugasan(date: Date) {
    if (new Date() > date) {
      return StatusPenugasanEnum.KEMBALI;
    }

    return StatusPenugasanEnum.AKTIF;
  }

  private async _validateIsActivePenugasan(personelId: number) {
    try {
      const penugasan = await this.prisma.bagassus_personel.findFirst({
        where: { personel_id: personelId },
        select: {
          tanggal_selesai: true,
          personel: { select: { nama_lengkap: true } },
        },
        orderBy: { tanggal_selesai: 'desc' },
      });

      if (!penugasan) return true;

      const isActive =
        this._getStatusPenugasan(new Date(penugasan.tanggal_selesai)) ==
        StatusPenugasanEnum.AKTIF;

      if (isActive) {
        throw new BadRequestException(
          `Penugasan personel gagal! ${penugasan.personel.nama_lengkap} sedang dalam penugasan aktif!`,
        );
      }

      return true;
    } catch (err) {
      throw err;
    }
  }

  private async _createPersonelDictionary(jsonData: DraftBulkPenugasanDto[]) {
    try {
      const NRPs = jsonData.map((data) => String(data.NRP));
      const personels = await this.prisma.personel.findMany({
        where: {
          nrp: { in: NRPs },
        },
        select: {
          id: true,
          nama_lengkap: true,
          jenis_kelamin: true,
          nrp: true,
        },
      });

      const personelDict = personels.reduce((final, temp) => {
        final[temp.nrp] = temp;
        return final;
      }, {});

      return personelDict;
    } catch (err) {
      throw err;
    }
  }

  private async _createInstansiDictionary(jsonData: DraftBulkPenugasanDto[]) {
    try {
      const IDs = jsonData.map((data) => Number(data['ID Instansi']));
      const instansi = await this.prisma.penugasan_instansi.findMany({
        where: { id: { in: IDs } },
        select: {
          id: true,
          nama: true,
        },
      });

      const instansiDict = instansi.reduce((final, temp) => {
        final[Number(temp.id)] = temp;
        return final;
      }, {});

      return instansiDict;
    } catch (err) {
      throw err;
    }
  }

  private _checkUniqueNRP(jsonData: DraftBulkPenugasanDto[]) {
    try {
      const duplicateNrps = [];

      const nrpMap: Record<string, boolean> = {};
      for (const data of jsonData) {
        if (data.NRP in nrpMap) {
          duplicateNrps.push(data.NRP);
          continue;
        }

        nrpMap[data.NRP] = true;
      }
      if (duplicateNrps.length > 1) {
        throw new BadRequestException(
          `Upload File Gagal! Terdapat Duplicate NRP: ${duplicateNrps.join(', ')}`,
        );
      }

      return true;
    } catch (err) {
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };

      return {
        ...file,
        key: uploaded.Key,
        url: uploaded.Location,
        filename: uploaded.filename,
        created_at: new Date(),
        updated_at: new Date(),
      };
    } catch (err) {}
  }
}
