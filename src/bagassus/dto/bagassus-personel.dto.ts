import { Type } from 'class-transformer';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class BagassusPersonelGetListDto {
  @IsOptional()
  @IsString()
  q: number;

  @IsOptional()
  @IsDateString()
  start_date: Date;

  @IsOptional()
  @IsDateString()
  end_date: Date;
}

export class BagassusPersonelCreateDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  bagassus_personel_id: number;

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  triwulan: number;

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tahun: number;
}
