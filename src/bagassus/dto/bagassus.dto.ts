import { selection_type_enum } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { MatchArrayLength } from '../../core/decorators';

export class GetManyPenugasan {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  loc_type: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pangkat_id: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;

  @IsOptional()
  @IsString()
  q: string;
}

export class CreatePenugasanDto {
  @IsNotEmpty()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateSinglePenugasanDto)
  data: CreateSinglePenugasanDto[];
}

export class CreateSinglePenugasanDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  personel_id: number;

  @IsNotEmpty()
  @IsEnum(selection_type_enum)
  type: selection_type_enum; // dalam/luar negeri

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;

  @IsNotEmpty()
  @IsString()
  jabatan: string;

  @IsNotEmpty()
  @IsString()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;

  file_sprint: Express.Multer.File;
  file_kep: Express.Multer.File;
}

export class CreateSeleksiDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  @Type(() => Number)
  location_type_id: number;

  @IsNotEmpty()
  @Type(() => Number)
  logo_file_id: number;

  @IsNotEmpty()
  @Type(() => Number)
  document_file_id: number;

  @IsNotEmpty()
  @Type(() => Number)
  penugasan: number;

  @IsNotEmpty()
  @IsString()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;

  @IsNotEmpty()
  @IsString()
  submission_start: string;

  @IsNotEmpty()
  @IsString()
  submission_end: string;

  @IsNotEmpty()
  @Type(() => Number)
  round: number;

  @IsOptional()
  @MatchArrayLength('round', {
    message: 'round_selection_dates must have the same length as round.',
  })
  @ValidateNested({ each: true })
  @Type(() => RoundSelectionDatesDto)
  round_selection_dates?: RoundSelectionDatesDto[];
}

export class RoundSelectionDatesDto {
  @IsNotEmpty()
  @IsString()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;
}

export class GetManySeleksiDto {
  @IsNotEmpty()
  @Type(() => Number)
  loc_type: number;
}

export class EndRegistrationDto {
  @IsNotEmpty()
  @Type(() => Number)
  id: number;
}

export class GetDataByIdDto {
  @IsNotEmpty()
  @Type(() => Number)
  id: number;
}

export class ApplyToSeleksiDto {
  @IsNotEmpty()
  @Type(() => Number)
  seleksi_id: number;
}

export class KeywordSearchDto {
  @IsOptional()
  @IsString()
  q: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit: number;
}

export class SearchPersonelDto {
  @IsOptional()
  @IsString()
  nrp: string;
}

export class DraftBulkPenugasanDto {
  @IsNotEmpty()
  No: string;

  @IsNotEmpty()
  NRP: string;

  @IsNotEmpty()
  Nama: string;

  @IsNotEmpty()
  'ID Instansi': string;

  @IsNotEmpty()
  'Nama Instansi': string;

  @IsNotEmpty()
  'Tanggal Mulai': string;

  @IsNotEmpty()
  'Tanggal Selesai': string;

  @IsNotEmpty()
  'Nama Jabatan': string;
}

export class CreateDraftPenugasanDto {
  @IsNotEmpty()
  @Type(() => Number)
  location_type: number;
}

export class GetManyDraftBulkPenugasanDto {
  @IsOptional()
  q: string;

  @IsNotEmpty()
  @Type(() => Number)
  page: number;

  @IsNotEmpty()
  @Type(() => Number)
  limit: number;
}

export class UpdateDraftPersonelDto {
  @IsNotEmpty()
  @Type(() => Number)
  id: number;

  @IsOptional()
  @Type(() => Number)
  file_sprin_id?: number;

  @IsOptional()
  @Type(() => Number)
  file_kep_id?: number;
}
