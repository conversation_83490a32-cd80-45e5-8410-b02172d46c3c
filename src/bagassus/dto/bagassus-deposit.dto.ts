import { Type } from 'class-transformer';
import {
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class BagassusDepositGetListDto {
  @IsOptional()
  @IsString()
  q: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pangkat_id: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;
}

export class BagassusDepositAssignDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  deposit_id: number;

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;

  @IsNotEmpty()
  @IsString()
  jabatan: string;

  @IsNotEmpty()
  @IsDateString()
  start_date: Date;

  @IsNotEmpty()
  @IsDateString()
  end_date: Date;
}
