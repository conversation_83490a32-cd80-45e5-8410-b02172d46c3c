import { selection_type_enum } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class BagassusPenugasanGetListDto {
  @IsOptional()
  @IsString()
  q: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pangkat_id: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;

  @IsNotEmpty()
  @IsEnum(selection_type_enum)
  type: selection_type_enum;
}

export class BagassusPenugasanExtendDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  instansi_id: number;

  @IsNotEmpty()
  @IsString()
  jabatan: string;

  @IsNotEmpty()
  @IsDateString()
  start_date: Date;

  @IsNotEmpty()
  @IsDateString()
  end_date: Date;
}

export class BagassusPenugasanCreateDto extends BagassusPenugasanExtendDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  personel_id: number;

  @IsNotEmpty()
  @IsEnum(selection_type_enum)
  type: selection_type_enum;
}
