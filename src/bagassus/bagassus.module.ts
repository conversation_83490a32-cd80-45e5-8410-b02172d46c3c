import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { UsersModule } from '../access-management/users/users.module';
import { ExcelModule } from '../api-utils/excel/excel.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { BagassusDepositController } from './controller/bagassus-deposit.controller';
import { BagassusPenugasanController } from './controller/bagassus-penugasan.controller';
import { BagassusPersonelController } from './controller/bagassus-personel.controller';
import { BagassusController } from './controller/bagassus.controller';
import { BagassusDepositService } from './service/bagassus-deposit.service';
import { BagassusPenugasanService } from './service/bagassus-penugasan.service';
import { BagassusOperatorService } from './service/bagassus.operator.service';
import { BagassusPersonelService } from './service/bagassus.personel.service';
import { BagassusSeleksiService } from './service/bagassus.seleksi.service';
import { FileBagassusService } from './service/file-bagassus.service';

@Module({
  imports: [
    PrismaModule,
    ExcelModule,
    LogsActivityModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [
    BagassusController,
    BagassusDepositController,
    BagassusPenugasanController,
    BagassusPersonelController,
  ],
  providers: [
    BagassusOperatorService,
    BagassusPersonelService,
    BagassusSeleksiService,
    BagassusDepositService,
    BagassusPenugasanService,
    MinioService,
    FileBagassusService,
  ],
})
export class BagassusModule {}
