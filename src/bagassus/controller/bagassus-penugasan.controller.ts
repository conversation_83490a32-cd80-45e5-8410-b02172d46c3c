import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  BagassusPenugasanCreateDto,
  BagassusPenugasanExtendDto,
  BagassusPenugasanGetListDto,
} from '../dto/bagassus-penugasan.dto';
import { BagassusPenugasanService } from '../service/bagassus-penugasan.service';

@UsePipes(new ValidationPipe({ transform: true }))
// @UseGuards(JwtAuthGuard)
@Controller('bagassus/penugasan')
export class BagassusPenugasanController {
  constructor(private readonly penugasanService: BagassusPenugasanService) {}

  @Get()
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query() query: BagassusPenugasanGetListDto,
  ) {
    return this.penugasanService.getList(
      req,
      paginationData,
      searchandsortData,
      query,
    );
  }

  @Get('personel-list')
  async getListPersonel(@Req() req: any) {
    return this.penugasanService.getListPersonel(req);
  }

  @Get(':id')
  async getListHistory(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.penugasanService.getDetail(req, id);
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post()
  async createPenugasan(
    @Req() req: any,
    @Body() body: BagassusPenugasanCreateDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.penugasanService.createPenugasan(req, body, files);
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('extend/:id')
  async extendPenugasan(
    @Req() req: any,
    @Body() body: BagassusPenugasanExtendDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.penugasanService.extendPenugasan(req, id, body, files);
  }
}
