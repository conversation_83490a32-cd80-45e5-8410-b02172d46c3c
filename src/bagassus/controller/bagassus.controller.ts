import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { ExcelService } from 'src/api-utils/excel/service/excel.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { MODULES } from '../../core/constants/module.constant';
import { Module, Permission } from '../../core/decorators';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import {
  CreateDraftPenugasanDto,
  CreatePenugasanDto,
  CreateSinglePenugasanDto,
  DraftBulkPenugasanDto,
  GetManyDraftBulkPenugasan<PERSON>to,
  GetManyPenugasan,
  KeywordSearchDto,
  SearchPersonelDto,
  UpdateDraftPersonelDto,
} from '../dto/bagassus.dto';
import { BagassusOperatorService } from '../service/bagassus.operator.service';
import { BagassusPersonelService } from '../service/bagassus.personel.service';
import { BagassusSeleksiService } from '../service/bagassus.seleksi.service';
import { FileBagassusService } from '../service/file-bagassus.service';

@Controller('bagassus')
@Module(MODULES.SELECTION_MODULE)
@UseGuards(JwtAuthGuard, PermissionGuard)
@UsePipes(
  new ValidationPipe({
    stopAtFirstError: true,
    transform: true,
  }),
)
export class BagassusController {
  private readonly logger = new Logger(BagassusController.name);

  constructor(
    private readonly operatorService: BagassusOperatorService,
    private readonly personelService: BagassusPersonelService,
    private readonly fileService: FileBagassusService,
    private readonly seleksiService: BagassusSeleksiService,
    private readonly excelService: ExcelService,
  ) {}

  // @Post('file/sprin')
  // @Permission('PERMISSION_CREATE')
  // @UseInterceptors(
  //   FileFieldsInterceptor([
  //     {
  //       name: 'file_sprin',
  //       maxCount: 1,
  //     },
  //   ]),
  // )
  // async uploadFileSprin(
  //   @Req() req: any,
  //   @UploadedFiles() files: { file_sprin?: Express.Multer.File[] },
  // ) {
  //   this.logger.log(`Entering ${this.uploadFileSprin.name}`);

  //   const response = await this.fileService.uploadFileSprin(
  //     req,
  //     files.file_sprin[0],
  //   );

  //   this.logger.log(
  //     `Leaving ${this.uploadFileSprin.name} with response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Post('file/kep')
  // @Permission('PERMISSION_CREATE')
  // @UseInterceptors(
  //   FileFieldsInterceptor([
  //     {
  //       name: 'file_kep',
  //       maxCount: 1,
  //     },
  //   ]),
  // )
  // async uploadFileKep(
  //   @Req() req: any,
  //   @UploadedFiles()
  //   files: {
  //     file_kep?: Express.Multer.File[];
  //   },
  // ) {
  //   this.logger.log(`Entering ${this.uploadFileKep.name}`);

  //   const response = await this.fileService.uploadFileKep(
  //     req,
  //     files.file_kep[0],
  //   );

  //   this.logger.log(
  //     `Leaving ${this.uploadFileKep.name} with response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  @Post('file/penugasan-bulk')
  @UseInterceptors(
    FileFieldsInterceptor([
      {
        name: 'list_penugasan',
        maxCount: 1,
      },
    ]),
  )
  @Permission('PERMISSION_CREATE')
  async uploadExcelPenugasan(
    @Req() req: any,
    @UploadedFiles()
    files: {
      list_penugasan?: Express.Multer.File[];
    },
    @Body() payload: CreateDraftPenugasanDto,
  ) {
    this.logger.log(
      `Entering ${this.uploadExcelPenugasan.name} with body ${JSON.stringify(payload)}`,
    );

    const file = files.list_penugasan[0];
    const tmpFilePath = file.originalname;
    fs.writeFileSync(tmpFilePath, file.buffer);
    const jsonData: DraftBulkPenugasanDto[] =
      await this.excelService.extractToJson(req, tmpFilePath, false);

    const response = await this.operatorService.createDraftBulkPenugasan(
      jsonData,
      payload,
      req,
    );

    this.logger.log(
      `Leaving ${this.uploadExcelPenugasan.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/draft')
  @Permission('PERMISSION_READ')
  async getPenunjukanDraft(
    @Req() req: any,
    @Query() queries: GetManyDraftBulkPenugasanDto,
  ) {
    this.logger.log(
      `Entering ${this.getPenunjukanDraft.name} with queries ${JSON.stringify(queries)}`,
    );

    const response = await this.operatorService.findDraftBulkPenugasan(
      req,
      queries,
    );

    this.logger.log(
      `Leaving ${this.getPenunjukanDraft.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/draft')
  @Permission('PERMISSION_UPDATE')
  async updateDraftPersonel(
    @Req() req: any,
    @Body() payload: UpdateDraftPersonelDto,
  ) {
    this.logger.log(
      `Entering ${this.updateDraftPersonel.name} with body ${JSON.stringify(payload)}`,
    );

    const response = await this.operatorService.updateDraftPersonel(
      req,
      payload,
    );

    this.logger.log(
      `Leaving ${this.updateDraftPersonel.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/draft/:id')
  @Permission('PERMISSION_DELETE')
  async deleteDraftPersonel(
    @Req() req: any,
    @Body() payload: UpdateDraftPersonelDto,
  ) {
    this.logger.log(
      `Entering ${this.deleteDraftPersonel.name} with body ${JSON.stringify(payload)}`,
    );

    const response = await this.operatorService.removeDraftPersonel(
      req,
      payload,
    );

    this.logger.log(
      `Leaving ${this.deleteDraftPersonel.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/draft/publish')
  @Permission('PERMISSION_CREATE')
  async publishDraftPersonel(@Req() req: Request) {
    this.logger.log(`Entering ${this.publishDraftPersonel.name}`);

    const publishDraftPenugasanData =
      await this.operatorService.publishDraftPenugasan(req);
    const removeAllDraftPenugasanData =
      await this.operatorService.removeAllDraftPenugasan(req);

    const response = {
      statusCode: HttpStatus.OK,
      message:
        publishDraftPenugasanData.message +
        ' > ' +
        removeAllDraftPenugasanData.message,
      data: {
        publishDraftPenugasanData: publishDraftPenugasanData.data,
        removeAllDraftPenugasanData: removeAllDraftPenugasanData.data,
      },
    };

    this.logger.log(
      `Leaving ${this.publishDraftPersonel.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/draft/cancel')
  @Permission('PERMISSION_DELETE')
  async cancelDraftPersonel(@Req() req: any) {
    this.logger.log(`Entering ${this.cancelDraftPersonel.name}`);

    const response = await this.operatorService.removeAllDraftPenugasan(req);

    this.logger.log(
      `Leaving ${this.cancelDraftPersonel.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('extend')
  @Permission('PERMISSION_CREATE')
  async extendPenugasan(
    @Req() req: any,
    @Body() body: CreateSinglePenugasanDto,
  ) {
    this.logger.log(
      `Entering ${this.extendPenugasan.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.operatorService.extendPenugasanV2(req, body);

    this.logger.log(
      `Leaving ${this.extendPenugasan.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post()
  @Permission('PERMISSION_CREATE')
  async createPenugasan(@Req() req: any, @Body() body: CreatePenugasanDto) {
    this.logger.log(
      `Entering ${this.createPenugasan.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.operatorService.createPenugasan(req, body);

    this.logger.log(
      `Leaving ${this.createPenugasan.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  async findAllActivePenugasan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetManyPenugasan,
  ) {
    this.logger.log(
      `Entering ${this.findAllActivePenugasan.name} with queries ${JSON.stringify(queries)} and pagination data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.operatorService.findAllActivePenugasan(
      req,
      queries,
      paginationData,
    );

    this.logger.log(
      `Leaving ${this.findAllActivePenugasan.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/search')
  async searchPersonel(@Req() req: any, @Query() queries: SearchPersonelDto) {
    this.logger.log(
      `Entering ${this.searchPersonel.name} with queries ${JSON.stringify(queries)}`,
    );

    const response = await this.operatorService.searchPersonel(req, queries);

    this.logger.log(
      `Leaving ${this.searchPersonel.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/keyword-suggestion/instansi')
  @Permission('PERMISSION_READ')
  async getInstansiKeywordSuggestion(
    @Req() req: any,
    @Query() queries: KeywordSearchDto,
  ) {
    this.logger.log(
      `Entering ${this.getInstansiKeywordSuggestion.name} with queries ${JSON.stringify(queries)}`,
    );

    const response = await this.operatorService.findInstansiKeywordSuggestion(
      req,
      queries,
    );

    this.logger.log(
      `Leaving ${this.getInstansiKeywordSuggestion.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/keyword-suggestion/pangkat')
  @Permission('PERMISSION_READ')
  async getPangkatKeywordSuggestion(
    @Req() req: any,
    @Query() queries: KeywordSearchDto,
  ) {
    this.logger.log(
      `Entering ${this.getPangkatKeywordSuggestion.name} with queries ${JSON.stringify(queries)}`,
    );

    const response = await this.operatorService.findPangkatKeywordSuggestion(
      req,
      queries,
    );

    this.logger.log(
      `Leaving ${this.getPangkatKeywordSuggestion.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/:uid')
  async findPersonelHistoryByUid(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.findPersonelHistoryByUid.name} with uid ${uid}`,
    );

    const response = await this.operatorService.findPersonelHistoryByUid(
      req,
      uid,
    );

    this.logger.log(
      `Leaving ${this.findPersonelHistoryByUid.name} with uid ${uid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/list/instansi')
  @Permission('PERMISSION_READ')
  async getListInstansi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListInstansi.name} with search and sort data ${searchandsortData} and pagination data ${paginationData}`,
    );

    const response = await this.operatorService.getListInstansi(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getListInstansi.name} with search and sort data ${searchandsortData} and pagination data ${paginationData} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/history')
  @Permission('PERMISSION_READ')
  async findAllHistoryPenugasan(
    @Req() req: any,
    @Param('personel_id') personelId: string,
  ) {
    this.logger.log(
      `Entering ${this.findAllHistoryPenugasan.name} with personel id ${personelId}`,
    );

    const response = await this.operatorService.findPersonelHistory(
      req,
      Number(personelId),
    );

    this.logger.log(
      `Leaving ${this.findAllHistoryPenugasan.name} with personel id ${personelId} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:personel_id/detail')
  @Permission('PERMISSION_READ')
  async findPenugasanPersonel(
    @Req() req: any,
    @Param('personel_id') personelId: string,
  ) {
    this.logger.log(
      `Entering ${this.findPenugasanPersonel.name} with personel id ${personelId}`,
    );

    const response = await this.operatorService.findSingleActivePenugasan(
      req,
      Number(personelId),
    );

    this.logger.log(
      `Leaving ${this.findPenugasanPersonel.name} with personel id ${personelId} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  // section SELEKSI
  // @Post('seleksi/file')
  // @Permission('PERMISSION_CREATE')
  // @UseInterceptors(
  //   FileFieldsInterceptor([
  //     {
  //       name: 'file_seleksi',
  //       maxCount: 1,
  //     },
  //   ]),
  // )
  // async uploadFileSeleksi(
  //   @Req() req: any,
  //   @UploadedFiles() files: { file_seleksi?: Express.Multer.File[] },
  // ) {
  //   this.logger.log(`Entering ${this.uploadFileSeleksi.name}`);

  //   const response = await this.fileService.uploadFileSeleksi(
  //     req,
  //     files.file_seleksi[0],
  //   );

  //   this.logger.log(
  //     `Leaving ${this.uploadFileSeleksi.name} with response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Post('seleksi')
  // @Permission('PERMISSION_CREATE')
  // async createSeleksi(@Req() req: any, @Body() body: CreateSeleksiDto) {
  //   this.logger.log(
  //     `Entering ${this.createSeleksi.name} with body ${JSON.stringify(body)}`,
  //   );

  //   const response = await this.seleksiService.createSeleksi(req, body);

  //   this.logger.log(
  //     `Leaving ${this.createSeleksi.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Post('seleksi/end')
  // @Permission('PERMISSION_CREATE')
  // async endRegistration(@Req() req: any, @Param() params: EndRegistrationDto) {
  //   this.logger.log(
  //     `Entering ${this.endRegistration.name} with params ${JSON.stringify(params)}`,
  //   );

  //   const response = await this.seleksiService.endRegistrationPeriod(
  //     req,
  //     params.id,
  //   );

  //   this.logger.log(
  //     `Leaving ${this.endRegistration.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Get('seleksi')
  // @Permission('PERMISSION_READ')
  // async getAllSeleksi(@Req() req: any, @Query() queries: GetManySeleksiDto) {
  //   this.logger.log(
  //     `Entering ${this.getAllSeleksi.name} with queries ${JSON.stringify(queries)}`,
  //   );

  //   const response = await this.seleksiService.getManySeleksi(req, queries);

  //   this.logger.log(
  //     `Leaving ${this.getAllSeleksi.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Get('seleksi/requirements')
  // @Permission('PERMISSION_READ')
  // async getRequirementType(@Req() req: any) {
  //   this.logger.log(`Entering ${this.getRequirementType.name}`);

  //   const response = await this.seleksiService.getRequirement(req);

  //   this.logger.log(
  //     `Leaving ${this.getRequirementType.name} with response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Get('seleksi/requirement/:requirement_id')
  // async getRequirementValues(
  //   @Req() req: any,
  //   @Param('requirement_id') requirementId: number,
  // ) {
  //   this.logger.log(
  //     `Entering ${this.getRequirementValues.name} with requirement id ${requirementId}`,
  //   );

  //   const response = await this.seleksiService.getRequirementValue(
  //     req,
  //     requirementId,
  //   );

  //   this.logger.log(
  //     `Leaving ${this.getRequirementValues.name} with requirement id ${requirementId} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Get('/personel/seleksi')
  // async getSeleksi(@Req() req: any, @Query() queries: GetManySeleksiDto) {
  //   this.logger.log(
  //     `Entering ${this.getSeleksi.name} with queries ${JSON.stringify(queries)}`,
  //   );

  //   const response = await this.personelService.getAllSeleksi(req, queries);

  //   this.logger.log(
  //     `Leaving ${this.getSeleksi.name} with queries ${JSON.stringify(queries)} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Post('/personel/seleksi/apply')
  // async applySeleksi(@Req() req: any, @Body() body: ApplyToSeleksiDto) {
  //   this.logger.log(
  //     `Entering ${this.applySeleksi.name} with body ${JSON.stringify(body)}`,
  //   );

  //   const response = await this.personelService.applySeleksi(req, body);

  //   this.logger.log(
  //     `Leaving ${this.applySeleksi.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }

  // @Get('/personel/seleksi/:id')
  // async getSeleksiDetail(@Req() req: any, @Param() params: GetDataByIdDto) {
  //   this.logger.log(
  //     `Entering ${this.getSeleksiDetail.name} with param ${JSON.stringify(params)}`,
  //   );

  //   const response = await this.personelService.getOneSeleksi(req, params.id);

  //   this.logger.log(
  //     `Leaving ${this.getSeleksiDetail.name} with param ${JSON.stringify(params)} and response ${JSON.stringify(response)}`,
  //   );

  //   return response;
  // }
}
