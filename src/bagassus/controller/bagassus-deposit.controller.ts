import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import {
  BagassusDepositAssignDto,
  BagassusDepositGetListDto,
} from '../dto/bagassus-deposit.dto';
import { BagassusDepositService } from '../service/bagassus-deposit.service';

@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(JwtAuthGuard)
@Controller('bagassus/deposit')
export class BagassusDepositController {
  constructor(private readonly depositService: BagassusDepositService) {}

  @Get('instansi')
  async getInstansi(@Req() req: Request) {
    return this.depositService.getInstansi(req);
  }

  @Get()
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query() query: BagassusDepositGetListDto,
  ) {
    return this.depositService.getList(
      req,
      paginationData,
      searchandsortData,
      query,
    );
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post('assign')
  async assign(
    @Req() req: Request,
    @Body() payload: BagassusDepositAssignDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.depositService.assign(req, payload, files);
  }
}
