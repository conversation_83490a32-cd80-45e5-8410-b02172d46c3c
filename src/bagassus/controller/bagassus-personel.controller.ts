import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import {
  BagassusPersonelCreateDto,
  BagassusPersonelGetListDto,
} from '../dto/bagassus-personel.dto';
import { BagassusPersonelService } from '../service/bagassus.personel.service';

@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(JwtAuthGuard)
@Controller('bagassus/personel')
export class BagassusPersonelController {
  constructor(private readonly personelService: BagassusPersonelService) {}

  @Get()
  async getList(
    @Req() req: any,
    @Query() query: BagassusPersonelGetListDto,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    return this.personelService.getList(
      req,
      paginationData,
      searchandsortData,
      query,
    );
  }

  @UseInterceptors(AnyFilesInterceptor())
  @Post()
  async createReport(
    @Req() req: any,
    @Body() body: BagassusPersonelCreateDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.personelService.createReport(req, body, files);
  }
}
