import { HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class KhirdinService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req, paginationData, searchandsortData) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: where,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const currentDate = new Date();

    const filteredPersonels = personels
      .map((personel) => {
        const birthDate = new Date(personel.tanggal_lahir);
        const retirementDate = new Date(
          birthDate.getFullYear() + 58,
          birthDate.getMonth(),
          birthDate.getDate(),
        );

        const diffTime = retirementDate.getTime() - currentDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        const monthsRemaining = Math.floor(diffDays / 30);

        return {
          ...personel,
          id: Number(personel.id),
          tanggal_lahir: new Date(personel.tanggal_lahir)
            .toISOString()
            .split('T')[0], // YYYY-MM-DD
          jabatan: personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
          monthsRemaining: monthsRemaining,
        };
      })
      .filter((personel) => personel.monthsRemaining <= 6);

    const queryResult = filteredPersonels.map((personel) => {
      const result = {
        ...personel,
        id: Number(personel.id),
        tanggal_lahir: personel.tanggal_lahir,
        jabatan: personel.jabatan,
        satuan: personel.satuan,
      };

      // Delete nested properties
      delete result.jabatan_personel;
      delete result.monthsRemaining;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      return result;
    });

    const totalPage = Math.ceil(queryResult.length / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KHIRDIN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KHIRDIN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: queryResult.length,
      data: queryResult,
    };
  }
}
