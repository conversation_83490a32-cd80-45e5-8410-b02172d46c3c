import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { KhirdinService } from '../service/khirdin.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { MODULES } from '../../core/constants/module.constant';

@Controller('akhir-dinas-personel')
@Module(MODULES.SERVICE_TERMINATION)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class KhirdinController {
  private readonly logger = new Logger(KhirdinController.name);

  constructor(private readonly khirdinService: KhirdinService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.khirdinService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(req)}`,
    );

    return response;
  }
}
