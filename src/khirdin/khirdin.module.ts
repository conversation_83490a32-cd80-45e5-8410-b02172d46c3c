import { forwardRef, Module } from '@nestjs/common';
import { KhirdinService } from './service/khirdin.service';
import { KhirdinController } from './controller/khirdin.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KhirdinController],
  providers: [KhirdinService],
})
export class KhirdinModule {}
