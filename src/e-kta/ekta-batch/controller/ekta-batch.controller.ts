import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { EktaBatchService } from '../service/ekta-batch.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FilterDistribusiDto } from '../dto/filter.dto';

@Controller('ekta-batch')
@UseGuards(JwtAuthGuard)
export class EktaBatchController {
  private readonly logger = new Logger(EktaBatchController.name);

  constructor(private readonly ektaBatchService: EktaBatchService) {}

  @Get('')
  @HttpCode(200)
  async getList(@Req() req: any, @Query() query: any) {
    this.logger.log(
      `Entering ${this.getList.name} with query ${JSON.stringify(query)}`,
    );

    const response = await this.ektaBatchService.getFilterDistribusiKTA(
      req,
      query,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with query ${JSON.stringify(query)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('waiting-approve')
  @HttpCode(200)
  async getListWaitingApprove(@Req() req: any, @Query() query: any) {
    this.logger.log(
      `Entering ${this.getListWaitingApprove.name} with query ${JSON.stringify(query)}`,
    );

    const response = await this.ektaBatchService.getWaitingApprove(req, query);

    this.logger.log(
      `Leaving ${this.getListWaitingApprove.name} with query ${JSON.stringify(query)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('approve')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(201)
  async approve(
    @Req() req: any,
    @Body() payload: { id: string },
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.approve.name} with payload ${JSON.stringify(payload)}`,
    );
    const response = await this.ektaBatchService.postApprovePengajuan(
      req,
      payload,
      file,
    );

    this.logger.log(
      `Leaving ${this.approve.name} with payload ${JSON.stringify(payload)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('update-distribusi')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(201)
  async distribution(
    @Req() req: any,
    @Body() payload: { id: string; status: string },
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.distribution.name} with payload ${JSON.stringify(payload)}`,
    );

    const response = await this.ektaBatchService.distribusiPengajuan(
      req,
      payload,
      file,
    );

    this.logger.log(
      `Leaving ${this.distribution.name} with payload ${JSON.stringify(payload)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('filter')
  @HttpCode(200)
  async getFilterDistribusiKTA(
    @Req() req: any,
    @Query() query: FilterDistribusiDto,
  ) {
    this.logger.log(
      `Entering ${this.getFilterDistribusiKTA.name} with query ${JSON.stringify(query)}`,
    );

    const response = await this.ektaBatchService.getFilterDistribusiKTA(
      req,
      query,
    );

    this.logger.log(
      `Leaving ${this.getFilterDistribusiKTA.name} with query ${JSON.stringify(query)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @HttpCode(200)
  async getData(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.getData.name} with id ${id}`);

    const response = await this.ektaBatchService.getFirst(req, id);

    this.logger.log(
      `Leaving ${this.getData.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
