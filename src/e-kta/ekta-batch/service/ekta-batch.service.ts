import {
  BadGatewayException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import type { FilterDistribusiDto } from '../dto/filter.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class EktaBatchService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  // async getList(req, query) {
  //   const { status, page = 1, limit = 10, month, year } = query;
  //
  //   let where = {};
  //
  //   if (status !== undefined) {
  //     where = {
  //       ...where,
  //       status: Number(status),
  //     };
  //   }
  //
  //   if (month !== undefined || year !== undefined) {
  //     where = {
  //       ...where,
  //       created_at: {
  //         ...(month !== undefined && {
  //           gte: new Date(year || new Date().getFullYear(), month - 1, 1),
  //           lt: new Date(year || new Date().getFullYear(), month, 1),
  //         }),
  //         ...(year !== undefined &&
  //           month === undefined && {
  //             gte: new Date(year, 0, 1),
  //             lt: new Date(Number(year) + 1, 0, 1),
  //           }),
  //       },
  //     };
  //   }
  //
  //   try {
  //     const [data, totalData] = await this.prisma.$transaction([
  //       this.prisma.ekta_batch.findMany({
  //         select: {
  //           id: true,
  //           kode: true,
  //           satuan: true,
  //           status: true,
  //           created_at: true,
  //           ekta_batch_distribusi_log: {
  //             orderBy: {
  //               id: 'desc',
  //             },
  //           },
  //           ekta_batch_approval_log: {
  //             orderBy: {
  //               id: 'desc',
  //             },
  //           },
  //         },
  //         orderBy: {
  //           id: 'asc',
  //         },
  //         take: +limit,
  //         skip: (page - 1) * limit,
  //         where: where,
  //       }),
  //       this.prisma.ekta_batch.count({
  //         where: where,
  //       }),
  //     ]);
  //
  //     if (!data) {
  //       throw new BadGatewayException('Data not found');
  //     }
  //
  //     const totalPage = Math.ceil(totalData / limit);
  //
  //     return { data, totalData, totalPage };
  //   } catch (err) {
  //     throw new BadGatewayException(err);
  //   }
  // }

  async getWaitingApprove(req: any, query) {
    const { status, page = 1, limit = 10, month, year } = query;

    let where = {};

    if (status !== undefined) {
      where = {
        ...where,
        status: Number(status),
      };
    }

    if (month !== undefined || year !== undefined) {
      where = {
        ...where,
        created_at: {
          ...(month !== undefined && {
            gte: new Date(year || new Date().getFullYear(), month - 1, 1),
            lt: new Date(year || new Date().getFullYear(), month, 1),
          }),
          ...(year !== undefined &&
            month === undefined && {
              gte: new Date(year, 0, 1),
              lt: new Date(Number(year) + 1, 0, 1),
            }),
        },
      };
    }

    const levelAuth = req.user?.['level_name_new'];
    if (levelAuth === 'Level 3') {
      where = {
        ...where,
        status: 1,
      };
    } else if (levelAuth === 'Level 2') {
      where = {
        ...where,
        status: 2,
      };
    } else if (levelAuth === 'Level 1') {
      where = {
        ...where,
        status: 3,
      };
    } else {
      throw new BadGatewayException('Not Allowed Level');
    }

    try {
      const [queryResult, totalData] = await this.prisma.$transaction([
        this.prisma.ekta_batch.findMany({
          select: {
            id: true,
            kode: true,
            satuan: true,
            status: true,
            created_at: true,
          },
          orderBy: {
            id: 'desc',
          },
          take: +limit,
          skip: (page - 1) * limit,
          where: where,
        }),
        this.prisma.ekta_batch.count({
          where: where,
        }),
      ]);

      if (!queryResult) {
        throw new BadGatewayException('Data not found');
      }

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_BATCH_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_BATCH_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async postApprovePengajuan(
    req: any,
    payload: any,
    file: Express.Multer.File,
  ) {
    try {
      // Basic validation
      if (!payload.id || isNaN(Number(payload.id))) {
        throw new BadGatewayException('Invalid or missing batch ID');
      }

      if (file && !file.originalname.endsWith('.pdf')) {
        throw new BadGatewayException('Invalid file format');
      }

      let statusApproveTo;
      const levelAuth = req.user?.['level_name_new'];

      if (levelAuth === 'Level 3') {
        statusApproveTo = 2;
      } else if (levelAuth === 'Level 2') {
        statusApproveTo = 3;
      } else if (levelAuth === 'Level 1') {
        statusApproveTo = 4;
      } else {
        throw new BadGatewayException('Not Allowed Level');
      }

      let statusApprove;

      if (levelAuth === 'Level 3') {
        statusApprove = 1;
      } else if (levelAuth === 'Level 2') {
        statusApprove = 2;
      } else if (levelAuth === 'Level 1') {
        statusApprove = 3;
      } else {
        throw new BadGatewayException('Not Allowed Level');
      }

      // Sanitize the data before database operations
      const sanitizedData = {
        ekta_batch_id: Number(payload.id),
        user_id: req.user?.['id'] ? Number(req.user['id']) : null,
        status: statusApprove,
        approval_status: statusApprove,
        approval_at: new Date(),
        approval_dokumen: null,
        approval_comment: null,
        approval_sign: null,
        created_by: req.user?.['id'] ? Number(req.user['id']) : null,
        created_at: new Date(),
      };

      // Handle file upload first if exists
      let uploadedFileLocation = null;
      if (file) {
        try {
          const uploadedFile = await this.minioService.uploadFile(file);
          uploadedFileLocation = uploadedFile.Location;
        } catch (uploadError) {
          Logger.error('File upload failed:', uploadError);
          throw new BadGatewayException('File upload failed');
        }
      }

      // Update sanitizedData with file location if uploaded
      if (uploadedFileLocation) {
        sanitizedData.approval_dokumen = uploadedFileLocation;
      }

      try {
        // Run database operations in transaction
        const [queryResult] = await this.prisma.$transaction([
          // Update ekta_batch
          this.prisma.ekta_batch.update({
            where: {
              id: Number(payload.id),
            },
            data: {
              status: statusApproveTo,
            },
          }),
          // Create approval log
          this.prisma.ekta_batch_approval_log.create({
            data: {
              ...sanitizedData,
            },
          }),
          // Update all connected ekta records
          this.prisma.ekta.updateMany({
            where: {
              ekta_batch_id: Number(payload.id),
            },
            data: {
              status: statusApproveTo,
              updated_at: new Date(),
            },
          }),
        ]);

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.LIST,
          ConstantLogModuleEnum.EKTA_BATCH_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.E_KTA_BATCH_UPDATE as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: 201,
          message: 'Successfully approve the EKTA Batch',
          data: queryResult,
        };
      } catch (dbError) {
        Logger.error('Database transaction failed:', dbError);
        throw new BadGatewayException('Database operation failed');
      }
    } catch (error) {
      Logger.error('Batch approval failed:', error);
      throw new BadGatewayException(error);
    }
  }

  async distribusiPengajuan(req: any, payload, file?: Express.Multer.File) {
    try {
      if (!payload.id || isNaN(Number(payload.id))) {
        throw new BadGatewayException('Invalid or missing batch ID');
      }

      if (!payload.status || isNaN(Number(payload.status))) {
        throw new BadGatewayException('Invalid or missing batch status');
      }

      const getBatch = await this.prisma.ekta_batch.findFirst({
        where: {
          id: Number(payload.id),
        },
      });

      if (!getBatch) {
        throw new BadGatewayException('Data not found');
      }

      if (getBatch.status < 4) {
        throw new BadGatewayException(
          'Data cannot be distributed, data was not approved yet.',
        );
      }

      const batchUpdate = await this.prisma.ekta_batch.update({
        where: {
          id: getBatch.id,
        },
        data: {
          status: Number(payload.status),
        },
      });

      const updateEktaPersonel = await this.prisma.ekta.updateMany({
        where: {
          ekta_batch_id: getBatch.id,
          status: {
            not: 99,
          },
        },
        data: {
          status: Number(payload.status),
        },
      });

      let fileLocation = null;
      if (file) {
        const uploaded = await this.minioService.uploadFile(file);
        fileLocation = uploaded.Location;
      }

      const dataLog = {
        ekta_batch_id: getBatch.id,
        status: Number(payload.status),
        created_by: req.user?.['id'],
        created_at: new Date(),
        bukti_perubahan: fileLocation,
      };

      const log = await this.prisma.ekta_batch_distribusi_log.create({
        data: dataLog,
      });

      const queryResult = { batchUpdate, updateEktaPersonel, log };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_BATCH_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_BATCH_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: 201,
        message,
        data: queryResult,
      };
    } catch (error) {
      Logger.error('Batch distribution failed:', error);
      throw new BadGatewayException(error);
    }
  }

  async getFirst(req: any, id: any) {
    try {
      const [data] = await this.prisma.$transaction([
        this.prisma.ekta_batch.findFirst({
          select: {
            id: true,
            kode: true,
            satuan: true,
            status: true,
            created_at: true,
            created_by_users: {
              select: {
                personel: {
                  select: {
                    nama_lengkap: true,
                    nrp: true,
                    email: true,
                    no_hp: true,
                  },
                },
              },
            },
            ekta: {
              select: {
                personel: {
                  select: {
                    nama_lengkap: true,
                    nrp: true,
                    foto_file: true,
                    pangkat_personel: {
                      select: {
                        pangkat: {
                          select: {
                            nama: true,
                          },
                        },
                      },
                      orderBy: { tmt: 'desc' },
                      take: 1,
                    },
                    jabatan_personel: {
                      select: {
                        jabatans: {
                          select: {
                            nama: true,
                            satuan: {
                              select: {
                                nama: true,
                              },
                            },
                          },
                        },
                      },
                      orderBy: { created_at: 'desc' },
                      take: 1,
                    },
                  },
                },
              },
            },
            ekta_batch_distribusi_log: {
              select: {
                id: true,
                status: true,
                created_at: true,
                created_by_users: {
                  select: {
                    personel: {
                      select: {
                        nama_lengkap: true,
                        nrp: true,
                        email: true,
                        no_hp: true,
                      },
                    },
                  },
                },
              },
              orderBy: {
                id: 'desc',
              },
            },
            ekta_batch_approval_log: {
              select: {
                id: true,
                status: true,
                approval_dokumen: true,
                created_at: true,
                created_by_users: {
                  select: {
                    personel: {
                      select: {
                        nama_lengkap: true,
                        nrp: true,
                        email: true,
                        no_hp: true,
                      },
                    },
                  },
                },
              },
              orderBy: {
                id: 'desc',
              },
            },
          },
          where: {
            id: Number(id),
          },
        }),
      ]);

      if (!data) {
        throw new BadGatewayException('Data not found');
      }
      const queryResult = {
        ...data,
        ekta: await Promise.all(
          data.ekta.map(async (item) => ({
            ...item,
            personel: item.personel
              ? {
                  ...item.personel,
                  foto_file: await this.minioService.checkFileExist(
                    `${process.env.MINIO_BUCKET_NAME}`,
                    `${process.env.MINIO_PATH_FILE}${item.personel.nrp}/${item.personel.foto_file}`,
                  ),
                }
              : null,
          })),
        ),
      };

      // const resultData = {
      //   ...data,
      //   ekta: data.ekta.map((item) => ({
      //     personel: {
      //       ...item.personel,
      //       foto_file: item.personel.foto_file
      //         ? this.minioService.convertFileKeyToURL(
      //             `${process.env.MINIO_BUCKET_NAME}`,
      //             `${process.env.MINIO_PATH_FILE}${item.personel.nrp}/${item.personel}`,
      //           )
      //         : null,
      //     },
      //   })),
      // };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_BATCH_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_BATCH_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getFilterDistribusiKTA(req: any, query: FilterDistribusiDto) {
    const { page = 1, limit = 10, kode, month, year, status, satuan } = query;
    Logger.debug(req.user);
    let where: any = {};

    // Add kode filter if provided
    if (kode !== undefined) {
      const decodedKode = decodeURIComponent(kode);
      where.kode = {
        equals: decodedKode, // Using exact match since the format is standardized
      };
    }

    // Add status filter if provided
    if (status !== undefined) {
      where.status = Number(status);
    }

    // Add satuan filter if provided
    if (satuan !== undefined) {
      const decodedSatuan = decodeURIComponent(satuan);
      where.satuan = {
        equals: decodedSatuan, // Using exact match since the format is standardized
      };
    }

    // Add date filter if month or year is provided
    if (month !== undefined || year !== undefined) {
      where = {
        ...where,
        AND: [
          {
            created_at:
              month !== undefined
                ? {
                    gte: new Date(
                      year || new Date().getFullYear(),
                      month - 1,
                      1,
                    ),
                    lt: new Date(year || new Date().getFullYear(), month, 1),
                  }
                : year !== undefined
                  ? {
                      gte: new Date(year, 0, 1),
                      lt: new Date(Number(year) + 1, 0, 1),
                    }
                  : undefined,
          },
        ].filter((condition) => condition.created_at !== undefined),
      };
    }

    try {
      const [queryResult, totalData] = await this.prisma.$transaction([
        this.prisma.ekta_batch.findMany({
          select: {
            id: true,
            kode: true,
            satuan: true,
            status: true,
            created_at: true,
            ekta_bank: true,
            created_by_users: {
              select: {
                personel: true,
              },
            },
            ekta_batch_distribusi_log: {
              select: {
                id: true,
                status: true,
                created_at: true,
                created_by_users: {
                  select: {
                    personel: true,
                  },
                },
              },
              orderBy: {
                id: 'desc',
              },
            },
            ekta_batch_approval_log: {
              select: {
                id: true,
                status: true,
                approval_dokumen: true,
                created_at: true,
                created_by_users: {
                  select: {
                    personel: true,
                  },
                },
              },
              orderBy: {
                id: 'desc',
              },
            },
            ekta: {
              select: {
                id: true,
                status: true,
                alasan_ditolak: true,
                nama: true,
                nrp: true,
                pangkat: true,
                jabatan: true,
              },
            },
          },
          orderBy: {
            created_at: 'desc',
          },
          take: +limit,
          skip: (page - 1) * limit,
          where: where,
        }),
        this.prisma.ekta_batch.count({
          where: where,
        }),
      ]);

      if (!queryResult) {
        throw new BadGatewayException('Data not found');
      }

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_BATCH_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_BATCH_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }
}
