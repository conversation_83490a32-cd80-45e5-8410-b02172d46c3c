import { IsInt, IsOptional, IsString, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FilterDistribusiDto {
  @ApiProperty({
    description:
      'Kode batch (URL encoded). Example: BNI-biasa%2FEKTA%2F01%2FXI%2F2024',
    required: false,
  })
  @IsOptional()
  @IsString()
  kode?: string;

  @ApiProperty({
    description: 'Month (1-12)',
    required: false,
    minimum: 1,
    maximum: 12,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @Max(12)
  month?: number;

  @ApiProperty({
    description: 'Year (>= 2000)',
    required: false,
    minimum: 2000,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(2000)
  year?: number;

  @ApiProperty({
    description: 'Status code',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  status?: number;

  @ApiProperty({
    description: 'Satuan (URL encoded). Example: Polda%20Sumsel',
    required: false,
  })
  @IsOptional()
  @IsString()
  satuan?: string;

  @ApiProperty({
    description: 'Page number',
    required: false,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    minimum: 1,
    default: 10,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  limit?: number = 10;
}
