import { forwardRef, Module } from '@nestjs/common';
import { EktaBatchController } from './controller/ekta-batch.controller';
import { EktaBatchService } from './service/ekta-batch.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [EktaBatchController],
  providers: [EktaBatchService, MinioService],
})
export class EktaBatchModule {}
