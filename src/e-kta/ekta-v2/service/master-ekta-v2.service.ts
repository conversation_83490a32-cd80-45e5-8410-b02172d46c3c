import {
  BadGatewayException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CONSTANT_LOG } from 'src/core/constants/log.constant';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from 'src/core/enums/log.enum';
import { ConstantLogType } from 'src/core/interfaces/log.type';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import {
  SortSearchColumn,
  SortSearchColumnV2,
} from 'src/core/utils/search.utils';
import {
  AcceptedBankDto,
  CreateBankDto,
  CreateSatuanBankDto,
} from '../dto/create.dto';

@Injectable()
export class MasterEktaServiceV2 {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly allowedRoles = [
    'operator level 3 e-kta',
    'operator level 2 e-kta',
    'operator level 1 e-kta',
    'approver level 3 e-kta',
    'approver level 2 e-kta',
    'approver level 1 e-kta',
    'admin e-kta',
    'superadmin',
  ].map((role) => role.toLowerCase());

  async getBank(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string';
      };
    } = {
      id: { field: 'id', type: 'string' },
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    // const userRoles = req.user.roles;
    // const matchedRoles = userRoles.filter((role) =>
    //   this.allowedRoles.includes(role.nama),
    // );
    // const approvalRole = matchedRoles.length > 0 ? matchedRoles[0].nama : null;

    try {
      const [banks, totalData] = await this.prisma.$transaction([
        this.prisma.bank.findMany({
          select: {
            id: true,
            nama: true,
          },
          where: {
            ...where,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.bank.count({
          where: {
            ...where,
          },
        }),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          banks,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: banks,
      };
    } catch (err) {
      throw err;
    }
  }

  async createBank(req: any, body: CreateBankDto) {
    const { nama } = body;

    try {
      const bank = await this.prisma.bank.findFirst({
        where: {
          nama: {
            equals: nama,
            mode: 'insensitive',
          },
        },
      });

      if (bank) {
        throw new ConflictException('Bank already exists');
      }

      const newBank = await this.prisma.bank.create({
        data: {
          nama,
        },
      });

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Bank created successfully',
        data: newBank,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateBank(req: any, id: number, body: CreateBankDto) {
    const { nama } = body;

    try {
      const bank = await this.prisma.bank.findUnique({
        where: {
          id,
        },
      });

      if (!bank) {
        throw new NotFoundException('Bank not found');
      }

      const isSameName =
        bank.nama.trim().toLowerCase() === nama.trim().toLowerCase();

      if (!isSameName) {
        const checkExistsBank = await this.prisma.bank.findFirst({
          where: {
            nama: {
              equals: nama,
              mode: 'insensitive',
            },
            NOT: {
              id: bank.id,
            },
          },
        });

        if (checkExistsBank) {
          throw new ConflictException('Bank already exists');
        }
      }

      const updatedBank = await this.prisma.bank.update({
        where: {
          id,
        },
        data: {
          nama,
        },
      });

      return {
        statusCode: HttpStatus.OK,
        message: 'Bank updated successfully',
        data: updatedBank,
      };
    } catch (err) {
      throw err;
    }
  }

  async deleteBank(req: any, id: number) {
    try {
      const bank = await this.prisma.bank.findUnique({
        where: {
          id,
        },
      });

      if (!bank) {
        throw new NotFoundException('Bank not found');
      }

      await this.prisma.bank.update({
        where: {
          id,
        },
        data: {
          deleted_at: new Date(),
        },
      });

      return {
        statusCode: HttpStatus.OK,
        message: 'Bank deleted successfully',
      };
    } catch (err) {
      throw err;
    }
  }

  async getSatuanBank(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'number';
      };
    } = {
      satuan_id: { field: 'id', type: 'string' },
      bank_id: { field: 'satuan_bank.some.bank_id', type: 'string' },
      request_status: {
        field: 'satuan_bank.some.request_status',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['id'],
    );

    try {
      const [satuans, totalData] = await this.prisma.$transaction([
        this.prisma.satuan.findMany({
          select: {
            id: true,
            satuan_bank: {
              select: {
                id: true,
                bank_id: true,
                requested_bank_id: true,
                request_status: true,
                requested_at: true,
                bank_request: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                bank: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
            mv_satuanFullname: {
              select: {
                satuan_fullname: true,
              },
            },
          },
          where,
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.satuan.count({
          where,
        }),
      ]);

      const result = satuans.map((satuan) => ({
        id: satuan.id,
        satuan_name: satuan.mv_satuanFullname.satuan_fullname,
        bank_id: satuan.satuan_bank[0]?.bank_id || null,
        bank_name: satuan.satuan_bank[0]?.bank?.nama || null,
        request_status: satuan.satuan_bank[0]?.request_status || null,
        requested_bank_id: satuan.satuan_bank[0]?.requested_bank_id || null,
        requested_bank_name: satuan.satuan_bank[0]?.bank_request?.nama || null,
        requested_at: satuan.satuan_bank[0]?.requested_at || null,
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          result,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: result,
      };
    } catch (err) {
      throw err;
    }
  }

  async getSatuanBankAuth(req: any) {
    try {
      const satuanBank = await this.prisma.satuan_bank.findMany({
        select: {
          bank: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        where: {
          satuan_id: BigInt(req.user.satuan_id),
        },
      });

      const result = satuanBank.map((satuan) => ({
        id: satuan?.bank?.id || null,
        nama: satuan?.bank?.nama || null,
      }));

      return {
        statusCode: HttpStatus.OK,
        message: 'Satuan bank retrieved successfully',
        data: result,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateSatuanBank(req: any, id: number, body: CreateSatuanBankDto) {
    const { bank_id } = body;

    try {
      const satuan = await this.prisma.satuan.findFirst({
        where: {
          id: BigInt(id),
        },
      });

      if (!satuan) {
        throw new NotFoundException('Satuan not found');
      }

      const bank = await this.prisma.bank.findFirst({
        where: {
          id: bank_id,
        },
      });

      if (!bank) {
        throw new NotFoundException('Bank not found');
      }

      const userRoles = req.user.roles;
      const matchedRoles = userRoles.filter((role) =>
        this.allowedRoles.includes(role.nama.toLowerCase()),
      );
      const approvalRole =
        matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

      let updatedSatuanBank;
      if (approvalRole.toLowerCase() === 'admin e-kta') {
        updatedSatuanBank = await this.prisma.satuan_bank.update({
          where: {
            id,
          },
          data: {
            bank_id,
          },
        });
      } else {
        const satuanBank = await this.prisma.satuan_bank.findFirst({
          where: {
            satuan_id: BigInt(id),
          },
        });

        if (!satuanBank) {
          updatedSatuanBank = await this.prisma.satuan_bank.create({
            data: {
              satuan_id: BigInt(id),
              requested_bank_id: bank_id,
              request_status: 'PENDING',
              requested_at: new Date(),
            },
          });
        } else {
          updatedSatuanBank = await this.prisma.satuan_bank.updateMany({
            where: {
              satuan_id: BigInt(id),
            },
            data: {
              satuan_id: BigInt(id),
              requested_bank_id: bank_id,
              request_status: 'PENDING',
              requested_at: new Date(),
            },
          });
        }
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Bank satuan updated successfully',
        data: updatedSatuanBank,
      };
    } catch (err) {
      throw err;
    }
  }

  async acceptSatuanBank(req: any, id: number, body: AcceptedBankDto) {
    const { status } = body;

    try {
      const satuanBank = await this.prisma.satuan.findFirst({
        select: {
          id: true,
          satuan_bank: {
            select: {
              id: true,
              requested_bank_id: true,
              request_status: true,
            },
          },
        },
        where: {
          id,
          satuan_bank: {
            some: {
              request_status: 'PENDING',
            },
          },
        },
      });

      if (!satuanBank) {
        throw new NotFoundException('Satuan bank not found');
      }

      let updatedSatuanBank;
      if (status === 'ACCEPTED') {
        updatedSatuanBank = await this.prisma.satuan_bank.updateMany({
          where: {
            id: satuanBank.satuan_bank[0].id,
          },
          data: {
            request_status: 'ACCEPTED',
            bank_id: satuanBank.satuan_bank[0].requested_bank_id,
            updated_at: new Date(),
          },
        });
      } else {
        updatedSatuanBank = await this.prisma.satuan_bank.updateMany({
          where: {
            id: satuanBank.satuan_bank[0].id,
          },
          data: {
            request_status: 'REJECTED',
            updated_at: new Date(),
          },
        });
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Satuan bank accepted successfully',
        data: updatedSatuanBank,
      };
    } catch (err) {
      throw err;
    }
  }
}
