import {
  BadGatewayException,
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateDto } from '../dto/create.dto';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import {
  SortSearchColumn,
  SortSearchColumnV3,
} from 'src/core/utils/search.utils';
import { AuditPersonelOperatorDto } from '../dto/audit-personel.dto';
import { Prisma } from '@prisma/client';
import { romanMonth } from 'src/core/utils/common.utils';
import { SubmitApprovalDto, SubmitOperatorDto } from '../dto/submit.dto';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { UpdateStatusDto } from '../dto/update.dto';
import { chunk } from 'lodash';

@Injectable()
export class EktaServiceV2 {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly allowedRoles = [
    'operator level 3 e-kta',
    'operator level 2 e-kta',
    'operator level 1 e-kta',
    'approver level 3 e-kta',
    'approver level 2 e-kta',
    'approver level 1 e-kta',
    'admin e-kta',
    'superadmin',
  ].map((role) => role.toLowerCase());

  async getListSatuan(userSatuanId: number): Promise<{ id: number }[]> {
    return await this.prisma.$queryRaw<{ id: number }[]>`
        WITH RECURSIVE satuan_hierarchy AS (SELECT id, 1 AS level
          FROM satuan
          WHERE id = ${userSatuanId}

          UNION ALL

          SELECT s.id, sh.level + 1
          FROM satuan s
          INNER JOIN satuan_hierarchy sh ON s.atasan_id = sh.id)
        SELECT id
        FROM satuan_hierarchy;
    `;
  }

  async getListSatuan12(userSatuanId: number): Promise<{ id: number }[]> {
    return await this.prisma.$queryRaw<{ id: number }[]>`
      SELECT id FROM satuan WHERE atasan_id = ${userSatuanId};
    `;
  }

  async getData(req: any, paginationData, searchandsortData, statusApproval) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      nama: { field: 'nama', type: 'string' },
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    let approvalConditions = [];
    if (statusApproval == 'REJECTED') {
      approvalConditions = [
        {
          status: statusApproval,
        },
      ];
    } else {
      approvalConditions = [
        {
          status: statusApproval,
          approval: approvalRole.toLowerCase(),
        },
      ];

      // where.e_kta_surat_detail = { none: {} };
    }

    where.approval_history = {
      array_contains: approvalConditions,
    };

    try {
      let listSatuan;

      if (approvalRole.toLowerCase() == 'operator level 3 e-kta') {
        listSatuan = await this.getListSatuan(req.user.satuan_id);
      } else if (
        approvalRole.toLowerCase() == 'operator level 1 e-kta' ||
        approvalRole.toLowerCase() == 'superadmin'
      ) {
        listSatuan = [{ id: 1 }];
      } else {
        listSatuan = [{ id: req.user.satuan_id }];
      }

      const [ektas, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta.findMany({
          select: {
            id: true,
            nama: true,
            bank: true,
            personel: {
              select: {
                id: true,
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: { nama: true, nama_singkat: true },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: { select: { nama: true } },
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                    jabatans: {
                      AND: [
                        {
                          satuan_id: { in: listSatuan.map((item) => item.id) },
                        },
                      ],
                    },
                  },
                },
              },
            },
            approval_history: true,
          },
          where: {
            ...where,
            personel: {
              pangkat_personel: {
                some: {
                  is_aktif: true,
                },
              },
              jabatan_personel: {
                some: {
                  is_aktif: true,
                  jabatans: {
                    is: {
                      satuan_id: { in: listSatuan.map((item) => item.id) },
                    },
                  },
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta.count({
          where: {
            ...where,
            personel: {
              jabatan_personel: {
                some: {
                  is_aktif: true,
                  jabatans: {
                    is: {
                      satuan_id: { in: listSatuan.map((item) => item.id) },
                    },
                  },
                },
              },
            },
          },
        }),
      ]);

      const queryResult = await Promise.all(
        ektas.map(async ({ personel, id, approval_history, nama, bank }) => {
          return {
            id: id,
            personel_id: personel?.id || '',
            nama_lengkap: nama ?? personel.nama_lengkap,
            nrp: personel?.nrp || '',
            bank: bank || '',
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
              )) ?? '',
            pangkat: personel?.pangkat_personel?.[0]?.pangkat?.nama || '',
            pangkat_singkat:
              personel?.pangkat_personel?.[0]?.pangkat?.nama_singkat || '',
            jabatan: personel?.jabatan_personel?.[0]?.jabatans?.nama || '',
            satuan:
              personel?.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
            approval_history: approval_history,
          };
        }),
      );
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getDataPengajuan(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
      ['nama'],
    );

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    console.log(approvalRole);
    try {
      let satuanId = req.user.satuan_id;
      if (
        approvalRole.toLowerCase() == 'approver level 1 e-kta' ||
        approvalRole.toLowerCase() == 'superadmin'
      ) {
        satuanId = 1;
      }

      const [pengajuan, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta_pengajuan.findMany({
          where: {
            ...where,
            satuan_id: satuanId,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta_pengajuan.count({
          where: {
            ...where,
            satuan_id: satuanId,
          },
        }),
      ]);

      const queryResult = await Promise.all(
        pengajuan.map(async (data) => {
          const totalPengajuanBaru = await this.prisma.e_kta.count({
            where: {
              jenis_permintaan: 1, // pengajuan baru
              deleted_at: null,
              e_kta_pengajuan_id: data.id,
            },
          });

          const totalPenggantian = await this.prisma.e_kta.count({
            where: {
              jenis_permintaan: 2, // pengajuan baru
              deleted_at: null,
              e_kta_pengajuan_id: data.id,
            },
          });

          const totalPengajuan = totalPengajuanBaru + totalPenggantian;

          return {
            id: data.id,
            tanggal_pengajuan: data?.tanggal_pengajuan || '',
            pengajuan_baru: totalPengajuanBaru || 0,
            penggantian: totalPenggantian || 0,
            total_pengajuan: totalPengajuan || 0,
            status: data.status || false,
            created_at: data?.created_at || '',
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getDataPengajuanDetail(
    req,
    idArray,
    paginationData,
    searchandsortData,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    try {
      const [ektas, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta.findMany({
          select: {
            id: true,
            nama: true,
            bank: true,
            jenis_permintaan: true,
            surat_permintaan: true,
            personel: {
              select: {
                id: true,
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
              },
            },
          },
          where: {
            ...where,
            e_kta_pengajuan_id: {
              in: idArray,
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta.count({
          where: {
            ...where,
            e_kta_pengajuan_id: {
              in: idArray,
            },
          },
        }),
      ]);

      const queryResult = await Promise.all(
        ektas.map(
          async ({
            personel,
            id,
            nama,
            bank,
            jenis_permintaan,
            surat_permintaan,
          }) => {
            return {
              id: id,
              personel_id: personel?.id || '',
              nama_lengkap: nama ?? personel.nama_lengkap,
              nrp: personel?.nrp || '',
              bank: bank || '',
              foto_file:
                (await this.minioService.convertFileKeyToURL(
                  `${process.env.MINIO_BUCKET_NAME}`,
                  `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
                )) ?? '',
              pangkat: personel?.pangkat_personel?.[0]?.pangkat?.nama || '',
              pangkat_singkat:
                personel?.pangkat_personel?.[0]?.pangkat?.nama_singkat || '',
              jabatan: personel?.jabatan_personel?.[0]?.jabatans?.nama || '',
              satuan:
                personel?.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
              jenis_permintaan: jenis_permintaan == 1 ? 'Baru' : 'Penggantian',
              surat_permintaan: surat_permintaan || '',
            };
          },
        ),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getSuratList(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      kode: { field: 'kode', type: 'string' },
      satuan: { field: 'satuan', type: 'string' },
      bank: { field: 'bank', type: 'string' },
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
      ['nama'],
    );

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    try {
      let listSatuan;

      if (
        approvalRole.toLowerCase() == 'operator level 3 e-kta' ||
        approvalRole.toLowerCase() == 'approver level 3 e-kta'
      ) {
        listSatuan = (await this.getListSatuan(req.user.satuan_id)).filter(
          (item) => item.id !== req.user.satuan_id,
        );

        where.satuan_id = { in: listSatuan.map((item) => item.id) };
        where.level_asal = 3;
        where.level_tujuan = 3;
      } else if (
        approvalRole.toLowerCase() == 'operator level 2 e-kta' ||
        approvalRole.toLowerCase() == 'approver level 2 e-kta'
      ) {
        listSatuan = (await this.getListSatuan12(req.user.satuan_id)).filter(
          (item) => item.id !== req.user.satuan_id,
        );

        where.satuan_id = { in: listSatuan.map((item) => item.id) };
        where.level_asal = 3;
        where.level_tujuan = 2;
      } else if (
        approvalRole.toLowerCase() == 'operator level 1 e-kta' ||
        approvalRole.toLowerCase() == 'approver level 1 e-kta' ||
        approvalRole.toLowerCase() == 'superadmin'
      ) {
        console.log('OP1');
        where.satuan_id = { not: BigInt(req.user.satuan_id ?? 1) };
        where.level_asal = 2;
        where.level_tujuan = 1;
      }

      const [groupSuratEkta, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta_surat.findMany({
          select: {
            id: true,
            nomor_surat: true,
            dokumen_surat: true,
            satuan_id: true,
            nama_satuan: true,
            status: true,
            level_asal: true,
            level_tujuan: true,
            created_at: true,
            created_by: true,
          },
          where: {
            ...where,
            // satuan_id: { in: listSatuan.map((item) => item.id) },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta_surat.count({
          where: {
            ...where,
            // satuan_id: { in: listSatuan.map((item) => item.id) },
            // status: false,
          },
        }),
      ]);

      const queryResult = await Promise.all(
        groupSuratEkta.map(async (data) => {
          const totalPengajuanBaru = await this.prisma.e_kta_surat_detail.count(
            {
              where: {
                e_kta_surat_id: data.id,
                e_kta: {
                  jenis_permintaan: 1, // pengajuan baru
                  deleted_at: null,
                },
              },
            },
          );

          const totalPenggantian = await this.prisma.e_kta_surat_detail.count({
            where: {
              e_kta_surat_id: data.id,
              e_kta: {
                jenis_permintaan: 2, // penggantian
                deleted_at: null,
              },
            },
          });

          const totalPengajuan = totalPengajuanBaru + totalPenggantian;

          return {
            id: data.id,
            nomor_surat: data?.nomor_surat || '',
            dokumen_surat: data?.dokumen_surat || '',
            satuan_id: data?.satuan_id || '',
            nama_satuan: data?.nama_satuan || '',
            pengajuan_baru: totalPengajuanBaru || 0,
            penggantian: totalPenggantian || 0,
            total_pengajuan: totalPengajuan || 0,
            status: data.status || false,
            created_at: data?.created_at || '',
            created_by: data?.created_by || '',
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getDataSuratDetail(
    req: any,
    idArray,
    paginationData,
    searchandsortData,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string';
      };
    } = {
      kode: { field: 'kode', type: 'string' },
      satuan: { field: 'satuan', type: 'string' },
      bank: { field: 'ekta_bank', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
      ['nama'],
    );

    try {
      const [ektas, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta.findMany({
          select: {
            id: true,
            nama: true,
            bank: true,
            jenis_permintaan: true,
            surat_permintaan: true,
            personel: {
              select: {
                id: true,
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: { nama: true, nama_singkat: true },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: { select: { nama: true } },
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
              },
            },
          },
          where: {
            ...where,
            personel: {
              jabatan_personel: {
                some: {
                  is_aktif: true,
                },
              },
              pangkat_personel: {
                some: {
                  is_aktif: true,
                },
              },
            },
            e_kta_surat_detail: {
              some: {
                e_kta_surat_id: {
                  in: idArray,
                },
                e_kta_surat: {
                  status: false,
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta.count({
          where: {
            ...where,
            e_kta_surat_detail: {
              some: {
                e_kta_surat_id: {
                  in: idArray,
                },
                e_kta_surat: {
                  status: false,
                },
              },
            },
          },
        }),
      ]);

      const queryResult = await Promise.all(
        ektas.map(
          async ({
            personel,
            id,
            nama,
            bank,
            jenis_permintaan,
            surat_permintaan,
          }) => {
            return {
              id: id,
              personel_id: personel?.id || '',
              nama_lengkap: nama ?? personel.nama_lengkap,
              nrp: personel?.nrp || '',
              bank: bank || '',
              foto_file:
                (await this.minioService.convertFileKeyToURL(
                  `${process.env.MINIO_BUCKET_NAME}`,
                  `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
                )) ?? '',
              pangkat: personel?.pangkat_personel?.[0]?.pangkat?.nama || '',
              pangkat_singkat:
                personel?.pangkat_personel?.[0]?.pangkat?.nama_singkat || '',
              jabatan: personel?.jabatan_personel?.[0]?.jabatans?.nama || '',
              satuan:
                personel?.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
              jenis_permintaan: jenis_permintaan == 1 ? 'Baru' : 'Penggantian',
              surat_permintaan: surat_permintaan || '',
            };
          },
        ),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async findBankFromParentSatuan(satuanId: number) {
    let currentSatuan = await this.prisma.satuan.findUnique({
      where: { id: satuanId },
      select: {
        id: true,
        nama: true,
        atasan_id: true,
        satuan_bank: {
          select: {
            bank: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
        },
      },
    });

    while (currentSatuan) {
      if (currentSatuan.satuan_bank?.[0]?.bank) {
        return currentSatuan.satuan_bank[0].bank;
      }
      if (!currentSatuan.atasan_id) break;
      currentSatuan = await this.prisma.satuan.findUnique({
        where: { id: currentSatuan.atasan_id },
        select: {
          id: true,
          nama: true,
          atasan_id: true,
          satuan_bank: {
            select: {
              bank: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
          },
        },
      });
    }

    return null;
  }

  async getDataPersonel(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { nama, nrp, jabatan, pangkat, orderBy } = searchandsortData;

    const pangkatArray =
      pangkat && pangkat !== 'undefined' && pangkat !== 'null'
        ? JSON.parse(pangkat).map((id) => BigInt(id))
        : undefined;

    const where: Prisma.personelWhereInput = {};

    if (nama) {
      where.nama_lengkap = {
        contains: nama,
        mode: 'insensitive',
      };
    }

    if (nrp) {
      where.nrp = {
        contains: nrp,
        mode: 'insensitive',
      };
    }

    const orderByClause: Prisma.personelOrderByWithRelationInput = {};

    if (orderBy) {
      const [field, direction] = orderBy.split(':');

      if (['asc', 'desc'].includes(direction) && field) {
        // Handle ordering by nivellering_id from jabatans
        if (field === 'nivellering_id') {
          orderByClause.jabatan_personel = {
            _count: 'desc', // This is a workaround, see better solution below
          };
        } else {
          orderByClause[field] = direction;
        }
      }
    }

    // Better approach: Add default ordering by nivellering_id
    // You can also add this as a secondary sort or primary sort
    if (!orderBy || orderBy.split(':')[0] === 'nivellering_id') {
      orderByClause.jabatan_personel = {
        _count: 'desc', // Workaround - see note below about limitations
      };
    }

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    try {
      let listSatuan;
      let shouldFilterBySatuan = true; // Flag untuk menentukan apakah perlu filter satuan

      if (approvalRole.toLowerCase() == 'operator level 3 e-kta') {
        listSatuan = await this.getListSatuan(req.user.satuan_id);
      } else if (
        approvalRole.toLowerCase() == 'operator level 1 e-kta' ||
        approvalRole.toLowerCase() == 'superadmin'
      ) {
        // Untuk role ini, tidak perlu filter satuan - bisa akses semua
        shouldFilterBySatuan = false;
        listSatuan = []; // Tidak digunakan karena shouldFilterBySatuan = false
      } else {
        listSatuan = [{ id: req.user.satuan_id }];
      }

      // Buat kondisi where untuk jabatan_personel
      const jabatanPersonelWhere: any = {
        is_aktif: true,
      };

      // Kondisi jabatan dengan filter satuan jika diperlukan
      const jabatansCondition: any = {
        nama: {
          contains: jabatan,
          mode: 'insensitive',
        },
      };

      // Tambahkan filter satuan hanya jika diperlukan
      if (shouldFilterBySatuan && listSatuan.length > 0) {
        jabatansCondition.satuan_id = { in: listSatuan.map((item) => item.id) };
      }

      jabatanPersonelWhere.jabatans = jabatansCondition;

      const [listPersonel, totalCount] = await Promise.all([
        this.prisma.personel.findMany({
          select: {
            id: true,
            nama_lengkap: true,
            nrp: true,
            foto_file: true,
            dikum_personel: {
              select: {
                dikum_detail: {
                  select: {
                    gelar: {
                      select: {
                        nama: true,
                        is_aktif: true,
                        is_gelar_belakang: true,
                      },
                    },
                  },
                  where: {
                    gelar_id: {
                      not: null,
                    },
                  },
                },
              },
            },
            pangkat_personel: {
              select: {
                pangkat: {
                  select: {
                    id: true,
                    nama: true,
                    nama_singkat: true,
                  },
                },
                tmt: true,
              },
              where: {
                is_aktif: true,
                pangkat: {
                  id: {
                    in: pangkatArray,
                  },
                },
              },
            },
            jabatan_personel: {
              select: {
                jabatans: {
                  select: {
                    id: true,
                    nama: true,
                    nivellering_id: true,
                    satuan: {
                      select: {
                        id: true,
                        nama: true,
                        satuan_bank: {
                          select: {
                            bank: {
                              select: {
                                id: true,
                                nama: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
                tmt_jabatan: true,
              },
              where: jabatanPersonelWhere,
              // Add ordering at relation level
              orderBy: {
                jabatans: {
                  nivellering_id: 'asc',
                },
              },
            },
          },
          where: {
            ...where,
            pangkat_personel: {
              some: {
                is_aktif: true,
                pangkat: {
                  id: {
                    in: pangkatArray,
                  },
                },
              },
            },
            jabatan_personel: {
              some: jabatanPersonelWhere,
            },
            dikum_personel: {
              some: {
                dikum_detail: {
                  gelar_id: {
                    not: null,
                  },
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: {
            ...orderByClause,
          },
        }),
        this.prisma.personel.count({
          where: {
            ...where,
            pangkat_personel: {
              some: {
                is_aktif: true,
                pangkat: {
                  id: {
                    in: pangkatArray,
                  },
                },
              },
            },
            jabatan_personel: {
              some: jabatanPersonelWhere,
            },
          },
        }),
      ]);

      // Sort the results by nivellering_id in JavaScript if Prisma ordering doesn't work as expected
      const sortedPersonel = listPersonel.sort((a, b) => {
        const aLevel = a.jabatan_personel?.[0]?.jabatans?.nivellering_id || 0;
        const bLevel = b.jabatan_personel?.[0]?.jabatans?.nivellering_id || 0;
        return Number(aLevel) - Number(bLevel); // ascending order
      });

      const queryResult = await Promise.all(
        sortedPersonel.map(async (personel) => {
          let bank =
            personel.jabatan_personel?.[0]?.jabatans?.satuan?.satuan_bank?.[0]
              ?.bank;

          if (!bank) {
            const satuanId =
              personel.jabatan_personel?.[0]?.jabatans?.satuan?.id;
            if (satuanId) {
              bank = await this.findBankFromParentSatuan(Number(satuanId));
            }
          }

          const satuanParent =
            await this.prisma.mv_satuan_with_top_parents.findFirst({
              where: {
                id: personel.jabatan_personel?.[0]?.jabatans?.satuan?.id,
              },
            });

          let satuanEktaId;
          let satuanEktaNama;
          if (
            satuanParent.third_top_parent_nama !==
              satuanParent.second_top_parent_nama &&
            satuanParent.second_top_parent_nama !== satuanParent.top_parent_nama
          ) {
            satuanEktaId = satuanParent.second_top_parent_id;
            satuanEktaNama = satuanParent.second_top_parent_nama;
          } else if (
            satuanParent.third_top_parent_nama ===
              satuanParent.second_top_parent_nama &&
            satuanParent.second_top_parent_nama !== satuanParent.top_parent_nama
          ) {
            satuanEktaId = satuanParent.second_top_parent_id;
            satuanEktaNama = satuanParent.second_top_parent_nama;
          } else if (
            satuanParent.third_top_parent_nama ===
              satuanParent.second_top_parent_nama &&
            satuanParent.second_top_parent_nama === satuanParent.top_parent_nama
          ) {
            satuanEktaId = satuanParent.top_parent_id;
            satuanEktaNama = satuanParent.top_parent_nama;
          }

          // Filter gelar yang valid
          const dikumDetailList = personel.dikum_personel
            ?.map((d) => d.dikum_detail?.gelar)
            .filter((gelar) => gelar && gelar.nama && gelar.is_aktif);

          const gelarDepan = dikumDetailList
            ?.filter((g) => !g.is_gelar_belakang)
            .map((g) => g.nama)
            .join(' ');

          const gelarBelakang = dikumDetailList
            ?.filter((g) => g.is_gelar_belakang)
            .map((g) => g.nama)
            .join(', ');

          const namaDenganGelar = `${gelarDepan ? gelarDepan + ' ' : ''}${personel.nama_lengkap}${gelarBelakang ? ', ' + gelarBelakang : ''}`;

          return {
            personel_id: personel.id,
            nama_lengkap: personel.nama_lengkap,
            nama_dengan_gelar: namaDenganGelar,
            nrp: personel.nrp,
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
              )) ?? '',
            pangkat_id: personel?.pangkat_personel[0]?.pangkat?.id || '',
            pangkat_nama: personel?.pangkat_personel[0]?.pangkat?.nama || '',
            pangkat_nama_singkat:
              personel?.pangkat_personel[0]?.pangkat?.nama_singkat || '',
            jabatan_id: personel.jabatan_personel?.[0]?.jabatans?.id || '',
            jabatan_nama: personel.jabatan_personel?.[0]?.jabatans?.nama || '',
            satuan_id:
              personel.jabatan_personel?.[0]?.jabatans?.satuan?.id || '',
            satuan_nama:
              personel.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
            bank_id: bank?.id || null,
            bank_nama: bank?.nama || null,
            nivellering_id:
              personel.jabatan_personel?.[0]?.jabatans?.nivellering_id || null,
            satuan_ekta_id: satuanEktaId,
            satuan_ekta_nama: satuanEktaNama,
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async create(body: CreateDto, req: any) {
    const { id_array } = body;
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );

    const approvalHistory = [
      {
        approval: matchedRoles[0].nama.toLowerCase(),
        status: 'PENDING',
        comment: '',
        created_date: new Date().toISOString(),
        created_by: req.user.personel_id,
      },
    ];

    try {
      const existingPending = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          personel_id: true,
        },
        where: {
          personel_id: { in: id_array },
          status: false,
        },
      });

      if (existingPending.length > 0) {
        const existingPersonelIds = existingPending.map((p) => p.personel_id);
        throw new ConflictException(
          `Some personel already exist: ${existingPersonelIds.join(', ')}`,
        );
      }

      if (existingPending.length > 200) {
        throw new UnprocessableEntityException(
          `Cannot create more than 200 records at once`,
        );
      }

      const listPersonel = await this.prisma.personel.findMany({
        select: {
          id: true,
          nama_lengkap: true,
          nrp: true,
          foto_file: true,
          pangkat_personel: {
            select: {
              pangkat: { select: { nama: true } },
            },
            orderBy: { tmt: 'desc' },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                      satuan_bank: {
                        select: {
                          bank: { select: { id: true, nama: true } },
                        },
                      },
                    },
                  },
                },
              },
              tmt_jabatan: true,
            },
            orderBy: { tmt_jabatan: 'desc' },
            take: 1,
          },
        },
        where: {
          id: {
            in: id_array,
          },
        },
      });

      if (listPersonel.length !== id_array.length) {
        throw new NotFoundException('Some personel not found');
      }

      const existingRejected = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          personel_id: true,
        },
        where: {
          personel_id: { in: id_array },
          approval_history: {
            array_contains: [
              {
                approval: matchedRoles[0].nama.toLowerCase(),
                status: 'REJECTED',
              },
            ],
          },
        },
      });

      const updatePromises = existingRejected.map((personel) =>
        this.prisma.e_kta.update({
          where: { id: personel.id },
          data: {
            approval_history: approvalHistory,
          },
        }),
      );

      const existingPersonelIds = existingRejected.map((p) => p.personel_id);
      const newPersonel = listPersonel.filter(
        (p) => !existingPersonelIds.includes(p.id),
      );

      const createPersonel = await Promise.all(
        newPersonel.map(async (personel) => {
          let bank =
            personel.jabatan_personel?.[0]?.jabatans?.satuan?.satuan_bank?.[0]
              ?.bank;

          if (!bank) {
            const satuanId =
              personel.jabatan_personel?.[0]?.jabatans?.satuan?.id;
            if (satuanId) {
              bank = await this.findBankFromParentSatuan(Number(satuanId));
            }
          }

          return {
            personel_id: personel.id,
            tanggal: new Date(),
            status: false,
            approval_history: approvalHistory,
            created_by: BigInt(req.user.personel_id),
            bank: bank?.nama || null,
          };
        }),
      );

      await this.prisma.$transaction([
        ...updatePromises,
        this.prisma.e_kta.createMany({ data: createPersonel }),
      ]);

      return { updated: updatePromises.length, created: createPersonel.length };
    } catch (err) {
      throw err;
    }
  }

  async ajukanUlang(body: CreateDto, req: any) {
    const { id_array } = body;
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );

    const approvalHistory = [
      {
        approval: matchedRoles[0].nama.toLowerCase(),
        status: 'PENDING',
        comment: '',
        created_date: new Date().toISOString(),
        created_by: req.user.personel_id,
      },
    ];

    try {
      const ekta = await this.prisma.e_kta.findMany({
        where: {
          id: { in: id_array },
          // status: false,
        },
      });

      if (ekta.length != id_array.length) {
        throw new NotFoundException(`Some e-kta not found`);
      }

      const reUpdated = await this.prisma.e_kta.updateMany({
        where: { id: { in: id_array } },
        data: {
          approval_history: approvalHistory,
          nama: null,
          surat_permintaan: null,
          e_kta_pengajuan_id: null,
          e_kta_batch_id: null,
        },
      });

      return reUpdated;
    } catch (err) {
      throw err;
    }
  }

  async auditPersonelOperator(
    req: any,
    id: string,
    body: AuditPersonelOperatorDto,
    file: Express.Multer.File,
  ) {
    const { nama, jenis_permintaan } = body;
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    if (jenis_permintaan == 2 && !file) {
      throw new UnprocessableEntityException('Surat permintaan is required');
    }

    try {
      const ekta = await this.prisma.e_kta.findUnique({
        where: { id: BigInt(id) },
      });

      if (!ekta) {
        throw new NotFoundException('E-KTA not found');
      }

      let resultFile;
      if (jenis_permintaan == 2 && file) {
        const uploadResult = await this.minioService.uploadFile(file);

        resultFile = {
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          key: uploadResult.Key || '',
          url: uploadResult.Location || '',
          filename: uploadResult.filename || '',
        };
      }

      let existingHistory = [];
      if (ekta.approval_history) {
        existingHistory = Array.isArray(ekta.approval_history)
          ? ekta.approval_history
          : JSON.parse(ekta.approval_history as unknown as string);
      }

      const updatedApprovalHistory = existingHistory.map((data) => {
        if (data.approval.toLowerCase() == approvalRole.toLowerCase()) {
          return {
            ...data,
            status: 'SUCCESS',
            created_date: new Date().toISOString(),
            created_by: req.user.personel_id,
          };
        }
        return data;
      });

      const queryResult = await this.prisma.e_kta.update({
        where: { id: BigInt(id) },
        data: {
          nama: nama,
          approval_history: updatedApprovalHistory,
          jenis_permintaan: jenis_permintaan,
          surat_permintaan: resultFile,
          updated_at: new Date(),
          updated_by: req.user.personel_id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async submitOperator(body: SubmitOperatorDto, req) {
    const { id_array } = body;
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const updatedDate = new Date().toISOString();

    try {
      const ekta = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          e_kta_batch_id: true,
          e_kta_surat_detail: {
            select: {
              e_kta_surat: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
          },
        },
        where: {
          id: {
            in: id_array,
          },
        },
      });

      if (ekta.length !== id_array.length) {
        throw new NotFoundException('Some E-KTA not found');
      }

      let satuanId = req.user.satuan_id;
      if (approvalRole.toLowerCase() == 'operator level 1 e-kta') {
        satuanId = 1;
      }

      const queryResult = await this.prisma.$transaction(async (tx) => {
        let existingPengajuan = await tx.e_kta_pengajuan.findFirst({
          where: {
            satuan_id: satuanId,
            tanggal_pengajuan: new Date(),
            status: false,
          },
        });

        if (!existingPengajuan) {
          existingPengajuan = await tx.e_kta_pengajuan.create({
            data: {
              satuan_id: satuanId,
              tanggal_pengajuan: new Date(),
              status: false,
            },
          });
        }

        await tx.e_kta.updateMany({
          where: { id: { in: ekta.map((p) => p.id) } },
          data: {
            e_kta_pengajuan_id: existingPengajuan.id,
          },
        });

        await tx.$executeRawUnsafe(
          `
            UPDATE e_kta
            SET approval_history = (SELECT jsonb_agg(
              CASE
                WHEN approval ->>'approval' = ANY($1::text[]) THEN
                approval || jsonb_build_object(
                  'status', 'SUBMITTED',
                  'created_date', $2
                )
                ELSE approval
              END
            )
            FROM jsonb_array_elements(e_kta.approval_history) AS approval)
            WHERE id = ANY ($3::bigint[])
          `,
          [approvalRole],
          updatedDate,
          ekta.map((p) => p.id),
        );

        const ektaSuratIds = ekta
          .flatMap((p) => p.e_kta_surat_detail.map((s) => s.e_kta_surat?.id))
          .filter((id) => id !== undefined);

        if (ektaSuratIds.length > 0) {
          await tx.e_kta_surat.updateMany({
            where: { id: { in: ektaSuratIds } },
            data: { status: true },
          });
        }

        return existingPengajuan;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async submitApproval(
    body: SubmitApprovalDto,
    req: any,
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new UnprocessableEntityException('Signature is required');
    }

    const { data_array } = body;
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const approvalMappings = {
      'approver level 3 e-kta': 'operator level 2 e-kta',
      'approver level 2 e-kta': 'operator level 1 e-kta',
    };

    let prefixSatuanSurat: string;
    let levelAsal: number;
    let levelTujuan: number;
    if (
      req.user.roles.some(
        (role) => role.nama.toLowerCase() == 'approver level 3 e-kta',
      )
    ) {
      levelAsal = 3;
      levelTujuan = 2;
      const parentSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: req.user.satuan_id,
          },
        });
      prefixSatuanSurat = `${parentSatuan.third_top_parent_nama}/${parentSatuan.second_top_parent_nama}`;
    } else if (
      req.user.roles.some(
        (role) => role.nama.toLowerCase() == 'approver level 2 e-kta',
      )
    ) {
      levelAsal = 2;
      levelTujuan = 1;
      prefixSatuanSurat = req.user.satuan;
    } else {
      levelAsal = 1;
      levelTujuan = 1;
      prefixSatuanSurat = `POLRI`;
    }

    try {
      const ektaList = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          approval_history: true,
          e_kta_pengajuan_id: true,
          jenis_permintaan: true,
          personel: {
            select: {
              id: true,
              nama_lengkap: true,
              nrp: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: { nama: true, nama_singkat: true },
                  },
                },
                where: {
                  is_aktif: true,
                },
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: { select: { nama: true } },
                    },
                  },
                },
                where: {
                  is_aktif: true,
                },
              },
            },
          },
          bank: true,
        },
        where: {
          id: {
            in: data_array.map((item) => item.id),
          },
          personel: {
            jabatan_personel: {
              some: {
                is_aktif: true,
              },
            },
            pangkat_personel: {
              some: {
                is_aktif: true,
              },
            },
          },
        },
      });

      if (ektaList.length !== data_array.length) {
        throw new NotFoundException('Some E-KTA not found');
      }

      let satuanNama = req.user.satuan;
      let satuanId = req.user.satuan_id;
      let sendTo = null;
      if (approvalRole.toLowerCase() == 'approver level 1 e-kta') {
        satuanNama = 'POLRI';
        satuanId = 1;

        const uniqueBanks = [
          ...new Set(ektaList.map((item) => item.bank).filter(Boolean)),
        ];
        const bankString = uniqueBanks.join('/');

        sendTo = `KEPADA DIREKTUR UTAMA BANK ${bankString}`;
      } else if (approvalRole.toLowerCase() == 'approver level 2 e-kta') {
        sendTo = 'AS SDM POLRI';
      } else {
        const atasanSatuan =
          await this.prisma.mv_satuan_with_top_parents.findFirst({
            select: {
              second_top_parent_nama: true,
            },
            where: { atasan_id: req.user.satuan_id },
          });

        sendTo = `KARO SDM ${atasanSatuan.second_top_parent_nama}`;
      }

      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const firstDayNextMonth = new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        1,
      );

      const checkPreviousBatch = await this.prisma.e_kta_surat.findFirst({
        where: {
          created_at: {
            gte: firstDayOfMonth,
            lt: firstDayNextMonth,
          },
        },
        orderBy: { id: 'desc' },
        select: { nomor_surat: true },
      });

      let seqNumber = 1;
      if (checkPreviousBatch) {
        const parts = checkPreviousBatch.nomor_surat.split('/');
        const maybeNumber = Number(parts[1]);

        if (!isNaN(maybeNumber)) {
          seqNumber = maybeNumber + 1;
        }
      }

      const modifiedSeqNumber = String(seqNumber).padStart(2, '0');

      const getCurrentRomanMonth = romanMonth();
      const getCurrentYear = new Date().getFullYear();

      const nomorSurat = `B/${modifiedSeqNumber}/${getCurrentRomanMonth}/EKTA/${prefixSatuanSurat}/${getCurrentYear}`;
      const uploadResult = await this.minioService.uploadFile(file);

      const updateResults = await this.prisma.$transaction(async (tx) => {
        const resultFile = {
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          key: uploadResult.Key || '',
          url: uploadResult.Location || '',
          filename: uploadResult.filename || '',
        };

        const allRejected = data_array.every(
          (item) => item.keterangan && item.keterangan.trim() !== '',
        );

        await tx.e_kta_pengajuan.updateMany({
          where: {
            id: {
              in: ektaList.map((item) => item.e_kta_pengajuan_id),
            },
          },
          data: {
            status: true,
          },
        });

        if (allRejected) {
          return {
            statusCode: HttpStatus.OK,
            message: 'Semua pengajuan ditolak. Surat tidak dibuat.',
            data: {
              total_ditolak: data_array.length,
              nama_operator: req.user.nama_operator,
              satuan: req.user.satuan,
              pangkat: req.user.pangkat,
              nrp: req.user.nrp,
            },
          };
        }

        const suratEkta = await tx.e_kta_surat.create({
          data: {
            nomor_surat: nomorSurat,
            satuan_id: satuanId,
            nama_satuan: satuanNama,
            level_asal: levelAsal,
            level_tujuan: levelTujuan,
            status: false,
            signature: resultFile,
            created_by: BigInt(req.user.personel_id),
            created_at: new Date(),
          },
        });

        const ektaWithEmptyKeterangan = ektaList.filter((item) => {
          const matchingData = data_array.find(
            (data) => BigInt(data.id) == item.id,
          );
          return (
            !matchingData?.keterangan || matchingData.keterangan.trim() == ''
          );
        });

        await tx.e_kta_surat_detail.createMany({
          data: ektaWithEmptyKeterangan.map((item) => ({
            e_kta_surat_id: suratEkta.id,
            e_kta_id: item.id,
            nama_lengkap: item.personel.nama_lengkap,
            nrp: item.personel.nrp,
            foto_file: item.personel.foto_file,
            pangkat: item.personel.pangkat_personel[0].pangkat.nama,
            jabatan: item.personel.jabatan_personel[0].jabatans.nama,
            bank: item.bank,
            jenis_permintaan:
              item.jenis_permintaan == 1 ? 'Baru' : 'Penggantian',
            created_at: new Date(),
          })),
        });

        let statusEkta = false;
        if (approvalRole.toLowerCase() == 'approver level 1 e-kta') {
          const dataArrayWithEmptyKeterangan = data_array.filter(
            (item) => !item.keterangan || item.keterangan.trim() == '',
          );

          statusEkta = true;

          await this.createBatch(
            { data_array: dataArrayWithEmptyKeterangan },
            req,
          );
        }

        const approvalUpdates = await Promise.all(
          ektaList.map(async (ekta) => {
            const itemData = data_array.find(
              (item) => BigInt(item.id) == ekta.id,
            );
            const hasRejection =
              itemData.keterangan && itemData.keterangan.trim() !== '';
            const nextApproval = hasRejection
              ? null
              : approvalMappings[matchedRoles[0].nama.toLowerCase()] || null;

            const approvalHistory = [
              {
                approval: matchedRoles[0].nama.toLowerCase(),
                status: hasRejection ? 'REJECTED' : 'SUBMITTED',
                comment: hasRejection ? itemData.keterangan : '',
                created_date: new Date().toISOString(),
                created_by: req.user.personel_id,
              },
            ];

            if (!hasRejection && nextApproval) {
              approvalHistory.push({
                approval: nextApproval,
                status: 'PENDING',
                comment: '',
                created_date: '',
                created_by: '',
              });
            }

            const existingHistory = Array.isArray(ekta.approval_history)
              ? ekta.approval_history
              : JSON.parse(ekta.approval_history as unknown as string) || [];

            const updatedHistory = [...existingHistory, ...approvalHistory];

            return tx.e_kta.update({
              where: { id: ekta.id },
              data: {
                approval_history: updatedHistory,
                status: statusEkta,
              },
            });
          }),
        );

        return { suratEkta, approvalUpdates };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_CREATE as ConstantLogType,
          message,
          updateResults,
        ),
      );

      const ektaListWithEmptyKeterangan = ektaList.filter((item) => {
        const matchingData = data_array.find(
          (data) => BigInt(data.id) == item.id,
        );
        return (
          !matchingData?.keterangan || matchingData.keterangan.trim() == ''
        );
      });

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: {
          surat: updateResults.suratEkta,
          total_pengajuan_baru: ektaListWithEmptyKeterangan.filter(
            (item) => item.jenis_permintaan == 1,
          ).length,
          total_penggantian: ektaListWithEmptyKeterangan.filter(
            (item) => item.jenis_permintaan == 2,
          ).length,
          total: ektaListWithEmptyKeterangan.length,
          nama_operator: req.user.nama_operator,
          satuan: req.user.satuan,
          pangkat: req.user.pangkat,
          nrp: req.user.nrp,
          jabatan_id: req.user.jabatan.id,
          jabatan_name: req.user.jabatan.nama,
          sent_to: sendTo,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async submitDocument(id, req, file) {
    if (!file) {
      throw new UnprocessableEntityException('Document is required');
    }

    try {
      const suratEkta = await this.prisma.e_kta_surat.findUnique({
        where: { id: BigInt(id) },
      });

      if (!suratEkta) {
        throw new NotFoundException('Surat E-KTA not found');
      }

      const uploadResult = await this.minioService.uploadFile(file);

      const resultFile = {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        key: uploadResult.Key || '',
        url: uploadResult.Location || '',
        filename: uploadResult.filename || '',
      };

      const queryResult = await this.prisma.e_kta_surat.update({
        where: { id: BigInt(id) },
        data: {
          dokumen_surat: resultFile,
          updated_at: new Date(),
          updated_by: req.user.personel_id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async submitApprovalOperator(body: SubmitApprovalDto, req: any) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;
    const updatedDate = new Date().toISOString();

    const successIds = body.data_array
      .filter((d) => !d.keterangan || d.keterangan.trim() == '')
      .map((d) => d.id);

    const rejectedIds = body.data_array
      .filter((d) => d.keterangan && d.keterangan.trim() !== '')
      .map((d) => d.id);

    let satuanId = req.user.satuan_id;
    if (approvalRole.toLowerCase() == 'operator level 1 e-kta') {
      satuanId = 1;
    }

    try {
      const ekta = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          e_kta_batch_id: true,
          e_kta_surat_detail: {
            select: {
              e_kta_surat: {
                select: {
                  id: true,
                  status: true,
                },
              },
            },
          },
        },
        where: {
          id: {
            in: [...successIds, ...rejectedIds],
          },
        },
      });

      if (ekta.length !== body.data_array.length) {
        throw new NotFoundException('Some E-KTA not found');
      }

      const queryResult = await this.prisma.$transaction(async (tx) => {
        let existingPengajuan = await tx.e_kta_pengajuan.findFirst({
          where: {
            satuan_id: satuanId,
            tanggal_pengajuan: new Date(),
            status: false,
          },
        });

        if (!existingPengajuan) {
          existingPengajuan = await tx.e_kta_pengajuan.create({
            data: {
              satuan_id: satuanId,
              tanggal_pengajuan: new Date(),
              status: false,
            },
          });
        }

        if (successIds.length > 0) {
          await tx.e_kta.updateMany({
            where: { id: { in: successIds } },
            data: { e_kta_pengajuan_id: existingPengajuan.id },
          });
        }

        await tx.$executeRawUnsafe(
          `
            UPDATE e_kta
            SET approval_history = (SELECT jsonb_agg(
              CASE
                WHEN approval ->>'approval' = ANY($1::text[]) THEN
                approval || jsonb_build_object(
                'status',
                CASE
                  WHEN e_kta.id = ANY ($2::bigint[]) THEN 'SUBMITTED'
                  WHEN e_kta.id = ANY ($3::bigint[]) THEN 'REJECTED'
                  ELSE approval ->> 'status'
                END,
                'created_by', $4,
                'created_date', $5
                )
                ELSE approval
              END
            )
            FROM jsonb_array_elements(e_kta.approval_history) AS approval)
            WHERE id = ANY ($6::bigint[]);
          `,
          [approvalRole],
          successIds,
          rejectedIds,
          req.user.personel_id,
          updatedDate,
          [...successIds, ...rejectedIds],
        );

        const ektaSuratIds = ekta
          .filter((p) => successIds.includes(Number(p.id)))
          .flatMap((p) =>
            p.e_kta_surat_detail
              .filter((s) => successIds.includes(Number(p.id)))
              .map((s) => s.e_kta_surat?.id),
          )
          .filter((id) => id !== undefined);

        if (ektaSuratIds.length > 0) {
          await tx.e_kta_surat.updateMany({
            where: { id: { in: ektaSuratIds } },
            data: { status: true },
          });
        }

        return existingPengajuan;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(body: CreateDto, req: any) {
    const { id_array } = body;

    try {
      const queryResult = await this.prisma.e_kta.findMany({
        where: {
          id: {
            in: id_array,
          },
        },
      });

      if (queryResult.length !== id_array.length) {
        throw new NotFoundException('Some E-KTA not found');
      }

      await this.prisma.e_kta.deleteMany({
        where: {
          id: {
            in: id_array,
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async createBatch(body: SubmitApprovalDto, req: any) {
    const { data_array } = body;

    try {
      const ekta = await this.prisma.e_kta.findMany({
        select: {
          id: true,
          e_kta_batch_id: true,
          bank: true,
          e_kta_surat_detail: {
            select: {
              e_kta_surat: {
                select: {
                  id: true,
                  status: true,
                  satuan_id: true,
                  nomor_surat: true,
                },
              },
            },
          },
        },
        where: {
          id: {
            in: data_array.map((item) => item.id),
          },
        },
      });

      const filteredEkta = ekta.map((item) => {
        const lowestSurat = item.e_kta_surat_detail.reduce(
          (min, curr) =>
            !min || curr.e_kta_surat.id < min.e_kta_surat.id ? curr : min,
          null,
        );

        return {
          id: item.id,
          bank: item.bank ?? '-',
          satuan_id: lowestSurat?.e_kta_surat.satuan_id ?? 1,
        };
      });

      // Group berdasarkan kombinasi satuan_id + bank
      const groupedMap = filteredEkta.reduce((acc, item) => {
        const key = `${item.satuan_id}-${item.bank}`;
        if (!acc.has(key)) {
          acc.set(key, {
            satuan_id: Number(item.satuan_id),
            bank: item.bank,
            personel: new Set<number>(),
          });
        }
        acc.get(key).personel.add(Number(item.id));
        return acc;
      }, new Map<string, { satuan_id: number; bank: string; personel: Set<number> }>());

      const groupedArray = Array.from(groupedMap.values()).map((group) => ({
        satuan_id: group.satuan_id,
        bank: group.bank,
        personel: Array.from(group.personel),
      }));

      const getCurrentRomanMonth = romanMonth();
      const getCurrentYear = new Date().getFullYear();

      const batchInsertPromises = groupedArray.map(async (group) => {
        const satuan = await this.prisma.satuan.findFirst({
          where: { id: group.satuan_id },
          select: { nama: true },
        });

        const namaSatuan =
          group.satuan_id == 1 ? 'POLRI' : (satuan?.nama ?? 'UNKNOWN');
        const formattedSatuan = namaSatuan.replace(/\s+/g, '-');
        const jalurPengajuan = group.satuan_id == 1 ? 'KHUSUS' : 'BIASA';

        // Cek nomor urut berdasarkan bank + satuan + jalur pengajuan bulan ini
        const previousBatch = await this.prisma.e_kta_batch.findFirst({
          where: {
            created_at: { gte: new Date(new Date().setDate(1)) },
            nama_bank: group.bank,
            satuan_id: group.satuan_id,
          },
          orderBy: { id: 'desc' },
          select: { kode: true },
        });

        const lastSeq = previousBatch?.kode?.split('/')?.[3] ?? '0';
        const seqNumber = Number(lastSeq) + 1;
        const modifiedSeqNumber = String(seqNumber).padStart(2, '0');

        const kodeBatch = `${group.bank.toUpperCase()}-${formattedSatuan}/${jalurPengajuan}/EKTA/${modifiedSeqNumber}/${getCurrentRomanMonth}/${getCurrentYear}`;

        const jumlahKartu = group.personel.length;

        const newBatch = await this.prisma.e_kta_batch.create({
          data: {
            kode: kodeBatch,
            nama_bank: group.bank,
            nama_satuan: namaSatuan,
            satuan_id: group.satuan_id,
            created_by: BigInt(req.user.personel_id),
            created_at: new Date(),
            jumlah_kartu: jumlahKartu,
          },
        });

        await this.prisma.e_kta.updateMany({
          where: { id: { in: group.personel } },
          data: { e_kta_batch_id: newBatch.id },
        });

        return newBatch;
      });

      const insertedBatches = await Promise.all(batchInsertPromises);
      return insertedBatches;
    } catch (err) {
      throw err;
    }
  }

  async getRiwayatList(req: any, paginationData, searchandsortData) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      kode: { field: 'kode', type: 'string' },
      satuan: { field: 'satuan', type: 'string' },
      bank: { field: 'bank', type: 'string' },
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
      ['nama'],
    );

    let satuanId = req.user.satuan_id;

    if (
      req.user.roles.some(
        (role) => role.nama.toLowerCase() == 'operator level 1 e-kta',
      ) ||
      req.user.roles.some(
        (role) => role.nama.toLowerCase() == 'approver level 1 e-kta',
      ) ||
      req.user.roles.some((role) => role.nama.toLowerCase() == 'superadmin')
    ) {
      satuanId = 1;
    }

    try {
      const [groupSuratEkta, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta_surat.findMany({
          select: {
            id: true,
            nomor_surat: true,
            dokumen_surat: true,
            satuan_id: true,
            nama_satuan: true,
            status: true,
            level_asal: true,
            level_tujuan: true,
            created_at: true,
            created_by: true,
          },
          where: {
            ...where,
            satuan_id: satuanId,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta_surat.count({
          where: {
            ...where,
            satuan_id: satuanId,
          },
        }),
      ]);

      const queryResult = await Promise.all(
        groupSuratEkta.map(async (data) => {
          const totalPengajuanBaru = await this.prisma.e_kta_surat_detail.count(
            {
              where: {
                e_kta_surat_id: data.id,
                e_kta: {
                  jenis_permintaan: 1, // pengajuan baru
                  deleted_at: null,
                },
              },
            },
          );

          const totalPenggantian = await this.prisma.e_kta_surat_detail.count({
            where: {
              e_kta_surat_id: data.id,
              e_kta: {
                jenis_permintaan: 2, // penggantian
                deleted_at: null,
              },
            },
          });

          const totalPengajuan = totalPengajuanBaru + totalPenggantian;

          return {
            id: data.id,
            nomor_surat: data?.nomor_surat || '',
            dokumen_surat: data?.dokumen_surat || '',
            satuan_id: data?.satuan_id || '',
            nama_satuan: data?.nama_satuan || '',
            pengajuan_baru: totalPengajuanBaru || 0,
            penggantian: totalPenggantian || 0,
            total_pengajuan: totalPengajuan || 0,
            status: data.status || false,
            created_at: data?.created_at || '',
            created_by: data?.created_by || '',
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getRiwayatListById(
    req: any,
    idArray,
    paginationData,
    searchandsortData,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string';
      };
    } = {
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      jabatan: { field: 'jabatan', type: 'string' },
      bank: { field: 'bank', type: 'string' },
      nrp: { field: 'nrp', type: 'string' },
      jenis_permintaan: { field: 'jenis_permintaan', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
      ['nama'],
    );

    try {
      const [ektas, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta_surat_detail.findMany({
          select: {
            id: true,
            e_kta_id: true,
            e_kta_surat_id: true,
            nama_lengkap: true,
            bank: true,
            jabatan: true,
            pangkat: true,
            jenis_permintaan: true,
            nrp: true,
            foto_file: true,
            created_at: true,
          },
          where: {
            ...where,
            e_kta_surat_id: {
              in: idArray,
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta_surat_detail.count({
          where: {
            ...where,
            e_kta_surat_id: {
              in: idArray,
            },
          },
        }),
      ]);

      const queryResult = await Promise.all(
        ektas.map(async (data) => {
          return {
            id: data.id,
            e_kta_id: data.e_kta_id,
            e_kta_surat_id: data.e_kta_surat_id,
            nama_lengkap: data.nama_lengkap || '',
            nrp: data.nrp || '',
            bank: data.bank || '',
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${data.nrp}/${data.foto_file}`,
              )) ?? '',
            pangkat_singkat: data?.pangkat || '',
            jabatan: data.jabatan || '',
            jenis_permintaan: data.jenis_permintaan || '',
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getBacthDistribusiList(
    req: any,
    paginationData,
    searchandsortData,
    status,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'date' | 'number';
      };
    } = {
      kode: { field: 'kode', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
      satuan_id: { field: 'satuan_id', type: 'number' },
      id: { field: 'id', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumnV3(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    let satuanIdChunks: number[][] = [[]];

    if (
      approvalRole !== 'approver level 1 e-kta' &&
      approvalRole !== 'operator level 1 e-kta' &&
      approvalRole !== 'superadmin'
    ) {
      const listSatuan = await this.getListSatuan(req.user.satuan_id);
      const satuanIds = listSatuan.map((item) => item.id);
      satuanIdChunks = chunk(satuanIds, 1000); // aman karena tidak lebih dari limit DB
    }

    if (status) {
      where.tracking_history = {
        array_contains: [
          {
            status: status,
          },
        ],
      };
    }

    try {
      let batchResults = [];
      let totalData = 0;

      // Jika tidak ada batasan satuan, jalankan sekali saja
      if (
        approvalRole === 'approver level 1 e-kta' ||
        approvalRole === 'operator level 1 e-kta' ||
        approvalRole === 'superadmin'
      ) {
        const [batch, total] = await this.prisma.$transaction([
          this.prisma.e_kta_batch.findMany({
            select: {
              id: true,
              kode: true,
              nama_bank: true,
              satuan_id: true,
              nama_satuan: true,
              created_at: true,
              created_by: true,
              jumlah_kartu: true,
              tracking_history: true,
              pic: true,
              no_telp: true,
              e_kta: {
                select: {
                  approval_history: true,
                },
              },
            },
            where: { ...where },
            take: +limit,
            skip: +limit * (+page - 1),
            orderBy,
          }),
          this.prisma.e_kta_batch.count({
            where: { ...where },
          }),
        ]);

        batchResults = batch;
        totalData = total;
      } else {
        // Jalankan per-chunk satuan
        for (const ids of satuanIdChunks) {
          const [chunkBatch, chunkTotal] = await this.prisma.$transaction([
            this.prisma.e_kta_batch.findMany({
              select: {
                id: true,
                kode: true,
                nama_bank: true,
                satuan_id: true,
                nama_satuan: true,
                created_at: true,
                created_by: true,
                jumlah_kartu: true,
                tracking_history: true,
                pic: true,
                no_telp: true,
                e_kta: {
                  select: {
                    approval_history: true,
                  },
                },
              },
              where: {
                ...where,
                satuan_id: { in: ids },
              },
              orderBy,
            }),
            this.prisma.e_kta_batch.count({
              where: {
                ...where,
                satuan_id: { in: ids },
              },
            }),
          ]);

          batchResults.push(...chunkBatch);
          totalData += chunkTotal;
        }

        // Pagination manual karena Prisma hanya bisa pagination per-query
        batchResults = batchResults.slice((page - 1) * limit, page * limit);
      }

      // post-processing data
      for (const item of batchResults) {
        if (!item.tracking_history) {
          const approvalHistories = item.e_kta
            .map((e) => e.approval_history as any[])
            .flat()
            .filter(Boolean);

          const lastApproval =
            approvalHistories.length > 0
              ? approvalHistories[approvalHistories.length - 1]
              : null;

          if (lastApproval) {
            const personel = await this.prisma.personel.findUnique({
              where: { id: lastApproval.created_by },
              select: {
                nama_lengkap: true,
                no_hp: true,
              },
            });

            if (personel) {
              item.pic = personel.nama_lengkap;
              item.no_telp = personel.no_hp;
            }
          }
        }
        delete item.e_kta;
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          batchResults,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData,
        data: batchResults,
      };
    } catch (err) {
      console.error('Error getBatchDistribusiList:', err);
      throw err;
    }
  }

  async updateStatusBatchDistribusi(
    id: string,
    req: any,
    body: UpdateStatusDto,
    file: Express.Multer.File,
  ) {
    const { status } = body;

    try {
      const uploadResult = await this.minioService.uploadFile(file);
      const resultFile = {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        key: uploadResult.Key || '',
        url: uploadResult.Location || '',
        filename: uploadResult.filename || '',
      };

      const batch = await this.prisma.e_kta_batch.findUnique({
        where: { id: BigInt(id) },
      });

      const newTracking = {
        status: status,
        bukti_kirim: resultFile,
        created_at: new Date(),
        created_by: req.user.personel_id,
        created_name: req.user.nama_operator,
      };

      const pic = await this.prisma.personel.findUnique({
        where: { id: req.user.personel_id },
        select: {
          nama_lengkap: true,
          no_hp: true,
        },
      });

      const updatedTrackingHistory = Array.isArray(batch.tracking_history)
        ? [...batch.tracking_history, newTracking]
        : [newTracking];

      const updatedBatch = await this.prisma.e_kta_batch.update({
        where: { id: BigInt(id) },
        data: {
          tracking_history: updatedTrackingHistory,
          pic: pic.nama_lengkap,
          no_telp: pic.no_hp,
          updated_at: new Date(),
          updated_by: req.user.personel_id,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          updatedTrackingHistory,
        ),
      );

      return updatedBatch;
    } catch (err) {
      throw err;
    }
  }

  async getBatchDistribusiDetail(
    req,
    idArray,
    paginationData,
    searchandsortData,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean';
      };
    } = {
      status: { field: 'status', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumnV3(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    try {
      const [ektas, totalData] = await this.prisma.$transaction([
        this.prisma.e_kta.findMany({
          select: {
            id: true,
            nama: true,
            bank: true,
            jenis_permintaan: true,
            surat_permintaan: true,
            approval_history: true,
            personel: {
              select: {
                id: true,
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                    tmt: true,
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                    tmt_jabatan: true,
                  },
                  orderBy: { tmt_jabatan: 'desc' },
                  take: 1,
                },
              },
            },
            e_kta_batch: {
              select: {
                id: true,
                pic: true,
                no_telp: true,
                tracking_history: true,
              },
            },
          },
          where: {
            ...where,
            e_kta_batch_id: {
              in: idArray,
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
        }),
        this.prisma.e_kta.count({
          where: {
            ...where,
            e_kta_batch_id: {
              in: idArray,
            },
          },
        }),
      ]);

      const queryResult = await Promise.all(
        ektas.map(async (ekta) => {
          const {
            personel,
            id,
            nama,
            bank,
            jenis_permintaan,
            surat_permintaan,
            e_kta_batch,
            approval_history,
          } = ekta;

          let approvalHistoryArray = Array.isArray(approval_history)
            ? (approval_history as any[])
            : [];

          let lastApproval =
            approvalHistoryArray[approvalHistoryArray.length - 1];

          if (
            e_kta_batch?.tracking_history &&
            Array.isArray(e_kta_batch.tracking_history)
          ) {
            approvalHistoryArray = e_kta_batch.tracking_history as any[];
            lastApproval =
              approvalHistoryArray[approvalHistoryArray.length - 1];
          }

          let pic = '';
          let no_telp = '';

          if (lastApproval?.created_by) {
            const personelApproval = await this.prisma.personel.findUnique({
              where: { id: lastApproval.created_by },
              select: {
                nama_lengkap: true,
                no_hp: true,
              },
            });

            if (personelApproval) {
              pic = personelApproval.nama_lengkap;
              no_telp = personelApproval.no_hp;
            }
          }

          return {
            id,
            personel_id: personel?.id || '',
            nama_lengkap: nama ?? personel.nama_lengkap,
            nrp: personel?.nrp || '',
            bank: bank || '',
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
              )) ?? '',
            pangkat: personel?.pangkat_personel?.[0]?.pangkat?.nama || '',
            pangkat_singkat:
              personel?.pangkat_personel?.[0]?.pangkat?.nama_singkat || '',
            jabatan: personel?.jabatan_personel?.[0]?.jabatans?.nama || '',
            satuan:
              personel?.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
            jenis_permintaan: jenis_permintaan == 1 ? 'Baru' : 'Penggantian',
            surat_permintaan: surat_permintaan || '',
            pic,
            no_telp,
            tracking_history: e_kta_batch?.tracking_history || 'Pengajuan Bank',
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getNextTrackingStatus(req, id) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const batch = await this.prisma.e_kta_batch.findUnique({
      where: { id: BigInt(id) },
    });

    if (!batch) {
      throw new NotFoundException('Batch not found');
    }

    const history = Array.isArray(batch.tracking_history)
      ? (batch.tracking_history as { status: string }[])
      : [];

    const lastStatus =
      history.length > 0 ? history[history.length - 1].status : null;

    if (approvalRole.toLowerCase() == 'operator level 1 e-kta') {
      if (!lastStatus) return ['Diterima Mabes Polri'];
      if (lastStatus == 'Diterima Mabes Polri')
        return ['Dikirim ke Polda', 'Dikirim ke Personel'];
      if (lastStatus == 'Dikirim ke Personel') return ['Diterima Personel'];
    }

    if (approvalRole.toLowerCase() == 'operator level 2 e-kta') {
      if (lastStatus == 'Dikirim ke Polda') return ['Diterima Polda'];
      if (lastStatus == 'Diterima Polda')
        return ['Dikirim ke Polres', 'Dikirim ke Personel'];
      if (lastStatus == 'Dikirim ke Personel') return ['Diterima Personel'];
    }

    if (approvalRole.toLowerCase() == 'operator level 3 e-kta') {
      if (lastStatus == 'Dikirim ke Polres') return ['Diterima Polres'];
      if (lastStatus == 'Diterima Polres') return ['Dikirim ke Personel'];
      if (lastStatus == 'Dikirim ke Personel') return ['Diterima Personel'];
    }

    return [];
  }

  async getBacthPengajuanList(
    req: any,
    paginationData,
    nama,
    pangkatArray,
    jabatan,
    nomorSurat,
    nrp,
    status,
    orby,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const where: Prisma.e_ktaWhereInput = {};
    const orderByParam = orby || 'id:asc';
    const [field, direction] = orderByParam.split(':');
    const orderBy = { [field]: direction.toLowerCase() };
    const personelFilter: any = {};

    // --- Role-based Satuan Filter ---
    let listSatuan: any[] = [];
    let satuanIdChunks: number[][] = [[]];

    if (
      approvalRole.toLowerCase() != 'approver level 1 e-kta' &&
      approvalRole.toLowerCase() != 'operator level 1 e-kta' &&
      approvalRole !== 'superadmin'
    ) {
      listSatuan = await this.getListSatuan(req.user.satuan_id);
      const satuanIds = listSatuan.map((item) => item.id);
      satuanIdChunks = chunk(satuanIds, 1000);
    }

    if (status) {
      if (status == 'Pengajuan Bank') {
        where.e_kta_batch = {
          is: {
            OR: [
              { tracking_history: { equals: [] } },
              { tracking_history: { equals: null } },
            ],
          },
        };
      } else {
        where.e_kta_batch = {
          is: {
            tracking_history: {
              array_contains: [
                {
                  status: status,
                },
              ],
            },
          },
        };
      }
    }

    if (nama) {
      personelFilter.nama_lengkap = {
        contains: nama,
        mode: 'insensitive',
      };
    }

    const pangkatArrays =
      pangkatArray && pangkatArray !== 'undefined' && pangkatArray !== 'null'
        ? JSON.parse(pangkatArray).map((id) => BigInt(id))
        : undefined;

    if (pangkatArrays) {
      personelFilter.pangkat_personel = {
        some: {
          is_aktif: true,
          pangkat: { id: { in: pangkatArrays } },
        },
      };
    }

    if (nrp) {
      personelFilter.nrp = {
        contains: nrp,
        mode: 'insensitive',
      };
    }

    if (Object.keys(personelFilter).length > 0) {
      where.personel = personelFilter;
    }

    if (nomorSurat) {
      where.e_kta_surat_detail = {
        some: {
          e_kta_surat: {
            nomor_surat: {
              contains: nomorSurat,
              mode: 'insensitive',
            },
          },
        },
      };
    }

    try {
      let allResults = [];
      let totalCount = 0;

      // FIX 1: Hitung total dulu sebelum chunking untuk pagination yang akurat
      if (satuanIdChunks.length > 0 && satuanIdChunks[0].length > 0) {
        // Ada filter satuan
        for (const ids of satuanIdChunks) {
          if (ids.length > 0) {
            const chunkWhere = {
              ...where,
              e_kta_batch_id: { not: null },
              personel: {
                ...personelFilter,
                jabatan_personel: {
                  some: {
                    is_aktif: true,
                    jabatans: {
                      ...(jabatan && {
                        nama: {
                          contains: jabatan,
                          mode: 'insensitive',
                        },
                      }),
                      satuan_id: { in: ids },
                    },
                  },
                },
              },
            };

            const chunkCount = await this.prisma.e_kta.count({
              where: chunkWhere,
            });
            totalCount += chunkCount;
          }
        }
      } else {
        // Tidak ada filter satuan atau role superadmin/approver level 1
        const baseWhere = {
          ...where,
          e_kta_batch_id: { not: null },
        };

        if (jabatan) {
          baseWhere.personel = {
            ...personelFilter,
            jabatan_personel: {
              some: {
                is_aktif: true,
                jabatans: {
                  nama: {
                    contains: jabatan,
                    mode: 'insensitive',
                  },
                },
              },
            },
          };
        }

        totalCount = await this.prisma.e_kta.count({
          where: baseWhere,
        });
      }

      // FIX 2: Implementasi pagination yang benar dengan chunking
      const totalPages = Math.ceil(totalCount / limit);
      const skip = limit * (page - 1);
      let collectedCount = 0;
      let hasCollectedForPage = false;

      if (satuanIdChunks.length > 0 && satuanIdChunks[0].length > 0) {
        // Ada filter satuan
        for (const ids of satuanIdChunks) {
          if (ids.length > 0 && !hasCollectedForPage) {
            const chunkWhere = {
              ...where,
              e_kta_batch_id: { not: null },
              personel: {
                ...personelFilter,
                jabatan_personel: {
                  some: {
                    is_aktif: true,
                    jabatans: {
                      ...(jabatan && {
                        nama: {
                          contains: jabatan,
                          mode: 'insensitive',
                        },
                      }),
                      satuan_id: { in: ids },
                    },
                  },
                },
              },
            };

            // Hitung berapa banyak record di chunk ini
            const chunkCount = await this.prisma.e_kta.count({
              where: chunkWhere,
            });

            // Skip chunk ini jika belum mencapai halaman yang diminta
            if (collectedCount + chunkCount < skip) {
              collectedCount += chunkCount;
              continue;
            }

            // Ambil data dari chunk ini
            const chunkSkip = Math.max(0, skip - collectedCount);
            const chunkTake = Math.min(limit - allResults.length, limit);

            const chunkResults = await this.prisma.e_kta.findMany({
              select: {
                id: true,
                nama: true,
                approval_history: true,
                e_kta_batch: {
                  select: {
                    tracking_history: true,
                    pic: true,
                    no_telp: true,
                  },
                },
                e_kta_surat_detail: {
                  select: {
                    e_kta_surat: {
                      select: {
                        id: true,
                        nomor_surat: true,
                        status: true,
                      },
                    },
                  },
                },
                personel: {
                  select: {
                    nama_lengkap: true,
                    nrp: true,
                    foto_file: true,
                    pangkat_personel: {
                      select: {
                        pangkat: {
                          select: {
                            nama: true,
                            nama_singkat: true,
                          },
                        },
                      },
                      where: {
                        is_aktif: true,
                        ...(pangkatArrays && {
                          pangkat: {
                            id: { in: pangkatArrays },
                          },
                        }),
                      },
                    },
                    jabatan_personel: {
                      select: {
                        jabatans: {
                          select: {
                            id: true,
                            nama: true,
                            satuan: {
                              select: {
                                id: true,
                                nama: true,
                              },
                            },
                          },
                        },
                      },
                      where: {
                        is_aktif: true,
                        jabatans: {
                          ...(jabatan && {
                            nama: {
                              contains: jabatan,
                              mode: 'insensitive',
                            },
                          }),
                          satuan_id: { in: ids },
                        },
                      },
                    },
                  },
                },
              },
              where: chunkWhere,
              take: Number(chunkTake),
              skip: Number(chunkSkip),
              orderBy: orderBy,
            });

            allResults.push(...chunkResults);
            collectedCount += chunkCount;

            // Jika sudah cukup data untuk halaman ini, berhenti
            if (allResults.length >= limit) {
              hasCollectedForPage = true;
              break;
            }
          }
        }
      } else {
        // Tidak ada filter satuan atau role superadmin/approver level 1
        const baseWhere = {
          ...where,
          e_kta_batch_id: { not: null },
        };

        if (jabatan) {
          baseWhere.personel = {
            ...personelFilter,
            jabatan_personel: {
              some: {
                is_aktif: true,
                jabatans: {
                  nama: {
                    contains: jabatan,
                    mode: 'insensitive',
                  },
                },
              },
            },
          };
        }

        allResults = await this.prisma.e_kta.findMany({
          select: {
            id: true,
            nama: true,
            approval_history: true,
            e_kta_batch: {
              select: {
                tracking_history: true,
                pic: true,
                no_telp: true,
              },
            },
            e_kta_surat_detail: {
              select: {
                e_kta_surat: {
                  select: {
                    id: true,
                    nomor_surat: true,
                    status: true,
                  },
                },
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                    ...(pangkatArrays && {
                      pangkat: {
                        id: { in: pangkatArrays },
                      },
                    }),
                  },
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                    ...(jabatan && {
                      jabatans: {
                        nama: {
                          contains: jabatan,
                          mode: 'insensitive',
                        },
                      },
                    }),
                  },
                },
              },
            },
          },
          where: baseWhere,
          take: Number(limit),
          skip: Number(skip),
          orderBy: orderBy,
        });
      }

      // FIX 3: Hapus duplikasi berdasarkan ID (jika masih ada)
      const uniqueResults = allResults.filter(
        (item, index, self) =>
          index === self.findIndex((t) => t.id === item.id),
      );

      const result = await Promise.all(
        uniqueResults.map(async (ektas) => {
          const { e_kta_batch, e_kta_surat_detail, personel, ...data } = ektas;

          let approvalHistoryArray = Array.isArray(data.approval_history)
            ? (data.approval_history as any[])
            : [];

          let lastApproval =
            approvalHistoryArray[approvalHistoryArray.length - 1];

          let trackingHistoryArray = Array.isArray(
            e_kta_batch?.tracking_history,
          )
            ? e_kta_batch.tracking_history
            : [];

          if (trackingHistoryArray.length > 0) {
            approvalHistoryArray = trackingHistoryArray;
            lastApproval =
              trackingHistoryArray[trackingHistoryArray.length - 1];
          }

          let personelApproval = null;
          if (lastApproval?.created_by) {
            personelApproval = await this.prisma.personel.findUnique({
              select: {
                nama_lengkap: true,
                no_hp: true,
                foto_file: true,
                nrp: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  where: {
                    is_aktif: true,
                  },
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                    tmt_jabatan: true,
                  },
                  where: {
                    is_aktif: true,
                  },
                },
              },
              where: {
                id: lastApproval.created_by,
                pangkat_personel: {
                  some: {
                    is_aktif: true,
                  },
                },
                jabatan_personel: {
                  some: {
                    is_aktif: true,
                  },
                },
              },
            });
          }

          const pic: any = {
            nama_lengkap: personelApproval?.nama_lengkap || '',
            no_hp: personelApproval?.no_hp || '',
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${personelApproval?.nrp}/${personelApproval?.foto_file}`,
              )) ?? '',
            nrp: personelApproval?.nrp || '',
            pangkat_nama:
              personelApproval?.pangkat_personel?.[0]?.pangkat?.nama || '',
            pangkat_nama_singkat:
              personelApproval?.pangkat_personel?.[0]?.pangkat?.nama_singkat ||
              '',
            satuan_nama:
              personelApproval?.jabatan_personel?.[0]?.jabatans?.satuan?.nama ||
              '',
          };

          const rejectedApproval = approvalHistoryArray.find(
            (approval) => (approval as any)?.status == 'REJECTED',
          );

          if (rejectedApproval) {
            const approvalData = rejectedApproval as any;
            pic.status = `Ditolak oleh ${approvalData.approval || 'Approver'} E-KTA`;
            pic.comment = approvalData.comment || '';
          } else if (trackingHistoryArray.length > 0) {
            const lastTracking = trackingHistoryArray[
              trackingHistoryArray.length - 1
            ] as any;
            pic.status = lastTracking?.status || '';
          } else if (approvalHistoryArray.length > 0) {
            const lastApproval = approvalHistoryArray[
              approvalHistoryArray.length - 1
            ] as any;
            const lastApprovalName = lastApproval?.approval || '';

            if (lastApprovalName.includes('approver level 1 e-kta')) {
              pic.status = 'Pengajuan Bank';
            } else {
              pic.status = `Sedang dalam Proses ${lastApprovalName}`;
            }

            pic.comment = lastApproval?.comment || '';
          } else {
            pic.status = 'Belum Ada Proses Approval';
            pic.comment = '';
          }

          return {
            id: data.id,
            nama_lengkap: data.nama ?? personel?.nama_lengkap,
            nrp: personel?.nrp || '',
            foto_file:
              (await this.minioService.convertFileKeyToURL(
                `${process.env.MINIO_BUCKET_NAME}`,
                `${process.env.MINIO_PATH_FILE}${personel?.nrp}/${personel?.foto_file}`,
              )) ?? '',
            pangkat: personel?.pangkat_personel?.[0]?.pangkat?.nama || '',
            pangkat_singkat:
              personel?.pangkat_personel?.[0]?.pangkat?.nama_singkat || '',
            jabatan: personel?.jabatan_personel?.[0]?.jabatans?.nama || '',
            nama_satuan:
              personel?.jabatan_personel?.[0]?.jabatans?.satuan?.nama || '',
            pic,
            tracking_history: e_kta_batch?.tracking_history || [],
            approval_history: data.approval_history,
          };
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          result,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: Math.ceil(totalCount / limit),
        totalData: totalCount,
        data: result,
      };
    } catch (err) {
      throw err;
    }
  }

  async getCountBankList(req: any, bulan: string, tahun: string) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (tahun && bulan) {
      startDate = new Date(parseInt(tahun), parseInt(bulan) - 1, 1);
      endDate = new Date(parseInt(tahun), parseInt(bulan), 1);
    } else if (tahun && !bulan) {
      startDate = new Date(parseInt(tahun), 0, 1);
      endDate = new Date(parseInt(tahun) + 1, 0, 1);
    }

    const where: Prisma.e_kta_batchWhereInput = {};
    try {
      let listSatuan: any[] = [];
      if (
        approvalRole.toLowerCase() != 'approver level 1 e-kta' &&
        approvalRole.toLowerCase() != 'operator level 1 e-kta'
      ) {
        listSatuan = await this.getListSatuan(req.user.satuan_id);
        where.satuan_id = {
          in: listSatuan.slice(0, 10000).map((item) => item.id),
        };
      }

      const e_kta = await this.prisma.e_kta_batch.findMany({
        select: {
          nama_bank: true,
          jumlah_kartu: true,
        },
        where: {
          ...where,
          deleted_at: null,
          ...(startDate && endDate
            ? {
                created_at: {
                  gte: startDate,
                  lt: endDate,
                },
              }
            : {}),
        },
      });

      let countBRI = 0;
      let countBNI = 0;
      let countMandiri = 0;
      let countBTN = 0;
      let countBSI = 0;
      e_kta.forEach((item) => {
        if (item.nama_bank == 'BRI') {
          countBRI += item.jumlah_kartu;
        } else if (item.nama_bank == 'BNI') {
          countBNI += item.jumlah_kartu;
        } else if (item.nama_bank == 'MANDIRI') {
          countMandiri += item.jumlah_kartu;
        } else if (item.nama_bank == 'BTN') {
          countBTN += item.jumlah_kartu;
        } else if (item.nama_bank == 'BSI') {
          countBSI += item.jumlah_kartu;
        }
      });

      const result = {
        BRI: countBRI,
        BNI: countBNI,
        Mandiri: countMandiri,
        BTN: countBTN,
        BSI: countBSI,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V2_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
          message,
          result,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }

  async getAllPersonelCount(req) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const where: Prisma.personelWhereInput = {};
    try {
      let satuanId = req.user.satuan_id;
      if (
        approvalRole.toLowerCase() != 'approver level 1 e-kta' &&
        approvalRole.toLowerCase() != 'operator level 1 e-kta'
      ) {
        const listSatuan = await this.getListSatuan(satuanId);

        where.jabatan_personel = {
          some: {
            is_aktif: true,
            jabatans: {
              AND: [
                {
                  satuan_id: {
                    in: listSatuan.slice(0, 10000).map((item) => item.id),
                  },
                },
              ],
            },
          },
        };
      }

      const personelCount = await this.prisma.personel.count({
        where: {
          deleted_at: null,
          ...where,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERSONEL_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
          message,
          personelCount,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: personelCount,
      };
    } catch (error) {
      throw error;
    }
  }

  async getRekapBySatuanAndMonth(
    req,
    satuan: string,
    bank: string,
    tahun: string,
    page = 1,
    limit = 10,
  ) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const where: Prisma.satuanWhereInput = {};
    let listSatuan: any[] = [];

    if (
      approvalRole !== 'approver level 1 e-kta' &&
      approvalRole !== 'operator level 1 e-kta' &&
      approvalRole !== 'superadmin'
    ) {
      listSatuan = await this.getListSatuan(req.user.satuan_id);
      where.id = {
        in: listSatuan.map((item) => item.id),
      };
    }

    const tahunSekarang = new Date().getFullYear();
    const tahunNumber = tahun ? parseInt(tahun, 10) : tahunSekarang;

    if (isNaN(tahunNumber)) {
      throw new BadRequestException('Format tahun tidak valid');
    }

    const bulanList = [
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];

    const offset = (page - 1) * limit;
    let satuans: any[] = [];
    let totalSatuan = 0;

    if (bank) {
      const ektasWithSatuan = await this.prisma.e_kta.findMany({
        where: {
          bank,
          created_at: {
            gte: new Date(`${tahunNumber}-01-01T00:00:00.000Z`),
            lte: new Date(`${tahunNumber}-12-31T23:59:59.999Z`),
          },
          e_kta_batch: { is: { satuan_id: { not: null } } },
        },
        select: {
          e_kta_batch: { select: { satuan_id: true } },
        },
      });

      let allSatuanIdsForBank = [
        ...new Set(
          ektasWithSatuan
            .map((e) => e.e_kta_batch?.satuan_id)
            .filter((id): id is bigint => !!id),
        ),
      ];

      const whereSatuan: Prisma.satuanWhereInput = { is_aktif: true };

      if (
        approvalRole !== 'approver level 1 e-kta' &&
        approvalRole !== 'operator level 1 e-kta' &&
        approvalRole !== 'superadmin'
      ) {
        const userSatuanIds = listSatuan.map((item) => BigInt(item.id));
        allSatuanIdsForBank = allSatuanIdsForBank.filter((id) =>
          userSatuanIds.includes(id),
        );
      }

      const chunkedSatuanIds = chunk(allSatuanIdsForBank, 1000);
      let totalCount = 0;
      let satuanList: any[] = [];

      for (const chunkIds of chunkedSatuanIds) {
        const [count, found] = await this.prisma.$transaction([
          this.prisma.satuan.count({
            where: { ...whereSatuan, id: { in: chunkIds } },
          }),
          this.prisma.satuan.findMany({
            where: { ...whereSatuan, id: { in: chunkIds } },
            select: {
              id: true,
              nama: true,
              mv_satuanFullname: { select: { satuan_fullname: true } },
            },
            orderBy: { nama: 'asc' },
          }),
        ]);
        totalCount += count;
        satuanList.push(...found);
      }

      totalSatuan = totalCount;
      satuans = satuanList.slice(offset, offset + limit); // manual pagination
    } else {
      const whereClause = {
        is_aktif: true,
        ...where,
        ...(satuan ? { id: Number(satuan) } : {}),
      };

      const idFilter = where.id as { in: bigint[] };

      if (
        idFilter?.in &&
        Array.isArray(idFilter.in) &&
        idFilter.in.length > 1000
      ) {
        const chunkedIds = chunk(idFilter.in, 1000) as bigint[][];

        let satuanList: any[] = [];
        let totalCount = 0;

        for (const chunkIds of chunkedIds) {
          const [count, found] = await this.prisma.$transaction([
            this.prisma.satuan.count({
              where: {
                ...whereClause,
                id: { in: chunkIds }, // ✅ sudah bigint[]
              },
            }),
            this.prisma.satuan.findMany({
              where: {
                ...whereClause,
                id: { in: chunkIds }, // ✅ sudah bigint[]
              },
              select: {
                id: true,
                nama: true,
                mv_satuanFullname: { select: { satuan_fullname: true } },
              },
              orderBy: { nama: 'asc' },
            }),
          ]);

          totalCount += count;
          satuanList.push(...found);
        }

        totalSatuan = totalCount;
        satuans = satuanList.slice(offset, offset + limit); // manual pagination
      } else {
        totalSatuan = await this.prisma.satuan.count({ where: whereClause });

        satuans = await this.prisma.satuan.findMany({
          skip: offset,
          take: limit,
          where: whereClause,
          select: {
            id: true,
            nama: true,
            mv_satuanFullname: { select: { satuan_fullname: true } },
          },
          orderBy: { nama: 'asc' },
        });
      }
    }

    const ektas = await this.prisma.e_kta_batch.findMany({
      where: {
        created_at: {
          gte: new Date(`${tahunNumber}-01-01T00:00:00.000Z`),
          lte: new Date(`${tahunNumber}-12-31T23:59:59.999Z`),
        },
        ...(bank ? { nama_bank: bank } : {}),
        satuan_id: {
          in: satuans.map((s) => s.id),
        },
      },
      select: {
        created_at: true,
        satuan_id: true,
        jumlah_kartu: true,
      },
    });

    const satuanMap = new Map<
      number,
      { created_at: Date; jumlah_kartu: number }[]
    >();

    for (const e of ektas) {
      const satuanId = Number(e.satuan_id);
      if (!satuanMap.has(satuanId)) {
        satuanMap.set(satuanId, []);
      }
      satuanMap.get(satuanId).push({
        created_at: new Date(e.created_at),
        jumlah_kartu: Number(e.jumlah_kartu) || 0,
      });
    }

    const data = satuans.map((s) => {
      const records = satuanMap.get(Number(s.id)) || [];

      const rekap = bulanList.map((bulan, index) => {
        const jumlah = records
          .filter((r) => r.created_at.getMonth() === index)
          .reduce((acc, r) => acc + r.jumlah_kartu, 0);

        return { bulan, jumlah };
      });

      const total = rekap.reduce((acc, curr) => acc + curr.jumlah, 0);

      return {
        satuan: s.mv_satuanFullname.satuan_fullname,
        rekap,
        total,
      };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EKTA_V2_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
        message,
        data,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data,
      meta: {
        page,
        limit,
        totalPages: Math.ceil(totalSatuan / limit),
        totalData: totalSatuan,
      },
    };
  }

  async getRekapBySatuanAndYear(
    req,
    satuan: string,
    bank: string,
    tahun: string,
    page = 1,
    limit = 10,
  ) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const where: Prisma.satuanWhereInput = {};
    let listSatuan: any[] = [];

    if (
      approvalRole !== 'approver level 1 e-kta' &&
      approvalRole !== 'operator level 1 e-kta'
    ) {
      listSatuan = await this.getListSatuan(req.user.satuan_id);
      where.id = {
        in: listSatuan.map((item) => BigInt(item.id)),
      };
    }

    const tahunSekarang = new Date().getFullYear();
    const tahunNumber = tahun ? parseInt(tahun, 10) : tahunSekarang;

    if (isNaN(tahunNumber)) {
      throw new BadRequestException('Format tahun tidak valid');
    }

    let tahunList: number[] = [];
    if (!tahun) {
      const tahunListRaw = await this.prisma.$queryRaw<
        Array<{ tahun: number }>
      >`SELECT DISTINCT EXTRACT(YEAR FROM "created_at") as tahun FROM "e_kta_batch" ORDER BY tahun DESC`;

      tahunList = tahunListRaw.map((row) => row.tahun);
      if (tahunList.length === 0) {
        tahunList = [tahunSekarang];
      }
    } else {
      tahunList = [tahunNumber];
    }

    const offset = (page - 1) * limit;
    let satuans: any[] = [];
    let totalSatuan = 0;

    // ==== CASE BANK ====
    if (bank) {
      const ektasWithSatuan = await this.prisma.e_kta.findMany({
        where: {
          bank,
          created_at: {
            gte: new Date(`${tahunNumber}-01-01T00:00:00.000Z`),
            lte: new Date(`${tahunNumber}-12-31T23:59:59.999Z`),
          },
          e_kta_batch: {
            is: {
              satuan_id: {
                not: null,
                ...(satuan ? { equals: Number(satuan) } : {}),
              },
            },
          },
        },
        select: {
          e_kta_batch: { select: { satuan_id: true } },
        },
      });

      let allSatuanIdsForBank = [
        ...new Set(
          ektasWithSatuan
            .map((e) => e.e_kta_batch?.satuan_id)
            .filter((id): id is bigint => !!id),
        ),
      ];

      const whereSatuan: Prisma.satuanWhereInput = { is_aktif: true };

      if (
        approvalRole !== 'approver level 1 e-kta' &&
        approvalRole !== 'operator level 1 e-kta'
      ) {
        const userSatuanIds = listSatuan.map((item) => BigInt(item.id));
        allSatuanIdsForBank = allSatuanIdsForBank.filter((id) =>
          userSatuanIds.includes(id),
        );
      }

      // Gunakan chunk jika ID terlalu banyak
      if (allSatuanIdsForBank.length > 1000) {
        const chunkedIds = chunk(allSatuanIdsForBank, 1000) as bigint[][];
        let satuanList: any[] = [];
        let totalCount = 0;

        for (const chunkIds of chunkedIds) {
          const [count, found] = await this.prisma.$transaction([
            this.prisma.satuan.count({
              where: { ...whereSatuan, id: { in: chunkIds } },
            }),
            this.prisma.satuan.findMany({
              where: { ...whereSatuan, id: { in: chunkIds } },
              select: {
                id: true,
                nama: true,
                mv_satuanFullname: { select: { satuan_fullname: true } },
              },
              orderBy: { nama: 'asc' },
            }),
          ]);
          totalCount += count;
          satuanList.push(...found);
        }

        totalSatuan = totalCount;
        satuans = satuanList.slice(offset, offset + limit);
      } else {
        totalSatuan = await this.prisma.satuan.count({
          where: { ...whereSatuan, id: { in: allSatuanIdsForBank } },
        });

        satuans = await this.prisma.satuan.findMany({
          skip: offset,
          take: limit,
          where: { ...whereSatuan, id: { in: allSatuanIdsForBank } },
          select: {
            id: true,
            nama: true,
            mv_satuanFullname: { select: { satuan_fullname: true } },
          },
          orderBy: { nama: 'asc' },
        });
      }
    } else {
      // ==== CASE NON BANK ====
      const whereClause = {
        is_aktif: true,
        ...where,
        ...(satuan ? { id: Number(satuan) } : {}),
      };

      if (
        where.id &&
        typeof where.id === 'object' &&
        'in' in where.id &&
        Array.isArray((where.id as any).in) &&
        (where.id as any).in.length > 1000
      ) {
        const chunkedIds = chunk((where.id as any).in, 1000) as bigint[][];
        let satuanList: any[] = [];
        let totalCount = 0;

        for (const chunkIds of chunkedIds) {
          const [count, found] = await this.prisma.$transaction([
            this.prisma.satuan.count({
              where: { ...whereClause, id: { in: chunkIds } },
            }),
            this.prisma.satuan.findMany({
              where: { ...whereClause, id: { in: chunkIds } },
              select: {
                id: true,
                nama: true,
                mv_satuanFullname: { select: { satuan_fullname: true } },
              },
              orderBy: { nama: 'asc' },
            }),
          ]);
          totalCount += count;
          satuanList.push(...found);
        }

        totalSatuan = totalCount;
        satuans = satuanList.slice(offset, offset + limit);
      } else {
        totalSatuan = await this.prisma.satuan.count({ where: whereClause });

        satuans = await this.prisma.satuan.findMany({
          skip: offset,
          take: limit,
          where: whereClause,
          select: {
            id: true,
            nama: true,
            mv_satuanFullname: { select: { satuan_fullname: true } },
          },
          orderBy: { nama: 'asc' },
        });
      }
    }

    // ==== Ambil e_kta_batch ====
    const dateFilters =
      tahunList.length > 0
        ? {
            OR: tahunList.map((tahun) => ({
              created_at: {
                gte: new Date(`${tahun}-01-01T00:00:00.000Z`),
                lte: new Date(`${tahun}-12-31T23:59:59.999Z`),
              },
            })),
          }
        : {};

    const ektas = await this.prisma.e_kta_batch.findMany({
      where: {
        ...dateFilters,
        ...(bank ? { nama_bank: bank } : {}),
        satuan_id: {
          in: satuans.map((s) => s.id),
        },
      },
      select: {
        created_at: true,
        satuan_id: true,
        jumlah_kartu: true,
      },
    });

    const satuanMap = new Map<
      number,
      { created_at: Date; jumlah_kartu: number }[]
    >();
    for (const e of ektas) {
      const satuanId = Number(e.satuan_id);
      if (!satuanMap.has(satuanId)) {
        satuanMap.set(satuanId, []);
      }
      satuanMap.get(satuanId).push({
        created_at: new Date(e.created_at),
        jumlah_kartu: Number(e.jumlah_kartu) || 0,
      });
    }

    const data = satuans.map((s) => {
      const records = satuanMap.get(Number(s.id)) || [];

      const rekap = tahunList.map((tahun) => {
        const jumlah = records
          .filter((r) => r.created_at.getFullYear() === Number(tahun))
          .reduce((acc, r) => acc + r.jumlah_kartu, 0);

        return { tahun: tahun.toString(), jumlah };
      });

      const total = rekap.reduce((acc, curr) => acc + curr.jumlah, 0);

      return {
        satuan: s.mv_satuanFullname.satuan_fullname,
        rekap,
        total,
      };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EKTA_V2_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KTA_V2_READ as ConstantLogType,
        message,
        data,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data,
      meta: {
        page,
        limit,
        totalPages: Math.ceil(totalSatuan / limit),
        totalData: totalSatuan,
      },
    };
  }

  async getYearAvailable(req) {
    const userRoles = req.user.roles;
    const matchedRoles = userRoles.filter((role) =>
      this.allowedRoles.includes(role.nama.toLowerCase()),
    );
    const approvalRole =
      matchedRoles.length > 0 ? matchedRoles[0].nama.toLowerCase() : null;

    const where: Prisma.e_kta_batchWhereInput = {};
    let listSatuan: any[] = [];
    if (
      approvalRole.toLowerCase() != 'approver level 1 e-kta' &&
      approvalRole.toLowerCase() != 'operator level 1 e-kta'
    ) {
      listSatuan = await this.getListSatuan(req.user.satuan_id);
      where.satuan_id = {
        in: listSatuan.map((item) => item.id),
      };
    }
    const tahunListRaw = await this.prisma.$queryRaw<
      Array<{ tahun: number }>
    >`SELECT DISTINCT EXTRACT(YEAR FROM "created_at") as tahun FROM "e_kta_batch" ORDER BY tahun DESC`;

    let tahunList = [];
    tahunList = tahunListRaw.map((row) => row.tahun);

    if (tahunList.length == 0) {
      tahunList = [new Date().getFullYear()];
    }

    return tahunList;
  }
}
