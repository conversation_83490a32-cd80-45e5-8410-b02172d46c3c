import { forwardRef, Module } from '@nestjs/common';
import { EktaControllerV2 } from './controller/ekta-v2.controller';
import { EktaServiceV2 } from './service/ekta-v2.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { MasterEktaControllerV2 } from './controller/master-ekta-v2.controller';
import { MasterEktaServiceV2 } from './service/master-ekta-v2.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [EktaControllerV2, MasterEktaControllerV2],
  providers: [EktaServiceV2, MasterEktaServiceV2, MinioService],
})
export class EktaModuleV2 {}
