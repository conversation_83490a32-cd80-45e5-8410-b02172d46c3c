import {
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max<PERSON>eng<PERSON>,
} from 'class-validator';

export class AuditPersonelDto {
  @IsOptional()
  @IsString()
  keterangan: string;
}

export class AuditPersonelOperatorDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(25)
  nama: string;

  @IsNotEmpty()
  @IsInt()
  jenis_permintaan: number;

  @IsOptional()
  surat_permintaan: string;
}
