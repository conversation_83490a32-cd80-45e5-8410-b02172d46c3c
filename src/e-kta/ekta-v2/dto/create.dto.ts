import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';

export class CreateDto {
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Number)
  @IsInt({ each: true })
  id_array: number[];
}

export class CreateBankDto {
  @IsString()
  @IsNotEmpty()
  nama: string;
}

export class CreateSatuanBankDto {
  @IsNumber()
  @IsNotEmpty()
  bank_id: number;
}
export class AcceptedBankDto {
  @IsString()
  @IsNotEmpty()
  status: string;
}
