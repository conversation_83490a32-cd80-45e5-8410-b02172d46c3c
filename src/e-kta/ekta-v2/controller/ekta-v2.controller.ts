import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { EktaServiceV2 } from '../service/ekta-v2.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { CreateDto } from 'src/e-kta/ekta-v2/dto/create.dto';
import { AuditPersonelOperatorDto } from '../dto/audit-personel.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { FileInterceptor } from '@nestjs/platform-express';
import { SubmitApprovalDto, SubmitOperatorDto } from '../dto/submit.dto';
import { QueryGetPersonelEKTADto } from '../dto/query-ekta.dto';
import { UpdateStatusDto } from '../dto/update.dto';
import { Module, Permission } from 'src/core/decorators';
import { MODULES } from 'src/core/constants/module.constant';

@Controller('ekta-v2')
@Module(MODULES.DIGITAL_KTA)
@UseGuards(JwtAuthGuard)
export class EktaControllerV2 {
  private readonly logger = new Logger(EktaControllerV2.name);

  constructor(private readonly ekta: EktaServiceV2) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query('status_approval') statusApproval: string,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and status_approval: ${JSON.stringify(statusApproval)}`,
    );
    const response = await this.ekta.getData(
      req,
      paginationData,
      searchandsortData,
      statusApproval,
    );
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and status_approval: ${JSON.stringify(statusApproval)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/pengajuan/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getDataPengajuan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDataPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getDataPengajuan(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getDataPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pengajuan/detail')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getDataPengajuanDetail(
    @Req() req: any,
    @Query('id') ids: string | string[],
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDataPengajuanDetail.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    let idArray: number[] = [];

    if (typeof ids === 'string') {
      try {
        idArray = JSON.parse(ids);
      } catch {
        idArray = ids
          .split(',')
          .map(Number)
          .filter((id) => !isNaN(id));
      }
    } else if (Array.isArray(ids)) {
      idArray = ids.map(Number).filter((id) => !isNaN(id));
    }

    const response = await this.ekta.getDataPengajuanDetail(
      req,
      idArray,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getDataPengajuanDetail.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/surat')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getSuratList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getSuratList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getSuratList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getSuratList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/surat/detail')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getSuratListById(
    @Req() req: any,
    @Query('id') ids: string | string[],
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getSuratListById.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    let idArray: number[] = [];

    if (typeof ids === 'string') {
      try {
        idArray = JSON.parse(ids);
      } catch {
        idArray = ids
          .split(',')
          .map(Number)
          .filter((id) => !isNaN(id));
      }
    } else if (Array.isArray(ids)) {
      idArray = ids.map(Number).filter((id) => !isNaN(id));
    }

    const response = await this.ekta.getDataSuratDetail(
      req,
      idArray,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getSuratListById.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getListPersonel(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: QueryGetPersonelEKTADto,
  ) {
    this.logger.log(
      `Entering ${this.getListPersonel.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getDataPersonel(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListPersonel.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body(new FieldValidatorPipe()) payload: CreateDto,
    @Req() req: any,
  ) {
    const result = await this.ekta.create(payload, req);

    return {
      statusCode: HttpStatus.CREATED,
      message: 'Successfully create E-KTA',
      data: result,
    };
  }

  @Patch('ajukan-ulang')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.CREATED)
  async ajukanUlang(
    @Body(new FieldValidatorPipe()) payload: CreateDto,
    @Req() req: any,
  ) {
    const result = await this.ekta.ajukanUlang(payload, req);

    return {
      statusCode: HttpStatus.CREATED,
      message: 'Successfully ajukan ulang E-KTA',
      data: result,
    };
  }

  @Patch('/operator/audit/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('surat_permintaan'))
  async auditPersonelOperator(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) payload: AuditPersonelOperatorDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.auditPersonelOperator.name} with id: ${JSON.stringify(id)} and payload: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.auditPersonelOperator(
      req,
      id,
      payload,
      file,
    );
    this.logger.log(
      `Leaving ${this.auditPersonelOperator.name} with id: ${JSON.stringify(id)} and payload: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('submit-operator')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  async submitOperator(
    @Body(new FieldValidatorPipe()) payload: SubmitOperatorDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.submitOperator.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.submitOperator(payload, req);
    this.logger.log(
      `Leaving ${this.submitOperator.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('submit-approval')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FileInterceptor('signature'))
  async submitApproval(
    @Body(new FieldValidatorPipe()) payload: SubmitApprovalDto,
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.submitApproval.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.submitApproval(payload, req, file);
    this.logger.log(
      `Leaving ${this.submitApproval.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('submit-approval/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  async submitDocument(
    @Req() req: any,
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.auditPersonelOperator.name} with id: ${JSON.stringify(id)}`,
    );
    const response = await this.ekta.submitDocument(id, req, file);
    this.logger.log(
      `Leaving ${this.submitApproval.name} with id: ${JSON.stringify(id)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/submit-approval')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.CREATED)
  async submitApprovalOperator(
    @Body(new FieldValidatorPipe()) payload: SubmitApprovalDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.submitApprovalOperator.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.submitApprovalOperator(payload, req);
    this.logger.log(
      `Leaving ${this.submitApprovalOperator.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/')
  @Permission('PERMISSION_DELETE')
  @HttpCode(HttpStatus.OK)
  async delete(
    @Body(new FieldValidatorPipe()) payload: CreateDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.delete.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.delete(payload, req);
    this.logger.log(
      `Leaving ${this.delete.name} with body: ${JSON.stringify(payload)} and result: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/riwayat/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getRiwayatList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRiwayatList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getRiwayatList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getRiwayatList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/riwayat/detail')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getRiwayatListById(
    @Req() req: any,
    @Query('id') ids: string | string[],
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRiwayatListById.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    let idArray: number[] = [];

    if (typeof ids === 'string') {
      try {
        idArray = JSON.parse(ids);
      } catch {
        idArray = ids
          .split(',')
          .map(Number)
          .filter((id) => !isNaN(id));
      }
    } else if (Array.isArray(ids)) {
      idArray = ids.map(Number).filter((id) => !isNaN(id));
    }

    const response = await this.ekta.getRiwayatListById(
      req,
      idArray,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getRiwayatListById.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/batch/distribusi/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getBacthDistribusiList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query('status') status: string,
  ) {
    this.logger.log(
      `Entering ${this.getBacthDistribusiList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getBacthDistribusiList(
      req,
      paginationData,
      searchandsortData,
      status,
    );
    this.logger.log(
      `Leaving ${this.getBacthDistribusiList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Patch('batch/distribusi/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  async updateStatusBatchDistribusi(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) payload: UpdateStatusDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.updateStatusBatchDistribusi.name} with id: ${JSON.stringify(id)}`,
    );
    const response = await this.ekta.updateStatusBatchDistribusi(
      id,
      req,
      payload,
      file,
    );
    this.logger.log(
      `Leaving ${this.updateStatusBatchDistribusi.name} with id: ${JSON.stringify(id)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/batch/distribusi/detail')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getBatchDistribusiDetail(
    @Req() req: any,
    @Query('id') ids: string | string[],
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getBatchDistribusiDetail.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    let idArray: number[] = [];

    if (typeof ids === 'string') {
      try {
        idArray = JSON.parse(ids);
      } catch {
        idArray = ids
          .split(',')
          .map(Number)
          .filter((id) => !isNaN(id));
      }
    } else if (Array.isArray(ids)) {
      idArray = ids.map(Number).filter((id) => !isNaN(id));
    }

    const response = await this.ekta.getBatchDistribusiDetail(
      req,
      idArray,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getBatchDistribusiDetail.name} with ids: ${JSON.stringify(ids)} pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/batch/distribusi/status/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getNextTrackingStatus(@Req() req: any, @Param('id') id: string) {
    this.logger.log(
      `Entering ${this.getNextTrackingStatus.name} with id: ${JSON.stringify(id)}`,
    );

    const response = await this.ekta.getNextTrackingStatus(req, id);

    this.logger.log(
      `Leaving ${this.getNextTrackingStatus.name} with id: ${JSON.stringify(id)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/batch/pengajuan/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getBacthPengajuanList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query('nama') nama: string,
    @Query('jabatan') jabatan: string,
    @Query('pangkat') pangkatArray: string | string[],
    @Query('nomor_surat') nomorSurat: string,
    @Query('nrp') nrp: string,
    @Query('status') status: string,
    @Query('order_by') orderBy: string,
  ) {
    // this.logger.log(`Entering ${this.getBacthDistribusiList.name}`);

    const response = await this.ekta.getBacthPengajuanList(
      req,
      paginationData,
      nama,
      pangkatArray,
      jabatan,
      nomorSurat,
      nrp,
      status,
      orderBy,
    );
    // this.logger.log(`Leaving ${this.getBacthDistribusiList.name}`);

    return response;
  }

  @Get('/dashboard/bank/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getCountBankList(
    @Req() req: any,
    @Query('bulan') bulan: string,
    @Query('tahun') tahun: string,
  ) {
    this.logger.log(`Entering ${this.getCountBankList.name}`);

    const response = await this.ekta.getCountBankList(req, bulan, tahun);
    this.logger.log(`Leaving ${this.getCountBankList.name}`);

    return response;
  }

  @Get('/personel/count/all')
  @HttpCode(200)
  async getAllPersonelCount(@Req() req: any) {
    const response = await this.ekta.getAllPersonelCount(req);

    return response;
  }

  @Get('/rekap/month/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getRekapBySatuanAndMonth(
    @Req() req: any,
    @Query('satuan') satuan: string,
    @Query('bank') bank: string,
    @Query('tahun') tahun: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const response = await this.ekta.getRekapBySatuanAndMonth(
      req,
      satuan,
      bank,
      tahun,
      Number(page),
      Number(limit),
    );

    return response;
  }

  @Get('/rekap/year/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getRekapBySatuanAndYear(
    @Req() req: any,
    @Query('satuan') satuan: string,
    @Query('bank') bank: string,
    @Query('tahun') tahun: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const response = await this.ekta.getRekapBySatuanAndYear(
      req,
      satuan,
      bank,
      tahun,
      Number(page),
      Number(limit),
    );

    return response;
  }

  @Get('/year/list')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getYearAvailable(@Req() req: any) {
    const response = await this.ekta.getYearAvailable(req);

    return response;
  }
}
