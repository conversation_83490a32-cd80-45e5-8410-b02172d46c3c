import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { MasterEktaServiceV2 } from '../service/master-ekta-v2.service';
import { CreateBankDto, CreateSatuanBankDto } from '../dto/create.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { Module, Permission } from 'src/core/decorators';
import { MODULES } from 'src/core/constants/module.constant';

@Controller('master-ekta-v2')
@Module(MODULES.DIGITAL_KTA)
@UseGuards(JwtAuthGuard)
export class MasterEktaControllerV2 {
  private readonly logger = new Logger(MasterEktaControllerV2.name);

  constructor(private readonly ekta: MasterEktaServiceV2) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getBank(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(HttpStatus.OK)
  async createBank(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateBankDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)}`,
    );
    const response = await this.ekta.createBank(req, body);
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  async updateBank(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: CreateBankDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)} and id: ${id}`,
    );
    const response = await this.ekta.updateBank(req, Number(id), body);
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)} and id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(HttpStatus.OK)
  async deleteBank(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getList.name} with id: ${id}`);
    const response = await this.ekta.deleteBank(req, Number(id));
    this.logger.log(
      `Entering ${this.getList.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/satuan')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getListSatuan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListSatuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.ekta.getSatuanBank(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Entering ${this.getListSatuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/satuan/auth')
  @Permission('PERMISSION_READ')
  @HttpCode(HttpStatus.OK)
  async getSatuanBankAuth(@Req() req: any) {
    this.logger.log(`Entering ${this.getSatuanBankAuth.name} `);
    const response = await this.ekta.getSatuanBankAuth(req);
    this.logger.log(`Entering ${this.getSatuanBankAuth.name}`);

    return response;
  }

  @Put('/satuan/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  async updateSatuanBank(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: CreateSatuanBankDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)} and id: ${id}`,
    );
    const response = await this.ekta.updateSatuanBank(req, Number(id), body);
    this.logger.log(
      `Entering ${this.getList.name} with body data: ${JSON.stringify(body)} and id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Patch('/satuan/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(HttpStatus.OK)
  async acceptSatuanBank(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: any,
  ) {
    this.logger.log(`Entering ${this.acceptSatuanBank.name} with ${id}`);
    const response = await this.ekta.acceptSatuanBank(req, Number(id), body);
    this.logger.log(
      `Entering ${this.acceptSatuanBank.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
