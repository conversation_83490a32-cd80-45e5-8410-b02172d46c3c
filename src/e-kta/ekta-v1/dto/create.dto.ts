import { Type } from 'class-transformer';
import {
  Is<PERSON>rray,
  IsNotEmpty,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';

class NestedDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  readonly nrp: string;
}

export class CreateDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NestedDto)
  data: NestedDto[];
}
