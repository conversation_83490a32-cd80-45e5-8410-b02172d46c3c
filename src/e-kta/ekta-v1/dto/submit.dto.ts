import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';

class NestedDto {
  @IsNumber()
  @IsNotEmpty()
  readonly id: number;
}

export class SubmitDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NestedDto)
  ekta: NestedDto[];

  @IsString()
  @IsNotEmpty()
  readonly ekta_bank: string;
}

export class SubmitDraft {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NestedDto)
  ekta: NestedDto[];
}
