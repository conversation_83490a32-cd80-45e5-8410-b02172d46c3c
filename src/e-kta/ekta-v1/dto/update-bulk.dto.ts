import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

class NestedDto {
  @IsNumber()
  @IsNotEmpty()
  readonly id: number;

  @IsString()
  @IsOptional()
  readonly ekta_bank: string;

  @IsNumber()
  @IsOptional()
  readonly status: number;
}

export class UpdateBulkDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NestedDto)
  data: NestedDto[];
}
