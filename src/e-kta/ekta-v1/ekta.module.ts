import { forwardRef, Module } from '@nestjs/common';
import { EktaController } from './controller/ekta.controller';
import { EktaService } from './service/ekta.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { StatistikController } from './controller/ekta.statistik.controller';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [EktaController, StatistikController],
  providers: [EktaService, MinioService],
})
export class EktaModule {}
