import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { EktaService } from '../service/ekta.service';
import { CreateDto } from '../dto/create.dto';
import { UpdateDto } from '../dto/update.dto';
import { UpdateBulkDto } from '../dto/update-bulk.dto';
import { DeleteBulkDto } from '../dto/delete-bulk.dto';
import { SubmitDto } from '../dto/submit.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { RekomendasiUlangDto } from '../dto/rekomendasiulang.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('ekta')
@UseGuards(JwtAuthGuard)
export class EktaController {
  private readonly logger = new Logger(EktaController.name);

  constructor(private readonly ekta: EktaService) {}

  @Get('/')
  @HttpCode(200)
  async getList(@Req() req: any, @Query() query: any) {
    this.logger.log(
      `Entering ${this.getList.name} with query: ${JSON.stringify(query)}`,
    );

    const response = await this.ekta.getData(req, query);

    this.logger.log(
      `Leaving ${this.getList.name} with query ${JSON.stringify(query)} response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel')
  @HttpCode(200)
  async getPersonel(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPersonel.name} with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.ekta.getPersonel(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getPersonel.name} with paginationData: ${JSON.stringify(paginationData)} and searchandsortData: ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('total-personel')
  @HttpCode(200)
  async getTotalPersonel(@Req() req: any) {
    this.logger.log(`Entering ${this.getTotalPersonel.name}`);

    const response = await this.ekta.getPersonelCount(req);

    this.logger.log(
      `Leaving ${this.getTotalPersonel.name} with response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/draft')
  @HttpCode(200)
  async getDraft(@Req() req: any, @Query() query: any) {
    this.logger.log(
      `Entering ${this.getDraft.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.ekta.getDataDraft(req, query);
    this.logger.log(
      `Leaving ${this.getDraft.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/ditolak')
  @HttpCode(200)
  async getDitolak(@Req() req: any, @Query() query: any) {
    this.logger.log(
      `Entering ${this.getDitolak.name} with query: ${JSON.stringify(query)}`,
    );

    const response = await this.ekta.getEktaDitolak(req, query);
    this.logger.log(
      `Leaving ${this.getDitolak.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('rekomendasi-ulang')
  @HttpCode(201)
  async rekomendasiUlang(
    @Body() payload: RekomendasiUlangDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.rekomendasiUlang.name} with body: ${JSON.stringify(payload)}`,
    );

    const response = await this.ekta.rekomendasiUlang(payload, req);

    this.logger.log(
      `Leaving ${this.rekomendasiUlang.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/batch/:batchId')
  @HttpCode(200)
  async getByBatchId(
    @Req() req: any,
    @Param('batchId') batchId: string,
    @Query() query: any,
  ) {
    this.logger.log(
      `Entering ${this.getByBatchId.name} with batchId: ${batchId} and query: ${JSON.stringify(query)}`,
    );
    const batchIdNum = parseInt(batchId, 10);

    const response = await this.ekta.getByBatchId(req, batchIdNum, query);

    this.logger.log(
      `Leaving ${this.getByBatchId.name} with batchId: ${batchId} and query: ${JSON.stringify(query)} response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/')
  @HttpCode(201)
  async create(@Req() req: any, @Body() payload: CreateDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.createData(payload, req);
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('')
  @HttpCode(201)
  async updateBulk(@Req() req: any, @Body() payload: UpdateBulkDto) {
    this.logger.log(
      `Entering ${this.updateBulk.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.updateBulkData(req, payload);
    this.logger.log(
      `Leaving ${this.updateBulk.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('/status')
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(201)
  async editStatusPengajuan(
    @Req() req,
    @Body() payload: { id: string; jenis_permintaan: string },
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.editStatusPengajuan.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.editStatusPengajuan(
      req,
      Number(payload.id),
      file,
      Number(payload.jenis_permintaan),
    );
    this.logger.log(
      `Leaving ${this.editStatusPengajuan.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('')
  @HttpCode(201)
  async deleteBulk(@Req() req: any, @Body() payload: DeleteBulkDto) {
    this.logger.log(
      `Entering ${this.deleteBulk.name} with body: ${JSON.stringify(payload)}`,
    );

    const response = await this.ekta.deleteBulkData(req, payload);

    this.logger.log(
      `Leaving ${this.deleteBulk.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('submit')
  async submit(@Body() payload: SubmitDto, @Req() req: any) {
    this.logger.log(
      `Entering ${this.submit.name} with body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.submitPengajuan(payload, req);
    this.logger.log(
      `Leaving ${this.submit.name} with body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get(':id')
  @HttpCode(200)
  async getFirst(@Param('id') id: any, @Req() req: any) {
    this.logger.log(`Entering ${this.getFirst.name} with id: ${id}`);
    const response = await this.ekta.getFirst(req, id);
    this.logger.log(
      `Leaving ${this.getFirst.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch(':id')
  @HttpCode(201)
  async update(
    @Param('id') id: any,
    @Body() payload: UpdateDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(payload)}`,
    );
    const response = await this.ekta.rejectData(id, payload, req);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(payload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @HttpCode(201)
  async delete(@Req() req: any, @Param('id') id: any) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.ekta.deleteData(req, id);
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
