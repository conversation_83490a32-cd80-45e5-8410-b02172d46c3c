import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { EktaService } from '../service/ekta.service';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('statistik')
@UseGuards(JwtAuthGuard)
export class StatistikController {
  private readonly logger = new Logger(StatistikController.name);

  constructor(private readonly ekta: EktaService) {}

  @Get('total/:year')
  @HttpCode(200)
  async getTotalByBankType(
    @Req() req: any,
    @Param('year') year: string,
    @Query('status') status?: 'pengajuan' | 'realisasi' | 'ditolak',
  ) {
    this.logger.log(
      `Entering ${this.getTotalByBankType.name} with year: ${year} and status: ${status}`,
    );
    const response = await this.ekta.getTotalByBankType(
      req,
      Number(year),
      status,
    );
    this.logger.log(
      `Leaving ${this.getTotalByBankType.name} with year: ${year} and status: ${status} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('monthly/:year')
  @HttpCode(200)
  async getMonthlyArchivedByBankType(
    @Req() req: any,
    @Param('year') year: string,
    @Query('status') status?: 'pengajuan' | 'realisasi' | 'ditolak',
  ) {
    this.logger.log(
      `Entering ${this.getMonthlyArchivedByBankType.name} with year: ${year} and status: ${status}`,
    );
    const response = await this.ekta.getMonthlyArchivedByBankType(
      req,
      Number(year),
      status,
    );
    this.logger.log(
      `Leaving ${this.getMonthlyArchivedByBankType.name} with year: ${year} and status: ${status} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('total/satuan/:year')
  async getTotalBySatuanMonthYear(
    @Req() req: any,
    @Param('year') year: string,
    @Query('bank') bank?: string,
  ) {
    this.logger.log(
      `Entering ${this.getTotalBySatuanMonthYear.name} with year: ${year} and bank: ${bank}`,
    );
    const response = await this.ekta.getTotalBySatuanMonthYear(
      req,
      Number(year),
      bank,
    );
    this.logger.log(
      `Leaving ${this.getTotalBySatuanMonthYear.name} with year: ${year} and bank: ${bank} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
