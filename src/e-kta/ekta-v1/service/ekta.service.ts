import {
  BadGatewayException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateDto } from '../dto/create.dto';
import { Prisma } from '@prisma/client';
import { UpdateDto } from '../dto/update.dto';
import { UpdateBulkDto } from '../dto/update-bulk.dto';
import { SubmitDto } from '../dto/submit.dto';
import { romanMonth } from 'src/core/utils/common.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { RekomendasiUlangDto } from '../dto/rekomendasiulang.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class EktaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getData(req: any, query: any) {
    const { status, page = 1, limit = 10 } = query;
    const where = status !== undefined ? { status: Number(status) } : {};

    try {
      const [datas, totalData] = await this.prisma.$transaction([
        this.prisma.ekta.findMany({
          select: {
            id: true,
            tanggal: true,
            status: true,
            ekta_bank: true,
            ekta_rejection: {
              select: {
                rejection_reason: true,
                created_by_users: {
                  select: {
                    personel: true,
                  },
                },
              },
            },
            created_by_users: {
              select: {
                personel: true,
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: { nama: true },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: { select: { nama: true } },
                      },
                    },
                  },
                  orderBy: { created_at: 'desc' },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: (page - 1) * limit,
          where,
        }),
        this.prisma.ekta.count({ where }),
      ]);

      if (!datas) {
        throw new NotFoundException('EKTA Tidak ditemukan');
      }

      const queryResult = await Promise.all(
        datas.map(async (item) => ({
          ...item,
          personel: item.personel
            ? {
                ...item.personel,
                foto_file: await this.minioService.convertFileKeyToURL(
                  `${process.env.MINIO_BUCKET_NAME}`,
                  `${process.env.MINIO_PATH_FILE}${item.personel.nrp}/${item.personel.foto_file}`,
                ),
              }
            : null,
        })),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: Math.ceil(totalData / limit),
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getFirst(req: any, id) {
    try {
      const data = await this.prisma.ekta.findFirst({
        where: {
          id: Number(id),
        },
        select: {
          id: true,
          tanggal: true,
          status: true,
          ekta_bank: true,
          jenis_permintaan: true,
          surat_permintaan: true,
          ekta_rejection: {
            select: {
              rejection_reason: true,
              created_by_users: {
                select: {
                  personel: true,
                },
              },
            },
          },
          created_by_users: {
            select: {
              personel: true,
            },
          },
          personel: {
            select: {
              nama_lengkap: true,
              nrp: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      nama: true,
                    },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: {
                        select: {
                          nama: true,
                        },
                      },
                    },
                  },
                },
                take: 1,
              },
            },
          },
        },
      });

      if (!data) {
        throw new NotFoundException('EKTA Tidak ditemukan');
      }

      const queryResult = {
        ...data,
        foto_file: await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${data.personel.nrp}/${data.personel.foto_file}`,
        ),
      };
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getDataDraft(req: any, query) {
    const { page = 1, limit = 10 } = query;

    let where = {};

    where = {
      ...where,
      status: 0,
    };

    try {
      const [datas, totalData] = await this.prisma.$transaction([
        this.prisma.ekta.findMany({
          select: {
            id: true,
            tanggal: true,
            status: true,
            ekta_bank: true,
            ekta_rejection: {
              select: {
                rejection_reason: true,
                created_by_users: {
                  select: {
                    personel: true,
                  },
                },
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: (page - 1) * limit,
          where: where,
        }),
        this.prisma.ekta.count({
          where: where,
        }),
      ]);

      if (!datas) {
        throw new NotFoundException('EKTA Tidak ditemukan');
      }

      const queryResult = await Promise.all(
        datas.map(async (item) => ({
          ...item,
          foto_file: await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${item.personel?.nrp}/${item.personel?.foto_file}`,
          ),
        })),
      );

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getEktaDitolak(req: any, query) {
    const { page = 1, limit = 10 } = query;

    let where = {};

    where = {
      ...where,
      status: 99,
    };

    try {
      const [data, totalData] = await this.prisma.$transaction([
        this.prisma.ekta.findMany({
          select: {
            id: true,
            tanggal: true,
            status: true,
            ekta_bank: true,
            alasan_ditolak: true,
            ekta_rejection: {
              select: {
                rejection_reason: true,
                created_by_users: {
                  select: {
                    personel: true,
                  },
                },
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: (page - 1) * limit,
          where: where,
        }),
        this.prisma.ekta.count({
          where: where,
        }),
      ]);

      if (!data) {
        throw new NotFoundException('EKTA Tidak ditemukan');
      }

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: data,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async createData(payload: CreateDto, req: any) {
    try {
      const queryResult = await Promise.all(
        payload.data.map(async (v) => {
          const personel = await this.prisma.personel.findFirst({
            where: { nrp: v.nrp },
            select: {
              id: true,
              nama_lengkap: true,
              nrp: true,
              pangkat_personel: {
                select: {
                  pangkat: { select: { nama: true } },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: { select: { nama: true } },
                    },
                  },
                },
                orderBy: { created_at: 'desc' },
                take: 1,
              },
            },
          });
          if (!personel) {
            throw new NotFoundException(`Personel with NRP ${v.nrp} not found`);
          }

          if (!personel.pangkat_personel?.[0]?.pangkat?.nama) {
            throw new NotFoundException(`Pangkat not found for NRP ${v.nrp}`);
          }

          if (!personel.jabatan_personel?.[0]?.jabatans?.nama) {
            throw new NotFoundException(`Jabatan not found for NRP ${v.nrp}`);
          }

          if (!personel.jabatan_personel?.[0]?.jabatans?.satuan?.nama) {
            throw new NotFoundException(`Satuan not found for NRP ${v.nrp}`);
          }

          const data: Prisma.ektaCreateInput = {
            personel: { connect: { id: Number(personel.id) } },
            tanggal: new Date(),
            created_at: new Date(),
            status: 0,
            created_by_users: { connect: { id: Number(req.user?.['id']) } },
            nama: personel.nama_lengkap,
            nrp: v.nrp,
            jabatan: personel.jabatan_personel[0].jabatans.nama,
            pangkat: personel.pangkat_personel[0].pangkat.nama,
            satuan: personel.jabatan_personel[0].jabatans.satuan.nama,
          };

          return await this.prisma.ekta.create({
            data,
            select: { id: true },
          });
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err;
      }
      throw new BadGatewayException(
        err.message || 'Failed to create EKTA data',
      );
    }
  }

  async rekomendasiUlang(payload: RekomendasiUlangDto, req: any) {
    try {
      const queryResult = await Promise.all(
        payload.data.map(async (v) => {
          const rejected = await this.prisma.ekta.findFirst({
            where: {
              id: Number(v.id),
              status: 99,
            },
          });

          if (!rejected) {
            throw new NotFoundException(
              `Rejected EKTA with ID ${v.id} not found`,
            );
          }

          return await this.prisma.ekta.update({
            where: {
              id: rejected.id,
            },
            data: {
              ekta_batch_id: null,
              status: 0,
              alasan_ditolak: null,
            },
            select: { id: true },
          });
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err;
      }
      throw new BadGatewayException(
        err.message || 'Failed to create EKTA data',
      );
    }
  }

  async rejectData(id: any, payload: UpdateDto, req: any) {
    try {
      Logger.debug(payload);
      const queryResult = await this.prisma.ekta.update({
        data: {
          status: Number(payload.status),
          alasan_ditolak: payload.alasan_ditolak ?? null,
          updated_at: new Date(),
        },
        where: {
          id: Number(id),
        },
        include: {
          ekta_batch: true,
        },
      });

      await this.prisma.ekta_rejection.create({
        data: {
          ekta_id: queryResult.id,
          user_id: req.user?.['id'],
          rejection_reason: payload.alasan_ditolak ?? null,

          created_by: req.user?.['id'],
          created_at: new Date(),
        },
      });

      if (queryResult.ekta_batch_id && Number(payload.status) === 99) {
        const batchRecords = await this.prisma.ekta.findMany({
          where: {
            ekta_batch_id: queryResult.ekta_batch_id,
          },
          select: {
            status: true,
          },
        });

        const allRejected = batchRecords.every(
          (record) => record.status === 99,
        );

        if (allRejected) {
          await this.prisma.ekta_batch.update({
            where: {
              id: queryResult.ekta_batch_id,
            },
            data: {
              status: 99,
            },
          });
        }
      }
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async updateBulkData(req: any, payload: UpdateBulkDto) {
    try {
      const queryResult = await Promise.all(
        payload.data.map(async (v) => {
          const { status, id, ekta_bank } = v;

          return await this.prisma.ekta.update({
            where: {
              id: Number(id),
            },
            data: {
              status: Number(status),
              ekta_bank,
            },
            select: {
              id: true,
            },
          });
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async submitPengajuan(payload: SubmitDto, req: any) {
    const logger = new Logger('EktaService');

    const dataPerSatuan = [];
    type Result = {
      satuan: string;
      ekta_id: number[];
    };

    await Promise.all(
      payload.ekta.map(async (v) => {
        const dataEkta = await this.prisma.ekta.findFirst({
          where: {
            id: Number(v.id),
            status: 0,
            created_at: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
              lt: new Date(
                new Date().getFullYear(),
                new Date().getMonth() + 1,
                1,
              ),
            },
          },
          select: {
            id: true,
            nrp: true,
            satuan: true,
          },
        });
        if (dataEkta) {
          dataPerSatuan.push({
            satuan: dataEkta.satuan,
            id: dataEkta.id,
          });
        }
      }),
    );

    const breakedDataPerSatuan = Object.values(
      dataPerSatuan.reduce<Record<string, Result>>((acc, item) => {
        if (!acc[item.satuan]) {
          acc[item.satuan] = { satuan: item.satuan, ekta_id: [] };
        }
        acc[item.satuan].ekta_id.push(item.id);
        return acc;
      }, {}),
    );

    const checkPreviousBatch = await this.prisma.ekta_batch.findFirst({
      where: {
        created_at: {
          gte: new Date(new Date().setMonth(new Date().getMonth() - 1)),
        },
      },
      orderBy: { id: 'desc' },
      select: { kode: true },
    });

    let seqNumber = 0;
    if (checkPreviousBatch) {
      seqNumber = Number(checkPreviousBatch.kode.split('/')[2]) + 1;
    }

    let modifiedSeqNumber: string;
    if (seqNumber < 10) {
      modifiedSeqNumber = String(seqNumber++).padStart(2, '0');
    } else {
      modifiedSeqNumber = String(seqNumber++);
    }

    const [queryResult] = await Promise.all(
      breakedDataPerSatuan.map(async (v) => {
        try {
          const ekta_bank = payload.ekta_bank;
          let status: number;
          let jalurPengajuan: string;

          if (req.user?.['level_name_new'] == 'Level 3') {
            status = 1;
            jalurPengajuan = 'biasa';
          } else if (req.user?.['level_name_new'] == 'Level 2') {
            status = 2;
            jalurPengajuan = 'biasa';
          } else if (req.user?.['level_name_new'] == 'Level 1') {
            status = 3;
            jalurPengajuan = 'khusus';
          }

          const getCurrentRomanMonth = romanMonth();
          const getCurrentYear = new Date().getFullYear();

          const kode = `${ekta_bank.trim()}-${jalurPengajuan}/EKTA/${modifiedSeqNumber}/${getCurrentRomanMonth}/${getCurrentYear}`;

          logger.debug(`Attempting to create batch with data:`, {
            kode,
            status,
            satuan: v.satuan,
            ekta_bank: payload.ekta_bank,
            created_by: req.user?.['personel_id'],
          });

          const insertBatch = await this.prisma.ekta_batch.create({
            data: {
              kode,
              status,
              satuan: v.satuan,
              ekta_bank: payload.ekta_bank,
              created_by: req.user?.['personel_id'],
              created_at: new Date(),
            },
            select: { id: true },
          });

          logger.debug(`Created batch with ID: ${insertBatch.id}`);

          await Promise.all(
            v.ekta_id.map(async (ektaId) => {
              await this.prisma.ekta.update({
                where: { id: Number(ektaId) },
                data: {
                  ekta_batch_id: insertBatch.id,
                  status,
                  ekta_bank,
                },
              });
              logger.debug(`Successfully updated ekta ID: ${ektaId}`);
            }),
          );
        } catch (error) {
          logger.error(
            `Error in batch processing: ${error.message}`,
            error.stack,
          );
          throw new BadGatewayException(error.message);
        }
      }),
    );
    logger.debug('submitPengajuan completed successfully');

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.EKTA_V1_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KTA_V1_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteBulkData(req: any, payload: any) {
    try {
      const dataToDelete = Array.isArray(payload) ? payload : payload.data;
      const queryResult = await this.prisma.$transaction(
        dataToDelete.map(async (v) => {
          const { id } = v;

          return await this.prisma.ekta.delete({
            where: {
              id: Number(id),
            },
            select: {
              id: true,
            },
          });
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async deleteData(req: any, id: any) {
    try {
      const queryResult = await this.prisma.ekta.delete({
        where: {
          id: Number(id),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async getTotalByBankType(
    req: any,
    year: number,
    status?: 'pengajuan' | 'realisasi' | 'ditolak',
  ) {
    try {
      let userSatuan: string;
      if (req.user?.['level_name_new'] !== 'Level 1') {
        userSatuan = req.user?.['satuan_nama'];
      }

      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31, 23, 59, 59, 999);

      let statusCondition: any;
      if (status === 'pengajuan') {
        statusCondition = { lt: 18 };
      } else if (status === 'realisasi') {
        statusCondition = 18;
      } else if (status === 'ditolak') {
        statusCondition = 99;
      }

      const where = {
        AND: [
          { satuan: userSatuan ? userSatuan : { not: null } },
          { tanggal: { gte: startDate, lte: endDate } },
          { ekta_bank: { not: null } },
          statusCondition ? { status: statusCondition } : {},
        ],
      };

      const results = await this.prisma.ekta.groupBy({
        by: ['ekta_bank'],
        where,
        _count: true,
      });

      const allBanks = ['BRI', 'BNI', 'MANDIRI', 'BTN', 'BSI'];
      const resultMap = new Map(
        results
          .filter((result) => result.ekta_bank)
          .map((result) => [result.ekta_bank, result._count]),
      );

      const queryResult = allBanks.map((bank) => ({
        bank,
        total: resultMap.get(bank) || 0,
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      Logger.error('Error in getTotalByBankType:', err);
      throw new BadGatewayException(err);
    }
  }

  async getMonthlyArchivedByBankType(
    req: any,
    year: number,
    status?: 'pengajuan' | 'realisasi' | 'ditolak',
  ) {
    Logger.debug('');
    try {
      let userSatuan: string;
      if (req.user?.['level_name_new'] !== 'Level 1') {
        userSatuan = req.user?.['satuan_nama'];
      }

      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31, 23, 59, 59, 999);

      let statusCondition: any;
      if (status === 'pengajuan') {
        statusCondition = { lt: 18 };
      } else if (status === 'realisasi') {
        statusCondition = 18;
      } else if (status === 'ditolak') {
        statusCondition = 99;
      }

      const results = await this.prisma.ekta.groupBy({
        by: ['ekta_bank', 'tanggal'],
        where: {
          AND: [
            { satuan: userSatuan ? userSatuan : { not: null } },
            { tanggal: { gte: startDate, lte: endDate } },
            { ekta_bank: { not: null } },
            statusCondition ? { status: statusCondition } : {},
          ],
        },
        _count: true,
      });

      const allBanks = ['BRI', 'BNI', 'MANDIRI', 'BTN', 'BSI'];
      const months = Array.from({ length: 12 }, (_, i) => i + 1);

      const countMap = new Map();

      allBanks.forEach((bank) => {
        const monthlyData = {};
        months.forEach((month) => {
          monthlyData[month] = 0;
        });
        countMap.set(bank, monthlyData);
      });

      results.forEach((result) => {
        if (result.ekta_bank) {
          const month = new Date(result.tanggal).getMonth() + 1;
          const currentCount = countMap.get(result.ekta_bank)[month] || 0;
          countMap.get(result.ekta_bank)[month] = currentCount + result._count;
        }
      });

      const queryResult = allBanks.map((bank) => ({
        bank,
        monthly_total: Object.fromEntries(
          months.map((month) => [
            `month_${month.toString().padStart(2, '0')}`,
            countMap.get(bank)[month],
          ]),
        ),
      }));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      Logger.error('Error in getMonthlyArchivedByBankType:', err);
      throw new BadGatewayException(err);
    }
  }

  async getByBatchId(
    req: any,
    batchId: number,
    query?: { page?: number; limit?: number },
  ) {
    const { page = 1, limit = 10 } = query || {};
    let status = 0;

    const levelAuth = req.user?.['level_name_new'];
    if (levelAuth === 'Level 3') {
      status = 1;
    } else if (levelAuth === 'Level 2') {
      status = 2;
    } else if (levelAuth === 'Level 1') {
      status = 3;
    } else {
      throw new BadGatewayException('Not Allowed Level');
    }
    try {
      const [
        datas,
        totalData,
        total_pengajuan,
        total_pengajuan_baru,
        total_pengajuan_lain,
      ] = await this.prisma.$transaction([
        this.prisma.ekta.findMany({
          where: {
            ekta_batch_id: batchId,
            // status: {
            //   equals: status,
            // },
          },
          select: {
            id: true,
            tanggal: true,
            status: true,
            ekta_bank: true,
            jenis_permintaan: true,
            surat_permintaan: true,
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: (page - 1) * limit,
        }),
        this.prisma.ekta.count({
          where: {
            ekta_batch_id: batchId,
            // status: status,
          },
        }),
        // Total pengajuan (all ekta)
        this.prisma.ekta.count({
          where: {
            ekta_batch_id: batchId,
            // status: {
            //   equals: status,
            // },
          },
        }),
        // Total pengajuan baru (jenis_permintaan = 1)
        this.prisma.ekta.count({
          where: {
            ekta_batch_id: batchId,
            jenis_permintaan: 1,
            // status: {
            //   equals: status,
            // },
          },
        }),
        // Total pengajuan lain (jenis_permintaan = 2)
        this.prisma.ekta.count({
          where: {
            ekta_batch_id: batchId,
            jenis_permintaan: 2,
            // status: {
            //   equals: status,
            // },
          },
        }),
      ]);

      if (!datas || datas.length === 0) {
        throw new NotFoundException('No EKTA records found for this batch');
      }

      const data = await Promise.all(
        datas.map(async (item) => ({
          ...item,
          personel: item.personel
            ? {
                ...item.personel,
                foto_file: await this.minioService.checkFileExist(
                  `${process.env.MINIO_BUCKET_NAME}`,
                  `${process.env.MINIO_PATH_FILE}${item.personel.nrp}/${item.personel.foto_file}`,
                ),
              }
            : null,
        })),
      );

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          data,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        total_pengajuan,
        total_pengajuan_baru,
        total_pengajuan_lain,
        data: data,
      };
    } catch (err) {
      throw new BadGatewayException(err);
    }
  }

  async editStatusPengajuan(
    req: any,
    id: number,
    file: Express.Multer.File | null,
    jenis_permintaan: number,
  ) {
    try {
      if (jenis_permintaan !== 1 && jenis_permintaan !== 2) {
        Logger.error('Invalid jenis_permintaan:', jenis_permintaan);
        throw new BadGatewayException(
          'Invalid jenis_permintaan. Must be 1 or 2',
        );
      }

      // For jenis_permintaan = 2, file is required
      if (jenis_permintaan === 2 && !file) {
        Logger.error('Missing file for jenis_permintaan 2');
        throw new BadGatewayException(
          'File is required for jenis_permintaan = 2',
        );
      }

      const updateData: any = {
        jenis_permintaan,
        updated_at: new Date(),
      };

      // Only upload and update file if jenis_permintaan = 2 and file exists
      if (jenis_permintaan === 2 && file) {
        const uploadedFile = await this.minioService.uploadFile(file);
        updateData.surat_permintaan = uploadedFile.Location;
      }

      const queryResult = await this.prisma.ekta.update({
        where: {
          id: Number(id),
        },
        data: updateData,
        select: {
          id: true,
          jenis_permintaan: true,
          surat_permintaan: true,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('EKTA not found');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err;
      }
      throw new BadGatewayException(
        err.message || 'Failed to update EKTA status',
      );
    }
  }

  async getPersonel(req: any, paginationData, searchandsortData) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      pangkat_nama: {
        field: 'pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      jabatan_nama: {
        field: 'jabatan_personel.some.jabatans.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
      'status_aktif.nama': { field: 'status_aktif.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: {
          ...where,
          jabatan_personel: {
            some: {
              jabatans: {
                satuan_id: req.user?.['satuan_id'],
              },
            },
          },
        },
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          foto_file: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: {
          ...where,
          jabatan_personel: {
            some: {
              jabatans: {
                satuan_id: req.user?.['satuan_id'],
              },
            },
          },
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const result = {
          ...personel,
          id: Number(personel.id),
          tanggal_lahir: personel.tanggal_lahir
            ? new Date(personel.tanggal_lahir).toISOString().split('T')[0]
            : null,
          foto_file: await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
          ),
          pangkat: personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
        };

        // Delete nested properties
        delete result.pangkat_personel;
        delete result.jabatan_personel;
        if (result.jabatan) delete result.jabatan.satuan;

        return result;
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EKTA_V1_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getPersonelCount(req: any) {
    const queryResult = await this.prisma.personel.count({
      where: {
        status_aktif_id: 1,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.EKTA_V1_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: {
        totalPersonel: queryResult,
      },
    };
  }

  async getTotalBySatuanMonthYear(req: any, year: number, bank?: string) {
    try {
      let userSatuan: string;

      if (req.user?.['level_name_new'] !== 'Level 1') {
        userSatuan = req.user?.['satuan_nama'];
      }
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31, 23, 59, 59, 999);

      // First, get all unique satuan values
      const allSatuanQuery: Prisma.ektaWhereInput = {};
      if (userSatuan) {
        allSatuanQuery.satuan = userSatuan;
      }

      const allSatuan = await this.prisma.ekta.groupBy({
        by: ['satuan'],
        where: allSatuanQuery,
      });

      // Base conditions for the main query
      const baseConditions: Prisma.ektaWhereInput[] = [
        { tanggal: { gte: startDate, lte: endDate } },
      ];

      // Handle different scenarios
      if (userSatuan && bank) {
        baseConditions.push({ satuan: userSatuan }, { ekta_bank: bank });
      } else if (userSatuan) {
        baseConditions.push({ satuan: userSatuan });
      } else if (bank) {
        baseConditions.push({ ekta_bank: bank });
      }

      const results = await this.prisma.ekta.groupBy({
        by: ['satuan', 'tanggal', 'ekta_bank'],
        where: {
          AND: baseConditions,
        },
        _count: true,
      });

      // Create a map to store monthly totals for each satuan
      const satuanMonthlyTotals = new Map();

      // Initialize all satuan with zero counts
      allSatuan.forEach((satuan) => {
        satuanMonthlyTotals.set(satuan.satuan, Array(12).fill(0));
      });

      // Update counts from actual results
      results.forEach((result) => {
        const month = new Date(result.tanggal).getMonth() + 1;
        const satuanKey = result.satuan;
        const monthlyTotals = satuanMonthlyTotals.get(satuanKey);
        if (monthlyTotals) {
          monthlyTotals[month - 1] += result._count;
        }
      });

      // Convert map to array of objects with monthly totals
      const queryResult = Array.from(satuanMonthlyTotals.entries()).map(
        ([satuan, monthlyTotals]) => ({
          satuan,
          monthly_total: Object.fromEntries(
            monthlyTotals.map((total, index) => [
              `month_${(index + 1).toString().padStart(2, '0')}`,
              total,
            ]),
          ),
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.EKTA_V1_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.E_KTA_V1_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: {
          totalPersonel: queryResult,
        },
      };
    } catch (err) {
      Logger.error('Error in getTotalBySatuanMonthYear:', err);
      throw new BadGatewayException(err);
    }
  }
}
