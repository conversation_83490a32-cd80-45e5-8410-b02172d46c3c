import { PartialType } from '@nestjs/mapped-types';
import { Decimal } from '@prisma/client/runtime/library';
import { float } from 'aws-sdk/clients/cloudfront';
import { Transform, Type } from 'class-transformer';
import {
  IsDecimal,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateJasmaniPersonelDto {
  @IsNotEmpty()
  personel_id: number;

  @IsNotEmpty()
  @Transform((value) => Number(value))
  tahun: number;

  @IsNotEmpty()
  @Transform((value) => Number(value))
  semester: number;

  @IsNotEmpty()
  nilai_akhir: float;

  @IsOptional()
  @IsString()
  @Type(() => String)
  nilai_file: String;

  @IsOptional()
  @IsString()
  @Type(() => String)
  keterangan: String;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_lari_12_m: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_lari_12_m: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_pull_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_pull_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_push_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_push_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_sit_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_sit_up: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_shutle_run: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_shutle_run: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_chinning: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  hasil_chinning: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_a: Decimal;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_b: Decimal;
}

export class QueryGetNilaiDto {
  @IsNotEmpty()
  @IsString()
  golongan: string;

  @IsNotEmpty()
  @IsString()
  jenis_kelamin: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  nilai_lari_12_m: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  nilai_pull_up: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  nilai_push_up: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  nilai_sit_up: number;

  @IsOptional()
  @IsDecimal()
  @Type(() => Decimal)
  nilai_shutle_run: Decimal;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  nilai_chinning: number;
}

export class UpdateJasmaniPersonelDto extends PartialType(
  CreateJasmaniPersonelDto,
) {}
