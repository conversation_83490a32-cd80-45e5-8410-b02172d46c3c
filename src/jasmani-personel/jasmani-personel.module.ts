import { forwardRef, Module } from '@nestjs/common';
import { JasmaniPersonelService } from './service/jasmani-personel.service';
import { JasmaniPersonelController } from './controller/jasmani-personel.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';
import { MinioService } from '../api-utils/minio/service/minio.service';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [JasmaniPersonelController],
  providers: [JasmaniPersonelService, MinioService],
})
export class JasmaniPersonelModule {}
