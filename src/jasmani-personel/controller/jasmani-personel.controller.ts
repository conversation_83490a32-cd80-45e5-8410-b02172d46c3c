import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  ParseArrayPipe,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { JasmaniPersonelService } from '../service/jasmani-personel.service';
import {
  CreateJasmaniPersonelDto,
  QueryGetNilaiDto,
} from '../dto/jasmani-personel.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Response } from 'express';

@Controller('jasmani-personel')
@UseGuards(JwtAuthGuard)
export class JasmaniPersonelController {
  private readonly logger = new Logger(JasmaniPersonelController.name);

  constructor(
    private readonly jasmaniPersonelService: JasmaniPersonelService,
  ) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreateJasmaniPersonelDto) {
    this.logger.log(
      `Entering ${this.create.name} jasmani personel with body: ${JSON.stringify(body)}`,
    );
    const response = await this.jasmaniPersonelService.create(req, body);
    this.logger.log(
      `Leaving ${this.create.name} jasmani personel with body ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/create/bulk')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createBulk(
    @Req() req: any,
    @Body(new ParseArrayPipe({ items: CreateJasmaniPersonelDto }))
    body: CreateJasmaniPersonelDto[],
  ) {
    this.logger.log(
      `Entering ${this.createBulk.name} jasmani personel with body: ${JSON.stringify(body)}`,
    );
    const response = await this.jasmaniPersonelService.createBulk(req, body);
    this.logger.log(
      `Leaving ${this.createBulk.name} jasmani personel with body ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} jasmani personel with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.jasmaniPersonelService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} jasmani personel with paginationData: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/get-nilai')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getNilai(@Req() req: any, @Query() queries: QueryGetNilaiDto) {
    this.logger.log(
      `Entering ${this.getNilai.name} jasmani personel with queries: ${JSON.stringify(queries)}`,
    );
    const response = await this.jasmaniPersonelService.getNilaiJasmani(
      req,
      queries,
    );
    this.logger.log(
      `Leaving ${this.getNilai.name} jasmani personel with queries: ${JSON.stringify(queries)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/download-template')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async downloadTemplate(
    @Req() req: any,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Entering ${this.downloadTemplate.name} jasmani personel`);
    const buffer = await this.jasmaniPersonelService.generateExcelTemplate(req);
    // Set the response headers and send the file
    response.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    response.setHeader(
      'Content-Disposition',
      'attachment; filename=Template-Jasmani-Personel.xlsx',
    );
    response.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(`Leaving ${this.downloadTemplate.name} jasmani personel`);

    response.end(buffer);
  }
}
