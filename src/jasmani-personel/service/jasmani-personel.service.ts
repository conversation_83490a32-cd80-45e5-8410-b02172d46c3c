import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import {
  CreateJasmaniPersonelDto,
  QueryGetNilaiDto,
} from '../dto/jasmani-personel.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import * as ExcelJS from 'exceljs';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import {
  addDataMasterToSheet,
  applyFormulaIfAny,
  applyValidationIfAny,
} from '../../core/utils/excel.utils';
import {
  IExcelColumns,
  IExcelMasterData,
} from '../../core/interfaces/excel.interface';

@Injectable()
export class JasmaniPersonelService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      //   nilai: { field: 'nilai', type: 'number' },
      //   nilai: { field: 'nilai', type: 'number' },
      nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
      personel_id: {
        field: 'personel.id',
        type: 'bigint',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.jasmani_personel.count({
        where: where,
      }),
      this.prisma.jasmani_personel.findMany({
        select: {
          id: true,
          personel: {
            select: {
              id: true,
              nama_lengkap: true,
              nrp: true,
              jenis_kelamin: true,
            },
          },
          tahun: true,
          semester: true,
          nilai_a: true,
          nilai_b: true,
          nilai_akhir: true,
          nilai_lari_12_m: true,
          hasil_lari_12_m: true,
          nilai_pull_up: true,
          hasil_pull_up: true,
          nilai_push_up: true,
          hasil_push_up: true,
          nilai_sit_up: true,
          hasil_sit_up: true,
          nilai_shutle_run: true,
          hasil_shutle_run: true,
          nilai_chinning: true,
          hasil_chinning: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JASMANI_PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getNilaiJasmani(req, queries: QueryGetNilaiDto) {
    let getNilaiLari = null;
    let batasLari = null;
    let getNilaiPullUp = null;
    let batasPullUp = null;
    let getNilaiPushUp = null;
    let batasPushUp = null;
    let getNilaiSitUp = null;
    let batasSitUp = null;
    let getNilaiShutleRun = null;
    let batasShutleRun = null;
    let getNilaiChinning = null;
    let batasChinning = null;

    if (queries.nilai_lari_12_m) {
      getNilaiLari = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'LARI 12 MENIT',
          batas_bawah: {
            lte: queries.nilai_lari_12_m,
          },
          batas_atas: {
            gte: queries.nilai_lari_12_m,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasLari = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'LARI 12 MENIT',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        +queries.nilai_lari_12_m > batasLari._max.batas_atas ||
        +queries.nilai_lari_12_m < batasLari._min.batas_bawah
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasLari._min.batas_bawah} dan ${batasLari._max.batas_atas} yang sesuai`,
        );
      }
    }

    if (queries.nilai_pull_up) {
      getNilaiPullUp = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'PULL UP',
          batas_bawah: {
            lte: queries.nilai_pull_up,
          },
          batas_atas: {
            gte: queries.nilai_pull_up,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasPullUp = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'PULL UP',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        +queries.nilai_pull_up > batasPullUp._max.batas_atas ||
        +queries.nilai_pull_up < batasPullUp._min.batas_bawah
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasPullUp._min.batas_bawah} dan ${batasPullUp._max.batas_atas} yang sesuai`,
        );
      }
    }

    if (queries.nilai_push_up) {
      getNilaiPushUp = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'PUSH UP',
          batas_bawah: {
            lte: queries.nilai_push_up,
          },
          batas_atas: {
            gte: queries.nilai_push_up,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasPushUp = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'PUSH UP',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        +queries.nilai_push_up > batasPushUp._max.batas_atas ||
        +queries.nilai_push_up < batasPushUp._min.batas_bawah
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasPushUp._min.batas_bawah} dan ${batasPushUp._max.batas_atas} yang sesuai`,
        );
      }
    }

    if (queries.nilai_sit_up) {
      getNilaiSitUp = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'SIT UP',
          batas_bawah: {
            lte: queries.nilai_sit_up,
          },
          batas_atas: {
            gte: queries.nilai_sit_up,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasSitUp = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'SIT UP',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        +queries.nilai_sit_up > batasSitUp._max.batas_atas ||
        +queries.nilai_sit_up < batasSitUp._min.batas_bawah
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasSitUp._min.batas_bawah} dan ${batasSitUp._max.batas_atas} yang sesuai`,
        );
      }
    }

    if (queries.nilai_shutle_run) {
      getNilaiShutleRun = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'SHUTLE RUN',
          batas_bawah: {
            lte: queries.nilai_shutle_run,
          },
          batas_atas: {
            gte: queries.nilai_shutle_run,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasShutleRun = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'SHUTLE RUN',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        queries.nilai_shutle_run > batasShutleRun._max.batas_atas.toNumber() ||
        queries.nilai_shutle_run < batasShutleRun._min.batas_bawah.toNumber()
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasShutleRun._min.batas_bawah.toNumber()} dan ${batasShutleRun._max.batas_atas.toNumber()} yang sesuai`,
        );
      }
    }

    if (queries.nilai_chinning) {
      getNilaiChinning = await this.prisma.binjas_formula.findFirst({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'CHINNING',
          batas_bawah: {
            lte: queries.nilai_chinning,
          },
          batas_atas: {
            gte: queries.nilai_chinning,
          },
        },
        select: {
          nilai: true,
        },
      });

      batasChinning = await this.prisma.binjas_formula.aggregate({
        where: {
          jenis_kelamin: queries.jenis_kelamin,
          golongan: queries.golongan,
          gerakan: 'CHINNING',
        },
        _max: {
          batas_atas: true,
        },
        _min: {
          batas_bawah: true,
        },
      });

      if (
        queries.nilai_chinning > batasChinning._max.batas_atas.toNumber() ||
        queries.nilai_chinning < batasChinning._min.batas_bawah.toNumber()
      ) {
        throw new BadRequestException(
          `Masukkan nilai antara ${batasChinning._min.batas_bawah.toNumber()} dan ${batasChinning._max.batas_atas.toNumber()} yang sesuai`,
        );
      }
    }

    const nilai_a = parseInt(getNilaiLari?.nilai) || 0;

    let nilai_b = null;
    if (queries.jenis_kelamin === 'LAKI-LAKI') {
      nilai_b =
        ((parseInt(getNilaiPullUp?.nilai) || 0) +
          (parseInt(getNilaiPushUp?.nilai) || 0) +
          (parseInt(getNilaiSitUp?.nilai) || 0) +
          (parseInt(getNilaiShutleRun?.nilai) || 0)) /
        4;
    }

    if (queries.jenis_kelamin === 'PEREMPUAN') {
      nilai_b =
        ((parseInt(getNilaiPullUp?.nilai) || 0) +
          (parseInt(getNilaiPushUp?.nilai) || 0) +
          (parseInt(getNilaiSitUp?.nilai) || 0) +
          (parseInt(getNilaiChinning?.nilai) || 0)) /
        4;
    }

    const nilai_akhir = (nilai_a + nilai_b) / 2;

    const queryResult = {
      nilai_lari_12_m: queries.nilai_lari_12_m ?? 0,
      hasil_lari_12_m: parseInt(getNilaiLari?.nilai) ?? 0,
      nilai_pull_up: queries.nilai_pull_up ?? 0,
      hasil_pull_up: parseInt(getNilaiPullUp?.nilai) ?? 0,
      nilai_push_up: queries.nilai_push_up ?? 0,
      hasil_push_up: parseInt(getNilaiPushUp?.nilai) ?? 0,
      nilai_sit_up: queries.nilai_sit_up ?? 0,
      hasil_sit_up: parseInt(getNilaiSitUp?.nilai) ?? 0,
      nilai_shutle_run: queries.nilai_shutle_run ?? 0,
      hasil_shutle_run: parseInt(getNilaiShutleRun?.nilai) ?? 0,
      nilai_chinning: queries.nilai_chinning ?? 0,
      hasil_chinning: parseInt(getNilaiChinning?.nilai) ?? 0,
      nilai_a: nilai_a ?? 0,
      nilai_b: nilai_b ?? 0,
      nilai_akhir: parseFloat((nilai_akhir ?? 0).toFixed(2)),
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JASMANI_PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async create(req, body: CreateJasmaniPersonelDto) {
    if (
      await this.prisma.jasmani_personel.findFirst({
        where: {
          tahun: body.tahun,
          semester: body.semester,
          personel_id: body.personel_id,
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('jasmanai personel sudah ada');
    }

    const nilai_akhir = (Number(body.nilai_a) + Number(body.nilai_b)) / 2;

    let keterangan;

    if (nilai_akhir >= 81) {
      keterangan = 'BS';
    } else if (nilai_akhir >= 61) {
      keterangan = 'B';
    } else if (nilai_akhir >= 41) {
      keterangan = 'C';
    } else {
      keterangan = 'KS';
    }

    const queryResult = await this.prisma.jasmani_personel.create({
      data: {
        personel_id: body.personel_id,
        tahun: body.tahun,
        semester: body.semester,
        nilai_akhir: Number(nilai_akhir.toFixed(2)),
        nilai_lari_12_m: body.nilai_lari_12_m,
        hasil_lari_12_m: body.hasil_lari_12_m,
        nilai_pull_up: body.nilai_pull_up,
        hasil_pull_up: body.hasil_pull_up,
        nilai_push_up: body.nilai_push_up,
        hasil_push_up: body.hasil_push_up,
        nilai_sit_up: body.nilai_sit_up,
        hasil_sit_up: body.hasil_sit_up,
        nilai_shutle_run: body.nilai_shutle_run,
        hasil_shutle_run: body.hasil_shutle_run,
        nilai_chinning: body.nilai_chinning,
        hasil_chinning: body.hasil_chinning,
        nilai_a: body.nilai_a,
        nilai_b: body.nilai_b,
        keterangan: keterangan,
        created_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JASMANI_PERSONEL_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  //! Note: yang di comment jangan dihapus dulu, buat jaga" butuh data master untuk membuat list nrp nya
  async generateExcelTemplate(req: any) {
    const workbook = new ExcelJS.Workbook();
    const mainSheet = workbook.addWorksheet('Template');

    // const masterPersonelPath = `Personel!A:F`;

    const columnDefinitions: IExcelColumns[] = [
      {
        header: 'Nrp',
        key: 'nrp',
        width: 15,
        // dataValidation: {
        //   type: 'list',
        //   sheet: 'Template',
        //   reference: {
        //     name: 'Personel',
        //     key: 'nrp',
        //   },
        // },
      },
      {
        header: 'Nama',
        key: 'nama',
        width: 15,
        // value: {
        //   formula:
        //     'IF(ISBLANK(|cell_1|), "", VLOOKUP(|cell_2|,|table_array_1|,|num_row|,FALSE))',
        //   result: '',
        //   columnKey: ['nrp', 'nrp'],
        //   tableNumRow: 2,
        //   tableArray: [masterPersonelPath],
        // },
      },
      {
        header: 'Jenis Kelamin',
        key: 'jenis_kelamin',
        width: 15,
        // value: {
        //   formula:
        //     'IF(ISBLANK(|cell_1|), "", VLOOKUP(|cell_2|,|table_array_1|,|num_row|,FALSE))',
        //   result: '',
        //   columnKey: ['nrp', 'nrp'],
        //   tableNumRow: 3,
        //   tableArray: [masterPersonelPath],
        // },
      },
      {
        header: 'Tahun',
        key: 'tahun',
        width: 10,
      },
      {
        header: 'Semester',
        key: 'semester',
        width: 10,
        dataValidation: {
          type: 'list',
          sheet: 'Template',
          reference: {
            name: 'Semester',
            key: 'semester',
          },
        },
      },
      {
        header: 'Golongan',
        key: 'golongan',
        width: 15,
        // value: {
        //   formula:
        //     'IF(ISBLANK(|cell_1|), "", VLOOKUP(|cell_2|,|table_array_1|,|num_row|,FALSE))',
        //   result: '',
        //   columnKey: ['nrp', 'nrp'],
        //   tableNumRow: 6,
        //   tableArray: [masterPersonelPath],
        // },
      },
      {
        header: 'Hasil Lari 12 M',
        key: 'hasil_lari_12_m',
        width: 15,
        dataValidation: {
          type: 'custom',
          sheet: 'Template',
          gerakan: 'LARI 12 MENIT',
          reference: {
            name: 'Group Binjas Formula',
            min: 'batas_bawah',
            max: 'batas_atas',
            groupBy: ['gerakan', 'jenis_kelamin', 'golongan'],
          },
        },
      },
      {
        header: 'Nilai Lari 12 M',
        key: 'nilai_lari_12_m',
        width: 15,
        value: {
          formula:
            'IF(ISBLANK(|cell_1|), "", INDEX(|table_array_1|, _xlfn.MATCH(1, (|table_array_2|=|cell_2|)*(|table_array_3|=|cell_3|)*(|table_array_4|="LARI 12 MENIT")*(|table_array_5|<=|cell_4|)*(|table_array_6|>=|cell_5|), 0)))',
          result: '',
          columnKey: [
            'hasil_lari_12_m',
            'jenis_kelamin',
            'golongan',
            'hasil_lari_12_m',
            'hasil_lari_12_m',
          ],
          tableNumRow: 4,
          tableArray: [
            "'Binjas Formula'!F:F",
            "'Binjas Formula'!A:A",
            "'Binjas Formula'!B:B",
            "'Binjas Formula'!C:C",
            "'Binjas Formula'!D:D",
            "'Binjas Formula'!E:E",
          ],
        },
      },
      {
        header: 'Hasil Pull Up',
        key: 'hasil_pull_up',
        width: 15,
        dataValidation: {
          type: 'custom',
          sheet: 'Template',
          gerakan: 'PULL UP',
          reference: {
            name: 'Group Binjas Formula',
            min: 'batas_bawah',
            max: 'batas_atas',
            groupBy: ['gerakan', 'jenis_kelamin', 'golongan'],
          },
        },
      },
      {
        header: 'Nilai Pull Up',
        key: 'nilai_pull_up',
        width: 15,
        value: {
          formula:
            'IF(ISBLANK(|cell_1|), "", INDEX(|table_array_1|, _xlfn.MATCH(1, (|table_array_2|=|cell_2|)*(|table_array_3|=|cell_3|)*(|table_array_4|="PULL UP")*(|table_array_5|<=|cell_4|)*(|table_array_6|>=|cell_5|), 0)))',
          result: '',
          columnKey: [
            'hasil_pull_up',
            'jenis_kelamin',
            'golongan',
            'hasil_pull_up',
            'hasil_pull_up',
          ],
          tableNumRow: 4,
          tableArray: [
            "'Binjas Formula'!F:F",
            "'Binjas Formula'!A:A",
            "'Binjas Formula'!B:B",
            "'Binjas Formula'!C:C",
            "'Binjas Formula'!D:D",
            "'Binjas Formula'!E:E",
          ],
        },
      },
      {
        header: 'Hasil Push Up',
        key: 'hasil_push_up',
        width: 15,
        dataValidation: {
          type: 'custom',
          sheet: 'Template',
          gerakan: 'PUSH UP',
          reference: {
            name: 'Group Binjas Formula',
            min: 'batas_bawah',
            max: 'batas_atas',
            groupBy: ['gerakan', 'jenis_kelamin', 'golongan'],
          },
        },
      },
      {
        header: 'Nilai Push Up',
        key: 'nilai_push_up',
        width: 15,
        value: {
          formula:
            'IF(ISBLANK(|cell_1|), "", INDEX(|table_array_1|, _xlfn.MATCH(1, (|table_array_2|=|cell_2|)*(|table_array_3|=|cell_3|)*(|table_array_4|="PUSH UP")*(|table_array_5|<=|cell_4|)*(|table_array_6|>=|cell_5|), 0)))',
          result: '',
          columnKey: [
            'hasil_push_up',
            'jenis_kelamin',
            'golongan',
            'hasil_push_up',
            'hasil_push_up',
          ],
          tableNumRow: 4,
          tableArray: [
            "'Binjas Formula'!F:F",
            "'Binjas Formula'!A:A",
            "'Binjas Formula'!B:B",
            "'Binjas Formula'!C:C",
            "'Binjas Formula'!D:D",
            "'Binjas Formula'!E:E",
          ],
        },
      },
      {
        header: 'Hasil Sit Up',
        key: 'hasil_sit_up',
        width: 15,
        dataValidation: {
          type: 'custom',
          sheet: 'Template',
          gerakan: 'SIT UP',
          reference: {
            name: 'Group Binjas Formula',
            min: 'batas_bawah',
            max: 'batas_atas',
            groupBy: ['gerakan', 'jenis_kelamin', 'golongan'],
          },
        },
      },
      {
        header: 'Nilai Sit Up',
        key: 'nilai_sit_up',
        width: 15,
        value: {
          formula:
            'IF(ISBLANK(|cell_1|), "", INDEX(|table_array_1|, _xlfn.MATCH(1, (|table_array_2|=|cell_2|)*(|table_array_3|=|cell_3|)*(|table_array_4|="SIT UP")*(|table_array_5|<=|cell_4|)*(|table_array_6|>=|cell_5|), 0)))',
          result: '',
          columnKey: [
            'hasil_sit_up',
            'jenis_kelamin',
            'golongan',
            'hasil_sit_up',
            'hasil_sit_up',
          ],
          tableNumRow: 4,
          tableArray: [
            "'Binjas Formula'!F:F",
            "'Binjas Formula'!A:A",
            "'Binjas Formula'!B:B",
            "'Binjas Formula'!C:C",
            "'Binjas Formula'!D:D",
            "'Binjas Formula'!E:E",
          ],
        },
      },
      {
        header: 'Hasil Shuttle Run',
        key: 'hasil_shutle_run',
        width: 20,
        dataValidation: {
          type: 'custom',
          sheet: 'Template',
          gerakan: 'SHUTLE RUN',
          reference: {
            name: 'Group Binjas Formula',
            min: 'batas_bawah',
            max: 'batas_atas',
            groupBy: ['gerakan', 'jenis_kelamin', 'golongan'],
          },
        },
      },
      {
        header: 'Nilai Shuttle Run',
        key: 'nilai_shutle_run',
        width: 20,
        value: {
          formula:
            'IF(ISBLANK(|cell_1|), "", INDEX(|table_array_1|, _xlfn.MATCH(1, (|table_array_2|=|cell_2|)*(|table_array_3|=|cell_3|)*(|table_array_4|="SHUTLE RUN")*(|table_array_5|<=|cell_4|)*(|table_array_6|>=|cell_5|), 0)))',
          result: '',
          columnKey: [
            'hasil_shutle_run',
            'jenis_kelamin',
            'golongan',
            'hasil_shutle_run',
            'hasil_shutle_run',
          ],
          tableNumRow: 4,
          tableArray: [
            "'Binjas Formula'!F:F",
            "'Binjas Formula'!A:A",
            "'Binjas Formula'!B:B",
            "'Binjas Formula'!C:C",
            "'Binjas Formula'!D:D",
            "'Binjas Formula'!E:E",
          ],
        },
      },
      {
        header: 'Nilai A',
        key: 'nilai_a',
        width: 20,
        value: {
          formula: 'IF(ISBLANK(|cell_1|), "", |cell_2|)',
          result: '',
          columnKey: ['nilai_lari_12_m', 'nilai_lari_12_m'],
        },
      },
      {
        header: 'Nilai B',
        key: 'nilai_b',
        width: 20,
        value: {
          formula:
            'IF(|cell_1|="LAKI-LAKI", AVERAGE(|cell_2|,|cell_3|,|cell_4|,|cell_5|), IF(A2="PEREMPUAN", AVERAGE(|cell_6|,|cell_7|,|cell_8|), ""))',
          result: '',
          columnKey: [
            'jenis_kelamin',
            'nilai_pull_up',
            'nilai_push_up',
            'nilai_sit_up',
            'nilai_shutle_run',
            'nilai_pull_up',
            'nilai_push_up',
            'nilai_sit_up',
          ],
        },
      },
    ];

    // =IF(A2="LAKI-LAKI", AVERAGE(B2,C2,D2,E2), IF(A2="PEREMPUAN", AVERAGE(B2,C2,D2,F2), ""))

    mainSheet.columns = columnDefinitions.map(({ header, key, width }) => ({
      header,
      key,
      width,
    }));

    // Fetch data
    const [groupedBinjas, binjas] = await Promise.all([
      this.prisma.binjas_formula.groupBy({
        by: ['jenis_kelamin', 'golongan', 'gerakan'],
        _max: { batas_atas: true },
        _min: { batas_bawah: true },
        orderBy: [
          { jenis_kelamin: 'asc' },
          { golongan: 'asc' },
          { gerakan: 'asc' },
        ],
      }),
      this.prisma.binjas_formula.findMany({
        select: {
          jenis_kelamin: true,
          golongan: true,
          gerakan: true,
          batas_bawah: true,
          batas_atas: true,
          nilai: true,
        },
        where: { deleted_at: null },
      }),
    ]);

    const [excelGroupBinjas, excelBinjas] = await Promise.all([
      this.convertedToExcelData(groupedBinjas),
      this.convertedToExcelData(binjas),
      // this.convertedToExcelData(convertPersonel),
    ]);

    const masterSheets: IExcelMasterData[] = [
      {
        sheet: workbook.addWorksheet('Semester'),
        key: 'semester',
        data: [{ header: 'semester', values: [1, 2] }],
        hidden: true,
      },
      {
        sheet: workbook.addWorksheet('Binjas Formula'),
        key: 'binjas_formula',
        data: excelBinjas,
        hidden: true,
      },
      {
        sheet: workbook.addWorksheet('Group Binjas Formula'),
        key: 'group_binjas_formula',
        data: excelGroupBinjas,
        hidden: false,
      },
      // {
      //   sheet: workbook.addWorksheet('Personel'),
      //   key: 'personel',
      //   data: excelPersonel,
      //   hidden: false,
      // },
    ];

    masterSheets.forEach(({ sheet, data, hidden }) =>
      addDataMasterToSheet(data, sheet, hidden),
    );

    columnDefinitions.forEach((col) => {
      applyFormulaIfAny(mainSheet, col);
      applyValidationIfAny(workbook, mainSheet, col);
    });

    return await workbook.xlsx.writeBuffer();
  }

  private async convertedToExcelData(data: any) {
    const convertedBinjasFormula = data.reduce(
      (acc, item) => {
        Object.entries(item).forEach(([key, value]) => {
          if (['_min', '_max'].includes(key)) {
            Object.entries(value).forEach(([subKey, subValue]) => {
              key = subKey;
              value = subValue;
            });
          }

          if (!acc[key]) acc[key] = { header: key, values: [] };

          const val = ['batas_bawah', 'batas_atas', 'nilai'].includes(key)
            ? Number(value)
            : value;

          acc[key].values.push(val);
        });

        return acc;
      },
      {} as Record<string, { header: string; values: any[] }>,
    );

    return Array.from(Object.values(convertedBinjasFormula));
  }

  async createBulk(req, body: CreateJasmaniPersonelDto[]) {
    const queryResult = [];

    for (const entry of body) {
      // Convert `Nrp` to string for the query
      const personel = await this.prisma.personel.findFirst({
        where: {
          nrp: String(entry['Nrp']),
          nama_lengkap: entry['Nama'],
        },
      });

      if (!personel) {
        throw new BadRequestException('personel tidak di temukan');
      }

      // Check if a record already exists for this person in the given year and semester
      const existingRecord = await this.prisma.jasmani_personel.findFirst({
        where: {
          tahun: entry['Tahun'],
          semester: entry['Semester'],
          personel_id: personel.id,
          deleted_at: null,
        },
      });

      if (existingRecord) {
        throw new BadRequestException('jasmanai personel sudah ada');
      }

      // Parse values or use default of 0
      const nilai_a = parseFloat(entry['Nilai A']) || 0;
      const nilai_b = parseFloat(entry['Nilai B']) || 0;
      const nilai_akhir = (nilai_a + nilai_b) / 2;

      // Determine keterangan based on nilai_akhir
      const keterangan =
        nilai_akhir >= 81
          ? 'BS'
          : nilai_akhir >= 61
            ? 'B'
            : nilai_akhir >= 41
              ? 'C'
              : 'KS';

      // Create the record
      const jasmaniPersonel = await this.prisma.jasmani_personel.create({
        data: {
          personel_id: personel.id,
          tahun: entry['Tahun'] || null,
          semester: entry['Semester'] || null,
          nilai_akhir: Number(nilai_akhir.toFixed(2)),
          nilai_lari_12_m: entry['Nilai Lari 12 M']
            ? parseFloat(entry['Nilai Lari 12 M'])
            : null,
          hasil_lari_12_m: entry['Hasil Lari 12 M']
            ? parseFloat(entry['Hasil Lari 12 M'])
            : null,
          nilai_pull_up: entry['Nilai Pull Up']
            ? parseFloat(entry['Nilai Pull Up'])
            : null,
          hasil_pull_up: entry['Hasil Pull Up']
            ? parseFloat(entry['Hasil Pull Up'])
            : null,
          nilai_push_up: entry['Nilai Push Up']
            ? parseFloat(entry['Nilai Push Up'])
            : null,
          hasil_push_up: entry['Hasil Push Up']
            ? parseFloat(entry['Hasil Push Up'])
            : null,
          nilai_sit_up: entry['Nilai Sit Up']
            ? parseFloat(entry['Nilai Sit Up'])
            : null,
          hasil_sit_up: entry['Hasil Sit Up']
            ? parseFloat(entry['Hasil Sit Up'])
            : null,
          nilai_shutle_run: entry['Nilai Shuttle Run']
            ? parseFloat(entry['Nilai Shuttle Run'])
            : null,
          hasil_shutle_run: entry['Hasil Shuttle Run']
            ? parseFloat(entry['Hasil Shuttle Run'])
            : null,
          nilai_chinning: entry['Nilai Chinning']
            ? parseFloat(entry['Nilai Chinning'])
            : null,
          hasil_chinning: entry['Hasil Chinning']
            ? parseFloat(entry['Hasil Chinning'])
            : null,
          nilai_a: nilai_a.toFixed(2),
          nilai_b: nilai_b.toFixed(2),
          keterangan: keterangan,
          created_at: new Date(),
        },
      });

      // Add created record to the list
      queryResult.push(jasmaniPersonel);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.JASMANI_PERSONEL_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
