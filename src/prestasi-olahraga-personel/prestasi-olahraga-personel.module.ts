import { forwardRef, Module } from '@nestjs/common';
import { PrestasiOlahragaPersonelService } from './service/prestasi-olahraga-personel.service';
import { PrestasiOlahragaPersonelController } from './controller/prestasi-olahraga-personel.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PrestasiOlahragaPersonelController],
  providers: [PrestasiOlahragaPersonelService],
})
export class PrestasiOlahragaPersonelModule {}
