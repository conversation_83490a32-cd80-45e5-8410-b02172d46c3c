import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PrestasiOlahragaPersonelService } from '../service/prestasi-olahraga-personel.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('prestasi-olahraga-personel')
@UseGuards(JwtAuthGuard)
export class PrestasiOlahragaPersonelController {
  private readonly logger = new Logger(PrestasiOlahragaPersonelController.name);

  constructor(
    private readonly prestasiOlahragaPersonelService: PrestasiOlahragaPersonelService,
  ) {}

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id: ${id}`);
    const response = await this.prestasiOlahragaPersonelService.get(req, id);
    this.logger.log(
      `Leaving ${this.get.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.prestasiOlahragaPersonelService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
