import { forwardRef, Module } from '@nestjs/common';
import { DikbangspesService } from './service/dikbangspes.service';
import { DikbangspesController } from './controller/dikbangspes.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [DikbangspesController],
  providers: [DikbangspesService],
})
export class DikbangspesModule {}
