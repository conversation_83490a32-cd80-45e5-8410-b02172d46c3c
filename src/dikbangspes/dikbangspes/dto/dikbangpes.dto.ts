import { PartialType } from '@nestjs/mapped-types';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateDikbangpesDTO {
  @IsNotEmpty({ message: 'Nama tidak boleh kosong' })
  @IsString({ message: 'Nama harus berupa string' })
  nama: string;

  @IsNotEmpty({ message: 'Lokasi ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Lokasi ID harus berupa number' })
  lokasi_id: number;

  @IsNotEmpty({ message: 'Tingkat ID tidak boleh kosong' })
  @IsNumber({}, { message: 'Tingkat ID harus berupa number' })
  tingkat_id: number;

  @IsOptional()
  @IsBoolean()
  is_aktif?: boolean;
}

export class UpdateDikbangpesDTO extends PartialType(CreateDikbangpesDTO) {}
