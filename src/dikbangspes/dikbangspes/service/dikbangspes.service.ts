import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateDikbangpesDTO,
  UpdateDikbangpesDTO,
} from '../dto/dikbangpes.dto';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { is } from 'date-fns/locale';

@Injectable()
export class DikbangspesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async dikbangspesPersonel(req, uid: string) {
    try {
      const getDikbangspesPersonel =
        await this.prisma.dikbangspes_personel.findMany({
          select: {
            dikbangspes: {
              select: {
                nama: true,
              },
            },
            tanggal_masuk: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_masuk: 'desc',
          },
        });

      const queryResult = getDikbangspesPersonel.map((item) => {
        const date = new Date(item.tanggal_masuk);
        const year = date.getFullYear();
        return {
          dikbang: item.dikbangspes.nama,
          tmt: year,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'number' | 'boolean' | 'date';
        };
      } = {
        nama: { field: 'nama', type: 'string' },
        is_aktif: { field: 'is_aktif', type: 'boolean' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, dik] = await this.prisma.$transaction([
        this.prisma.dikbangspes.count({
          where: {
            ...where,
            OR: [
              {
                dikbangspes_lokasi: {
                  nama: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                nama: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          },
        }),
        this.prisma.dikbangspes.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
            dikbangspes_lokasi: {
              select: {
                id: true,
                nama: true,
              },
            },
            dikbangspes_tingkat: {
              select: {
                id: true,
                nama: true,
              },
            },
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: {
            ...where,
            OR: [
              {
                dikbangspes_lokasi: {
                  nama: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              },
              {
                nama: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          },
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const queryResult = dik.map(
        ({ dikbangspes_lokasi, dikbangspes_tingkat, ...item }) => ({
          ...item,
          lokasi_id: dikbangspes_lokasi?.id || null,
          lokasi: dikbangspes_lokasi?.nama || null,
          tingkat_id: dikbangspes_tingkat?.id || null,
          tingkat: dikbangspes_tingkat?.nama || null,
        }),
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const dikbangspes = await this.prisma.dikbangspes.findFirst({
        select: {
          id: true,
          nama: true,
          is_aktif: true,
          dikbangspes_lokasi: {
            select: {
              id: true,
              nama: true,
            },
          },
          dikbangspes_tingkat: {
            select: {
              id: true,
              nama: true,
            },
          },
          created_at: true,
          updated_at: true,
        },
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!dikbangspes) {
        throw new NotFoundException('ID dikbangspes tidak ditemukan');
      }

      const { dikbangspes_lokasi, dikbangspes_tingkat, ...rest } = dikbangspes;

      const queryResult = {
        ...rest,
        lokasi_id: dikbangspes_lokasi?.id || null,
        lokasi_nama: dikbangspes_lokasi?.nama || null,
        tingkat_id: dikbangspes_tingkat?.id || null,
        tingkat_nama: dikbangspes_tingkat?.nama || null,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreateDikbangpesDTO) {
    const { nama, lokasi_id, tingkat_id, is_aktif } = body;

    const lokasi = await this.prisma.dikbangspes_lokasi.findFirst({
      where: {
        id: lokasi_id,
      },
    });

    if (!lokasi) {
      throw new NotFoundException('Lokasi tidak ditemukan');
    }

    const tingkat = await this.prisma.dikbangspes_tingkat.findFirst({
      where: { id: tingkat_id },
    });

    if (!tingkat) {
      throw new NotFoundException('Tingkat tidak ditemukan');
    }

    try {
      const queryResult = await this.prisma.dikbangspes.create({
        data: {
          nama: nama,
          lokasi_id: lokasi.id,
          tingkat_id: tingkat.id,
          is_aktif: Boolean(is_aktif) ?? true,
          created_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdateDikbangpesDTO) {
    const { nama, lokasi_id, tingkat_id, is_aktif } = body;

    const checkIdDikbangpes = await this.prisma.dikbangspes.findFirst({
      where: {
        id: BigInt(id),
      },
    });

    if (!checkIdDikbangpes) {
      throw new NotFoundException('ID dikbangpes tidak ditemukan');
    }

    const lokasi = await this.prisma.dikbangspes_lokasi.findFirst({
      where: {
        id: lokasi_id,
      },
    });

    if (!lokasi) {
      throw new NotFoundException('Lokasi tidak ditemukan');
    }

    const tingkat = await this.prisma.dikbangspes_tingkat.findFirst({
      where: { id: tingkat_id },
    });

    if (!tingkat) {
      throw new NotFoundException('Tingkat tidak ditemukan');
    }

    try {
      const queryResult = await this.prisma.dikbangspes.update({
        where: { id: +id },
        data: {
          nama: nama,
          lokasi_id: lokasi_id,
          tingkat_id: tingkat_id,
          is_aktif: Boolean(is_aktif) ?? true,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    const checkIdDikbangpes = await this.prisma.dikbangspes.findFirst({
      where: {
        id: BigInt(id),
      },
    });

    if (!checkIdDikbangpes) {
      throw new NotFoundException('ID dikbangpes tidak ditemukan');
    }

    try {
      const queryResult = await this.prisma.dikbangspes.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
