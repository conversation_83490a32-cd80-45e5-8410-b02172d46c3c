import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DikbangspesService } from '../service/dikbangspes.service';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  CreateDikbangpesDTO,
  UpdateDikbangpesDTO,
} from '../dto/dikbangpes.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';

@Controller('dikbangspes')
@UseGuards(JwtAuthGuard)
export class DikbangspesController {
  private readonly logger = new Logger(DikbangspesController.name);

  constructor(private readonly dikbangspesService: DikbangspesService) {}

  @Get('/personel/:uid')
  @HttpCode(200)
  async dikbangspesPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.dikbangspesPersonel.name} with uid ${uid}`,
    );

    const response = await this.dikbangspesService.dikbangspesPersonel(
      req,
      uid,
    );

    this.logger.log(
      `Leaving ${this.dikbangspesPersonel.name} with uid ${uid} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.dikbangspesService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getById(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getById.name} with id ${id}`);

    const response = await this.dikbangspesService.getById(req, id);

    this.logger.log(
      `Leaving ${this.getById.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateDikbangpesDTO,
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangspesService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: UpdateDikbangpesDTO,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.dikbangspesService.update(req, id, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.dikbangspesService.delete(req, id);

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
