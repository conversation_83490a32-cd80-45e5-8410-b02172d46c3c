import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { DikbangspesTingkatController } from './controller/dikbangspes-tingkat.controller';
import { DikbangspesTingkatService } from './service/dikbangspes-tingkat.service';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [DikbangspesTingkatController],
  providers: [DikbangspesTingkatService],
})
export class DikbangspesTingkatModule {}
