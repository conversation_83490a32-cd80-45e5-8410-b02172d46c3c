import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateDikbangpesTingkatDTO,
  UpdateDikbangpesTingkatDTO,
} from '../dto/dikbangspes-tingkat.dto';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class DikbangspesTingkatService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'nama', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.dikbangspes_tingkat.count({
          where: where,
        }),
        this.prisma.dikbangspes_tingkat.findMany({
          select: {
            id: true,
            nama: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_TINGKAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getById(req: any, id: string) {
    try {
      const queryResult = await this.prisma.dikbangspes_tingkat.findFirst({
        select: {
          id: true,
          nama: true,
          created_at: true,
          updated_at: true,
        },
        where: {
          id: +id,
          deleted_at: null,
        },
      });

      if (!queryResult) {
        throw new NotFoundException('ID dikbangspes tingkat tidak ditemukan');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_TINGKAT_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async create(req: any, body: CreateDikbangpesTingkatDTO) {
    const { nama } = body;

    try {
      const dikbangpes = await this.prisma.dikbangspes_tingkat.findFirst({
        where: {
          nama: nama,
        },
      });

      if (dikbangpes) {
        throw new ConflictException('Tingkat already exists');
      }

      const queryResult = await this.prisma.dikbangspes_tingkat.create({
        data: {
          nama: nama,
          created_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_TINGKAT_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(req: any, id: string, body: UpdateDikbangpesTingkatDTO) {
    const { nama } = body;

    try {
      const dikbangpes = await this.prisma.dikbangspes_tingkat.findFirst({
        where: {
          id: +id,
        },
      });

      if (!dikbangpes) {
        throw new NotFoundException('Tingkat ID not found');
      }
      const checkExists = await this.prisma.dikbangspes_tingkat.findFirst({
        where: {
          nama: nama,
          NOT: {
            id: +id,
          },
        },
      });

      if (checkExists) {
        throw new ConflictException('Tingkat already exists');
      }

      const queryResult = await this.prisma.dikbangspes_tingkat.update({
        where: { id: +id },
        data: {
          nama: nama,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_TINGKAT_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async delete(req: any, id: string) {
    try {
      const dikbangpes = await this.prisma.dikbangspes_tingkat.findFirst({
        where: {
          id: +id,
        },
      });

      if (!dikbangpes) {
        throw new NotFoundException('Tingkat ID not found');
      }

      const queryResult = await this.prisma.dikbangspes_tingkat.update({
        where: { id: +id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.DIKBANGSPES_TINGKAT_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
