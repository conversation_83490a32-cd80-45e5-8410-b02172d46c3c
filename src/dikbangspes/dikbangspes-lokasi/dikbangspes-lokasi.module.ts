import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { DikbangspesLokasiController } from './controller/dikbangspes-lokasi.controller';
import { DikbangspesLokasiService } from './service/dikbangspes-lokasi.service';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [DikbangspesLokasiController],
  providers: [DikbangspesLokasiService],
})
export class DikbangspesLokasiModule {}
