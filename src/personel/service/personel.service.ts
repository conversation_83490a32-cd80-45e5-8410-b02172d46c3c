import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { findAgeGroup } from '../../core/utils/search.utils';
import {
  calculateAge,
  calculateDateDifference,
} from '../../core/utils/common.utils';
import * as moment from 'moment';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { gender_enum } from '@prisma/client';

@Injectable()
export class PersonelService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async get(req: any, uid: string) {
    const personel = await this.prisma.personel.findFirst({
      select: {
        id: true,
        uid: true,
        nrp: true,
        nama_lengkap: true,
        tanggal_lahir: true,
        tempat_lahir: true,
        jenis_kelamin: true,
        no_hp: true,
        email: true,
        foto_file: true,
        status_aktif: {
          select: {
            id: true,
            nama: true,
          },
        },
        status_kawin: {
          select: {
            id: true,
            nama: true,
          },
        },
        pangkat_personel: {
          select: {
            pangkat: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                id: true,
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
      },
      where: { uid },
    });

    if (!personel) throw new NotFoundException(`personel uid ${uid} not found`);

    const age = calculateAge(personel.tanggal_lahir);
    const golonganBinjas = findAgeGroup(age);
    const retirementDate = moment(personel.tanggal_lahir)
      .add(58, 'years')
      .toISOString();

    const remainingServiceTime = moment().isBefore(retirementDate)
      ? calculateDateDifference(retirementDate)
      : 'Pensiun';

    const { pangkat_personel, jabatan_personel, ...restData } = personel;
    const { jabatans } = jabatan_personel?.[0] ?? {};
    const { satuan, ...restJabatans } = jabatans ?? {};

    //production pake ini sementara
    const photoFile = await this.minioService.checkFileExist(
      `${process.env.MINIO_BUCKET_NAME}`,
      `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
    );

    const queryResult = {
      ...restData,
      id: Number(personel.id),
      tanggal_lahir: moment(personel.tanggal_lahir).format('YYYY-MM-DD'),
      sisa_dinas: remainingServiceTime,
      umur: age,
      golongan_binjas: golonganBinjas,
      foto_file: photoFile,
      pangkat: pangkat_personel?.[0]?.pangkat ?? null,
      jabatan: restJabatans,
      satuan: satuan,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      pangkat_nama: {
        field: 'pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      jabatan_nama: {
        field: 'jabatan_personel.some.jabatans.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
      'status_aktif.nama': { field: 'status_aktif.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: where,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          foto_file: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const result = {
          ...personel,
          id: Number(personel.id),
          tanggal_lahir: personel.tanggal_lahir
            ? new Date(personel.tanggal_lahir).toISOString().split('T')[0]
            : null,
          foto_file: await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
          ),
          pangkat: personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
        };

        // Delete nested properties
        delete result.pangkat_personel;
        delete result.jabatan_personel;
        if (result.jabatan) delete result.jabatan.satuan;

        return result;
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async withLastPangkatJabatan(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { limit, page } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nrp: {
        field: 'mv_personel_pangkat_jabatan_terakhir_personel.nrp',
        type: 'string',
      },
      nama_lengkap: {
        field: 'mv_personel_pangkat_jabatan_terakhir_personel.nama_lengkap',
        type: 'string',
      },
      pangkat_nama: {
        field: 'mv_personel_pangkat_jabatan_terakhir_pangkat.nama',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.mv_personel_pangkat_jabatan_terakhir.count({
        where: where,
      }),
      this.prisma.mv_personel_pangkat_jabatan_terakhir.findMany({
        select: {
          mv_personel_pangkat_jabatan_terakhir_personel: {
            select: {
              uid: true,
              nrp: true,
              nama_lengkap: true,
              foto_file: true,
              mv_gelar: {
                select: {
                  gelar_depan: true,
                  gelar_belakang: true,
                },
              },
            },
          },
          mv_personel_pangkat_jabatan_terakhir_pangkat: {
            select: {
              id: true,
              nama: true,
              nama_singkat: true,
            },
          },
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const uid = personel.mv_personel_pangkat_jabatan_terakhir_personel.uid;
        const nrp = personel.mv_personel_pangkat_jabatan_terakhir_personel.nrp;
        const gelarDepan = personel
          ?.mv_personel_pangkat_jabatan_terakhir_personel?.mv_gelar?.gelar_depan
          ? `${
              personel?.mv_personel_pangkat_jabatan_terakhir_personel?.mv_gelar
                ?.gelar_depan
            } `
          : '';
        const gelarBelakang = personel
          ?.mv_personel_pangkat_jabatan_terakhir_personel?.mv_gelar
          ?.gelar_belakang
          ? `, ${
              personel?.mv_personel_pangkat_jabatan_terakhir_personel?.mv_gelar
                ?.gelar_belakang
            }`
          : '';
        const nama_lengkap = `${gelarDepan}${personel.mv_personel_pangkat_jabatan_terakhir_personel.nama_lengkap}${gelarBelakang}`;
        const foto_file =
          personel.mv_personel_pangkat_jabatan_terakhir_personel.foto_file;

        const photo = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${nrp}/${foto_file}`,
        );

        return {
          uid,
          nrp,
          nama_lengkap,
          foto_file: photo,
          pangkat: {
            nama: personel.mv_personel_pangkat_jabatan_terakhir_pangkat?.nama,
            nama_singkat:
              personel.mv_personel_pangkat_jabatan_terakhir_pangkat
                ?.nama_singkat,
          },
        };
      }),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListShort(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: where,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          nrp: true,
          nama_lengkap: true,
          jenis_kelamin: true,
          tanggal_lahir: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const age = calculateAge(personel.tanggal_lahir);
        const golonganBinjas = findAgeGroup(age);

        const photoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
        );

        return {
          ...personel,
          jenis_kelamin:
            personel.jenis_kelamin === gender_enum.LAKI_LAKI
              ? 'LAKI-LAKI'
              : 'PEREMPUAN',
          id: Number(personel.id),
          tanggal_lahir: moment(personel.tanggal_lahir).format('YYYY-MM-DD'),
          umur: age,
          foto_file: photoFile,
          golongan_binjas: golonganBinjas,
        };
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListStatusAktif(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const [totalData, status_aktif] = await this.prisma.$transaction([
      this.prisma.status_aktif.count({
        where: where,
      }),
      this.prisma.status_aktif.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        status_aktif,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: status_aktif,
      totalPage,
      totalData,
      page,
    };
  }

  async getListPolda(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    let adminId = req['user'].personel_id;

    let satuanAdmin = req['user'].satuan_id;

    let adminSatuan = await this.prisma.mv_satuan_with_top_parents.findFirst({
      where: {
        id: satuanAdmin,
      },
    });

    //const satuanId = adminSatuan.satuan_id;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      pangkat_nama: {
        field: 'pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      jabatan_nama: {
        field: 'jabatan_personel.some.jabatans.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
      'status_aktif.nama': { field: 'status_aktif.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const formattedWhere = {
      ...where,
      mv_latest_jabatan_personel: {
        satuan: {
          mv_satuan_with_top_parents_self: {
            second_top_parent_id: adminSatuan.second_top_parent_id,
          },
        },
      },
    };

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: formattedWhere,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: formattedWhere,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const { pangkat_personel, jabatan_personel, ...restData } = personel;
        const { jabatans } = jabatan_personel?.[0] ?? {};
        const { satuan, ...restJabatans } = jabatans ?? {};

        const photoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
        );

        return {
          ...restData,
          id: Number(personel.id),
          tanggal_lahir: moment(personel.tanggal_lahir).format('YYYY-MM-DD'),
          pangkat: pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: restJabatans,
          satuan: satuan,
          foto_file: photoFile,
        };
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get personel',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListShortForTanhor(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: where,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          nrp: true,
          nama_lengkap: true,
          jenis_kelamin: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = await Promise.all(
      personels.map(async (personel) => {
        const { pangkat_personel, jabatan_personel } = personel;
        const { jabatans } = jabatan_personel?.[0] ?? {};
        const { satuan } = jabatans ?? {};

        return {
          id: Number(personel.id),
          nrp: personel.nrp,
          nama_lengkap: personel.nama_lengkap,
          jenis_kelamin: personel.jenis_kelamin,
          pangkat: pangkat_personel[0]?.pangkat?.nama_singkat,
          jabatan: jabatans?.nama,
          satuan: satuan?.nama,
          status_aktif: personel.status_aktif,
        };
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async findByNrp(nrp: string) {
    return await this.prisma.personel.findFirst({
      where: { nrp: nrp },
      include: {
        users: true,
      },
    });
  }

  async getKeluarga(req: any, uid: string) {
    const personel = await this.prisma.personel.findFirst({
      select: {
        id: true,
      },
      where: { uid: uid },
    });

    if (!personel) {
      throw new NotFoundException(`personel uid ${uid} not found`);
    }

    const queryResult = await this.prisma.keluarga_personel.findMany({
      select: {
        id: true,
        nama_keluarga: true,
        jenis_kelamin: true,
        alamat: true,
        status: true,
        nrp_keluarga: true,
        tempat_lahir: true,
        tanggal_lahir: true,
        agama: {
          select: {
            id: true,
            nama: true,
          },
        },
        personel_id: true,
        hubungan_keluarga: {
          select: {
            id: true,
            hubungan: true,
          },
        },
        personel_keluarga_id: true,
        golongan: {
          select: {
            id: true,
            nama: true,
          },
        },
        pekerjaan_keluarga: {
          select: {
            id: true,
            nama_institusi: true,
            jenis_pekerjaan: {
              select: {
                id: true,
                jenis: true,
              },
            },
          },
        },
        tanggal_nikah: true,
        kpis_nomor: true,
        kpis_file: true,
        buku_nikah_nomor: true,
        buku_nikah_file: true,
        foto_file: true,
        status_nikah: true,
        status_pernikahan: true,
        tanggal_cerai: true,
        cerai_file: true,
        parent_hubungan_keluarga_id: true,
        status_personel: true,
        no_ijinnikah: true,
        file_ijinnikah: true,
        ktp_nomor: true,
        ktp_file: true,
        no_hp: true,
      },
      where: {
        personel_id: personel.id,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAlamat(req: any, uid: string) {
    const personel = await this.prisma.personel.findFirst({
      select: {
        id: true,
      },
      where: { uid: uid },
    });

    if (!personel) {
      throw new NotFoundException(`personel uid ${uid} not found`);
    }

    const queryResult = await this.prisma.alamat.findMany({
      select: {
        id: true,
        alamat: true,
        no_rt: true,
        no_rw: true,
      },
      where: {
        personel_id: personel.id,
        deleted_at: null,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PERSONEL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAllPersonelCount(req) {
    try {
      const personelCount = await this.prisma.personel.count({
        where: {
          deleted_at: null,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERSONEL_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERSONEL_READ as ConstantLogType,
          message,
          personelCount,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: personelCount,
      };
    } catch (error) {
      throw error;
    }
  }

  async getCountBankList(req: any, date: string) {
    try {
    } catch (error) {
      throw error;
    }
  }
}
