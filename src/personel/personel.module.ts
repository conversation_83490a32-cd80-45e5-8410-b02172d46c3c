import { forwardRef, Module } from '@nestjs/common';
import { PersonelService } from './service/personel.service';
import { PersonelController } from './controller/personel.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PersonelController],
  providers: [PersonelService, MinioService],
  exports: [PersonelService],
})
export class PersonelModule {}
