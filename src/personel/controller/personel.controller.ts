import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  <PERSON>gger,
  Param,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { PersonelService } from '../service/personel.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';

@Controller('personel')
@UseGuards(JwtAuthGuard)
export class PersonelController {
  private readonly logger = new Logger(PersonelController.name);

  constructor(private readonly personelService: PersonelService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.personelService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/short')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListShort(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListShort.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.personelService.getListShort(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListShort.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/short/tanhor')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListShortTanhor(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListShortTanhor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.personelService.getListShortForTanhor(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListShortTanhor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/polda')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListPolda(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.personelService.getListPolda(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/last-pangkat-jabatan')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async withLastPangkatJabatan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.personelService.withLastPangkatJabatan(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/list/status-aktif')
  @Permission('PERMISSION_READ')
  async getListStatusAktif(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListStatusAktif.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.personelService.getListStatusAktif(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListStatusAktif.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/count/all')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getAllPersonelCount(@Req() req: any) {
    this.logger.log(`Entering ${this.getAlamat.name}`);
    const response = await this.personelService.getAllPersonelCount(req);
    this.logger.log(`Leaving ${this.getAlamat.name}`);

    return response;
  }

  @Get('/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.get.name} with uid: ${uid}`);
    const response = await this.personelService.get(req, uid);
    this.logger.log(
      `Leaving ${this.get.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:uid/keluarga')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getKeluarga(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.getKeluarga.name} with uid: ${uid}`);
    const response = await this.personelService.getKeluarga(req, uid);
    this.logger.log(
      `Leaving ${this.getKeluarga.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:uid/alamat')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getAlamat(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.getAlamat.name} with uid: ${uid}`);
    const response = await this.personelService.getAlamat(req, uid);
    this.logger.log(
      `Leaving ${this.getAlamat.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
