import { PartialType } from '@nestjs/mapped-types';
import { gender_enum } from '@prisma/client';
import { IsEnum, IsOptional } from 'class-validator';
import { JenisRambutEnum } from '../../core/enums/personel.enum';

export class CreatePersonelDTO {
  nrp: string;
  nama_lengkap: string;
  tanggal_lahir: Date;
  jenis_kelamin: gender_enum;
  foto_file: string;
  tempat_lahir: string;
  agama_id: number;
  ktp_nomor: string;
  ktp_file: string;
  kk_nomor: string;
  kk_file: string;
  status_kawin_id: number;
  golongan_darah: string;
  suku_id: number;
  email: string;
  no_hp: string;
  akta_kelahiran_file: string;
  asabri_nomor: string;
  asabri_file: string;
  bpjs_nomor: string;
  bpjs_file: string;
  paspor_nomor: string;
  paspor_file: string;
  npwp_nomor: string;
  npwp_file: string;
  lhkpn_file: string;
  masa_dinas_surut_tmt: Date;
  masa_dinas_surut_file: string;
  anak_ke: number;
  jumlah_saudara: number;
  status_aktif_id: number;
}

export class UpdatePersonelDTO extends PartialType(CreatePersonelDTO) {
  @IsOptional()
  @IsEnum(JenisRambutEnum, { message: 'Jenis rambut tidak valid' })
  jenis_rambut: string;
  warna_rambut: string;
  warna_kulit: string;
  warna_mata: string;

  nama_ibu: string;
  telepon_orang_tua: string;
  alamat_orang_tua: string;

  alamat: string;

  tinggi_badan: number;
  berat_badan: number;

  ukuran_topi: string;
  ukuran_celana: string;
  ukuran_baju: string;

  sidik_jari_1: string;
  sidik_jari_2: string;
}
