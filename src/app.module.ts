import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './access-management/auth/auth.module';
import { BagianModule } from './reference-data/bagian/bagian.module';
import { BahasaModule } from './mdm/bahasa/bahasa.module';
import { BeritaModule } from './berita/berita.module';
import { FirebaseService } from './api-utils/firebase/service/firebase.service';
import { MinioService } from './api-utils/minio/service/minio.service';
import { DikbangumKategoriModule } from './dikbangum/dikbangum-kategori/dikbangum-kategori.module';
import { DikbangumModule } from './dikbangum/dikbangum/dikbangum.module';
import { DiktukKategoriModule } from './mdm/diktuk/diktuk-kategori/diktuk-kategori.module';
import { DiktukModule } from './mdm/diktuk/diktuk/diktuk.module';
import { EKandidatModule } from './e-kandidat/e-kandidat.module';
import { EselonModule } from './reference-data/eselon/eselon.module';
import { ExcelModule } from './api-utils/excel/excel.module';
import { GolonganModule } from './reference-data/golongan/golongan.module';
import { JabatanKategoriModule } from './mdm/jabatan/jabatan-kategori/jabatan-kategori.module';
import { JabatanModule } from './mdm/jabatan/jabatan.module';
import { KemampuanBahasaModule } from './reference-data/kemampuan-bahasa/kemampuan-bahasa.module';
import { KerjasamaModule } from './kerjasama/kerjasama.module';
import { LevelModule } from './reference-data/level/level.module';
import { LogsActivityModule } from './api-utils/logs-activity/logs-activity.module';
import { NivelleringModule } from './reference-data/nivellering/nivellering.module';
import { PangkatModule } from './mdm/pangkat/pangkat/pangkat.module';
import { PangkatKategoriModule } from './mdm/pangkat/pangkat_kategori/pangkat_kategori.module';
import { PenghargaanModule } from './mdm/penghargaan/penghargaan.module';
import { PeraturanPolriModule } from './peraturan-polri/peraturan-polri.module';
import { PermissionModule } from './reference-data/permission/permission.module';
import { PersonelModule } from './personel/personel.module';
import { RoleTipeModule } from './reference-data/role-tipe/role-tipe.module';
import { RolesModule } from './access-management/roles/roles.module';
import { SatuanJenisModule } from './mdm/satuan/satuan-jenis/satuan-jenis.module';
import { SatuanModule } from './mdm/satuan/satuan/satuan.module';
import { SertifikatModule } from './reference-data/sertifikat/sertifikat.module';
import { SeleksiBagassusModule } from './seleksi/seleksi-bagassus/seleksi-bagassus.module';
import { SipkModule } from './sipk/sipk.module';
import { SukuModule } from './mdm/suku/suku.module';
import { TanhorModule } from './mdm/tanhor/tanhor.module';
import { TingkatPenghargaanModule } from './reference-data/tingkat_penghargaan/tingkat_penghargaan.module';
import { UsersModule } from './access-management/users/users.module';
import { SurveyModule } from './survey/survey.module';
import { WorkflowModule } from './access-management/workflow/workflow.module';
import { DikbangspesModule } from './dikbangspes/dikbangspes/dikbangspes.module';
import { DikumModule } from './mdm/dikum/dikum.module';
import { EktaModule } from './e-kta/ekta-v1/ekta.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NcrModule } from './ncr/ncr.module';
import { KhirdinModule } from './khirdin/khirdin.module';
import { AgamaModule } from './reference-data/agama/agama.module';
import { KabupatenModule } from './reference-data/kabupaten/kabupaten.module';
import { StatusKawinModule } from './reference-data/status_kawin/status_kawin.module';
import { PekerjaanModule } from './reference-data/pekerjaan/pekerjaan.module';
import { SeleksiBagrimdikModule } from './seleksi/seleksi-bagrimdik/seleksi-bagrimdik.module';
import { BagassusModule } from './bagassus/bagassus.module';
import { BagsipersModule } from './bagsipers/bagsipers.module';
import { AssessmentModule } from './assessment/assessment.module';
import { DataKelulusanRekrutmenModule } from './e-patma/data-kelulusan-rekrutmen/data-kelulusan-rekrutmen.module';
import { JasmaniPersonelModule } from './jasmani-personel/jasmani-personel.module';
import { DataPendidikanPembentukanModule } from './e-patma/data-pendidikan-pembentukan/data-pendidikan-pembentukan.module';
import { PengolahanDataDokumenModule } from './e-patma/pengolahan-data-dokumen/pengolahan-data-dokumen.module';
import { DistribusiDataDokumenModule } from './e-patma/distribusi-data-dokumen/distribusi-data-dokumen.module';
import { PortalModule } from './e-patma/portal/portal.module';
import { KonselingModule } from './psikologi/e-konseling/konseling.module';
import { PengajuanCutiModule } from './pengajuan/pengajuan-cuti/pengajuan-cuti.module';
import { PengajuanPengakhiranDinasModule } from './pengajuan/pengajuan-pengakhiran-dinas/pengajuan_pengakhiran_dinas.module';
import { MutasiJabatanModule } from './mutasi-jabatan/mutasi-jabatan.module';
import { NotifikasiModule } from './api-utils/notifikasi/notifikasi.module';
import { KenaikanGajiBerkalaModule } from './kenaikan-gaji-berkala/kenaikan-gaji-berkala.module';
import { PortalSiswaModule } from './e-patma/siswa/siswa.module';
import { RekrutmenBagrimdikModule } from './rekrutmen-bagrimdik/rekrutmen-bagrimdik.module';
import { Sipp3Module } from './sipp/sipp-3/sipp-3.module';
import { JurusanModule } from './reference-data/jurusan/jurusan.module';
import { RiwayatLengkapModule } from './sipp/riwayat-lengkap/riwayat-lengkap.module';
import { EktaBatchModule } from './e-kta/ekta-batch/ekta-batch.module';
import { GenerateNrpModule } from './generate-nrp/generate-nrp.module';
import { APIGeneratorModule } from './api-management/api-generator/api-generator.module';
import { ApiKeyModule } from './api-management/api-key/api-key.module';
import { APIModule } from './api-management/api/api.module';
import { SeleksiBaglekdikModule } from './seleksi/seleksi-baglekdik/seleksi-baglekdik.module';
import { PengajuanPengakhiranDinasBupModule } from './pengajuan/pengajuan-pengakhiran-dinas-bup/pengajuan_pengakhiran_dinas-bup.module';
import { ScheduleModule } from '@nestjs/schedule';
import { PengajuanPenghargaanModule } from './pengajuan/pengajuan-penghargaan/pengajuan_penghargaan.module';
import { PengajuanTanhorModule } from './pengajuan/pengajuan-tanhor/pengajuan_tanhor.module';
import { BeasiswaDikumDinasModule } from './beasiswa-dikum-dinas/beasiswa-dikum-dinas.module';
import { SeleksiBagrimdikPnsModule } from './seleksi/seleksi-bagrimdik-pns/seleksi-bagrimdik-pns.module';
import { PrestasiOlahragaPersonelModule } from './prestasi-olahraga-personel/prestasi-olahraga-personel.module';
import { KenaikanPangkatModule } from './kenaikan-pangkat/kenaikan-pangkat.module';
import { SmtpModule } from './api-utils/smtp/smtp.module';
import { EMentalModule } from './psikologi/e-mental/e-mental.module';
import { ERohaniModule } from './e-rohani/e-rohani.module';
import { GlobalFilter } from './core/exceptions/global.filter';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { DuplikasiSatuanModule } from './mdm/duplikasi/duplikasi-satuan/duplikasi-satuan.module';
import { DuplikasiJabatanModule } from './mdm/duplikasi/duplikasi-jabatan/duplikasi-jabatan.module';
import { AxiosModule } from './api-utils/axios/axios.module';
import { PelatihanModule } from './pelatihan/pelatihan/pelatihan.module';
import { SatuanJabatanModule } from './mdm/satuan/satuan-jabatan/satuan-jabatan.module';
import { FieldValidatorPipe } from './core/validator/field.validator';
import { PenugasanLuarStrukturModule } from './penugasan-luar-struktur/penugasan-luar-struktur.module';
import { MisiModule } from './reference-data/misi/misi.module';
import { DikbangspesLokasiModule } from './dikbangspes/dikbangspes-lokasi/dikbangspes-lokasi.module';
import { DikbangspesTingkatModule } from './dikbangspes/dikbangspes-tingkat/dikbangspes-tingkat.module';
import { PelatihanKategoriModule } from './pelatihan/pelatihan-kategori/pelatihan-kategori.module';
import { EktaModuleV2 } from './e-kta/ekta-v2/ekta-v2.module';
import { ApiLogService } from './api-management/api-log/service/api-log.service';
import { ApiLogModule } from './api-management/api-log/api-log.module';
import { PrismaService } from './api-utils/prisma/service/prisma.service';
import { AxiosService } from './api-utils/axios/service/axios.service';
import { PromosiJabatanModule } from './promosi-jabatan/promosi-jabatan.module';
import { LoggingInterceptor } from './core/interceptors/response.interceptor';
import { SippRecapModule } from './sipp/sipp-recap/sipp-recap.module';
import { PendataanPerumahanModule } from './pendataan-perumahan/pendataan-perumahan.module';
import { ApiWebModule } from './api-management-v2/api-web/web.module';
import { EPatmaWebModule } from './e-patma-v2/web/web.module';
import { ApiModuleV2 } from './api-management-v2/api-clients/api-clients.module';
import { EPatmaSiswaModule } from './e-patma-v2/siswa/siswa.module';
import { ClientAPIManagementV2Module } from './api-management-v2/clients/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    BeritaModule,
    AuthModule,
    PermissionModule,
    LevelModule,
    RolesModule,
    UsersModule,
    LogsActivityModule,
    BagianModule,
    PersonelModule,
    SatuanModule,
    SatuanJenisModule,
    EselonModule,
    NivelleringModule,
    JabatanKategoriModule,
    KerjasamaModule,
    JabatanModule,
    ExcelModule,
    RoleTipeModule,
    TanhorModule,
    PenghargaanModule,
    PangkatModule,
    PangkatKategoriModule,
    GolonganModule,
    TingkatPenghargaanModule,
    BahasaModule,
    SukuModule,
    DiktukKategoriModule,
    DiktukModule,
    DikbangumKategoriModule,
    DikbangumModule,
    EKandidatModule,
    SurveyModule,
    WorkflowModule,
    DikbangspesModule,
    DikumModule,
    KhirdinModule,
    NcrModule,
    AgamaModule,
    KabupatenModule,
    StatusKawinModule,
    PekerjaanModule,
    SeleksiBagrimdikModule,
    SeleksiBaglekdikModule,
    BagassusModule,
    BagsipersModule,
    AssessmentModule,
    DataKelulusanRekrutmenModule,
    JasmaniPersonelModule,
    DataPendidikanPembentukanModule,
    PengolahanDataDokumenModule,
    DistribusiDataDokumenModule,
    PengajuanCutiModule,
    PortalModule,
    KonselingModule,
    PengajuanPengakhiranDinasModule,
    Sipp3Module,
    SippRecapModule,
    JurusanModule,
    RiwayatLengkapModule,
    PeraturanPolriModule,
    PengajuanPengakhiranDinasModule,
    KemampuanBahasaModule,
    SertifikatModule,
    EktaModule,
    EktaModuleV2,
    EktaBatchModule,
    KenaikanGajiBerkalaModule,
    MutasiJabatanModule,
    NotifikasiModule,
    PortalSiswaModule,
    GenerateNrpModule,
    APIGeneratorModule,
    ApiKeyModule,
    APIModule,
    ApiWebModule,
    EPatmaWebModule,
    EPatmaSiswaModule,
    PengajuanPengakhiranDinasBupModule,
    RekrutmenBagrimdikModule,
    PengajuanPenghargaanModule,
    PengajuanTanhorModule,
    SeleksiBagrimdikPnsModule,
    SipkModule,
    BeasiswaDikumDinasModule,
    PrestasiOlahragaPersonelModule,
    KenaikanPangkatModule,
    SmtpModule,
    EMentalModule,
    ERohaniModule,
    DuplikasiSatuanModule,
    DuplikasiJabatanModule,
    PelatihanModule,
    PenugasanLuarStrukturModule,
    MisiModule,
    DikbangspesLokasiModule,
    DikbangspesTingkatModule,
    PelatihanKategoriModule,
    AxiosModule,
    SatuanJabatanModule,
    PromosiJabatanModule,
    ApiLogModule,
    PendataanPerumahanModule,
    ApiModuleV2,
    SeleksiBagassusModule,
    ClientAPIManagementV2Module,
  ],
  controllers: [AppController],
  exports: [FieldValidatorPipe],
  providers: [
    AppService,
    MinioService,
    SmtpModule,
    PrismaService,
    AxiosService,

    FieldValidatorPipe,
    FirebaseService,
    LogsActivityModule,
    {
      provide: APP_FILTER,
      useClass: GlobalFilter,
    },
    ApiLogService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
  ],
})
export class AppModule {}
