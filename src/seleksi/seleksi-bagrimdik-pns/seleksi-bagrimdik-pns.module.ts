import { forwardRef, Module } from '@nestjs/common';
import { SeleksiBagrimdikPnsService } from './service/seleksi-bagrimdik-pns.service';
import { SeleksiBagrimdikPnsController } from './controller/seleksi-bagrimdik-pns.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { ExcelModule } from '../../api-utils/excel/excel.module';

@Module({
  imports: [
    PrismaModule,
    ExcelModule,
    LogsActivityModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [SeleksiBagrimdikPnsController],
  providers: [SeleksiBagrimdikPnsService, MinioService],
})
export class SeleksiBagrimdikPnsModule {}
