import {
  jenis_seleksi_bagrimdik_pns_enum,
  status_dokumen_seleksi_bagrimdik_pns_enum,
  status_seleksi_bagrimdik_pns_enum,
} from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { OrderEnum } from '../../../core/enums/rekrutmen-bagrimdik.enum';
import { OrderByEnum } from '../../../core/enums/seleksi-bagrimdik-pns.enum';

export class GetListSeleksiBagrimdikPNSDto {
  @IsOptional() // Allows this field to be omitted or null
  @IsEnum(jenis_seleksi_bagrimdik_pns_enum, {
    message: 'Jenis seleksi tidak valid',
  })
  jenis?: jenis_seleksi_bagrimdik_pns_enum;
}

export class CheckCriteriaSeleksiBagrimdikPNSDto {
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty({ message: 'id seleksi is not provided' })
  public idSeleksi: number;
}

export class GetSeleksiBagrimdikASN {
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty({ message: 'id seleksi is not provided' })
  public idSeleksi: number;

  @IsOptional()
  @IsString()
  public q?: string;

  @IsOptional()
  @IsArray()
  // @IsEnum(StatusSeleksiBagrimdikPNS, { each: true })
  public status?: status_seleksi_bagrimdik_pns_enum[];

  @IsOptional()
  @IsArray()
  // @IsEnum(StatusDokumenSeleksiBagrimdikPNS, { each: true })
  public statusDokumen?: status_dokumen_seleksi_bagrimdik_pns_enum[];

  @IsOptional()
  @IsEnum(OrderByEnum)
  public orderBy?: OrderByEnum = OrderByEnum.no_pendaftaran;

  @IsOptional()
  @IsEnum(OrderEnum)
  public order?: OrderEnum = OrderEnum.asc;
}
