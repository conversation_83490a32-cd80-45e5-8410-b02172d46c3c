import { jenis_seleksi_bagrimdik_pns_enum } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';

class SeleksiTahapDto {
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'startdate harus dalam format YYYY-MM-DD',
  }) // Validasi format tanggal YYYY-MM-DD
  startdate: string;

  @IsString() // Memastikan nilai adalah string
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'enddate harus dalam format YYYY-MM-DD',
  }) // Validasi format tanggal YYYY-MM-DD
  enddate: string;

  @IsNumber()
  @IsNotEmpty()
  tahap: number;
}

export class CreateSeleksiBagrimdikPNSDto {
  @IsNotEmpty({ message: 'Judul seleksi tidak boleh kosong' })
  @IsString()
  judul: string;

  @IsNotEmpty({ message: 'Deskripsi seleksi tidak boleh kosong' })
  @IsString()
  deskripsi: string;

  @IsNotEmpty({ message: 'Jenis seleksi tidak boleh kosong' })
  @IsEnum(jenis_seleksi_bagrimdik_pns_enum, {
    message: 'Jenis seleksi tidak valid',
  })
  jenis: jenis_seleksi_bagrimdik_pns_enum;

  @IsNotEmpty({ message: 'Tanggal mulai seleksi tidak boleh kosong' })
  @IsDateString()
  startdate: string;

  @IsNotEmpty({ message: 'Tanggal berakhir seleksi tidak boleh kosong' })
  @IsDateString()
  enddate: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SeleksiTahapDto)
  seleksiTahap: SeleksiTahapDto[];

  @IsOptional()
  persyaratan: Record<string, any>[];
}
