import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { SeleksiBagrimdikPnsService } from '../service/seleksi-bagrimdik-pns.service';
import { CreateSeleksiBagrimdikPNSDto } from '../dto/create-seleksi-bagrimdik-pns.dto';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Permission } from 'src/core/decorators';
import {
  AnyFilesInterceptor,
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { PaginationDto } from 'src/core/dtos';
import {
  CheckCriteriaSeleksiBagrimdikPNSDto,
  GetListSeleksiBagrimdikPNSDto,
  GetSeleksiBagrimdikASN,
} from '../dto/read-seleksi-bagrimdik-pns.dto';
import { IDProperty } from 'src/rekrutmen-bagrimdik/dto/read-rekrutmen-bagrimdik.dto';
import { UpdateDocumentStatusDto } from '../dto/update-seleksi-bagrimdik-pns.dto';
import { UploadDokumenDTO } from 'src/rekrutmen-bagrimdik/dto/create-rekrutmen-bagrimdik.dto';

@Controller('seleksi-bagrimdik-pns')
@UseGuards(JwtAuthGuard)
export class SeleksiBagrimdikPnsController {
  private readonly logger = new Logger(SeleksiBagrimdikPnsController.name);

  constructor(
    private readonly seleksiBagrimdikPnsService: SeleksiBagrimdikPnsService,
  ) {}

  @Post('/create-seleksi')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: 'logo', maxCount: 1 },
        { name: 'dokumen', maxCount: 1 },
      ],
      {
        fileFilter: (req, file, callback) => {
          if (file.fieldname === 'logo') {
            // Allow only jpg, jpeg, or png for logos
            if (
              !['image/jpeg', 'image/png', 'image/jpg'].includes(file.mimetype)
            ) {
              return callback(
                new BadRequestException(
                  'Only jpg, jpeg, or png files are allowed for logos',
                ),
                false,
              );
            }
          }

          if (file.fieldname === 'dokumen') {
            // Allow only pdf for documents
            if (file.mimetype !== 'application/pdf') {
              return callback(
                new BadRequestException(
                  'Only pdf files are allowed for documents',
                ),
                false,
              );
            }
          }

          callback(null, true); // Accept the file if it passes validation
        },
      },
    ),
  )
  async createSeleksi(
    @Req() req,
    @Body() body: CreateSeleksiBagrimdikPNSDto,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    const response = await this.seleksiBagrimdikPnsService.createSeleksi(
      req,
      body,
      files,
    );
    return response;
  }

  @Get('/list-seleksi')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetListSeleksiBagrimdikPNSDto,
  ) {
    const { data, page, totalPage, totalData } =
      await this.seleksiBagrimdikPnsService.getList(
        req,
        paginationData,
        queries,
      );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get data daftar seleksi',
      page,
      totalPage,
      totalData,
      data,
    };
  }

  @Get('/cek-memenuhi-kriteria-seleksi')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async cekMemenuhiKriteriaSeleksi(
    @Req() req: any,
    @Query() queries: CheckCriteriaSeleksiBagrimdikPNSDto,
  ) {
    const response = await this.seleksiBagrimdikPnsService.cekKriteriaSeleksi(
      req,
      queries,
    );

    return response;
  }

  @Post('/daftar')
  @Permission('PERMISSION_CREATE')
  @UseInterceptors(AnyFilesInterceptor())
  async registerSeleksi(
    @Req() req: any,
    @Body('id_seleksi') id_seleksi: number,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] }[],
  ) {
    const response = await this.seleksiBagrimdikPnsService.registerSeleksi(
      req,
      id_seleksi,
      files,
    );

    return response;
  }

  @Get('/list-peserta')
  @UseGuards(JwtAuthGuard)
  @Permission('PERMISSION_READ')
  async getPesertaList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetSeleksiBagrimdikASN,
  ) {
    const response = await this.seleksiBagrimdikPnsService.listPeserta(
      req,
      queries,
      paginationData,
    );

    return response;
  }

  @Get('/detail-peserta/:id')
  @Permission('PERMISSION_READ')
  async getDetailPeserta(@Req() req: any, @Param() params: IDProperty) {
    const response = await this.seleksiBagrimdikPnsService.findDetailPeserta(
      req,
      String(params.id),
    );

    return response;
  }

  @Patch('/update-document-status/:id')
  @Permission('PERMISSION_UPDATE')
  async updateDocumentStatus(
    @Req() req: any,
    @Param() params: IDProperty,
    @Body() body: UpdateDocumentStatusDto,
  ) {
    const response = await this.seleksiBagrimdikPnsService.updateStatusDocument(
      req,
      params.id,
      body,
    );

    return response;
  }

  @Post('/simpan-dto-peserta/:id')
  @Permission('PERMISSION_UPDATE')
  async simpanDokumenPeserta(@Req() req: any, @Param() params: IDProperty) {
    const response = await this.seleksiBagrimdikPnsService.simpanDokumenPeserta(
      req,
      String(params.id),
    );
    return response;
  }

  @Get('/cek-tahap-seleksi/:id')
  @Permission('PERMISSION_READ')
  async getCekTahapSeleksi(@Req() req: any, @Param() params: IDProperty) {
    const response = await this.seleksiBagrimdikPnsService.cekTahapSeleksi(
      req,
      params.id,
    );

    return response;
  }

  @Get('/data-counter/:id')
  @Permission('PERMISSION_READ')
  async getDataCounter(@Req() req: any, @Param() params: IDProperty) {
    const response = await this.seleksiBagrimdikPnsService.getDataCounter(
      req,
      params.id,
    );

    return response;
  }

  @Post('/import-data-penerimaan-seleksi')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async importDataPenerimaanSeleksi(
    @Req() req: any,
    @Body() body: UploadDokumenDTO,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const response = await this.importDataPenerimaanSeleksi(req, body, file);
    return response;
  }
}
