import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateSeleksiBagrimdikPNSDto } from '../dto/create-seleksi-bagrimdik-pns.dto';
import {
  Prisma,
  status_dokumen_seleksi_bagrimdik_pns_enum,
  status_seleksi_bagrimdik_pns_enum,
  type_persyaratan_seleksi_bagrimdik_pns_enum,
} from '@prisma/client';
import { PaginationDto } from 'src/core/dtos';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import {
  CheckCriteriaSeleksiBagrimdikPNSDto,
  GetListSeleksiBagrimdikPNSDto,
  GetSeleksiBagrimdikASN,
} from '../dto/read-seleksi-bagrimdik-pns.dto';
import { differenceInYears } from 'date-fns';
import {
  listReserveWordNameHeadersSeleksiPNS,
  staticFields,
} from '../../../core/constants/seleksi-bagrimdik-pns.constant';
import { UpdateDocumentStatusDto } from '../dto/update-seleksi-bagrimdik-pns.dto';
import { OrderEnum } from '../../../core/enums/rekrutmen-bagrimdik.enum';
import { OrderByEnum } from '../../../core/enums/seleksi-bagrimdik-pns.enum';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import * as ExcelJS from 'exceljs';
import { TDataImport } from '../../../core/interfaces/rekrutmen-bagrimdik.type';
import { UploadDokumenDTO } from '../../../rekrutmen-bagrimdik/dto/create-rekrutmen-bagrimdik.dto';
import {
  parseDate,
  parseNumber,
  splitIndonesianName,
} from '../../../core/utils/common.utils';
import { ExcelService } from '../../../api-utils/excel/service/excel.service';
import { buildOrderBy, createSearchQuery } from '../../../core/utils/db.utils';

@Injectable()
export class SeleksiBagrimdikPnsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly excelService: ExcelService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async createSeleksi(
    req: any,
    body: CreateSeleksiBagrimdikPNSDto,
    files: any,
  ) {
    try {
      const { judul, deskripsi, jenis, startdate, enddate } = body;

      // Parsing seleksiTahap
      const seleksiTahap = [];
      let i = 0;
      const dataTahap = this.ensureArray(body['seleksiTahap']);
      dataTahap.forEach((data) => {
        seleksiTahap.push({
          tahap_mulai: parseDate(data.startdate),
          tahap_selesai: parseDate(data.enddate),
          tahap: data.tahap,
        });
      });

      const persyaratan = [];
      i = 0;
      const dataPersyaratan = this.ensureArray(body['persyaratan']);
      dataPersyaratan.forEach((data) => {
        persyaratan.push({
          id: data.id,
          value: data.value,
          prioritas: data.prioritas === 'true',
        });
      });

      const upload: Record<string, any> = {};
      if (files.logo) {
        const uploadLogo = await this.minioService.uploadFile(files.logo[0]);
        upload.logo = uploadLogo.Location;
      }

      if (files.dokumen) {
        const uploadDokumen = await this.minioService.uploadFile(
          files.dokumen[0],
        );
        upload.dokumen = uploadDokumen.Location;
      }

      const seleksiData = {
        judul,
        deskripsi,
        jenis,
        pendaftaran_mulai: parseDate(startdate),
        pendaftaran_selesai: parseDate(enddate),
        sedang_tahap: 1,
        logo_file: upload?.logo || null,
        dokumen_file: upload?.dokumen || null,
        tahapan_seleksi: seleksiTahap.length,
        persyaratan_list: persyaratan as Prisma.JsonArray,
      };

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const createSeleksi = await tx.seleksi_bagrimdik_pns.create({
          data: {
            ...seleksiData,
            seleksi_bagrimdik_pns_tahap: {
              createMany: {
                data: seleksiTahap.map((item) => {
                  return {
                    tahap_mulai: item.tahap_mulai,
                    tahap_selesai: item.tahap_selesai,
                    tahap: BigInt(item.tahap),
                  };
                }),
              },
            },
          },
        });

        await tx.seleksi_bagrimdik_pns_syarat.createMany({
          data: persyaratan.map((item) => {
            return {
              seleksi_bagrimdik_pns_id: BigInt(createSeleksi.id),
              seleksi_bagrimdik_pns_tipe_persyaratan_id: BigInt(item.id),
              min_value: item.value,
              prioritas: item.prioritas.toString().toLowerCase() === 'true',
            };
          }),
        });

        return createSeleksi;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        seleksi: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(
    req,
    paginationData: PaginationDto,
    queries: GetListSeleksiBagrimdikPNSDto,
  ) {
    const limit = parseNumber(paginationData.limit, 100);
    const page = parseNumber(paginationData.page, 1);
    const { jenis } = queries;

    let searchOrm: Prisma.seleksi_bagrimdik_pnsWhereInput = {};
    let searchBy: Prisma.seleksi_bagrimdik_pnsWhereInput = {};
    if (jenis !== undefined) {
      searchBy.jenis = jenis;
    }
    const startIndex = (page - 1) * limit;
    searchOrm = {
      ...searchBy,
      ...searchOrm,
    };
    const selectOrm: Prisma.seleksi_bagrimdik_pnsFindManyArgs = {
      select: {
        id: true,
        judul: true,
        deskripsi: true,
        jenis: true,
        pendaftaran_mulai: true,
        pendaftaran_selesai: true,
        created_at: true,
      },
      orderBy: {
        created_at: OrderEnum.desc,
      },
    };
    let whereOrm: Prisma.seleksi_bagrimdik_pnsFindManyArgs = {
      where: {
        ...searchOrm,
      },
    };
    let whereOrmCount: Prisma.seleksi_bagrimdik_pnsCountArgs = {
      where: {
        ...searchOrm,
      },
    };
    const [totalData, result] = await this.prisma.$transaction([
      this.prisma.seleksi_bagrimdik_pns.count({
        ...whereOrmCount,
      }),
      this.prisma.seleksi_bagrimdik_pns.findMany({
        ...selectOrm,
        ...whereOrm,
        take: limit,
        skip: startIndex,
      }),
    ]);
    const now = new Date();
    // Map over the result to add the `status` field
    const data = result.map((item) => {
      let status_waktu_seleksi = 'n/a'; // Default status
      if (now < item.pendaftaran_mulai) {
        status_waktu_seleksi = 'Belum Dibuka';
      } else if (
        now >= item.pendaftaran_mulai &&
        now <= item.pendaftaran_selesai
      ) {
        status_waktu_seleksi = 'Dibuka';
      } else if (now > item.pendaftaran_selesai) {
        status_waktu_seleksi = 'Ditutup';
      }

      return { ...item, status_waktu_seleksi };
    });
    const totalPage = Math.ceil(totalData / limit);

    return { data, page, totalPage, totalData };
  }

  async cekKriteriaSeleksi(req, queries: CheckCriteriaSeleksiBagrimdikPNSDto) {
    const { idSeleksi } = queries;
    const getDataSeleksi = await this.prisma.seleksi_bagrimdik_pns.findFirst({
      where: { id: Number(idSeleksi) },
      select: {
        seleksi_bagrimdik_pns_syarat: {
          select: {
            min_value: true,
            prioritas: true,
            seleksi_bagrimdik_pns_tipe_persyaratan: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
    });
    if (!getDataSeleksi) {
      throw new NotFoundException('Data seleksi not found');
    }
    const flatDataSeleksi: Array<any> =
      getDataSeleksi.seleksi_bagrimdik_pns_syarat.map((item) => ({
        value: item.min_value,
        prioritas: item.prioritas,
        tipe_persyaratan_id: item.seleksi_bagrimdik_pns_tipe_persyaratan.id,
        tipe_persyaratan_name: item.seleksi_bagrimdik_pns_tipe_persyaratan.name,
        tipe_persyaratan_type: item.seleksi_bagrimdik_pns_tipe_persyaratan.type,
      }));
    let selectOrm: Prisma.personelSelect = { nama_lengkap: true };
    let islama_mddp_personel_selected = false;
    flatDataSeleksi.forEach((dataSeleksi) => {
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.PANGKAT_RELATION
      ) {
        selectOrm.pangkat_personel = {
          select: {
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
          },
          orderBy: { tmt: 'desc' },
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_PERWIRA ||
        (dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_DALAM_PANGKAT_RELATION &&
          !islama_mddp_personel_selected)
      ) {
        selectOrm.lama_mddp_personel = {
          select: {
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
            lama_menjabat_bulan: true,
          },
          orderBy: { tmt: 'asc' },
        };
        islama_mddp_personel_selected = true;
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.TINGKAT_PENDIDIKAN_UMUM_RELATION
      ) {
        selectOrm.dikum_personel = {
          select: {
            dikum_detail: {
              select: {
                dikum: true,
              },
            },
          },
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.AKREDITASI_PENDIDIKAN_UMUM_RELATION
      ) {
        /// ?? tidak ada rujukan
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.UMUR
      ) {
        selectOrm.tanggal_lahir = true;
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KINERJA
      ) {
        /// ?? tidak ada rujukan
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KESEHATAN
      ) {
        selectOrm.rikkesla_personel = {
          select: { nilai: true },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_JASMANI
      ) {
        selectOrm.jasmani_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai_akhir: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_ROHANI
      ) {
        selectOrm.rohani_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_MENTAL
      ) {
        selectOrm.psikologi_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_AKADEMIS
      ) {
        /// ?? tidak ada rujukan
      }
    });
    const getDataPersonel = await this.prisma.personel.findFirst({
      where: { nrp: req.user.nrp },
      select: {
        ...selectOrm,
      },
    });
    let isPassed = true;
    let dataDefine = { detail: { uploadDokumen: [] }, isEligible: false };
    let ifHavePangkatCondition = false;
    let whatIsPangkatPersonel = null;
    if (getDataPersonel) {
      dataDefine['detail']['nama_lengkap'] = getDataPersonel.nama_lengkap;
      const [namaDepan, namaBelakang] = splitIndonesianName(
        getDataPersonel.nama_lengkap,
      );
      dataDefine['detail']['nama_depan'] = namaDepan;
      dataDefine['detail']['nama_belakang'] = namaBelakang;
      flatDataSeleksi.forEach((dataSeleksi) => {
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.PANGKAT_RELATION
        ) {
          if (getDataPersonel.pangkat_personel.length > 0) {
            const getPangkatPersonel =
              getDataPersonel.pangkat_personel[0]['pangkat']['nama_singkat'];
            const isPassedValue = dataSeleksi.value === getPangkatPersonel;
            ifHavePangkatCondition = true;
            whatIsPangkatPersonel = getPangkatPersonel;
            dataDefine['detail']['pangkat_personel'] = {
              value: getPangkatPersonel,
              minimum: dataSeleksi.value,
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['pangkat_personel'] = {
              value: '',
              minimum: dataSeleksi.value,
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_PERWIRA
        ) {
          if (getDataPersonel.lama_mddp_personel.length > 0) {
            const getMasaDinasPerwira =
              getDataPersonel.lama_mddp_personel.reduce(
                (sum, item) => sum + item.lama_menjabat_bulan,
                0,
              );
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getMasaDinasPerwira >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['masa_dinas_perwira'] = {
              value: getMasaDinasPerwira,
              minimum: parseNumber(dataSeleksi.value),
              wording:
                Math.floor(getMasaDinasPerwira / 12) > 0
                  ? Math.floor(getMasaDinasPerwira / 12) + ' Tahun'
                  : getMasaDinasPerwira + ' Bulan',
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['masa_dinas_perwira'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              wording: '0 Tahun',
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
            type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_DALAM_PANGKAT_RELATION &&
          ifHavePangkatCondition &&
          whatIsPangkatPersonel
        ) {
          if (getDataPersonel.lama_mddp_personel.length > 0) {
            const getMatchMasaDinasDalamPangkat =
              getDataPersonel.lama_mddp_personel.find(
                (item) =>
                  item['pangkat']?.nama_singkat === whatIsPangkatPersonel,
              );
            if (getMatchMasaDinasDalamPangkat) {
              const getMasaDinasDalamPangkat =
                getMatchMasaDinasDalamPangkat.lama_menjabat_bulan;
              const isPassedValue =
                parseNumber(dataSeleksi.value) > 0 &&
                getMasaDinasDalamPangkat >= parseNumber(dataSeleksi.value);
              dataDefine['detail']['masa_dinas_dalam_pangkat'] = {
                value: getMasaDinasDalamPangkat,
                minimum: parseNumber(dataSeleksi.value),
                wording:
                  Math.floor(getMasaDinasDalamPangkat / 12) > 0
                    ? Math.floor(getMasaDinasDalamPangkat / 12) + ' Tahun'
                    : getMasaDinasDalamPangkat + ' Bulan',
                isPassed: isPassedValue,
              };
              if (!isPassedValue) isPassed = false;
            } else {
              dataDefine['detail']['masa_dinas_dalam_pangkat'] = {
                value: 0,
                minimum: parseNumber(dataSeleksi.value),
                wording: '0 Tahun',
                isPassed: false,
              };
              isPassed = false;
            }
          } else {
            dataDefine['detail']['masa_dinas_dalam_pangkat'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              wording: '0 Tahun',
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.TINGKAT_PENDIDIKAN_UMUM_RELATION
        ) {
          if (getDataPersonel.dikum_personel.length > 0) {
            const getMatchTingkatPendidikanUmum =
              getDataPersonel.dikum_personel.find(
                (item) => item['dikum']?.nama === dataSeleksi.value,
              );
            if (getMatchTingkatPendidikanUmum) {
              const getTingkatPendidikanUmum =
                getMatchTingkatPendidikanUmum['dikum']?.nama;
              const isPassedValue =
                dataSeleksi.value === getTingkatPendidikanUmum;
              dataDefine['detail']['tingkat_pendidikan_umum'] = {
                value: getTingkatPendidikanUmum,
                minimum: dataSeleksi.value,
                isPassed: isPassedValue,
              };
              if (!isPassedValue) isPassed = false;
            } else {
              dataDefine['detail']['tingkat_pendidikan_umum'] = {
                value: '',
                minimum: dataSeleksi.value,
                isPassed: false,
              };
              isPassed = false;
            }
          } else {
            dataDefine['detail']['tingkat_pendidikan_umum'] = {
              value: '',
              minimum: dataSeleksi.value,
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.AKREDITASI_PENDIDIKAN_UMUM_RELATION
        ) {
          /// ?? tidak ada rujukan
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.UMUR
        ) {
          if (getDataPersonel.tanggal_lahir) {
            // Get the current date
            const today = new Date();
            const getUmur = differenceInYears(
              today,
              getDataPersonel.tanggal_lahir,
            );
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getUmur >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['usia'] = {
              value: getUmur,
              minimum: parseNumber(dataSeleksi.value),
              wording: getUmur + ' Tahun',
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['usia'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              wording: '0 Tahun',
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KINERJA
        ) {
          /// ?? tidak ada rujukan
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KESEHATAN
        ) {
          if (getDataPersonel.rikkesla_personel.length > 0) {
            const getNilaiKesehatan =
              getDataPersonel.rikkesla_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiKesehatan >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['nilai_kesehatan'] = {
              value: getNilaiKesehatan,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['nilai_kesehatan'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_JASMANI
        ) {
          if (getDataPersonel.jasmani_personel.length > 0) {
            const getNilaiJasmani =
              getDataPersonel.jasmani_personel[0].nilai_akhir;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              parseNumber(getNilaiJasmani) >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['nilai_jasmani'] = {
              value: parseNumber(getNilaiJasmani),
              minimum: parseNumber(dataSeleksi.value),
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['nilai_jasmani'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_ROHANI
        ) {
          if (getDataPersonel.rohani_personel.length > 0) {
            const getNilaiRohani = getDataPersonel.rohani_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiRohani >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['nilai_rohani'] = {
              value: getNilaiRohani,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['nilai_rohani'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_MENTAL
        ) {
          if (getDataPersonel.psikologi_personel.length > 0) {
            const getNilaiMental = getDataPersonel.psikologi_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiMental >= parseNumber(dataSeleksi.value);
            dataDefine['detail']['nilai_mental'] = {
              value: getNilaiMental,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: isPassedValue,
            };
            if (!isPassedValue) isPassed = false;
          } else {
            dataDefine['detail']['nilai_mental'] = {
              value: 0,
              minimum: parseNumber(dataSeleksi.value),
              isPassed: false,
            };
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_AKADEMIS
        ) {
          /// ?? tidak ada rujukan
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.UPLOAD_DOKUMEN
        ) {
          dataDefine['detail']['uploadDokumen'].push({
            tipe_persyaratan_id: Number(dataSeleksi.tipe_persyaratan_id),
            tipe_persyaratan_name: dataSeleksi.tipe_persyaratan_name,
          });
        }
      });
    }
    dataDefine['isEligible'] = isPassed;
    const queryResult = dataDefine;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async registerSeleksi(req: any, seleksi_id: number, files: any[]) {
    try {
      const isEligible = this.isEligible(req, String(seleksi_id));
      if (!isEligible)
        throw new BadRequestException('Maaf, user tidak memenuhi kriteri');
      const unixTimestamp = Math.floor(Date.now() / 1000);
      const nrp = req.user.nrp;
      const personel_id = req.user.personel_id;
      const seleksi = await this.prisma.seleksi_bagrimdik_pns.findFirst({
        where: { id: BigInt(seleksi_id) },
        select: {
          id: true,
          seleksi_bagrimdik_pns_tahap: {
            select: {
              id: true,
              tahap_mulai: true,
              tahap_selesai: true,
              tahap: true,
            },
            orderBy: { tahap: 'asc' },
          },
          seleksi_bagrimdik_pns_syarat: {
            select: {
              id: true,
              min_value: true,
              seleksi_bagrimdik_pns_tipe_persyaratan: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
            where: {
              deleted_at: null,
              seleksi_bagrimdik_pns_tipe_persyaratan: {
                type: type_persyaratan_seleksi_bagrimdik_pns_enum.UPLOAD_DOKUMEN,
              },
            },
          },
        },
      });
      const getSeleksiIsStillOpen = seleksi.seleksi_bagrimdik_pns_tahap[0];
      const now = new Date();
      // Reset the time part for both dates to ensure a fair comparison
      const nowDateOnly = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
      );
      if (nowDateOnly > getSeleksiIsStillOpen.tahap_selesai)
        throw new BadRequestException('Seleksi sudah ditutup');
      const cekSudahDaftar =
        await this.prisma.seleksi_bagrimdik_pns_peserta.count({
          where: {
            personel_id: personel_id,
            seleksi_bagrimdik_pns_tahap_id:
              seleksi.seleksi_bagrimdik_pns_tahap[0].id,
          },
        });
      if (cekSudahDaftar > 0)
        throw new BadRequestException('Anda sudah mendaftar di seleksi ini');
      if (seleksi.seleksi_bagrimdik_pns_syarat.length) {
        const isPdfPresent = files.some(
          (item) => item.mimetype === 'application/pdf',
        );
        if (!isPdfPresent)
          throw new BadRequestException(
            'Invalid upload document, please check type document, is must pdf format',
          );
        const isValidFile = seleksi.seleksi_bagrimdik_pns_syarat.every(
          (syarat) => {
            if (
              syarat.seleksi_bagrimdik_pns_tipe_persyaratan.type ===
              type_persyaratan_seleksi_bagrimdik_pns_enum.UPLOAD_DOKUMEN
            ) {
              const isFound = files.some(
                (item) =>
                  item.fieldname ===
                  String(syarat.seleksi_bagrimdik_pns_tipe_persyaratan.id),
              );
              if (isFound) {
                return true;
              } else {
                return false;
              }
            }
            return true;
          },
        );

        if (!isValidFile)
          throw new BadRequestException(
            'Invalid upload document, please check document input',
          );

        const distinctFiles = new Set(files.map((file) => file.fieldname));
        if (distinctFiles.size != files.length)
          throw new BadRequestException('file cannot be duplicate');
      }

      const registerSeleksi =
        await this.prisma.seleksi_bagrimdik_pns_peserta.create({
          data: {
            personel_id: personel_id,
            no_pendaftaran: unixTimestamp + nrp,
            seleksi_bagrimdik_pns_tahap_id:
              seleksi.seleksi_bagrimdik_pns_tahap[0].id,
            seleksi_bagrimdik_pns_id: seleksi.id,
            status_dokumen:
              status_dokumen_seleksi_bagrimdik_pns_enum.BELUM_DIPERIKSA,
          },
        });
      let uploadedFile = {};
      await Promise.all(
        files.map(async (dataFile) => {
          const fieldName = dataFile.fieldname;
          const { rawFile, uploaded } =
            await this.minioService.uploadFilesWithNRP(dataFile, req.user.nrp);
          const insertToDb =
            await this.prisma.seleksi_bagrimdik_pns_peserta_file.create({
              data: {
                ...rawFile,
                key: uploaded.Key,
                url: uploaded.Location,
                filename: uploaded.filename,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          if (insertToDb) {
            const createTahap1ValueSeleksi =
              await this.prisma.seleksi_bagrimdik_pns_tahap_value.create({
                data: {
                  value: uploaded.Location,
                  seleksi_bagrimdik_pns_peserta_file_id: Number(insertToDb.id),
                  status_dokumen:
                    status_dokumen_seleksi_bagrimdik_pns_enum.BELUM_DIPERIKSA,
                  personel_id: personel_id,
                  seleksi_bagrimdik_pns_tahap_id:
                    seleksi.seleksi_bagrimdik_pns_tahap[0].id,
                },
              });
            if (createTahap1ValueSeleksi) {
              uploadedFile[fieldName] = uploaded.Location;
            }
          } else {
            console.log(
              `failed save info file into db seleksi_bagrimdik_pns_peserta_file ${uploaded.filename} on url ${uploaded.Location}`,
            );
          }
        }),
      );
      const queryResult = { fileUploaded: uploadedFile };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async listPeserta(req: any, queries: GetSeleksiBagrimdikASN, paginationData) {
    try {
      const { q, idSeleksi, status, statusDokumen, orderBy, order } = queries;
      const currentDate = new Date();
      const limit = parseNumber(paginationData.limit, 100);
      const page = parseNumber(paginationData.page, 1);
      // Build the orderBy clause
      let searchOrm: Prisma.seleksi_bagrimdik_pns_pesertaWhereInput = {};
      let searchBy: Prisma.seleksi_bagrimdik_pns_pesertaWhereInput = {};
      if (q !== undefined && q) {
        const createSearchNama = {
          OR: [
            {
              seleksi_bagrimdik_pns_peserta_personel: createSearchQuery({
                name: 'nama_lengkap',
                search: q,
                normal: false,
              }) as Prisma.personelWhereInput,
            },
          ],
        };
        searchOrm = {
          ...createSearchNama,
        };
      }
      if (status !== undefined && status) {
        if (Array.isArray(status)) {
          // Filter out empty strings
          const filteredStatus = status.filter((item) => item);
          if (filteredStatus.length > 0) {
            searchBy.status = {
              in: filteredStatus as status_seleksi_bagrimdik_pns_enum[],
            };
          }
        } else {
          searchBy.status = status;
        }
      }
      if (statusDokumen !== undefined && statusDokumen) {
        if (Array.isArray(statusDokumen)) {
          // Filter out empty strings
          const filteredStatusDokumen = statusDokumen.filter((item) => item);
          if (filteredStatusDokumen.length > 0) {
            searchBy.status_dokumen = {
              in: filteredStatusDokumen as status_dokumen_seleksi_bagrimdik_pns_enum[],
            };
          }
        } else {
          searchBy.status_dokumen = statusDokumen;
        }
      }
      const orderByField =
        orderBy !== undefined && orderBy
          ? staticFields[orderBy]
          : staticFields[OrderByEnum.no_pendaftaran];
      const orderSet = order !== undefined && order ? order : OrderEnum.desc;
      const orderByClause = buildOrderBy(orderByField, orderSet);
      const [listPeserta, totalData] = await this.prisma.$transaction([
        this.prisma.seleksi_bagrimdik_pns_peserta.findMany({
          where: {
            ...searchOrm,
            ...searchBy,
            seleksi_bagrimdik_pns_tahap: {
              seleksi_bagrimdik_pns_id: Number(idSeleksi),
              tahap_mulai: { lte: currentDate },
              tahap_selesai: { gte: currentDate },
            },
          },
          select: {
            id: true,
            no_pendaftaran: true,
            no_ujian: true,
            status_dokumen: true,
            status: true,
            seleksi_bagrimdik_pns_peserta_personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: [orderByClause], // Use the dynamically built orderBy
        }),
        this.prisma.seleksi_bagrimdik_pns_peserta.count({
          where: {
            ...searchOrm,
            ...searchBy,
            seleksi_bagrimdik_pns_tahap: {
              seleksi_bagrimdik_pns_id: Number(idSeleksi),
              tahap_mulai: { lte: currentDate },
              tahap_selesai: { gte: currentDate },
            },
          },
        }),
      ]);

      const queryResult = listPeserta.map((item) => ({
        no_pendaftaran: item.no_pendaftaran,
        no_ujian: item.no_ujian,
        nama_peserta: item.seleksi_bagrimdik_pns_peserta_personel.nama_lengkap,
        nrp: item.seleksi_bagrimdik_pns_peserta_personel.nrp,
        pangkat:
          item.seleksi_bagrimdik_pns_peserta_personel.pangkat_personel[0]
            ?.pangkat.nama_singkat || 'N/A',
        jabatan:
          item.seleksi_bagrimdik_pns_peserta_personel.jabatan_personel[0]
            ?.jabatans.nama || 'N/A',
        satuan:
          item.seleksi_bagrimdik_pns_peserta_personel.jabatan_personel[0]
            ?.jabatans.satuan.nama || 'N/A',
        status: item.status,
        status_dokumen: item.status_dokumen,
      }));

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async findDetailPeserta(req: any, id: string) {
    try {
      const getTahap =
        await this.prisma.seleksi_bagrimdik_pns_peserta.findFirst({
          where: { no_pendaftaran: id },
          select: {
            seleksi_bagrimdik_pns_tahap_id: true,
          },
          orderBy: { created_at: OrderEnum.desc },
        });
      if (!getTahap)
        throw new NotFoundException('Requested detail peserta not found');
      const data = await this.prisma.seleksi_bagrimdik_pns_peserta.findFirst({
        where: { no_pendaftaran: id },
        select: {
          no_pendaftaran: true,
          no_ujian: true,
          status: true,
          status_dokumen: true,
          seleksi_bagrimdik_pns_peserta_personel: {
            select: {
              foto_file: true,
              nama_lengkap: true,
              nrp: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          seleksi_bagrimdik_pns_tahap: {
            select: {
              seleksi_bagrimdik_pns_tahap_value: {
                select: {
                  id: true,
                  value: true,
                  status_dokumen: true,
                },
                where: {
                  status_dokumen: {
                    not: null,
                  },
                  seleksi_bagrimdik_pns_tahap_id:
                    getTahap.seleksi_bagrimdik_pns_tahap_id,
                },
              },
            },
          },
        },
      });

      if (!data) {
        throw new NotFoundException('Requested detail peserta not found');
      }
      const queryResult = {
        foto: data.seleksi_bagrimdik_pns_peserta_personel.foto_file,
        nama_peserta: data.seleksi_bagrimdik_pns_peserta_personel.nama_lengkap,
        pangkat:
          data.seleksi_bagrimdik_pns_peserta_personel.pangkat_personel[0]
            ?.pangkat.nama_singkat || 'N/A',
        nrp: data.seleksi_bagrimdik_pns_peserta_personel.nrp,
        nomor_pendaftaran: data.no_pendaftaran,
        nomor_ujian: data.no_ujian,
        jabatan:
          data.seleksi_bagrimdik_pns_peserta_personel.jabatan_personel[0]
            ?.jabatans.nama || 'N/A',
        satuan:
          data.seleksi_bagrimdik_pns_peserta_personel.jabatan_personel[0]
            ?.jabatans.satuan.nama || 'N/A',
        status: data.status,
        status_dokumen: data.status_dokumen,
        uploaded_dokumen:
          data.seleksi_bagrimdik_pns_tahap.seleksi_bagrimdik_pns_tahap_value ||
          [],
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  public async updateStatusDocument(
    req: any,
    id: number,
    body: UpdateDocumentStatusDto,
  ) {
    try {
      const { statusDokumen } = body;
      if (
        id !== undefined &&
        id &&
        UpdateDocumentStatusDto !== undefined &&
        UpdateDocumentStatusDto
      ) {
        const queryResult =
          await this.prisma.seleksi_bagrimdik_pns_tahap_value.update({
            where: {
              id: id,
            },
            data: {
              status_dokumen: statusDokumen,
            },
            select: {
              id: true,
              status_dokumen: true,
              value: true,
            },
          });
        if (queryResult) {
          const message = convertToLogMessage(
            ConstantLogStatusEnum.SUCCESS,
            ConstantLogTypeEnum.READ_LOG_TYPE,
            ConstantLogDataTypeEnum.OBJECT,
            ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
          );
          await this.logsActivityService.addLogsActivity(
            convertToILogData(
              req,
              CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ as ConstantLogType,
              message,
              queryResult,
            ),
          );

          return {
            statusCode: HttpStatus.OK,
            message,
            data: queryResult,
          };
        } else {
          throw new BadRequestException(`Failed update status document`);
        }
      } else {
        throw new BadRequestException(`Parameter not valid`);
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async simpanDokumenPeserta(req: any, id: string) {
    let queryResult;

    try {
      if (id !== undefined && id) {
        const getTahap =
          await this.prisma.seleksi_bagrimdik_pns_peserta.findFirst({
            where: { no_pendaftaran: id },
            select: {
              id: true,
              personel_id: true,
              seleksi_bagrimdik_pns_tahap_id: true,
              no_pendaftaran: true,
            },
            orderBy: { created_at: OrderEnum.desc },
          });
        if (!getTahap)
          throw new NotFoundException('Requested detail peserta not found');
        const getTahapValueStatusDocument =
          await this.prisma.seleksi_bagrimdik_pns_tahap_value.findMany({
            where: {
              personel_id: getTahap.personel_id,
              seleksi_bagrimdik_pns_tahap_id:
                getTahap.seleksi_bagrimdik_pns_tahap_id,
              seleksi_bagrimdik_pns_peserta_file_id: {
                not: null,
              },
            },
            select: {
              status_dokumen: true,
            },
          });
        let isAllDocumentDiterima = false;
        let isAllDocumentBelumDiperikasa = true;
        if (getTahapValueStatusDocument.length > 0) {
          isAllDocumentDiterima = getTahapValueStatusDocument.every(
            (item) =>
              item.status_dokumen ===
              status_dokumen_seleksi_bagrimdik_pns_enum.DITERIMA,
          );
        }
        if (getTahapValueStatusDocument.length > 0) {
          isAllDocumentBelumDiperikasa = getTahapValueStatusDocument.every(
            (item) =>
              item.status_dokumen ===
              status_dokumen_seleksi_bagrimdik_pns_enum.BELUM_DIPERIKSA,
          );
        }
        if (isAllDocumentDiterima) {
          queryResult = await this.prisma.seleksi_bagrimdik_pns_peserta.update({
            where: {
              id: getTahap.id,
            },
            data: {
              status_dokumen:
                status_dokumen_seleksi_bagrimdik_pns_enum.DITERIMA,
              status: status_seleksi_bagrimdik_pns_enum.LOLOS,
            },
            select: {
              id: true,
              no_pendaftaran: true,
              status_dokumen: true,
            },
          });
        } else if (!isAllDocumentBelumDiperikasa) {
          queryResult = await this.prisma.seleksi_bagrimdik_pns_peserta.update({
            where: {
              id: getTahap.id,
            },
            data: {
              status_dokumen:
                status_dokumen_seleksi_bagrimdik_pns_enum.DIPERIKSA,
              status: status_seleksi_bagrimdik_pns_enum.TIDAK_LOLOS,
            },
            select: {
              id: true,
              no_pendaftaran: true,
              status_dokumen: true,
            },
          });
        }
      } else {
        throw new BadRequestException(`Please, check parameter input`);
      }

      if (queryResult) {
        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_UPDATE as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        };
      } else {
        throw new BadRequestException(`Failed simpan document peserta`);
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async cekTahapSeleksi(req: any, id: number) {
    try {
      if (id !== undefined && id) {
        const queryResult = await this.prisma.seleksi_bagrimdik_pns.findFirst({
          where: { id: Number(id) },
          select: {
            id: true,
            sedang_tahap: true,
          },
        });
        if (!queryResult) {
          throw new NotFoundException('Requested detail tahap not found');
        } else {
          const message = convertToLogMessage(
            ConstantLogStatusEnum.SUCCESS,
            ConstantLogTypeEnum.READ_LOG_TYPE,
            ConstantLogDataTypeEnum.OBJECT,
            ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
          );
          await this.logsActivityService.addLogsActivity(
            convertToILogData(
              req,
              CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ as ConstantLogType,
              message,
              queryResult,
            ),
          );

          return {
            statusCode: HttpStatus.OK,
            message,
            queryResult,
          };
        }
      } else {
        throw new BadRequestException(`Please, check parameter input`);
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async getDataCounter(req: any, id: number) {
    try {
      if (id !== undefined && id) {
        const dataTotalPeserta =
          await this.prisma.seleksi_bagrimdik_pns_peserta.count({
            where: { seleksi_bagrimdik_pns_id: Number(id) },
          });

        const dataTotalPesertaSudahDiperiksa =
          await this.prisma.seleksi_bagrimdik_pns_peserta.count({
            where: {
              seleksi_bagrimdik_pns_id: Number(id),
              status_dokumen:
                status_dokumen_seleksi_bagrimdik_pns_enum.DITERIMA,
            },
          });
        const queryResult = {
          totalPeserta: dataTotalPeserta,
          diperiksa: dataTotalPesertaSudahDiperiksa,
        };

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.READ_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        };
      } else {
        throw new BadRequestException(`Please, check parameter input`);
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  // helper

  private ensureArray(input) {
    if (Array.isArray(input)) {
      return input; // Already an array
    }

    if (typeof input === 'string') {
      try {
        const parsed = JSON.parse(input.replace(/'/g, '"')); // Replace single quotes with double quotes and parse
        if (Array.isArray(parsed)) {
          return parsed; // Successfully parsed into an array
        }
      } catch (error) {
        return [];
      }
    }
    return [];
  }

  private async isEligible(req, idSeleksi: string) {
    const getDataSeleksi = await this.prisma.seleksi_bagrimdik_pns.findFirst({
      where: { id: Number(idSeleksi) },
      select: {
        seleksi_bagrimdik_pns_syarat: {
          select: {
            min_value: true,
            prioritas: true,
            seleksi_bagrimdik_pns_tipe_persyaratan: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
    });
    if (!getDataSeleksi) {
      return false;
    }
    const flatDataSeleksi: Array<any> =
      getDataSeleksi.seleksi_bagrimdik_pns_syarat.map((item) => ({
        value: item.min_value,
        prioritas: item.prioritas,
        tipe_persyaratan_id: item.seleksi_bagrimdik_pns_tipe_persyaratan.id,
        tipe_persyaratan_name: item.seleksi_bagrimdik_pns_tipe_persyaratan.name,
        tipe_persyaratan_type: item.seleksi_bagrimdik_pns_tipe_persyaratan.type,
      }));
    let selectOrm: Prisma.personelSelect = { nama_lengkap: true };
    let islama_mddp_personel_selected = false;
    flatDataSeleksi.forEach((dataSeleksi) => {
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.PANGKAT_RELATION
      ) {
        selectOrm.pangkat_personel = {
          select: {
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
          },
          orderBy: { tmt: 'desc' },
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_PERWIRA ||
        (dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_DALAM_PANGKAT_RELATION &&
          !islama_mddp_personel_selected)
      ) {
        selectOrm.lama_mddp_personel = {
          select: {
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
            lama_menjabat_bulan: true,
          },
          orderBy: { tmt: 'asc' },
        };
        islama_mddp_personel_selected = true;
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.TINGKAT_PENDIDIKAN_UMUM_RELATION
      ) {
        selectOrm.dikum_personel = {
          select: {
            dikum_detail: {
              select: {
                dikum: true,
              },
            },
          },
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.AKREDITASI_PENDIDIKAN_UMUM_RELATION
      ) {
        /// ?? tidak ada rujukan
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.UMUR
      ) {
        selectOrm.tanggal_lahir = true;
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KINERJA
      ) {
        /// ?? tidak ada rujukan
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KESEHATAN
      ) {
        selectOrm.rikkesla_personel = {
          select: { nilai: true },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_JASMANI
      ) {
        selectOrm.jasmani_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai_akhir: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_ROHANI
      ) {
        selectOrm.rohani_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_MENTAL
      ) {
        selectOrm.psikologi_personel = {
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: [
            { tahun: 'desc' },
            { semester: 'desc' },
            { created_at: 'desc' },
          ],
          take: 1,
        };
      }
      if (
        dataSeleksi.tipe_persyaratan_type ===
        type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_AKADEMIS
      ) {
        /// ?? tidak ada rujukan
      }
    });
    const getDataPersonel = await this.prisma.personel.findFirst({
      where: { nrp: req.user.nrp },
      select: {
        ...selectOrm,
      },
    });
    let isPassed = true;
    let ifHavePangkatCondition = false;
    let whatIsPangkatPersonel = null;
    if (getDataPersonel) {
      flatDataSeleksi.forEach((dataSeleksi) => {
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.PANGKAT_RELATION
        ) {
          if (getDataPersonel.pangkat_personel.length > 0) {
            const getPangkatPersonel =
              getDataPersonel.pangkat_personel[0]['pangkat']['nama_singkat'];
            const isPassedValue = dataSeleksi.value === getPangkatPersonel;
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_PERWIRA
        ) {
          if (getDataPersonel.lama_mddp_personel.length > 0) {
            const getMasaDinasPerwira =
              getDataPersonel.lama_mddp_personel.reduce(
                (sum, item) => sum + item.lama_menjabat_bulan,
                0,
              );
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getMasaDinasPerwira >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
            type_persyaratan_seleksi_bagrimdik_pns_enum.MASA_DINAS_DALAM_PANGKAT_RELATION &&
          ifHavePangkatCondition &&
          whatIsPangkatPersonel
        ) {
          if (getDataPersonel.lama_mddp_personel.length > 0) {
            const getMatchMasaDinasDalamPangkat =
              getDataPersonel.lama_mddp_personel.find(
                (item) =>
                  item['pangkat']?.nama_singkat === whatIsPangkatPersonel,
              );
            if (getMatchMasaDinasDalamPangkat) {
              const getMasaDinasDalamPangkat =
                getMatchMasaDinasDalamPangkat.lama_menjabat_bulan;
              const isPassedValue =
                parseNumber(dataSeleksi.value) > 0 &&
                getMasaDinasDalamPangkat >= parseNumber(dataSeleksi.value);
              if (!isPassedValue) isPassed = false;
            } else {
              isPassed = false;
            }
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.TINGKAT_PENDIDIKAN_UMUM_RELATION
        ) {
          if (getDataPersonel.dikum_personel.length > 0) {
            const getMatchTingkatPendidikanUmum =
              getDataPersonel.dikum_personel.find(
                (item) => item['dikum']?.nama === dataSeleksi.value,
              );
            if (getMatchTingkatPendidikanUmum) {
              const getTingkatPendidikanUmum =
                getMatchTingkatPendidikanUmum['dikum']?.nama;
              const isPassedValue =
                dataSeleksi.value === getTingkatPendidikanUmum;
              if (!isPassedValue) isPassed = false;
            } else {
              isPassed = false;
            }
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.AKREDITASI_PENDIDIKAN_UMUM_RELATION
        ) {
          /// ?? tidak ada rujukan
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.UMUR
        ) {
          if (getDataPersonel.tanggal_lahir) {
            // Get the current date
            const today = new Date();
            const getUmur = differenceInYears(
              today,
              getDataPersonel.tanggal_lahir,
            );
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getUmur >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KINERJA
        ) {
          /// ?? tidak ada rujukan
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_KESEHATAN
        ) {
          if (getDataPersonel.rikkesla_personel.length > 0) {
            const getNilaiKesehatan =
              getDataPersonel.rikkesla_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiKesehatan >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_JASMANI
        ) {
          if (getDataPersonel.jasmani_personel.length > 0) {
            const getNilaiJasmani =
              getDataPersonel.jasmani_personel[0].nilai_akhir;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              parseNumber(getNilaiJasmani) >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_ROHANI
        ) {
          if (getDataPersonel.rohani_personel.length > 0) {
            const getNilaiRohani = getDataPersonel.rohani_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiRohani >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_MENTAL
        ) {
          if (getDataPersonel.psikologi_personel.length > 0) {
            const getNilaiMental = getDataPersonel.psikologi_personel[0].nilai;
            const isPassedValue =
              parseNumber(dataSeleksi.value) > 0 &&
              getNilaiMental >= parseNumber(dataSeleksi.value);
            if (!isPassedValue) isPassed = false;
          } else {
            isPassed = false;
          }
        }
        if (
          dataSeleksi.tipe_persyaratan_type ===
          type_persyaratan_seleksi_bagrimdik_pns_enum.NILAI_AKADEMIS
        ) {
          /// ?? tidak ada rujukan
        }
      });
    }
    return isPassed;
  }

  async importDataPenerimaanSeleksi(
    req: any,
    body: UploadDokumenDTO,
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }
    const workbook = new ExcelJS.Workbook();
    try {
      await workbook.xlsx.load(file.buffer);
      // Loop through all sheets and process them individually
      // Store the result for this sheet in the result object, identified by the sheet name
      let queryResult: TDataImport = { headerMap: [], data: [] };
      const firstWorksheet = workbook.worksheets[0];
      if (firstWorksheet) {
        // Determine how many rows are used for headers in the sheet
        const headerDepth = this.excelService.getHeaderDepth(
          firstWorksheet,
          listReserveWordNameHeadersSeleksiPNS,
        );

        // Dynamically parse the headers into a flat structure using dot notation
        const headerMap = this.excelService.parseHeaders(
          firstWorksheet,
          headerDepth,
        );

        // Parse data rows and map them to the flat header structure
        const data = this.excelService.parseData(
          firstWorksheet,
          headerMap,
          headerDepth,
        );
        // Store the result for this sheet in the result object, identified by the sheet name
        queryResult = { headerMap, data };
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_PNS_READ_EXCEL as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Error loading workbook: ' + error,
      );
    } finally {
      // You can allow garbage collection to free memory by ensuring you no longer reference the workbook
      workbook.xlsx.writeBuffer = null; // Clear the internal write buffer if you're done writing
    }
  }
}
