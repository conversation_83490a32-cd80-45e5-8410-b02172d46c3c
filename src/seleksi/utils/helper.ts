import { BadRequestException } from '@nestjs/common';
import {
  comparison_type_enum,
  Prisma,
  promosi_jabatan_stage_status_enum,
} from '@prisma/client';
import * as fs from 'fs';
import { capitalize } from 'lodash';
import * as moment from 'moment/moment';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { PromosiJabatanParticipantStatusEnum } from '../../core/enums/promosi-jabatan.enum';
import {
  IHistoryStage,
  IValidationRequirement,
} from '../../core/interfaces/promosi-jabatan.interface';
import {
  IFileUpload,
  IProceedExcel,
  IRestructureParticipantList,
  ISeleksiResult,
  IValidateRequirement,
} from '../../core/interfaces/seleksi.interface';
import { capitalizeEachWord } from '../../core/utils/common.utils';
import {
  compareValues,
  validateFile,
  validationValue,
} from '../../core/utils/validation.utils';

export class SeleksiHelper {
  protected minioService: MinioService;
  protected prisma: PrismaService;
  protected excelService: ExcelService;

  constructor(
    minioService: MinioService,
    prisma: PrismaService,
    excelService: ExcelService,
  ) {
    this.prisma = prisma;
    this.minioService = minioService;
    this.excelService = excelService;
  }

  protected parseHistoryStages(historyStages?: any): IHistoryStage[] {
    if (!Array.isArray(historyStages)) return [];
    return (historyStages as unknown as IHistoryStage[]).map((stage) => ({
      stage_id: Number(stage.stage_id),
      stage: Number(stage.stage),
      created_at: String(stage.created_at),
      status: stage.status || null,
    }));
  }

  protected parseFileUploads(fileUploads?: any): IFileUpload[] {
    if (!Array.isArray(fileUploads)) return [];

    return (fileUploads as unknown as IFileUpload[]).map((fileUpload) => ({
      requirement_file_id: fileUpload.requirement_file_id,
      file_id: fileUpload.file_id,
      created_at: fileUpload.created_at,
      status: fileUpload.status,
      reason: fileUpload.reason,
    }));
  }

  protected async uploadFile(file: Express.Multer.File) {
    if (!file) return null;
    const uploadedFile = await this.minioService.uploadFile(file);
    return {
      original_name: file.originalname,
      encoding: file.encoding,
      mime_type: file.mimetype,
      file_name: uploadedFile.filename,
      size: file.size,
      key: uploadedFile.Key,
      url: uploadedFile.Location,
    };
  }

  protected async validationRequirement(
    personel: any,
    name: string,
    value: string,
    comparison_type: comparison_type_enum,
  ): Promise<IValidationRequirement> {
    if (!personel || !name || !value) return { is_valid: false, value: null };

    const lowerName = name.toLowerCase();

    switch (lowerName) {
      case 'pangkat':
        return this.validatePangkat(personel, value, comparison_type);
      case 'umur':
        return this.validateUmur(
          personel.tanggal_lahir,
          value,
          comparison_type,
        );
      case 'jabatan':
        return this.validateJabatan(personel, value, comparison_type);
      case 'tmt jabatan':
        return this.validateTmtJabatan(personel, value, comparison_type);
      default:
        return { is_valid: false, value: null };
    }
  }

  protected async validatePangkat(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): Promise<IValidationRequirement> {
    const pangkatDetail = await this.prisma.pangkat.findFirst({
      where: { deleted_at: null, id: Number(value) },
      select: {
        id: true,
        nama: true,
        nama_singkat: true,
        tingkat_id: true,
      },
    });

    if (!pangkatDetail) return { is_valid: false, value: null };

    const pangkatPersonel = personel.pangkat_personel?.[0]?.pangkat;
    if (!pangkatPersonel) return { is_valid: false, value: pangkatDetail.nama };

    const { nama, nama_singkat, tingkat_id } = pangkatPersonel;

    if (comparison_type !== 'EQUAL')
      return {
        is_valid: compareValues(
          tingkat_id,
          pangkatDetail.tingkat_id,
          comparison_type,
        ),
        value: pangkatDetail.nama,
      };

    return {
      is_valid:
        compareValues(nama, pangkatDetail.nama, comparison_type) ||
        compareValues(
          nama_singkat,
          pangkatDetail.nama_singkat,
          comparison_type,
        ),
      value: pangkatDetail.nama,
    };
  }

  protected validateUmur(
    tanggalLahir: string,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const birthDate = moment(tanggalLahir, moment.ISO_8601, true);
    if (!birthDate.isValid()) return { is_valid: false, value: null };

    const age = Number(birthDate.fromNow(true).split(' ')[0]);
    return {
      is_valid: compareValues(age, Number(value), comparison_type),
      value,
    };
  }

  protected validateJabatan(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const jabatan = personel.jabatan_personel?.[0]?.jabatans?.[0]?.nama;
    if (!jabatan) return { is_valid: false, value: null };

    return { is_valid: compareValues(value, jabatan, comparison_type), value };
  }

  protected validateTmtJabatan(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const tmt = personel.jabatan_personel?.[0]?.tmt_jabatan;
    if (!tmt) return { is_valid: false, value: null };

    const tmtDate = moment(tmt, moment.ISO_8601, true).startOf('day');
    const inputDate = moment(value, moment.ISO_8601, true).startOf('day');

    if (!tmtDate.isValid() || !inputDate.isValid())
      return { is_valid: false, value: null };

    return {
      is_valid: compareValues(inputDate, tmtDate, comparison_type, true),
      value,
    };
  }

  protected async processFile(
    file: Express.Multer.File | undefined,
    fieldname: string,
    maxSize?: number | null,
    extensions?: string[] | null,
  ) {
    if (!file) return null;
    validateFile(file, fieldname, maxSize, extensions);
    return this.uploadFile(file);
  }

  protected async processBannerStages(files: Express.Multer.File[]) {
    const bannerStages: Record<number, Express.Multer.File> = {};

    for (const file of files) {
      const match = file.fieldname.match(/^banner_stages\[(\d+)]$/);
      if (match) {
        const index = parseInt(match[1], 10);
        validateFile(file, `banner_stages[${index}]`, 2 * 1024 * 1024, [
          'jpg',
          'jpeg',
          'png',
          'gif',
        ]);
        if (!bannerStages[index]) bannerStages[index] = file;
      }
    }

    const uploadBannerStages = {};
    for (const [idx, file] of Object.entries(bannerStages)) {
      uploadBannerStages[idx] = await this.uploadFile(file);
    }
    return uploadBannerStages;
  }

  protected async processRequirements(requirements: any[]) {
    const result = { create: [], update: [] };

    if (!requirements?.length) return result;

    await Promise.all(
      requirements.map(async (requirement) => {
        const requirementExisting =
          await this.prisma.selection_requirement.findFirst({
            where: { id: requirement.id },
          });

        if (!requirementExisting) {
          throw new BadRequestException('Syarat tidak ditemukan');
        }

        const mock = {
          requirement_id: requirement.id,
          is_required: requirement.is_required,
          comparison_type: requirement.comparison_type,
          value: validationValue(
            requirementExisting.input_type,
            requirement.value,
          ),
        };

        if (!requirement.existing_id) {
          result.create.push(mock);
          return;
        }

        result.update.push({ id: requirement.existing_id, data: mock });
      }),
    );

    return result;
  }

  protected async processStages(
    stages: any[],
    fileBannerStages: Record<string, any>,
  ) {
    const result = { create: [], update: [] };

    stages.map((stage, index) => {
      const mock = {
        stage: index + 1,
        name: stage.name,
        banner_id: fileBannerStages[index]?.id ?? null,
        status: promosi_jabatan_stage_status_enum.DIBUKA,
        start_date: moment(stage.start_date)
          .startOf('day')
          .add(1, 'day')
          .toISOString(),
        end_date: moment(stage.end_date)
          .startOf('day')
          .add(1, 'day')
          .toISOString(),
      };

      if (!stage.existing_id) {
        result.create.push(mock);
        return;
      }

      if (!mock.banner_id) delete mock.banner_id;
      delete mock.status;
      result.update.push({ id: stage.existing_id, data: mock });
    });
    return result;
  }

  protected async storeBannerStages(
    tx: any,
    uploadBannerStages: Record<string, any>,
  ) {
    const storedBannerStages = {};
    for (const [idx, bannerStage] of Object.entries(uploadBannerStages)) {
      storedBannerStages[idx] = await tx.selection_file.create({
        data: bannerStage,
      });
    }
    return storedBannerStages;
  }

  protected async restructurePersonel(personel: any) {
    if (!personel) return null;

    const jabatanPersonel = personel.jabatan_personel?.[0];
    const pangkatPersonel = personel.pangkat_personel?.[0]?.pangkat;
    const jabatan = jabatanPersonel?.jabatans;

    const foto_file = personel.foto_file
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
        )
      : null;

    return {
      id: personel.id,
      nrp: personel.nrp,
      jenis_kelamin: personel.jenis_kelamin,
      nama_lengkap: personel.nama_lengkap,
      tanggal_lahir: personel.tanggal_lahir,
      foto_file: foto_file,
      pangkat: pangkatPersonel,
      jabatan: jabatan
        ? {
            id: jabatan.id,
            nama: jabatan.nama,
          }
        : null,
      tmt_jabatan: jabatanPersonel?.tmt_jabatan || null,
      satuan: jabatan?.satuan || null,
    };
  }

  protected async restructureStages(stage: Record<string, any>) {
    stage['banner'] = '';
    if (stage.selection_bagassus_stage_files_banner?.file_name) {
      stage['banner'] = await this.minioService.checkFileExist(
        process.env.MINIO_BUCKET_NAME!,
        `${process.env.MINIO_PATH_FILE}/${stage.selection_bagassus_stage_files_banner.file_name}`,
      );
    }
    delete stage.selection_bagassus_stage_files_banner;
  }

  protected setEligibleMessage(hasRegistered: boolean, result: ISeleksiResult) {
    if (hasRegistered) {
      result.message = 'Anda sudah terdaftar';
      result.eligible = false;
    } else if (!result.stages?.['id']) {
      result.message = 'Registrasi sudah ditutup';
      result.eligible = false;
    } else if (!result.eligible) {
      result.message = 'Anda tidak memenuhi kriteria';
    } else {
      result.message = 'Anda memenuhi kriteria';
    }
  }

  protected async validateRequirements(
    requirements: Array<any>,
    personelData: Record<string, any>,
    result: Record<string, any>,
  ) {
    await Promise.all(
      requirements.map(async (requirement) => {
        const {
          selection_requirement: { auto_validate, name },
        } = requirement;
        delete requirement.selection_requirement;

        const validatorKey = `_validate${capitalize(name?.replace(/\W/g, ''))}`;
        const validatorFunc = this?.[validatorKey];
        const withValidate = !auto_validate || !validatorFunc;

        requirement.is_valid = !requirement.is_required;
        requirement.auto_validate = auto_validate;
        requirement.name = name;

        if (withValidate) {
          result.requirements.push(requirement);
          return;
        }

        const params: IValidateRequirement = {
          prisma: this.prisma,
          personelData,
          requirement,
          result,
        };

        await validatorFunc(params);
      }),
    );

    const eligibleCheck = [];
    result.requirements.sort((a, b) => {
      if (a.is_required) eligibleCheck.push(a.is_valid);

      if (a.id < b.id) {
        return -1;
      } else if (a.id > b.id) {
        return 1;
      } else {
        return 0;
      }
    });

    result.eligible = eligibleCheck.every((val) => val);
  }

  private async _validatePangkat(params: IValidateRequirement) {
    const { requirement, result, prisma, personelData } = params;
    let valid: boolean;
    const pangkat = await prisma.pangkat.findFirst({
      where: { id: Number(requirement.value), deleted_at: null },
      select: { id: true, nama: true },
    });
    if (personelData?.pangkat?.pangkat_id) {
      if (!pangkat) {
        valid = false;
      } else {
        valid = compareValues(
          String(personelData?.pangkat?.pangkat_id),
          requirement.value,
          requirement.comparison_type,
        );
      }
    } else {
      valid = false;
    }

    requirement.value = capitalizeEachWord(pangkat?.nama);
    requirement['is_valid'] = valid;
    result.requirements.push(requirement);
  }

  private async _validateJabatan(params: IValidateRequirement) {
    const { requirement, result, prisma, personelData } = params;
    let valid: boolean;
    const jabatan = await prisma.jabatan.findFirst({
      where: { id: Number(requirement.value), deleted_at: null },
      select: { id: true, nama: true },
    });

    if (personelData?.jabatan?.jabatan_id) {
      if (!jabatan) {
        valid = false;
      } else {
        valid = compareValues(
          String(personelData?.jabatan?.jabatan_id),
          requirement.value,
          requirement.comparison_type,
        );
      }
    } else {
      valid = false;
    }

    requirement.value = capitalizeEachWord(jabatan?.nama);
    requirement['is_valid'] = valid;
    result.requirements.push(requirement);
  }

  private async _validateUmur(params: IValidateRequirement) {
    const { requirement, result, personelData } = params;
    const umur = moment().diff(personelData.tanggal_lahir, 'years');

    requirement['is_valid'] = compareValues(
      String(umur),
      requirement.value,
      requirement.comparison_type,
    );
    result.requirements.push(requirement);
  }

  private async _validateTmtjabatan(params: IValidateRequirement) {
    const { requirement, result, personelData } = params;
    const tmt = personelData.jabatan?.tmt;
    if (tmt) {
      const tmtDate = moment(tmt, moment.ISO_8601, true).startOf('day');
      const inputDate = moment(
        requirement.value,
        moment.ISO_8601,
        true,
      ).startOf('day');

      if (!tmtDate.isValid() || !inputDate.isValid()) {
        requirement['is_valid'] = false;
      } else {
        requirement['is_valid'] = compareValues(
          inputDate,
          tmtDate,
          requirement.comparison_type,
          true,
        );
      }
    } else {
      requirement['is_valid'] = false;
    }

    result.requirements.push(requirement);
  }

  protected determineParticipantStatus(
    stage,
    fileUploads,
    historyStages: IHistoryStage[],
  ) {
    if (!stage || stage?.stage === 1) {
      return fileUploads?.some((file) =>
        [
          PromosiJabatanParticipantStatusEnum.REJECTED,
          PromosiJabatanParticipantStatusEnum.REVISION,
          PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
        ].includes(file.status),
      )
        ? fileUploads.find((file) =>
            [
              PromosiJabatanParticipantStatusEnum.REJECTED,
              PromosiJabatanParticipantStatusEnum.REVISION,
              PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
            ].includes(file.status),
          )?.status
        : PromosiJabatanParticipantStatusEnum.CHECKED;
    }

    return (
      historyStages?.find((history) => history.stage === stage.stage)?.status ||
      PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET
    );
  }

  protected async getFileDictionary(files: Array<any>) {
    const fileMap = new Map();
    await Promise.all(
      files.map(async (file) => {
        fileMap.set(String(file.id), {
          id: file.id,
          url: await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}/${file.file_name}`,
          ),
          file_name: file.file_name,
        });
      }),
    );
    return fileMap;
  }

  protected async getRequirementFileDictionary(requirementFiles: Array<any>) {
    const requirementFileMap = new Map();
    await Promise.all(
      requirementFiles.map(async (requirementFile) => {
        requirementFileMap.set(String(requirementFile.id), requirementFile);
      }),
    );
    return requirementFileMap;
  }

  protected async getFotoDictionary(personels: Array<any>) {
    const fotoMap = new Map();
    await Promise.all(
      personels.map(async (personel) => {
        fotoMap.set(
          personel.nrp,
          await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}/${personel.nrp}/${personel.foto_file}`,
          ),
        );
      }),
    );
    return fotoMap;
  }

  protected async getJabatanDictionary(jabatan: Array<any>) {
    const jabatanMap = new Map();

    if (!jabatan?.length) return jabatanMap;

    await Promise.all(
      jabatan.map(async (item) => {
        if (jabatanMap.has(item.personel_id)) return;

        const { satuan_id, nama, ...rest } = item;
        jabatanMap.set(item.personel_id, {
          jabatan: rest,
          satuan: { id: satuan_id, nama },
        });
      }),
    );
    return jabatanMap;
  }

  protected async getPangkatDictionary(pangkat: Array<any>) {
    const pangkatMap = new Map();

    if (!pangkat?.length) return pangkatMap;

    await Promise.all(
      pangkat.map(async (item) => {
        if (pangkatMap.has(item.personel_id)) return;

        pangkatMap.set(item.personel_id, item);
      }),
    );
    return pangkatMap;
  }

  protected async restructureParticipationList({
    fileMap,
    fotoMap,
    jabatanMap,
    pangkatMap,
    stage,
    data,
    fileRequirementMap,
  }: IRestructureParticipantList) {
    await Promise.all(
      data.map((item) => {
        item.file_uploads = (item.file_uploads as any).map((file) => {
          const fileData = fileMap.get(String(file.file_id));
          const fileRequirement = fileRequirementMap.get(
            String(file.requirement_file_id),
          );
          file.file = fileData;
          file.file_requirement = fileRequirement;
          return file;
        });

        const jabatanData = jabatanMap.get(item.personel.id);
        item.personel['foto_file'] = fotoMap.get(item.personel.nrp);
        item.personel['jabatan'] = jabatanData.jabatan;
        item.personel['satuan'] = jabatanData.satuan;
        item.personel['pangkat'] = pangkatMap.get(item.personel.id);
        item['status_participant'] = this.determineParticipantStatus(
          stage,
          item.file_uploads,
          this.parseHistoryStages(item.history_stages),
        );
      }),
    );
  }

  protected async getAddionalDataList(data: Array<any>) {
    const personelFotoUrls = [];
    const personelIds = [];
    const fileIds = [];
    const requirementFileIds = [];
    data.map((item) => {
      for (const file of item.file_uploads as any) {
        fileIds.push(file.file_id);
        requirementFileIds.push(file.requirement_file_id);
      }

      personelIds.push(item.personel.id);
      if (item.personel.foto_file) {
        personelFotoUrls.push({
          foto_file: item.personel.foto_file,
          nrp: item.personel.nrp,
        });
      }
    });

    const promises: Array<any> = [
      this.prisma.selection_file.findMany({
        where: {
          id: { in: fileIds.flat() },
        },
        select: {
          id: true,
          file_name: true,
        },
      }),
      this.prisma.selection_bagassus_file_requirement.findMany({
        where: {
          id: { in: requirementFileIds.flat() },
        },
        select: {
          id: true,
          title: true,
        },
      }),
    ];

    if (personelIds.length) {
      promises.push(
        ...[
          this.prisma.$queryRaw(Prisma.sql`
            SELECT pangkat_id, pangkat, personel_id
            FROM mv_pangkat_terakhir
            WHERE personel_id IN (${Prisma.join(personelIds)})
            ORDER BY personel_id, tmt DESC
          `),
          this.prisma.$queryRaw(Prisma.sql`
            SELECT mvjbt.jabatan_id, mvjbt.jabatan, mvjbt.satuan_id, mvjbt.personel_id, s.nama
            FROM mv_jabatan_terakhir mvjbt
            JOIN satuan s ON s.id = mvjbt.satuan_id
            WHERE personel_id IN (${Prisma.join(personelIds)})
            ORDER BY personel_id, tmt DESC
          `),
        ],
      );
    }

    const result = await this.prisma.$transaction(promises);
    const files = result[0];
    const fileRequirements = result[1];
    const latestPangkat = result?.[2];
    const latestJabatan = result?.[3];

    return {
      files,
      fileRequirements,
      latestPangkat,
      latestJabatan,
      personelFotoUrls,
    };
  }

  protected countStatusParticipant(data: Array<string>) {
    const status_count = {
      TOTAL: 0,
      NOT_CHECKED_YET: 0,
      CHECKED: 0,
      REJECTED: 0,
      REVISION: 0,
    };

    data.map((item) => {
      const statusEnum = Object.entries(
        PromosiJabatanParticipantStatusEnum,
      ).find(([_, val]) => {
        return val === item;
      });
      status_count[statusEnum?.[0]]++;
      status_count.TOTAL++;
    });

    return status_count;
  }

  protected async proceedExcel(params: IProceedExcel) {
    const { file, callback, req } = params;

    const tmpPath = 'tmp-seleksi-' + Date.now() + file.originalname;

    try {
      fs.writeFileSync(tmpPath, file.buffer);
      const json = await this.excelService.extractToJson(req, tmpPath, false);
      const header = await this.excelService.getHeader(tmpPath);

      return await callback({ data: json?.data || [], header });
    } catch (err) {
      throw err;
    } finally {
      fs.unlinkSync(tmpPath);
    }
  }
}
