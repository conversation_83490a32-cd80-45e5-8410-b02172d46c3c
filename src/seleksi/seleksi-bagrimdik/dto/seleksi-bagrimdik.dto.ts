import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';
import { JenisSeleksiEnum } from '../../../core/enums/seleksi-bagrimdik.enum';

class SeleksiTahapDto {
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'startDate harus dalam format YYYY-MM-DD',
  }) // Validasi format tanggal YYYY-MM-DD
  startDate: string;

  @IsString() // Memastikan nilai adalah string
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'endDate harus dalam format YYYY-MM-DD',
  }) // Validasi format tanggal YYYY-MM-DD
  endDate: string;

  @IsNumber()
  @IsNotEmpty()
  tahap: number;
}

export class CreateSeleksiBagrimdikDto {
  @IsNotEmpty({ message: 'Judul seleksi tidak boleh kosong' })
  @IsString()
  judul: string;

  @IsNotEmpty({ message: 'Deskripsi seleksi tidak boleh kosong' })
  @IsString()
  deskripsi: string;

  @IsNotEmpty({ message: 'Jenis seleksi tidak boleh kosong' })
  @IsEnum(JenisSeleksiEnum, { message: 'Jenis seleksi tidak valid' })
  jenis: JenisSeleksiEnum;

  @IsNotEmpty({ message: 'Tanggal mulai seleksi tidak boleh kosong' })
  @IsDateString()
  startdate: string;

  @IsNotEmpty({ message: 'Tanggal berakhir seleksi tidak boleh kosong' })
  @IsDateString()
  enddate: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SeleksiTahapDto)
  seleksiTahap: SeleksiTahapDto[];

  @IsOptional()
  persyaratan: Record<string, any>[];
}
