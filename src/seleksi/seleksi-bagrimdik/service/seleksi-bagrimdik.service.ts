import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateSeleksiBagrimdikDto } from '../dto/seleksi-bagrimdik.dto';
import {
  Prisma,
  status_dokumen_seleksi_bagrimdik_pns_enum,
} from '@prisma/client';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class SeleksiBagrimdikService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreateSeleksiBagrimdikDto, files: any) {
    try {
      const { judul, deskripsi, jenis, startdate, enddate } = body;

      // Parsing seleksiTahap
      const seleksiTahap = [];
      let i = 0;
      while (body[`seleksiTahap[${i}].startdate`]) {
        seleksiTahap.push({
          startdate: new Date(body[`seleksiTahap[${i}].startdate`].trim()),
          enddate: new Date(body[`seleksiTahap[${i}].enddate`].trim()),
          tahap: body[`seleksiTahap[${i}].tahap`].trim(),
        });
        i++;
      }

      const persyaratan = [];
      i = 0;
      while (body[`persyaratan[${i}].id`]) {
        persyaratan.push({
          id: body[`persyaratan[${i}].id`].trim(),
          value: body[`persyaratan[${i}].value`].trim(),
          prioritas: body[`persyaratan[${i}].prioritas`] === 'true',
        });
        i++;
      }

      const upload: Record<string, any> = {};
      if (files.logo) {
        const uploadLogo = await this.minioService.uploadFile(files.logo[0]);
        upload.logo = uploadLogo.Location;
      }

      if (files.dokumen) {
        const uploadDokumen = await this.minioService.uploadFile(
          files.dokumen[0],
        );
        upload.dokumen = uploadDokumen.Location;
      }

      const seleksiData = {
        judul,
        deskripsi,
        jenis,
        startdate: new Date(startdate),
        enddate: new Date(enddate),
        tahap: 1,
        logo_file: upload?.logo || null,
        dokumen_file: upload?.dokumen || null,
        tahapan_seleksi: seleksiTahap.length,
        persyaratan_list: persyaratan as Prisma.JsonArray,
      };

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const createSeleksi = await tx.seleksi_bagrimdik.create({
          data: {
            ...seleksiData,
            seleksi_bagrimdik_tahap: {
              createMany: {
                data: seleksiTahap.map((item) => {
                  return {
                    startdate: item.startdate,
                    enddate: item.enddate,
                    tahap: BigInt(item.tahap),
                  };
                }),
              },
            },
          },
        });

        await tx.seleksi_bagrimdik_syarat.createMany({
          data: persyaratan.map((item) => {
            return {
              seleksi_bagrimdik_id: BigInt(createSeleksi.id),
              seleksi_persyaratan_id: BigInt(item.id),
              min_value: item.value,
              prioritas: item.prioritas.toString().toLowerCase() === 'true',
            };
          }),
        });

        return createSeleksi;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        seleksi: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getList(
    req,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'date' | 'boolean';
      };
    } = {
      judul: { field: 'title', type: 'string' },
      deskripsi: { field: 'description', type: 'string' },
      tanggal: { field: 'publish_at', type: 'date' },
      status: { field: 'is_publish', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.seleksi_bagrimdik.count({
        where: where,
      }),
      this.prisma.seleksi_bagrimdik.findMany({
        select: {
          id: true,
          judul: true,
          deskripsi: true,
          jenis: true,
          startdate: true,
          enddate: true,
          created_at: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async registerSeleksi(req: any, seleksi_id: number, files: any[]) {
    try {
      const unixTimestamp = Math.floor(Date.now() / 1000);
      const nrpPersonel = req['nrp'];
      const idPersonel = req['personel_id'];
      const seleksi = await this.prisma.seleksi_bagrimdik.findFirst({
        where: { id: BigInt(seleksi_id) },
        select: {
          id: true,
          seleksi_bagrimdik_tahap: {
            select: {
              id: true,
              startdate: true,
              enddate: true,
              tahap: true,
            },
            orderBy: { tahap: 'asc' },
          },
          seleksi_bagrimdik_syarat: {
            select: {
              id: true,
              min_value: true,
              seleksi_persyaratan: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
            where: {
              deleted_at: null,
              seleksi_persyaratan: {
                type: 'file',
              },
            },
          },
        },
      });

      if (seleksi.seleksi_bagrimdik_syarat.length) {
        const isValidFile = seleksi.seleksi_bagrimdik_syarat.every((syarat) => {
          if (syarat.seleksi_persyaratan.type === 'file') {
            const indexFile = files.findIndex(
              (item) => item.fieldname == syarat.min_value,
            );
            if (indexFile >= 0) {
              return true;
            } else {
              return false;
            }
          }
          return true;
        });

        if (!isValidFile)
          throw new BadRequestException('Invalid upload document!');

        const distinctFiles = new Set(files.map((file) => file.fieldname));
        if (distinctFiles.size != files.length)
          throw new BadRequestException('file cannot be duplicate');
      }

      const queryResult = await this.prisma.seleksi_bagrimdik_peserta.create({
        data: {
          personel_id: idPersonel,
          no_pendaftaran: unixTimestamp + nrpPersonel,
          tahap_id: seleksi.seleksi_bagrimdik_tahap[0].id,
          status_dokumen:
            status_dokumen_seleksi_bagrimdik_pns_enum.BELUM_DIPERIKSA,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async listPeserta(req: any, paginationData, seleksi_id: number) {
    try {
      const currentDate = new Date();
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const [listPeserta, totalData] = await this.prisma.$transaction([
        this.prisma.seleksi_bagrimdik_peserta.findMany({
          where: {
            seleksi_bagrimdik_tahap: {
              seleksi_bagrimdik_id: seleksi_id,
              startdate: { lte: currentDate },
              enddate: { gte: currentDate },
            },
          },
          select: {
            id: true,
            no_pendaftaran: true,
            no_ujian: true,
            status_dokumen: true,
            status: true,
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
        }),
        this.prisma.seleksi_bagrimdik_peserta.count({
          where: {
            seleksi_bagrimdik_tahap: {
              seleksi_bagrimdik_id: seleksi_id,
              startdate: { lte: currentDate },
              enddate: { gte: currentDate },
            },
          },
        }),
      ]);

      const queryResult = listPeserta.map((item) => ({
        no_pendaftaran: item.no_pendaftaran,
        no_ujian: item.no_ujian,
        nama_peserta: item.personel.nama_lengkap,
        nrp: item.personel.nrp,
        pangkat:
          item.personel.pangkat_personel[0]?.pangkat.nama_singkat || 'N/A',
        jabatan: item.personel.jabatan_personel[0]?.jabatans.nama || 'N/A',
        satuan:
          item.personel.jabatan_personel[0]?.jabatans.satuan.nama || 'N/A',
        status: item.status,
        status_dokumen: item.status_dokumen,
      }));

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
