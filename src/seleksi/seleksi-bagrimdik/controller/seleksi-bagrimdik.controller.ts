import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { SeleksiBagrimdikService } from '../service/seleksi-bagrimdik.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { CreateSeleksiBagrimdikDto } from '../dto/seleksi-bagrimdik.dto';
import { Module, Permission } from 'src/core/decorators';
import {
  AnyFilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';

@Controller('seleksi-bagrimdik')
@Module(MODULES.SELECTION_MODULE)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class SeleksiBagrimdikController {
  private readonly logger = new Logger(SeleksiBagrimdikController.name);

  constructor(
    private readonly seleksiBagrimdikService: SeleksiBagrimdikService,
  ) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'dokumen', maxCount: 1 },
    ]),
  )
  async create(
    @Req() req,
    @Body() body: CreateSeleksiBagrimdikDto,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    const response = await this.seleksiBagrimdikService.create(
      req,
      body,
      files,
    );
    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    const response = await this.seleksiBagrimdikService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    return response;
  }

  @Post('/register')
  @Permission('PERMISSION_READ')
  @UseInterceptors(AnyFilesInterceptor())
  async registerSeleksi(
    @Req() req: any,
    @Body('seleksi_id') seleksi_id: number,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] }[],
  ) {
    const response = await this.seleksiBagrimdikService.registerSeleksi(
      req,
      seleksi_id,
      files,
    );

    return response;
  }

  @Get('/peserta')
  @Permission('PERMISSION_READ')
  async getPesertaList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query('seleksi_id') seleksi_id: number,
  ) {
    const response = await this.seleksiBagrimdikService.listPeserta(
      req,
      paginationData,
      seleksi_id,
    );

    return response;
  }
}
