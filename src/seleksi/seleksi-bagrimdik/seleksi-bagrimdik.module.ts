import { forwardRef, Module } from '@nestjs/common';
import { SeleksiBagrimdikService } from './service/seleksi-bagrimdik.service';
import { SeleksiBagrimdikController } from './controller/seleksi-bagrimdik.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SeleksiBagrimdikController],
  providers: [SeleksiBagrimdikService, MinioService],
})
export class SeleksiBagrimdikModule {}
