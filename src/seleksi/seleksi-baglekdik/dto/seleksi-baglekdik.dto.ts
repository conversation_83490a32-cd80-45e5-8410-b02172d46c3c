import { seleksi_baglekdik_type_enum } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';

class SelectionStageDto {
  @IsString()
  @IsNotEmpty()
  @IsDateString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'startDate harus dalam format YYYY-MM-DD',
  })
  start_date: string;

  @IsString()
  @IsNotEmpty()
  @IsDateString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'endDate harus dalam format YYYY-MM-DD',
  })
  end_date: string;
}

class SelectionRequirementDto {
  @IsNotEmpty({ message: 'ID tidak boleh kosong.' })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'ID harus berupa angka.' })
  id: number;

  @IsOptional()
  @IsString({ message: 'Title harus berupa string.' })
  title?: string;

  @IsOptional()
  @IsString({ message: 'File extension harus berupa string.' })
  file_extension?: string;

  @IsOptional()
  @IsString({ message: 'Value harus berupa string.' })
  value?: string;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'Min value harus berupa angka.' })
  min_value?: number;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'Max value harus berupa angka.' })
  max_value?: number;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean({ message: 'Is required harus berupa boolean (true/false).' })
  is_required?: boolean;
}

export class CreateSelectionBaglekdikDto {
  @IsNotEmpty({ message: 'Judul seleksi tidak boleh kosong' })
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty({ message: 'Jenis seleksi tidak boleh kosong' })
  @IsEnum(seleksi_baglekdik_type_enum, { message: 'Jenis seleksi tidak valid' })
  type: seleksi_baglekdik_type_enum;

  @IsNotEmpty({ message: 'Tahap seleksi tidak boleh kosong' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SelectionStageDto)
  stages: SelectionStageDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SelectionRequirementDto)
  requirements?: SelectionRequirementDto[];
}

export class RegisterSelectionBaglekdikDto {
  @IsNotEmpty({ message: 'ID seleksi tidak boleh kosong' })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'ID seleksi harus berupa angka' })
  selection_id: number;

  @IsOptional()
  @IsArray({ message: 'Requirement IDs harus berupa array' })
  @IsNumber(
    {},
    { each: true, message: 'Semua Requirement IDs harus berupa angka' },
  )
  @ArrayNotEmpty({
    message: 'Requirement IDs tidak boleh kosong jika diberikan',
  })
  requirement_ids: number[];
}

export class FileUploadsSelectionBaglekdikDto {
  @IsNotEmpty({ message: 'ID requirement file tidak boleh kosong' })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'ID requirement file harus berupa angka' })
  id: number;

  @IsNotEmpty({ message: 'Status file upload tidak boleh kosong' })
  @IsString()
  status: string;

  @IsOptional()
  @IsString()
  reason?: string;
}

export class ParticipantSelectionBaglekdikDto {
  @IsNotEmpty({ message: 'ID peserta tidak boleh kosong' })
  @Transform(({ value }) => Number(value))
  @IsNumber({}, { message: 'ID peserta harus berupa angka' })
  id: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FileUploadsSelectionBaglekdikDto)
  file_uploads: FileUploadsSelectionBaglekdikDto[];
}

export class SubmitParticipantSelectionBaglekdikDto {
  @ArrayNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ParticipantSelectionBaglekdikDto)
  participants: ParticipantSelectionBaglekdikDto[];
}
