import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SeleksiBaglekdikService } from '../service/seleksi-baglekdik.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import {
  CreateSelectionBaglekdikDto,
  RegisterSelectionBaglekdikDto,
  SubmitParticipantSelectionBaglekdikDto,
} from '../dto/seleksi-baglekdik.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { Module, Permission } from '../../../core/decorators';
import { MODULES } from '../../../core/constants/module.constant';

@Controller('seleksi-baglekdik')
@UseGuards(JwtAuthGuard)
export class SeleksiBaglekdikController {
  private readonly logger = new Logger(SeleksiBaglekdikController.name);

  constructor(
    private readonly seleksiBaglekdikService: SeleksiBaglekdikService,
  ) {}

  // ==================================================================================================================================== //
  //                                                          EVERYBODY                                                                   //
  // ==================================================================================================================================== //

  @Get('/selections')
  @HttpCode(200)
  async getAllSelections(@Req() req: any) {
    this.logger.log(`Entering ${this.getAllSelections.name}`);
    const response = await this.seleksiBaglekdikService.getAllSelections(req);
    this.logger.log(
      `Leaving ${this.getAllSelections.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/requirements')
  @HttpCode(200)
  async getAllRequirements(@Req() req: any) {
    this.logger.log(`Entering ${this.getAllRequirements.name}`);
    const response = await this.seleksiBaglekdikService.getAllRequirements(req);
    this.logger.log(
      `Leaving ${this.getAllRequirements.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  // ==================================================================================================================================== //
  //                                                        OPERATOR / ADMIN                                                              //
  // ==================================================================================================================================== //

  @Post('/selections')
  @Module(MODULES.SELECTION_MODULE)
  @Permission('PERMISSION_READ')
  @UseGuards(PermissionGuard)
  @HttpCode(201)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'document', maxCount: 1 },
    ]),
  )
  async createSelection(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateSelectionBaglekdikDto,
    @UploadedFiles() files: { [key: string]: Express.Multer.File },
  ) {
    this.logger.log(
      `Entering ${this.createSelection.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.seleksiBaglekdikService.createSelection(
      req,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.createSelection.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/selections/:id')
  @Module(MODULES.SELECTION_MODULE)
  @Permission('PERMISSION_READ')
  @UseGuards(PermissionGuard)
  @HttpCode(200)
  async getSelectionDetails(
    @Req() req: any,
    @Param('id') idSelection: string,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getSelectionDetails.name} with id: ${idSelection} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.seleksiBaglekdikService.getSelectionDetails(
      req,
      BigInt(idSelection),
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getSelectionDetails.name} with id: ${idSelection} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('/selections/:id/close')
  @Module(MODULES.SELECTION_MODULE)
  @Permission('PERMISSION_CREATE')
  @UseGuards(PermissionGuard)
  @HttpCode(200)
  async closeSelectionRegistration(
    @Req() req: any,
    @Param('id') idSelection: string,
  ) {
    this.logger.log(
      `Entering ${this.closeSelectionRegistration.name} with id: ${idSelection}`,
    );
    const response =
      await this.seleksiBaglekdikService.closeSelectionRegistration(
        req,
        BigInt(idSelection),
      );
    this.logger.log(
      `Leaving ${this.closeSelectionRegistration.name} with id: ${idSelection} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/selections/:id')
  @Module(MODULES.SELECTION_MODULE)
  @Permission('PERMISSION_DELETE')
  @UseGuards(PermissionGuard)
  @HttpCode(200)
  async deleteSelection(@Req() req: any, @Param('id') idSelection: string) {
    this.logger.log(
      `Entering ${this.deleteSelection.name} with id: ${idSelection}`,
    );
    const response = await this.seleksiBaglekdikService.deleteSelection(
      req,
      BigInt(idSelection),
    );
    this.logger.log(
      `Leaving ${this.deleteSelection.name} with id: ${idSelection} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/selections/submit-participants')
  @Module(MODULES.SELECTION_MODULE)
  @Permission('PERMISSION_CREATE')
  @UseGuards(PermissionGuard)
  @HttpCode(200)
  @UsePipes(new ValidationPipe({ transform: true }))
  async submitSelectedParticipants(
    @Req() req: any,
    @Body() body: SubmitParticipantSelectionBaglekdikDto,
  ) {
    this.logger.log(
      `Entering ${this.submitSelectedParticipants.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.seleksiBaglekdikService.submitSelectedParticipants(req, body);
    this.logger.log(
      `Leaving ${this.submitSelectedParticipants.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  // ==================================================================================================================================== //
  //                                                        PERSONEL                                                                      //
  // ==================================================================================================================================== //

  @Get('/registrations')
  @HttpCode(200)
  async getPersonelSelections(@Req() req: any) {
    this.logger.log(`Entering ${this.getPersonelSelections.name}`);
    const response =
      await this.seleksiBaglekdikService.getPersonelSelections(req);
    this.logger.log(
      `Leaving ${this.getPersonelSelections.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/registrations/:id')
  @HttpCode(200)
  async getPersonelSelectionDetail(
    @Req() req: any,
    @Param('id') idSelection: string,
  ) {
    this.logger.log(
      `Entering ${this.getPersonelSelectionDetail.name} with id: ${idSelection}`,
    );
    const response =
      await this.seleksiBaglekdikService.getPersonelSelectionDetail(
        req,
        BigInt(idSelection),
      );
    this.logger.log(
      `Leaving ${this.getPersonelSelectionDetail.name} with id: ${idSelection} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/selections/:id/eligibility')
  @HttpCode(200)
  async checkSelectionEligibility(
    @Req() req: any,
    @Param('id') idSelection: string,
  ) {
    this.logger.log(
      `Entering ${this.checkSelectionEligibility.name} with id: ${idSelection}`,
    );
    const response =
      await this.seleksiBaglekdikService.checkSelectionEligibility(
        req,
        BigInt(idSelection),
      );
    this.logger.log(
      `Leaving ${this.checkSelectionEligibility.name} with id: ${idSelection} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/registrations')
  @HttpCode(200)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file_uploads' }]))
  async registerForSelection(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: RegisterSelectionBaglekdikDto,
    @UploadedFiles() files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.registerForSelection.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.seleksiBaglekdikService.registerForSelection(
      req,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.registerForSelection.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
