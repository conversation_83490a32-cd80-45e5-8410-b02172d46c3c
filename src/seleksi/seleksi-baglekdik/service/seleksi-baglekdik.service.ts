import * as path from 'path';
import * as moment from 'moment';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateSelectionBaglekdikDto,
  RegisterSelectionBaglekdikDto,
  SubmitParticipantSelectionBaglekdikDto,
} from '../dto/seleksi-baglekdik.dto';
import {
  Prisma,
  seleksi_baglekdik_participant_status_enum,
  seleksi_baglekdik_stage_status_enum,
} from '@prisma/client';
import {
  IBaglekdikSelections,
  IBaglekdikSelectionsParticipantFiles,
  IParticipantResult,
  IParticipantSelectionDetail,
  IResponseDetailSelection,
} from '../../../core/interfaces/seleksi-baglekdik.interface';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import {
  makeRegistrationNumber,
  parseNumber,
} from '../../../core/utils/common.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class SeleksiBaglekdikService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getAllSelections(req: any): Promise<any> {
    const user = req.user;

    const dateNow = moment().startOf('day');
    try {
      const selections =
        await this.prisma.seleksi_baglekdik_selections.findMany({
          where: { deleted_at: null },
          select: {
            id: true,
            title: true,
            type: true,
            seleksi_baglekdik_files_logo: { select: { url: true } },
            description: true,
            seleksi_baglekdik_selection_requirement: {
              select: {
                id: true,
                requirement_id: true,
                is_required: true,
                title: true,
                file_extension: true,
                value: true,
                min_value: true,
                max_value: true,
                seleksi_baglekdik_requirements: { select: { name: true } },
              },
            },
            seleksi_baglekdik_selection_stages: {
              select: {
                stage: true,
                start_date: true,
                end_date: true,
                status: true,
              },
            },
          },
        });

      let personel: IParticipantSelectionDetail | null = null;
      if (!user.role_id) {
        personel = await this._detailPersonel(BigInt(user.personel_id));
      }

      const queryResult: IBaglekdikSelections[] = selections.reduce(
        (acc, selection) => {
          const result: IBaglekdikSelections = {
            id: selection.id,
            title: selection.title,
            description: selection.description,
            type: selection.type,
            logo: selection.seleksi_baglekdik_files_logo
              ? selection.seleksi_baglekdik_files_logo.url
              : '',
            isRegisterOpen: false,
            isQualified: false,
          } as IBaglekdikSelections;

          if (!user.role_id) {
            result.isQualified = this._checkRequirementParticipant(
              selection.seleksi_baglekdik_selection_requirement,
              personel,
            );
            if (personel && personel.selectionId.length > 0) {
              const indexSelection = personel.selectionId.findIndex(
                (val) => val.id === selection.id,
              );
              if (indexSelection !== -1) return acc;
            }
          }

          for (const [
            index,
            stage,
          ] of selection.seleksi_baglekdik_selection_stages.entries()) {
            const isWithinDateRange =
              dateNow.isSameOrAfter(moment(stage.start_date).startOf('day')) &&
              dateNow.isSameOrBefore(moment(stage.end_date).startOf('day'));

            if (
              stage.stage === 0 &&
              isWithinDateRange &&
              stage.status === seleksi_baglekdik_stage_status_enum.DIBUKA &&
              result.isQualified
            )
              result.isRegisterOpen = true;

            if (
              isWithinDateRange ||
              (index ===
                selection.seleksi_baglekdik_selection_stages.length - 1 &&
                !result.status)
            ) {
              if (result.isRegisterOpen && result.stageName === 'Pendaftaran')
                break;

              result.startDate = stage.start_date;
              result.endDate = stage.end_date;
              result.stageName =
                stage.stage === 0
                  ? 'Pendaftaran'
                  : `Seleksi Tahap ${stage.stage}`;
              result.status =
                stage.stage !== 0
                  ? `Seleksi tahap ${stage.stage}`
                  : stage.status.charAt(0).toUpperCase() +
                    stage.status.slice(1).toLowerCase();
            }
          }

          acc.push(result);
          return acc;
        },
        [] as IBaglekdikSelections[],
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error findSelections => ', error);
      throw error;
    }
  }

  async getAllRequirements(req: any) {
    try {
      const queryResult =
        await this.prisma.seleksi_baglekdik_requirements.findMany({
          select: {
            id: true,
            name: true,
            input_type: true,
          },
          orderBy: { id: 'asc' },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error findSelections => ', error);
      throw error;
    }
  }

  async createSelection(
    req: any,
    body: CreateSelectionBaglekdikDto,
    fileMap: { [key: string]: Express.Multer.File },
  ): Promise<any> {
    const { user } = req;

    try {
      let logo: Prisma.seleksi_baglekdik_filesCreateInput | null = null;
      let document: Prisma.seleksi_baglekdik_filesCreateInput | null = null;

      // Process all files
      for (const [field, files] of Object.entries(fileMap)) {
        const fileList = Array.isArray(files) ? files : [files];
        for (const file of fileList) {
          // Only process the first file for 'logo' or 'document'
          if (field === 'logo' && !logo) {
            logo = await this._uploadFileSelection(field, file);
            break;
          }

          if (field === 'document' && !document) {
            document = await this._uploadFileSelection(field, file);
            break;
          }
        }
      }

      const { title, description, type, stages, requirements } = body;

      const queryResult = await this.prisma.$transaction(async (tx) => {
        // Insert Logo and Get ID
        let logoId: bigint | null = null;
        if (logo) {
          const insertLogo = await tx.seleksi_baglekdik_files.create({
            data: logo,
          });
          logoId = insertLogo.id;
        }

        // Insert Document and Get ID
        let documentId: bigint | null = null;
        if (document) {
          const insertDocument = await tx.seleksi_baglekdik_files.create({
            data: document,
          });
          documentId = insertDocument.id;
        }

        // Insert Selection
        const insertSelection = await tx.seleksi_baglekdik_selections.create({
          data: {
            title,
            description,
            type,
            selection_stages: stages.length,
            logo_file_id: logoId,
            document_file_id: documentId,
            created_by: user.id,
          },
        });

        // Insert Stages
        await tx.seleksi_baglekdik_selection_stages.createMany({
          data: stages.map((item, index, row) => {
            if (!item && index + 1 === row.length)
              throw new BadRequestException('Tahapan seleksi tidak lengkap');

            return {
              selection_id: insertSelection.id,
              stage: index,
              start_date: new Date(item.start_date),
              end_date: new Date(item.end_date),
              status: seleksi_baglekdik_stage_status_enum.DIBUKA,
              created_by: user.id,
            };
          }),
        });

        // Insert Requirements
        if (requirements && requirements.length > 0) {
          await tx.seleksi_baglekdik_selection_requirement.createMany({
            data: requirements.map((item) => {
              return {
                selection_id: insertSelection.id,
                requirement_id: BigInt(item.id),
                title: item.title ?? null,
                file_extension: item.file_extension ?? null,
                is_required: item.is_required,
                value: item.value ? item.value.toString() : null,
                min_value: item.min_value ?? null,
                max_value: item.max_value ?? null,
              };
            }),
          });
        }

        return insertSelection;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error createSelections => ', error);
      throw error;
    }
  }

  async getSelectionDetails(
    req: any,
    idSelection: bigint,
    paginationData,
    searchandsortData,
  ) {
    const dateNow = moment().startOf('day');
    try {
      const limit = paginationData.limit || 8;
      const page = paginationData.page || 1;
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const selectionStages =
        await this.prisma.seleksi_baglekdik_selection_stages.findMany({
          where: { id: idSelection },
        });

      if (!selectionStages || selectionStages.length <= 0)
        throw new NotFoundException('Seleksi tidak ditemukan');

      let activeStageId: bigint | null = null;
      let numberStageParticipant: bigint | null = null;
      for (const [index, stage] of selectionStages.entries()) {
        const isWithinDateRange =
          dateNow.isSameOrAfter(moment(stage.start_date).startOf('day')) &&
          dateNow.isSameOrBefore(moment(stage.end_date).startOf('day'));

        if (isWithinDateRange) {
          activeStageId = stage.id;
          numberStageParticipant = stage.id;

          if (stage.stage != 0) {
            const findIdStageBefore = selectionStages.find(
              (item) => item.stage === stage.stage - 1,
            );
            numberStageParticipant = findIdStageBefore.id;
          }
        }

        if (!activeStageId && index === selectionStages.length - 1) {
          activeStageId = stage.id;
          numberStageParticipant = stage.id;
        }
      }

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'date' | 'number' };
      } = {
        status: {
          field: 'status',
          type: 'string',
        },
        registrationNumber: {
          field: 'registrationNumber',
          type: 'string',
        },
        participantNrp: {
          field: '"participantNrp"',
          type: 'string',
        },
        participantName: {
          field: '"participantName"',
          type: 'string',
        },
        participantRank: {
          field: '"participantRank"',
          type: 'string',
        },
        participantUnit: {
          field: '"participantUnit"',
          type: 'string',
        },
        participantPosition: {
          field: '"participantPosition"',
          type: 'string',
        },
      };

      const { orderBy } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const orderClause = Prisma.sql`ORDER BY ${Prisma.raw(
        orderBy
          .map((obj) =>
            Object.entries(obj)
              .map(([key, value]) => `${key} ${value.toUpperCase()}`)
              .join(', '),
          )
          .join(', '),
      )}`;

      const rawQuery = Prisma.sql`
          SELECT sp.id,
                 sp.registration_number AS "registrationNumber",
                 sp.exam_number         AS "examNumber",
                 sp.file_uploads        AS "fileUploads",
                 sp.status,
                 p.nama_lengkap         AS "participantName",
                 p.nrp                  AS "participantNrp",
                 (SELECT p2.nama_singkat
                  FROM pangkat_personel pp
                           JOIN pangkat p2 ON p2.id = pp.pangkat_id
                  WHERE pp.personel_id = p.id
                  ORDER BY pp.tmt DESC
                                           LIMIT 1)              AS "participantRank",
                       (SELECT j.nama
                        FROM jabatan_personel jp
                                 JOIN jabatan j ON j.id = jp.jabatan_id
                        WHERE jp.personel_id = p.id
                        ORDER BY jp.tmt_jabatan DESC
                        LIMIT 1)              AS "participantPosition",
                       (SELECT s.nama
                        FROM jabatan_personel jp
                                 JOIN jabatan j ON j.id = jp.jabatan_id
                                 JOIN satuan s ON j.satuan_id = s.id
                        WHERE jp.personel_id = p.id
                        ORDER BY jp.tmt_jabatan DESC
                        LIMIT 1)              AS "participantUnit"
          FROM seleksi_baglekdik_selection_participants sp
              LEFT JOIN personel p
          ON p.id = sp.personel_id
          WHERE sp.stage_id = ${numberStageParticipant} ${orderClause}
              LIMIT ${limit}
          OFFSET ${limit * (page - 1)}
      `;

      const [totalData, participants] = await Promise.all([
        this.prisma.seleksi_baglekdik_selection_participants.count({
          where: { stage_id: numberStageParticipant },
        }),
        this.prisma.$queryRaw<IParticipantResult[]>(rawQuery),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const result: IResponseDetailSelection[] = await Promise.all(
        participants.map(async (item) => {
          const { fileUploads, ...restItem } = item;

          const files = await Promise.all(
            fileUploads.map(async (file) => {
              const { fileId, ...restFile } = file;
              const urlFile =
                await this.prisma.seleksi_baglekdik_files.findFirst({
                  where: { id: fileId },
                  select: { url: true },
                });

              return {
                ...restFile,
                urlFile: urlFile?.url || null,
              };
            }),
          );

          return {
            ...restItem,
            files,
          };
        }),
      );

      const queryResult = {
        participants: result,
        page,
        totalPage,
        totalData,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error detailSelection => ', error);
      throw error;
    }
  }

  async closeSelectionRegistration(req: any, idSelection: bigint) {
    const { user } = req;
    try {
      const selectionStage =
        await this.prisma.seleksi_baglekdik_selections.findFirst({
          where: { id: idSelection },
          select: {
            seleksi_baglekdik_selection_stages: {
              where: { stage: 0 },
              select: { id: true },
              take: 1,
            },
          },
        });

      if (!selectionStage)
        throw new NotFoundException('Seleksi tidak ditemukan');

      const queryResult =
        await this.prisma.seleksi_baglekdik_selection_stages.update({
          where: {
            id: selectionStage.seleksi_baglekdik_selection_stages[0].id,
          },
          data: {
            status: seleksi_baglekdik_stage_status_enum.DITUTUP,
            updated_at: new Date(),
            updated_by: user.id,
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error closeSelectionStage => ', error);
      throw error;
    }
  }

  async deleteSelection(req: any, idSelection: bigint) {
    const { user } = req;

    try {
      const queryResult = await this.prisma.seleksi_baglekdik_selections.update(
        {
          where: { id: idSelection },
          data: {
            deleted_at: new Date(),
            deleted_by: user.id,
          },
        },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error deleteSelection => ', error);
      throw error;
    }
  }

  async submitSelectedParticipants(
    req: any,
    body: SubmitParticipantSelectionBaglekdikDto,
  ) {
    const { user } = req;
    const { participants } = body;

    try {
      const participantUpdates = await Promise.all(
        participants.map(async (participant) => {
          const { id, file_uploads } = participant;

          // Default status participant
          let statusParticipant: seleksi_baglekdik_participant_status_enum =
            seleksi_baglekdik_participant_status_enum.SUDAH_DIPERIKSA;

          // Fetch file uploads for the participant
          const fileUploadsParticipant =
            await this.prisma.seleksi_baglekdik_selection_participants.findFirst(
              {
                where: { id },
                select: { file_uploads: true },
              },
            );

          if (!fileUploadsParticipant || !fileUploadsParticipant.file_uploads) {
            return {
              id,
              itemUpdate: [],
              status: seleksi_baglekdik_participant_status_enum.BELUM_DIPERIKSA,
            };
          }

          const fileParticipant: IBaglekdikSelectionsParticipantFiles[] =
            JSON.parse(JSON.stringify(fileUploadsParticipant.file_uploads));

          const itemUpdates = fileParticipant.map((file) => {
            const matchingFile = file_uploads.find(
              (val) => val.id === Number(file.id),
            );

            const statusFile = matchingFile?.status ?? file.status;
            const reasonFile = matchingFile?.reason ?? file.reason;

            // Update participant status based on file status
            if (
              statusFile === seleksi_baglekdik_participant_status_enum.DITOLAK
            ) {
              statusParticipant =
                seleksi_baglekdik_participant_status_enum.DITOLAK;
            } else if (
              statusFile === seleksi_baglekdik_participant_status_enum.REVISI
            ) {
              statusParticipant =
                seleksi_baglekdik_participant_status_enum.REVISI;
            } else if (!matchingFile) {
              statusParticipant =
                seleksi_baglekdik_participant_status_enum.BELUM_DIPERIKSA;
            }

            return {
              id: file.id,
              fileId: file.fileId,
              status: statusFile,
              reason: reasonFile,
            };
          });

          return {
            id,
            itemUpdate: itemUpdates,
            status: statusParticipant,
          };
        }),
      );

      const queryResult = await this.prisma.$transaction(async (tx) => {
        return await Promise.all(
          participantUpdates.map(async (updateItem) => {
            return tx.seleksi_baglekdik_selection_participants.update({
              where: { id: updateItem.id },
              data: {
                file_uploads: JSON.parse(JSON.stringify(updateItem.itemUpdate)),
                status: updateItem.status,
                updated_by: user.id,
              },
            });
          }),
        );
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error detailSelectionPersonel => ', error);
      throw error;
    }
  }

  async getPersonelSelections(req: any): Promise<any> {
    const { user } = req;
    try {
      const personel: IParticipantSelectionDetail = await this._detailPersonel(
        BigInt(user.personel_id),
      );

      const idSelections = [];
      personel.selectionId.forEach((item) => {
        const idxIdSelections = idSelections.findIndex(
          (val) => val.selectionId === item.id,
        );
        if (idxIdSelections !== -1) {
          idSelections[idxIdSelections].lastStage = item.stage_id;
        } else {
          idSelections.push({
            id: item.id,
            lastStage: item.stage_id,
          });
        }
      });

      const selections =
        await this.prisma.seleksi_baglekdik_selections.findMany({
          where: {
            deleted_at: null,
            id: { in: idSelections.map((item) => item.id) },
          },
          select: {
            id: true,
            title: true,
            type: true,
            seleksi_baglekdik_files_logo: { select: { url: true } },
            description: true,
            seleksi_baglekdik_selection_requirement: {
              select: {
                id: true,
                requirement_id: true,
                is_required: true,
                title: true,
                file_extension: true,
                value: true,
                min_value: true,
                max_value: true,
                seleksi_baglekdik_requirements: { select: { name: true } },
              },
            },
            seleksi_baglekdik_selection_stages: {
              where: {
                id: { in: idSelections.map((item) => item.lastStage) },
              },
              select: {
                stage: true,
                start_date: true,
                end_date: true,
                status: true,
              },
              orderBy: { stage: 'desc' },
              take: 1,
            },
          },
        });

      const queryResult = selections.map((selection) => {
        const logo = selection.seleksi_baglekdik_files_logo?.url || '';
        const stageData = selection.seleksi_baglekdik_selection_stages?.[0];

        let stageName = '';
        let status = '';
        let startDate = null;
        let endDate = null;

        if (stageData) {
          stageName =
            stageData.stage === 0
              ? 'Pendaftaran'
              : `Seleksi Tahap ${stageData.stage}`;
          status =
            stageData.stage === 0
              ? 'Terdaftar'
              : `Seleksi tahap ${stageData.stage}`;
          startDate = stageData.start_date;
          endDate = stageData.end_date;
        }

        return {
          id: selection.id,
          title: selection.title,
          description: selection.description,
          type: selection.type,
          logo,
          stageName,
          status,
          startDate,
          endDate,
        } as IBaglekdikSelections;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error findSelections => ', error);
      throw error;
    }
  }

  async getPersonelSelectionDetail(req: any, idSelection: bigint) {
    const { user } = req;
    try {
      const personelDetail = await this._detailPersonel(user.personel_id);

      const selection =
        await this.prisma.seleksi_baglekdik_selections.findFirst({
          where: {
            id: idSelection,
            deleted_at: null,
          },
          select: {
            type: true,
            selection_stages: true,
            seleksi_baglekdik_selection_stages: {
              select: {
                stage: true,
                start_date: true,
                end_date: true,
                status: true,
                seleksi_baglekdik_selection_participants: {
                  select: {
                    status: true,
                    registration_number: true,
                    exam_number: true,
                  },
                },
              },
            },
          },
        });

      if (!selection) throw new NotFoundException('Seleksi tidak ditemukan');

      const stages = [];
      for (const stage of selection.seleksi_baglekdik_selection_stages) {
        if (
          stage.stage === 0 ||
          stage.seleksi_baglekdik_selection_participants.length <= 0
        )
          continue;

        stages.push({
          stage: stage.stage,
          startDate: stage.start_date,
          endDate: stage.end_date,
          status: stage.seleksi_baglekdik_selection_participants[0].status,
          registrationNumber:
            stage.seleksi_baglekdik_selection_participants[0]
              .registration_number,
          examNumber:
            stage.seleksi_baglekdik_selection_participants[0].exam_number,
        });
      }

      const queryResult = {
        fullName: personelDetail.fullName,
        photoFile: personelDetail.photoFile,
        nrp: personelDetail.nrp,
        age: personelDetail.age,
        rankId: personelDetail.rankId,
        rank: personelDetail.rank,
        physicalScore: personelDetail.physicalScore,
        spiritualScore: personelDetail.spiritualScore,
        mentalScore: personelDetail.mentalScore,
        educationLevel: personelDetail.educationLevel,
        mddp: personelDetail.mddp,
        selectionType: selection.type,
        stages: stages,
        stageCount: selection.selection_stages - 1,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error detailSelectionPersonel => ', error);
      throw error;
    }
  }

  async checkSelectionEligibility(req: any, idSelection: bigint) {
    const { user } = req;
    const dateNow = moment().startOf('day');
    try {
      const personelDetail = await this._detailPersonel(user.personel_id);

      const selection =
        await this.prisma.seleksi_baglekdik_selections.findFirst({
          where: {
            id: idSelection,
            deleted_at: null,
          },
          select: {
            seleksi_baglekdik_selection_requirement: {
              select: {
                id: true,
                is_required: true,
                title: true,
                file_extension: true,
                min_value: true,
                max_value: true,
                value: true,
                requirement_id: true,
                seleksi_baglekdik_requirements: true,
              },
            },
            seleksi_baglekdik_selection_stages: {
              where: { stage: 0 },
              select: {
                start_date: true,
                end_date: true,
                status: true,
              },
            },
          },
        });

      if (!selection) throw new NotFoundException('Seleksi tidak ditemukan');

      const isWithinDateRange =
        dateNow.isSameOrAfter(
          moment(
            selection.seleksi_baglekdik_selection_stages[0].start_date,
          ).startOf('day'),
        ) &&
        dateNow.isSameOrBefore(
          moment(
            selection.seleksi_baglekdik_selection_stages[0].end_date,
          ).startOf('day'),
        );

      if (
        !isWithinDateRange ||
        selection.seleksi_baglekdik_selection_stages[0].status !==
          seleksi_baglekdik_stage_status_enum.DIBUKA
      )
        personelDetail.isAllowRegister = false;
      if (user.role_id) personelDetail.isAllowRegister = false;

      if (
        selection.seleksi_baglekdik_selection_requirement &&
        selection.seleksi_baglekdik_selection_requirement.length > 0
      ) {
        personelDetail.isAllowRegister = this._checkRequirementParticipant(
          selection.seleksi_baglekdik_selection_requirement,
          personelDetail,
        );

        for (const requirement of selection.seleksi_baglekdik_selection_requirement) {
          if (requirement.requirement_id === BigInt(13)) {
            personelDetail.requirementFiles.push({
              id: requirement.id,
              isRequired: requirement.requirement_id,
              title: requirement.title,
              fileExtension: requirement.file_extension,
              minValue: requirement.min_value,
              maxValue: requirement.max_value,
              value: requirement.value,
            });
          }
        }
      }

      const { selectionId, ...queryResult } = personelDetail;

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error detailSelectionPersonel => ', error);
      throw error;
    }
  }

  async registerForSelection(
    req: any,
    body: RegisterSelectionBaglekdikDto,
    files: { [key: string]: Express.Multer.File[] },
  ) {
    const { user } = req;
    const dateNow = moment().startOf('day');
    try {
      const { selection_id, requirement_ids } = body;
      const fileUploadsLength = files.file_uploads
        ? files.file_uploads.length
        : 0;

      if (requirement_ids && requirement_ids.length !== fileUploadsLength)
        throw new BadRequestException('Jumlah file dan ID tidak sesuai');

      const selection =
        await this.prisma.seleksi_baglekdik_selections.findFirst({
          where: {
            id: selection_id,
            deleted_at: null,
          },
          select: {
            selection_stages: true,
            seleksi_baglekdik_selection_requirement: {
              where: { requirement_id: 13 },
              select: {
                id: true,
                title: true,
                is_required: true,
                file_extension: true,
                value: true,
                min_value: true,
                max_value: true,
              },
            },
            seleksi_baglekdik_selection_stages: {
              select: {
                id: true,
                start_date: true,
                end_date: true,
                status: true,
              },
            },
          },
        });

      if (!selection) throw new NotFoundException('Seleksi tidak ditemukan');

      const registrationEndDate = moment(
        selection.seleksi_baglekdik_selection_stages[0].end_date,
      ).startOf('day');
      const registrationStatus =
        selection.seleksi_baglekdik_selection_stages[0].status;

      if (
        dateNow.isAfter(registrationEndDate) ||
        registrationStatus == seleksi_baglekdik_stage_status_enum.DITUTUP
      )
        throw new ForbiddenException('Seleksi tahap pendaftaran sudah ditutup');

      const [userRegistered, lastNumberParticipant] = await Promise.all([
        this.prisma.seleksi_baglekdik_selection_participants.findFirst({
          where: {
            id: selection_id,
            personel_id: user.personel_id,
          },
        }),
        this.prisma.seleksi_baglekdik_selection_participants.count({
          where: { id: selection_id },
        }),
      ]);

      if (userRegistered) throw new ConflictException('Anda sudah terdaftar');

      const fileParticipant: IBaglekdikSelectionsParticipantFiles[] = [];
      const fileParticipantCreate: Prisma.seleksi_baglekdik_filesCreateInput[] =
        [];

      if (
        selection.seleksi_baglekdik_selection_requirement &&
        selection.seleksi_baglekdik_selection_requirement.length > 0
      ) {
        for (
          let x = 0;
          x < selection.seleksi_baglekdik_selection_requirement.length;
          x++
        ) {
          const requirement =
            selection.seleksi_baglekdik_selection_requirement[x];

          if (!requirement_ids)
            throw new BadRequestException('ID requirement tidak ditemukan');

          const findIndexIds = requirement_ids.findIndex(
            (item) => BigInt(item) === requirement.id,
          );
          if (findIndexIds == -1)
            throw new BadRequestException('ID requirement tidak ditemukan');

          const file = files.file_uploads[findIndexIds];

          if (
            path.extname(file.originalname).toLowerCase() !=
            requirement.file_extension
          )
            throw new BadRequestException(
              `File ${requirement.title} harus berformat ${requirement.file_extension}`,
            );

          if (requirement.value && file.size > Number(requirement.value))
            throw new BadRequestException(
              `Ukuran file ${requirement.title} tidak boleh kurang dari ${requirement.min_value} byte.`,
            );

          if (requirement.max_value && file.size > requirement.max_value)
            throw new BadRequestException(
              `Ukuran file ${requirement.title} tidak boleh lebih dari ${requirement.max_value} byte.`,
            );

          if (requirement.min_value && file.size < requirement.min_value)
            throw new BadRequestException(
              `Ukuran file ${requirement.title} tidak boleh kurang dari ${requirement.min_value} byte.`,
            );

          const uploadResult = await this.minioService.uploadFile(file);
          fileParticipantCreate.push({
            original_name: file.originalname,
            encoding: file.encoding,
            mime_type: file.mimetype,
            size: file.size,
            key: uploadResult.Key,
            url: uploadResult.Location,
            file_name: uploadResult.filename,
            path: file.path,
          });

          fileParticipant.push({
            id: requirement.id,
          } as IBaglekdikSelectionsParticipantFiles);
        }
      }

      const registrationNumber = makeRegistrationNumber(lastNumberParticipant);
      const queryResult = await this.prisma.$transaction(async (tx) => {
        for (const [index, file] of fileParticipantCreate.entries()) {
          const insertedFile = await tx.seleksi_baglekdik_files.create({
            data: file,
          });
          fileParticipant[index].fileId = insertedFile.id;
          fileParticipant[index].status =
            seleksi_baglekdik_participant_status_enum.BELUM_DIPERIKSA;
        }

        const insertParticipant =
          await tx.seleksi_baglekdik_selection_participants.create({
            data: {
              registration_number: registrationNumber,
              personel_id: user.personel_id as never,
              id: selection_id,
              stage_id: selection.seleksi_baglekdik_selection_stages[1]
                .id as never,
              status: seleksi_baglekdik_participant_status_enum.BELUM_DIPERIKSA,
              file_uploads: JSON.parse(JSON.stringify(fileParticipant)),
              created_by_users: user.id,
            },
          });

        return insertParticipant;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SELEKSI_BAGLEKDIK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log('error registerParticipantSelection => ', error);
      throw error;
    }
  }

  private async _uploadFileSelection(
    field: string,
    files: Express.Multer.File,
  ): Promise<Prisma.seleksi_baglekdik_filesCreateInput> {
    const allowedExtensions = ['.pdf', '.png', '.gif'];
    const MAX_LOGO_SIZE = 800 * 1024; // 800Kb

    try {
      const fileExtension = path.extname(files.originalname).toLowerCase();

      // Validate logo file size and extension
      if (field === 'logo') {
        if (files.size > MAX_LOGO_SIZE)
          throw new BadRequestException('Logo tidak boleh melebihi 800KB');

        if (!allowedExtensions.includes(fileExtension))
          throw new BadRequestException(
            `Logo harus dalam format PDF, PNG, atau GIF. Format ditemukan: ${fileExtension}`,
          );
      }

      const uploadResult = await this.minioService.uploadFile(files);
      return {
        original_name: files.originalname,
        encoding: files.encoding,
        mime_type: files.mimetype,
        size: files.size,
        key: uploadResult.Key,
        url: uploadResult.Location,
        file_name: uploadResult.filename,
        path: files.path,
      };
    } catch (error) {
      console.error(`Error uploading ${field}:`, error);
      throw new BadRequestException(
        `Gagal mengunggah file ${field}: ${error.message}`,
      );
    }
  }

  private async _detailPersonel(
    id: bigint,
  ): Promise<IParticipantSelectionDetail> {
    const personel = await this.prisma.personel.findFirst({
      where: { id },
      select: {
        nama_lengkap: true,
        foto_file: true,
        nrp: true,
        tanggal_lahir: true,
        seleksi_baglekdik_selection_participants: {
          select: {
            id: true,
            stage_id: true,
          },
        },
        pangkat_personel: {
          select: {
            pangkat: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
          },
          orderBy: { tmt: 'desc' },
          take: 1,
        },
        psikologi_personel: {
          where: { deleted_at: null },
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        jasmani_personel: {
          where: { deleted_at: null },
          select: {
            tahun: true,
            semester: true,
            nilai_akhir: true,
            keterangan: true,
          },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        rohani_personel: {
          where: { deleted_at: null },
          select: {
            tahun: true,
            semester: true,
            nilai: true,
            keterangan: true,
          },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        dikum_personel: {
          where: { deleted_at: null },
          select: {
            dikum_detail: {
              select: {
                dikum: true,
              },
            },
          },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        lama_mddp_personel: {
          select: { lama_menjabat_bulan: true },
          orderBy: { tmt: 'desc' },
          take: 1,
        },
        rikkesla_personel: {
          where: { deleted_at: null },
          select: { nilai: true },
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    const durasiMDDP = moment.duration(
      personel.lama_mddp_personel[0].lama_menjabat_bulan ?? 0,
      'months',
    );
    const result: IParticipantSelectionDetail = {
      fullName: personel.nama_lengkap ?? '',
      photoFile: personel.foto_file ?? '',
      nrp: personel.nrp,
      age: Number(moment(personel.tanggal_lahir).fromNow(true).split(' ')[0]),
      rankId: personel.pangkat_personel[0]?.pangkat.id,
      rank: personel.pangkat_personel[0]?.pangkat.nama_singkat ?? '',
      physicalScore:
        parseNumber(personel.jasmani_personel[0]?.nilai_akhir) ?? 0,
      spiritualScore: personel.rohani_personel[0]?.nilai ?? 0,
      mentalScore: personel.psikologi_personel[0]?.nilai ?? 0,
      healthScore: personel.rikkesla_personel[0]?.nilai ?? 0,
      educationLevel:
        personel.dikum_personel[0]?.dikum_detail?.dikum?.nama ?? '',
      mddp: durasiMDDP.years(),
      isAllowRegister:
        !personel.seleksi_baglekdik_selection_participants ||
        personel.seleksi_baglekdik_selection_participants.length <= 0
          ? true
          : false,
      requirementFiles: [],
      selectionId: personel.seleksi_baglekdik_selection_participants,
    };

    return result;
  }

  private _checkRequirementParticipant(
    requirements,
    personel: IParticipantSelectionDetail,
  ): boolean {
    const isInRange = (value, min, max, threshold) => {
      return (
        (min === null || value >= Number(min)) &&
        (max === null || value <= Number(max)) &&
        (threshold === null || value >= Number(threshold))
      );
    };

    for (const requirement of requirements) {
      if (!requirement.requirement_id) continue;

      const requirementId = Number(requirement.requirement_id);

      const requirementChecks = {
        1: () =>
          personel.rankId === BigInt(requirement.value) ||
          personel.rank.toLowerCase() === requirement.value.toLowerCase(),
        3: () => personel.mddp >= Number(requirement.value),
        4: () =>
          personel.educationLevel.toLowerCase() ===
          requirement.value.toLowerCase(),
        6: () =>
          isInRange(
            personel.age,
            requirement.min_value,
            requirement.max_value,
            requirement.value,
          ),
        8: () =>
          isInRange(
            personel.healthScore,
            requirement.min_value,
            requirement.max_value,
            requirement.value,
          ),
        9: () =>
          isInRange(
            personel.physicalScore,
            requirement.min_value,
            requirement.max_value,
            requirement.value,
          ),
        10: () =>
          isInRange(
            personel.spiritualScore,
            requirement.min_value,
            requirement.max_value,
            requirement.value,
          ),
        11: () =>
          isInRange(
            personel.mentalScore,
            requirement.min_value,
            requirement.max_value,
            requirement.value,
          ),
      };

      const isValid = requirementChecks[requirementId]?.() ?? true;
      if (!isValid) return false;
    }

    return true;
  }
}
