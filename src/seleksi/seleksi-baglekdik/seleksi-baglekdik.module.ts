import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { SeleksiBaglekdikController } from './controller/seleksi-baglekdik.controller';
import { SeleksiBaglekdikService } from './service/seleksi-baglekdik.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SeleksiBaglekdikController],
  providers: [SeleksiBaglekdikService, MinioService],
})
export class SeleksiBaglekdikModule {}
