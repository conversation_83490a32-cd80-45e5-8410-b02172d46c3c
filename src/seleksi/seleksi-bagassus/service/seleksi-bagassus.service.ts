import { BadRequestException, Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import * as dayjs from 'dayjs';
import { translateDotNotation } from 'src/core/utils/common.utils';
import { ExcelService } from '../../../api-utils/excel/service/excel.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import {
  PromosiJabatanParticipantStageStatusEnum,
  PromosiJabatanParticipantStatusEnum,
} from '../../../core/enums/promosi-jabatan.enum';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import {
  IFileUpload,
  IHistoryStage,
} from '../../../core/interfaces/promosi-jabatan.interface';
import {
  IFilteredKesehatanExcel,
  IPersonelData,
  ISeleksiResult,
} from '../../../core/interfaces/seleksi.interface';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  buildPaginatedResponse,
  buildResponse,
} from '../../../core/utils/response.util';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import { SeleksiHelper } from '../../utils/helper';
import {
  ApprovalStatusEnum,
  ApprovalSubmissionDto,
  SeleksiBagassusCreateDto,
  SeleksiBagassusFilter,
  SeleksiBagassusListFilter,
} from '../dto/seleksi-bagassus.dto';

@Injectable()
export class SeleksiBagassusService extends SeleksiHelper {
  constructor(
    protected readonly prisma: PrismaService,
    protected readonly minioService: MinioService,
    protected readonly logsActivityService: LogsActivityService,
    protected readonly excelService: ExcelService,
  ) {
    super(minioService, prisma, excelService);
  }

  async getRequirements(req: any) {
    const queryResult = await this.prisma.selection_requirement.findMany({
      where: { selection_module: { in: ['ALL', 'BAGASSUS'] } },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async createSeleksi(
    user: any,
    payload: SeleksiBagassusCreateDto,
    files: Express.Multer.File[],
  ) {
    const { stages, requirements, requirement_files, ...rest } = payload;

    const uploadBannerSelection = await this.processFile(
      files.find((file) => file.fieldname === 'banner_selection'),
      'banner_selection',
      800 * 1024,
      ['jpg', 'jpeg', 'png', 'gif'],
    );

    if (!uploadBannerSelection) {
      throw new BadRequestException('Banner is required');
    }

    const [uploadBannerStages, listRequirements] = await Promise.all([
      this.processBannerStages(files),
      this.processRequirements(requirements),
    ]);

    return this.prisma.$transaction(async (tx) => {
      try {
        const fileBannerSelection = uploadBannerSelection
          ? await tx.selection_file.create({ data: uploadBannerSelection })
          : null;

        const fileBannerStages = await this.storeBannerStages(
          tx,
          uploadBannerStages,
        );

        const { create } = await this.processStages(stages, fileBannerStages);

        const data: Prisma.selection_bagassusCreateInput = {
          ...rest,
          total_stages: stages.length,
          created_by_user: { connect: { id: user?.id } },
          selection_bagassus_files_banner: {
            connect: { id: fileBannerSelection.id },
          },
          selection_bagassus_requirement: { create: listRequirements.create },
          selection_bagassus_stage: { create },
        };

        if (requirement_files?.length) {
          data.selection_bagassus_file_requirement = {
            create: requirement_files.filter(Boolean),
          };
        }

        return await tx.selection_bagassus.create({ data });
      } catch (err) {
        if (err?.code === 'P2002') {
          throw new BadRequestException(
            "There's duplicate stage with same name and comparision type",
          );
        }
        throw err;
      }
    });
  }

  async updateSeleksi(
    user: any,
    id: number,
    payload: SeleksiBagassusCreateDto,
    files: Express.Multer.File[],
  ) {
    console.log({ payload, files });
    const { stages, requirements, requirement_files, ...rest } = payload;

    const exist = await this.prisma.selection_bagassus.findFirst({
      where: { id },
      include: {
        selection_bagassus_requirement: true,
        selection_bagassus_stage: true,
        selection_bagassus_files_banner: true,
        selection_bagassus_file_requirement: true,
      },
    });

    if (!exist) {
      throw new BadRequestException('Seleksi tidak ditemukan');
    }

    if (!files?.length) files = [];

    const uploadBannerSelection = await this.processFile(
      files.find((file) => file.fieldname === 'banner_selection'),
      'banner_selection',
      800 * 1024,
      ['jpg', 'jpeg', 'png', 'gif'],
    );

    const [uploadBannerStages, listRequirements] = await Promise.all([
      this.processBannerStages(files),
      this.processRequirements(requirements),
    ]);

    const fileRequirementCreate = [];
    const fileRequirementMap = new Map();
    const requirementMap = new Map();

    requirement_files.map((d) => {
      const { existing_id, ...rest } = d;
      if (existing_id) fileRequirementMap.set(existing_id, rest);
      else fileRequirementCreate.push(d);
    });
    requirements.map((d) => {
      if (d.existing_id) requirementMap.set(d.existing_id, d);
    });

    return this.prisma.$transaction(async (tx) => {
      try {
        // delete unlisted data from client
        await Promise.all(
          exist.selection_bagassus_file_requirement.map(async (d) => {
            if (fileRequirementMap.has(Number(d.id))) return;
            return tx.selection_bagassus_file_requirement.delete({
              where: { id: d.id },
            });
          }),
        );

        await Promise.all(
          exist.selection_bagassus_requirement.map(async (d) => {
            if (requirementMap.has(Number(d.id))) return;
            return tx.selection_bagassus_requirement.delete({
              where: { id: d.id },
            });
          }),
        );

        const fileBannerSelection = uploadBannerSelection
          ? await tx.selection_file.create({ data: uploadBannerSelection })
          : null;

        const fileBannerStages = await this.storeBannerStages(
          tx,
          uploadBannerStages,
        );

        const { create, update } = await this.processStages(
          stages,
          fileBannerStages,
        );

        // set create query, for data without existing id
        const data: Prisma.selection_bagassusCreateInput = {
          ...rest,
          is_closed: false,
          total_stages: stages.length,
          created_by_user: { connect: { id: user?.id } },
          selection_bagassus_requirement: { create: listRequirements.create },
          selection_bagassus_stage: { create },
        };

        if (fileBannerSelection?.id) {
          data.selection_bagassus_files_banner = {
            connect: { id: fileBannerSelection.id },
          };
        }

        if (requirement_files?.length) {
          data.selection_bagassus_file_requirement = {
            create: fileRequirementCreate,
          };
        }

        // update data, for data with existing id
        if (update.length) {
          await Promise.all(
            update.map(async (item) => {
              await tx.selection_bagassus_stage.update({
                where: { id: item.id },
                data: item.data,
              });
            }),
          );
        }

        if (listRequirements.update?.length) {
          await Promise.all(
            listRequirements.update.map(async (item) => {
              await tx.selection_bagassus_requirement.update({
                where: { id: item.id },
                data: item.data,
              });
            }),
          );
        }

        if (fileRequirementMap.size) {
          await Promise.all(
            Array.from(fileRequirementMap).map(async ([id, data]) => {
              await tx.selection_bagassus_file_requirement.update({
                where: { id },
                data,
              });
            }),
          );
        }

        return await tx.selection_bagassus.update({ where: { id }, data });
      } catch (err) {
        if (err?.code === 'P2002') {
          throw new BadRequestException(
            "There's duplicate stage with same name and comparision type",
          );
        }
        throw err;
      }
    });
  }

  async getStages(req: any, id: number) {
    const queryResult = await this.prisma.selection_bagassus_stage.findMany({
      where: { selection_id: id },
      include: {
        selection: true,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async getDetail(req: any, id: number) {
    const queryResult = await this.prisma.selection_bagassus.findFirst({
      where: { id },
      include: {
        selection_bagassus_stage: {
          include: {
            selection_bagassus_stage_files_banner: {
              select: {
                file_name: true,
              },
            },
          },
        },
        selection_bagassus_file_requirement: true,
        selection_bagassus_requirement: {
          include: {
            selection_requirement: true,
          },
          orderBy: { id: 'asc' },
        },
        selection_bagassus_files_banner: {
          select: {
            file_name: true,
          },
        },
      },
    });

    queryResult['banner'] = await this.minioService.checkFileExist(
      process.env.MINIO_BUCKET_NAME!,
      `${process.env.MINIO_PATH_FILE}/${queryResult.selection_bagassus_files_banner.file_name}`,
    );

    await Promise.all(
      queryResult.selection_bagassus_stage.map(async (item) => {
        if (item.selection_bagassus_stage_files_banner?.file_name) {
          item['banner'] = await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}/${item.selection_bagassus_stage_files_banner.file_name}`,
          );
        }

        delete item.selection_bagassus_stage_files_banner;
      }),
    );

    queryResult.selection_bagassus_requirement.forEach((item) => {
      item['name'] = item.selection_requirement.name;
      item['input_type'] = item.selection_requirement.input_type;
      item['url_data'] = item.selection_requirement.url_data;
      delete item.selection_requirement;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async getSeleksiList(
    req: any,
    filter: SeleksiBagassusFilter,
    pagination: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { type, user_id, show_closed } = filter;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    let { limit, page } = pagination;

    const columnMapping: IColumnMapping = {
      nama: {
        field: 'nama',
        type: 'string',
      },
    };

    limit = Number(limit || 10);
    page = Number(page || 1);

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    delete where.deleted_at;

    if (type) where['type'] = type;
    if (user_id) where['created_by_user'] = { id: user_id };
    if (!show_closed) where['is_closed'] = false;

    const [totalData, result] = await this.prisma.$transaction([
      this.prisma.selection_bagassus.count({
        where: where,
      }),
      this.prisma.selection_bagassus.findMany({
        select: {
          id: true,
          title: true,
          description: true,
          total_stages: true,
          selection_bagassus_files_banner: {
            select: {
              file_name: true,
            },
          },
          selection_bagassus_participant: {
            select: {
              id: true,
            },
          },
          selection_bagassus_stage: {
            select: {
              id: true,
              stage: true,
              name: true,
              start_date: true,
              end_date: true,
              status: true,
            },
          },
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    await Promise.all(
      result.map(async (item) => {
        item['banner'] = '';

        const current = dayjs().startOf('day').add(7, 'hour').toDate();
        item['stage'] = item.selection_bagassus_stage.find((stage) => {
          return stage.start_date <= current && stage.end_date >= current;
        });

        if (!item['stage']) {
          const nearestDate = item.selection_bagassus_stage
            .sort((a, b) => b.start_date.valueOf() - a.start_date.valueOf())
            .find((stage) => {
              return stage.start_date < new Date();
            });
          if (nearestDate) {
            item['stage'] = nearestDate;
          } else {
            item['stage'] = item.selection_bagassus_stage.pop();
          }
        }

        if (item.selection_bagassus_participant?.[0]) {
          item['is_registered'] = true;
        }

        item['stage']['status'] =
          item['stage']['start_date'] <= current &&
          item['stage']['end_date'] >= current
            ? 'DIBUKA'
            : 'DITUTUP';

        if (item.selection_bagassus_files_banner?.file_name) {
          item['banner'] = await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}/${item.selection_bagassus_files_banner.file_name}`,
          );
        }

        delete item.selection_bagassus_files_banner;
        delete item.selection_bagassus_stage;
      }),
    );

    const queryResult = {
      result,
      page,
      totalPage: Math.ceil(totalData / limit),
      totalData,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(queryResult, message);
  }

  async getSeleksiPersonelDetail(req: any, id: number) {
    const queryResult = await this._getDetailAndValidate(id, req?.user || {});

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async getParticipantsList(
    req: any,
    id: number,
    stage_id: number,
    pagination: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
    filter: SeleksiBagassusListFilter,
  ) {
    if (isNaN(id) || isNaN(stage_id)) {
      throw new BadRequestException('ID seleksi/ID tahapan tidak valid');
    }

    let { limit, page } = pagination;
    const { status, name } = filter;
    const { sort_column, sort_desc } = searchAndSortData;

    limit = Number(limit || 10);
    page = Number(page || 1);

    const stage = await this.prisma.selection_bagassus_stage.findFirst({
      where: { id: stage_id },
      select: {
        id: true,
        stage: true,
        name: true,
        start_date: true,
        end_date: true,
        status: true,
      },
    });

    const rawWhere = [
      `selection_id = ${id}`,
      `jsonb_path_exists(history_stages::jsonb,'$[*] ? (@.stage == ${stage.stage})')`,
    ];

    if (status) {
      rawWhere.push(
        `jsonb_path_exists(history_stages::jsonb,'$[*] ? (@.stage == ${stage.stage} && @.status == "${status === 'NOT_CHECKED_YET' ? '' : status}")')`,
      );
    }
    if (name) {
      rawWhere.push(`p.nama_lengkap ILIKE '%${name}%'`);
    }

    let sort = 'sbp.id ASC';
    if (sort_column) {
      sort = `sbp.${sort_column} ${sort_desc || 'ASC'}`;
    }

    const [total, dataRaw, selection, all] = await this.prisma.$transaction([
      this.prisma.$queryRawUnsafe(
        `SELECT COUNT(sbp.id)
         FROM selection_bagassus_participant sbp
             JOIN personel p ON p.id = sbp.personel_id 
         WHERE ${rawWhere.join(' AND ')}`,
      ),
      this.prisma.$queryRawUnsafe<any[]>(
        `SELECT 
            sbp.id AS id,
            sbp.registration_number AS registration_number,
            sbp.exam_number AS exam_number,
            sbp.file_uploads AS file_uploads,
            sbp.history_stages AS history_stages,
            p.id AS "personel.id",
            p.nama_lengkap AS "personel.nama_lengkap",
            p.nrp AS "personel.nrp",
            p.tanggal_lahir AS "personel.tanggal_lahir",
            p.jenis_kelamin AS "personel.jenis_kelamin",
            p.foto_file AS "personel.foto_file"
        FROM selection_bagassus_participant sbp
        JOIN personel p ON p.id = sbp.personel_id
        WHERE ${rawWhere.join(' AND ')}
        ORDER BY ${sort}
        LIMIT ${limit}
        OFFSET ${limit * (page - 1)}
      `,
      ),
      this.prisma.selection_bagassus.findFirst({
        where: { id },
        select: {
          id: true,
          title: true,
          description: true,
          total_stages: true,
        },
      }),
      this.prisma.selection_bagassus_participant.findMany({
        where: { selection_id: id },
        select: {
          file_uploads: true,
          history_stages: true,
        },
      }),
    ]);

    const data = dataRaw.map((item) => translateDotNotation(item));

    const {
      latestJabatan,
      latestPangkat,
      personelFotoUrls,
      files,
      fileRequirements,
    } = await this.getAddionalDataList(data);

    const [fileMap, fotoMap, jabatanMap, pangkatMap, fileRequirementMap] =
      await Promise.all([
        this.getFileDictionary(files),
        this.getFotoDictionary(personelFotoUrls),
        this.getJabatanDictionary(latestJabatan as Array<any>),
        this.getPangkatDictionary(latestPangkat as Array<any>),
        this.getRequirementFileDictionary(fileRequirements),
      ]);

    await this.restructureParticipationList({
      data,
      stage,
      pangkatMap,
      jabatanMap,
      fotoMap,
      fileMap,
      fileRequirementMap,
    });

    const totalData = Number(total?.[0]?.count || 0);

    const statuses = [];
    all.forEach((item) => {
      statuses.push(
        this.determineParticipantStatus(
          stage,
          item.file_uploads,
          this.parseHistoryStages(item.history_stages),
        ),
      );
    });

    const status_count = this.countStatusParticipant(statuses);
    const totalPage = Math.ceil(totalData / limit);

    const queryResult = {
      result: {
        list: data,
        selection,
        stage,
        status_count,
      },
      page,
      totalPage,
      totalData,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(queryResult, message);
  }

  async personelSubmission(req: any, id: number, files: Express.Multer.File[]) {
    if (isNaN(id)) {
      throw new BadRequestException('ID seleksi tidak valid');
    }

    const { eligible, message, stages, file_requirement, personel } =
      await this._getDetailAndValidate(id, req?.user || {});

    if (!eligible) throw new BadRequestException(message);

    const fileMap = new Map(files?.map((d) => [d.fieldname, d]));

    const fileMock = [];
    await Promise.all(
      file_requirement.map(async (requirement) => {
        const title = requirement.title.toLowerCase();

        if (requirement.is_required && !fileMap.has(title)) {
          throw new BadRequestException(
            `File ${requirement.title} wajib diunggah!`,
          );
        }

        const file = fileMap.get(title);

        fileMock.push({
          requirement_file_id: Number(requirement.id),
          file: await this.processFile(
            file,
            title,
            requirement.max_size,
            requirement.extensions,
          ),
        });
      }),
    );

    const now = new Date().toISOString();

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const fileUploads: IFileUpload[] = await Promise.all(
        fileMock.map(async ({ requirement_file_id, file }) => {
          const createdFile = await tx.selection_file.create({ data: file });

          return {
            requirement_file_id,
            file_id: Number(createdFile.id),
            created_at: now,
            status: PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
            reason: null,
          };
        }),
      );

      const registrationNumber = String(id).padStart(10, '0') + personel.nrp;

      return tx.selection_bagassus_participant.create({
        data: {
          registration_number: registrationNumber,
          personel_id: req?.user?.personel_id,
          selection_id: id,
          current_stage_id: stages.id,
          history_stages: [
            {
              stage_id: stages.id,
              stage: stages.stage,
              created_at: now,
              status: '',
            },
          ],
          file_uploads: fileUploads as unknown as Prisma.JsonArray,
        },
      });
    });

    const resMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_CREATE as ConstantLogType,
        resMessage,
        queryResult,
      ),
    );

    return buildResponse(queryResult, resMessage);
  }

  async updateSubmissionSeleksiBagassus(
    req: any,
    registrationNumber: string,
    files: Express.Multer.File[],
  ) {
    const exist = await this.prisma.selection_bagassus_participant.findFirst({
      where: { registration_number: registrationNumber },
      select: {
        id: true,
        selection_id: true,
        current_stage_id: true,
        history_stages: true,
        file_uploads: true,
      },
    });

    const file_requirement =
      await this.prisma.selection_bagassus_file_requirement.findMany({
        where: { selection_id: exist.selection_id },
      });

    if (!exist) {
      throw new BadRequestException('Peserta tidak ditemukan');
    }

    const fileMap = new Map(files?.map((d) => [d.fieldname, d]));

    const fileMock = [];
    await Promise.all(
      file_requirement.map(async (requirement) => {
        const title = requirement.title.toLowerCase();

        if (!fileMap.has(title)) return;

        const file = fileMap.get(title);

        fileMock.push({
          requirement_file_id: Number(requirement.id),
          file: await this.processFile(
            file,
            title,
            requirement.max_size,
            requirement.extensions,
          ),
        });
      }),
    );

    const now = new Date().toISOString();

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const fileUploads: IFileUpload[] = await Promise.all(
        fileMock.map(async ({ requirement_file_id, file }) => {
          const createdFile = await tx.selection_file.create({ data: file });

          return {
            requirement_file_id,
            file_id: Number(createdFile.id),
            created_at: now,
            status: PromosiJabatanParticipantStatusEnum.REVISION,
            reason: null,
          };
        }),
      );

      const mock = this.parseFileUploads(exist.file_uploads).map((file) => {
        const exist = fileUploads.find(
          (f) => f.requirement_file_id === file.requirement_file_id,
        );
        if (!exist) return file;

        return exist;
      });

      return tx.selection_bagassus_participant.update({
        where: { id: exist.id },
        data: {
          file_uploads: mock as unknown as Prisma.JsonArray,
        },
      });
    });

    const resMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_UPDATE as ConstantLogType,
        resMessage,
        queryResult,
      ),
    );

    return buildResponse(queryResult, resMessage);
  }

  async close(req: any, id: number) {
    const queryResult = await this.prisma.selection_bagassus.update({
      where: { id },
      data: { is_closed: true },
    });

    const resMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_UPDATE as ConstantLogType,
        resMessage,
        queryResult,
      ),
    );

    return buildResponse(queryResult, resMessage);
  }

  async submissionApproval(
    req: any,
    registrationNumber: string,
    body: ApprovalSubmissionDto,
  ) {
    const { status, reason, file_ids } = body;
    const exist = await this.prisma.selection_bagassus_participant.findFirst({
      where: { registration_number: registrationNumber },
      select: {
        id: true,
        selection_id: true,
        current_stage_id: true,
        history_stages: true,
        file_uploads: true,
        selection_stage: {
          select: { stage: true, id: true },
        },
      },
    });

    if (!exist) {
      throw new BadRequestException('Peserta tidak ditemukan');
    }

    const history_stages = this.parseHistoryStages(exist.history_stages);
    const currentStage = history_stages.find(
      (stage) => stage.stage === exist.selection_stage.stage - 1,
    );

    if (
      currentStage?.status === PromosiJabatanParticipantStageStatusEnum.LOLOS
    ) {
      throw new BadRequestException('Peserta sudah lolos tahap ini');
    }

    const stage = await this.prisma.selection_bagassus_stage.findFirst({
      where: {
        stage: { gt: exist.selection_stage.stage },
        selection_id: exist.selection_id,
      },
      select: { id: true, stage: true },
      orderBy: { stage: 'asc' },
    });

    const updateFileIdMap = new Map(
      file_ids?.map((id) => [
        id,
        status === ApprovalStatusEnum.APPROVE
          ? PromosiJabatanParticipantStatusEnum.CHECKED
          : PromosiJabatanParticipantStatusEnum.REJECTED,
      ]),
    );

    const files = this.parseFileUploads(exist.file_uploads);
    const fileStatus = [];
    files.forEach((file) => {
      file.status =
        updateFileIdMap.get(file.file_id) ||
        PromosiJabatanParticipantStatusEnum.CHECKED;

      if (file.status === PromosiJabatanParticipantStatusEnum.REJECTED) {
        file.reason = reason;
      }

      fileStatus.push(
        file.status === PromosiJabatanParticipantStatusEnum.CHECKED,
      );
    });

    let current_stage_id = exist.current_stage_id;
    if (fileStatus.every(Boolean)) {
      history_stages.forEach((history) => {
        if (history.stage_id === Number(exist.current_stage_id)) {
          history.status = PromosiJabatanParticipantStageStatusEnum.LOLOS;
        }
      });
      history_stages.push({
        stage_id: Number(stage.id),
        stage: stage.stage,
        created_at: new Date().toISOString(),
        status: '',
      });
      if (stage?.id) current_stage_id = stage.id;
    }

    const queryResult = await this.prisma.selection_bagassus_participant.update(
      {
        where: { id: exist.id },
        data: {
          current_stage_id,
          file_uploads: files as unknown as Prisma.JsonArray,
          history_stages: history_stages as unknown as Prisma.JsonArray,
        },
      },
    );

    const resMessage = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SELEKSI_BAGASSUS_UPDATE as ConstantLogType,
        resMessage,
        queryResult,
      ),
    );

    return buildResponse(queryResult, resMessage);
  }

  async importKesehatan(req: any, id: number, file: Express.Multer.File) {
    // TODO: min exam number and excel format
    return this.proceedExcel({
      req,
      file,
      callback: async ({ data, header }) => {
        const expectedHeader = { nrp: true, status: true };
        const { filteredData, personelMap, participantMap } =
          await this._extractAndValidateExcel(header, expectedHeader, id, data);

        const nextStage = await this.prisma.selection_bagassus_stage.findFirst({
          where: { selection_id: id, stage: 3 },
          select: { id: true },
        });

        if (!nextStage) {
          throw new BadRequestException('Next stage (stage 3) not found.');
        }

        const skippedNrps: string[] = [];
        const successfulNrps: string[] = [];
        const updatePromises = [];

        for (const d of filteredData) {
          const personel = personelMap.get(d.nrp);
          const participant = personel
            ? participantMap.get(personel.id)
            : undefined;

          if (!participant) continue;

          const history = this.parseHistoryStages(participant.history_stages);
          const adminStage = history.find((h) => h.stage === 1);

          if (
            adminStage?.status !==
            PromosiJabatanParticipantStageStatusEnum.LOLOS
          ) {
            skippedNrps.push(d.nrp);
            continue;
          }

          const newStatus =
            d.status?.toLowerCase() === 'lulus'
              ? PromosiJabatanParticipantStageStatusEnum.LOLOS
              : PromosiJabatanParticipantStageStatusEnum.TIDAK_LOLOS;

          const currentStage = history.find((h) => h.stage === 2);
          if (currentStage) currentStage.status = newStatus;

          let current_stage_id = participant.current_stage_id;
          if (newStatus === PromosiJabatanParticipantStageStatusEnum.LOLOS) {
            current_stage_id = nextStage.id;
            const nextStageHistory = history.find((h) => h.stage === 3);
            if (!nextStageHistory) {
              history.push({
                stage_id: Number(nextStage.id),
                stage: 3,
                created_at: new Date().toISOString(),
                status: '',
              });
            }
          }

          updatePromises.push(
            this.prisma.selection_bagassus_participant.update({
              where: { id: participant.id },
              data: {
                current_stage_id,
                history_stages: history as unknown as Prisma.JsonArray,
              },
            }),
          );
          successfulNrps.push(d.nrp);
        }

        await this.prisma.$transaction(updatePromises);

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.SELEKSI_BAGASSUS_UPDATE as ConstantLogType,
            message,
            {},
          ),
        );

        return buildResponse(
          { skipNrp: skippedNrps, success: successfulNrps },
          message,
        );
      },
    });
  }

  async importExamResult(req: any, id: number, file: Express.Multer.File) {
    // TODO: excel format
    return await this.proceedExcel({
      req,
      file,
      callback: async ({ data, header }) => {
        const expectedHeader = { nrp: true, status: true, instansi: true };
        const { filteredData, personelMap, participantMap } =
          await this._extractAndValidateExcel(header, expectedHeader, id, data);

        const skippedNrps: string[] = [];
        const successfulNrps: string[] = [];
        const updatePromises = [];

        for (const d of filteredData) {
          const personel = personelMap.get(d.nrp);
          const participant = personel
            ? participantMap.get(personel.id)
            : undefined;

          if (!participant) continue;

          const history = this.parseHistoryStages(participant.history_stages);
          const pastStage = history.find((h) => h.stage === 2);

          if (
            pastStage?.status !== PromosiJabatanParticipantStageStatusEnum.LOLOS
          ) {
            skippedNrps.push(d.nrp);
            continue;
          }
          const currStage = history.find((h) => h.stage === 3);
          if (
            currStage?.status === PromosiJabatanParticipantStageStatusEnum.LOLOS
          ) {
            skippedNrps.push(d.nrp);
            continue;
          }

          const newStatus =
            d.status?.toLowerCase() === 'lulus'
              ? PromosiJabatanParticipantStageStatusEnum.LOLOS
              : PromosiJabatanParticipantStageStatusEnum.TIDAK_LOLOS;

          const currentStage = history.find((h) => h.stage === 3);
          if (currentStage) currentStage.status = newStatus;

          const promises: Array<any> = [
            this.prisma.selection_bagassus_participant.update({
              where: { id: participant.id },
              data: {
                history_stages: history as unknown as Prisma.JsonArray,
              },
            }),
          ];

          if (newStatus === PromosiJabatanParticipantStageStatusEnum.LOLOS) {
            const instansi = await this.prisma.bagassus_instansi.upsert({
              where: { nama_lowercase: d.instansi.toLowerCase() },
              create: {
                nama: d.instansi,
                nama_lowercase: d.instansi.toLowerCase(),
              },
              update: {
                nama: d.instansi,
                nama_lowercase: d.instansi.toLowerCase(),
              },
            });
            promises.push(
              this.prisma.bagassus_deposit.create({
                data: {
                  personel_id: personel.id,
                  instansi_id: instansi.id,
                  no_pendaftaran_seleksi: participant.registration_number,
                  no_ujian_seleksi: participant.exam_number,
                  created_at: new Date(),
                  type: participant.type,
                },
              }),
            );
          }

          updatePromises.push(...promises);
          successfulNrps.push(d.nrp);
        }

        await this.prisma.$transaction(updatePromises);

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.SELEKSI_BAGASSUS_UPDATE as ConstantLogType,
            message,
            {},
          ),
        );

        return buildResponse(
          { skipNrp: skippedNrps, success: successfulNrps },
          message,
        );
      },
    });
  }

  private async _extractAndValidateExcel(
    excelHeader: Array<string>,
    expectedHeader: Record<string, any>,
    id: number,
    data: Array<any>,
  ) {
    const filtered = excelHeader.filter((h) => expectedHeader[h.toLowerCase()]);
    if (filtered?.length !== Object.keys(expectedHeader).length) {
      throw new BadRequestException(
        'Format template tidak valid. harap gunakan template yang disediakan.',
      );
    }

    const check = await this._checkSelectionExist(id);
    const filteredData = await this._getDataFromExcel(data, expectedHeader);
    const personelMap = await this._getPersonel(filteredData.map((d) => d.nrp));

    const participantMap = new Map();
    check.selection_bagassus_participant.forEach((d) => {
      participantMap.set(d.personel_id, { ...d, type: check.type });
    });

    const notFoundNrp = filteredData.filter((d) => !personelMap.has(d.nrp));
    if (notFoundNrp.length) {
      throw new BadRequestException(
        `NRP ${notFoundNrp.map((d) => d.nrp).join(', ')} tidak ditemukan`,
      );
    }

    return { filteredData, personelMap, participantMap };
  }

  private async _getDataFromExcel(
    data: Array<any>,
    expectedHeader: Record<string, any>,
  ): Promise<Array<IFilteredKesehatanExcel>> {
    return data
      .map((d) => {
        const result: IFilteredKesehatanExcel = { nrp: null, status: null };
        for (const key of Object.keys(d)) {
          if (!expectedHeader[key.toLowerCase()]) continue;
          result[key] = d[key];
        }

        return result;
      })
      .filter((d) => d.nrp && d.status);
  }

  private async _getPersonel(nrp: string[]): Promise<Map<any, any>> {
    const personel = await this.prisma.personel.findMany({
      where: { nrp: { in: nrp } },
      select: { id: true, nrp: true, nama_lengkap: true },
    });

    const personelMap = new Map();
    personel.forEach((d) => personelMap.set(d.nrp, d));

    return personelMap;
  }

  private async _checkSelectionExist(id: number) {
    const check = await this.prisma.selection_bagassus.findFirst({
      where: { id },
      select: {
        id: true,
        type: true,
        selection_bagassus_participant: {
          select: {
            id: true,
            personel_id: true,
            registration_number: true,
            history_stages: true,
            current_stage_id: true,
            exam_number: true,
          },
        },
      },
    });

    if (!check?.id) {
      throw new BadRequestException('Seleksi tidak ditemukan');
    }

    return check;
  }

  private async _getDetailAndValidate(id: number, user: Record<string, any>) {
    const [data, personel, latestPangkat, latestJabatan] =
      await this.prisma.$transaction([
        this.prisma.selection_bagassus.findFirst({
          where: { id },
          select: {
            id: true,
            title: true,
            description: true,
            total_stages: true,
            selection_bagassus_files_banner: {
              select: {
                file_name: true,
              },
            },
            selection_bagassus_requirement: {
              select: {
                id: true,
                is_required: true,
                value: true,
                comparison_type: true,
                selection_requirement: {
                  select: {
                    name: true,
                    input_type: true,
                    url_data: true,
                    table_foreign: true,
                    auto_validate: true,
                  },
                },
              },
            },
            selection_bagassus_file_requirement: {
              select: {
                id: true,
                is_required: true,
                title: true,
                max_size: true,
                min_size: true,
                extensions: true,
              },
              orderBy: { id: 'asc' },
            },
            selection_bagassus_stage: {
              select: {
                id: true,
                stage: true,
                name: true,
                start_date: true,
                end_date: true,
                status: true,
                selection_bagassus_stage_files_banner: {
                  select: {
                    original_name: true,
                    url: true,
                    file_name: true,
                  },
                },
              },
              where: {
                start_date: { lte: new Date() },
                end_date: { gte: new Date() },
              },
            },
            selection_bagassus_participant: {
              select: {
                registration_number: true,
                exam_number: true,
                history_stages: true,
                file_uploads: true,
              },
              where: { personel_id: user?.personel_id },
            },
          },
        }),
        this.prisma.personel.findFirst({
          where: { id: user.personel_id },
          select: {
            id: true,
            nrp: true,
            nama_lengkap: true,
            tanggal_lahir: true,
            jenis_kelamin: true,
            foto_file: true,
          },
        }),
        this.prisma.$queryRaw(Prisma.sql`
            SELECT pangkat_id, pangkat
            FROM mv_pangkat_terakhir
            WHERE personel_id = ${user.personel_id}
            ORDER BY tmt DESC
            LIMIT 1
          `),
        this.prisma.$queryRaw(Prisma.sql`
            SELECT jabatan_id, jabatan, satuan_id, tmt
            FROM mv_jabatan_terakhir
            WHERE personel_id = ${user.personel_id}
            ORDER BY tmt DESC
            LIMIT 1
          `),
      ]);

    let satuan = {};
    if (latestJabatan?.[0]?.satuan_id) {
      satuan = await this.prisma.satuan.findFirst({
        where: { id: latestJabatan?.[0]?.satuan_id },
        select: { id: true, nama: true },
      });
    }

    const personelData: IPersonelData = {
      ...personel,
      satuan,
      pangkat: latestPangkat?.[0] || {},
      jabatan: latestJabatan?.[0] || {},
    };

    if (personelData.foto_file) {
      personelData.foto_file = await this.minioService.checkFileExist(
        process.env.MINIO_BUCKET_NAME!,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      );
    }

    const {
      selection_bagassus_requirement: requirements,
      selection_bagassus_file_requirement: file_requirement,
      selection_bagassus_stage: stages,
      selection_bagassus_files_banner: banner,
      selection_bagassus_participant: participants,
      ...rest
    } = data;

    const result: ISeleksiResult = {
      ...rest,
      banner: '',
      stages: stages?.[0] || {},
      requirements: [],
      file_requirement,
      personel: personelData,
      message: '',
      status: '',
      eligible: true,
      uploaded_file: [],
      history_stages:
        (participants?.[0]?.history_stages as unknown as IHistoryStage[]) || [],
      registration_number: participants?.[0]?.registration_number || null,
    };

    if (participants?.length) {
      result.status = this.determineParticipantStatus(
        stages?.[0],
        participants?.[0]?.file_uploads,
        this.parseHistoryStages(participants?.[0]?.history_stages),
      );

      const fileUploads = [];
      await Promise.all(
        this.parseFileUploads(participants[0]?.file_uploads).map(
          async (file) => {
            const fileData = await this.prisma.selection_file.findFirst({
              where: { id: file.file_id },
              select: { file_name: true, size: true },
            });

            file['url'] = await this.minioService.checkFileExist(
              process.env.MINIO_BUCKET_NAME!,
              `${process.env.MINIO_PATH_FILE}/${fileData.file_name}`,
            );
            file['file_name'] = fileData.file_name;
            file['title'] = file_requirement?.find(
              (r) => Number(r.id) === file.requirement_file_id,
            )?.title;
            file['size'] = fileData.size;

            fileUploads.push(file);
          },
        ),
      );

      result.uploaded_file = fileUploads;
    }

    if (banner?.file_name) {
      result.banner = await this.minioService.checkFileExist(
        process.env.MINIO_BUCKET_NAME!,
        `${process.env.MINIO_PATH_FILE}/${banner.file_name}`,
      );
    }
    await this.restructureStages(result.stages);
    await this.validateRequirements(requirements, personelData, result);
    this.setEligibleMessage(participants.length > 0, result);

    return result;
  }
}
