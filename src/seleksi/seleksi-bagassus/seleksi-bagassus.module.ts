import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import { SeleksiBagassusController } from './controller/seleksi-bagassus.controller';
import { SeleksiBagassusService } from './service/seleksi-bagassus.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SeleksiBagassusController],
  providers: [SeleksiBagassusService, MinioService, ExcelService],
})
export class SeleksiBagassusModule {}
