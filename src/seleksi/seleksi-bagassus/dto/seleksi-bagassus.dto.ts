import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Validate,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsSequentialDateRange } from 'src/core/validator/date-range.validator';
import { comparison_type_enum, selection_type_enum } from '@prisma/client';
import { PromosiJabatanParticipantStatusEnum } from '../../../core/enums/promosi-jabatan.enum';

export enum ApprovalStatusEnum {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
}

class StageDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  existing_id?: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  start_date: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  end_date: Date;
}

class RequirementsDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  existing_id?: number;

  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  id: number;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean = false;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsNotEmpty()
  @IsEnum(comparison_type_enum)
  comparison_type: comparison_type_enum;
}

class RequirementFilesDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  existing_id?: number;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean = false;

  @IsOptional()
  @IsArray()
  extensions?: string[] = [];

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_size: number = 0;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_size: number = 0;
}

export class SeleksiBagassusCreateDto {
  @IsNotEmpty({ message: 'Judul seleksi tidak boleh kosong' })
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsEnum(selection_type_enum)
  type: selection_type_enum;

  @IsNotEmpty({ message: 'Tahapan seleksi tidak boleh kosong' })
  @IsArray()
  @ValidateNested({ each: true })
  @Validate(IsSequentialDateRange)
  @Type(() => StageDto)
  stages: StageDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequirementsDto)
  requirements?: RequirementsDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequirementFilesDto)
  requirement_files?: RequirementFilesDto[];
}

export class SeleksiBagassusFilter {
  @ValidateIf(({ value }) => !!value)
  @IsEnum(selection_type_enum)
  type?: selection_type_enum;

  @IsOptional()
  @IsBoolean()
  show_closed?: boolean;

  user_id?: number;
}

export class SeleksiBagassusListFilter {
  @IsOptional()
  @IsString()
  name: string;

  @ValidateIf(({ value }) => !!value)
  @IsEnum(PromosiJabatanParticipantStatusEnum)
  status?: string;
}

export class ApprovalSubmissionDto {
  @IsNotEmpty()
  @IsArray()
  @IsNumber({ allowNaN: false }, { each: true })
  @ValidateIf((obj) => obj.status === ApprovalStatusEnum.REJECT)
  @ArrayMinSize(1)
  @Type(() => Number)
  file_ids: Array<number>;

  @IsNotEmpty()
  @IsEnum(ApprovalStatusEnum)
  status: ApprovalStatusEnum;

  @ValidateIf((obj) => obj.status === ApprovalStatusEnum.REJECT)
  @IsNotEmpty()
  @IsString()
  reason?: string;
}
