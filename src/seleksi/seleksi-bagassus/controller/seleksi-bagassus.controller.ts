import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UnprocessableEntityException,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { FieldValidatorPipe } from '../../../core/validator/field.validator';
import {
  ApprovalSubmissionDto,
  SeleksiBagassusCreateDto,
  SeleksiBagassusFilter,
  SeleksiBagassusListFilter,
} from '../dto/seleksi-bagassus.dto';
import { SeleksiBagassusService } from '../service/seleksi-bagassus.service';

@UseGuards(JwtAuthGuard)
// @UsePipes(
//   new ValidationPipe({
//     transform: true,
//     whitelist: true,
//     stopAtFirstError: true,
//   }),
// )
@Controller('seleksi-bagassus')
export class SeleksiBagassusController {
  private readonly logger = new Logger(SeleksiBagassusController.name);

  constructor(
    private readonly seleksiBagassusService: SeleksiBagassusService,
  ) {}

  @Get('/requirements')
  @HttpCode(HttpStatus.OK)
  async findOperatorRequirements(@Req() req: any) {
    this.logger.log(`Entering ${this.findOperatorRequirements.name}`);
    const response = await this.seleksiBagassusService.getRequirements(req);
    this.logger.log(
      `Leaving ${this.findOperatorRequirements.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  /** ============================ OPERATOR ============================ */

  @Post('/operator')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async createOperatorBagassus(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: SeleksiBagassusCreateDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    if (!files?.length) {
      throw new UnprocessableEntityException({
        status: HttpStatus.UNPROCESSABLE_ENTITY,
        message: { file: 'File is required' },
      });
    }

    this.logger.log(
      `Entering ${this.createOperatorBagassus.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.seleksiBagassusService.createSeleksi(
      req?.user,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.createOperatorBagassus.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/operator/:selection_id')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async updateOperatorBagassus(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selection_id: number,
    @Body(new FieldValidatorPipe()) body: SeleksiBagassusCreateDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updateOperatorBagassus.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.seleksiBagassusService.updateSeleksi(
      req?.user,
      selection_id,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.updateOperatorBagassus.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator')
  @HttpCode(HttpStatus.OK)
  async listSeleksiBagassusOperator(
    @Req() req: any,
    @Query() filter: SeleksiBagassusFilter,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query() paginationData: PaginationDto,
  ) {
    filter.user_id = req.user?.id;
    const data = { ...filter, ...searchandsortData, ...paginationData };
    this.logger.log(
      `Entering ${this.listSeleksiBagassusOperator.name} with params data: ${JSON.stringify(data)}`,
    );
    const response = await this.seleksiBagassusService.getSeleksiList(
      req,
      filter,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.listSeleksiBagassusOperator.name} with params data: ${JSON.stringify(data)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/:selection_id')
  @HttpCode(HttpStatus.OK)
  async detailSeleksiBagassusOperator(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
  ) {
    this.logger.log(
      `Entering ${this.detailSeleksiBagassusOperator.name} with selection id: ${selectionId}}`,
    );
    const response = await this.seleksiBagassusService.getDetail(
      req,
      selectionId,
    );
    this.logger.log(
      `Leaving ${this.detailSeleksiBagassusOperator.name} with selection id: ${selectionId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/stages/:selection_id')
  @HttpCode(HttpStatus.OK)
  async stagesDetailSeleksiBagassusOperator(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
  ) {
    this.logger.log(
      `Entering ${this.stagesDetailSeleksiBagassusOperator.name} with selection id: ${selectionId}}`,
    );
    const response = await this.seleksiBagassusService.getStages(
      req,
      selectionId,
    );
    this.logger.log(
      `Leaving ${this.stagesDetailSeleksiBagassusOperator.name} with selection id: ${selectionId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/:selection_id/:stage_id')
  @HttpCode(HttpStatus.OK)
  async detailSeleksiParticipantBagassusOperator(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @Param('stage_id', ParseIntPipe) stageId: number,
    @Query() filter: SeleksiBagassusListFilter,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.detailSeleksiParticipantBagassusOperator.name} with selection id: ${selectionId}, stage id: ${stageId}, pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.seleksiBagassusService.getParticipantsList(
      req,
      selectionId,
      stageId,
      paginationData,
      searchAndSortData,
      filter,
    );
    this.logger.log(
      `Leaving ${this.detailSeleksiParticipantBagassusOperator.name} with selection id: ${selectionId}, stage id: ${stageId}, pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('/operator/close/:selection_id')
  @HttpCode(HttpStatus.OK)
  async closeSeleksiBagassus(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) id: number,
  ) {
    this.logger.log(`Entering ${this.closeSeleksiBagassus.name}`);
    const response = await this.seleksiBagassusService.close(req, id);
    this.logger.log(
      `Leaving ${this.closeSeleksiBagassus.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/approval/:registration_number')
  @HttpCode(HttpStatus.OK)
  async submissionApprovalSeleksiBagassus(
    @Req() req: any,
    @Param('registration_number') registrationNumber: string,
    @Body() body: ApprovalSubmissionDto,
  ) {
    this.logger.log(`Entering ${this.submissionApprovalSeleksiBagassus.name}`);
    const response = await this.seleksiBagassusService.submissionApproval(
      req,
      registrationNumber,
      body,
    );
    this.logger.log(
      `Leaving ${this.submissionApprovalSeleksiBagassus.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/import-kesehatan/:selection_id')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  @HttpCode(HttpStatus.OK)
  async importKesehatanSeleksiBagassus(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.importKesehatanSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${file ? 1 : 0}`,
    );
    const response = await this.seleksiBagassusService.importKesehatan(
      req,
      selectionId,
      file,
    );
    this.logger.log(
      `Leaving ${this.importKesehatanSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${file ? 1 : 0} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/import-ujian/:selection_id')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  @HttpCode(HttpStatus.OK)
  async importUjianSeleksiBagassus(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.importUjianSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${file ? 1 : 0}`,
    );
    const response = await this.seleksiBagassusService.importExamResult(
      req,
      selectionId,
      file,
    );
    this.logger.log(
      `Leaving ${this.importUjianSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${file ? 1 : 0} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  /** ============================ PERSONEL ============================ */
  @Post('/personel/:selection_id')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async submissionSeleksiBagassus(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.submissionSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${files?.length}`,
    );
    const response = await this.seleksiBagassusService.personelSubmission(
      req,
      selectionId,
      files,
    );
    this.logger.log(
      `Leaving ${this.submissionSeleksiBagassus.name} with selection id: ${selectionId} and files length: ${files?.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/personel/:registration_number')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async updateSubmissionSeleksiBagassus(
    @Req() req: any,
    @Param('registration_number') registrationNumber: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updateSubmissionSeleksiBagassus.name} with selection id: ${registrationNumber} and files length: ${files?.length}`,
    );
    const response =
      await this.seleksiBagassusService.updateSubmissionSeleksiBagassus(
        req,
        registrationNumber,
        files,
      );
    this.logger.log(
      `Leaving ${this.updateSubmissionSeleksiBagassus.name} with selection id: ${registrationNumber} and files length: ${files?.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel')
  @HttpCode(HttpStatus.OK)
  async listSeleksiBagassusPersonel(
    @Req() req: any,
    @Query() filter: SeleksiBagassusFilter,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query() paginationData: PaginationDto,
  ) {
    const data = { ...filter, ...searchandsortData, ...paginationData };
    this.logger.log(
      `Entering ${this.listSeleksiBagassusPersonel.name} with pagination data: ${JSON.stringify(data)}`,
    );
    const response = await this.seleksiBagassusService.getSeleksiList(
      req,
      filter,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.listSeleksiBagassusPersonel.name} with pagination data: ${JSON.stringify(data)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/:selection_id')
  @HttpCode(HttpStatus.OK)
  async detailSeleksiBagassusPersonel(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
  ) {
    this.logger.log(
      `Entering ${this.detailSeleksiBagassusPersonel.name} with selection id: ${selectionId}`,
    );
    const response = await this.seleksiBagassusService.getSeleksiPersonelDetail(
      req,
      selectionId,
    );
    this.logger.log(
      `Leaving ${this.detailSeleksiBagassusPersonel.name} with selection id: ${selectionId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
