import { forwardRef, Module } from '@nestjs/common';
import { SurveyService } from './service/survey.service';
import { SurveyController } from './controller/survey.controller';
import { SurveyPersonelService } from './service/survey-personel.service';
import { SurveyOperatorService } from './service/survey-operator.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { SurveyEventListenerService } from './service/survey.events.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SurveyController],
  providers: [
    SurveyService,
    SurveyPersonelService,
    SurveyOperatorService,
    MinioService,
    SurveyEventListenerService,
  ],
})
export class SurveyModule {}
