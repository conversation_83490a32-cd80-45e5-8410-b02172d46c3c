import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import * as moment from 'moment';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateSurveyDTO,
  PublishSurveyDto,
  UpdateSurveyDto,
} from '../dto/survey.dto';
import { Prisma } from '@prisma/client';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class SurveyOperatorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async createSurveyDraft(req, body: CreateSurveyDTO) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Create survey hanya bisa dilakukan oleh admin`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const { title, description, countdown, questions } = body;
      let { start_date, end_date } = body;

      start_date = moment(start_date).startOf('day').toISOString();

      if (end_date) {
        end_date = moment(end_date).endOf('day').toISOString();
        const now = moment(new Date()).endOf('day').toDate();
        if (new Date(end_date) < now) {
          throw new BadRequestException(
            'end_date must be greater or equal to today',
          );
        }
      }

      const surveyData = {
        title,
        description,
        start_date: new Date(start_date),
        end_date: end_date ? new Date(end_date) : undefined,
        countdown_second: countdown,
        question_list: questions as Prisma.JsonArray,
        question_count: questions.length,
      };

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const createdSurvey = await tx.survey_question.create({
          data: {
            ...surveyData,
            status_id: 1,
            created_by_personel: req.user.personel_id,
            created_at: new Date(),
            updated_at: new Date(),
          },
        });

        return createdSurvey;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateSurvey(req: any, id: number, payload: UpdateSurveyDto) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Update survey hanya bisa dilakukan oleh admin`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const survey = await this.prisma.survey_question.findFirst({
        where: { id, deleted_at: null },
      });
      if (!survey) {
        throw new BadRequestException(`Survey tidak ditemukan!`);
      }

      const notDraft = [2, 3].includes(Number(survey.status_id));
      if (notDraft) {
        throw new BadRequestException(
          `Update gagal! Hanya bisa update survey dengan status draft`,
        );
      }

      const { title, description, start_date, end_date, countdown, questions } =
        payload;

      const surveyData = {
        title,
        description,
        start_date: start_date ? new Date(start_date) : undefined,
        end_date: end_date ? new Date(end_date) : undefined,
        countdown_second: countdown,
        question_list: questions as Prisma.JsonArray,
        question_count: questions.length,
      };

      const queryResult = await this.prisma.survey_question.update({
        where: { id },
        data: {
          ...surveyData,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async deleteSurvey(req: any, id: number) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Deaktivasi survey hanya bisa dilakukan oleh admin`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const queryResult = await this.prisma.survey_question.update({
        where: { id },
        data: {
          deleted_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async publishSurvey(req: any, payload: PublishSurveyDto) {
    try {
      const { survey_id, ...destinations } = payload;

      const destGroupLength = destinations?.destination_group?.length;
      const destSatuanLength = destinations?.destination_satuan?.length;
      const destPangkatLength = destinations?.destination_pangkat?.length;
      const destBagianLength = destinations?.destination_bagian?.length;

      const isAllDestSelected = [
        destGroupLength,
        destSatuanLength,
        destPangkatLength,
      ].every((value) => value > 0 || value != undefined);
      if (isAllDestSelected)
        throw new BadRequestException(
          `Responden Satuan dan Group tidak boleh dipilih bersamaan!`,
        );

      const isGroup = [destGroupLength].some(
        (value) => value > 0 || value != undefined,
      );

      const isSatuan = [destSatuanLength, destPangkatLength].every(
        (value) => value > 0 || value != undefined,
      );

      if (!isGroup) {
        if (!isSatuan)
          throw new BadRequestException(
            `Satuan, Pangkat dan Bagian harus diisi!`,
          );
      }

      const survey = await this.prisma.survey_question.findFirst({
        where: { id: survey_id, deleted_at: null },
      });

      if (!survey) throw new BadRequestException(`Survey tidak ditemukan!`);

      if (survey.status_id === BigInt(2))
        throw new ConflictException(`Survey sudah dipublish`);

      if (survey.status_id === BigInt(3))
        throw new ForbiddenException(
          `Survey tidak dapat dipublish karena sudah dinonaktifkan.`,
        );

      if (survey.status_id !== BigInt(1))
        throw new BadRequestException(`Survey tidak ditemukan`);

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const destinationObj = this._surveyDestination(destinations);

        const updatedSurvey = await tx.survey_question.update({
          where: { id: survey_id },
          data: {
            status_id: 2,
            updated_at: new Date(),
            survey_destination: {
              createMany: { data: destinationObj },
            },
          },
        });

        return updatedSurvey;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getSurveyResponses(
    req: any,
    id: number,
    paginationData,
    searchandsortData,
  ) {
    try {
      const limit = Number(paginationData.limit || 100);
      const page = Number(paginationData.page || 1);

      const where = {
        deleted_at: null,
        question_id: id,
        submission_end: { not: null }, // only get finished answers. submission_end = null, indicates that the answer is not completed yet
      };

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'date' | 'number' };
      } = {
        nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
        email: { field: 'personel.email', type: 'string' },
        created_at: { field: 'created_at', type: 'date' },
      };

      const { orderBy } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.survey_answers.count({
          where,
        }),
        this.prisma.survey_answers.findMany({
          where,
          select: {
            id: true,
            personel: {
              select: {
                nama_lengkap: true,
                email: true,
              },
            },
            created_at: true,
          },
          take: limit,
          skip: limit * (page - 1),
          orderBy: orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: { responses: queryResult, page, totalPage, totalData },
      };
    } catch (err) {
      throw err;
    }
  }

  async getSurveyResponse(req: any, answerId: number) {
    try {
      const queryResult = await this.prisma.survey_answers.findFirst({
        where: { id: answerId },
        select: {
          id: true,
          submission_start: true,
          submission_end: true,
          list: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getManySurveys(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'date' | 'number' };
      } = {
        title: { field: 'title', type: 'string' },
        created_by: { field: 'survey_personel.nama_lengkap', type: 'string' },
        created_at: { field: 'created_at', type: 'date' },
        status: { field: 'survey_question_status.name', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, surveys] = await this.prisma.$transaction([
        this.prisma.survey_question.count({
          where,
        }),
        this.prisma.survey_question.findMany({
          select: {
            id: true,
            title: true,
            created_at: true,
            survey_personel: {
              select: {
                nama_lengkap: true,
              },
            },
            survey_question_status: {
              select: {
                name: true,
              },
            },
            survey_answers: {
              select: {
                id: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where,
        }),
      ]);

      const queryResult = surveys.map((survey) => ({
        id: survey.id,
        title: survey.title,
        created_by: survey.survey_personel.nama_lengkap,
        created_at: survey.created_at,
        status: survey.survey_question_status.name,
        responses: survey.survey_answers.length,
      }));

      // if sorting by response_count
      if (sort_column === 'responses') {
        queryResult.sort((a, b) => {
          const compare = a.responses - b.responses;
          return sort_desc === 'asc' ? compare : -compare;
        });
      }

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page,
        totalPage,
        totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOneSurvey(req, id: number) {
    try {
      const queryResult = await this.prisma.survey_question.findFirst({
        where: {
          id,
          deleted_at: null,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async deactivateSurvey(req: any, id: number) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Deaktivasi survey hanya bisa dilakukan oleh admin`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const queryResult = await this.prisma.survey_question.update({
        where: { id },
        data: { status_id: 3 },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async setEndSurvey(req: any, id: number) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Pengakhiran masa survey hanya bisa dilakukan oleh admin!`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const survey = await this.prisma.survey_question.findFirst({
        where: {
          id,
          deleted_at: null,
        },
        select: { id: true },
      });

      if (!survey) {
        throw new BadRequestException(`Survey tidak ditemukan!`);
      }

      const queryResult = await this.prisma.survey_question.update({
        where: { id },
        data: {
          end_date: new Date(),
          updated_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private _surveyDestination(body: Record<string, Array<number>>) {
    try {
      const time = {
        created_at: new Date(),
        updated_at: new Date(),
      };
      const destination = [];
      // handle destination group first
      if (body.destination_group?.length) {
        body.destination_group.forEach((group) => {
          destination.push({
            survey_group_id: group,
            ...time,
          });
        });
        return destination;
      }

      body.destination_satuan.forEach((satuan) => {
        body.destination_pangkat.forEach((pangkat) => {
          destination.push({
            satuan_id: satuan,
            pangkat_id: pangkat,
            // bagian_id: bagian,
            ...time,
          });
          // body.destination_bagian.forEach((bagian) => {
          //
          // });
        });
      });

      return destination;
    } catch (err) {
      throw err;
    }
  }
}
