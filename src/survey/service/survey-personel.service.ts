import * as moment from 'moment';
import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateSurveyAnswer } from '../dto/survey.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SurveyEventsEnum } from '../../core/enums/survey.enum';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class SurveyPersonelService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;
      const personel_id = req['user']['personel_id'];
      const satuan_id = req['user']['satuan_id'];
      const pangkat_id = req['user']['pangkat_id'];

      // development variables
      // const personel_id = 304504;
      // const satuan_id = 7452014;
      // const pangkat_id = 19;
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' | 'date' | 'number' };
      } = {
        title: { field: 'title', type: 'string' },
        created_at: { field: 'created_at', type: 'date' },
        question_count: { field: 'question_count', type: 'number' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const group = await this.prisma.survey_group_personel.findMany({
        where: {
          personel_id: personel_id,
        },
        select: {
          group_id: true,
        },
      });

      const surveys = await this.prisma.survey_destination.findMany({
        where: {
          OR: [
            {
              AND: {
                satuan_id: satuan_id,
                pangkat_id: pangkat_id,
              },
            },
            {
              survey_group_id: {
                in: group.map((item) => Number(item.group_id)),
              },
            },
          ],
        },
        select: {
          survey_id: true,
        },
      });
      const nonDuplicateSurveyIds = Array.from(
        new Set(surveys.map((survey) => Number(survey.survey_id))),
      );

      const filter = {
        ...where,
        id: { in: nonDuplicateSurveyIds },
        status_id: { not: 1 },
      };

      const [totalData, _surveysData] = await this.prisma.$transaction([
        this.prisma.survey_question.count({ where: filter }),
        this.prisma.survey_question.findMany({
          where: filter,
          select: {
            id: true,
            title: true,
            created_at: true,
            survey_question_status: {
              select: {
                id: true,
                name: true,
              },
            },
            question_count: true,
          },
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const surveysWithNewLabel = await this._assignNewLabel(
        personel_id,
        _surveysData,
      );

      const queryResult = await this._updateSurveyStatus(
        personel_id,
        surveysWithNewLabel,
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getPersonelSurveys(req: any) {
    const personelId = Number(req.user.personel_id);

    try {
      const surveys = await this.prisma.survey_personel.findMany({
        where: { personel_id: personelId },
        select: { survey_id: true },
        orderBy: { created_at: 'asc' },
      });

      const surveyIds = surveys.map((survey) => survey.survey_id);
      const detailSurvey = await this.prisma.survey_question.findMany({
        where: { id: { in: surveyIds } },
        select: {
          id: true,
          start_date: true,
          end_date: true,
          deleted_at: true,
        },
      });

      let queryResult: { count: number; ids: BigInt[] } = {
        count: 0,
        ids: [],
      };
      for (let x = 0; x < detailSurvey.length; x++) {
        const survey = detailSurvey[x];
        let isOpenSurvey = false;

        if (
          moment().isAfter(survey.start_date) &&
          moment().isBefore(survey.end_date)
        )
          isOpenSurvey = true;

        if (survey.deleted_at != null) isOpenSurvey = true;

        if (isOpenSurvey) {
          queryResult.count++;
          queryResult.ids.push(survey.id);
        }
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getSurvey(req: any, id: number) {
    try {
      const queryResult = await this.prisma.survey_question.findFirst({
        where: { id },
        select: {
          id: true,
          title: true,
          description: true,
          countdown_second: true,
          end_date: true,
          question_count: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async startSurveySession(surveyId: number, req: any) {
    try {
      const [survey, answer] = await Promise.all([
        this.prisma.survey_question.findFirst({
          where: { id: surveyId },
          select: { question_list: true, end_date: true },
        }),
        this.prisma.survey_answers.findFirst({
          where: {
            question_id: surveyId,
            personel_id: req.user.personel_id,
          },
          select: { id: true },
        }),
      ]);

      if (!survey) {
        throw new BadRequestException(`Survey tidak ditemukan`);
      }

      if (new Date() > new Date(survey.end_date)) {
        throw new BadRequestException(`Periode pengisian survey telah ditutup`);
      }

      if (answer) {
        return answer;
      }
      const questions = survey.question_list as Prisma.JsonArray;
      const questionWithAnswerType = await Promise.all(
        questions.map(async (question) => {
          const questionObj = question as Prisma.JsonObject;

          const answerType = await this.prisma.survey_question_type.findFirst({
            where: { question_type: question['type'] },
            select: { answer_type: true },
          });

          questionObj['answer'] = {
            type: answerType.answer_type,
            value: null,
          };

          return questionObj;
        }),
      );

      const queryData = await this.prisma.survey_answers.create({
        data: {
          personel_id: req.user.personel_id,
          question_id: surveyId,
          list: questionWithAnswerType,
          submission_start: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
        },
        select: { id: true },
      });

      const queryResult = {
        answer_id: queryData.id,
      };
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getQuestion(req: any, surveyId: number, answerId: number) {
    try {
      const queryResult = await this.prisma.survey_answers.findFirst({
        where: {
          id: answerId,
          question_id: surveyId,
          personel_id: req.user.personel_id,
          deleted_at: null,
        },
        select: {
          list: true,
        },
      });

      if (!queryResult) {
        throw new BadRequestException(`Survey tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult.list,
      };
    } catch (err) {
      throw err;
    }
  }

  async saveAnswer(
    surveyId: number,
    answerId: number,
    payload: CreateSurveyAnswer,
    req: any,
  ) {
    const qNum = +payload.q_num;
    try {
      await this._validateQuestionNumber(surveyId, qNum);
      const survey = await this.prisma.survey_answers.findFirst({
        where: {
          id: answerId,
          question_id: surveyId,
          personel_id: req.user.personel_id,
          deleted_at: null,
        },
        select: {
          id: true,
          list: true,
          submission_end: true,
          submission_start: true,
        },
      });

      if (!survey) {
        throw new BadRequestException(`Survey tidak ditemukan`);
      }

      if (survey.submission_end) {
        throw new BadRequestException(
          `Survey sudah disubmit. Tidak bisa isi jawaban`,
        );
      }

      if (!survey.submission_start) {
        throw new BadRequestException(
          `Survey belum dimulai. Tidak bisa isi jawaban`,
        );
      }

      const qIdx = qNum - 1;
      const questions = survey.list as Prisma.JsonArray;
      const queryResult = questions[qIdx] as Prisma.JsonObject;
      queryResult['answer']['value'] = payload.answer;
      questions[qIdx] = queryResult;

      const updatedAnswer = await this.prisma.survey_answers.update({
        where: { id: answerId },
        data: {
          list: questions,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          updatedAnswer,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async submitAnswers(surveyId: number, answerId: number, req: any) {
    try {
      const question = await this.prisma.survey_question.findFirst({
        where: { id: surveyId },
        select: { end_date: true },
      });

      if (new Date() > new Date(question.end_date)) {
        throw new BadRequestException(`Periode pengisian survey telah ditutup`);
      }

      const survey = await this.prisma.survey_answers.findFirst({
        where: {
          id: answerId,
          question_id: surveyId,
          personel_id: req.user.personel_id,
          deleted_at: null,
        },
        select: { id: true, submission_start: true, submission_end: true },
      });

      if (!survey) {
        throw new BadRequestException(`Survey tidak ditemukan`);
      }

      if (survey.submission_end) {
        throw new BadRequestException(
          `Submit gagal. Survey sudah pernah disubmit!`,
        );
      }

      if (!survey.submission_start) {
        throw new BadRequestException(
          `Submit gagal. Pengisian survey belum dimulai`,
        );
      }

      const queryResult = await this.prisma.survey_answers.update({
        where: {
          id: answerId,
        },
        data: {
          submission_end: new Date(),
          updated_at: new Date(),
        },
      });

      this.eventEmitter.emit(SurveyEventsEnum.RESPONSE_SUBMITTED, {
        surveyId,
        personelId: Number(req.user.personel_id),
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _assignNewLabel(
    personel_id: number,
    surveys: Record<string, any>[],
  ) {
    try {
      const surveyIds = surveys.map((survey) => survey.id);
      const viewLogs = await this.prisma.survey_view_log.findMany({
        where: {
          personel_id,
          survey_id: {
            in: surveyIds,
          },
        },
        select: {
          viewed_at: true,
          survey_id: true,
        },
      });

      const surveyLog = viewLogs.reduce((final, data) => {
        final[Number(data.survey_id)] = data.viewed_at;
        return final;
      }, {});

      const labeled = surveys.map((survey) => {
        if (!(survey.id in surveyLog)) {
          survey.is_new = true;
          return survey;
        }

        const duration = moment.duration(
          moment().diff(moment(surveyLog[survey.id])),
        );
        const hoursDiff = duration.asHours();
        survey.is_new = hoursDiff < 24;
        return survey;
      });

      return labeled;
    } catch (err) {
      throw err;
    }
  }

  private async _updateSurveyStatus(
    personel_id: number,
    surveys: Record<string, any>[],
  ) {
    try {
      const surveyIds = surveys.map((survey) => survey.id);
      const answers = await this.prisma.survey_answers.findMany({
        where: { personel_id, question_id: { in: surveyIds } },
        select: {
          personel_id: true,
          question_id: true,
        },
      });

      const answerLog = answers.reduce((final, data) => {
        final[Number(data.question_id)] = 1;
        return final;
      }, {});

      const answerStatus = new Map();
      answerStatus.set(true, 'Telah Dijawab');
      answerStatus.set(false, 'Belum Dijawab');

      const updatedStatus = surveys.map((survey) => {
        if (Number(survey.status.id) === 3) {
          survey['status'] = survey.status.name;
          return survey;
        }

        survey['status'] = answerStatus.get(survey.id in answerLog);
        return survey;
      });

      return updatedStatus;
    } catch (err) {
      throw err;
    }
  }

  private async _validateQuestionNumber(
    surveyId: number,
    questionNumber: number,
  ) {
    try {
      const survey = await this.prisma.survey_question.findFirst({
        where: { id: surveyId },
        select: {
          question_count: true,
        },
      });

      if (questionNumber > survey.question_count) {
        throw new BadRequestException(
          `Pertanyaan ${questionNumber} tidak tersedia!`,
        );
      }

      return survey.question_count;
    } catch (err) {
      throw err;
    }
  }
}
