import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateGroupSurveyDTO } from '../dto/survey.dto';
import * as moment from 'moment';
import * as exceljs from 'exceljs';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class SurveyService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async createGroup(req, body: CreateGroupSurveyDTO) {
    try {
      const queryResult = await this.prisma.$transaction(async (tx) => {
        const { nama_group, personel_id } = body;

        if (!nama_group)
          throw new BadRequestException('Group name cannot be empty!');

        if (!personel_id.length)
          throw new BadRequestException('Personel cannot be empty!');

        if (personel_id.filter((item) => !Number.isInteger(item)).length)
          throw new BadRequestException('Personel must be number!');

        if (await tx.survey_group.findFirst({ where: { nama: nama_group } }))
          throw new BadRequestException('Group name already exists!');

        const createGroup = await tx.survey_group.create({
          data: {
            nama: nama_group,
            survey_group_personel: {
              createMany: {
                data: [...new Set(personel_id)].map((values) => {
                  return {
                    personel_id: values,
                  };
                }),
              },
            },
          },
          include: {
            survey_group_personel: true,
          },
        });

        return createGroup;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getGroupList(req, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'nama', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.survey_group.count({
          where: where,
        }),
        this.prisma.survey_group.findMany({
          select: {
            id: true,
            nama: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getSummary(req: any) {
    try {
      const queryResult = await this.prisma.survey_agg.findFirst({
        where: { id: 1 },
        select: {
          total: true,
          respondens: true,
          responses: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getHistory(req: any, days: number) {
    try {
      const nDaysAgo = moment().subtract(days, 'days').format('YYYY-MM-DD');
      const past = new Date(nDaysAgo);
      past.setHours(0, 0, 0, 0);
      const surveys = await this.prisma.survey_question.findMany({
        where: {
          created_at: { gte: past },
          status_id: { in: [2, 3] },
          deleted_at: null,
        },
        select: {
          id: true,
          created_at: true,
        },
      });

      // create x axis for plotting graph - timeseries N days
      const queryResult = {};
      let dt = 0;
      for (dt; dt < days; dt++) {
        const dtKey = moment().subtract(dt, 'days').format('YYYY-MM-DD');
        queryResult[dtKey] = 0;
      }

      // assign survey count for each date point
      surveys.forEach((survey) => {
        const dtKey = moment(survey.created_at)
          .utcOffset(7)
          .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
          .format('YYYY-MM-DD');
        queryResult[dtKey] += 1;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getQuestionTypes(req: any) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Data hanya bisa diakses oleh admin!`,
        HttpStatus.FORBIDDEN,
      );
    }

    const queryResult = await this.prisma.survey_question_type.findMany({
      select: { display_name: true, question_type: true },
      orderBy: { id: 'asc' },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SURVEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SURVEY_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getFileExtensions(req: any) {
    const role_tipe: string = req['user']['role_tipe'];
    if (role_tipe.toLowerCase() !== 'admin') {
      throw new HttpException(
        `Data hanya bisa diakses oleh admin!`,
        HttpStatus.FORBIDDEN,
      );
    }

    try {
      const queryResult = await this.prisma.survey_file_extensions.findMany({
        where: { deleted_at: null },
        select: { display_name: true, extensions: true },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getResponseSurveyExcel(req, res, survey_id: number) {
    try {
      const survey = await this.prisma.survey_question.findUnique({
        where: { id: +survey_id },
        include: {
          survey_answers: {
            include: {
              personel: true,
            },
          },
        },
      });

      const headerSurvey = survey.question_list as object[];
      if (!survey) throw new BadRequestException('Survey Not Found!');

      const workbook = new exceljs.Workbook();
      const worksheet = workbook.addWorksheet('Responses Survey');
      const header = [
        'No',
        'NRP',
        'Nama',
        ...headerSurvey.map((value: any) => value.title),
      ];
      const headerRow = worksheet.addRow(header);

      headerRow.eachCell((cell, colNumber) => {
        cell.font = {
          bold: true,
          size: 13,
        };
      });

      survey.survey_answers.forEach((answer, index) => {
        const list = answer.list as object[];
        const listAnswer = list.map((item: any, index) => {
          const value = item.answer.value;
          const type = typeof item.answer.value;
          if (type === 'object') {
            if (typeof value[0] != 'object') {
              return value.join(', ');
            } else {
              return value.map((item: any) => item.filename).join(', ');
            }
          } else {
            return value;
          }
        });
        const row = [
          index + 1,
          answer.personel.nrp,
          answer.personel.nama_lengkap,
          ...listAnswer,
        ];

        worksheet.addRow(row);
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const filename = `Responses Survey - ${survey.title}`;

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_WRITE_EXCEL as ConstantLogType,
          message,
          survey,
        ),
      );

      return { buffer, filename };
    } catch (error) {
      throw error;
    }
  }

  async uploadFiles(
    req: any,
    surveyId: number,
    answerId: number,
    files: Express.Multer.File[],
  ) {
    try {
      if (Number.isNaN(surveyId))
        throw new BadRequestException(`Survey ID not found`);
      if (Number.isNaN(answerId))
        throw new BadRequestException(`Answer ID not found`);

      if (!files || !Array.isArray(files) || files.length === 0)
        throw new BadRequestException('Files not found or invalid');

      const errors: { file: string; reason: string }[] = [];

      const uploadedFiles = await Promise.all(
        files.map(async (file) => {
          try {
            const uploaded = await this.minioService.uploadFile(file);
            if (!uploaded.Key || !uploaded.Location)
              throw new InternalServerErrorException(
                `Failed to upload file ${file.originalname}`,
              );

            delete file.buffer;
            delete file.fieldname;

            return {
              ...file,
              key: uploaded.Key,
              url: uploaded.Location,
              filename: uploaded.filename,
              survey_id: surveyId,
              answer_id: answerId,
            };
          } catch (err) {
            errors.push({
              file: file.originalname,
              reason: err.message || 'Failed to upload file',
            });
            return null;
          }
        }),
      );

      if (errors.length > 0)
        throw new InternalServerErrorException(errors[0].reason);

      const queryResult = uploadedFiles.filter((file) => file !== null);
      if (queryResult.length > 0) {
        await this.prisma.survey_files.createMany({
          data: queryResult,
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SURVEY_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SURVEY_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }
}
