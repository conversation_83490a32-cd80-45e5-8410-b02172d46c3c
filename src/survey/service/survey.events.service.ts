import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SurveyEventsEnum } from '../../core/enums/survey.enum';
import { IRemoveRespondenEvent } from '../../core/interfaces/survey.interface';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';

@Injectable()
export class SurveyEventListenerService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  @OnEvent(SurveyEventsEnum.SURVEY_CREATED)
  async incrementSurveyCount() {
    try {
      await this.prisma.survey_agg.updateMany({
        data: {
          total: {
            increment: 1,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.SURVEY_CREATED)
  async incrementRespondenCount(surveyId: number) {
    try {
      const { count } = await this._getRespondens(surveyId);
      await this.prisma.survey_agg.updateMany({
        data: {
          respondens: {
            increment: count,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.SURVEY_CREATED)
  async insertRespondens(surveyId: number) {
    try {
      const { respondens } = await this._getRespondens(surveyId);
      const data = respondens.map((responden) => ({
        survey_id: surveyId,
        personel_id: responden,
        created_at: new Date(),
      }));

      await this.prisma.survey_personel.createMany({
        data,
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.SURVEY_DELETED)
  async decrementSurveyCount() {
    try {
      await this.prisma.survey_agg.updateMany({
        data: {
          total: {
            decrement: 1,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.SURVEY_DELETED)
  async decrementRespondenCount(surveyId: number) {
    try {
      const { count } = await this._getRespondens(surveyId);
      await this.prisma.survey_agg.updateMany({
        data: {
          respondens: {
            decrement: count,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.RESPONSE_SUBMITTED)
  async incrementResponsesCount() {
    try {
      await this.prisma.survey_agg.updateMany({
        data: {
          responses: {
            increment: 1,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  @OnEvent(SurveyEventsEnum.RESPONSE_SUBMITTED)
  async removeResponden(payload: IRemoveRespondenEvent) {
    try {
      const { surveyId, personelId } = payload;
      await this.prisma.survey_personel.delete({
        where: {
          personel_id_survey_id: {
            survey_id: surveyId,
            personel_id: personelId,
          },
        },
      });
    } catch (err) {
      throw err;
    }
  }

  private async _getRespondens(surveyId: number) {
    try {
      const destinations = await this.prisma.survey_destination.findMany({
        where: { survey_id: surveyId, deleted_at: null },
        select: {
          satuan_id: true,
          pangkat_id: true,
          survey_group_id: true,
        },
      });

      let totalCount = 0;
      const respondens = [];
      for (const { survey_group_id, satuan_id, pangkat_id } of destinations) {
        // handle personel count assigned by group
        if (survey_group_id) {
          const personelsGroup =
            await this.prisma.survey_group_personel.findMany({
              where: { group_id: survey_group_id },
              select: { personel_id: true },
            });

          totalCount += personelsGroup.length;
          personelsGroup.forEach((personel) =>
            respondens.push(personel.personel_id),
          );
          continue;
        }

        // handle personel count assigned satuan and pangkat
        const personelSatuanJabatan = await this.prisma.personel.findMany({
          where: {
            jabatan_personel: {
              some: { jabatans: { satuan: { id: satuan_id } } },
            },
            pangkat_personel: {
              some: { pangkat: { id: pangkat_id } },
            },
          },
          select: {
            id: true,
            pangkat_personel: {
              select: {
                pangkat: {
                  select: {
                    id: true,
                  },
                },
              },
              orderBy: {
                tmt: 'desc',
              },
              take: 1,
            },
            jabatan_personel: {
              select: {
                jabatans: {
                  select: {
                    id: true,
                    satuan: {
                      select: {
                        id: true,
                      },
                    },
                  },
                },
              },
              orderBy: {
                tmt_jabatan: 'desc',
              },
              take: 1,
            },
          },
        });
        totalCount += personelSatuanJabatan.length;
        personelSatuanJabatan.forEach((personel) =>
          respondens.push(personel.id),
        );
      }

      return {
        count: totalCount,
        respondens,
      };
    } catch (err) {
      throw err;
    }
  }
}
