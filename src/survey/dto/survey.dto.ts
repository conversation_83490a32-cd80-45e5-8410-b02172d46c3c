import { PartialType } from '@nestjs/mapped-types';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateGroupSurveyDTO {
  @IsString()
  @IsNotEmpty()
  nama_group: string;

  @IsArray()
  @IsNumber({}, { each: true })
  personel_id: number[];
}

export class CreateSurveyDTO {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  start_date: string;

  @IsOptional()
  end_date?: string;

  @IsOptional()
  countdown: number;

  @IsOptional()
  questions: Record<string, any>[];

  @IsOptional()
  destination_satuan: number[];

  @IsOptional()
  destination_pangkat: number[];

  @IsOptional()
  destination_group: number[];
}

export class UpdateSurveyDto extends PartialType(CreateSurveyDTO) {}

export class SurveyQuestionDTO {
  title: string;
  type: string;
  required: boolean;
  options?: string | string[];
  file_options: FileOptionsDTO;
}

export class FileOptionsDTO {
  allowed_extensions: string[];
  max_size: number;
  min_files: number;
}

export class GetSurveyHistoryDto {
  days: number;
}

export class CreateSurveyAnswer {
  q_num: number;
  answer: string | string[] | Record<string, any>[];
}

export class CreateQuestionDto {
  title: string;
  type: string;
  required: boolean;
  options?: string | string[];
  file_options?: CreateFileOptionDto[];
}

export class CreateFileOptionDto {
  allowed_extensions: string[];
  max_size_megabyte: number;
  min_num_files: number;
}

export class PublishSurveyDto {
  @IsNotEmpty()
  survey_id: number;

  @IsOptional()
  destination_satuan?: number[];

  @IsOptional()
  destination_pangkat?: number[];

  @IsOptional()
  destination_bagian?: number[];

  @IsOptional()
  destination_group?: number[];
}
