import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SurveyService } from '../service/survey.service';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  CreateGroupSurveyDTO,
  CreateSurveyAnswer,
  CreateSurveyDTO,
  GetSurveyHistoryDto,
  PublishSurveyDto,
  UpdateSurveyDto,
} from '../dto/survey.dto';
import { SurveyPersonelService } from '../service/survey-personel.service';
import { SurveyOperatorService } from '../service/survey-operator.service';
import { FilesInterceptor } from '@nestjs/platform-express';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SurveyEventsEnum } from '../../core/enums/survey.enum';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { MODULES } from '../../core/constants/module.constant';
import { Response } from 'express';

@Controller('survey')
@Module(MODULES.SURVEY)
@UseGuards(JwtAuthGuard, PermissionGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class SurveyController {
  private readonly logger = new Logger(SurveyController.name);

  constructor(
    private readonly surveyService: SurveyService,
    private readonly surveyPersonelService: SurveyPersonelService,
    private readonly surveyOperatorService: SurveyOperatorService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  // OPERATOR SECTIONS!
  @Get('/group')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getGroupList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getGroupList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.surveyService.getGroupList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getGroupList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/group')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createSurveyGroup(@Req() req: any, @Body() body: CreateGroupSurveyDTO) {
    this.logger.log(
      `Entering ${this.createSurveyGroup.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.surveyService.createGroup(req, body);
    this.logger.log(
      `Leaving ${this.createSurveyGroup.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/question/types')
  @Permission('PERMISSION_READ')
  async getQuestionTypes(@Req() req: any) {
    this.logger.log(`Entering ${this.getQuestionTypes.name}`);
    const response = await this.surveyService.getQuestionTypes(req);
    this.logger.log(
      `Leaving ${this.getQuestionTypes.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/question/extensions')
  @Permission('PERMISSION_READ')
  async getQuestionFileExt(@Req() req: any) {
    this.logger.log(`Entering ${this.getQuestionFileExt.name}`);
    const response = await this.surveyService.getFileExtensions(req);
    this.logger.log(
      `Leaving ${this.getQuestionFileExt.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/summary')
  @Permission('PERMISSION_READ')
  async getSurveySummary(@Req() req: any) {
    this.logger.log(`Entering ${this.getSurveySummary.name}`);
    const response = await this.surveyService.getSummary(req);
    this.logger.log(
      `Leaving ${this.getSurveySummary.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/history')
  @Permission('PERMISSION_READ')
  async getSurveyHistory(
    @Req() req: any,
    @Query() queries: GetSurveyHistoryDto,
  ) {
    this.logger.log(
      `Entering ${this.getSurveyHistory.name} with queries: ${JSON.stringify(queries)}`,
    );
    const response = await this.surveyService.getHistory(
      req,
      Number(queries.days),
    );
    this.logger.log(
      `Leaving ${this.getSurveyHistory.name} with queries: ${JSON.stringify(queries)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/forms')
  @Permission('PERMISSION_READ')
  async getAllOperatorForms(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAllOperatorForms.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.surveyOperatorService.getManySurveys(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getAllOperatorForms.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/form/draft')
  @Permission('PERMISSION_CREATE')
  async createDraftSurvey(@Body() body: CreateSurveyDTO, @Req() req) {
    this.logger.log(
      `Entering ${this.createDraftSurvey.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.surveyOperatorService.createSurveyDraft(
      req,
      body,
    );
    this.logger.log(
      `Leaving ${this.createDraftSurvey.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/form/publish')
  @Permission('PERMISSION_CREATE')
  async publishSurvey(@Req() req: any, @Body() body: PublishSurveyDto) {
    this.logger.log(
      `Entering ${this.publishSurvey.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.surveyOperatorService.publishSurvey(req, body);

    // background processes
    this.eventEmitter.emit(SurveyEventsEnum.SURVEY_CREATED, response.data.id);
    this.logger.log(
      `Leaving ${this.publishSurvey.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/operator/form/:id')
  @Permission('PERMISSION_UPDATE')
  async updateForm(
    @Param('id') surveyId: string,
    @Body() body: UpdateSurveyDto,
    @Req() req,
  ) {
    this.logger.log(
      `Entering ${this.updateForm.name} with survey id: ${surveyId} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.surveyOperatorService.updateSurvey(
      req,
      Number(surveyId),
      body,
    );
    this.logger.log(
      `Leaving ${this.updateForm.name} with survey id: ${surveyId} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Patch('/operator/form/:id')
  @Permission('PERMISSION_UPDATE')
  async deactivateForm(@Req() req: any, @Param('id') surveyId: string) {
    this.logger.log(
      `Entering ${this.deactivateForm.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyOperatorService.deactivateSurvey(
      req,
      +surveyId,
    );
    this.logger.log(
      `Leaving ${this.deactivateForm.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/operator/form/:id')
  @Permission('PERMISSION_DELETE')
  async deleteForm(@Req() req: any, @Param('id') surveyId: string) {
    this.logger.log(
      `Entering ${this.deleteForm.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyOperatorService.deleteSurvey(
      req,
      Number(surveyId),
    );

    this.eventEmitter.emit(SurveyEventsEnum.SURVEY_DELETED, Number(surveyId));
    this.logger.log(
      `Leaving ${this.deleteForm.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/:id')
  @Permission('PERMISSION_READ')
  async getForm(@Req() req: any, @Param('id') surveyId: string) {
    this.logger.log(
      `Entering ${this.getForm.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyOperatorService.getOneSurvey(
      req,
      Number(surveyId),
    );
    this.logger.log(
      `Leaving ${this.getForm.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/:id/end')
  @Permission('PERMISSION_READ')
  async endSurveyPeriod(@Req() req: any, @Param('id') surveyId: string) {
    this.logger.log(
      `Entering ${this.endSurveyPeriod.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyOperatorService.setEndSurvey(
      req,
      Number(surveyId),
    );
    this.logger.log(
      `Leaving ${this.endSurveyPeriod.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/:id/responses')
  @Permission('PERMISSION_READ')
  async getResponses(
    @Req() req: any,
    @Param('id') surveyId: string,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getResponses.name} with survey id: ${surveyId} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.surveyOperatorService.getSurveyResponses(
      req,
      Number(surveyId),
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getResponses.name} with survey id: ${surveyId} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/form/:id/response/:answer_id')
  @Permission('PERMISSION_READ')
  async getAnswers(@Req() req: any, @Param('answer_id') answerid: string) {
    this.logger.log(
      `Entering ${this.getAnswers.name} with answer id: ${answerid}`,
    );
    const response = await this.surveyOperatorService.getSurveyResponse(
      req,
      Number(answerid),
    );
    this.logger.log(
      `Leaving ${this.getAnswers.name} with answer id: ${answerid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/download/response/:survey_id')
  @Permission('PERMISSION_READ')
  async getResponseSurveyExcel(
    @Req() req: any,
    @Res() res: Response,
    @Param('survey_id') survey_id: number,
  ) {
    this.logger.log(
      `Entering ${this.getResponseSurveyExcel.name} with survey id: ${survey_id}`,
    );
    const { buffer, filename } =
      await this.surveyService.getResponseSurveyExcel(req, res, survey_id);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.getResponseSurveyExcel.name} with survey id: ${survey_id}`,
    );
    res.end(buffer);
  }

  // END OF OPERATOR CONTROLLERS!

  // PERSONEL CONTROLLERS!
  @Get('/personel/forms')
  @Permission('PERMISSION_READ')
  async getAllPersonelForms(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAllPersonelForms.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.surveyPersonelService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getAllPersonelForms.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/form/mysurvey')
  @Permission('PERMISSION_READ')
  async getMySurvey(@Req() req) {
    this.logger.log(`Entering ${this.getMySurvey.name}`);
    const response = await this.surveyPersonelService.getPersonelSurveys(req);
    this.logger.log(
      `Leaving ${this.getMySurvey.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/form/:id')
  @Permission('PERMISSION_READ')
  async getFormPersonel(@Req() req: any, @Param('id') surveyId: string) {
    this.logger.log(
      `Entering ${this.getFormPersonel.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyPersonelService.getSurvey(
      req,
      Number(surveyId),
    );
    this.logger.log(
      `Leaving ${this.getFormPersonel.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/form/:id/start')
  @Permission('PERMISSION_CREATE')
  async startSurvey(@Param('id') surveyId: string, @Req() req) {
    this.logger.log(
      `Entering ${this.startSurvey.name} with survey id: ${surveyId}`,
    );
    const response = await this.surveyPersonelService.startSurveySession(
      Number(surveyId),
      req,
    );
    this.logger.log(
      `Leaving ${this.startSurvey.name} with survey id: ${surveyId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/form/:id/answer/:ans_id/submit')
  @Permission('PERMISSION_CREATE')
  async submitSurvey(
    @Req() req: any,
    @Param('id') surveyId: string,
    @Param('ans_id') answerId: string,
  ) {
    this.logger.log(
      `Entering ${this.submitSurvey.name} with survey id: ${surveyId} and answer id: ${answerId}`,
    );
    const response = await this.surveyPersonelService.submitAnswers(
      Number(surveyId),
      Number(answerId),
      req,
    );

    // background process
    this.eventEmitter.emit(
      SurveyEventsEnum.RESPONSE_SUBMITTED,
      Number(surveyId),
    );
    this.logger.log(
      `Leaving ${this.submitSurvey.name} with survey id: ${surveyId} and answer id: ${answerId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/personel/form/:id/answer/:ans_id/upload')
  @UseInterceptors(FilesInterceptor('files'))
  @Permission('PERMISSION_CREATE')
  async uploadFileSurvey(
    @Req() req: any,
    @Param('id') surveyId: string,
    @Param('ans_id') answerId: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.uploadFileSurvey.name} with survey id: ${surveyId} and answer id: ${answerId} and file length: ${files.length}`,
    );
    const response = await this.surveyService.uploadFiles(
      req,
      Number(surveyId),
      Number(answerId),
      files,
    );
    this.logger.log(
      `Leaving ${this.uploadFileSurvey.name} with survey id: ${surveyId} and answer id: ${answerId} and file length: ${files.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/form/:id/answer/:ans_id')
  @Permission('PERMISSION_READ')
  async getSurveyQuestions(
    @Req() req: any,
    @Param('id') surveyId: string,
    @Param('ans_id') answerId: string,
  ) {
    this.logger.log(
      `Entering ${this.getSurveyQuestions.name} with survey id: ${surveyId} and answer id: ${answerId}`,
    );
    const response = await this.surveyPersonelService.getQuestion(
      req,
      Number(surveyId),
      Number(answerId),
    );
    this.logger.log(
      `Leaving ${this.getSurveyQuestions.name} with survey id: ${surveyId} and answer id: ${answerId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/personel/form/:id/answer/:ans_id')
  @Permission('PERMISSION_CREATE')
  async answerQuestion(
    @Req() req: any,
    @Param('id') surveyId: string,
    @Param('ans_id') answerId: string,
    @Body() answerPayload: CreateSurveyAnswer,
  ) {
    this.logger.log(
      `Entering ${this.answerQuestion.name} with survey id: ${surveyId} and answer id: ${answerId} and answer payload: ${JSON.stringify(answerPayload)}`,
    );
    const response = await this.surveyPersonelService.saveAnswer(
      Number(surveyId),
      Number(answerId),
      answerPayload,
      req,
    );
    this.logger.log(
      `Leaving ${this.answerQuestion.name} with survey id: ${surveyId} and answer id: ${answerId} and answer payload: ${JSON.stringify(answerPayload)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
