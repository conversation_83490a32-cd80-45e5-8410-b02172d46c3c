import * as moment from 'moment';
import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import {
  ProJabCheckDocumentsDto,
  ProJabCreateDto,
} from '../dto/promosi-jabatan.dto';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import {
  IFileUpload,
  IHistoryStage,
  IJobPromotionSelection,
  IJobPromotionSelectionRequirement,
  IParamQuerySelectSelection,
  IValidationRequirement,
} from '../../core/interfaces/promosi-jabatan.interface';
import {
  compareValues,
  validateFile,
  validationValue,
} from '../../core/utils/validation.utils';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { PromosiJabatanParticipantStatusEnum } from '../../core/enums/promosi-jabatan.enum';
import {
  comparison_type_enum,
  Prisma,
  promosi_jabatan_stage_status_enum,
} from '@prisma/client';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../core/utils/search.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  buildPaginatedResponse,
  buildResponse,
} from '../../core/utils/response.util';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import {
  capitalizeEachWord,
  makeRegistrationNumber,
} from '../../core/utils/common.utils';

@Injectable()
export class PromosiJabatanService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async createJobPromotion(
    user: any,
    payload: ProJabCreateDto,
    files: Express.Multer.File[],
  ) {
    const { title, description, stages, requirements, requirement_files } =
      payload;

    const uploadBannerSelection = await this.processFile(
      files.find((file) => file.fieldname === 'banner_selection'),
      'banner_selection',
      800 * 1024,
      ['jpg', 'jpeg', 'png', 'gif'],
    );

    const uploadBannerStages = await this.processBannerStages(files);

    const listRequirements = await this.processRequirements(requirements);

    return this.prisma.$transaction(async (tx) => {
      const fileBannerSelection = uploadBannerSelection
        ? await tx.promosi_jabatan_files.create({ data: uploadBannerSelection })
        : null;

      const fileBannerStages = await this.storeBannerStages(
        tx,
        uploadBannerStages,
      );

      return await tx.promosi_jabatan_selections.create({
        data: {
          title,
          description,
          total_stages: stages.length,
          created_by: user?.id,
          banner_id: fileBannerSelection?.id ?? null,
          promosi_jabatan_selection_requirements: { create: listRequirements },
          promosi_jabatan_selection_requirement_files: {
            create: requirement_files.filter(Boolean),
          },
          promosi_jabatan_selection_stages: {
            create: this.processStages(stages, fileBannerStages),
          },
        },
      });
    });
  }

  async registerJobPromotionPersonel(
    user: any,
    selection_id: number,
    files: Express.Multer.File[],
  ) {
    if (isNaN(selection_id))
      throw new BadRequestException('ID seleksi tidak valid');

    const currentTime = moment.utc().toISOString();

    const { enable_register, message, stage_register, requirement_files } =
      await this.detailJobPromotion(user, selection_id);

    if (!enable_register) throw new BadRequestException(message);

    const fileUploadMap = new Map(files?.map((file) => [file.fieldname, file]));

    const uploadedFiles = await Promise.all(
      requirement_files.map(async (requirementFile) => {
        const title = requirementFile.title.toLowerCase();

        if (requirementFile.is_required && !fileUploadMap.has(title))
          throw new BadRequestException(
            `File ${requirementFile.title} wajib diunggah!`,
          );

        const file = fileUploadMap.get(title);

        return file
          ? {
              requirement_file_id: Number(requirementFile.id),
              file: await this.processFile(
                file,
                title,
                requirementFile.max_size,
                requirementFile.extensions,
              ),
            }
          : null;
      }),
    );

    const validUploadedFiles = uploadedFiles.filter(Boolean) as {
      requirement_file_id: number;
      file: any;
    }[];

    return this.prisma.$transaction(async (tx) => {
      const fileUploads: IFileUpload[] = await Promise.all(
        validUploadedFiles.map(async ({ requirement_file_id, file }) => {
          const createdFile = await tx.promosi_jabatan_files.create({
            data: file,
          });

          return {
            requirement_file_id,
            file_id: Number(createdFile.id),
            created_at: currentTime,
            status: PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
            reason: null,
          };
        }),
      );

      const lastValue = await tx.promosi_jabatan_selection_participants.count({
        where: { selection_id },
      });

      const historyStages: IHistoryStage[] = [
        {
          stage: stage_register.stage,
          created_at: currentTime,
          status: null,
        },
      ];

      return tx.promosi_jabatan_selection_participants.create({
        data: {
          registration_number: makeRegistrationNumber(lastValue),
          personel_id: user.personel_id,
          selection_id,
          current_stage_id: stage_register.id,
          history_stages: historyStages as unknown as Prisma.JsonArray,
          file_uploads: fileUploads as unknown as Prisma.JsonArray,
        },
      });
    });
  }

  async submitDocumentsParticipants() {}

  async findAllJobPromotion(
    user: any,
    paginationData: PaginationDto,
    type_user: 'PERSONNEL' | 'OPERATOR',
  ) {
    const currentDate = moment().startOf('days');

    const { limit, page } = paginationData;
    const where =
      type_user === 'OPERATOR' ? { created_by_user: { id: user.id } } : {};

    const [selections, totalData] = await Promise.all([
      this.selectDataJobPromotions({
        withStages: true,
        take: limit,
        skip: limit * (page - 1),
        where,
      }),
      this.prisma.promosi_jabatan_selections.count({ where }),
    ]);

    const idStagesClosed: bigint[] = [];

    const result = await Promise.all(
      selections.map(async (selection) => {
        const stageActive = this.getActiveStage(
          selection.promosi_jabatan_selection_stages,
          currentDate,
          idStagesClosed,
        );

        const bannerUrl = selection.promosi_jabatan_files_banner_selection
          ?.file_name
          ? await this.minioService.checkFileExist(
              process.env.MINIO_BUCKET_NAME!,
              `${process.env.MINIO_PATH_FILE}/${selection.promosi_jabatan_files_banner_selection.file_name}`,
            )
          : null;

        return {
          id: selection.id,
          title: selection.title,
          description: selection.description,
          total_stages: selection.total_stages,
          banner: bannerUrl,
          stage: stageActive,
        };
      }),
    );

    if (idStagesClosed.length > 0) {
      await this.prisma.promosi_jabatan_selection_stages.updateMany({
        where: { id: { in: idStagesClosed } },
        data: { status: promosi_jabatan_stage_status_enum.DITUTUP },
      });
    }

    return {
      result,
      page,
      totalPage: Math.ceil(totalData / limit),
      totalData,
    };
  }

  async findParticipantsBasedOnStage(
    selection_id: number,
    stage_id: number,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    if (isNaN(selection_id) || isNaN(stage_id))
      throw new BadRequestException('ID seleksi/ID tahapan tidak valid');

    const { limit, page } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: {
        field: 'nama',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const { deleted_at, ...whereClause } = where;

    const [totalData, participants, stages] = await this.prisma.$transaction([
      this.prisma.promosi_jabatan_selection_participants.count({
        where: {
          selection_id,
          current_stage_id: stage_id,
          ...whereClause,
        },
      }),
      this.prisma.promosi_jabatan_selection_participants.findMany({
        where: {
          selection_id,
          current_stage_id: stage_id,
          ...whereClause,
        },
        select: {
          id: true,
          registration_number: true,
          exam_number: true,
          file_uploads: true,
          history_stages: true,
          personel: {
            select: this.personelSelectionColumn(),
          },
          promosi_jabatan_selections: {
            select: {
              id: true,
              title: true,
              description: true,
              total_stages: true,
              promosi_jabatan_files_banner_selection: {
                select: { url: true },
              },
            },
          },
          promosi_jabatan_selection_stages: {
            select: {
              id: true,
              stage: true,
              name: true,
              location: true,
              status: true,
              start_date: true,
              end_date: true,
              promosi_jabatan_files_banner_stage: {
                select: { url: true },
              },
            },
          },
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
      }),
      this.prisma.promosi_jabatan_selection_stages.findFirst({
        where: {
          id: stage_id,
          selection_id,
        },
        select: {
          id: true,
          stage: true,
          name: true,
          location: true,
          status: true,
          start_date: true,
          end_date: true,
          promosi_jabatan_files_banner_stage: {
            select: { url: true },
          },
        },
      }),
    ]);

    if (!stages)
      throw new BadRequestException('Tahapan tidak ditemukan pada seleksi ini');

    const totalPage = Math.ceil(totalData / limit);

    const restructuredParticipants = await Promise.all(
      participants.map(async (participant) =>
        this.restructureParticipant(participant),
      ),
    );

    const count_status_participants = {
      NOT_CHECKED_YET: 0,
      CHECKED: 0,
      REJECTED: 0,
      REVISION: 0,
    };

    restructuredParticipants.forEach((item) => {
      const statusEnum = Object.entries(
        PromosiJabatanParticipantStatusEnum,
      ).find(([_, val]) => {
        return val === item.status_participant;
      });
      count_status_participants[statusEnum[0]] += 1;
    });

    const restructuredStages = await this.restructureDataWithBanner(
      stages,
      'promosi_jabatan_files_banner_stage',
    );

    return {
      result: restructuredParticipants,
      stage: restructuredStages,
      count_status_participants,
      page,
      totalPage,
      totalData,
    };
  }

  async checkDocumentsOperatorJobPromotion(
    req: any,
    registrationNumber: string,
    body: ProJabCheckDocumentsDto,
  ) {
    const { requirement_file_id, file_id, status, reason } = body;

    const participant =
      await this.prisma.promosi_jabatan_selection_participants.findFirst({
        where: {
          registration_number: registrationNumber,
        },
        select: {
          id: true,
          registration_number: true,
          exam_number: true,
          file_uploads: true,
          history_stages: true,
          personel: {
            select: this.personelSelectionColumn(),
          },
          promosi_jabatan_selections: {
            select: {
              id: true,
              title: true,
              description: true,
              total_stages: true,
              promosi_jabatan_files_banner_selection: {
                select: { url: true },
              },
            },
          },
          promosi_jabatan_selection_stages: {
            select: {
              id: true,
              stage: true,
              name: true,
              location: true,
              status: true,
              start_date: true,
              end_date: true,
              promosi_jabatan_files_banner_stage: {
                select: { url: true },
              },
            },
          },
        },
      });

    if (!participant) throw new NotFoundException('Peserta tidak ditemukan');

    const fileUploads = this.parseFileUploads(participant.file_uploads);
    const updatedFileUploads = fileUploads.map((fileUpload) => {
      if (
        fileUpload.file_id === file_id &&
        fileUpload.requirement_file_id === requirement_file_id
      ) {
        fileUpload.status = status;
        fileUpload.reason = reason;
      }

      return fileUpload;
    });

    const result =
      await this.prisma.promosi_jabatan_selection_participants.update({
        where: { id: participant.id },
        data: {
          file_uploads: updatedFileUploads as unknown as Prisma.JsonArray,
        },
      });

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully updated',
      data: result,
    };
  }

  async detailJobPromotion(user: any, selection_id: number) {
    if (isNaN(selection_id))
      throw new BadRequestException('ID seleksi tidak valid');

    const currentDate = moment().startOf('days');

    const [selections, personel] = await Promise.all([
      this.selectDataJobPromotions({
        withParticipants: true,
        participantId: user.personel_id,
        withStages: true,
        withRequirements: true,
        withRequirementFiles: true,
        where: { id: selection_id },
      }),
      this.prisma.personel.findFirst({
        where: { id: user.personel_id },
        select: this.personelSelectionColumn(),
      }),
    ]);

    if (!selections.length)
      throw new BadRequestException('Seleksi tidak ditemukan');

    const selection = selections[0];

    const bannerUrl = selection.promosi_jabatan_files_banner_selection
      ?.file_name
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}/${selection.promosi_jabatan_files_banner_selection?.file_name}`,
        )
      : null;

    // const stage = selection.promosi_jabatan_selection_stages[0];
    const stage = await this.restructureDataWithBanner(
      selection.promosi_jabatan_selection_stages[0],
      'promosi_jabatan_files_banner_stage',
    );
    const isOpenRegister = this.isStageOpen(stage, currentDate);

    const personelRequirements = await this.validatePersonelRequirement(
      selection.promosi_jabatan_selection_requirements,
      personel,
    );

    const isPersonelRequirementValid = personelRequirements.every(
      (req) => req.is_valid,
    );
    const isAlreadyRegistered =
      selection.promosi_jabatan_selection_participants.length > 0;

    const message = isAlreadyRegistered
      ? 'Anda sudah terdaftar'
      : !isPersonelRequirementValid
        ? 'Data personel tidak memenuhi syarat'
        : !isOpenRegister
          ? 'Registrasi sudah ditutup'
          : 'Anda memenuhi kriteria';

    return {
      title: selection.title,
      description: selection.description,
      banner: bannerUrl,
      personel: await this.restructurePersonel(personel),
      requirements: personelRequirements,
      requirement_files: selection.promosi_jabatan_selection_requirement_files,
      stage_register: stage,
      enable_register:
        isOpenRegister && !isAlreadyRegistered && isPersonelRequirementValid,
      message,
    };
  }

  async findRequirements() {
    return await this.prisma.promosi_jabatan_requirements.findMany({
      select: {
        id: true,
        name: true,
        input_type: true,
        url_data: true,
      },
      orderBy: { id: 'asc' },
    });
  }

  private getActiveStage(
    stages: any[],
    currentDate: moment.Moment,
    idStagesClosed: bigint[],
  ) {
    return stages.reduce((activeStage, stage) => {
      const startDate = moment(stage.start_date).startOf('days');
      const endDate = moment(stage.end_date).startOf('days');
      const isBetween = currentDate.isBetween(startDate, endDate, 'days', '[]');

      if (
        currentDate.isSameOrAfter(startDate) ||
        !Object.keys(activeStage).length
      ) {
        if (
          stage.status === promosi_jabatan_stage_status_enum.DIBUKA &&
          !isBetween
        ) {
          idStagesClosed.push(stage.id);
        }

        return {
          id: stage.id,
          name: stage.name,
          location: stage.location ?? '-',
          start_date: stage.start_date,
          end_date: stage.end_date,
          status:
            stage.stage !== 0
              ? null
              : isBetween
                ? stage.status
                : promosi_jabatan_stage_status_enum.DITUTUP,
        };
      }

      return activeStage;
    }, {} as any);
  }

  private async selectDataJobPromotions(
    param?: IParamQuerySelectSelection,
  ): Promise<IJobPromotionSelection[]> {
    const data = await this.prisma.promosi_jabatan_selections.findMany({
      where: { deleted_at: null, ...param?.where },
      select: {
        id: true,
        title: true,
        description: true,
        total_stages: true,
        promosi_jabatan_files_banner_selection: {
          select: {
            original_name: true,
            url: true,
            file_name: true,
          },
        },
        promosi_jabatan_selection_requirements: param?.withRequirements
          ? {
              select: {
                id: true,
                is_required: true,
                value: true,
                comparison_type: true,
                promosi_jabatan_requirements: {
                  select: {
                    name: true,
                    input_type: true,
                    url_data: true,
                    table_foreign: true,
                  },
                },
              },
            }
          : undefined,
        promosi_jabatan_selection_requirement_files: param?.withRequirementFiles
          ? {
              select: {
                id: true,
                is_required: true,
                title: true,
                extensions: true,
                max_size: true,
                min_size: true,
              },
            }
          : undefined,
        created_by_user: param?.withUser
          ? {
              select: {
                id: true,
                personel: {
                  select: {
                    id: true,
                    nrp: true,
                    nama_lengkap: true,
                  },
                },
              },
            }
          : undefined,
        promosi_jabatan_selection_stages: param?.withStages
          ? {
              select: {
                id: true,
                stage: true,
                name: true,
                location: true,
                status: true,
                start_date: true,
                end_date: true,
                promosi_jabatan_files_banner_stage: {
                  select: {
                    original_name: true,
                    url: true,
                    file_name: true,
                  },
                },
              },
              orderBy: { stage: 'asc' },
            }
          : undefined,
        promosi_jabatan_selection_participants: param?.withParticipants
          ? {
              where: { personel_id: param?.participantId },
              select: {
                registration_number: true,
                exam_number: true,
                history_stages: true,
                file_uploads: true,
              },
            }
          : undefined,
      },
      take: param?.take ?? undefined,
      skip: param?.skip ?? undefined,
    });
    return data.map((item) => ({
      ...item,
      promosi_jabatan_selection_participants:
        item.promosi_jabatan_selection_participants?.map((participant) => ({
          ...participant,
          history_stages: this.parseHistoryStages(participant.history_stages),
        })),
      promosi_jabatan_selection_requirement_files:
        item.promosi_jabatan_selection_requirement_files?.map((file) => ({
          ...file,
          extensions: this.parseExtensions(file.extensions),
        })) || [],
    }));
  }

  private parseHistoryStages(historyStages?: any): IHistoryStage[] {
    if (!Array.isArray(historyStages)) return [];
    return (historyStages as unknown as IHistoryStage[]).map((stage) => ({
      stage: Number(stage.stage),
      created_at: String(stage.created_at),
      status: stage.status || null,
    }));
  }

  private parseFileUploads(fileUploads?: any): IFileUpload[] {
    if (!Array.isArray(fileUploads)) return [];

    return (fileUploads as unknown as IFileUpload[]).map((fileUpload) => ({
      requirement_file_id: fileUpload.requirement_file_id,
      file_id: fileUpload.file_id,
      created_at: fileUpload.created_at,
      status: fileUpload.status,
      reason: fileUpload.reason,
    }));
  }

  private parseExtensions(extensions?: any): string[] {
    return Array.isArray(extensions) ? extensions.map(String) : [];
  }

  private async uploadFile(file: Express.Multer.File) {
    if (!file) return null;
    const uploadedFile = await this.minioService.uploadFile(file);
    return {
      original_name: file.originalname,
      encoding: file.encoding,
      mime_type: file.mimetype,
      file_name: uploadedFile.filename,
      size: file.size,
      key: uploadedFile.Key,
      url: uploadedFile.Location,
    };
  }

  private async validationRequirement(
    personel: any,
    name: string,
    value: string,
    comparison_type: comparison_type_enum,
  ): Promise<IValidationRequirement> {
    if (!personel || !name || !value) return { is_valid: false, value: null };

    const lowerName = name.toLowerCase();

    switch (lowerName) {
      case 'pangkat':
        return this.validatePangkat(personel, value, comparison_type);
      case 'umur':
        return this.validateUmur(
          personel.tanggal_lahir,
          value,
          comparison_type,
        );
      case 'jabatan':
        return this.validateJabatan(personel, value, comparison_type);
      case 'tmt jabatan':
        return this.validateTmtJabatan(personel, value, comparison_type);
      default:
        return { is_valid: false, value: null };
    }
  }

  private async validatePangkat(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): Promise<IValidationRequirement> {
    const pangkatDetail = await this.prisma.pangkat.findFirst({
      where: { deleted_at: null, id: Number(value) },
      select: {
        id: true,
        nama: true,
        nama_singkat: true,
        tingkat_id: true,
      },
    });

    if (!pangkatDetail) return { is_valid: false, value: null };

    const pangkatPersonel = personel.pangkat_personel?.[0]?.pangkat;
    if (!pangkatPersonel) return { is_valid: false, value: pangkatDetail.nama };

    const { nama, nama_singkat, tingkat_id } = pangkatPersonel;

    if (comparison_type !== 'EQUAL')
      return {
        is_valid: compareValues(
          tingkat_id,
          pangkatDetail.tingkat_id,
          comparison_type,
        ),
        value: pangkatDetail.nama,
      };

    return {
      is_valid:
        compareValues(nama, pangkatDetail.nama, comparison_type) ||
        compareValues(
          nama_singkat,
          pangkatDetail.nama_singkat,
          comparison_type,
        ),
      value: pangkatDetail.nama,
    };
  }

  private validateUmur(
    tanggalLahir: string,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const birthDate = moment(tanggalLahir, moment.ISO_8601, true);
    if (!birthDate.isValid()) return { is_valid: false, value: null };

    const age = Number(birthDate.fromNow(true).split(' ')[0]);
    return {
      is_valid: compareValues(age, Number(value), comparison_type),
      value,
    };
  }

  private validateJabatan(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const jabatan = personel.jabatan_personel?.[0]?.jabatans?.[0]?.nama;
    if (!jabatan) return { is_valid: false, value: null };

    return { is_valid: compareValues(value, jabatan, comparison_type), value };
  }

  private validateTmtJabatan(
    personel: any,
    value: string,
    comparison_type: comparison_type_enum,
  ): IValidationRequirement {
    const tmt = personel.jabatan_personel?.[0]?.tmt_jabatan;
    if (!tmt) return { is_valid: false, value: null };

    const tmtDate = moment(tmt, moment.ISO_8601, true).startOf('day');
    const inputDate = moment(value, moment.ISO_8601, true).startOf('day');

    if (!tmtDate.isValid() || !inputDate.isValid())
      return { is_valid: false, value: null };

    return {
      is_valid: compareValues(inputDate, tmtDate, comparison_type, true),
      value,
    };
  }

  private async validatePersonelRequirement(
    requirements: IJobPromotionSelectionRequirement[],
    personel: any,
  ) {
    return await Promise.all(
      requirements.map(
        async ({
          is_required,
          promosi_jabatan_requirements,
          value,
          comparison_type,
        }) => {
          const validate = await this.validationRequirement(
            personel,
            promosi_jabatan_requirements.name,
            value,
            comparison_type,
          );

          return {
            name: promosi_jabatan_requirements.name,
            is_required,
            value: capitalizeEachWord(validate.value),
            comparison_type,
            is_valid: !is_required || validate.is_valid,
          };
        },
      ),
    );
  }

  private async processFile(
    file: Express.Multer.File | undefined,
    fieldname: string,
    maxSize?: number | null,
    extensions?: string[] | null,
  ) {
    if (!file) return null;
    validateFile(file, fieldname, maxSize, extensions);
    return this.uploadFile(file);
  }

  private async processBannerStages(files: Express.Multer.File[]) {
    const bannerStages: Record<number, Express.Multer.File> = {};

    for (const file of files) {
      const match = file.fieldname.match(/^banner_stages\[(\d+)]$/);
      if (match) {
        const index = parseInt(match[1], 10);
        validateFile(file, `banner_stages[${index}]`, 2 * 1024 * 1024, [
          'jpg',
          'jpeg',
          'png',
          'gif',
        ]);
        if (!bannerStages[index]) bannerStages[index] = file;
      }
    }

    const uploadBannerStages = {};
    for (const [idx, file] of Object.entries(bannerStages)) {
      uploadBannerStages[idx] = await this.uploadFile(file);
    }
    return uploadBannerStages;
  }

  private async processRequirements(requirements: any[]) {
    return await Promise.all(
      requirements.map(async (requirement) => {
        const requirementExisting =
          await this.prisma.promosi_jabatan_requirements.findFirst({
            where: { id: requirement.id },
          });

        if (!requirementExisting)
          throw new BadRequestException('Syarat tidak ditemukan');

        return {
          requirement_id: requirement.id,
          is_required: requirement.is_required,
          comparison_type: requirement.comparison_type,
          value: validationValue(
            requirementExisting.input_type,
            requirement.value,
          ),
        };
      }),
    );
  }

  private processStages(stages: any[], fileBannerStages: Record<string, any>) {
    return stages.map((stage, index) => ({
      stage: index,
      name: stage.name,
      location: stage.location ?? null,
      banner_id: fileBannerStages[index]?.id ?? null,
      status: promosi_jabatan_stage_status_enum.DIBUKA,
      start_date: moment(stage.start_date).startOf('day').toISOString(),
      end_date: moment(stage.end_date).startOf('day').toISOString(),
    }));
  }

  private isStageOpen(stage: any, currentDate: moment.Moment): boolean {
    const startDate = moment(stage.start_date).startOf('days');
    const endDate = moment(stage.end_date).startOf('days');
    return (
      currentDate.isBetween(startDate, endDate, 'days', '[]') &&
      stage.status === promosi_jabatan_stage_status_enum.DIBUKA
    );
  }

  private async storeBannerStages(
    tx: any,
    uploadBannerStages: Record<string, any>,
  ) {
    const storedBannerStages = {};
    for (const [idx, bannerStage] of Object.entries(uploadBannerStages)) {
      storedBannerStages[idx] = await tx.promosi_jabatan_files.create({
        data: bannerStage,
      });
    }
    return storedBannerStages;
  }

  private async restructurePersonel(personel: any) {
    if (!personel) return null;

    const jabatanPersonel = personel.jabatan_personel?.[0];
    const pangkatPersonel = personel.pangkat_personel?.[0]?.pangkat;
    const jabatan = jabatanPersonel?.jabatans;

    const foto_file = personel.foto_file
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
        )
      : null;

    return {
      id: personel.id,
      nrp: personel.nrp,
      jenis_kelamin: personel.jenis_kelamin,
      nama_lengkap: personel.nama_lengkap,
      tanggal_lahir: personel.tanggal_lahir,
      foto_file: foto_file,
      pangkat: pangkatPersonel,
      jabatan: jabatan
        ? {
            id: jabatan.id,
            nama: jabatan.nama,
          }
        : null,
      tmt_jabatan: jabatanPersonel?.tmt_jabatan || null,
      satuan: jabatan?.satuan || null,
    };
  }

  private async restructureDataWithBanner(data: any, bannerKey: string) {
    const { [bannerKey]: bannerFile, ...restData } = data;

    const bannerUrl = bannerFile?.file_name
      ? await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}/${bannerFile?.file_name}`,
        )
      : null;

    return {
      ...restData,
      banner: bannerUrl,
    };
  }

  private async restructureParticipant({
    personel,
    file_uploads,
    history_stages,
    promosi_jabatan_selections,
    promosi_jabatan_selection_stages,
    ...rest
  }) {
    const [stage, selection, historyStages, fileUploads] =
      await Promise.allSettled([
        this.restructureDataWithBanner(
          promosi_jabatan_selection_stages,
          'promosi_jabatan_files_banner_stage',
        ),
        this.restructureDataWithBanner(
          promosi_jabatan_selections,
          'promosi_jabatan_files_banner_selection',
        ),
        this.parseHistoryStages(history_stages),
        this.getFileUploads(this.parseFileUploads(file_uploads)),
      ]).then((results) =>
        results.map((result) =>
          result.status === 'fulfilled' ? result.value : null,
        ),
      );

    const statusParticipant = this.determineParticipantStatus(
      stage,
      fileUploads,
      historyStages,
    );

    return {
      ...rest,
      personel: personel ? await this.restructurePersonel(personel) : null,
      selection,
      status_participant: statusParticipant,
      enable_action: true,
      file_uploads: fileUploads,
    };
  }

  private determineParticipantStatus(
    stage,
    fileUploads,
    historyStages: IHistoryStage[],
  ) {
    if (!stage || stage.stage === 0) {
      return fileUploads?.some((file) =>
        [
          PromosiJabatanParticipantStatusEnum.REJECTED,
          PromosiJabatanParticipantStatusEnum.REVISION,
          PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
        ].includes(file.status),
      )
        ? fileUploads.find((file) =>
            [
              PromosiJabatanParticipantStatusEnum.REJECTED,
              PromosiJabatanParticipantStatusEnum.REVISION,
              PromosiJabatanParticipantStatusEnum.NOT_CHECKED_YET,
            ].includes(file.status),
          )?.status
        : PromosiJabatanParticipantStatusEnum.CHECKED;
    }

    return (
      historyStages?.find((history) => history.stage === stage.stage)?.status ||
      ''
    );
  }

  private async getFileUploads(file_uploads: any[]) {
    if (!file_uploads || file_uploads.length === 0) return [];

    const parsedUploads = this.parseFileUploads(file_uploads);

    return await Promise.all(
      parsedUploads.map(async ({ requirement_file_id, file_id, ...rest }) => {
        const [requirement_file, files] = await Promise.all([
          this.prisma.promosi_jabatan_selection_requirement_files.findFirst({
            where: { id: requirement_file_id },
            select: {
              id: true,
              title: true,
            },
          }),
          this.prisma.promosi_jabatan_files.findFirst({
            where: { id: file_id },
            select: { file_name: true },
          }),
        ]);

        const fileUrl = files
          ? await this.minioService.checkFileExist(
              process.env.MINIO_BUCKET_NAME!,
              `${process.env.MINIO_PATH_FILE}/${files.file_name}`,
            )
          : null;

        return {
          ...rest,
          requirement_file,
          file: {
            id: file_id,
            url: fileUrl,
          },
        };
      }),
    );
  }

  private personelSelectionColumn() {
    return {
      id: true,
      nrp: true,
      nama_lengkap: true,
      tanggal_lahir: true,
      jenis_kelamin: true,
      foto_file: true,
      pangkat_personel: {
        select: {
          pangkat: {
            select: {
              id: true,
              nama: true,
              nama_singkat: true,
              tingkat_id: true,
            },
          },
        },
        orderBy: { tmt: 'desc' as const },
        take: 1,
      },
      jabatan_personel: {
        select: {
          tmt_jabatan: true,
          jabatans: {
            select: {
              id: true,
              nama: true,
              satuan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
          },
        },
        orderBy: { tmt_jabatan: 'desc' as const },
        take: 1,
      },
    };
  }

  async processFindOperatorRequirements(req: any) {
    const queryResult = await this.findRequirements();

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async processCreateOperatorJobPromotion(
    req: any,
    body: ProJabCreateDto,
    files: Express.Multer.File[],
  ) {
    const queryResult = await this.createJobPromotion(req.user, body, files);
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async processFindListOperatorJobPromotion(
    req: any,
    paginationData: PaginationDto,
  ) {
    const queryResult = await this.findAllJobPromotion(
      req.user,
      paginationData,
      'OPERATOR',
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(queryResult, message);
  }

  async processFindOperatorParticipantsBasedOnStage(
    req: any,
    selectionId: number,
    stageId: number,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const queryResult = await this.findParticipantsBasedOnStage(
      selectionId,
      stageId,
      paginationData,
      searchAndSortData,
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(queryResult, message);
  }

  async processRegisterJobPromotionPersonel(
    req: any,
    selectionId: number,
    files: Express.Multer.File[],
  ) {
    const queryResult = await this.registerJobPromotionPersonel(
      req.user,
      selectionId,
      files,
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }

  async processFindListJobPromotionPersonel(
    req: any,
    paginationData: PaginationDto,
  ) {
    const queryResult = await this.findAllJobPromotion(
      req.user,
      paginationData,
      'PERSONNEL',
    );
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildPaginatedResponse(queryResult, message);
  }

  async processDetailJobPromotionPersonel(req: any, selectionId: number) {
    const queryResult = await this.detailJobPromotion(req.user, selectionId);
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PROMOSI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return buildResponse(queryResult, message);
  }
}
