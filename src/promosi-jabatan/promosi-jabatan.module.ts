import { Modu<PERSON> } from '@nestjs/common';
import { PromosiJabatanService } from './service/promosi-jabatan.service';
import { PromosiJabatanController } from './controller/promosi-jabatan.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { MinioService } from '../api-utils/minio/service/minio.service';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [PromosiJabatanController],
  providers: [PromosiJabatanService, MinioService],
})
export class PromosiJabatanModule {}
