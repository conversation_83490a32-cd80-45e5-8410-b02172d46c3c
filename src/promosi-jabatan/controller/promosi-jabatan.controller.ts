import { PromosiJabatanService } from '../service/promosi-jabatan.service';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { FieldValidatorPipe } from '../../core/validator/field.validator';
import {
  ProJabCheckDocumentsDto,
  ProJabCreateDto,
} from '../dto/promosi-jabatan.dto';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { Module } from "../../core/decorators";

@UseGuards(JwtAuthGuard)
@Controller('promosi-jabatan')
export class PromosiJabatanController {
  private readonly logger = new Logger(PromosiJabatanController.name);

  constructor(private readonly promosiJabatanService: PromosiJabatanService) {}

  // habis daftar, peserta perlu menampilkan apa di list seleksi nya? atau mau dipisah saja tampilan seleksi yang
  // sudah di daftar sama belum

  @Get('/requirements')
  @HttpCode(HttpStatus.OK)
  async findOperatorRequirements(@Req() req: any) {
    this.logger.log(`Entering ${this.findOperatorRequirements.name}`);
    const response =
      await this.promosiJabatanService.processFindOperatorRequirements(req);
    this.logger.log(
      `Leaving ${this.findOperatorRequirements.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  /** ============================ OPERATOR ============================ */

  @Post('/operator')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async createOperatorJobPromotion(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: ProJabCreateDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.createOperatorJobPromotion.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.promosiJabatanService.processCreateOperatorJobPromotion(
        req,
        body,
        files,
      );
    this.logger.log(
      `Leaving ${this.createOperatorJobPromotion.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )
  @HttpCode(HttpStatus.OK)
  async findListOperatorJobPromotion(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.findListOperatorJobPromotion.name} with pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response =
      await this.promosiJabatanService.processFindListOperatorJobPromotion(
        req,
        paginationData,
      );
    this.logger.log(
      `Leaving ${this.findListOperatorJobPromotion.name} with pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/:selection_id/:stage_id')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )
  @HttpCode(HttpStatus.OK)
  async findOperatorParticipantsBasedOnStage(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @Param('stage_id', ParseIntPipe) stageId: number,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findOperatorParticipantsBasedOnStage.name} with selection id: ${selectionId}, stage id: ${stageId}, pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response =
      await this.promosiJabatanService.processFindOperatorParticipantsBasedOnStage(
        req,
        selectionId,
        stageId,
        paginationData,
        searchAndSortData,
      );
    this.logger.log(
      `Leaving ${this.findOperatorParticipantsBasedOnStage.name} with selection id: ${selectionId}, stage id: ${stageId}, pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/operator/check-documents/:registration_number')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )
  @HttpCode(HttpStatus.OK)
  async checkDocumentsOperatorJobPromotion(
    @Req() req: any,
    @Param('registration_number') registrationNumber: string,
    @Body() body: ProJabCheckDocumentsDto,
  ) {
    this.logger.log(`Entering ${this.checkDocumentsOperatorJobPromotion.name}`);
    const response =
      await this.promosiJabatanService.checkDocumentsOperatorJobPromotion(
        req,
        registrationNumber,
        body,
      );
    this.logger.log(
      `Leaving ${this.checkDocumentsOperatorJobPromotion.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  /** ============================ PERSONEL ============================ */

  @Post('/personel/:selection_id')
  @UseInterceptors(AnyFilesInterceptor())
  @HttpCode(HttpStatus.OK)
  async registerJobPromotionPersonel(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.registerJobPromotionPersonel.name} with selection id: ${selectionId} and files length: ${JSON.stringify(files.length)}`,
    );
    const response =
      await this.promosiJabatanService.processRegisterJobPromotionPersonel(
        req,
        selectionId,
        files,
      );
    this.logger.log(
      `Leaving ${this.registerJobPromotionPersonel.name} with selection id: ${selectionId} and files length: ${JSON.stringify(files.length)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )
  @HttpCode(HttpStatus.OK)
  async findListJobPromotionPersonel(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.findListJobPromotionPersonel.name} with pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response =
      await this.promosiJabatanService.processFindListJobPromotionPersonel(
        req,
        paginationData,
      );
    this.logger.log(
      `Leaving ${this.findListJobPromotionPersonel.name} with pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/:selection_id')
  @HttpCode(HttpStatus.OK)
  async detailJobPromotionPersonel(
    @Req() req: any,
    @Param('selection_id', ParseIntPipe) selectionId: number,
  ) {
    this.logger.log(
      `Entering ${this.detailJobPromotionPersonel.name} with selection id: ${selectionId}`,
    );
    const response =
      await this.promosiJabatanService.processDetailJobPromotionPersonel(
        req,
        selectionId,
      );
    this.logger.log(
      `Leaving ${this.detailJobPromotionPersonel.name} with selection id: ${selectionId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
