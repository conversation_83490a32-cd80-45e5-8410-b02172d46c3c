import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { IsSequentialDateRange } from 'src/core/validator/date-range.validator';
import { IsNotPastDate } from '../../core/validator/past-date.validator';
import { comparison_type_enum } from '@prisma/client';
import { PromosiJabatanParticipantStatusEnum } from '../../core/enums/promosi-jabatan.enum';

class ProJabStageDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsNotEmpty()
  @IsString()
  @IsDateString()
  @IsNotPastDate()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  @IsDateString()
  @IsNotPastDate()
  end_date: string;
}

class ProJabRequirementsDto {
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  id: number;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean = false;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsNotEmpty()
  @IsEnum(comparison_type_enum)
  comparison_type: comparison_type_enum;
}

class ProJabRequirementFilesDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean = false;

  @IsOptional()
  @IsArray()
  extensions?: string[] = [];

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  max_size: number = 0;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  min_size: number = 0;
}

export class ProJabCreateDto {
  @IsNotEmpty({ message: 'Judul seleksi tidak boleh kosong' })
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty({ message: 'Tahapan seleksi tidak boleh kosong' })
  @IsArray()
  @ValidateNested({ each: true })
  @Validate(IsSequentialDateRange)
  @Type(() => ProJabStageDto)
  stages: ProJabStageDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProJabRequirementsDto)
  requirements?: ProJabRequirementsDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProJabRequirementFilesDto)
  requirement_files?: ProJabRequirementFilesDto[];
}

export class ProJabCheckDocumentsDto {
  @Transform(({ value }) => parseInt(value))
  @IsNotEmpty()
  @IsNumber()
  requirement_file_id: number;

  @Transform(({ value }) => parseInt(value))
  @IsNotEmpty()
  @IsNumber()
  file_id: number;

  @IsNotEmpty()
  @IsString()
  @IsEnum(PromosiJabatanParticipantStatusEnum)
  status: PromosiJabatanParticipantStatusEnum;

  @IsOptional()
  @IsString()
  reason?: string;
}
