import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { KenaikanGajiBerkalaService } from '../service/kenaikan-gaji-berkala.service';
import {
  ApproveKGBRequestBodyDTO,
  ExportKGBDataDTO,
} from '../dto/kenaikan-gaji-berkala.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Response } from 'express';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import {Module, Permission, RolePermissionNeeded} from '../../core/decorators';
import { MODULES } from '../../core/constants/module.constant';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import {UserRoleGuard} from "../../core/guards/user-role.guards";

@Controller('kenaikan-gaji-berkala')
@Module(MODULES.REGULAR_SALARY_INCREASE)
@UseGuards(JwtAuthGuard, PermissionGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: false }))
export class KenaikanGajiBerkalaController {
  private readonly logger = new Logger(KenaikanGajiBerkalaController.name);

  constructor(
    private readonly kenaikanGajiBerkalaService: KenaikanGajiBerkalaService,
  ) {}

  @Get('/upcoming')
  @RolePermissionNeeded('LIST_PENGAJUAN', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getListUpcoming(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.getListUpcoming(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/logs/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getKGBLogs(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getKGBLogs.name} with id: ${id}`);
    let parsedInt = parseInt(id);
    const response = await this.kenaikanGajiBerkalaService.getKGBLogs(
      req,
      parsedInt,
    );
    this.logger.log(
      `Leaving ${this.getKGBLogs.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/approved')
  @RolePermissionNeeded('LIST_PENERBITAN', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getListApproved(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListApproved.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.getListApproved(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListApproved.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/gaji-pokok')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getListGajiPokok(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListGajiPokok.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.getListGajiPokok(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListGajiPokok.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/gaji-pokok/:pangkat_id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getDetailGajiPokok(
    @Req() req: any,
    @Param('pangkat_id') pangkat_id: number,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDetailGajiPokok.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.getDetailGajiPokok(
      req,
      paginationData,
      searchandsortData,
      pangkat_id
    );
    this.logger.log(
      `Leaving ${this.getDetailGajiPokok.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/gaji-pokok/:pangkat_id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createDetailGajiPokok(
    @Req() req: any,
    @Param('pangkat_id') pangkat_id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createDetailGajiPokok.name} with id: ${pangkat_id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.createDetailGajiPokok(
      req,
      body,
      pangkat_id
    );
    this.logger.log(
      `Leaving ${this.createDetailGajiPokok.name} with id: ${pangkat_id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/gaji-pokok/update/:id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateDetailGajiPokok(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateDetailGajiPokok.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.updateDetailGajiPokok(
      req,
      body,
      id
    );
    this.logger.log(
      `Leaving ${this.updateDetailGajiPokok.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/gaji-pokok/delete/:id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteDetailGajiPokok(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteDetailGajiPokok.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.kenaikanGajiBerkalaService.deleteDetailGajiPokok(
      req,
      body,
      id
    );
    this.logger.log(
      `Leaving ${this.deleteDetailGajiPokok.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/export/template')
  @RolePermissionNeeded('LIST_PENGAJUAN', ['PERMISSION_IMPORT'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async exportSelectedData(
    @Req() req: any,
    @Res() res: Response,
    @Query() queries: ExportKGBDataDTO,
  ) {
    this.logger.log(
      `Entering ${this.exportSelectedData.name} with queries: ${JSON.stringify(queries)}`,
    );
    const buffer = await this.kenaikanGajiBerkalaService.exportSelectedData(
      req,
      queries,
    );

    const filename = `Hasil Ekspor - KGB`;
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    this.logger.log(
      `Leaving ${this.exportSelectedData.name} with queries: ${JSON.stringify(queries)} and response: ${JSON.stringify(buffer.byteLength)}`,
    );

    res.end(buffer);
  }

  @Get('/export/penerbitan')
  @RolePermissionNeeded('LIST_PENERBITAN', ['PERMISSION_DOWNLOAD'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async exportSelectedPenerbitanData(
    @Req() req: any,
    @Res() res: Response,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.exportSelectedData.name}`,
    );
    const buffer = await this.kenaikanGajiBerkalaService.exportSelectedPenerbitanData(
      req,
      searchandsortData
    );

    const filename = `Hasil Ekspor - KGB`;
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    this.logger.log(
      `Leaving ${this.exportSelectedData.name} and response: ${JSON.stringify(buffer.byteLength)}`,
    );

    res.end(buffer);
  }

  @Post('approve/batch/v3')
  @RolePermissionNeeded('LIST_PENGAJUAN', ['PERMISSION_IMPORT'])
  @Permission('PERMISSION_CREATE')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async uploadBatchV3(
    @Req() req: any,
    @Body() body: ApproveKGBRequestBodyDTO,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.uploadBatchV3.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.kenaikanGajiBerkalaService.processSelectedKGBWithFilesV3(
        req,
        JSON.parse(body.data),
        files,
      );
    this.logger.log(
      `Leaving ${this.uploadBatchV3.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/import/template')
  @RolePermissionNeeded('LIST_PENGAJUAN', ['PERMISSION_IMPORT'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getRekapUploadData(@Req() req: any, @Res() res: Response) {
    this.logger.log(`Entering ${this.getRekapUploadData.name}`);
    const buffer =
      await this.kenaikanGajiBerkalaService.getRekapUploadData(req);

    const filename = `Template - KGB`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.getRekapUploadData.name} with response: ${JSON.stringify(buffer.byteLength)}`,
    );

    res.end(buffer);
  }

  @Get('/saya')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getMyDataForKGB(@Req() req: any) {
    this.logger.log(`Entering ${this.getMyDataForKGB.name}`);
    const response = await this.kenaikanGajiBerkalaService.getMyDataForKGB(req);

    this.logger.log(
      `Leaving ${this.getMyDataForKGB.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/download/pengajuan')
  @RolePermissionNeeded('LIST_PENGAJUAN', ['PERMISSION_DOWNLOAD'])
  @Permission('PERMISSION_READ')
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async downloadPengajuan(@Req() req: any, @Res() res: Response) {
    const buffer = await this.kenaikanGajiBerkalaService.downloadPengajuan(req);
    const filename = `List Pengajuan`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);

    this.logger.log(
      `Leaving ${this.downloadPengajuan.name} with response: ${JSON.stringify(buffer.byteLength)}`,
    );

    res.end(buffer);
  }
}
