import { forwardRef, Module } from '@nestjs/common';
import { KenaikanGajiBerkalaService } from './service/kenaikan-gaji-berkala.service';
import { KenaikanGajiBerkalaController } from './controller/kenaikan-gaji-berkala.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KenaikanGajiBerkalaController],
  providers: [KenaikanGajiBerkalaService, MinioService],
})
export class KenaikanGajiBerkalaModule {}
