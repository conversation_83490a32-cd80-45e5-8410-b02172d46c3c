import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  ApproveKGBDataDTO,
  ExportKGBDataDTO,
} from '../dto/kenaikan-gaji-berkala.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import * as moment from 'moment';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import * as exceljs from 'exceljs';
import * as dayjs from 'dayjs';

@Injectable()
export class KenaikanGajiBerkalaService {
  constructor(
    private readonly minioService: MinioService,
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getRekapUploadData(req: any) {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('KGB');
    const header = [
      'Nama',
      'NRP',
      'Pangkat',
      'Jabatan',
      'TMT',
      'Masa Dinas Surut',
      'Gaji Baru',
      'TMT Kenaikan Gaji',
      'No KEP Kenaikan Gaji',
      'Status',
    ];

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell, index) => {
      if (index > 6) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {
            argb: '99f7f701',
          },
        };
      }
    });

    worksheet.addRow([
      'Asep Gunawan',
      '123456789',
      'IPDA',
      'PAMA POLDA JABAR',
      '16-07-2024',
      '',
      '3600000',
      '',
      '',
      'MS/TMS',
    ]);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        [],
      ),
    );

    return workbook.xlsx.writeBuffer();
  }

  async getListUpcoming(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      personel_id: { field: 'personel_id', type: 'number' },
      nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
      nrp: { field: 'personel.nrp', type: 'string' },
      pangkat_nama: {
        field: 'personel.pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'personel.jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    let maxDate = moment(new Date()).add(1, 'month');
    let twoYearsAgo = moment().subtract(2, "year")



    const formattedWhere = {
      ...where,
      kgb_selanjutnya: {
        lt: new Date()
      },
      kgb_personel: {
        tmt : {
          lt: new Date()
        }
      },
      personel : {
        status_aktif_id : 1
      }
    };

    const [totalData, kgbMasterData] = await this.prisma.$transaction([
      this.prisma.mv_kgb_terakhir.count({
        where: formattedWhere,
      }),
      this.prisma.mv_kgb_terakhir.findMany({
        select: {
          kgb_selanjutnya : true,
          kgb_personel : true,
          // kgb_personel :{
          //   id: true,
          //   kep_file:true,
          //   // last_kep_file: true,
          //   tanggal_kgb_berikutnya: true,
          //   gaji_pokok_lama: true,
          //   gaji_pokok_baru: true,
          // },
          // last_calculation_date: true,
          // alasan_penundaan: true,
          personel: {
            select: {
              id: true,
              nrp: true,
              nama_lengkap: true,
              masa_dinas_surut_tmt: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  id: true,
                  pangkat_id: true,
                  tmt: true,
                  kep_file: true,
                  kep_nomor: true,
                  pangkat: {
                    select: {
                      nama: true,
                      nama_singkat : true,
                    },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: { tmt_jabatan: 'desc' },
                take: 1,
              },
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        where: formattedWhere,
        orderBy
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    let queryResult = [];


    for (let kgbData of kgbMasterData) {
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData.personel.foto_file}`,
      );
      let firstPangkat = await this.prisma.pangkat_personel.findFirst({
        select: {
          tmt: true,
          kep_file: true,
          kep_nomor: true,
        },
        where: {
          personel_id: kgbData.personel.id,
        },
        orderBy: {
          tmt: 'asc',
        },
      });
      let tmt_for_calculation = kgbData.personel.masa_dinas_surut_tmt
        ? kgbData.personel.masa_dinas_surut_tmt
        : firstPangkat.tmt;

      let mkg_year = Math.abs(moment().diff(tmt_for_calculation, 'year'));
      let mkg_month = Math.abs(
        moment().diff(tmt_for_calculation, 'month') % 12,
      );

      let gajiBerlaku = kgbData.personel.pangkat_personel[0]
        ? await this.prisma.kgb_gaji_pangkat.findFirst({
          select: {
            gaji: true,
          },
          where: {
            mkg: {
              gte: mkg_year,
              lte: mkg_year + 2,
            },
            pangkat_id: kgbData.personel.pangkat_personel[0].pangkat_id,
          },
        })
        : null;

      const lastKepFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData?.kgb_personel?.kep_file}`,
      );
      const firstPangkatKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${firstPangkat.kep_file}`,
      );
      const pangkatTerbaruKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData.personel.pangkat_personel?.[0].kep_file}`,
      );
    //
      queryResult.push({
        ...kgbData.kgb_personel,
        id : kgbData.personel.id,
        kgb_selanjutnya : kgbData.kgb_selanjutnya,
        tmt: firstPangkat?.tmt,
        mkg_year: mkg_year,
        mkg_month: mkg_month,
        next_gaji: gajiBerlaku?.gaji,
        last_kep_file: pangkatTerbaruKep,
        personel: {
          ...kgbData.personel,
          first_pangkat: {
            ...firstPangkat,
            kep_file: firstPangkatKep,
          },
          pangkat: kgbData.personel.pangkat_personel?.[0]?.pangkat
            ? {
              ...kgbData.personel.pangkat_personel?.[0]?.pangkat,
              kep_file: pangkatTerbaruKep,
              kep_nomor: kgbData.personel.pangkat_personel?.[0].kep_nomor,
            }
            : null,
          jabatan: kgbData.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan:
            kgbData.personel.jabatan_personel[0]?.jabatans?.satuan ?? null,
          foto_file: fotoFile,
        },
      });
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListApproved(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search, date_start, date_end } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      personel_id: { field: 'personel_id', type: 'number' },
      nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
      nrp: { field: 'personel.nrp', type: 'string' },
      pangkat_nama: {
        field: 'personel.pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'personel.jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    let minDate = moment(new Date()).subtract(1, 'month');
    const formattedWhere = {
      ...where,
      tmt : {
        gte : dayjs(date_start).set("hour",0).set("minute",0).toDate(),
        lt : dayjs(date_end).add(1, "day").set("hour",0).set("minute",0).toDate()
      }
    };

    if (orderBy.length === 0) {
      orderBy.push({ id: 'desc' });
    }

    const [totalData, kgbMasterData] = await this.prisma.$transaction([
      this.prisma.kgb_personel.count({
        where: formattedWhere,
      }),
      this.prisma.kgb_personel.findMany({
        select: {
          id: true,
          personel_id : true,
          tmt : true,
          kep_nomor : true,
          kep_file : true,
          gaji_pokok_baru: true,
          tanggal_kgb_berikutnya : true,
          personel: {
            select: {
              id: true,
              nrp: true,
              nama_lengkap: true,
              masa_dinas_surut_tmt: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  id: true,
                  pangkat_id: true,
                  tmt: true,
                  kep_file: true,
                  kep_nomor: true,
                  pangkat: {
                    select: {
                      nama: true,
                      nama_singkat: true
                    },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: { tmt_jabatan: 'desc' },
                take: 1,
              },
            },
          },
          mv_kgb_terakhir : true
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: formattedWhere,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    let queryResult = [];

    for (let kgbData of kgbMasterData) {
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData.personel.foto_file}`,
      );

      let firstJabatan = await this.prisma.jabatan_personel.findFirst({
        select: {
          tmt_jabatan: true,
        },
        where: {
          personel_id: kgbData.personel.id,
        },
        orderBy: {
          tmt_jabatan: 'asc',
        },
      });
      let firstPangkat = await this.prisma.pangkat_personel.findFirst({
        select: {
          tmt: true,
          kep_file: true,
          kep_nomor: true,
        },
        where: {
          personel_id: kgbData.personel.id,
        },
        orderBy: {
          tmt: 'asc',
        },
      });

      const lastKepFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData.kep_file}`,
      );


      const firstPangkatKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${firstPangkat.kep_file}`,
      );
      const pangkatTerbaruKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${kgbData.personel.nrp}/${kgbData.personel.pangkat_personel?.[0].kep_file}`,
      );

      let tmt_for_calculation = kgbData.personel.masa_dinas_surut_tmt
        ? kgbData.personel.masa_dinas_surut_tmt
        : firstPangkat.tmt;

      let mkg_year = Math.abs(moment().diff(tmt_for_calculation, 'year'));
      let mkg_month = Math.abs(
        moment().diff(tmt_for_calculation, 'month') % 12,
      );

      queryResult.push({
        ...kgbData,
        mkg_year: mkg_year,
        mkg_month: mkg_month,
        last_kep_file: pangkatTerbaruKep,
        kgb_selanjutnya : kgbData?.tanggal_kgb_berikutnya,
        // tmt_awal : kgbData?.mv_kgb_terakhir?.tmt_awal,
        personel: {
          ...kgbData.personel,
          pangkat: kgbData.personel.pangkat_personel?.[0]?.pangkat
            ? {
                ...kgbData.personel.pangkat_personel?.[0]?.pangkat,
                kep_file: pangkatTerbaruKep,
                kep_nomor: kgbData.personel.pangkat_personel?.[0].kep_nomor,
              }
            : null,
          jabatan: kgbData.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan:
            kgbData.personel.jabatan_personel[0]?.jabatans?.satuan ?? null,
          foto_file: fotoFile,
          first_pangkat: {
            ...firstPangkat,
            kep_file: firstPangkatKep,
          },
        },
      });
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  
  async getListGajiPokok(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    /* const formattedWhere = {
      ...where,
    }; */

    const [totalData, result] = await this.prisma.$transaction([
      this.prisma.$queryRawUnsafe(`SELECT COUNT (DISTINCT pangkat_id) FROM public.kgb_gaji_pangkat`),
      this.prisma.kgb_gaji_pangkat.findMany({
        select: {
          id: true,
          pangkat: {
            select: {
              id: true,
              nama: true,
              nama_singkat: true,
            },
          },
          updated_at: true,
        },
        distinct: ['pangkat_id'],
        take: +limit,
        skip: +limit * (+page - 1),
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(parseInt(totalData[0].count) / limit);


    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: result,
    };
  }

  async getDetailGajiPokok(req, paginationData, searchandsortData, pangkat_id) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    /* const formattedWhere = {
      ...where,
      pangkat_id
    }; */

    const [totalData, result] = await this.prisma.$transaction([
      this.prisma.kgb_gaji_pangkat.count({
        where: {
          pangkat_id,
        },
      }),
      this.prisma.kgb_gaji_pangkat.findMany({
        select: {
          id: true,
          pangkat: {
            select: {
              nama: true,
              nama_singkat: true
            },
          },
          mkg: true,
          gaji: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        where: {
          pangkat_id,
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);


    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: result,
    };
  }

  async createDetailGajiPokok(req, body, pangkat_id) {
    try {
      if (!pangkat_id) throw new BadRequestException('Pangkat Id cannot be empty!');

      const queryResult = await this.prisma.kgb_gaji_pangkat.create({
          data: {
            pangkat_id: Number(pangkat_id),
            mkg: Number(body.mkg),
            gaji: Number(body.gaji),
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateDetailGajiPokok(req, body, id) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.kgb_gaji_pangkat.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Detail gaji pokok id ${id} not found!`);

      let data = {
        mkg: Number(body.mkg),
        gaji: Number(body.gaji),
      };

      const queryResult = await this.prisma.kgb_gaji_pangkat.update({
        where: { id: +id },
        data: {
          ...data,
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteDetailGajiPokok(req, body, id) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.kgb_gaji_pangkat.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Detail gaji pokok id ${id} not found!`);

      let queryResult = await this.prisma.kgb_gaji_pangkat.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async exportSelectedData(req: any, queries: ExportKGBDataDTO) {
    let { ids } = queries;

    const { kgbMasterData } = await this.getListForExport(ids);

    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('KGB');
    const header = [
      'Nama',
      'NRP',
      'Pangkat',
      'Jabatan',
      'TMT',
      'Masa Dinas Surut',
      'Gaji Baru',
      'TMT Kenaikan Gaji',
      'No KEP Kenaikan Gaji',
      'Status',
    ];

    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell, index) => {
      if (index > 6) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {
            argb: '99f7f701',
          },
        };
      }
    });
    for (let kgb of kgbMasterData) {
      const newRow = worksheet.addRow([
        kgb.personel?.nama_lengkap,
        kgb.personel?.nrp,
        kgb?.personel?.pangkat?.nama_singkat,
        kgb?.personel?.jabatan?.nama,
        kgb.tmt,
        kgb.personel?.masa_dinas_surut_tmt,
        kgb.next_gaji,
        '',
        '',
        'MS/TMS',
      ]);

      newRow.eachCell((cell, index) => {

        if(index === 10){
          cell.dataValidation = {
            type: 'list',
            allowBlank: false,
            formulae: [`"MS,TMS"`],
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: 'Kondisi Tidak Valid',
            error: 'Harap Pilih MS/TMS',
          };
        }

        if(index === 9){
          cell.dataValidation = {
            type: 'custom',
            allowBlank: false,
            formulae: [`=NOT(ISBLANK(${cell.address}))`],
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: 'Kondisi Tidak Valid',
            error: 'Kolom ini wajib diisi',
          };
        }

        if(index === 8){
          cell.dataValidation = {
            type: 'custom',
            allowBlank: false,
            showErrorMessage: true,
            errorStyle: 'stop',
            formulae: [`=NOT(ISBLANK(${cell.address}))`],
            errorTitle: 'Kondisi Tidak Valid',
            error: 'Kolom ini wajib diisi',
          };
        }

        if(index === 8){
          cell.dataValidation = {
            type: 'date',
            allowBlank: false,
            showErrorMessage: true,
            formulae: [new Date(1900, 0, 1)],
            errorStyle: 'stop',
            errorTitle: 'Kondisi Tidak Valid',
            error: 'Kolom ini wajib diisi dengan tanggal',
          };
        }

        if(index === 7){
          cell.dataValidation = {
            type: 'whole',
            allowBlank: false,
            showErrorMessage: true,
            formulae: [`=NOT(ISBLANK(${cell.address}))`],
            errorStyle: 'stop',
            errorTitle: 'Kondisi Tidak Valid',
            error: 'Kolom ini wajib diisi dengan angka',
          };
        }

        if (index > 6) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {
              argb: '99f7f701',
            },
          };
        }
      });
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        kgbMasterData,
      ),
    );

    return await workbook.xlsx.writeBuffer();
  }

  async exportSelectedPenerbitanData(req: any, searchandsortData) {

    const { date_start, date_end } = searchandsortData;

    const kgbMasterData = await this.prisma.kgb_personel.findMany({
      select: {
        personel: {
          select: {
            id: true,
            nrp: true,
            nama_lengkap: true,
            mv_pangkat_terakhir: {
              select : {
                mv_pangkat_terakhir_pangkat_sekarang : {
                  select : {
                    nama : true
                  }
                }
              }
            },
            mv_latest_jabatan_personel : {
              select : {
                jabatan_sekarang : {
                  select : {
                    nama : true
                  }
                }
              }
            }
          },
        },
        tmt : true,
        gaji_pokok_baru : true,
      },
      where: {
        tmt : {
          gte : dayjs(date_start).set("hour",0).set("minute",0).toDate(),
          lt : dayjs(date_end).add(1, "day").set("hour",0).set("minute",0).toDate()
        }
      }
    })


    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('KGB');
    const header = [
      'Nama',
      'NRP',
      'Pangkat',
      'Jabatan',
      'TMT',
      'Gaji',
    ];

    const headerRow = worksheet.addRow(header);

    for (let kgb of kgbMasterData) {
      // console.log("KGB",kgb)
      const newRow = worksheet.addRow([
        kgb.personel?.nama_lengkap,
        kgb.personel?.nrp,
        kgb?.personel?.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang?.nama,
        kgb?.personel?.mv_latest_jabatan_personel?.jabatan_sekarang?.nama,
        kgb.tmt,
        kgb.gaji_pokok_baru
      ]);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        kgbMasterData,
      ),
    );

    return await workbook.xlsx.writeBuffer();
  }

  async getListForExport(ids) {
    const [kgbMasterData] = await this.prisma.$transaction([
      this.prisma.mv_kgb_terakhir.findMany({
        select: {
          kgb_personel : true,
          personel: {
            select: {
              id: true,
              nrp: true,
              nama_lengkap: true,
              masa_dinas_surut_tmt: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  id: true,
                  pangkat_id: true,
                  tmt: true,
                  kep_file: true,
                  kep_nomor: true,
                  pangkat: {
                    select: {
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: { tmt_jabatan: 'desc' },
                take: 1,
              },
            },
          },
        },
        // orderBy: orderBy,
        where: {
          personel_id: {
            in: JSON.parse(ids),
          },
        }
      }),
    ]);

    let formattedKGBData = [];

    for (let kgbData of kgbMasterData) {
      let firstJabatan = await this.prisma.jabatan_personel.findFirst({
        select: {
          tmt_jabatan: true,
        },
        where: {
          personel_id: kgbData.personel.id,
        },
        orderBy: {
          tmt_jabatan: 'asc',
        },
      });

      let tmt_for_calculation = kgbData.personel.masa_dinas_surut_tmt
        ? kgbData.personel.masa_dinas_surut_tmt
        : firstJabatan?.tmt_jabatan;

      let mkg_year = Math.abs(moment().diff(tmt_for_calculation, 'year'));
      let mkg_month = Math.abs(
        moment().diff(tmt_for_calculation, 'month') % 12,
      );

      let gajiBerlaku = kgbData.personel.pangkat_personel[0]
        ? await this.prisma.kgb_gaji_pangkat.findFirst({
            select: {
              gaji: true,
            },
            where: {
              mkg: {
                gte: mkg_year,
                lte: mkg_year + 2,
              },
              pangkat_id: kgbData.personel.pangkat_personel[0].pangkat_id,
            },
          })
        : null;

      formattedKGBData.push({
        ...kgbData,
        tmt: firstJabatan?.tmt_jabatan,
        mkg_year: mkg_year,
        mkg_month: mkg_month,
        next_gaji: gajiBerlaku?.gaji,
        personel: {
          ...kgbData.personel,
          pangkat: kgbData.personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: kgbData.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan:
            kgbData.personel.jabatan_personel[0]?.jabatans?.satuan ?? null,
        },
      });
    }

    return { kgbMasterData: formattedKGBData };
  }

  async getKGBLogs(req: any, id: number) {
    try {
      let queryResult = await this.prisma.$transaction(async (trx) => {
        let existingData = await trx.kgb_personel.findMany({
          where: {
            personel_id: id,
          },
          orderBy: {
            created_at: 'desc',
          },
        });

        let userData = await trx.personel.findUnique({
          where: {
            id: id,
          },
        });

        let formatted = [];
        for (let obj of existingData) {
          const kepFile = await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${userData.nrp}/${obj.kep_file}`,
          );
          formatted.push({
            ...obj,
            kep_file: kepFile,
          });
        }
        return formatted;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async processSelectedKGBWithFilesV3(
    req: Request,
    allData: ApproveKGBDataDTO[],
    files: any,
  ) {
    try {

      const toCreate = []

      let fileIndex = 0
      for (let adIndex in allData) {
        let ad = allData[adIndex];


        if (ad.status === 'MS') {
          let file = files.files[fileIndex];
          fileIndex++;

          await this.prisma.$transaction(async (trx) => {

            let personelData = await this.prisma.personel.findFirst({
              where :  {
                nrp : ad.nrp
              }
            })

            let uploadedFile = await this.minioService.uploadFileWithNRP(
              file,
              personelData.nrp,
            );

            let firstPangkat = await this.prisma.pangkat_personel.findFirst({
              select: {
                tmt: true,
              },
              where: {
                personel_id: personelData.id,
              },
              orderBy: {
                tmt: 'asc',
              },
            });
            let tmtAwal = personelData.masa_dinas_surut_tmt
              ? personelData.masa_dinas_surut_tmt
              : firstPangkat?.tmt;

            let tanggalKgbBerikutnyaDuration =  moment(ad.tmt).diff(tmtAwal, 'year')  - moment(ad.tmt).diff(tmtAwal, 'year') % 2 + 2;

            toCreate.push({
              personel_id : personelData.id,
              tmt : moment(ad.tmt).toDate(),
              kep_nomor : ad.kep_nomor,
              kep_file : uploadedFile.filename,
              gaji_pokok_baru : ad.gaji_pokok_baru,
              tanggal_kgb_berikutnya:  moment(tmtAwal).add(tanggalKgbBerikutnyaDuration, "year").toDate()
            })
          });
        } else {
          //Handle TMS
        }
      }

      const queryResult = await this.prisma.$transaction(
        async (tx) => {
          await tx.kgb_personel.createMany({
            data: toCreate,
          })
        },
        {
          timeout : 15000
        }
      )

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File, nrp: String) {
    try {
      const uploaded = await this.minioService.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }

  async getMyDataForKGB(req: any) {
    const [kgbData] = await this.prisma.$transaction([
      this.prisma.kgb_personel.findMany({
        where: {
          personel_id: req['user']['personel_id'],
        },
      }),
    ]);


    const personelData = await this.prisma.personel.findUnique({
      select: {
        id: true,
        nrp: true,
        nama_lengkap: true,
        foto_file : true,
        masa_dinas_surut_tmt: true,
        pangkat_personel: {
          select: {
            id: true,
            pangkat_id: true,
            tmt: true,
            kep_file: true,
            kep_nomor: true,
            pangkat: {
              select: {
                nama: true,
                nama_singkat: true,
              },
            },
          },
          orderBy: { tmt: 'desc' },
          take: 1,
        },
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
          },
          orderBy: { tmt_jabatan: 'desc' },
          take: 1,
        },
      },
      where: {
        id: req['user']['personel_id'],
      },
    });

    let formattedKGB = []
    for(let kgbD of kgbData){
      formattedKGB.push({
        ...kgbD,
        kep_file : await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${kgbD.kep_file}`,
        )
      })
    }

    let firstJabatan = await this.prisma.jabatan_personel.findFirst({
      select: {
        tmt_jabatan: true,
      },
      where: {
        personel_id: req['user']['personel_id'],
      },
      orderBy: {
        tmt_jabatan: 'asc',
      },
    });

    let tmt_for_calculation = personelData.masa_dinas_surut_tmt
      ? personelData.masa_dinas_surut_tmt
      : firstJabatan.tmt_jabatan;

    let mkg_year = Math.abs(moment().diff(tmt_for_calculation, 'year'));
    let mkg_month = Math.abs(moment().diff(tmt_for_calculation, 'month') % 12);

    let gajiBerlaku = personelData.pangkat_personel[0]
      ? await this.prisma.kgb_gaji_pangkat.findFirst({
          select: {
            gaji: true,
          },
          where: {
            mkg: {
              gte: mkg_year,
              lte: mkg_year + 2,
            },
            pangkat_id: personelData.pangkat_personel[0].pangkat_id,
          },
        })
      : null;

    let formattedData = {
      // ...kgbData,
      tmt: firstJabatan?.tmt_jabatan,
      mkg_year: mkg_year,
      mkg_month: mkg_month,
      next_gaji: gajiBerlaku?.gaji,
      personel: {
        ...personelData,
        pangkat: personelData.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan: personelData.jabatan_personel?.[0]?.jabatans ?? null,
        satuan: personelData.jabatan_personel[0]?.jabatans?.satuan ?? null,
        foto_file: await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.foto_file}`,
        )
      },
      kgb_personel : formattedKGB
    };

    const queryResult = { personelData: formattedData };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async downloadPengajuan(req: any) {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet('List Data');

    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Nama', key: 'nama', width: 32 },
      { header: 'NRP', key: 'nrp', width: 32 },
      { header: 'Pangkat', key: 'pangkat', width: 32 },
      { header: 'Jabatan', key: 'jabatan', width: 32 },
      { header: 'SKEP Pertama', key: 'skep_pertama', width: 32 },
      { header: 'SKEP Presiden', key: 'skep_presiden', width: 32 },
      { header: 'Masa Surut', key: 'masa_surut', width: 32 },
      { header: 'SKEP Pangkat', key: 'skep_pangkat', width: 32 },
      { header: 'KEP File', key: 'kep_file', width: 32 },
      { header: 'Status', key: 'status', width: 32 },
    ];

    let tempRows2 = [
      {
        id: 1,
        nama: 'Norman Cahyadi',
        nrp: 123123123,
        pangkat: 'Pembina',
        jabatan: 'KADIV TIK POLRI',
        skep_pertama: 'SKEP/27/XI/2016-SKEP-Pengangkatan-Pertama',
        skep_presiden: 'SKEP/27/XI/2016-SKEP-Presiden',
        masa_surut: 'Surat Masa Dinas Surut',
        skep_pangkat: 'SKEP/27/XI/2016-SKEP-Pangkat-Terakhir',
        kep_file: 'KEP/27/XI/2016',
        status: 'MS',
      },
      {
        id: 10,
        nama: 'Norman Cahyadi',
        nrp: 123123123,
        pangkat: 'Pembina',
        jabatan: 'KADIV TIK POLRI',
        skep_pertama: 'SKEP/27/XI/2016-SKEP-Pengangkatan-Pertama',
        skep_presiden: 'SKEP/27/XI/2016-SKEP-Presiden',
        masa_surut: 'Surat Masa Dinas Surut',
        skep_pangkat: 'SKEP/27/XI/2016-SKEP-Pangkat-Terakhir',
        kep_file: 'KEP/27/XI/2016',
        status: 'TMS',
      },
    ];

    worksheet.addRows(tempRows2);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_GAJI_BERKALA_READ as ConstantLogType,
        message,
        tempRows2,
      ),
    );
    return await workbook.xlsx.writeBuffer();
  }
}
