import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty } from 'class-validator';

export class KenaikanGajiBerkalaDto {
  @IsNotEmpty()
  personel_id: number;

  @IsNotEmpty()
  last_kep_file: string;

  @IsNotEmpty()
  recent_calculation_date: Date;

  @IsNotEmpty()
  created_at: Date;

  @IsNotEmpty()
  modified_at: Date;

  @IsNotEmpty()
  last_calculation_date: Date;

  @IsNotEmpty()
  current_gaji: number;

  tmt: Date;
}

export class ExportKGBDataDTO {
  @IsNotEmpty()
  ids: number[];
}

export class ApproveKGBRequestBodyDTO {
  @IsNotEmpty()
  data: string;
}

export class ApproveKGBDataDTO {
  @IsNotEmpty()
  id: number;
  nrp: string;
  new_gaji: number;
  alasan_penundaan?: string;
  status: string;

  gaji_pokok_baru: number;
  kep_nomor: string;
  tmt: Date;

  // new_upcoming_calculation_date:Date;
}

export class UpdateKenaikanGajiBerkalaDto extends PartialType(
  KenaikanGajiBerkalaDto,
) {}
