import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateBeritaDto,
  GetListByCategory,
  UpdateBeritaDto,
} from '../dto/berita.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { jenis_berita_enum } from '@prisma/client';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import * as moment from 'moment';
import { ISendNotificationMultiple } from '../../core/interfaces/firebase.interface';
import { FirebaseEnum } from '../../core/enums/firebase.enum';
import { JenisDokumenEnum } from '../../core/enums/berita.enum';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class BeritaService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly feUrl = this.configService.get<string>('FE_URL');

  async create(
    req: any,
    createBeritaDto: CreateBeritaDto,
    files: any,
    jenisBerita: jenis_berita_enum,
  ) {
    const { user } = req;
    const satuan_id = user.satuan_id || 1;

    const uploadPromises = [];
    if (files.file) {
      uploadPromises.push(
        this.uploadDocument(files.file[0], JenisDokumenEnum.COVER),
      );
    }

    if (files.file_pendukung) {
      for (const file of files.file_pendukung) {
        uploadPromises.push(
          this.uploadDocument(file, JenisDokumenEnum.PENDUKUNG),
        );
      }
    }

    const uploadResult = await Promise.all(uploadPromises);

    let insertFiles = {};
    if (uploadResult.length) {
      insertFiles = { create: uploadResult };
    }

    const { title, description, is_publish, kategori_id } = createBeritaDto;

    if (jenisBerita === jenis_berita_enum.PENGUMUMAN && !description)
      throw new BadRequestException('Deskripsi tidak boleh kosong');

    // const publishAt = moment().format('YYYY-MM-DD hh:mm:ss.SSS');

    const queryResult = await this.prisma.berita.create({
      data: {
        title,
        description,
        satuan: {
          connect: { id: satuan_id },
        },
        berita_kategori: {
          connect: { id: kategori_id },
        },
        is_publish,
        publish_at: is_publish ? new Date() : null,
        berita_file: insertFiles,
        created_by_users: {
          connect: { id: user.id },
        },
        jenis: jenisBerita,
      },
    });

    if (is_publish && jenisBerita === jenis_berita_enum.PENGUMUMAN) {
      await this._sendNotification(queryResult);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  async getList(
    req: any,
    jenisBerita: jenis_berita_enum,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
    getByCategory: GetListByCategory,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      judul: { field: 'title', type: 'string' },
      deskripsi: { field: 'description', type: 'string' },
      tanggal: { field: 'publish_at', type: 'date' },
      status: { field: 'is_publish', type: 'boolean' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    where['jenis'] = jenisBerita;

    if (getByCategory.category_id) {
      where.kategori_id = getByCategory.category_id;
    }

    const [totalData, beritaData] = await this.prisma.$transaction([
      this.prisma.berita.count({
        where: where,
      }),
      this.prisma.berita.findMany({
        select: {
          id: true,
          title: true,
          description: true,
          is_publish: true,
          publish_at: true,
          created_at: true,
          berita_file: {
            select: {
              id: true,
              filename: true,
              originalname: true,
              key: true,
              url: true,
              jenis_dokumen: true,
            },
            where: { deleted_at: null },
          },
          satuan: {
            select: {
              id: true,
              nama: true,
            },
          },
          berita_kategori: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const queryResult = await this.convertUrlPhoto(beritaData);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async get(req: any, id: number, jenisBerita: jenis_berita_enum) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const queryResult = await this.prisma.berita.findFirst({
      select: {
        id: true,
        title: true,
        description: true,
        is_publish: true,
        publish_at: true,
        created_at: true,
        berita_file: {
          select: {
            id: true,
            filename: true,
            originalname: true,
            key: true,
            url: true,
            jenis_dokumen: true,
          },
          where: { deleted_at: null },
        },
        satuan: {
          select: {
            id: true,
            nama: true,
          },
        },
        berita_kategori: {
          select: {
            id: true,
            nama: true,
          },
        },
      },
      where: { id: id, jenis: jenisBerita, deleted_at: null },
    });

    if (!queryResult) throw new NotFoundException('Berita tidak ditemukan');

    await this.convertUrlPhoto([queryResult]);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async update(req: any, id: number, data: UpdateBeritaDto, files: any) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    const berita = await this.prisma.berita.findFirst({
      where: { id },
      select: {
        id: true,
        title: true,
        description: true,
        satuan_id: true,
        kategori_id: true,
        is_publish: true,
        publish_at: true,
        berita_file: true,
      },
    });

    if (!berita)
      throw new NotFoundException('Berita yang ingin diubah tidak ditemukan');

    const { delete_pendukung_id, satker_id, ...restData } = data;

    const publishAt = data.is_publish ? moment().toDate() : null;
    Object.assign(berita, {
      ...restData,
      publish_at: publishAt,
      kategori_id: BigInt(data.kategori_id),
    });

    const queryResult = await this.prisma.$transaction(async (tx) => {
      const updatedBerita = await tx.berita.update({
        where: { id },
        data: {
          title: berita.title,
          description: berita.description,
          satuan: { connect: { id: berita.satuan_id } },
          berita_kategori: { connect: { id: berita.kategori_id } },
          is_publish: berita.is_publish,
          publish_at: berita.publish_at,
          updated_at: moment().toDate(),
        },
      });

      await this.handleFiles(tx, files, berita, data);

      if (berita.is_publish) {
        berita.description ??= '';
        await this._sendNotification(berita);
      }

      return updatedBerita;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async delete(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');
    const currentDate = moment().toDate();

    const queryResult = await this.prisma.berita.findFirst({
      where: { id },
    });

    if (!queryResult) throw new NotFoundException(`Berita not found`);

    await this.prisma.berita.update({
      where: { id },
      data: {
        deleted_at: currentDate,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getKategori(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      nama: { field: 'nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.berita_kategori.count({
        where: where,
      }),
      this.prisma.berita_kategori.findMany({
        select: {
          id: true,
          nama: true,
          created_at: true,
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BERITA_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BERITA_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      totalPage,
      totalData,
      page,
    };
  }

  //START HELPER
  async uploadDocument(file: any, documentType: JenisDokumenEnum) {
    const document = file;
    const upload = await this.minioService.uploadFile(document);
    delete document.buffer;
    delete document.fieldname;

    return {
      ...document,
      key: upload.Key,
      url: upload.Location,
      filename: upload.filename,
      jenis_dokumen: documentType,
    };
  }

  private async _sendNotification(data: Record<string, any>) {
    const allDevice = await this.prisma.users_device_token.findMany({});

    const mock: ISendNotificationMultiple = {
      title: data.title,
      body:
        data.description?.substring(0, 25)?.replace(/<[^>]*>/g, '') +
          data.description?.length >
        25
          ? '...'
          : '',
      tokens: allDevice,
      data: {
        link: `${this.feUrl}/pengumuman/view/${data.id}`,
      },
      type: 'PENGUMUMAN',
    };
    this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
  }

  private async handleFiles(tx: any, files: any, berita: any, data: any) {
    const currentDate = moment().toDate();

    if (files?.file) {
      const uploadDocument = await this.uploadDocument(
        files.file[0],
        JenisDokumenEnum.COVER,
      );
      const existingCover = berita.berita_file.find(
        (item) => item.jenis_dokumen === JenisDokumenEnum.COVER,
      );

      await tx.berita_file.upsert({
        where: { id: existingCover?.id || 0 },
        update: { ...uploadDocument, updated_at: currentDate },
        create: { ...uploadDocument, berita: { connect: { id: berita.id } } },
      });
    }

    if (files?.file_pendukung) {
      const pendukungFiles = await Promise.all(
        files.file_pendukung.map((file) =>
          this.uploadDocument(file, JenisDokumenEnum.PENDUKUNG),
        ),
      );

      await tx.berita_file.createMany({
        data: pendukungFiles.map((item) => ({ ...item, id_berita: berita.id })),
      });
    }

    if (data.delete_pendukung_id?.length) {
      await tx.berita_file.updateMany({
        where: {
          id: { in: data.delete_pendukung_id.map(Number) },
          jenis_dokumen: JenisDokumenEnum.PENDUKUNG,
        },
        data: { deleted_at: currentDate },
      });
    }
  }

  private async convertUrlPhoto(data: any) {
    for (const berita of data) {
      if (berita.berita_foto && berita.berita_foto.length > 0) {
        for (const foto of berita.berita_foto) {
          foto.url = await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}/${foto.filename}`,
          );
        }
      }
    }

    return data;
  }

  // private async getSatuanBawahan(atasanId: number) {
  //   const bawahanSatuan = await this.prisma.satuan.findMany({
  //     where: {
  //       atasan_id: atasanId,
  //     },
  //   });
  //
  //   const result = [];
  //   result.push(atasanId);
  //
  //   for (const bawahan of bawahanSatuan) {
  //     result.push(bawahan.id);
  //
  //     const childBawahan = await this.getSatuanBawahan(Number(bawahan.id));
  //     result.push(...childBawahan);
  //   }
  //
  //   return result;
  // }
  //
  // async createTelegram(
  //   req: any,
  //   createBeritaTelegramDto: CreateBeritaTelegramDto,
  //   files: any,
  // ) {
  //   const { user } = req;
  //   const satuan_id = user.satuan_id || 1;
  //
  //   const uploadPromises = [];
  //   if (files.file) {
  //     uploadPromises.push(
  //       this.uploadDocument(files.file[0], JenisDokumenEnum.PENDUKUNG),
  //     );
  //   }
  //
  //   const uploadResult = await Promise.all(uploadPromises);
  //
  //   let insertFiles = {};
  //   if (uploadResult.length) {
  //     insertFiles = { create: uploadResult };
  //   }
  //
  //   const { title, is_publish, kategori_id } = createBeritaTelegramDto;
  //   const publishAt = moment().format('YYYY-MM-DD hh:mm:ss.SSS');
  //
  //   return await this.prisma.berita.create({
  //     data: {
  //       title,
  //       satuan_id,
  //       kategori_id,
  //       is_publish,
  //       publish_at: is_publish ? publishAt : null,
  //       berita_file: insertFiles,
  //       created_by_user: user.id,
  //       jenis: jenis_berita_enum.TELEGRAM,
  //     },
  //   });
  // }
  //
  // async getListTelegram(
  //   paginationData: PaginationDto,
  //   searchAndSortData: SearchAndSortDTO,
  // ) {
  //   const { sort_column, sort_desc, search_column, search_text, search } =
  //     searchAndSortData;
  //
  //   const columnMapping: IColumnMapping = {
  //     judul: { field: 'title', type: 'string' },
  //     deskripsi: { field: 'description', type: 'string' },
  //     tanggal: { field: 'publish_at', type: 'date' },
  //     status: { field: 'is_publish', type: 'boolean' },
  //   };
  //
  //   const { orderBy, where } = SortSearchColumn(
  //     sort_column,
  //     sort_desc as any,
  //     search_column,
  //     search_text,
  //     search,
  //     columnMapping,
  //     true,
  //   );
  //
  //   where['jenis'] = jenis_berita_enum.TELEGRAM;
  //
  //   const limit = paginationData.limit || 100;
  //   const page = paginationData.page || 1;
  //
  //   const [totalData, beritaData] = await this.prisma.$transaction([
  //     this.prisma.berita.count({
  //       where: where,
  //     }),
  //     this.prisma.berita.findMany({
  //       select: {
  //         id: true,
  //         title: true,
  //         is_publish: true,
  //         publish_at: true,
  //         created_at: true,
  //         berita_file: {
  //           select: {
  //             id: true,
  //             filename: true,
  //             originalname: true,
  //             key: true,
  //             url: true,
  //             jenis_dokumen: true,
  //           },
  //           where: { deleted_at: null },
  //         },
  //         satuan: {
  //           select: {
  //             id: true,
  //             nama: true,
  //           },
  //         },
  //         berita_kategori: {
  //           select: {
  //             id: true,
  //             nama: true,
  //           },
  //         },
  //       },
  //       take: +limit,
  //       skip: +limit * (+page - 1),
  //       orderBy: orderBy,
  //       where: where,
  //     }),
  //   ]);
  //
  //   const results = await this.convertUrlPhoto(beritaData);
  //
  //   const totalPage = Math.ceil(totalData / limit);
  //
  //   return { results, page, totalPage, totalData };
  // }
  //
  // async getTelegram(id: number) {
  //   if (isNaN(id)) throw new BadRequestException('ID is required');
  //
  //   const berita = await this.prisma.berita.findFirst({
  //     select: {
  //       id: true,
  //       title: true,
  //       is_publish: true,
  //       publish_at: true,
  //       created_at: true,
  //       berita_file: {
  //         select: {
  //           id: true,
  //           filename: true,
  //           originalname: true,
  //           key: true,
  //           url: true,
  //           jenis_dokumen: true,
  //         },
  //         where: { deleted_at: null },
  //       },
  //       satuan: {
  //         select: {
  //           id: true,
  //           nama: true,
  //         },
  //       },
  //       berita_kategori: {
  //         select: {
  //           id: true,
  //           nama: true,
  //         },
  //       },
  //     },
  //     where: { id: id, jenis: jenis_berita_enum.TELEGRAM, deleted_at: null },
  //   });
  //
  //   if (!berita) throw new NotFoundException('Berita tidak ditemukan');
  //
  //   await this.convertUrlPhoto(berita);
  //
  //   return berita;
  // }
  //
  // async updateTelegram(id: number, data: UpdateBeritaTelegramDto, files: any) {
  //   try {
  //     const { title, is_publish, satker_id, kategori_id } = data;
  //
  //     const berita = await this.prisma.berita.findFirst({
  //       where: { id: +id },
  //     });
  //
  //     if (!berita) throw new NotFoundException(`berita ${id} not found`);
  //
  //     berita.title = title;
  //     berita.is_publish = is_publish.toString().toLowerCase() === 'true';
  //     berita.publish_at = berita.is_publish ? new Date() : null;
  //     berita.satuan_id = BigInt(satker_id);
  //     berita.kategori_id = BigInt(kategori_id);
  //
  //     return this.prisma.$transaction(async (prisma) => {
  //       const updateBerita = await prisma.berita.update({
  //         where: { id: +id },
  //         data: {
  //           ...berita,
  //           updated_at: new Date(),
  //         },
  //       });
  //
  //       if (files.file) {
  //         const file = files.file[0];
  //         const upload = await this.uploadDocument(
  //           file,
  //           JenisDokumenEnum.PENDUKUNG,
  //         );
  //
  //         const existingFoto = await prisma.berita_foto.findFirst({
  //           where: {
  //             id_berita: berita.id,
  //             deleted_at: null,
  //             jenis_dokumen: JenisDokumenEnum.PENDUKUNG,
  //           },
  //         });
  //
  //         if (existingFoto) {
  //           // If a photo already exists, update it
  //           await prisma.berita_foto.update({
  //             where: { id: existingFoto.id },
  //             data: {
  //               ...upload,
  //               updated_at: new Date(),
  //             },
  //           });
  //         } else {
  //           await prisma.berita_foto.create({
  //             data: {
  //               ...upload,
  //               id_berita: berita.id,
  //             },
  //           });
  //         }
  //       }
  //
  //       if (berita.is_publish) await this._sendNotification(berita);
  //       // if (berita.is_publish) {
  //       //   const allDevice = await this.prisma.users_device_token.findMany({
  //       //     select: {
  //       //       token: true,
  //       //     },
  //       //   });
  //       //
  //       //   // const existingFoto = await prisma.berita_foto.findFirst({
  //       //   //   where: {
  //       //   //     id_berita: berita.id,
  //       //   //     deleted_at: null,
  //       //   //     jenis_dokumen: JenisDokumen.COVER,
  //       //   //   },
  //       //   // });
  //       //
  //       //   this.firebaseService.sendNotificationMultiple(
  //       //     allDevice.map((item) => item.token),
  //       //     berita.title,
  //       //     '',
  //       //     '',
  //       //     {
  //       //       link: `https://devs-dashboard.satusdm.com/pengumuman/view/${berita.id}`,
  //       //     },
  //       //     // existingFoto.url,
  //       //   );
  //       // }
  //
  //       return updateBerita;
  //     });
  //   } catch (error) {
  //     console.log(error);
  //     throw error;
  //   }
  // }
}
