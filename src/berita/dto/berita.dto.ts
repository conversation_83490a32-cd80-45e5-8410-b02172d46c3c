import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';

export class CreateBeritaDto {
  @IsNotEmpty({ message: 'Judul tidak boleh kosong' })
  @IsString({ message: 'Judul format harus string' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Deskripsi format harus string' })
  description: string;

  // @IsNotEmpty({ message: 'ID satuan tidak boleh kosong' })
  @IsOptional()
  @IsNumber()
  satker_id?: number;

  @IsNotEmpty({ message: 'ID kategori tidak boleh kosong' })
  @IsNumber()
  kategori_id: number;

  @IsBoolean()
  is_publish: boolean;

  @IsOptional()
  file?: any;

  @IsOptional()
  file_pendukung?: any;
}

export class UpdateBeritaDto extends CreateBeritaDto {
  @IsOptional()
  @IsArray()
  delete_pendukung_id: number[];
}

export class CreateBeritaTelegramDto {
  @IsNotEmpty({ message: 'Judul tidak boleh kosong' })
  @IsString({ message: 'Judul format harus string' })
  title: string;

  // @IsNotEmpty({ message: 'ID satuan tidak boleh kosong' })
  @IsOptional()
  @IsNumber()
  satker_id?: number;

  // @IsNotEmpty({ message: 'ID kategori tidak boleh kosong' })
  @IsOptional()
  @IsNumber()
  kategori_id?: number;

  // @IsOptional()
  // @IsNumber()
  // tahun?: number;

  @IsOptional()
  @IsBoolean()
  is_publish?: boolean;
}

export class UpdateBeritaTelegramDto extends CreateBeritaTelegramDto {}

export class GetListByCategory {
  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  category_id: number;
}
