import { forwardRef, Module } from '@nestjs/common';
import { BeritaService } from './service/berita.service';
import { BeritaController } from './controller/berita.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { BeritaKategoriController } from './controller/berita-kategori.controller';
import { BeritaTelegramController } from './controller/berita-telegram.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [
    BeritaController,
    BeritaTelegramController,
    BeritaKategoriController,
  ],
  providers: [BeritaService, MinioService],
})
export class BeritaModule {}
