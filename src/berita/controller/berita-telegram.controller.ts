import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { BeritaService } from '../service/berita.service';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import {
  CreateBeritaDto,
  GetListByCategory,
  UpdateBeritaDto,
} from '../dto/berita.dto';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FieldValidatorPipe } from '../../core/validator/field.validator';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { jenis_berita_enum } from '@prisma/client';

@Controller('berita-telegram')
@UseGuards(JwtAuthGuard)
export class BeritaTelegramController {
  private readonly logger = new Logger(BeritaTelegramController.name);

  constructor(private readonly beritaService: BeritaService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  async create(
    @Req() req: any,
    @Body(new FieldValidatorPipe())
    createBeritaDto: CreateBeritaDto,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(createBeritaDto)}`,
    );

    const response = await this.beritaService.create(
      req,
      createBeritaDto,
      files,
      jenis_berita_enum.TELEGRAM,
    );

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(createBeritaDto)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
    @Query() getByCategory: GetListByCategory,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)}`,
    );

    const response = await this.beritaService.getList(
      req,
      jenis_berita_enum.TELEGRAM,
      paginationData,
      searchAndSortData,
      getByCategory,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getList.name} with id ${id}`);

    const response = await this.beritaService.get(
      req,
      Number(id),
      jenis_berita_enum.TELEGRAM,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body(new FieldValidatorPipe()) body: UpdateBeritaDto,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.beritaService.update(
      req,
      Number(id),
      body,
      files,
    );

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.beritaService.delete(req, Number(id));

    this.logger.log(
      `Leaving ${this.delete.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
