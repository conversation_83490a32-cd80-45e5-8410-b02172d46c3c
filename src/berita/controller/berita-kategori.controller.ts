import {
  <PERSON>,
  Get,
  HttpCode,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { BeritaService } from '../service/berita.service';
import { Permission } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';

@Controller('berita-kategori')
@UseGuards(JwtAuthGuard)
export class BeritaKategoriController {
  private readonly logger = new Logger(BeritaKategoriController.name);

  constructor(private readonly beritaService: BeritaService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getBeritaKategori(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getBeritaKategori.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)}`,
    );

    const response = await this.beritaService.getKategori(
      req,
      paginationData,
      searchAndSortData,
    );

    this.logger.log(
      `Leaving ${this.getBeritaKategori.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
