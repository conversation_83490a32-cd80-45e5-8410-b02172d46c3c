import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiKeyService } from '../service/api-key.service';
import { ApiManagementPaginationDto } from '../../../core/dtos';
import { AddApiToApiKeyDto } from '../dto/api-key.dto';
import { Module, Permission } from '../../../core/decorators';
import { MODULES } from '../../../core/constants/module.constant';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('api-key')
@Module(MODULES.API_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class ApiKeyController {
  private readonly logger = new Logger(ApiKeyController.name);

  constructor(private readonly apiKey: ApiKeyService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async createApiKey(@Req() req: any) {
    this.logger.log(`Entering ${this.createApiKey.name}`);

    const response = await this.apiKey.createApiKey(req);

    this.logger.log(
      `Leaving ${this.createApiKey.name} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/mapping-apis')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async addApiToApiKey(@Req() req: Request, @Body() body: AddApiToApiKeyDto) {
    this.logger.log(
      `Entering ${this.addApiToApiKey.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.apiKey.addApiToApiKey(req, body);

    this.logger.log(
      `Leaving ${this.addApiToApiKey.name} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getApiKeys(
    @Req() req: any,
    @Query() paginationData: ApiManagementPaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.getApiKeys.name} with pagination data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.apiKey.getApiKeyList(req, paginationData);

    this.logger.log(
      `Leaving ${this.getApiKeys.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.apiKey.get(req, parseInt(id, 10));

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:api_key_id/apis/:api_id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async removeApiFromApiKey(
    @Req() req: Request,
    @Param('api_key_id') api_key_id: string,
    @Param('api_id') api_id: string,
  ) {
    this.logger.log(
      `Entering ${this.removeApiFromApiKey.name} with api key id ${api_key_id} and api id ${api_id}`,
    );

    const response = await this.apiKey.removeApiFromApiKey(
      req,
      parseInt(api_key_id, 10),
      parseInt(api_id, 10),
    );

    this.logger.log(
      `Leaving ${this.removeApiFromApiKey.name} with api key id ${api_key_id} and api id ${api_id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
