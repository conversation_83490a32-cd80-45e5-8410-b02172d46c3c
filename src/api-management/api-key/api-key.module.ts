import { Module } from '@nestjs/common';
import { ApiKeyService } from './service/api-key.service';
import { ApiKeyController } from './controller/api-key.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  providers: [ApiKeyService],
  exports: [ApiKeyService],
  controllers: [ApiKeyController],
})
export class ApiKeyModule {}
