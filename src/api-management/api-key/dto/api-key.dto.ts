import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class AddApiToApiKeyDto {
  @IsNumber()
  @IsNotEmpty({ message: 'api_id not provided' })
  public api_id: number;

  @IsString()
  @IsNotEmpty({ message: 'api_key not provided' })
  public api_key: string;
}

export class RemoveApiFromApiKeyDto {
  @IsString()
  @IsNotEmpty({ message: 'api_id not provided' })
  public api_id: number;
}
