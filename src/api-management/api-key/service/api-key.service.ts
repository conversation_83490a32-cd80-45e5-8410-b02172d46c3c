import * as CryptoJS from 'crypto-js';
import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto } from '../../../core/dtos';

@Injectable()
export class ApiKeyService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getApiKeyList(req: any, paginationData: PaginationDto) {
    const { limit, page } = paginationData;

    const [totalData, queryData] = await this.prisma.$transaction([
      this.prisma.api_key.count({
        where: {
          deleted_at: null,
        },
      }),
      this.prisma.api_key.findMany({
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          created_at: 'desc',
        },
        where: {
          deleted_at: null,
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = { queryData, page, totalPage, totalData };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.API_KEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_KEY_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully get Api Key List',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryData,
    };
  }

  async createApiKey(req: any) {
    const randomBytes = CryptoJS.lib.WordArray.random(32); // 256 bits
    const generatedApiKey = CryptoJS.enc.Hex.stringify(randomBytes);
    const queryResult = await this.prisma.api_key.create({
      data: {
        api_key: generatedApiKey,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_KEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_KEY_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async get(req: any, id) {
    const queryResult = await this.prisma.api_key.findFirst({
      where: { id, deleted_at: null },
      select: {
        id: true,
        api_key: true,
        created_at: true,
        updated_at: true,
        akses_api_key: {
          select: {
            api: true,
          },
        },
      },
    });

    if (!queryResult) {
      throw new BadRequestException('Api key id not found.');
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_KEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_KEY_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async addApiToApiKey(req, body) {
    const { api_id } = body;

    const apiKey = await this.prisma.api_key.findFirst({
      where: { api_key: body?.api_key, deleted_at: null },
      include: { akses_api_key: true },
    });

    if (!apiKey) {
      throw new BadRequestException('Invalid api_key id');
    }

    const api = await this.prisma.api.findFirst({
      where: { id: api_id, deleted_at: null },
    });

    if (!api) {
      throw new BadRequestException('Invalid api_id');
    }

    const foundApi = apiKey.akses_api_key.find(
      (item) => item.api_id === api.id,
    );

    if (foundApi) {
      throw new ConflictException('API already exists.');
    }

    const updatedApiKey = await this.prisma.akses_api_key.upsert({
      where: {
        api_key_id_api_id: {
          api_id: api.id,
          api_key_id: apiKey.id,
        },
      },
      update: {},
      create: {
        api_id: api.id,
        api_key_id: apiKey.id,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.API_KEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_KEY_UPSERT as ConstantLogType,
        message,
        updatedApiKey,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: updatedApiKey,
    };
  }

  async removeApiFromApiKey(req, api_key_id: number, api_id: number) {
    const apiKey = await this.prisma.api_key.findFirst({
      where: { id: api_key_id, deleted_at: null },
      include: { akses_api_key: true },
    });

    if (!apiKey) {
      throw new BadRequestException('Invalid api_key id');
    }

    const api = await this.prisma.api.findFirst({
      where: { id: api_id, deleted_at: null },
    });

    if (!api) {
      throw new BadRequestException('Invalid api_id');
    }

    const foundApi = apiKey.akses_api_key.find(
      (item) => item.api_id === api.id,
    );

    if (!foundApi) {
      throw new ConflictException('Invalid api_id');
    }

    const queryResult = await this.prisma.akses_api_key.delete({
      where: {
        api_key_id_api_id: {
          api_id: api.id,
          api_key_id: apiKey.id,
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_KEY_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_KEY_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
