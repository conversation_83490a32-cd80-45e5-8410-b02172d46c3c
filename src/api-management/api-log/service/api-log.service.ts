import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { ILogApi } from '../../../core/interfaces/log.interface';

@Injectable()
export class ApiLogService {
  private readonly logger = new Logger(ApiLogService.name);

  constructor(private readonly prisma: PrismaService) {}

  async addLogsApiActivity(logData: ILogApi) {
    const {
      api_id,
      user_agent,
      method,
      activity,
      table,
      payload,
      url,
      headers,
    } = logData;

    const data = {
      api_id,
      user_agent,
      action: JSON.stringify({
        url,
        headers,
        method,
        activity,
        table,
        payload,
      }),
      created_at: new Date(),
    };
    try {
      await this.prisma.log_management_api.create({ data });
    } catch (error) {
      this.logger.error(
        `Error ${this.addLogsApiActivity.name} dengan data ${JSON.stringify(data)} karena ${error.toString()}`,
      );
    }
  }
}
