import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiService } from './service/api.service';
import { APIController } from './controller/api.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { ApiLogModule } from '../api-log/api-log.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';
import { MinioService } from '../../api-utils/minio/service/minio.service';

@Module({
  imports: [PrismaModule, ApiLogModule, LogsActivityModule],
  providers: [ApiService, ConfigService, MinioService],
  exports: [ApiService],
  controllers: [APIController],
})
export class APIModule {}
