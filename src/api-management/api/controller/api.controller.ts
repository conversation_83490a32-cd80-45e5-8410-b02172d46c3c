import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiService } from '../service/api.service';
import { ApiManagementPaginationDto } from '../../../core/dtos';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@Controller('api')
export class APIController {
  private readonly logger = new Logger(APIController.name);

  constructor(private readonly apiService: ApiService) {}

  @Post('/post/*')
  @HttpCode(200)
  async apiPost(
    @Req() req: Request,
    @Param() params: Record<string, string>,
    @Body() fieldData: any,
  ) {
    this.logger.log(
      `Entering ${this.apiPost.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(fieldData)}`,
    );

    const api_url = params[0];
    const response = await this.apiService.post(req, api_url, fieldData);

    this.logger.log(
      `Leaving ${this.apiPost.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(fieldData)} and response ${JSON.stringify(response)} `,
    );
    return response;
  }

  @Get('/get/*')
  @HttpCode(200)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async apiGet(
    @Req() req: Request,
    @Param() params: Record<string, string>,
    @Query() paginationData: ApiManagementPaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.apiGet.name} with params ${JSON.stringify(params)} and query pagination data ${JSON.stringify(paginationData)}`,
    );

    const api_url = params[0];
    const response = await this.apiService.get(req, api_url, paginationData);

    this.logger.log(
      `Leaving ${this.apiGet.name} with params ${JSON.stringify(params)} and query pagination data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)} `,
    );

    return response;
  }

  @Put('/put/*/:id')
  @HttpCode(200)
  async apiPut(
    @Req() req: any,
    @Param() params: Record<string, string>,
    @Body() fieldData: any,
  ) {
    this.logger.log(
      `Entering ${this.apiPut.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(fieldData)}`,
    );

    const api_url = params[0];
    const response = await this.apiService.put(req, api_url, fieldData);

    this.logger.log(
      `Leaving ${this.apiPut.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(fieldData)} and response ${JSON.stringify(response)} `,
    );

    return response;
  }

  @Get('/log-activity')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async getLogActivity(
    @Req() req: any,
    @Query('user_agent') user_agent: string,
    @Query('action') action: string,
    @Query() paginationData: ApiManagementPaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.getLogActivity.name} with user agent ${user_agent} and action ${action} and pagination data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.apiService.getLogActivity(
      req,
      { user_agent, action },
      paginationData,
    );

    return response;
  }
}
