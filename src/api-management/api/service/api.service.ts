import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ServiceUnavailableException,
  UnauthorizedException,
} from '@nestjs/common';
import { api_method_enum, Prisma } from '@prisma/client';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { parseSearchString } from '../../../core/utils/common.utils';
import {
  convertStringToPostgresValue,
  convertToPostgresValue,
} from '../../../core/utils/db.utils';
import { ApiLogService } from '../../api-log/service/api-log.service';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { CONSTANT_REST_METHOD } from '../../../core/constants/api-management.constant';
import { ConstantRestMethodType } from '../../../core/interfaces/api-management.type';
import {
  convertToILogApiData,
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { ApiManagementPaginationDto } from '../../../core/dtos';
import { MinioService } from '../../../api-utils/minio/service/minio.service';

@Injectable()
export class ApiService {
  private readonly logger = new Logger(ApiService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly apiLogService: ApiLogService,
    private readonly logsActivityService: LogsActivityService,
    private readonly minioService: MinioService,
  ) {}

  async post(req, apiUrl, data) {
    const apiKey = await this.validateApiKey(req);
    const api = await this.validateApi(apiUrl, CONSTANT_REST_METHOD.POST);
    await this.validateApiKeyAccess(api, apiKey);

    const payload = await this.mapData(api, data);
    const selectedColumns = await this.getSelectedColumns(
      api,
      CONSTANT_REST_METHOD.POST,
    );

    const filteredColumns = {};
    Object.keys(selectedColumns).map((key) => {
      if (selectedColumns[key]) {
        filteredColumns[key] = selectedColumns[key];
      }
    });

    const result = await this.prisma[api?.table].create({
      data: payload,
      select: filteredColumns,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_MODULE,
    );
    await this.apiLogService.addLogsApiActivity(
      convertToILogApiData(
        req,
        api,
        CONSTANT_REST_METHOD.POST,
        CONSTANT_LOG.API_CREATE as ConstantLogType,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      result,
    };
  }

  async put(req, apiUrl, data) {
    const id = parseInt(req.params.id, 10) || null;
    const api = await this.validateApi(apiUrl, CONSTANT_REST_METHOD.PUT);
    const apiKey = await this.validateApiKey(req);
    await this.validateApiKeyAccess(api, apiKey);

    const payload = await this.mapData(api, data);

    const columnExist = await this.prisma.$queryRaw<any[]>`
        SELECT *
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = 'public'
          AND TABLE_NAME = ${api?.table}
          AND COLUMN_NAME = 'nrp'
    `;

    const where =
      columnExist.length <= 0
        ? { id: BigInt(id) }
        : { OR: [{ id: BigInt(id) }, { nrp: id.toString() }] };
    const result = await this.prisma[api?.table].updateMany({
      where,
      data: payload,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_MODULE,
    );
    await this.apiLogService.addLogsApiActivity(
      convertToILogApiData(
        req,
        api,
        CONSTANT_REST_METHOD.PUT,
        CONSTANT_LOG.API_UPDATE as ConstantLogType,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      result,
    };
  }

  async getColumnDataTypeInfo(tablename: string, checkKeys: string[]) {
    return await this.prisma.$queryRaw<any[]>`
        SELECT column_name, data_type
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = 'public'
          AND TABLE_NAME = ${tablename}
          AND COLUMN_NAME IN (${Prisma.join(checkKeys)})
    `;
  }

  async getRelationshipTableInfo(
    tablename: string,
  ): Promise<{ foreign_column; referenced_table: string }[]> {
    return await this.prisma.$queryRaw<
      { foreign_column; referenced_table: string }[]
    >`
        SELECT kcu.column_name AS foreign_column,
               ccu.table_name  AS referenced_table
        FROM information_schema.key_column_usage kcu
                 JOIN
             information_schema.referential_constraints rc
             ON kcu.constraint_name = rc.constraint_name
                 JOIN
             information_schema.constraint_column_usage ccu
             ON ccu.constraint_name = rc.unique_constraint_name
        WHERE kcu.table_schema = 'public'
          AND kcu.table_name = ${tablename};
    `;
  }

  private async includeColumn(tablename: any) {
    const relationshipTableInfo =
      await this.getRelationshipTableInfo(tablename);

    return relationshipTableInfo.reduce((acc, data) => {
      acc[data.foreign_column] = false;
      acc[data.referenced_table] = true;
      return acc;
    }, {});
  }

  private async selectColumn(
    columnInfoKeys: any,
    includedRelationshipKeys: any,
  ) {
    return Object.keys(columnInfoKeys).reduce((acc, columnInfoKey) => {
      if (!includedRelationshipKeys[columnInfoKey]) {
        acc[columnInfoKey] = true;
      }
      return acc;
    }, {});
  }

  private async getSelectedColumns(api: any, method: ConstantRestMethodType) {
    const tableDef = Prisma.dmmf.datamodel.models.find(
      (m) => m.name === api?.table,
    );

    if (!tableDef) return [];

    const columns = tableDef.fields
      .filter((f) => f.kind !== 'object')
      .map((f) => ({ [f.name]: true }));
    const relations = tableDef.fields
      .filter((f) => f.kind === 'object')
      .map((f) => ({ [f.name]: f.relationFromFields[0] }));

    const includedRelationshipColumn = {};
    for (const relation of relations) {
      Object.keys(relation).map((key) => {
        if (relation[key]) {
          includedRelationshipColumn[key] = true;
          includedRelationshipColumn[relation[key]] = false;
        }
      });
    }

    const selectedColumn = await this.selectColumn(
      Object.assign({}, ...columns),
      includedRelationshipColumn,
    );
    const mapColumn = {
      ...selectedColumn,
      ...includedRelationshipColumn,
    };

    this.filterColumn(method, api, mapColumn);
    return mapColumn;
  }

  private filterColumn(
    method: ConstantRestMethodType,
    api: any,
    mapColumn: any,
  ) {
    switch (method) {
      case CONSTANT_REST_METHOD.GET:
        Object.keys(mapColumn).forEach((column) => {
          mapColumn[column] = api?.api_fields.some(
            (field) => field.name.includes(column) && !column.endsWith('_id'),
          );
        });
        break;
      case CONSTANT_REST_METHOD.POST:
      case CONSTANT_REST_METHOD.PUT:
        break;
      default:
        throw new InternalServerErrorException(
          `Failed to filter column due unknown method ${method} `,
        );
    }
  }

  containCondition(columnValuePair) {
    return Object.fromEntries(
      Object.entries(columnValuePair).map(([key, value]) => {
        return [
          key,
          typeof value === 'string'
            ? { contains: value, mode: 'insensitive' }
            : value,
        ];
      }),
    );
  }

  async get(
    req: any,
    apiUrl: string,
    paginationData: ApiManagementPaginationDto,
  ) {
    const { limit = 10, page = 1 } = paginationData;

    const apiKey = await this.validateApiKey(req);
    const api = await this.validateApi(
      apiUrl,
      CONSTANT_REST_METHOD.GET,
      apiKey.api_key,
    );
    await this.validateApiKeyAccess(api, apiKey);

    let convertedSearchString = {};

    if (paginationData.search) {
      const parsedSearchString = parseSearchString(paginationData.search);
      this.validateFields(api, parsedSearchString);

      const parsedSearchStringKeys = Object.keys(parsedSearchString);
      const columnDatatypeInfo = await this.getColumnDataTypeInfo(
        api?.table,
        parsedSearchStringKeys,
      );

      convertedSearchString = convertToPostgresValue(
        columnDatatypeInfo,
        parsedSearchString,
      );
    }

    const selectedColumns = await this.getSelectedColumns(
      api,
      CONSTANT_REST_METHOD.GET,
    );

    const whereConditionData = {
      where: {
        ...this.containCondition(convertedSearchString),
      },
    };

    const filteredColumns = {};
    Object.keys(selectedColumns).map((key) => {
      if (key === 'deleted_at') whereConditionData.where.deleted_at = null;

      if (selectedColumns[key]) {
        filteredColumns[key] = selectedColumns[key];
      }
    });

    const query = {
      take: +limit,
      skip: +limit * (+page - 1),
      orderBy: {
        id: 'desc',
      },
      ...whereConditionData,
      select: {
        ...filteredColumns,
      },
    };

    const [totalData, queryData] = await this.prisma.$transaction([
      this.prisma[api?.table].count({
        ...whereConditionData,
      }),
      this.prisma[api?.table].findMany(query),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    let queryDataImageProcessed = []

    for(let qd of queryData){
      if(qd.foto_file){
        queryDataImageProcessed.push({
          ...qd,
          foto_file : await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${qd.nrp}/${qd.foto_file}`,
          )
        })
      }else{
        queryDataImageProcessed.push(qd)
      }

    }

    const queryResult = { queryData : queryDataImageProcessed, page, totalPage, totalData };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.API_MODULE,
    );
    await this.apiLogService.addLogsApiActivity(
      convertToILogApiData(
        req,
        api,
        CONSTANT_REST_METHOD.GET,
        CONSTANT_LOG.API_READ as ConstantLogType,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getLogActivity(req: any, query: any, paginationData) {
    const { limit, page } = paginationData;
    const [totalData, queryData] = await this.prisma.$transaction([
      this.prisma.log_management_api.count({
        where: {
          ...(query?.user_agent && {
            user_agent: { contains: query.user_agent, mode: 'insensitive' },
          }),
          ...(query?.action && {
            action: { contains: query.action, mode: 'insensitive' },
          }),
        },
      }),
      this.prisma.log_management_api.findMany({
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          created_at: 'desc',
        },
        where: {
          ...(query?.user_agent && {
            user_agent: { contains: query.user_agent, mode: 'insensitive' },
          }),
          ...(query?.action && {
            action: { contains: query.action, mode: 'insensitive' },
          }),
        },
        include: {
          api: {
            select: {
              id: true,
              application_name: true,
              method: true,
              table: true,
              api_url: true,
              is_active: true,
              created_at: true,
              updated_at: true,
              created_by: true,
              created_by_users: {
                select: {
                  id: true,
                  personel_id: true,
                  personel: true,
                },
              },
              akses_api_key: {
                select: {
                  api_key: true,
                },
              },
            },
          },
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = { queryData, page, totalPage, totalData };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.API_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return queryResult;
  }

  private validateFields(api: any, queryParameters: any) {
    const keys = api.api_fields;
    const mappedKey = keys.map((keyObj) => keyObj.name);
    const queryParams = Object.keys(queryParameters);

    queryParams.forEach((queryParam) => {
      if (!mappedKey.includes(queryParam)) {
        throw new BadRequestException(
          `Unauthorized query parameter '${queryParam}' to access due accessed parameter you can access containing ${mappedKey.join(', ')} .`,
        );
      }
    });
  }

  private getApiMethod(source: ConstantRestMethodType) {
    switch (source) {
      case CONSTANT_REST_METHOD.GET:
        return api_method_enum.GET;
      case CONSTANT_REST_METHOD.POST:
        return api_method_enum.POST;
      case CONSTANT_REST_METHOD.PUT:
        return api_method_enum.PUT;
      default:
        throw new Error(`Unsupported API Method ${source}`);
    }
  }

  private async validateApi(
    apiUrl: string,
    source: ConstantRestMethodType,
    apiKey?: string,
  ) {
    const method = this.getApiMethod(source);

    const accessApi = await this.prisma.akses_api_key.findFirst({
      where: {
        api: {
          api_url: {
            contains: apiUrl,
            mode: 'insensitive',
          },
          method,
        },
        api_key: { api_key: apiKey || undefined },
      },
      select: {
        api: {
          select: {
            id: true,
            table: true,
            is_active: true,
            api_fields: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    if (!accessApi?.api) throw new NotFoundException('Invalid API Url');

    if (!(await this.isTableExistsInSchema(accessApi?.api?.table)))
      throw new NotFoundException('Table doesn`t exist in schema.');

    if (!accessApi?.api?.is_active)
      throw new ServiceUnavailableException(
        "Sorry, this API isn't available now.",
      );

    return accessApi.api;
  }

  private async validateApiKeyAccess(api: any, apiKey: any): Promise<void> {
    const apiKeyAccess = await this.prisma.akses_api_key.findFirst({
      where: {
        api_id: api.id,
        api_key_id: apiKey.id,
      },
    });

    if (!apiKeyAccess) throw new ForbiddenException('Invalid API Key');
  }

  private async validateApiKey(req: any) {
    const apiKey = req.headers['ssdm-api-key'];
    if (!apiKey) throw new UnauthorizedException('API Key is required');

    const existingApiKey = await this.prisma.api_key.findFirst({
      where: { api_key: apiKey },
    });

    if (!existingApiKey) throw new ForbiddenException('Invalid API Key');

    return existingApiKey;
  }

  private async isTableExistsInSchema(tableName: string) {
    try {
      const result = await this.prisma.$queryRaw<{ name: string }[]>`
          SELECT tablename
          FROM pg_catalog.pg_tables
          WHERE schemaname = 'public'
            AND tablename = ${tableName}
      `;

      return result.length > 0;
    } catch (error) {
      this.logger.error('Error fetching table names:', error);
      return [];
    } finally {
      await this.prisma.$disconnect();
    }
  }

  private async getColumnInfo(tableName: string): Promise<{
    [columnName: string]: {
      dataType: string;
      isNullable: string;
    };
  }> {
    const queryColumnInfo = await this.prisma.$queryRaw<
      {
        column_name: string;
        data_type: string;
        is_nullable: string;
      }[]
    >`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public'
          AND table_name = ${tableName};
    `;

    return queryColumnInfo.reduce((acc, item) => {
      acc[item.column_name] = {
        dataType: item.data_type,
        isNullable: item.is_nullable,
      };
      return acc;
    }, {});
  }

  private async mapData(api, inputData) {
    const payload = {};
    const columnDefinitions = await this.getColumnInfo(api.table);
    const fieldNames = api.api_fields.map((field) => field.name);

    for (const fieldName of fieldNames) {
      const fieldValue = inputData[fieldName];
      const columnDefinition = columnDefinitions[fieldName];

      // Check if field is required but not provided
      if (
        (fieldValue === undefined || fieldValue === null) &&
        columnDefinition.isNullable === 'NO'
      )
        throw new BadRequestException(
          `Field ${fieldName} is required. Please provide a value for this field.`,
        );

      // Convert and assign the value
      payload[fieldName] = convertStringToPostgresValue(
        fieldName,
        columnDefinition.dataType,
        fieldValue ?? null,
      );
    }

    return payload;
  }
}
