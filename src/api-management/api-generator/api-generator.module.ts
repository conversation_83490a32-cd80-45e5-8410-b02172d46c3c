import { forwardRef, Module } from '@nestjs/common';
import { ApiGeneratorService } from './service/api-generator.service';
import { ApiGeneratorController } from './controller/api-generator.controller';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, forwardRef(() => UsersModule), LogsActivityModule],
  providers: [ApiGeneratorService],
  exports: [ApiGeneratorService],
  controllers: [ApiGeneratorController],
})
export class APIGeneratorModule {}
