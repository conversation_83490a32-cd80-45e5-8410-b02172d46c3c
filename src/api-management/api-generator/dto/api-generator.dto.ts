import { api_method_enum } from '@prisma/client';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsString,
} from 'class-validator';

export class CreateAPIDTO {
  @IsString()
  @IsNotEmpty({ message: 'application name not provided' })
  public application_name: string;

  @IsString()
  @IsNotEmpty({ message: 'table not provided' })
  public table: string;

  @IsArray()
  public fields: any;

  @IsEnum(api_method_enum)
  @IsNotEmpty({ message: 'method not provided' })
  public method: api_method_enum;

  @IsString()
  @IsNotEmpty({ message: 'api_url not provided' })
  public api_url: string;

  @IsBoolean()
  public is_active: boolean;
}

export class UpdateAPIDTO {
  @IsString()
  @IsNotEmpty({ message: 'name not provided' })
  public application_name: string;

  @IsString()
  @IsNotEmpty({ message: 'table not provided' })
  public table: string;

  @IsArray()
  public fields: any;

  @IsEnum(api_method_enum)
  @IsNotEmpty({ message: 'method not provided' })
  public method: api_method_enum;

  @IsString()
  @IsNotEmpty({ message: 'api_url not provided' })
  public api_url: string;

  @IsBoolean()
  public is_active: boolean;
}
