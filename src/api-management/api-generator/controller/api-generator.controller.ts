import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { ApiGeneratorService } from '../service/api-generator.service';
import { CreateAPIDTO, UpdateAPIDTO } from '../dto/api-generator.dto';
import { PaginationDto } from 'src/core/dtos';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('api-generator')
@Module(MODULES.API_MANAGEMENT)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class ApiGeneratorController {
  private readonly logger = new Logger(ApiGeneratorController.name);

  constructor(private readonly apiGeneratorService: ApiGeneratorService) {}

  @Post('/')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: Request, @Body() body: CreateAPIDTO) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.apiGeneratorService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: Request) {
    this.logger.log(`Entering ${this.get.name}`);

    const response = await this.apiGeneratorService.get(req);

    this.logger.log(
      `Leaving ${this.get.name} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query('name') name: string,
    @Query() paginationData: PaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with name ${name} and pagination data ${JSON.stringify(paginationData)}`,
    );

    const response = await this.apiGeneratorService.getList(
      req,
      name,
      paginationData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with name ${name} and pagination data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(@Req() req: any, @Body() body: UpdateAPIDTO) {
    const id = parseInt(req?.params?.id, 10);

    this.logger.log(
      `Entering ${this.update.name} with id ${id} and body ${JSON.stringify(body)}`,
    );

    const response = await this.apiGeneratorService.update(req, body);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Delete('/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any) {
    const id = parseInt(req?.params?.id, 10);

    this.logger.log(`Entering ${this.delete.name} with id ${id}`);

    const response = await this.apiGeneratorService.delete(req);

    this.logger.log(
      `Leaving ${this.update.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/columns/:table_name/method/:method')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListColumnNames(
    @Req() req: any,
    @Param('table_name') table_name: string,
    @Param('method') method: string,
  ) {
    this.logger.log(
      `Entering ${this.getListColumnNames.name} with table name ${table_name} and method ${method}`,
    );

    const { response } = await this.apiGeneratorService.getColumnNames(
      req,
      table_name,
      method,
    );

    this.logger.log(
      `Leaving ${this.update.name} with table name ${table_name} and method ${method} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
