import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateAPIDTO, UpdateAPIDTO } from '../dto/api-generator.dto';
import { validatePath } from '../../../core/utils/validation.utils';
import { getMethodRules } from '../../../core/constants/api-management.constant';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { PaginationDto } from '../../../core/dtos';

@Injectable()
export class ApiGeneratorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req, body: CreateAPIDTO) {
    const { application_name, table, fields, method, api_url, is_active } =
      body;

    const isVerifiedPath: boolean = validatePath(api_url);
    if (!isVerifiedPath) throw new BadRequestException('Path URL is invalid');

    const { queryResult: columns } = await this.getColumnNames(
      req,
      table,
      method,
    );
    this.validationRequiredFields(fields, columns);

    const result = await this.prisma.api.create({
      data: {
        application_name,
        table,
        method,
        api_url,
        is_active,
        created_by: req.user.id,
        api_fields: {
          createMany: {
            data: this.transformedFields(fields),
          },
        },
      },
      include: {
        api_fields: {
          select: {
            name: true,
          },
        },
        akses_api_key: {
          select: {
            api_key: true,
          },
        },
        created_by_users: {
          select: {
            id: true,
            personel_id: true,
            personel: true,
          },
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_GENERATOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_GENERATOR_CREATE as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: result,
    };
  }

  async update(req, body: UpdateAPIDTO) {
    const { application_name, table, fields, method, api_url, is_active } =
      body;
    const id = parseInt(req?.params?.id, 10);

    const isVerifiedPath: boolean = validatePath(api_url);
    if (!isVerifiedPath) throw new BadRequestException('Path URL is invalid');

    const api = await this.prisma.api.findFirst({
      where: { id },
      include: {
        api_fields: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!api) throw new BadRequestException('Invalid api id');

    const data = await this.prisma.$transaction(async (tx) => {
      await tx.api_fields.deleteMany({
        where: { api_id: api.id },
      });

      return await tx.api.update({
        where: { id },
        data: {
          application_name,
          table,
          method,
          api_url,
          is_active,
          api_fields: {
            createMany: {
              data: this.transformedFields(fields),
            },
          },
        },
        include: {
          api_fields: true,
          akses_api_key: {
            select: {
              api_key: true,
            },
          },
          created_by_users: {
            select: {
              id: true,
              personel_id: true,
              personel: true,
            },
          },
        },
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_GENERATOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_GENERATOR_UPDATE as ConstantLogType,
        message,
        data,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data,
    };
  }

  async get(req) {
    const queryResult = await this.prisma.api.findFirst({
      where: { id: parseInt(req?.params?.id, 10) },
      include: {
        api_fields: true,
        akses_api_key: {
          select: {
            api_key: true,
          },
        },
        created_by_users: {
          select: {
            id: true,
            personel_id: true,
            personel: true,
          },
        },
      },
    });

    if (!queryResult) {
      throw new BadRequestException('Invalid api id');
    }

    (queryResult as any).relative_api_url = queryResult?.api_url;
    queryResult.api_url = `${process.env.SSDM_API_HOST}/api/${queryResult?.method?.toLowerCase()}${queryResult?.api_url}`;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_GENERATOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_GENERATOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getList(req: any, name: string, paginationData: PaginationDto) {
    const { limit, page } = paginationData;

    const [totalData, queryData] = await this.prisma.$transaction([
      this.prisma.api.count({
        where: {
          deleted_at: null,
          ...(name && {
            application_name: { contains: name, mode: 'insensitive' },
          }),
        },
      }),
      this.prisma.api.findMany({
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          created_at: 'desc',
        },
        where: {
          deleted_at: null,
          ...(name && {
            application_name: { contains: name, mode: 'insensitive' },
          }),
        },
        include: {
          api_fields: true,
          akses_api_key: {
            select: {
              api_key: true,
            },
          },
          created_by_users: {
            select: {
              id: true,
              personel_id: true,
              personel: true,
            },
          },
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = {
      apis: queryData.map((el) => ({
        ...el,
        api_url: `${process.env.SSDM_API_HOST}/api/${el?.method?.toLowerCase()}${el?.api_url}`,
        relative_api_url: el?.api_url,
      })),
      page,
      totalPage,
      totalData,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.API_GENERATOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_GENERATOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async delete(req) {
    const queryResult = await this.prisma.api.findFirst({
      where: { id: parseInt(req?.params?.id, 10), deleted_at: null },
    });

    if (!queryResult) {
      throw new BadRequestException('Invalid API id');
    }

    queryResult.deleted_at = new Date();

    await this.prisma.api.update({
      where: { id: queryResult.id },
      data: queryResult,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.API_GENERATOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.API_GENERATOR_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'API was successfully deleted',
      data: queryResult,
    };
  }

  async getColumnNames(req: any, tableName: string, method: string) {
    try {
      const result = await this.getColumnAttributes(tableName);

      const queryResult = result
        .filter(
          (item) => !getMethodRules(method).column.includes(item.column_name),
        )
        .map((item) => {
          return {
            column_name: item.column_name,
            is_required:
              getMethodRules(method).isShowNullable &&
              item.is_nullable === 'NO',
          };
        })
        .sort((a, b) => Number(b.is_required) - Number(a.is_required));

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.API_GENERATOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.API_GENERATOR_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        queryResult,
        response: {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        },
      };
    } catch (error) {
      console.error('Error fetching column names:', error);
      return {
        queryResult: [],
        response: null,
      };
    } finally {
      // It's important to disconnect the Prisma Client when it's not needed anymore
      await this.prisma.$disconnect();
    }
  }

  private transformedFields(fields) {
    return fields.map((field) => {
      return { name: field.field_name };
    });
  }

  private async getColumnAttributes(
    tableName: string,
  ): Promise<{ column_name: string; is_nullable: string }[]> {
    try {
      return await this.prisma.$queryRaw<
        { column_name: string; is_nullable: string }[]
      >`
             SELECT column_name, is_nullable
             FROM information_schema.columns
             WHERE table_name = ${tableName};
         `;
    } catch (error) {
      return [];
    }
  }

  private validationRequiredFields(
    api_fields: { field_name: string }[],
    columns: {
      column_name: string;
      is_required: boolean;
    }[],
  ) {
    const errors: string[] = [];
    const fieldNamesSet = new Set(api_fields.map((field) => field.field_name));
    columns.forEach((column) => {
      if (column.is_required && !fieldNamesSet.has(column.column_name))
        errors.push(column.column_name);
    });

    if (errors.length > 0)
      throw new BadRequestException(
        `Fields ${errors.join(', ')} is required but not provided.`,
      );
  }
}
