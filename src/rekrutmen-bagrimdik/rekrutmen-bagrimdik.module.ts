import { forwardRef, Module } from '@nestjs/common';
import { RekrutmenBagrimdikASNService } from './service/rekrutmen-bagrimdik.asn.service';
import { RekrutmenBagrimdikASNController } from './controller/rekrutmen-bagrimdik.asn.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { RekrutmenBagrimdikPPPKService } from './service/rekrutmen-bagrimdik.pppk.service';
import { RekrutmenBagrimdikScheduler } from './scheduler/rekrutmen-bagrimdik.scheduler';
import { RekrutmenBagrimdikPPPKController } from './controller/rekrutmen-bagrimdik.pppk.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { ExcelModule } from '../api-utils/excel/excel.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [
    PrismaModule,
    LogsActivityModule,
    ExcelModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [
    RekrutmenBagrimdikASNController,
    RekrutmenBagrimdikPPPKController,
  ],
  providers: [
    RekrutmenBagrimdikScheduler,
    RekrutmenBagrimdikASNService,
    RekrutmenBagrimdikPPPKService,
    MinioService,
  ],
})
export class RekrutmenBagrimdikModule {}
