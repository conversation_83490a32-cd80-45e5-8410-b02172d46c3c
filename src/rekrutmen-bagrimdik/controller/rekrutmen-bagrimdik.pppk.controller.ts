import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Permission } from 'src/core/decorators';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadSuratKeputusanDto } from '../dto/create-rekrutmen-bagrimdik.dto';
import { GetManyPPPK, IDProperty } from '../dto/read-rekrutmen-bagrimdik.dto';
import { RekrutmenBagrimdikPPPKService } from '../service/rekrutmen-bagrimdik.pppk.service';
import { PaginationDto } from '../../core/dtos';

@Controller('rekrutmen-bagrimdik')
@UseGuards(JwtAuthGuard)
export class RekrutmenBagrimdikPPPKController {
  private readonly logger = new Logger(RekrutmenBagrimdikPPPKController.name);

  constructor(
    private readonly rekrutmenBagrimdikPPPKService: RekrutmenBagrimdikPPPKService,
  ) {}

  @Post('pppk/import-data-pppk')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async uploadDokumenPPPK(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.uploadDokumenPPPK.name} with file size: ${file.size}`,
    );
    const response = await this.rekrutmenBagrimdikPPPKService.uploadDokumenPPPK(
      req,
      file,
    );
    this.logger.log(
      `Leaving ${this.uploadDokumenPPPK.name} with file size: ${file.size} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('pppk/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllPPPK(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetManyPPPK,
  ) {
    this.logger.log(
      `Entering ${this.findAllPPPK.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response = await this.rekrutmenBagrimdikPPPKService.findAll(
      req,
      queries,
      paginationData,
    );
    this.logger.log(
      `Leaving ${this.findAllPPPK.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('pppk/detail-peserta/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findDetailPesertaPPPK(@Req() req: any, @Param() params: IDProperty) {
    this.logger.log(
      `Entering ${this.findDetailPesertaPPPK.name} with id: ${params.id}`,
    );
    const response = await this.rekrutmenBagrimdikPPPKService.findDetailPeserta(
      req,
      params.id,
    );
    this.logger.log(
      `Leaving ${this.findDetailPesertaPPPK.name} with id: ${params.id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('pppk/upload-surat-keputusan')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(pdf)$/)) {
          return callback(
            new BadRequestException('Only PDF file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async uploadSuratKeputusan(
    @Req() req: any,
    @Body() body: UploadSuratKeputusanDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.uploadSuratKeputusan.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.rekrutmenBagrimdikPPPKService.uploadFileSuratKeputusan(
        req,
        body,
        file,
      );
    this.logger.log(
      `Leaving ${this.uploadSuratKeputusan.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('pppk/list-status')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListStatusPPPK(@Req() req: any) {
    this.logger.log(`Entering ${this.getListStatusPPPK.name}`);
    const response = await this.getListStatusPPPK(req);
    this.logger.log(
      `Leaving ${this.getListStatusPPPK.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('pppk/tanggal-import-terbaru')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getLatestImportDatePPPK(@Req() req: any) {
    this.logger.log(`Entering ${this.getLatestImportDatePPPK.name}`);
    const response =
      await this.rekrutmenBagrimdikPPPKService.getLatestImportDate(req);
    this.logger.log(
      `Leaving ${this.getLatestImportDatePPPK.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
