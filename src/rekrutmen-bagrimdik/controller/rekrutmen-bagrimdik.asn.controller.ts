import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { RekrutmenBagrimdikASNService } from '../service/rekrutmen-bagrimdik.asn.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Permission } from 'src/core/decorators';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadSuratPerintahLatsarDto } from '../dto/create-rekrutmen-bagrimdik.dto';
import { GetManyASN, IDProperty } from '../dto/read-rekrutmen-bagrimdik.dto';
import { PaginationDto } from '../../core/dtos';

@Controller('rekrutmen-bagrimdik')
@UseGuards(JwtAuthGuard)
export class RekrutmenBagrimdikASNController {
  private readonly logger = new Logger(RekrutmenBagrimdikASNController.name);

  constructor(
    private readonly rekrutmenBagrimdikASNService: RekrutmenBagrimdikASNService,
  ) {}

  @Post('asn/import-data-asn')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async uploadDokumenASN(
    @Req() req: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.uploadDokumenASN.name} with file size: ${file.size}`,
    );
    const response = await this.rekrutmenBagrimdikASNService.uploadDokumenASN(
      req,
      file,
    );
    this.logger.log(
      `Leaving ${this.uploadDokumenASN.name} with file size: ${file.size} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllASN(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetManyASN,
  ) {
    this.logger.log(
      `Entering ${this.findAllASN.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response = await this.rekrutmenBagrimdikASNService.findAll(
      req,
      queries,
      paginationData,
    );
    this.logger.log(
      `Leaving ${this.findAllASN.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/detail-peserta/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findDetailPeserta(@Req() req: any, @Param() params: IDProperty) {
    this.logger.log(
      `Entering ${this.findDetailPeserta.name} with id: ${params.id}`,
    );
    const response = await this.rekrutmenBagrimdikASNService.findDetailPeserta(
      req,
      params.id,
    );
    this.logger.log(
      `Leaving ${this.findDetailPeserta.name} with id: ${params.id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/nilai-detil/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findNilaiDetailPeserta(@Req() req: any, @Param() params: IDProperty) {
    this.logger.log(
      `Entering ${this.findNilaiDetailPeserta.name} with id: ${params.id}`,
    );
    const response =
      await this.rekrutmenBagrimdikASNService.findNilaiDetailPeserta(
        req,
        params.id,
      );
    this.logger.log(
      `Leaving ${this.findNilaiDetailPeserta.name} with id: ${params.id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('asn/buat-draft-siap-latsar')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async createDraftSiapLatsar(@Req() req: any) {
    this.logger.log(`Entering ${this.createDraftSiapLatsar.name}`);
    const response =
      await this.rekrutmenBagrimdikASNService.createDraftSiapLatsar(req);
    this.logger.log(
      `Leaving ${this.createDraftSiapLatsar.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/list-draft-siap-latsar')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllDraftSiapLatsarASN(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() queries: GetManyASN,
  ) {
    this.logger.log(
      `Entering ${this.findAllDraftSiapLatsarASN.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)}`,
    );
    const response =
      await this.rekrutmenBagrimdikASNService.findAllDraftSiapLatsarASN(
        req,
        queries,
        paginationData,
      );

    this.logger.log(
      `Leaving ${this.findAllDraftSiapLatsarASN.name} with queries: ${JSON.stringify(queries)} and pagination data: ${JSON.stringify(paginationData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('asn/upload-surat-perintah-siap-latsar')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(pdf)$/)) {
          return callback(
            new BadRequestException('Only PDF file are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async uploadSuratPerintahSiapLatsar(
    @Req() req: any,
    @Body() body: UploadSuratPerintahLatsarDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.uploadSuratPerintahSiapLatsar.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.rekrutmenBagrimdikASNService.uploadFileSuratPerintahSiapLatsar(
        req,
        body,
        file,
      );
    this.logger.log(
      `Leaving ${this.uploadSuratPerintahSiapLatsar.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('asn/submit-draft-siap-latsar')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async submitDraftSiapLatsar(@Req() req: any) {
    this.logger.log(`Entering ${this.submitDraftSiapLatsar.name}`);
    const response =
      await this.rekrutmenBagrimdikASNService.submitDraftSiapLatsar(req);
    this.logger.log(
      `Leaving ${this.submitDraftSiapLatsar.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/list-status')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getListStatusASN(@Req() req: any) {
    // Extract enum values
    this.logger.log(`Entering ${this.getListStatusASN.name}`);
    const response =
      await this.rekrutmenBagrimdikASNService.getListStatusASN(req);
    this.logger.log(
      `Leaving ${this.getListStatusASN.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('asn/tanggal-import-terbaru')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getLatestImportDate(@Req() req: any) {
    this.logger.log(`Entering ${this.getLatestImportDate.name}`);
    const response =
      await this.rekrutmenBagrimdikASNService.getLatestImportDate(req);
    this.logger.log(
      `Leaving ${this.getLatestImportDate.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
