import { IsInt, <PERSON>NotEmpty, Min } from 'class-validator';

export class UploadDokumenDTO {
  file: Express.Multer.File;
}

export class UploadSuratPerintahLatsarDto {
  @IsInt()
  @Min(1)
  @IsNotEmpty({ message: 'id draft is not provided' })
  public id: number;

  file: Express.Multer.File;
}

export class UploadSuratKeputusanDto {
  @IsInt()
  @Min(1)
  @IsNotEmpty({ message: 'id is not provided' })
  public id: number;

  file: Express.Multer.File;
}
