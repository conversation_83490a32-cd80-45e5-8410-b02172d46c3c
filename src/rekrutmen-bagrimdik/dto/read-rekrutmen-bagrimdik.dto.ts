import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  TStatusSeleksiBagrimdikASN,
  TStatusSeleksiBagrimdikPPPK,
} from '../../core/interfaces/rekrutmen-bagrimdik.type';

export class GetManyASN {
  @IsOptional()
  @IsString()
  public q?: string;

  @IsOptional()
  @IsArray()
  // @IsEnum(StatusRekrutmenASN, { each: true })
  public status?: TStatusSeleksiBagrimdikASN[];
}

export class GetManyDraftSiapLatsarASN {
  @IsOptional()
  @IsString()
  public q?: string;
}

export class GetManyPPPK {
  @IsOptional()
  @IsString()
  public q?: string;

  @IsOptional()
  @IsArray()
  // @IsEnum(StatusRekrutmenPPPK, { each: true })
  public status?: TStatusSeleksiBagrimdikPPPK[];
}

export class IDProperty {
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty({ message: 'id is not provided' })
  public id: number;
}
