import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { TDataImport } from '../../core/interfaces/rekrutmen-bagrimdik.type';
import {
  listFieldPesertaPPPK,
  listReserveWordNameHeadersPPPK,
} from '../../core/constants/rekrutmen-bagrimdik.constant';
import { GetManyPPPK } from '../dto/read-rekrutmen-bagrimdik.dto';
import {
  bagrimdik_peserta_rekrutmen_pppk,
  Prisma,
  status_rekrutmen_pppk_enum,
} from '@prisma/client';
import { UploadSuratKeputusanDto } from '../dto/create-rekrutmen-bagrimdik.dto';
import { OrderEnum } from '../../core/enums/rekrutmen-bagrimdik.enum';
import { PaginationDto } from '../../core/dtos';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import * as ExcelJS from 'exceljs';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import { parseDate, parseNumber } from '../../core/utils/common.utils';
import { createSearchQuery } from '../../core/utils/db.utils';
import { MinioService } from '../../api-utils/minio/service/minio.service';

// Import Prisma's generated types for this table

@Injectable()
export class RekrutmenBagrimdikPPPKService {
  constructor(
    private prisma: PrismaService,
    private readonly excelService: ExcelService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async uploadDokumenPPPK(req: any, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }
    const workbook = new ExcelJS.Workbook();
    try {
      await workbook.xlsx.load(file.buffer);
      const currentDate: Date = new Date(); // Get the current date and time

      // Loop through all sheets and process them individually
      // Store the result for this sheet in the result object, identified by the sheet name
      let dataPeserta: TDataImport = { headerMap: [], data: [] };
      workbook.worksheets.forEach((worksheet) => {
        const sheetName = worksheet.name;
        const replaceSheetName = sheetName
          ?.trim()
          .replace(/\s+/g, '_')
          .replace(/\./g, '')
          .replace(/\(|\)/g, '')
          .replace(/%/g, 'persen')
          .toLowerCase();
        if (replaceSheetName === 'data_nip') {
          // Determine how many rows are used for headers in the sheet
          const headerDepth = this.excelService.getHeaderDepth(
            worksheet,
            listReserveWordNameHeadersPPPK,
          );

          // Dynamically parse the headers into a flat structure using dot notation
          const headerMap = this.excelService.parseHeaders(
            worksheet,
            headerDepth,
          );

          // Parse data rows and map them to the flat header structure
          const data = this.excelService.parseData(
            worksheet,
            headerMap,
            headerDepth,
          );
          // Store the result for this sheet in the result object, identified by the sheet name
          dataPeserta = { headerMap, data };
        }
      });

      await this.importDataPPPK(req, dataPeserta, currentDate);

      const queryResult = {};
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Error loading workbook: ' + error,
      );
    } finally {
      // You can allow garbage collection to free memory by ensuring you no longer reference the workbook
      workbook.xlsx.writeBuffer = null; // Clear the internal write buffer if you're done writing
    }
  }

  /**
   * Create data from import excel bagrimdik pppk to database rekrutment bagrimdik pppk.
   * @param req - The request headers can use for get credential from sessions or other headers of request.
   * @param dataPeserta - The data source peserta from excel convert to DataImport type format.
   * @param currentDate - The current date time of import file.
   */
  public async importDataPPPK(
    req: Request,
    dataPeserta: TDataImport,
    currentDate: Date,
  ) {
    if (dataPeserta.data.length > 0) {
      const mappedDataPeserta: Array<any> = dataPeserta?.data?.map((row) => {
        const mappedRow: Record<string, any> = {};

        dataPeserta?.headerMap?.forEach((header, index) => {
          if (listFieldPesertaPPPK[header]?.field) {
            if (
              listFieldPesertaPPPK[header]?.type &&
              listFieldPesertaPPPK[header]?.type === 'float'
            ) {
              mappedRow[listFieldPesertaPPPK[header].field] =
                parseFloat(row[index]) || 0.0; // Map values or default to null
            } else if (
              listFieldPesertaPPPK[header]?.type &&
              listFieldPesertaPPPK[header]?.type === 'int'
            ) {
              mappedRow[listFieldPesertaPPPK[header].field] =
                parseInt(row[index]) || 0; // Map values or default to null
            } else if (
              listFieldPesertaPPPK[header]?.type &&
              listFieldPesertaPPPK[header]?.type === 'date'
            ) {
              // Check if row[index] is a valid date string, else set it to null
              const dateString = row[index] || null;
              if (dateString) {
                // Assign the parsed date to the mappedRow field
                mappedRow[listFieldPesertaPPPK[header].field] =
                  parseDate(dateString);
              } else {
                // If dateString is null or empty, set the mapped value to null
                mappedRow[listFieldPesertaPPPK[header].field] = null;
              }
            } else {
              mappedRow[listFieldPesertaPPPK[header].field] =
                String(row[index]) || null; // Map values or default to null
            }
          }
        });
        mappedRow['import_file_date'] = currentDate;
        return mappedRow;
      });

      try {
        const createDataPeserta =
          await this.prisma.bagrimdik_peserta_rekrutmen_pppk.createMany({
            data: mappedDataPeserta, // Explicitly mapped
            skipDuplicates: true,
          });
        return 'This action adds a new rekrutmenBagrimdik';
      } catch (err) {
        throw new InternalServerErrorException(
          'Imported failed on progress : ' + err,
        );
      }
    } else {
      throw new InternalServerErrorException('Data is empty');
    }
  }

  /**
   * get all data from bagrimdik_peserta_rekrutmen_pppk and support by specific criteria.
   * @param queries - The query for find all data specific.
   * @param paginationData - The meta data want to search.
   */
  async findAll(req: any, queries: GetManyPPPK, paginationData: PaginationDto) {
    try {
      const limit = parseNumber(paginationData.limit, 100);
      const page = parseNumber(paginationData.page, 1);
      const { q, status } = queries;
      let searchOrm: Prisma.bagrimdik_peserta_rekrutmen_pppkWhereInput = {};
      let createSearchPesertaBy: Prisma.bagrimdik_peserta_rekrutmen_pppkWhereInput =
        {};
      if (status !== undefined) {
        if (Array.isArray(status)) {
          if (status.length > 0) {
            createSearchPesertaBy.status_rekrutmen = {
              in: status as status_rekrutmen_pppk_enum[],
            };
          }
        } else {
          createSearchPesertaBy.status_rekrutmen = status;
        }
      }
      if (q !== undefined) {
        const createSearchNoPeserta = {
          OR: [
            createSearchQuery({
              name: 'no_peserta',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_pppkWhereInput,
          ],
        };
        const createSearchNIP = {
          OR: [
            createSearchQuery({
              name: 'nip',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_pppkWhereInput,
          ],
        };
        const createSearchNama = {
          OR: [
            createSearchQuery({
              name: 'nama',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_pppkWhereInput,
          ],
        };
        searchOrm = {
          ...createSearchNoPeserta,
          ...createSearchNIP,
          ...createSearchNama,
        };
      }
      const startIndex = (page - 1) * limit;
      searchOrm = {
        ...createSearchPesertaBy,
        ...searchOrm,
      };
      const selectOrm: Prisma.bagrimdik_peserta_rekrutmen_pppkFindManyArgs = {
        select: {
          id: true,
          no_peserta: true,
          nama: true,
          nip: true,
          jenis_formasi_nama: true,
          periode: true,
          tanggal_usulan: true,
          tanggal_pertek: true,
          no_pertek: true,
          status_usulan: true,
          tahapan: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          pendidikan_pertama_nama: true,
          kode_dik: true,
          tanggal_tahun_lulus: true,
          nomor_ijazah: true,
          jabatan_fungsional_umum_nama: true,
          jabatan_fungsional_nama: true,
          sub_jabatan_fungsional_nama: true,
          gaji_pokok: true,
          kpkn_nama: true,
          golongan_nama: true,
          agama_id: true,
          agama_nama: true,
          jenis_kelamin: true,
          jenis_kelamin_id: true,
          jenis_kawin_nama: true,
          unor_nama: true,
          unor_induk_nama: true,
          satuan_kerja_nama: true,
          rencana_perjanjian_kontrak_atau_tmt: true,
          ket_sehat_dokter: true,
          ket_sehat_tanggal: true,
          ket_sehat_nomor: true,
          ket_bebas_narkoba_nomor: true,
          ket_bebas_narkoba_tanggal: true,
          ket_kelakuanbaik_nomor: true,
          ket_kelakuanbaik_pejabat: true,
          ket_kelakuanbaik_tanggal: true,
          tgl_kontrak_mulai: true,
          tgl_kontrak_akhir: true,
          kode_polda: true,
          polda: true,
          kode_cek: true,
          ket: true,
          status_rekrutmen: true,
          import_file_date: true,
          bagrimdik_dokumen_skep_file_id: true,
        },
        orderBy: {
          created_at: OrderEnum.asc,
        },
      };
      let whereOrm: Prisma.bagrimdik_peserta_rekrutmen_pppkFindManyArgs = {
        where: {
          ...searchOrm,
        },
      };
      let whereOrmCount: Prisma.bagrimdik_peserta_rekrutmen_pppkCountArgs = {
        where: {
          ...searchOrm,
        },
      };
      const totalData =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.count({
          ...whereOrmCount,
        });
      const result: bagrimdik_peserta_rekrutmen_pppk[] =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.findMany({
          ...selectOrm,
          ...whereOrm,
          take: limit,
          skip: startIndex,
        });
      // Flatten the ticket data
      const queryResult = result;
      const totalPage = Math.ceil(totalData / limit);
      const extraData = {};
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get all rekrutmen pppk',
        data: queryResult,
        page,
        totalPage,
        totalData,
        extraData,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get detail data peserta from bagrimdik_peserta_rekrutmen_pppk.
   * @param params - The params for find data by id.
   */
  async findDetailPeserta(req: any, id: number) {
    try {
      const queryResult =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.findFirst({
          where: { id: Number(id) },
          select: {
            id: true,
            no_peserta: true,
            nama: true,
            nip: true,
            jenis_formasi_nama: true,
            periode: true,
            tanggal_usulan: true,
            tanggal_pertek: true,
            no_pertek: true,
            status_usulan: true,
            tahapan: true,
            tempat_lahir: true,
            tanggal_lahir: true,
            pendidikan_pertama_nama: true,
            kode_dik: true,
            tanggal_tahun_lulus: true,
            nomor_ijazah: true,
            jabatan_fungsional_umum_nama: true,
            jabatan_fungsional_nama: true,
            sub_jabatan_fungsional_nama: true,
            gaji_pokok: true,
            kpkn_nama: true,
            golongan_nama: true,
            agama_id: true,
            agama_nama: true,
            jenis_kelamin: true,
            jenis_kelamin_id: true,
            jenis_kawin_nama: true,
            unor_nama: true,
            unor_induk_nama: true,
            satuan_kerja_nama: true,
            rencana_perjanjian_kontrak_atau_tmt: true,
            ket_sehat_dokter: true,
            ket_sehat_tanggal: true,
            ket_sehat_nomor: true,
            ket_bebas_narkoba_nomor: true,
            ket_bebas_narkoba_tanggal: true,
            ket_kelakuanbaik_nomor: true,
            ket_kelakuanbaik_pejabat: true,
            ket_kelakuanbaik_tanggal: true,
            tgl_kontrak_mulai: true,
            tgl_kontrak_akhir: true,
            kode_polda: true,
            polda: true,
            kode_cek: true,
            ket: true,
            status_rekrutmen: true,
            import_file_date: true,
            bagrimdik_dokumen_skep_file_id: true,
          },
        });

      if (!queryResult) {
        throw new NotFoundException('Requested detail peserta not found');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * upload file surat perintah siap latsar into storage service and save to the database for file info.
   * @param body - parameter for support data.
   * @param file - the file for submitted into service.
   */
  async uploadFileSuratKeputusan(
    req: any,
    body: UploadSuratKeputusanDto,
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }

    try {
      const { id } = body;
      const { rawFile, uploaded } =
        await this.minioService.uploadFilesWithoutBufferAndFilename(file);
      const ifPPKHaveFile =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.count({
          where: {
            id: Number(id),
            bagrimdik_dokumen_skep_file_id: {
              not: null,
            },
          },
        });
      if (ifPPKHaveFile === 0) {
        const insertToDb = await this.prisma.bagrimdik_dokumen_skep_file.create(
          {
            data: {
              ...rawFile,
              key: uploaded.Key,
              url: uploaded.Location,
              filename: uploaded.filename,
              created_at: new Date(),
              updated_at: new Date(),
            },
          },
        );
        if (insertToDb) {
          const updatedPPPKData =
            await this.prisma.bagrimdik_peserta_rekrutmen_pppk.update({
              where: { id: Number(id) },
              data: {
                bagrimdik_dokumen_skep_file_id: Number(insertToDb.id), // Update agent's status
              },
            });
          if (updatedPPPKData) {
            const queryResult = {
              id: insertToDb.id,
              key: uploaded.Key,
              url: uploaded.Location,
              filename: uploaded.filename,
            };

            const message = convertToLogMessage(
              ConstantLogStatusEnum.SUCCESS,
              ConstantLogTypeEnum.UPDATE_LOG_TYPE,
              ConstantLogDataTypeEnum.OBJECT,
              ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
            );
            await this.logsActivityService.addLogsActivity(
              convertToILogData(
                req,
                CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_UPDATE as ConstantLogType,
                message,
                queryResult,
              ),
            );

            return {
              statusCode: HttpStatus.OK,
              message,
              data: queryResult,
            };
          } else {
            throw new InternalServerErrorException(
              `failed update pppk ${id} into db`,
            );
          }
        } else {
          throw new InternalServerErrorException(
            'failed save info file into db',
          );
        }
      } else {
        throw new InternalServerErrorException(
          `failed upload file, can't reupload file on pppk ${id}, file exist on pppk data`,
        );
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get latest import date from bagrimdik_peserta_rekrutmen_asn.
   */
  async getLatestImportDate(req: any) {
    try {
      const getLatestDate =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.findFirst({
          select: {
            import_file_date: true,
          },
          orderBy: {
            import_file_date: OrderEnum.desc,
          },
        });
      if (!getLatestDate) {
        throw new ForbiddenException('Latest Date not found');
      }
      const isoDate = getLatestDate.import_file_date;
      let unixTimestamp = null;
      if (isoDate) {
        unixTimestamp = new Date(isoDate).getTime() / 1000; // Dividing by 1000 to convert milliseconds to seconds
      }
      const queryResult = { date: unixTimestamp };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async updatePesertaStatus(): Promise<void> {
    try {
      const today = new Date();

      // Ambil data kontrak yang masih aktif
      const contracts =
        await this.prisma.bagrimdik_peserta_rekrutmen_pppk.findMany({
          where: {
            tgl_kontrak_akhir: {
              lte: today, // Hanya ambil kontrak yang tgl_kontrak_akhir sudah lewat atau sama dengan hari ini
            },
          },
        });

      // Iterasi setiap kontrak untuk cek apakah 6 bulan sebelum tgl_kontrak_akhir sudah lewat atau kontrak sudah habis
      for (const contract of contracts) {
        const tglKontrakAkhir = new Date(contract.tgl_kontrak_akhir);
        const sixMonthsBefore = new Date(tglKontrakAkhir);
        sixMonthsBefore.setMonth(sixMonthsBefore.getMonth() - 6); // Mengurangi 6 bulan

        // Jika tanggal sekarang sudah melewati 6 bulan sebelum tgl_kontrak_akhir
        if (today >= sixMonthsBefore && today < tglKontrakAkhir) {
          // Update status menjadi 'kontrak akan habis' jika dalam jangka waktu 6 bulan sebelum habis
          await this.prisma.bagrimdik_peserta_rekrutmen_pppk.update({
            where: { id: contract.id },
            data: {
              status_rekrutmen: status_rekrutmen_pppk_enum.KONTRAK_AKAN_HABIS,
            },
          });
        }

        // Jika tanggal kontrak sudah lewat (tgl_kontrak_akhir sudah lebih kecil dari hari ini)
        if (today > tglKontrakAkhir) {
          // Update status menjadi 'kontrak sudah habis'
          await this.prisma.bagrimdik_peserta_rekrutmen_pppk.update({
            where: { id: contract.id },
            data: { status_rekrutmen: status_rekrutmen_pppk_enum.KADULAWARSA },
          });
        }
      }
    } catch (err) {
      throw err;
    }
  }

  async getListStatusPPPK(req: any) {
    const queryResult = Object.values(status_rekrutmen_pppk_enum);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
