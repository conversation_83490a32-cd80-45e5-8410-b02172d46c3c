import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  T_bagrimdik_draft_siap_latsar_asnWithRelations,
  TDataImport,
} from '../../core/interfaces/rekrutmen-bagrimdik.type';
import { GetManyASN } from '../dto/read-rekrutmen-bagrimdik.dto';
import {
  bagrimdik_draft_siap_latsar_asn,
  bagrimdik_peserta_rekrutmen_asn,
  jenis_nilai_komponen_enum,
  Prisma,
  status_rekrutmen_asn_enum,
} from '@prisma/client';
import {
  listFieldNilaiASN,
  listFieldPesertaASN,
  listReserveWordNameHeadersASN,
} from '../../core/constants/rekrutmen-bagrimdik.constant';
import { UploadSuratPerintahLatsarDto } from '../dto/create-rekrutmen-bagrimdik.dto';
import { OrderEnum } from '../../core/enums/rekrutmen-bagrimdik.enum';
import { PaginationDto } from '../../core/dtos';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import * as ExcelJS from 'exceljs';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { parseDate, parseNumber } from '../../core/utils/common.utils';
import { createSearchQuery } from 'src/core/utils/db.utils';

// Import Prisma's generated types for this table

@Injectable()
export class RekrutmenBagrimdikASNService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly excelService: ExcelService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async uploadDokumenASN(req: any, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }
    const workbook = new ExcelJS.Workbook();
    try {
      await workbook.xlsx.load(file.buffer);
      const currentDate: Date = new Date(); // Get the current date and time

      // Loop through all sheets and process them individually
      // Store the result for this sheet in the result object, identified by the sheet name
      let dataPeserta: TDataImport = { headerMap: [], data: [] };
      let dataNilaiSKBDetil: TDataImport = { headerMap: [], data: [] };
      workbook.worksheets.forEach((worksheet) => {
        const sheetName = worksheet.name;
        const replaceSheetName = sheetName
          ?.trim()
          .replace(/\s+/g, '_')
          .replace(/\./g, '')
          .replace(/\(|\)/g, '')
          .replace(/%/g, 'persen')
          .toLowerCase();
        if (replaceSheetName === 'data_peserta') {
          // Determine how many rows are used for headers in the sheet
          const headerDepth = this.excelService.getHeaderDepth(
            worksheet,
            listReserveWordNameHeadersASN,
          );

          // Dynamically parse the headers into a flat structure using dot notation
          const headerMap = this.excelService.parseHeaders(
            worksheet,
            headerDepth,
          );

          // Parse data rows and map them to the flat header structure
          const data = this.excelService.parseData(
            worksheet,
            headerMap,
            headerDepth,
          );
          // Store the result for this sheet in the result object, identified by the sheet name
          dataPeserta = { headerMap, data };
        } else if (
          replaceSheetName === 'nilai_skb_detil' ||
          replaceSheetName === 'nilai_skt_detil'
        ) {
          // Determine how many rows are used for headers in the sheet
          const headerDepth = this.excelService.getHeaderDepth(
            worksheet,
            listReserveWordNameHeadersASN,
          );

          // Dynamically parse the headers into a flat structure using dot notation
          const headerMap = this.excelService.parseHeaders(
            worksheet,
            headerDepth,
          );

          // Parse data rows and map them to the flat header structure
          const data = this.excelService.parseData(
            worksheet,
            headerMap,
            headerDepth,
          );
          // Store the result for this sheet in the result object, identified by the sheet name
          dataNilaiSKBDetil = { headerMap, data };
        }
      });

      await this.importDataASN(
        req,
        dataPeserta,
        dataNilaiSKBDetil,
        currentDate,
      );

      const queryResult = {};
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Error loading workbook: ' + error,
      );
    } finally {
      // You can allow garbage collection to free memory by ensuring you no longer reference the workbook
      workbook.xlsx.writeBuffer = null; // Clear the internal write buffer if you're done writing
    }
  }

  /**
   * Create data from import excel bagrimdik asn to database rekrutment bagrimdik asn.
   * @param req - The request headers can use for get credential from sessions or other headers of request.
   * @param dataPeserta - The data source peserta from excel convert to DataImport type format.
   * @param dataNilai - The data source detail nilai from excel convert to DataImport type format.
   * @param currentDate - The current date time of import file.
   */
  public async importDataASN(
    req: Request,
    dataPeserta: TDataImport,
    dataNilai: TDataImport,
    currentDate: Date,
  ) {
    if (dataPeserta.data.length > 0) {
      const mappedDataPeserta: Array<any> = dataPeserta?.data?.map((row) => {
        const mappedRow: Record<string, any> = {};

        dataPeserta?.headerMap?.forEach((header, index) => {
          if (listFieldPesertaASN[header]?.field) {
            if (
              listFieldPesertaASN[header]?.type &&
              listFieldPesertaASN[header]?.type === 'float'
            ) {
              mappedRow[listFieldPesertaASN[header].field] =
                parseFloat(row[index]) || 0.0; // Map values or default to null
            } else if (
              listFieldPesertaASN[header]?.type &&
              listFieldPesertaASN[header]?.type === 'int'
            ) {
              mappedRow[listFieldPesertaASN[header].field] =
                parseInt(row[index]) || 0; // Map values or default to null
            } else if (
              listFieldPesertaASN[header]?.type &&
              listFieldPesertaASN[header]?.type === 'date'
            ) {
              // Check if row[index] is a valid date string, else set it to null
              const dateString = row[index] || null;
              if (dateString) {
                // Assign the parsed date to the mappedRow field
                mappedRow[listFieldPesertaASN[header].field] =
                  parseDate(dateString);
              } else {
                // If dateString is null or empty, set the mapped value to null
                mappedRow[listFieldPesertaASN[header].field] = null;
              }
            } else {
              mappedRow[listFieldPesertaASN[header].field] =
                String(row[index]) || null; // Map values or default to null
            }
          }
        });
        mappedRow['import_file_date'] = currentDate;
        return mappedRow;
      });

      try {
        const createDataPeserta =
          await this.prisma.bagrimdik_peserta_rekrutmen_asn.createMany({
            data: mappedDataPeserta, // Explicitly mapped
            skipDuplicates: true,
          });

        if (createDataPeserta.count > 0 && dataNilai.data.length > 0) {
          // this is for filter duplication data of detail nilai, because using skipDuplicates for table bagrimdik_peserta_rekrutmen_asn

          let mappingJenisNilai = {
            komponen_skt: [],
            komponen_skb: [],
          };
          const mappedDataNilai: Array<any> = dataNilai?.data?.map((row) => {
            const mappedRow: Record<string, any> = {};
            let whereIsNoPeserta = 0;
            dataNilai?.headerMap?.forEach((header, index) => {
              if (header === 'no_peserta') whereIsNoPeserta = index;
              if (listFieldNilaiASN[header]?.field) {
                if (
                  listFieldNilaiASN[header]?.type &&
                  listFieldNilaiASN[header]?.type === 'int'
                ) {
                  mappedRow[listFieldNilaiASN[header].field] =
                    parseInt(row[index]) || 0; // Map values or default to null
                } else if (
                  listFieldNilaiASN[header]?.type &&
                  listFieldNilaiASN[header]?.type === 'float'
                ) {
                  mappedRow[listFieldNilaiASN[header].field] =
                    parseFloat(row[index]) || 0.0; // Map values or default to null
                } else if (
                  listFieldNilaiASN[header]?.type &&
                  listFieldNilaiASN[header]?.type === 'date'
                ) {
                  // Check if row[index] is a valid date string, else set it to null
                  const dateString = row[index] || null;
                  if (dateString) {
                    // Assign the parsed date to the mappedRow field
                    mappedRow[listFieldNilaiASN[header].field] =
                      parseDate(dateString);
                  } else {
                    // If dateString is null or empty, set the mapped value to null
                    mappedRow[listFieldNilaiASN[header].field] = null;
                  }
                } else {
                  mappedRow[listFieldNilaiASN[header].field] =
                    row[index] || null; // Map values or default to null
                }
              }
              if (header === 'nama_komponen_skt') {
                mappedRow['jenis_nilai'] =
                  jenis_nilai_komponen_enum.KOMPONEN_SKT;
                mappingJenisNilai.komponen_skt.push(row[whereIsNoPeserta]);
              } else if (header === 'nama_komponen_skb') {
                mappedRow['jenis_nilai'] =
                  jenis_nilai_komponen_enum.KOMPONEN_SKB;
                mappingJenisNilai.komponen_skb.push(row[whereIsNoPeserta]);
              }
            });
            return mappedRow;
          });
          mappingJenisNilai.komponen_skt = [
            ...new Set(mappingJenisNilai.komponen_skt),
          ];
          mappingJenisNilai.komponen_skb = [
            ...new Set(mappingJenisNilai.komponen_skb),
          ];
          const createDataNilai =
            await this.prisma.bagrimdik_nilai_sk_detil_asn.createMany({
              data: mappedDataNilai, // Explicitly mapped
              skipDuplicates: true,
            });
          if (createDataNilai.count > 0) {
            if (mappingJenisNilai.komponen_skt.length > 0) {
              const updatedDataPesertaSKT =
                await this.prisma.bagrimdik_peserta_rekrutmen_asn.updateMany({
                  where: {
                    no_peserta: {
                      in: mappingJenisNilai.komponen_skt, // IDs to match
                    },
                  },
                  data: {
                    jenis_nilai: jenis_nilai_komponen_enum.KOMPONEN_SKT, // New status value
                  },
                });
            }
            if (mappingJenisNilai.komponen_skb.length > 0) {
              const updatedDataPesertaSKB =
                await this.prisma.bagrimdik_peserta_rekrutmen_asn.updateMany({
                  where: {
                    no_peserta: {
                      in: mappingJenisNilai.komponen_skb, // IDs to match
                    },
                  },
                  data: {
                    jenis_nilai: jenis_nilai_komponen_enum.KOMPONEN_SKB, // New status value
                  },
                });
            }
          }
        }
        return 'This action adds a new rekrutmenBagrimdik';
      } catch (err) {
        throw new InternalServerErrorException(
          'Imported failed on progress : ' + err,
        );
      }
    } else {
      throw new InternalServerErrorException('Data is empty');
    }
  }

  /**
   * get all data from bagrimdik_peserta_rekrutmen_asn and support by specific criteria.
   * @param queries - The query for find all data specific.
   * @param paginationData - The meta data want to search.
   */
  async findAll(req: any, queries: GetManyASN, paginationData: PaginationDto) {
    try {
      const limit = parseNumber(paginationData.limit, 100);
      const page = parseNumber(paginationData.page, 1);
      const { q, status } = queries;
      let searchOrm: Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput = {};
      let searchBy: Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput = {};
      let siapLatsarStatus = false;
      if (status !== undefined) {
        if (Array.isArray(status)) {
          siapLatsarStatus = status.some(
            (status) => status.toLowerCase() === 'siap_latsar',
          );
          if (status.length > 0) {
            searchBy.status_rekrutmen = {
              in: status as status_rekrutmen_asn_enum[],
            };
          }
        } else {
          siapLatsarStatus = status;
          searchBy.status_rekrutmen = status;
        }
      }
      if (q !== undefined) {
        const createSearchNoPeserta = {
          OR: [
            createSearchQuery({
              name: 'no_peserta',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
          ],
        };
        const createSearchNIK = {
          OR: [
            createSearchQuery({
              name: 'nik',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
          ],
        };
        const createSearchNama = {
          OR: [
            createSearchQuery({
              name: 'nama',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
          ],
        };
        searchOrm = {
          ...createSearchNoPeserta,
          ...createSearchNIK,
          ...createSearchNama,
        };
      }
      const startIndex = (page - 1) * limit;
      searchOrm = {
        ...searchBy,
        ...searchOrm,
      };
      const selectOrm: Prisma.bagrimdik_peserta_rekrutmen_asnFindManyArgs = {
        select: {
          id: true,
          no_peserta: true,
          nik: true,
          nama: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          status_rekrutmen: true,
          jenis_nilai: true,
        },
        orderBy: {
          created_at: OrderEnum.asc,
        },
      };
      let whereOrm: Prisma.bagrimdik_peserta_rekrutmen_asnFindManyArgs = {
        where: {
          ...searchOrm,
        },
      };
      let whereOrmCount: Prisma.bagrimdik_peserta_rekrutmen_asnCountArgs = {
        where: {
          ...searchOrm,
        },
      };
      const totalData = await this.prisma.bagrimdik_peserta_rekrutmen_asn.count(
        {
          ...whereOrmCount,
        },
      );
      const result: bagrimdik_peserta_rekrutmen_asn[] =
        await this.prisma.bagrimdik_peserta_rekrutmen_asn.findMany({
          ...selectOrm,
          ...whereOrm,
          take: limit,
          skip: startIndex,
        });
      // Flatten the ticket data
      const queryResult = result;
      const totalPage = Math.ceil(totalData / limit);
      const extraData = {
        siapLatsar: {
          count: 0,
          moreThan100: false,
          draft: false,
        },
      };
      if (siapLatsarStatus) {
        const totalDataSiapLatsarOnDraft =
          await this.prisma.bagrimdik_draft_siap_latsar_asn.count();
        if (totalDataSiapLatsarOnDraft > 0) {
          extraData.siapLatsar.count =
            totalDataSiapLatsarOnDraft > 100 ? 100 : totalDataSiapLatsarOnDraft;
          extraData.siapLatsar.moreThan100 =
            totalDataSiapLatsarOnDraft > 100 ? true : false;
          extraData.siapLatsar.draft = true;
        } else {
          const totalDataSiapLatsarNonDraft =
            await this.prisma.bagrimdik_peserta_rekrutmen_asn.count({
              where: {
                status_rekrutmen: status_rekrutmen_asn_enum.SIAP_LATSAR,
              },
            });
          if (totalDataSiapLatsarNonDraft > 0) {
            extraData.siapLatsar.count =
              totalDataSiapLatsarNonDraft > 100
                ? 100
                : totalDataSiapLatsarNonDraft;
            extraData.siapLatsar.moreThan100 =
              totalDataSiapLatsarNonDraft > 100 ? true : false;
            extraData.siapLatsar.draft = false;
          }
        }
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get all rekrutmen asn',
        data: queryResult,
        page,
        totalPage,
        totalData,
        extraData,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get detail data peserta from bagrimdik_peserta_rekrutmen_asn.
   * @param params - The params for find data by id.
   */
  async findDetailPeserta(req: any, id: number) {
    try {
      const queryResult =
        await this.prisma.bagrimdik_peserta_rekrutmen_asn.findFirst({
          where: { id: Number(id) },
          select: {
            id: true,
            no_peserta: true,
            nik: true,
            nama: true,
            tempat_lahir: true,
            tanggal_lahir: true,
            instansi_kode: true,
            instansi_nama: true,
            lokasi_formasi_kode: true,
            lokasi_formasi_nama: true,
            jenis_formasi_kode: true,
            jenis_formasi_nama: true,
            jabatan_kode: true,
            jabatan_nama: true,
            nilai_sk_teknis_murni: true,
            nilai_sk_eptp: true,
            nilai_sk_bing: true,
            nilai_sk_ppm: true,
            nilai_sk_dpsi: true,
            nilai_sk_afirmasi_teknis: true,
            nilai_sk_total_teknis: true,
            nilai_sk_manajerial: true,
            nilai_sk_sosiokultural: true,
            nilai_sk_wawancara: true,
            nilai_sk_total: true,
            nilai_skd_twk: true,
            nilai_skd_tiu: true,
            nilai_skd_tkp: true,
            nilai_skd_total: true,
            nilai_skd_total_skala_100: true,
            skor_skd: true,
            skor_skb_60_persen: true,
            skor_skb: true,
            nilai_akhir: true,
            keterangan: true,
            afirmasi: true,
            lokasi_ujian: true,
            status_peserta: true,
            status_rekrutmen: true,
            import_file_date: true,
          },
        });

      if (!queryResult) {
        throw new NotFoundException('Requested detail peserta not found');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get detail data peserta from bagrimdik_peserta_rekrutmen_asn.
   * @param params - The params for find data by id.
   */
  async findNilaiDetailPeserta(req: any, id: number) {
    try {
      const dataPeserta =
        await this.prisma.bagrimdik_peserta_rekrutmen_asn.findFirst({
          where: { id: Number(id) },
          select: {
            no_peserta: true,
          },
        });

      if (!dataPeserta) {
        throw new NotFoundException('Requested detail peserta not found');
      }
      const no_peserta = dataPeserta.no_peserta;
      const queryResult =
        await this.prisma.bagrimdik_nilai_sk_detil_asn.findMany({
          select: {
            id: true,
            no_peserta: true,
            nama_peserta: true,
            jabatan_kode: true,
            jabatan_nama: true,
            urutan: true,
            nama_komponen: true,
            jenis_nilai: true,
            nilai: true,
            nilai_skala_100: true,
            bobot: true,
            skor: true,
            status_peserta: true,
          },
          where: {
            no_peserta: no_peserta,
          },
          orderBy: {
            urutan: OrderEnum.asc,
          },
        });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get latest import date from bagrimdik_peserta_rekrutmen_asn.
   */
  async getLatestImportDate(req: any) {
    try {
      const getLatestDate =
        await this.prisma.bagrimdik_peserta_rekrutmen_asn.findFirst({
          select: {
            import_file_date: true,
          },
          orderBy: {
            import_file_date: OrderEnum.desc,
          },
        });
      if (!getLatestDate) {
        throw new ForbiddenException('Latest Date not found');
      }
      const isoDate = getLatestDate.import_file_date;
      let unixTimestamp = null;
      if (isoDate) {
        unixTimestamp = new Date(isoDate).getTime() / 1000; // Dividing by 1000 to convert milliseconds to seconds
      }
      const queryResult = { date: unixTimestamp };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * create draft siap latsar from bagrimdik_peserta_rekrutmen_asn to bagrimdik_draft_siap_latsar_asn with some criteria.
   */
  async createDraftSiapLatsar(req: any) {
    try {
      const totalDataSiapLatsarOnDraft =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.count();
      if (totalDataSiapLatsarOnDraft === 0) {
        const selectOrm: Prisma.bagrimdik_peserta_rekrutmen_asnFindManyArgs = {
          select: {
            id: true,
          },
          orderBy: {
            created_at: OrderEnum.asc,
          },
        };
        let whereOrm: Prisma.bagrimdik_peserta_rekrutmen_asnFindManyArgs = {
          where: {
            status_rekrutmen: status_rekrutmen_asn_enum.SIAP_LATSAR,
          },
        };
        const resultPesertaNonDraft: bagrimdik_peserta_rekrutmen_asn[] =
          await this.prisma.bagrimdik_peserta_rekrutmen_asn.findMany({
            ...selectOrm,
            ...whereOrm,
            take: 100,
          });
        if (resultPesertaNonDraft.length === 0) {
          throw new ForbiddenException(
            'Tidak ada data peserta yang siap latsar.',
          );
        }
        // Flatten the id Siap latsar
        const dataIdSiapLatsar: Array<any> = resultPesertaNonDraft.map(
          (data_bagrimdik) => ({
            bagrimdik_peserta_rekrutmen_asn_id: data_bagrimdik.id,
          }),
        );
        const createDataDraftPeserta =
          await this.prisma.bagrimdik_draft_siap_latsar_asn.createMany({
            data: dataIdSiapLatsar, // Explicitly mapped
            skipDuplicates: true,
          });
        if (createDataDraftPeserta.count > 0) {
          const queryResult = { created: true };

          const message = convertToLogMessage(
            ConstantLogStatusEnum.SUCCESS,
            ConstantLogTypeEnum.CREATE_LOG_TYPE,
            ConstantLogDataTypeEnum.OBJECT,
            ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
          );
          await this.logsActivityService.addLogsActivity(
            convertToILogData(
              req,
              CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_CREATE as ConstantLogType,
              message,
              queryResult,
            ),
          );

          return {
            statusCode: HttpStatus.OK,
            message,
            data: queryResult,
          };
        } else {
          throw new ForbiddenException('Gagal buat data draft latsar.');
        }
      } else {
        throw new ForbiddenException('Existing draft is found.');
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * get all data draft from bagrimdik_draft_siap_latsar_asn and support by specific criteria.
   * @param queries - The query for find all data specific.
   * @param paginationData - The meta data want to search.
   */
  async findAllDraftSiapLatsarASN(
    req: any,
    queries: GetManyASN,
    paginationData: PaginationDto,
  ) {
    try {
      const page = parseNumber(paginationData.page, 1);
      const limit = parseNumber(paginationData.limit, 100);
      const { q } = queries;
      let searchOrm: Prisma.bagrimdik_draft_siap_latsar_asnWhereInput = {};
      let searchBy: Prisma.bagrimdik_draft_siap_latsar_asnWhereInput = {};
      if (q !== undefined) {
        const createSearchNoPeserta = {
          OR: {
            bagrimdik_peserta_rekrutmen_asn: createSearchQuery({
              name: 'no_peserta',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
          },
        };

        const createSearchNIK = {
          OR: {
            bagrimdik_peserta_rekrutmen_asn: createSearchQuery({
              name: 'nik',
              search: q,
              normal: false,
            }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
          },
        };
        const createSearchNama = {
          OR: [
            {
              bagrimdik_peserta_rekrutmen_asn: createSearchQuery({
                name: 'nama',
                search: q,
                normal: false,
              }) as Prisma.bagrimdik_peserta_rekrutmen_asnWhereInput,
            },
          ],
        };
        searchOrm = {
          ...createSearchNoPeserta,
          ...createSearchNIK,
          ...createSearchNama,
        };
      }
      const startIndex = (page - 1) * limit;
      searchOrm = {
        ...searchBy,
        ...searchOrm,
      };
      const selectOrm: Prisma.bagrimdik_draft_siap_latsar_asnFindManyArgs = {
        select: {
          id: true,
          bagrimdik_peserta_rekrutmen_asn: {
            select: {
              no_peserta: true,
              nik: true,
              nama: true,
            },
          },
          bagrimdik_dokumen_perintah_latsar_file_id: true,
        },
        orderBy: {
          id: OrderEnum.asc,
        },
      };
      let whereOrm: Prisma.bagrimdik_draft_siap_latsar_asnFindManyArgs = {
        where: {
          ...searchOrm,
        },
      };
      let whereOrmCount: Prisma.bagrimdik_draft_siap_latsar_asnCountArgs = {
        where: {
          ...searchOrm,
        },
      };
      const totalData = await this.prisma.bagrimdik_draft_siap_latsar_asn.count(
        {
          ...whereOrmCount,
        },
      );
      const totalDataUploadDocument =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.count({
          where: {
            bagrimdik_dokumen_perintah_latsar_file_id: {
              not: null,
            },
          },
        });
      const result: T_bagrimdik_draft_siap_latsar_asnWithRelations[] =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.findMany({
          ...selectOrm,
          ...whereOrm,
          take: limit,
          skip: startIndex,
        });
      // Flatten the ticket data
      const queryResult = result.map((data_draft) => ({
        id: data_draft.id,
        no_peserta: data_draft.bagrimdik_peserta_rekrutmen_asn.no_peserta,
        nik: data_draft.bagrimdik_peserta_rekrutmen_asn.nik,
        nama: data_draft.bagrimdik_peserta_rekrutmen_asn.nama,
        bagrimdik_dokumen_perintah_latsar_file_id:
          data_draft.bagrimdik_dokumen_perintah_latsar_file_id,
      }));
      const totalPage = Math.ceil(totalData / limit);
      const extraData = {
        allUploadDone: totalDataUploadDocument === totalData ? true : false,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get all draft siap latsar asn',
        data: queryResult,
        page,
        totalPage,
        totalData,
        extraData,
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * upload file surat perintah siap latsar into storage service and save to the database for file info.
   * @param body - parameter for support data.
   * @param file - the file for submitted into service.
   */
  async uploadFileSuratPerintahSiapLatsar(
    req: any,
    body: UploadSuratPerintahLatsarDto,
    file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }

    try {
      const { id } = body;
      const { rawFile, uploaded } =
        await this.minioService.uploadFilesWithoutBufferAndFilename(file);
      const ifDraftHaveFile =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.count({
          where: {
            id: Number(id),
            bagrimdik_dokumen_perintah_latsar_file_id: {
              not: null,
            },
          },
        });
      if (ifDraftHaveFile === 0) {
        const insertToDb =
          await this.prisma.bagrimdik_dokumen_perintah_latsar_file.create({
            data: {
              ...rawFile,
              key: uploaded.Key,
              url: uploaded.Location,
              filename: uploaded.filename,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });
        if (insertToDb) {
          const updatedDraft =
            await this.prisma.bagrimdik_draft_siap_latsar_asn.update({
              where: { id: Number(id) },
              data: {
                bagrimdik_dokumen_perintah_latsar_file_id: Number(
                  insertToDb.id,
                ), // Update agent's status
              },
            });
          if (updatedDraft) {
            const queryResult = {
              id: insertToDb.id,
              key: uploaded.Key,
              url: uploaded.Location,
              filename: uploaded.filename,
            };

            const message = convertToLogMessage(
              ConstantLogStatusEnum.SUCCESS,
              ConstantLogTypeEnum.UPDATE_LOG_TYPE,
              ConstantLogDataTypeEnum.OBJECT,
              ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
            );
            await this.logsActivityService.addLogsActivity(
              convertToILogData(
                req,
                CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_UPDATE as ConstantLogType,
                message,
                queryResult,
              ),
            );

            return {
              statusCode: HttpStatus.OK,
              message,
              data: queryResult,
            };
          } else {
            throw new InternalServerErrorException(
              `failed update draft ${id} into db`,
            );
          }
        } else {
          throw new InternalServerErrorException(
            'failed save info file into db',
          );
        }
      } else {
        throw new InternalServerErrorException(
          `failed upload file, can't reupload file on draft ${id}, file exist on draft`,
        );
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  /**
   * submit draft siap latsar from bagrimdik_draft_siap_latsar_asn to bagrimdik_peserta_rekrutmen_asn with some criteria.
   */
  async submitDraftSiapLatsar(req: any) {
    try {
      const totalDataSiapLatsarOnDraftAll =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.count();
      const totalDataSiapLatsarOnDraftHaveFile =
        await this.prisma.bagrimdik_draft_siap_latsar_asn.count({
          where: {
            bagrimdik_dokumen_perintah_latsar_file_id: {
              not: null,
            },
          },
        });
      if (
        totalDataSiapLatsarOnDraftAll === totalDataSiapLatsarOnDraftHaveFile
      ) {
        const selectOrm: Prisma.bagrimdik_draft_siap_latsar_asnFindManyArgs = {
          select: {
            id: true,
            bagrimdik_peserta_rekrutmen_asn_id: true,
            bagrimdik_dokumen_perintah_latsar_file_id: true,
          },
        };
        const resultPesertaDraft: bagrimdik_draft_siap_latsar_asn[] =
          await this.prisma.bagrimdik_draft_siap_latsar_asn.findMany({
            ...selectOrm,
          });
        // Flatten the id Siap latsar
        const dataSiapLatsar: Array<any> = resultPesertaDraft.map(
          (data_draft) => ({
            id: data_draft.bagrimdik_peserta_rekrutmen_asn_id,
            bagrimdik_dokumen_perintah_latsar_file_id:
              data_draft.bagrimdik_dokumen_perintah_latsar_file_id,
            id_draft: data_draft.id,
          }),
        );
        await Promise.all(
          dataSiapLatsar.map(async (data_latsar) => {
            // Update the participant's data
            await this.prisma.bagrimdik_peserta_rekrutmen_asn.update({
              where: {
                id: Number(data_latsar.id),
              },
              data: {
                bagrimdik_dokumen_perintah_latsar_file_id: Number(
                  data_latsar.bagrimdik_dokumen_perintah_latsar_file_id,
                ),
                status_rekrutmen: status_rekrutmen_asn_enum.SEDANG_LATSAR,
              },
            });

            // Delete the draft record
            await this.prisma.bagrimdik_draft_siap_latsar_asn.delete({
              where: {
                id: Number(data_latsar.id_draft),
              },
            });
          }),
        );
        const queryResult = {};

        const message = convertToLogMessage(
          ConstantLogStatusEnum.SUCCESS,
          ConstantLogTypeEnum.UPDATE_LOG_TYPE,
          ConstantLogDataTypeEnum.OBJECT,
          ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
        );
        await this.logsActivityService.addLogsActivity(
          convertToILogData(
            req,
            CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_UPDATE as ConstantLogType,
            message,
            queryResult,
          ),
        );

        return {
          statusCode: HttpStatus.OK,
          message,
          data: queryResult,
        };
      } else {
        throw new ForbiddenException('Draft not all complete.');
      }
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async updatePesertaStatus(): Promise<void> {
    try {
      const today = new Date();
      const oneYearAgo = new Date(
        today.getFullYear() - 1,
        today.getMonth(),
        today.getDate(),
      );

      // Handle leap year edge case for February 28
      if (today.getMonth() === 1) {
        // Get the number of days in February for the current year
        const februaryDays = new Date(today.getFullYear(), 2, 0).getDate();
        if (today.getDate() === 28 && februaryDays === 28) {
          await this.prisma.bagrimdik_peserta_rekrutmen_asn.updateMany({
            where: {
              OR: [
                {
                  import_file_date: {
                    lte: oneYearAgo,
                  },
                }, // Matches 28 Feb last year
                { import_file_date: new Date(today.getFullYear() - 1, 1, 29) }, // Matches 29 Feb if it exists
              ],
              status_rekrutmen: status_rekrutmen_asn_enum.AKTIF,
            },
            data: {
              status_rekrutmen: status_rekrutmen_asn_enum.SIAP_LATSAR,
            },
          });
        } else {
          await this.prisma.bagrimdik_peserta_rekrutmen_asn.updateMany({
            where: {
              import_file_date: {
                lte: oneYearAgo,
              },
              status_rekrutmen: status_rekrutmen_asn_enum.AKTIF,
            },
            data: {
              status_rekrutmen: status_rekrutmen_asn_enum.SIAP_LATSAR,
            },
          });
        }
      } else {
        // Default case for all other dates
        await this.prisma.bagrimdik_peserta_rekrutmen_asn.updateMany({
          where: {
            import_file_date: {
              lte: oneYearAgo,
            },
            status_rekrutmen: status_rekrutmen_asn_enum.AKTIF,
          },
          data: {
            status_rekrutmen: status_rekrutmen_asn_enum.SIAP_LATSAR,
          },
        });
      }
    } catch (err) {
      throw err;
    }
  }

  async getListStatusASN(req: any) {
    const data = Object.values(status_rekrutmen_asn_enum);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.REKRUTMEN_BAGRIMDIK_READ as ConstantLogType,
        message,
        data,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data,
    };
  }
}
