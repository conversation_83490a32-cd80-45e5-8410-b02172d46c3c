import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { RekrutmenBagrimdikASNService } from '../service/rekrutmen-bagrimdik.asn.service';
import { RekrutmenBagrimdikPPPKService } from '../service/rekrutmen-bagrimdik.pppk.service';

@Injectable()
export class RekrutmenBagrimdikScheduler {
  constructor(
    private readonly rekrutmenBagrimdikASNService: RekrutmenBagrimdikASNService,
    private readonly rekrutmenBagrimdikPPPKService: RekrutmenBagrimdikPPPKService,
  ) {}

  @Cron('0 2,5 * * *') // Runs at 1 AM and 4 AM daily
  async updateStatusPesertaASN(): Promise<void> {
    console.log('Update status peserta ASN triggered...');
    await this.rekrutmenBagrimdikASNService.updatePesertaStatus();
  }

  @Cron('30 0,4 * * *') // Runs at 2 AM and 5 AM daily
  async updateStatusPesertaPPPK(): Promise<void> {
    console.log('Update status peserta PPPK triggered...');
    await this.rekrutmenBagrimdikPPPKService.updatePesertaStatus();
  }
}
