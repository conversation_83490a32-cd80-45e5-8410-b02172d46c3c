import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { SippRecapService } from '../service/sipp-recap.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { UserRoleGuard } from '../../../core/guards/user-role.guards';
import { RolePermissionNeeded } from '../../../core/decorators/role-permission.decorator';

@Controller('sipp-rekap')
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class SippRecapController {
  private readonly logger = new Logger(SippRecapController.name);

  constructor(private readonly sippRecapService: SippRecapService) {}

  @Get('/agama')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getRekapAgama(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRekapAgama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.agama(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getRekapAgama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/golongan_darah')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getRekapGolonganDarah(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRekapGolonganDarah.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.golonganDarah(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getRekapGolonganDarah.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/nivellering')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getRekapNivellering(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRekapNivellering.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.nivellering(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getRekapNivellering.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/status_pernikahan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getStatusPernikahan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getStatusPernikahan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.statusPernikahan(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getStatusPernikahan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/pendidikan_pembentukan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getPendidikanPembentukan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPendidikanPembentukan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.pendidikanPembentukan(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getPendidikanPembentukan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/pendidikan_pengembangan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getPendidikanPengembangan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPendidikanPengembangan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.pendidikanPengembangan(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getPendidikanPengembangan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/pendidikan_umum')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getPendidikanUmum(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPendidikanUmum.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.pendidikanUmum(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getPendidikanUmum.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jurusan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getJurusan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJurusan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.jurusan(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getJurusan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jumlah_anggota_polri')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getJumlahAnggotaPolri(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJumlahAnggotaPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.jumlahAnggotaPolri(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getJumlahAnggotaPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jumlah_anggota_pns_polri')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getJumlahAnggotaPNSPolri(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJumlahAnggotaPNSPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.jumlahAnggotaPNSPolri(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getJumlahAnggotaPNSPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jumlah_anggota_p3k')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getJumlahAnggotaP3K(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJumlahAnggotaPNSPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.jumlahAnggotaP3K(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getJumlahAnggotaPNSPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_polri_persatker')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPolriPersatker(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPolriPersatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPolriPersatker(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPolriPersatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_polri_persatwil')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPolriPersatwil(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPolriPersatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPolriPersatwil(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPolriPersatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_pns_polri_persatker')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPNSPolriPerSatker(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPNSPolriPerSatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPNSPolriPersatker(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPNSPolriPerSatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_pns_polri_persatwil')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPNSPolriPerSatwil(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPNSPolriPerSatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPNSPolriPersatwil(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPNSPolriPerSatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_p3k_persatker')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaP3KPersatker(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaP3KPersatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaP3KPersatker(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaP3KPersatker.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_p3k_persatwil')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaP3KPersatwil(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaP3KPersatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaP3KPersatwil(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaP3KPersatwil.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_polri_perpangkat_patipamenpama')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPolriPerPangkat_patipamenpama(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPolriPerPangkat_patipamenpama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPolriPerPangkat_patipamenpama(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPolriPerPangkat_patipamenpama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
  
  @Get('/anggota_polri_perpangkat_bintaratamtama')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPolriPerPangkat_bintaratamtama(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPolriPerPangkat_bintaratamtama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPolriPerPangkat_bintaratamtama(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPolriPerPangkat_bintaratamtama.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_pns_polri_perpangkat_pembinautamapenatamuda')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPNSPolriPerPangkat_pembinautamapenatamuda(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPNSPolriPerPangkat_pembinautamapenatamuda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPNSPolriPerPangkat_pembinautamapenatamuda(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPNSPolriPerPangkat_pembinautamapenatamuda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/anggota_pns_polri_perpangkat_pengaturjurumuda')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getAnggotaPNSPolriPerPangkat_pengaturjurumuda(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAnggotaPNSPolriPerPangkat_pengaturjurumuda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.anggotaPNSPolriPerPangkat_pengaturjurumuda(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getAnggotaPNSPolriPerPangkat_pengaturjurumuda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel_polri')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getPersonelPolri(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPersonelPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.personelPolri(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getPersonelPolri.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
  
  @Get('/ulang_tahun')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getUlangTahun(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getUlangTahun.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.ulangTahun(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getUlangTahun.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jumlah_personel_perangkat_sisdm')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getJumlahPersonelPerangkatSisdm(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJumlahPersonelPerangkatSisdm.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.jumlahPersonelPerangkatSisdm(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.getJumlahPersonelPerangkatSisdm.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/search_satuan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async searchSatuan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.searchSatuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sippRecapService.searchSatuan(req,paginationData,searchandsortData);
    this.logger.log(
      `Leaving ${this.searchSatuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  
}
