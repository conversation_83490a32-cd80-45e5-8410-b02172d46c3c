import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { getAge } from 'src/core/utils/sipp-3.utils';
import { PaginationDto } from 'src/core/dtos/pagination.dto';
import { SearchAndSortDTO } from 'src/core/dtos/searchandsort.dto';

function filterLevelSatuanQuery(personel_id: string, level: string, satuan_id: number) {

  let satuanFilterQuery = `AND personel.id IN (
    select personel_id
    from mv_latest_jabatan_personel sjstp
    inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
    where swtp.[LEVEL]_top_parent_id  = (select [LEVEL]_top_parent_id from mv_satuan_with_top_parents where id = (select satuan_id from mv_latest_jabatan_personel where personel_id = ${personel_id}))
  )`

  if (level === "Level 3") satuanFilterQuery = satuanFilterQuery.replaceAll(`[LEVEL]`, `third`)
  else if (level === "Level 2") satuanFilterQuery = satuanFilterQuery.replaceAll(`[LEVEL]`, `second`)
  else if (satuan_id) satuanFilterQuery = satuanFilterQuery.replace(/where swtp.\[LEVEL.+/, `where swtp.second_top_parent_id = ${satuan_id}`)
  else satuanFilterQuery = ``

  return satuanFilterQuery
}

@Injectable()
export class SippRecapService {
  constructor(
    private prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) { }

  async agama(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select agama.id as id ,agama.nama, jenis_kelamin, count(*)
          from mv_latest_pangkat_personel lpp
          inner join personel on personel.id = lpp.personel_id
          inner join agama on agama.id = personel.agama_id
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
          where status_pilihan in ('AKTIF', 'NOTHING')
          ${satuanFilterQuery}
          group by agama.id,agama.nama, jenis_kelamin;
      `;



      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { agama: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { agama: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async golonganDarah(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']

    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select golongan_darah, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by golongan_darah, jenis_kelamin
      `;



      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { golongan_darah: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { golongan_darah, jenis_kelamin, count } of arrayResult) {
        if (!map.has(golongan_darah)) map.set(golongan_darah, { golongan_darah: golongan_darah, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(golongan_darah);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async nivellering(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select nivellering.id, nivellering.nama, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_nivellering_terakhir on mv_nivellering_terakhir.personel_id = personel.id
		      inner join nivellering on nivellering.id = mv_nivellering_terakhir.nivellering_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by nivellering.id,nivellering.nama, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { nivellering: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { nivellering: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async statusPernikahan(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `

          select status_kawin.id, status_kawin.nama, jenis_kelamin, count(*)
          from mv_latest_pangkat_personel lpp 
          inner join personel on personel.id = lpp.personel_id
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
          inner join status_kawin on status_kawin.id = personel.status_kawin_id
          where status_pilihan in ('AKTIF', 'NOTHING')
          ${satuanFilterQuery}
          group by status_kawin.id, status_kawin.nama, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { status_pernikahan: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      map.set(0, { status_pernikahan: "CERAI", pria: 0, wanita: 0, jumlah: 0 })
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { status_pernikahan: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        if (nama === `DUDA`) {
          map.get(0).pria = count;
          map.get(0).jumlah += parseInt(count)
        }
        else if (nama === `JANDA`) {
          map.get(0).wanita = count
          map.get(0).jumlah += parseInt(count)
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }


      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async pendidikanPembentukan(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select diktuk.id, diktuk.nama, diktuk_kategori.nama kategori, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join diktuk_personel on diktuk_personel.personel_id = personel.id
		      inner join diktuk on diktuk.id = diktuk_personel.diktuk_id
			    inner join diktuk_kategori on diktuk_kategori.id = diktuk.kategori_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by diktuk.id,diktuk.nama, diktuk_kategori.nama, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { tingkat: "TOTAL", kategori: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, kategori, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { tingkat: nama, kategori: kategori, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async pendidikanPengembangan(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select dikbangum.id, dikbangum.nama, dikbangum.nama_alternatif, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
          inner join dikbangum_personel on dikbangum_personel.personel_id = personel.id
          inner join dikbangum on dikbangum.id = dikbangum_personel.dikbangum_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by dikbangum.id,dikbangum.nama, dikbangum.nama_alternatif, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { tingkat: "TOTAL", nama_alternatif: 'TOTAL', pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, nama_alternatif, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { tingkat: nama, nama_alternatif: nama_alternatif, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async pendidikanUmum(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select dikum.id, dikum.nama, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
          inner join dikum_personel on dikum_personel.personel_id = personel.id
          inner join dikum_detail on dikum_personel.dikum_detail_id = dikum_detail.id
          inner join dikum on dikum.id = dikum_detail.dikum_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by dikum.id,dikum.nama, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { tingkat: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { tingkat: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async jurusan(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select jurusan.id,jurusan.nama, jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join dikum_personel on dikum_personel.personel_id = personel.id
          inner join dikum_detail on dikum_personel.dikum_detail_id = dikum_detail.id
		      inner join jurusan on jurusan.id = dikum_detail.jurusan_id
          where status_pilihan = 'AKTIF'
          ${satuanFilterQuery}
          group by jurusan.id,jurusan.nama, jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { jurusan: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { jurusan: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async jumlahAnggotaPolri(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select jenis_kelamin, count(*) jumlah
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 2
          ${satuanFilterQuery}
          group by jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);



      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          personelData,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: personelData,
      };
    } catch (err) {
      throw err;
    }
  }

  async jumlahAnggotaPNSPolri(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select jenis_kelamin, count(*) jumlah
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 1
          ${satuanFilterQuery}
          group by jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);



      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          personelData,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: personelData,
      };
    } catch (err) {
      throw err;
    }
  }

  async jumlahAnggotaP3K(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      //let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select jenis_kelamin, count(*) jumlah
          from p3k
          group by jenis_kelamin
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);



      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          personelData,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: personelData,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPolriPersatker(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 2 and satuan.nama not like 'POLDA%'
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`'POLDA%'`, `'POLRES%'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`and satuan.nama not like 'POLDA%'`, `and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id and satuan.nama not like '%POLSEK%'`)
      }


      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { satker: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satker: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPolriPersatwil(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 2 and satuan.nama like 'POLDA%'
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`'POLDA%'`, `'POLRES%'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`and satuan.nama like 'POLDA%'`, `and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id and satuan.nama like '%POLSEK%'`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satwil: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satwil: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPNSPolriPersatker(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 1 and satuan.nama not like 'POLDA%'
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`'POLDA%'`, `'POLRES%'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`and satuan.nama not like 'POLDA%'`, `and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id and satuan.nama not like '%POLSEK%'`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satker: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPNSPolriPersatwil(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 1 and satuan.nama like 'POLDA%'
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`'POLDA%'`, `'POLRES%'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`and satuan.nama not like 'POLDA%'`, `and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id and satuan.nama like '%POLSEK%'`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satwil: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satwil: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaP3KPersatker(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      //let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from p3k
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = p3k.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.id
          where satuan.nama not like 'POLDA%'
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satker: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaP3KPersatwil(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      //let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id,satuan.nama,jenis_kelamin, count(*)
          from p3k
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = p3k.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.id
          where satuan.nama like 'POLDA%'
          group by satuan.id,satuan.nama, jenis_kelamin
          ORDER BY satuan.jenis_id ASC
      `;

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satwil: "TOTAL", pria: 0, wanita: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, jenis_kelamin, count } of arrayResult) {
        if (!map.has(id)) map.set(id, { satwil: nama, pria: 0, wanita: 0, jumlah: 0 });

        const currentEntry = map.get(id);

        if (jenis_kelamin === "LAKI-LAKI") {
          currentEntry.pria = count;
          totalEntry.pria += parseInt(count);
        } else if (jenis_kelamin === "PEREMPUAN") {
          currentEntry.wanita = count;
          totalEntry.wanita += parseInt(count);
        }

        currentEntry.jumlah += parseInt(count);
        totalEntry.jumlah += parseInt(count);
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPolriPerPangkat_patipamenpama(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id satuan_id,satuan.nama satker,pangkat.id pangkat_id,pangkat.nama_singkat pangkat, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 2 and pangkat.id between 31 and 40
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, pangkat.id,pangkat.nama_singkat
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause.replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF' and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { satuan_id, pangkat_id, satker, pangkat, count } of arrayResult) {


        const key = satuan_id

        if (!map.has(key)) map.set(key, { satker: satker, jumlah: 0 })

        let currentEntry = map.get(key)

        currentEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = count
        currentEntry.jumlah += parseInt(count)

        if(totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] == null) totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = 0
        totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] += parseInt(count)

        totalEntry.jumlah += parseInt(count)

      }
      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPolriPerPangkat_bintaratamtama(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id satuan_id,satuan.nama satker,pangkat.id pangkat_id,pangkat.nama_singkat pangkat, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 2 and pangkat.id between 19 and 30
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, pangkat.id,pangkat.nama_singkat
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause.replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF' and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { satuan_id, pangkat_id, satker, pangkat, count } of arrayResult) {


        const key = satuan_id

        if (!map.has(key)) map.set(key, { satker: satker, jumlah: 0 })

        let currentEntry = map.get(key)

        currentEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = count
        currentEntry.jumlah += parseInt(count)

        if(totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] == null) totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = 0
        totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] += parseInt(count)
        totalEntry.jumlah += parseInt(count)

      }
      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPNSPolriPerPangkat_pembinautamapenatamuda(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id satuan_id,satuan.nama satker,pangkat.id pangkat_id,pangkat.nama_singkat pangkat, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 1 and pangkat.id between 10 and 18
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, pangkat.id,pangkat.nama_singkat
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause.replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF' and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { satuan_id, pangkat_id, satker, pangkat, count } of arrayResult) {


        const key = satuan_id

        if (!map.has(key)) map.set(key, { satker: satker, jumlah: 0 })

        let currentEntry = map.get(key)

        currentEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = count
        currentEntry.jumlah += parseInt(count)

        if(totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] == null) totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = 0
        totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] += parseInt(count)
        totalEntry.jumlah += parseInt(count)

      }
      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async anggotaPNSPolriPerPangkat_pengaturjurumuda(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select satuan.id satuan_id,satuan.nama satker,pangkat.id pangkat_id,pangkat.nama_singkat pangkat, count(*)
          from personel
          inner join status_aktif on status_aktif.id = personel.status_aktif_id
		      inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
		      inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join mv_satuan_with_top_parents on mv_satuan_with_top_parents.id = mv_latest_jabatan_personel.satuan_id
          inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id
          where status_pilihan = 'AKTIF' and pangkat.kategori_id = 1 and pangkat.id between 2 and 9
          ${satuanFilterQuery}
          group by satuan.id,satuan.nama, pangkat.id,pangkat.nama_singkat
          ORDER BY satuan.jenis_id ASC
      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause.replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.third_top_parent_id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF'  AND satuan.nama not like 'POLDA%' `)
          .replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      else if (level === `Level 3`) {
        whereClause = whereClause
          .replace(`inner join satuan on satuan.id = mv_satuan_with_top_parents.second_top_parent_id`, `inner join satuan on satuan.id = mv_satuan_with_top_parents.id`)
          .replace(`where status_pilihan = 'AKTIF'`, `where status_pilihan = 'AKTIF' and mv_satuan_with_top_parents.third_top_parent_id = mv_satuan_with_top_parents.atasan_id`)
      }

      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();

      map.set("TOTAL", { satker: "TOTAL", jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { satuan_id, pangkat_id, satker, pangkat, count } of arrayResult) {


        const key = satuan_id

        if (!map.has(key)) map.set(key, { satker: satker, jumlah: 0 })

        let currentEntry = map.get(key)

        currentEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = count
        currentEntry.jumlah += parseInt(count)

        if(totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] == null) totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] = 0
        totalEntry[pangkat.toLowerCase().replace(/\s+/g, '')] += parseInt(count)
        totalEntry.jumlah += parseInt(count)

      }
      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async personelPolri(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const nivelering = search_text[search_column.indexOf('nivelering')]
    const jenis_kelamin = search_text[search_column.indexOf('jenis_kelamin')]

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select personel.nama_lengkap, pangkat.nama nama_pangkat, personel.nrp, jabatan.nama nama_jabatan, mv_latest_jabatan_personel.tmt_jabatan, nivellering.nama nama_nivellering, personel.uid
          from personel
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join jabatan on jabatan.id = mv_latest_jabatan_personel.jabatan_id
          inner join mv_nivellering_terakhir on mv_nivellering_terakhir.personel_id = personel.id
          inner join nivellering on nivellering.id = mv_nivellering_terakhir.nivellering_id
          inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
          inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          where nivellering.nama = '${nivelering}' and jenis_kelamin = '${jenis_kelamin}'
          ${satuanFilterQuery}
          LIMIT ${limit} OFFSET ${limit * (page - 1)}
          
      `;

      const [personelData, totalData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause),
        this.prisma.$queryRawUnsafe<{}>(whereClause.replace(/select personel.nama.+/g, 'select count(*)').replace(/LIMIT.+/g, '')),
      ]);

      let queryResult = (personelData as any[]).map(x => new Object({ nama: x.nama_lengkap, pangkat: x.nama_pangkat, nip_nrp: x.nrp, jabatan: `${x.nama_jabatan} (${x.nama_nivellering}) (${getAge(x.tmt_jabatan, new Date())}) `, uid: x.uid }))

      const totalPage = Math.ceil(parseInt(totalData[0].count) / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          { data: queryResult, totalData, totalPage, page },
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalData: totalData[0].count,
        totalPage,
        page
      };
    } catch (err) {
      throw err;
    }
  }

  async ulangTahun(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const satuan_id = search_text[search_column.indexOf('satuan_id')]
    const pangkat = JSON.parse(search_text[search_column.indexOf('pangkat')])
    const bulan = parseInt(search_text[search_column.indexOf('bulan')])

    const noFilter = !satuan_id && pangkat.length === 0 && !bulan


    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
          select personel.nama_lengkap, pangkat.nama_singkat nama_pangkat, personel.nrp, jabatan.nama nama_jabatan, mv_latest_jabatan_personel.tmt_jabatan, nivellering.nama nama_nivellering, personel.uid, personel.tanggal_lahir
          from personel
          inner join mv_latest_jabatan_personel on mv_latest_jabatan_personel.personel_id = personel.id
          inner join jabatan on jabatan.id = mv_latest_jabatan_personel.jabatan_id
          inner join mv_nivellering_terakhir on mv_nivellering_terakhir.personel_id = personel.id
          inner join nivellering on nivellering.id = mv_nivellering_terakhir.nivellering_id
          inner join mv_pangkat_terakhir on mv_pangkat_terakhir.personel_id = personel.id
          inner join pangkat on pangkat.id = mv_pangkat_terakhir.pangkat_id
          WHERE true
          ${bulan ? ` AND EXTRACT(MONTH from tanggal_lahir) = ${bulan}` : ``} 
          ${pangkat.length !== 0 ? `AND pangkat.nama in (${pangkat.map(p => `'${p}'`).join(',')})` : ``}
          ${noFilter ? `AND ( EXTRACT(MONTH from tanggal_lahir) = EXTRACT(MONTH from CURRENT_DATE) ) AND ( EXTRACT(DAY from tanggal_lahir) = EXTRACT(DAY from CURRENT_DATE) )` : ``}
          ${satuanFilterQuery}
          LIMIT ${limit} OFFSET ${limit * (page - 1)}
          
      `;

      const [personelData, totalData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause),
        this.prisma.$queryRawUnsafe<{}>(whereClause.replace(/select personel.nama.+/g, 'select count(*)').replace(/LIMIT.+/g, '')),
      ]);

      let queryResult = (personelData as any[]).map(x => new Object({ nama: x.nama_lengkap, pangkat: x.nama_pangkat, nip_nrp: x.nrp, jabatan: `${x.nama_jabatan} (${x.nama_nivellering}) (${getAge(x.tmt_jabatan, new Date())}) `, uid: x.uid, tanggal_lahir: x.tanggal_lahir }))

      const totalPage = Math.ceil(parseInt(totalData[0].count) / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          { data: queryResult, totalData, totalPage, page },
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalData: totalData[0].count,
        totalPage,
        page
      };
    } catch (err) {
      throw err;
    }
  }

  async jumlahPersonelPerangkatSisdm(req, paginationData, searchandsortData) {

    const { sort_column, sort_desc, search_column, search_text, search } = searchandsortData

    const satuan_id = req.query.satuan_id
    const personel_id = req['user']['personel_id']
    const level = req['selected_user_role']['level']


    try {
      let satuanFilterQuery = filterLevelSatuanQuery(personel_id, level, satuan_id)

      let whereClause = `
        select satuan.id,satuan.nama, 
        count(case when activity = 'Verifikasi data otp lewat authentication service' then 1 end) sudah_login,
        statistik_satker.count - count(case when activity = 'Verifikasi data otp lewat authentication service' then 1 end) belum_login
        from (select second_top_parent_id, sum(statistik_satker.count) count from statistik_satker group by second_top_parent_id) statistik_satker
        INNER JOIN satuan on satuan.id = statistik_satker.second_top_parent_id
        INNER JOIN mv_satuan_with_top_parents mswtp on mswtp.second_top_parent_id = satuan.id
        INNER JOIN mv_latest_jabatan_personel mljp on mljp.satuan_id = mswtp.id
        INNER JOIN personel ON personel.id = mljp.personel_id
        INNER JOIN users ON users.personel_id = personel.id
        INNER join status_aktif on status_aktif.id = personel.status_aktif_id
        LEFT JOIN (select user_id, activity from logs_activity group by user_id,activity) logs_activity ON logs_activity.user_id = users.id
        WHERE status_pilihan = 'AKTIF'
        {satuanFilterQuery}
        group by satuan.id, satuan.nama, statistik_satker.count
        ORDER BY satuan.jenis_id ASC

      `;

      if (satuan_id || level === `Level 2`) {
        whereClause = whereClause.replace(/second/g, `third`).replace(/statistik_satker/g, `statistik_satker_thirdtoparent`).replace(`ORDER BY satuan.jenis_id ASC`, `ORDER BY satuan.jenis_id DESC`)
      }

      if (satuan_id || level === `Level 3`) {
        whereClause = whereClause.replace(/second_top_parent_id/g, `id`).replace(/statistik_satker/g, `statistik_satker_fourthlevel`)
      }

      whereClause = whereClause.replace(`{satuanFilterQuery}`, satuanFilterQuery)


      const [personelData] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause)
      ]);

      let arrayResult = personelData as any[];

      const map = new Map();
      map.set("TOTAL", { satker: "TOTAL", sudah_login: 0, belum_login: 0, jumlah: 0 });
      const totalEntry = map.get("TOTAL");

      for (const { id, nama, sudah_login, belum_login } of arrayResult) {

        if (!map.has(id)) map.set(id, { satker: nama, sudah_login: parseInt(sudah_login), belum_login: parseInt(belum_login), jumlah: 0 })

        const currentEntry = map.get(id);


        currentEntry.jumlah += parseInt(sudah_login + belum_login)


        totalEntry.sudah_login += parseInt(sudah_login)
        totalEntry.belum_login += parseInt(belum_login)

        totalEntry.jumlah += parseInt(sudah_login + belum_login)
      }

      const queryResult = [...map.values()];

      // Move the first element (which is "TOTAL") to the end
      if (queryResult.length > 1) {
        const totalRow = queryResult.shift();
        queryResult.push(totalRow);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP_REKAP_MODULE,
      );

      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP_REKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async searchSatuan(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } = searchAndSortData;


    let [totalData, satuans] = await this.prisma.$transaction([
      this.prisma.satuan.count({
        where: {nama: {contains:search, mode:'insensitive'}}
      }),
      this.prisma.satuan.findMany({
        where: {nama: {contains:search, mode:'insensitive'}, mv_satuan_with_top_parents_self:{isNot:null}},
        include:{mv_satuan_with_top_parents_self:true},
        take: limit

      }),
    ]);

    //Only get first to second level
    satuans = satuans.filter(s=> s.mv_satuan_with_top_parents_self.second_top_parent_id === s.mv_satuan_with_top_parents_self.third_top_parent_id)


    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SATUAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SATUAN_READ as ConstantLogType,
        message,
        satuans,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: satuans,
    };
  }

}
