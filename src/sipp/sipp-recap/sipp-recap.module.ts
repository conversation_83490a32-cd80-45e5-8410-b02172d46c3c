import { forwardRef, Module } from '@nestjs/common';
import { SippRecapService } from './service/sipp-recap.service';
import { SippRecapController } from './controller/sipp-recap.controller';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SippRecapController],
  providers: [SippRecapService, MinioService],
})
export class SippRecapModule {}
