import { forwardRef, Module } from '@nestjs/common';
import { RiwayatLengkapService } from './service/riwayat-lengkap.service';
import { RiwayatLengkapController } from './controller/riwayat-lengkap.controller';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PangkatService } from '../../mdm/pangkat/pangkat/service/pangkat.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { Sipp3Service } from '../sipp-3/service/sipp-3.service';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [RiwayatLengkapController],
  providers: [
    RiwayatLengkapService,
    PangkatService,
    MinioService,
    Sipp3Service,
  ],
})
export class RiwayatLengkapModule {}
