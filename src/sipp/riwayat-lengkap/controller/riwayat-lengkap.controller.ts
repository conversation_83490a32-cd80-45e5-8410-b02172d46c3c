import {
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { RiwayatLengkapService } from '../service/riwayat-lengkap.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PangkatService } from '../../../mdm/pangkat/pangkat/service/pangkat.service';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { UserRoleGuard } from '../../../core/guards/user-role.guards';
import { SippAllowAccessGuard } from '../../../core/guards/sipp-allow-access-guard';
import { RolePermissionNeeded } from '../../../core/decorators/role-permission.decorator';

@Controller('riwayat-lengkap')
export class RiwayatLengkapController {
  private readonly logger = new Logger(RiwayatLengkapController.name);

  constructor(
    private readonly riwayatLengkapService: RiwayatLengkapService,
    private readonly pangkatService: PangkatService,
  ) {}

  @Get('/personel/:uid')
  @RolePermissionNeeded('RIWAYAT_HIDUP_LENGKAP', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatPersonel.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.personelData(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/bio/:uid')
  @RolePermissionNeeded('RIWAYAT_HIDUP_SINGKAT', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  @HttpCode(200)
  async get(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.get.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.bio(req, uid);
    this.logger.log(
      `Leaving ${this.get.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pangkat/:uid')
  @RolePermissionNeeded('KEPANGKATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPersentasePersonel(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.riwayatPersentasePersonel.name} with uid: ${uid}`,
    );
    const response = await this.riwayatLengkapService.pangkatPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPersentasePersonel.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pendidikan/:uid')
  @RolePermissionNeeded('PENDIDIKAN_UMUM', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPersentasePendidikan(
    @Req() req: any,
    @Param('uid') uid: string,
  ) {
    this.logger.log(
      `Entering ${this.riwayatPersentasePendidikan.name} with uid: ${uid}`,
    );
    const response = await this.riwayatLengkapService.pendidikan(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPersentasePendidikan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pemindahan-instansi/:uid')
  @RolePermissionNeeded('RIWAYAT_PINDAH_INSTANSI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPindahInstansi(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.riwayatPindahInstansi.name} with uid: ${uid}`,
    );
    const response = await this.riwayatLengkapService.pindahInstansi(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPindahInstansi.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/jabatan/:uid')
  @RolePermissionNeeded('JABATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatJabatan(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatJabatan.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.jabatanPersonel(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatJabatan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pelatihan/:uid')
  @RolePermissionNeeded('PELATIHAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPelatihan(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatPelatihan.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.pelatihan(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPelatihan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/smk/:uid')
  @RolePermissionNeeded('SMK', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatSMK(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatSMK.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.smk(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatSMK.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/orang-tua/:uid')
  @RolePermissionNeeded('KELUARGA_ORANG_TUA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatOrangTua(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatOrangTua.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.orangtua(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatOrangTua.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/pasangan/:uid')
  @RolePermissionNeeded('KELUARGA_PASANGAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPasangan(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatPasangan.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.pasangan(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPasangan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/anak/:uid')
  @RolePermissionNeeded('KELUARGA_ANAK', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatAnak(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatAnak.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.anak(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatAnak.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dikbangspes/:uid')
  @RolePermissionNeeded('DIKBANGSPES', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async dikbangspes(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.dikbangspes.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.dikbangspes(req, uid);
    this.logger.log(
      `Leaving ${this.dikbangspes.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dikbangnum/:uid')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async dikbangnum(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.dikbangnum.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.dikbangnum(req, uid);
    this.logger.log(
      `Leaving ${this.dikbangnum.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/diktuk/:uid')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async diktuk(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.diktuk.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.diktuk(req, uid);
    this.logger.log(
      `Leaving ${this.diktuk.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/psikologi/:uid')
  @RolePermissionNeeded('PSIKOLOGI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async psikologi(@Req() req: any, @Param('uid') uid: string) {
    const response = await this.riwayatLengkapService.psikologi(req, uid);

    return response;
  }

  @Get('/kesehatan/:uid')
  @RolePermissionNeeded('KESEHATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async kesehatan(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.kesehatan.name} with uid: ${uid}`);

    const response = await this.riwayatLengkapService.rikkesla(req, uid);
    this.logger.log(
      `Leaving ${this.kesehatan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/rohani/:uid')
  @RolePermissionNeeded('ROHANI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async rohani(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.rohani.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.rohani(req, uid);
    this.logger.log(
      `Leaving ${this.rohani.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/jasmani/:uid')
  @RolePermissionNeeded('JASMANI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async jasmani(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.jasmani.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.jasmani(req, uid);
    this.logger.log(
      `Leaving ${this.jasmani.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/bko/:uid')
  @RolePermissionNeeded('BKO', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async bko(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.bko.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.bko(req, uid);
    this.logger.log(
      `Leaving ${this.bko.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/olahraga/:uid')
  @RolePermissionNeeded('OLAHRAGA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async olahraga(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.olahraga.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.olahraga(req, uid);
    this.logger.log(
      `Leaving ${this.olahraga.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/penghargaan/:uid')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPenghargaan(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.riwayatPenghargaan.name} with uid: ${uid}`,
    );
    const response = await this.riwayatLengkapService.penghargaan(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatPenghargaan.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/assesment/:uid')
  @RolePermissionNeeded('ASSESSMENT', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatAssesment(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatAssesment.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.assesment(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatAssesment.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/penugasan-luar-struktur/:uid')
  @RolePermissionNeeded('PENUGASAN_LUAR_STRUKTUR', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatPenugasanLuarStruktur(
    @Req() req: any,
    @Param('uid') uid: string,
  ) {
    this.logger.log(
      `Entering ${this.riwayatPenugasanLuarStruktur.name} with uid: ${uid}`,
    );
    const response = await this.riwayatLengkapService.penugasanDiluarStruktur(
      req,
      uid,
    );
    this.logger.log(
      `Leaving ${this.riwayatPenugasanLuarStruktur.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/dikpol/:uid')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatDikpol(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatDikpol.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.dikpol(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatDikpol.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/tanhor/:uid')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatTanhor(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatTanhor.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.tanhor(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatTanhor.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/bahasa/:uid')
  @RolePermissionNeeded('BAHASA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async riwayatBahasa(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatBahasa.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.bahasa(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatBahasa.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/misi/:uid')
  @RolePermissionNeeded('MISI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async misi(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.misi.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.misi(req, uid);
    this.logger.log(
      `Leaving ${this.misi.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/hobi/:uid')
  @RolePermissionNeeded('HOBI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async hobi(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.hobi.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.hobi(req, uid);
    this.logger.log(
      `Leaving ${this.hobi.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/brevet/:uid')
  @RolePermissionNeeded('BREVET', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async brevet(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.brevet.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.brevet(req, uid);
    this.logger.log(
      `Leaving ${this.brevet.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/kgb/:uid')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async kgb(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.kgb.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.kgb(req, uid);
    this.logger.log(
      `Leaving ${this.kgb.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/dto/:dokumen_type/:uid')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async documentUpdate(
    @Req() req: any,
    @Param('uid') uid: string,
    @Param('dokumen_type') documentType: string,
    @UploadedFiles() file: any,
    // @UploadedFiles() file: Express.Multer.File[],
  ) {
    this.logger.log(`Entering ${this.documentUpdate.name} with uid: ${uid}`);

    const response =
      await this.riwayatLengkapService.dokumenUpdateRiwayatLengkap(
        req,
        uid,
        documentType,
        file.file[0],
      );
    this.logger.log(
      `Leaving ${this.documentUpdate.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/riwayat_singkat/:uid')
  @RolePermissionNeeded('RIWAYAT_HIDUP_SINGKAT', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard ,SippAllowAccessGuard, UserRoleGuard)
  @HttpCode(200)
  async riwayatSingkat(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.riwayatSingkat.name} with uid: ${uid}`);
    const response = await this.riwayatLengkapService.riwayatSingkat(req, uid);
    this.logger.log(
      `Leaving ${this.riwayatSingkat.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
