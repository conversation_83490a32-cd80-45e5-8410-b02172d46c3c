import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { MinioService } from '../../../api-utils/minio/service/minio.service';
import * as lodash from 'lodash';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class RiwayatLengkapService {
  constructor(
    private prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async personelData(req, uid: string) {
    try {
      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          tempat_lahir: true,
          jenis_kelamin: true,
          no_hp: true,
          email: true,
          foto_file: true,
          golongan_darah: true,
          akta_kelahiran_file: true,
          asabri_nomor: true,
          asabri_file: true,
          bpjs_nomor: true,
          bpjs_file: true,
          paspor_nomor: true,
          paspor_file: true,
          npwp_nomor: true,
          npwp_file: true,
          anak_ke: true,
          jumlah_saudara: true,
          masa_dinas_surut_tmt: true,
          lhkpn_file: true,
          kk_nomor: true,
          kk_file: true,
          ktp_nomor: true,
          ktp_file: true,
          masa_dinas_surut_file: true,
          suku: {
            select: {
              id: true,
              nama: true,
            },
          },

          agama: {
            select: {
              id: true,
              nama: true,
            },
          },

          alamat: {
            select: {
              id: true,
              alamat: true,
            },
          },

          keluarga_personel: {
            select: {
              id: true,
              nama_keluarga: true,
              no_hp: true,
              alamat: true,
              hubungan_keluarga: {
                select: { hubungan: true },
              },
            },
          },

          personel_fisik: {
            select: {
              berat_badan: true,
              tinggi_badan: true,
              warna_kulit: true,
              warna_mata: true,
              warna_rambut: true,
              jenis_rambut: true,
              sidik_jari_1: true,
              sidik_jari_2: true,
            },
          },

          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          status_kawin: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        where: { uid: uid },
      });
      const foto_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      );
      const bpjs_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.bpjs_file}`,
      );
      const asabri_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.asabri_file}`,
      );
      const npwp_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.npwp_file}`,
      );
      const kk_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.kk_file}`,
      );
      const paspor_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.paspor_file}`,
      );
      const akta_kelahiran_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.akta_kelahiran_file}`,
      );
      const lhkpn_file = await this.minioService.convertFileKeyToURL(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.lhkpn_file}`,
      );

      const queryResult = {
        ...personel,
        foto_file: foto_file,
        bpjs_file: bpjs_file,
        asabri_file: asabri_file,
        npwp_file: npwp_file,
        kk_file: kk_file,
        paspor_file: paspor_file,
        akta_kelahiran_file: akta_kelahiran_file,
        lhkpn_file: lhkpn_file,
        status_kawan: personel.status_kawin?.nama,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async pangkatPersonel(req, uid: string) {
    try {
      const getPangkatPersonel = await this.prisma.pangkat_personel.findMany({
        select: {
          id: true,
          tmt: true,
          pangkat: {
            select: {
              id: true,
              nama_singkat: true,
            },
          },
          kep_nomor: true,
          kep_tanggal: true,
          kep_file: true,
        },
        where: {
          personel: {
            uid: uid,
          },
          deleted_at: null,
        },
        orderBy: {
          tmt: 'desc',
        },
      });

      const result = getPangkatPersonel.map((item) => {
        return {
          ...item,
          pangkat: item.pangkat.nama_singkat,
          pangkat_id: item.pangkat.id,
          // hubungan_keluarga : item.hubungan_keluarga?.hubungan
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });

      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          kep_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.kep_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async pendidikan(req, uid: string) {
    try {
      const getDikumPersonel = await this.prisma.dikum_personel.findMany({
        select: {
          id: true,
          created_at: true,
          dikum_detail: {
            select: {
              dikum: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              institusi: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              jurusan: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              gelar: {
                select: {
                  nama: true,
                },
              },
            },
          },
          tanggal_mulai: true,
          tanggal_lulus: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tanggal_lulus: 'asc',
        },
      });

      const queryResult = getDikumPersonel.map((item) => {
        const date = new Date(item.tanggal_lulus);
        const year = date.getFullYear();
        return {
          id: item.id,
          tanggal_mulai: item.tanggal_mulai,
          tanggal_lulus: item.tanggal_lulus,
          tingkat: item.dikum_detail?.dikum?.nama,
          jurusan: item.dikum_detail?.jurusan?.nama || null,
          institusi: item.dikum_detail?.institusi?.nama || null,
          dikum_id: item.dikum_detail?.dikum?.id,
          jurusan_id: item.dikum_detail?.jurusan?.id,
          institusi_id: item.dikum_detail?.institusi?.id,
          tahun: item.tanggal_lulus ? year : null,
          gelar: item.dikum_detail?.gelar?.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async pindahInstansi(req, uid: string) {
    try {
      const getDikumPersonel = await this.prisma.penugasan_personel.findMany({
        select: {
          id: true,
          created_at: true,
          jabatan_penugasan: true,
          penugasan_instansi: {
            select: {
              nama: true,
            },
          },
          tmt_mulai: true,
          tmt_selesai: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tmt_mulai: 'asc',
        },
      });

      const queryResult = getDikumPersonel.map((item) => {
        return {
          ...item,
          instansi: item?.penugasan_instansi?.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async jabatanPersonel(req, uid: string) {
    try {
      const getJabatanPersonel = await this.prisma.jabatan_personel.findMany({
        select: {
          id: true,
          jabatan : true,
          jabatans: {
            select: {
              nama: true,
              satuan: {
                select: {
                  mv_satuan_with_top_parents_self: true,
                },
              },
              nivellering: {
                select: {
                  nama: true
                }
              }
            },
          },
          tmt_jabatan: true,
          is_ps: true,
          keterangan: true,
          kep_nomor: true,
          kep_file: true,
        },
        where: {
          personel: {
            uid: uid,
          },
          deleted_at: null,
        },
        orderBy: {
          tmt_jabatan: 'desc',
        },
      });

      const result = getJabatanPersonel.map((item) => {
        const date = new Date(item.tmt_jabatan);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const tmt = `${month}-${day}-${year}`;

        return {
          id: item.id,
          jabatan: item.jabatans?.nama ? item.jabatans?.nama : item.jabatan,
          tmt: tmt,
          nivellering: item.jabatans?.nivellering?.nama,
          kep_nomor: item.kep_nomor,
          kep_file: item.kep_file,
          is_ps: item.is_ps,
          keterangan: item.keterangan,
          satuan_second_top_parent:
            item.jabatans?.satuan?.mv_satuan_with_top_parents_self
              ?.second_top_parent_nama,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          kep_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.kep_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async pelatihan(req, uid: string) {
    try {
      const getPelatihan = await this.prisma.pelatihan_personel.findMany({
        select: {
          id: true,
          tanggal_masuk: true,
          tanggal_selesai: true,
          nilai: true,
          transkrip_nilai_file: true,
          sertifikat_nomor: true,
          sertifikat_file: true,
          pelatihan: {
            select: {
              nama: true,
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tanggal_selesai: 'asc',
        },
      });

      const result = getPelatihan.map((item) => {
        return {
          ...item,
          nama_pelatihan: item.pelatihan.nama,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          transkrip_nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.transkrip_nilai_file}`,
          ),
          sertifikat_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.sertifikat_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async smk(req, uid: string) {
    try {
      const personelData = await this.personelData(req, uid);

      if (!personelData) {
        throw new HttpException(`Personel Not Found`, HttpStatus.NOT_FOUND);
      }

      const personel_id = parseInt(personelData.data.id + '');
      const getSMK = await this.prisma.smk_personel.findMany({
        select: {
          id: true,
          tahun: true,
          semester: true,
          nilai: true,
          keterangan: true,
          // nilai_file: true,
        },
        where: {
          personel_id: personel_id,
        },
        orderBy: [
          {
            tahun: 'asc',
          },
          {
            semester: 'asc',
          },
        ],
      });

      const result = getSMK.map((item) => {
        return {
          ...item,
        };
      });
      // const personel = await this.prisma.personel.findFirstOrThrow({
      //   select: {
      //     nrp: true,
      //   },
      //   where: {
      //     id: personel_id,
      //   },
      // });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          // nilai_file: await this.minioService.convertFileKeyToURL(
          //   process.env.MINIO_BUCKET_NAME,
          //   `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.nilai_file}`,
          // ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async orangtua(req, uid: string) {
    try {
      const getSMK = await this.prisma.keluarga_personel.findMany({
        select: {
          id: true,
          nama_keluarga: true,
          jenis_kelamin: true,
          alamat: true,
          status: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          agama: {
            select: {
              nama: true,
            },
          },
          hubungan_keluarga: {
            select: {
              hubungan: true,
            },
          },
        },
        where: {
          AND: [
            {
              personel: {
                uid: uid,
              },
              OR: [
                {
                  hubungan_keluarga: {
                    hubungan: {
                      contains: 'AYAH',
                    },
                  },
                },
                {
                  hubungan_keluarga: {
                    hubungan: {
                      contains: 'IBU',
                    },
                  },
                },
              ],
            },
          ],
        },
      });

      const queryResult = getSMK.map((item) => {
        return {
          ...item,
          agama: item.agama?.nama,
          hubungan_keluarga: item.hubungan_keluarga?.hubungan,
        };
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async pasangan(req, uid: string) {
    try {
      const data = await this.prisma.keluarga_personel.findMany({
        select: {
          id: true,
          nama_keluarga: true,
          jenis_kelamin: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          agama: {
            select: {
              nama: true,
            },
          },
          status: true,
          hubungan_keluarga: true,
          tanggal_nikah: true,
          kpis_nomor: true,
          kpis_file: true,
          buku_nikah_nomor: true,
          buku_nikah_file: true,
          status_nikah: true,
          status_pernikahan: true,
          cerai_file: true,
          no_ijinnikah: true,
          file_ijinnikah: true,
          ktp_nomor: true,
          pekerjaan_keluarga: {
            select: {
              jenis_pekerjaan: true,
              jenis_pekerjaan_id: true,
            },
          },
        },
        where: {
          AND: [
            {
              personel: {
                uid: uid,
              },
              OR: [
                {
                  hubungan_keluarga: {
                    hubungan: {
                      contains: 'ISTRI',
                    },
                  },
                },
                {
                  hubungan_keluarga: {
                    hubungan: {
                      contains: 'SUAMI',
                    },
                  },
                },
              ],
            },
          ],
        },
        orderBy: { tanggal_nikah: 'asc' },
      });

      const result = data.map((item) => {
        return {
          ...item,
          agama: item.agama?.nama,
          hubungan_keluarga: item.hubungan_keluarga?.hubungan,
          jenis_pekerjaan:
            item.pekerjaan_keluarga[item.pekerjaan_keluarga.length - 1]
              ?.jenis_pekerjaan?.jenis,
          jenis_pekerjaan_id:
            item.pekerjaan_keluarga[item.pekerjaan_keluarga.length - 1]
              ?.jenis_pekerjaan_id,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          buku_nikah_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.buku_nikah_file}`,
          ),
          kpis_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.kpis_file}`,
          ),
          file_ijinnikah: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.file_ijinnikah}`,
          ),
          cerai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.cerai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async anak(req, uid: string) {
    try {
      const data = await this.prisma.keluarga_personel.findMany({
        select: {
          id: true,
          nama_keluarga: true,
          jenis_kelamin: true,
          alamat: true,
          status: true,
          tempat_lahir: true,
          tanggal_lahir: true,
          agama: {
            select: {
              nama: true,
            },
          },
          hubungan_keluarga: {
            select: {
              hubungan: true,
            },
          },
        },
        where: {
          AND: [
            {
              personel: {
                uid: uid,
              },
              OR: [
                {
                  hubungan_keluarga: {
                    hubungan: {
                      contains: 'ANAK',
                    },
                  },
                },
              ],
            },
          ],
        },
        orderBy: { tanggal_lahir: 'asc' },
      });

      const queryResult = data.map((item) => {
        return {
          ...item,
          agama: item.agama?.nama,
          hubungan_keluarga: item.hubungan_keluarga?.hubungan,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async dikbangspes(req: any, uid: string) {
    try {
      const result = await this.prisma.dikbangspes_personel.findMany({
        select: {
          id: true,
          dikbangspes: { select: { nama: true } },
          tanggal_masuk: true,
          tanggal_selesai: true,
          nilai: true,
          transkrip_nilai_file: true,
          ijazah_no: true,
          ijazah_file: true,
          ranking: true,
          jumlah_siswa: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tanggal_selesai: 'asc',
        },
      });

      const f = result.map((r) => {
        return { ...r, dikbangspes: r.dikbangspes.nama };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of f) {
        queryResult.push({
          ...obj,
          ijazah_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.ijazah_file}`,
          ),
          transkrip_nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.transkrip_nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async dikbangnum(req: any, uid: string) {
    try {
      const result = await this.prisma.dikbangum_personel.findMany({
        select: {
          id: true,
          dikbangum: { select: { nama: true } },
          tanggal_masuk: true,
          tanggal_selesai: true,
          nilai: true,
          transkrip_nilai_file: true,
          ijazah_no: true,
          ijazah_file: true,
          ranking: true,
          jumlah_siswa: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tanggal_selesai: 'asc',
        },
      });

      const f = result.map((r) => {
        return { ...r, dikbangnum: r.dikbangum.nama };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of f) {
        queryResult.push({
          ...obj,
          ijazah_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.ijazah_file}`,
          ),
          transkrip_nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.transkrip_nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async diktuk(req: any, uid: string) {
    try {
      const result = await this.prisma.diktuk_personel.findMany({
        select: {
          id: true,
          diktuk: { select: { nama: true } },
          tanggal_masuk: true,
          tanggal_selesai: true,
          nilai: true,
          transkrip_nilai_file: true,
          ijazah_no: true,
          ijazah_file: true,
          ranking: true,
          jumlah_siswa: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tanggal_selesai: 'asc',
        },
      });

      /* const f = result.map((r) => {
        return { ...r, diktuk: r.diktuk?.nama };
      }); */

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          ijazah_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.ijazah_file}`,
          ),
          transkrip_nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.transkrip_nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Successfully get riwayat dikbangnum',
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async psikologi(req: any, uid: string) {
    try {
      const result = await this.prisma.psikologi_personel.findMany({
        select: {
          id: true,
          tahun: true,
          semester: true,
          nilai: true,
          nilai_file: true,
          keterangan: true,
        },
        where: {
          personel: { uid },
        },
        orderBy: [{ tahun: 'asc' }, { semester: 'asc' }],
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async rikkesla(req: any, uid: string) {
    try {
      const queryResult = await this.prisma.rikkesla_personel.findMany({
        select: {
          id: true,
          tahun: true,
          semester: true,
          nilai: true,
          nilai_file: true,
          keterangan: true,
        },
        where: {
          personel: { uid },
        },
        orderBy: [{ tahun: 'asc' }, { semester: 'asc' }],
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async rohani(req: any, uid: string) {
    try {
      const result = await this.prisma.rohani_personel.findMany({
        select: {
          id: true,
          tahun: true,
          semester: true,
          nilai: true,
          nilai_file: true,
          keterangan: true,
        },
        where: {
          personel: { uid },
        },
        orderBy: [{ tahun: 'asc' }, { semester: 'asc' }],
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async jasmani(req: any, uid: string) {
    try {
      const result = await this.prisma.jasmani_personel.findMany({
        select: {
          id: true,
          tahun: true,
          nilai_akhir: true,
          keterangan: true,
          nilai_lari_12_m: true,
          hasil_lari_12_m: true,
          hasil_pull_up: true,
          nilai_pull_up: true,
          nilai_sit_up: true,
          hasil_sit_up: true,
          semester: true,
          nilai_file: true,
          nilai_shutle_run: true,
          hasil_shutle_run: true,
          nilai_chinning: true,
          hasil_chinning: true,
          nilai_a: true,
          nilai_b: true,
        },
        where: {
          personel: { uid },
        },
        orderBy: {
          tahun: 'asc',
        },
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async bko(req: any, uid: string) {
    try {
      const result = await this.prisma.bko_personel.findMany({
        select: {
          id: true,
          satuan: true,
          tmt_bko: true,
          sprin_nomor: true,
          sprin_file: true,
        },
        where: {
          personel: { uid },
        },
        orderBy: {
          tmt_bko: 'asc',
        },
      });

      const res = result.map((r) => {
        return { ...r, satuan: r.satuan.nama, satuan_id: r.satuan.id };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of res) {
        queryResult.push({
          ...obj,
          sprin_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.sprin_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async olahraga(req: any, uid: string) {
    try {
      const queryResult = await this.prisma.olahraga_personel.findMany({
        include: {
          olahraga: true,
        },
        where: {
          personel: { uid: uid },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async penghargaan(req, uid: string) {
    try {
      const data = await this.prisma.penghargaan_personel.findMany({
        select: {
          id: true,
          penghargaan: {
            select: {
              id: true,
              nama: true,
            },
          },
          penghargaan_tingkat: {
            select: {
              id: true,
              nama: true,
            },
          },
          tgl_penghargaan: true,
          surat_no: true,
          surat_file: true,
          penghargaan_file: true,
          updated_at: true,
          note: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: { tgl_penghargaan: 'asc' },
      });

      const result = data.map((item) => {
        return {
          ...item,
          penghargaan: item.penghargaan?.nama,
          penghargaan_id: item.penghargaan?.id,
          penghargaan_tingkat: item.penghargaan_tingkat?.nama,
          penghargaan_tingkat_id: item.penghargaan_tingkat?.id,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          penghargaan_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.penghargaan_file}`,
          ),
          surat_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.surat_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async assesment(req, uid: string) {
    try {
      const data = await this.prisma.assessment_personel.findMany({
        select: {
          id: true,
          nama: true,
          tmt_mulai: true,
          tmt_selesai: true,
          nilai: true,
          nilai_file: true,
          nilai_kualitatif: true,
          keterangan: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: [
          {
            tmt_mulai: 'asc',
          },
        ],
      });

      const result = data.map((item) => {
        return {
          ...item,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          nilai_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.nilai_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async penugasanDiluarStruktur(req, uid: string) {
    try {
      const data = await this.prisma.penugasan_personel.findMany({
        select: {
          id: true,
          sprin_nomor: true,
          sprin_file: true,
          sprin_penerbit: true,
          jabatan_penugasan: true,
          tmt_mulai: true,
          tmt_selesai: true,
          negara: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
          penugasan_instansi: {
            isNot: {
              nama: 'MABES POLRI',
            },
          },
          deleted_at: null,
        },
        orderBy: [
          {
            tmt_mulai: 'asc',
          },
        ],
      });

      const result = data.map((item) => {
        return {
          ...item,
          negara: item.negara?.nama,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          sprin_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.sprin_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async dikpol(req, uid: string) {
    try {
      const data = await this.prisma.dikbangum_personel.findMany({
        select: {
          dikbangum: {
            select: {
              nama: true,
              nama_alternatif: true,
              dikbangum_kategori: {
                select: {
                  nama: true,
                },
              },
            },
          },
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: [
          {
            id: 'desc',
          },
        ],
      });

      const queryResult = data.map((item) => {
        return {
          ...item,
          nama: item.dikbangum.nama,
          nama_alternatif: item.dikbangum.nama_alternatif,
          kategori: item.dikbangum.dikbangum_kategori.nama,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async tanhor(req, uid: string) {
    try {
      const data = await this.prisma.tanhor_personel.findMany({
        select: {
          id: true,
          tanhor: {
            select: {
              nama: true,
            },
          },
          tgl_tanhor: true,
          surat_no: true,
          surat_file: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: [
          {
            tgl_tanhor: 'asc',
          },
        ],
      });

      const result = data.map((item) => {
        return {
          ...item,
          tanhor: item.tanhor.nama,
        };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          surat_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.surat_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async bahasa(req, uid: string) {
    try {
      const data = await this.prisma.bahasa_personel.findMany({
        select: {
          id: true,
          bahasa: {
            select: {
              id: true,
              nama: true,
            },
          },
          is_aktif: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: [],
      });

      const queryResult = data.map((item) => {
        return {
          ...item,
          bahasa: item.bahasa.nama,
          bahasa_id: item.bahasa.id,
          is_aktif: item.is_aktif,
          id: item.id,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async misi(req: any, uid: string) {
    try {
      const data = await this.prisma.misi_personel.findMany({
        select: {
          id: true,
          misi_id: true,
          misi: {
            select: {
              nama: true,
              kota: true,
              provinsi: true,
              negara: true,
            },
          },
        },
        where: {
          personel: { uid: uid },
        },
        orderBy: {
          created_at: 'asc',
        },
      });

      const queryResult = data.map((item) => {
        return {
          ...item,
          nama: item.misi.nama,
          kota: item.misi.kota,
          provinsi: item.misi.provinsi,
          negara: item.misi.negara,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async hobi(req: any, uid: string) {
    try {
      const queryResult = await this.prisma.hobi_personel.findMany({
        include: {
          hobi: true,
        },
        where: {
          personel: { uid: uid },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async brevet(req: any, uid: string) {
    try {
      const data = await this.prisma.brevet_personel.findMany({
        select: {
          id: true,
          brevet: { select: { nama: true } },
          tmt_brevet: true,
          kep_nomor: true,
          kep_file: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: { tmt_brevet: 'asc' },
      });

      const result = data.map((d) => {
        return { ...d, brevet: d.brevet.nama };
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          kep_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.kep_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async kgb(req: any, uid: string) {
    try {
      const result = await this.prisma.kgb_personel.findMany({
        select: {
          id: true,
          kep_nomor: true,
          kep_file: true,
          tmt: true,
        },
        where: {
          personel: {
            uid: uid,
          },
        },
        orderBy: {
          tmt: 'asc',
        },
      });

      const personel = await this.prisma.personel.findFirstOrThrow({
        select: {
          nrp: true,
        },
        where: {
          uid: uid,
        },
      });
      const queryResult = [];

      for (const obj of result) {
        queryResult.push({
          ...obj,
          kep_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${obj.kep_file}`,
          ),
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async dokumenUpdateRiwayatLengkap(
    req: any,
    uid: string,
    documentType: string,
    file: any,
  ) {
    try {
      const allowedDocuments = [
        'bpjs_file',
        'asabri_file',
        'npwp_file',
        'kk_file',
        'paspor_file',
        'akta_kelahiran_file',
        'lhkpn_file',
        'foto_file'
      ];

      if (!allowedDocuments.includes(documentType)) {
        throw new BadRequestException(
          `Harap upload type document in ${allowedDocuments}`,
        );
      }

      // if (!uploaded.ETag) return { rawFile: file };

      // if(documentType!= "")

      let personel;
      try {
        personel = await this.prisma.personel.findFirstOrThrow({
          where: {
            uid: uid,
          },
        });
      } catch (e) {
        console.error(e);
      }

      const queryResult = await this.minioService.uploadFileWithNRP(
        file,
        personel.nrp,
      );
      delete file.buffer;
      delete file.fieldname;

      let data = {
        updated_at: new Date(),
      };

      data[documentType] = queryResult.filename;

      await this.prisma.personel.update({
        where: { id: personel.id },
        data: data,
        // data: {
        //   bpjs_file : "",
        //   asabri_file : "",
        //   npwp_file : "",
        //   kk_file : "",
        //   paspor_file : "",
        //   akta_kelahiran_file : "",
        //   updated_at: new Date(),
        // },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_WRITE_MINIO as ConstantLogType,
          message,
          queryResult.filename,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult.filename,
        url: queryResult.Location,
      };
    } catch (error) {
      throw error;
    }
  }

  async bio(req, uid) {
    const personel = await this.prisma.personel.findFirst({
      select: {
        id: true,
        uid: true,
        nrp: true,
        nama_lengkap: true,
        tanggal_lahir: true,
        tempat_lahir: true,
        jenis_kelamin: true,
        no_hp: true,
        email: true,
        foto_file: true,
        agama: {
          select: {
            id: true,
            nama: true,
          },
        },
        suku: {
          select: {
            id: true,
            nama: true,
          },
        },
        status_aktif: {
          select: {
            id: true,
            nama: true,
          },
        },
        status_kawin: {
          select: {
            id: true,
            nama: true,
          },
        },
        mv_pangkat_terakhir: {
          select: {
            mv_pangkat_terakhir_pangkat_sekarang: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
          },
        },
        jabatan_personel: {
          select: {
            tmt_jabatan: true,
            jabatans: {
              select: {
                id: true,
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                nivellering: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
            is_ps: true,
            keterangan: true,
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        dikum_personel: {
          select: {
            dikum_detail: {
              select: {
                gelar: true,
                dikum : true
              },
            },
            tanggal_lulus: true,
          },
        },
        dikbangum_personel: { select: { gelar: true, tanggal_selesai: true } },
        diktuk_personel: { select: { gelar: true, tanggal_selesai: true } },
      },
      where: { uid: uid },
    });

    if (!personel) {
      throw new NotFoundException(`personel uid ${uid} not found`);
    }

    const currentDate = new Date();
    const birthDate = new Date(personel.tanggal_lahir);

    // Calculate age
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDifference = currentDate.getMonth() - birthDate.getMonth();
    if (
      monthDifference < 0 ||
      (monthDifference === 0 && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    let golongan_binjas = '';

    if (age >= 18 && age <= 30) {
      golongan_binjas = 'GOLONGAN I';
    } else if (age >= 31 && age <= 40) {
      golongan_binjas = 'GOLONGAN II';
    } else if (age >= 41 && age <= 50) {
      golongan_binjas = 'GOLONGAN III';
    } else if (age >= 51 && age <= 58) {
      golongan_binjas = 'GOLONGAN IV';
    } else {
      golongan_binjas = 'Tidak sesuai kriteria';
    }

    const retirementDate = new Date(
      birthDate.getFullYear() + 58,
      birthDate.getMonth(),
      birthDate.getDate(),
    );

    let sisa_dinas = null;
    if (currentDate < retirementDate) {
      const diffTime = Math.abs(
        retirementDate.getTime() - currentDate.getTime(),
      );
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const years = Math.floor(diffDays / 365);
      const remainingDaysAfterYears = diffDays % 365;

      const months = Math.floor(remainingDaysAfterYears / 30);
      const remainingDays = remainingDaysAfterYears % 30;

      if (years > 0) {
        sisa_dinas = `${years} tahun ${months} bulan ${remainingDays} hari`;
      } else if (months > 0) {
        sisa_dinas = `${months} bulan ${remainingDays} hari`;
      } else {
        sisa_dinas = `${remainingDays} hari`;
      }
    } else {
      sisa_dinas = 'Pensiun';
    }

    const queryResult = {
      ...personel,
      id: Number(personel.id),
      tanggal_lahir: new Date(personel.tanggal_lahir)
        .toISOString()
        .split('T')[0], // YYYY-MM-DD
      sisa_dinas: sisa_dinas,
      umur: age,
      golongan_binjas: golongan_binjas,

      // foto_file:

      //production pake ini sementara
      foto_file: await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      ),

      pangkat:
        personel.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang ??
        null,
      jabatan: personel.jabatan_personel?.[0]?.jabatans
        ? {
            ...personel.jabatan_personel?.[0]?.jabatans,
            lama_jabatan: this.getAge(
              personel.jabatan_personel?.[0]?.tmt_jabatan,
              personel.jabatan_personel?.[1]?.tmt_jabatan ?? new Date(),
            ),
            tmt: new Date(personel.jabatan_personel?.[0]?.tmt_jabatan)
              .toISOString()
              .split('T')[0],
            is_ps: personel.jabatan_personel?.[0]?.is_ps,
            keterangan: personel.jabatan_personel?.[0]?.keterangan,
          }
        : null,
      satuan: personel.jabatan_personel[0]?.jabatans.satuan ?? null,
      gelar: [
        personel.dikum_personel.map(
          (d) =>
            new Object({
              ...d.dikum_detail?.gelar,
              from: 'dikum',
              tanggal_selesai: d.tanggal_lulus,
            }),
        ),
        personel.dikbangum_personel.map(
          (d) =>
            new Object({
              ...d.gelar,
              from: 'dikbangum',
              tanggal_selesai: d.tanggal_selesai,
            }),
        ),
        personel.diktuk_personel.map(
          (d) =>
            new Object({
              ...d.gelar,
              from: 'diktuk',
              tanggal_selesai: d.tanggal_selesai,
            }),
        ),
      ]
        .flat(2)
        .filter((g: any) => g.id),
    };


    delete queryResult.mv_pangkat_terakhir;
    delete queryResult.jabatan_personel;
    delete queryResult.jabatan?.satuan;
    delete queryResult.dikbangum_personel;
    delete queryResult.diktuk_personel;
    delete queryResult.dikum_personel;

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async riwayatSingkat(req, uid) {
    try {
      // let bio = await this.bio(req,uid);

      let [
        diktuk,
        dikbangum,
        dikbangspes,
        dikum,
        pangkat,
        jabatan,
        assessment,
        tanhor,
        penghargaan,
        sertifikat,
        bahasa,
        bagassus,
      ]: any[] = await this.prisma.$transaction([
        this.prisma.diktuk_personel.findMany({
          select: {
            diktuk: {
              select: {
                nama: true,
              },
            },
            tanggal_selesai: true,
            ranking: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_selesai: 'asc',
          },
        }),
        this.prisma.dikbangum_personel.findMany({
          select: {
            dikbangum: {
              select: {
                nama: true,
                nama_alternatif: true,
              },
            },
            tanggal_selesai: true,
            ranking: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_selesai: 'asc',
          },
        }),
        this.prisma.dikbangspes_personel.findMany({
          select: {
            dikbangspes: {
              select: {
                nama: true,
              },
            },
            tanggal_selesai: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_selesai: 'asc',
          },
        }),
        this.prisma.dikum_personel.findMany({
          select: {
            tanggal_lulus: true,
            created_at: true,
            dikum_detail: {
              select: {
                dikum: {
                  select: {
                    nama: true,
                    tingkat_pendidikan: true,
                  },
                },
                institusi: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal_lulus: 'asc',
          },
        }),
        this.prisma.pangkat_personel.findMany({
          select: {
            tmt: true,
            pangkat: {
              select: {
                nama_singkat: true,
              },
            },
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tmt: 'asc',
          },
        }),
        this.prisma.jabatan_personel.findMany({
          select: {
            jabatan : true,
            jabatans: {   
              select: {
                nama: true,
                satuan: {
                  select: {
                    mv_satuan_with_top_parents_self: true,
                  },
                },
                nivellering: {
                  select: {
                    nama: true
                  }
                }
              }
            },
            tmt_jabatan: true,
            is_ps: true,
            keterangan: true,
          },
          where: {
            personel: {
              uid: uid,
            },
            deleted_at: null
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
        }),

        this.prisma.assessment_personel.findMany({
          select: {
            id: true,
            personel: {
              select: {
                id: true,
                nama_lengkap: true,
              },
            },
            nama: true,
            tmt_mulai: true,
            tmt_selesai: true,
            nilai: true,
            nilai_file: true,
            nilai_kualitatif: true,
            keterangan: true,
          },
          where: {
            personel: {
              uid: uid,
            },
            deleted_at: null,
          },
          orderBy: {
            tmt_mulai: 'asc',
          },
        }),
        this.prisma.tanhor_personel.findMany({
          select: {
            tgl_tanhor: true,
            tanhor: {
              select: {
                nama: true,
              },
            },
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tgl_tanhor: 'asc',
          },
        }),
        this.prisma.penghargaan_personel.findMany({
          select: {
            tgl_penghargaan: true,
            penghargaan: {
              select: {
                nama: true,
              },
            },
            penghargaan_tingkat: {
              select: {
                nama: true,
              },
            },
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tgl_penghargaan: 'asc',
          },
        }),
        this.prisma.personel_sertifikasi.findMany({
          select: {
            tanggal: true,
            tingkat: true,
            cabang: true,
          },
          where: {
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            tanggal: 'asc',
          },
        }),
        this.prisma.bahasa_personel.findMany({
          select: {
            bahasa: {
              select: {
                nama: true,
                jenis: true,
              },
            },
            is_aktif: true,
          },
          where: {
            deleted_at: null,
            personel: {
              uid: uid,
            },
          },
          orderBy: {
            created_at: 'asc',
          },
        }),
        this.prisma.penugasan_personel.findMany({
          where: {
            personel: {
              uid: uid,
            },
            penugasan_instansi: {
              isNot: {
                nama: 'MABES POLRI',
              },
            },
            deleted_at: null,
          },
          select: {
            jabatan_penugasan: true,
            penugasan_instansi: { select: { nama: true } },
          },
          orderBy: { tmt_mulai: 'asc' },
        }),
      ]);

      bahasa = bahasa.map((item) => {
        return {
          bahasa: item.bahasa.nama,
          jenis: item.bahasa.jenis,
          status: item.is_aktif,
        };
      });

      sertifikat = sertifikat.map((item) => {
        delete item.tanggal;
        return { ...item, tahun: item.tanggal.getFullYear() };
      });

      tanhor = tanhor.map((item) => {
        return {
          tmt: item?.tgl_tanhor,
          tanhor: item.tanhor.nama,
        };
      });

      penghargaan = penghargaan.map((item) => {
        return {
          tmt: item?.tgl_penghargaan,
          penghargaan: item.penghargaan.nama,
          tingkat: item.penghargaan_tingkat?.nama
        };
      });

      assessment = assessment.map((item) => {
        const tmtStart = new Date(item.tmt_mulai);
        const dayMulai = String(tmtStart.getDate()).padStart(2, '0');
        const monthMulai = String(tmtStart.getMonth() + 1).padStart(2, '0');
        const yearMulai = tmtStart.getFullYear();
        const tmtMulai =
          item.tmt_mulai == null
            ? null
            : `${monthMulai}-${dayMulai}-${yearMulai}`;

        const tmtEnd = new Date(item.tmt_selesai);
        const daySelesai = String(tmtEnd.getDate()).padStart(2, '0');
        const monthSelesai = String(tmtEnd.getMonth() + 1).padStart(2, '0');
        const yearSelesai = tmtEnd.getFullYear();
        const tmtSelesai =
          item.tmt_selesai == null
            ? null
            : `${monthSelesai}-${daySelesai}-${yearSelesai}`;
        return {
          jabatan: item.nama,
          nilai_kualitatif: item.nilai_kualitatif,
          tmt_mulai: tmtMulai,
          tmt_selesai: tmtSelesai,
        };
      });

      jabatan = jabatan.map((item) => {
        const date = new Date(item.tmt_jabatan);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const tmt = item.tmt_jabatan == null ? null : `${month}-${day}-${year}`;

        return {
          jabatan: item.jabatans?.nama ? item.jabatans?.nama : item.jabatan,
          tmt: tmt,
          nivellering: item.jabatans?.nivellering?.nama,
          is_ps: item.is_ps,
          keterangan: item.keterangan,
          manual : true
        };
      });

      dikbangum = dikbangum.map((item) => {
        const date = item.tanggal_selesai ? new Date(item.tanggal_selesai) : null;
        const year = date ? date.getFullYear() : '-';
        return {
          tingkat: item.dikbangum.nama_alternatif,
          tahun: year,
          ranking: item.ranking,
        };
      });

      diktuk = diktuk.map((item) => {
        const date = item.tanggal_selesai ? new Date(item.tanggal_selesai) : null;
        const year = date ? date.getFullYear() : '-';
        return {
          tingkat: item.diktuk.nama,
          tahun: year,
          ranking: item.ranking
        };
      });

      dikbangspes = dikbangspes.map((item) => {
        return {
          dikbang: item.dikbangspes.nama,
          tmt: item?.tanggal_selesai,
        };
      });


      dikum = dikum.map((item) => {
        const date = new Date(item.tanggal_lulus);
        const year = date.getFullYear();
        return {
          tingkat: item.dikum_detail?.dikum?.nama,
          institusi: item.dikum_detail?.institusi?.nama || null,
          tahun: year,
        };
      });

      pangkat = pangkat.map((item) => {
        const date = new Date(item.tmt);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const tmt = item.tmt == null ? null : `${month}-${day}-${year}`;
        return {
          tmt: tmt,
          pangkat: item.pangkat.nama_singkat,
        };
      });

      let dikpol = [...diktuk, ...dikbangum];
      let dikpolSorted = lodash.orderBy(dikpol, ['tahun'], ['asc']);

      const queryResult = {
        dikpol: dikpolSorted,
        diktuk,
        dikbangum,
        dikbangspes,
        dikum,
        pangkat,
        jabatan,
        assessment,
        tanhor,
        penghargaan,
        sertifikat,
        bahasa,
        bagassus,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.RIWAYAT_LENGKAP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  getAge(startDate, endDate) {
    let start = new Date(startDate);
    let end = new Date(endDate);

    // Calculate the differences in years, months, and days
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    // Adjust for negative month or day differences
    if (days < 0) {
      months--;
      let previousMonth = new Date(
        end.getFullYear(),
        end.getMonth(),
        0,
      ).getDate(); // Days in the previous month
      days += previousMonth;
    }
    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Tahun ${months} Bulan ${days} Hari`;
  }
}
