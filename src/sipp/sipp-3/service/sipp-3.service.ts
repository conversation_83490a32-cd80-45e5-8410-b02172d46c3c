import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import {
  SortSearchColumn,
  SortSearchColumnV2,
} from '../../../core/utils/search.utils';
import { jenis_kelamin_enum, Prisma } from '@prisma/client';
import {
  CreatePersonelDTO,
  UpdatePersonelDTO,
} from 'src/personel/dto/personel.dto';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import * as moment from 'moment';
import * as lodash from 'lodash';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  deepMerge,
  getAge,
  multiSortBy<PERSON>ey,
  autofitExcelJsColumns,
} from '../../../core/utils/sipp-3.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

const ExcelJS = require('exceljs');

@Injectable()
export class Sipp3Service {
  constructor(
    private prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) { }

  async getStatisikPersonelData(req: any) {
    try {
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = '';
      let p3kQuery =
        'select count(id) total, jenis_kelamin from p3k [WHEREAREYOU] group by jenis_kelamin';
      let satuanName = '';

      if (selectedUserRole.level === 'Level 3') {
        //   satuanFilterQuery = `AND p.id IN (
        //   select personel_id
        //   from mv_latest_jabatan_personel jt
        //   where jt.satuan_id = ${req["user"]["satuan_id"]}
        // )`

        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id})`;

        //Don't return anything
        p3kQuery = p3kQuery.replace('[WHEREAREYOU]', ' WHERE 1 = 0 ');

        let satuan = await this.prisma.satuan.findUnique({
          where: { id: satuanAtas.third_top_parent_id },
        });
        satuanName = satuan.nama;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;

        p3kQuery = p3kQuery.replace(
          '[WHEREAREYOU]',
          ` WHERE satuan_id = (select second_top_parent_id from mv_satuan_with_top_parents where id = ${satuanTerakhir.satuan_id})`,
        );

        let satuan = await this.prisma.mv_satuan_with_top_parents.findUnique({
          where: { id: req['user']['satuan_id'] },
        });
        satuanName = satuan.second_top_parent_nama;
      } else p3kQuery = p3kQuery.replace('[WHEREAREYOU]', ``);

      let whereClause = `
          SELECT
            jenis_kelamin,
            kategori_id,
            COUNT(DISTINCT p.id)
          FROM
            personel p
            INNER JOIN mv_pangkat_terakhir spp ON spp.personel_id = p.id
            INNER JOIN pangkat ON pangkat.id = spp.pangkat_id
            INNER JOIN status_aktif sa ON p.status_aktif_id = sa.id
          WHERE
            sa.status_pilihan = 'AKTIF' 
            ${satuanFilterQuery}
          GROUP BY
            jenis_kelamin,
            kategori_id
      `;

      const [personelData, p3kdata] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(whereClause),
        this.prisma.$queryRawUnsafe<{}>(p3kQuery),
      ]);

      // const x = await this.prisma.p3k.groupBy({
      //   by:['jenis_kelamin'], _count:{id:true},
      //   where:{satuan_id:{gte:}}
      // })

      let arrayResult = personelData as any[];

      const queryResult = {
        nama_satuan: satuanName,
        polri_laki_laki: arrayResult.find((ar: any) => {
          return ar.jenis_kelamin === 'LAKI-LAKI' && ar.kategori_id == 2;
        })
          ? arrayResult.find((ar: any) => {
            return ar.jenis_kelamin === 'LAKI-LAKI' && ar.kategori_id == 2;
          }).count
          : 0,
        polri_perempuan: arrayResult.find((ar: any) => {
          return ar.jenis_kelamin === 'PEREMPUAN' && ar.kategori_id == 2;
        })
          ? arrayResult.find((ar: any) => {
            return ar.jenis_kelamin === 'PEREMPUAN' && ar.kategori_id == 2;
          }).count
          : 0,
        pns_laki_laki: arrayResult.find((ar: any) => {
          return ar.jenis_kelamin === 'LAKI-LAKI' && ar.kategori_id == 1;
        })
          ? arrayResult.find((ar: any) => {
            return ar.jenis_kelamin === 'LAKI-LAKI' && ar.kategori_id == 1;
          }).count
          : 0,
        pns_perempuan: arrayResult.find((ar: any) => {
          return ar.jenis_kelamin === 'PEREMPUAN' && ar.kategori_id == 1;
        })
          ? arrayResult.find((ar: any) => {
            return ar.jenis_kelamin === 'PEREMPUAN' && ar.kategori_id == 1;
          }).count
          : 0,
        p3k_laki_laki:
          (p3kdata as any[]).find((p) => p.jenis_kelamin === 'LAKI-LAKI')
            ?.total ?? 0,
        p3k_perempuan:
          (p3kdata as any[]).find((p) => p.jenis_kelamin === 'PEREMPUAN')
            ?.total ?? 0,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        ...queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForPatiPamenPama(req: any) {
    try {
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = '';

      if (selectedUserRole.level === 'Level 3') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
      }

      const [queryResult] = await this.prisma.$transaction([
        this.prisma.$queryRaw<{}>(
          Prisma.raw(`
              select pangkat.id, pangkat.nama_singkat, count(distinct spp.personel_id)
              from mv_pangkat_terakhir  spp
                       inner join pangkat on pangkat.id = spp.pangkat_id
                       inner join personel p on p.id = spp.personel_id
                       inner join status_aktif sa on p.status_aktif_id = sa.id
              where sa.status_pilihan in ('AKTIF','NOTHING')
                AND spp.pangkat_id >= 31
                and spp.pangkat_id <= 40
                  ${satuanFilterQuery}
              group by pangkat.id, pangkat.nama_singkat
              order by pangkat.id ASC`),
        ),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForPersonelPNS(req: any) {
    let selectedUserRole = req['selected_user_role'];
    let satuanFilterQuery = '';

    if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
    } else if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
    }

    try {
      const [queryResult] = await this.prisma.$transaction([
        this.prisma.$queryRaw<{}>(
          Prisma.raw(`
              select pangkat.id, pangkat.nama_singkat, count(distinct spp.personel_id)
              from mv_latest_pangkat_personel  spp
                       inner join pangkat on pangkat.id = spp.pangkat_id
                       inner join personel p on p.id = spp.personel_id
                       inner join status_aktif sa on p.status_aktif_id = sa.id
              where sa.status_pilihan in ('AKTIF','NOTHING')
                AND spp.pangkat_id >= 10
                and spp.pangkat_id <= 18
                  ${satuanFilterQuery}
              group by pangkat.id, pangkat.nama_singkat
              order by pangkat.id ASC`),
        ),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForBintaraTamtama(req: any) {
    try {
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = '';

      if (selectedUserRole.level === 'Level 3') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
      }

      const [queryResult] = await this.prisma.$transaction([
        this.prisma.$queryRaw<{}>(
          Prisma.raw(`
              select pangkat.id, pangkat.nama_singkat, count(distinct spp.personel_id)
              from mv_latest_pangkat_personel  spp
                       inner join pangkat on pangkat.id = spp.pangkat_id
                       inner join personel p on p.id = spp.personel_id
                       inner join status_aktif sa on p.status_aktif_id = sa.id
              where sa.status_pilihan in ('AKTIF','NOTHING')
                AND spp.pangkat_id >= 19
                and spp.pangkat_id <= 30
                  ${satuanFilterQuery}
              group by pangkat.id, pangkat.nama_singkat
              order by pangkat.id ASC`),
        ),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForLanjutan(req: any) {
    try {
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = '';

      if (selectedUserRole.level === 'Level 3') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
      }

      const [queryResult] = await this.prisma.$transaction([
        this.prisma.$queryRaw<{}>(
          Prisma.raw(`
              select pangkat.id, pangkat.nama_singkat, count(distinct spp.personel_id)
              from mv_latest_pangkat_personel spp
                       inner join pangkat on pangkat.id = spp.pangkat_id
                       inner join personel p on p.id = spp.personel_id
                       inner join status_aktif sa on p.status_aktif_id = sa.id
              where sa.status_pilihan in ('AKTIF','NOTHING')
                AND spp.pangkat_id >= 1
                and spp.pangkat_id <= 9
                  ${satuanFilterQuery}
              group by pangkat.id, pangkat.nama_singkat
              order by pangkat.id ASC`),
        ),
      ]);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForSatkerPolda(req: any, mainSatuanId: number) {
    try {
      let result = await this.prisma.$queryRaw<{}>(Prisma.sql`
        select msp.provinsi as provinsi, pk.id as kategori_id ,
        count (distinct p.id) as count
        from personel p
        left join mv_latest_jabatan_personel jp on jp.personel_id = p.id
        left join jabatan j on j.id = jp.jabatan_id
        left join satuan s on s.id = j.satuan_id
        left join mv_satuan_provinsi msp on s.id = msp.satuan_id
        left join pangkat_personel pp on pp.personel_id = p.id
        left join pangkat p2 on pp.pangkat_id = p2.id
        left join pangkat_kategori pk on pk.id = p2.kategori_id
        left join status_aktif sa  on p.status_aktif_id = sa.id
        where sa.status_pilihan in ('AKTIF', 'NOTHING') and nama_atasan_polda in (
          select satuan.nama from satuan where satuan.id = ${mainSatuanId}
        )
        group by 1, pk.id
        order by 1 asc

      `);

      // let result = await this.prisma.mv_statistik_satker.findMany({
      //   where: { second_top_parent_id: mainSatuanId },
      // });

      let arrayResult = result as any[];

      let p3kQuery = `select satuan.id, count(*) total
                      from p3k
                      inner join mv_satuan_with_top_parents mv on mv.id = p3k.satuan_id
                      inner join satuan on satuan.id = mv.id
                      where satuan.id = ${mainSatuanId}
                      group by satuan.id`;
      const [p3kdata] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(p3kQuery),
      ]);

      let queryResult = {
        pns_count: arrayResult.find((o: any) => o.kategori_id == 1)?.count
          ? arrayResult.find((o: any) => o.kategori_id == 1)?.count
          : 0,
        polri_count: arrayResult.find((o: any) => o.kategori_id == 2)?.count
          ? arrayResult.find((o: any) => o.kategori_id == 2)?.count
          : 0,
        p3k_count:
          (p3kdata as any[]).find((p) => p.id == mainSatuanId)?.total ?? 0,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        ...queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikForSatker(req: any, mainSatuanId: number) {
    try {
      // let result = await this.prisma.$queryRaw<{}>(Prisma.sql`
      //   select kategori_id , count(distinct ssp.personel_id)
      //   from sipp_pangkat_personel spp
      //   inner join mv_latest_jabatan_personel ssp on ssp.personel_id = spp.personel_id
      //   inner join mv_satuan_with_top_parents swtp on swtp.id = ssp.satuan_id
      //   inner join personel p on spp.personel_id = p.id
      //   inner join status_aktif sa on p.status_aktif_id = sa.id
      //   where swtp.second_top_parent_id = ${mainSatuanId} AND sa.status_pilihan = 'AKTIF'
      //   group by kategori_id

      // `);

      let result = await this.prisma.$queryRaw<{}>(Prisma.sql`
                select s.leaf_1 as satuan,
                pk.id as kategori_id,
                count (distinct jp.personel_id) as count
                from personel p
                left join mv_latest_jabatan_personel jp on jp.personel_id = p.id
                left join jabatan j on j.id = jp.jabatan_id
                left join mv_detail_satuan_hierarki s on s.id = j.satuan_id
                left join mv_latest_pangkat_personel pp on pp.personel_id = p.id
                left join pangkat p2 on pp.pangkat_id = p2.id
                left join pangkat_kategori pk on pk.id = p2.kategori_id
                left join status_aktif sa  on p.status_aktif_id = sa.id
                where sa.status_pilihan in ('AKTIF', 'NOTHING')
                and s.leaf_1 not like '%POLDA%'
                and s.id != 1 and leaf_1 in (
          select satuan.nama from satuan where satuan.id = ${mainSatuanId}
        )
        group by 1,2
        order by 1 asc;
      `);

      let arrayResult = result as any[];

      let p3kQuery = `select satuan.id, count(*) total
                      from p3k
                      inner join mv_satuan_with_top_parents mv on mv.id = p3k.satuan_id
                      inner join satuan on satuan.id = mv.id
                      where satuan.id = ${mainSatuanId}
                      group by satuan.id`;
      const [p3kdata] = await this.prisma.$transaction([
        this.prisma.$queryRawUnsafe<{}>(p3kQuery),
      ]);

      let queryResult = {
        pns_count: arrayResult.find((o: any) => o.kategori_id == 1)?.count
          ? arrayResult.find((o: any) => o.kategori_id == 1)?.count
          : 0,
        polri_count: arrayResult.find((o: any) => o.kategori_id == 2)?.count
          ? arrayResult.find((o: any) => o.kategori_id == 2)?.count
          : 0,
        p3k_count:
          (p3kdata as any[]).find((p) => p.id == mainSatuanId)?.total ?? 0,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        ...queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikOverviewForDaerah(req: any) {
    try {
      let queryResult = await this.prisma.$queryRaw<{}>(Prisma.sql`
          select sp.nama_atasan_polda, count(distinct jp.personel_id)
          from satuan
                   inner join satuan_provinsi sp on sp.satuan_id = satuan.id
                   inner join jabatan on jabatan.satuan_id = sp.satuan_id
                   inner join jabatan_personel jp on jp.jabatan_id = jabatan.id
          group by sp.nama_atasan_polda
      `);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikBawahanForSatuan(
    req,
    paginationData,
    searchandsortData,
    satuan_id: number,
  ) {
    try {
      const limit = parseInt(paginationData.limit) || 100;
      const page = parseInt(paginationData.page) || 1;

      let selectedUserRole = req['selected_user_role'];
      let extraQueryResult: any = {};

      if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let swtp = await this.prisma.mv_satuan_with_top_parents.findUnique({
          where: {
            id: satuanTerakhir.satuan_id,
          },
        });

        satuan_id = parseInt(swtp.second_top_parent_id + '');
        extraQueryResult.second_top_parent_nama = swtp.second_top_parent_nama;
      }

      let result = await this.prisma.$queryRaw<{}>(Prisma.sql`
          select swtp.third_top_parent_id, kategori_id, satuan.nama, count(distinct sjstp.personel_id)

          from mv_latest_jabatan_personel sjstp
                   inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id
                   inner join mv_pangkat_terakhir spp on spp.personel_id = sjstp.personel_id
                   inner join pangkat on pangkat.id = spp.pangkat_id
                   inner join personel on personel.id = sjstp.personel_id
                   inner join status_aktif sa on personel.status_aktif_id = sa.id
                   left join satuan on satuan.id = swtp.third_top_parent_id

          where swtp.second_top_parent_id = ${satuan_id}
            AND third_top_parent_id <> ${satuan_id}
            AND sa.status_pilihan = 'AKTIF'
          group by swtp.third_top_parent_id, satuan.nama, kategori_id, jenis_id
          ORDER BY jenis_id DESC

      `);

      let arrayResult = result as any[];

      let satuan = {};

      for (let ar of arrayResult) {
        if (!ar.nama) {
          satuan['Non Satker'] = true;
        } else {
          satuan[ar.nama] = true;
        }
      }

      let queryResult = [];

      for (let s of Object.keys(satuan)) {
        let data = arrayResult.filter((ar: any) => {
          return ar.nama === s;
        });
        if (data.length === 0) {
          continue;
        }
        let dataPolri = data.find((ar: any) => ar.kategori_id == 2);
        let dataPNS = data.find((ar: any) => ar.kategori_id == 1);

        queryResult.push({
          satuan: s,
          satuan_id: data[0].third_top_parent_id,
          count_polri: dataPolri?.count ? dataPolri.count : 0,
          count_pns: dataPNS?.count ? dataPNS.count : 0,
        });
      }

      if (satuan['Non Satker']) {
        let data = arrayResult.filter((ar: any) => {
          return ar.nama === null;
        });
        let dataPolri = data.find((ar: any) => ar.kategori_id == 2);
        let dataPNS = data.find((ar: any) => ar.kategori_id == 1);
        queryResult.push({
          satuan: 'Non Satker',
          count_polri: dataPolri?.count ? dataPolri.count : 0,
          count_pns: dataPNS?.count ? dataPNS.count : 0,
        });
      }

      let totalData = queryResult?.length;
      let totalPage;

      if (limit !== 9999999999) {
        totalPage = Math.ceil(queryResult?.length / limit);
        queryResult = queryResult.slice(limit * (page - 1), limit * page);
      } else {
        totalPage = 1;
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        //data: result,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        data: queryResult,
        extraData: extraQueryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getPersonelBawahanForSatuan(
    req,
    paginationData,
    searchandsortData,
    satuan_id: number,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {};

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    /*     try {
      let result = await this.prisma.personel.findMany({
        where: {
          mv_latest_jabatan_personel: {
            some: {
              satuan_dibawah_id: satuan_id
            }
          },
        },
        // take: limit,
        // skip: limit * (page - 1),
        // orderBy,
      });
      return result
    } catch (err) {
      throw err;
    } */

    let [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: {
          mv_latest_jabatan_personel: {
            // satuan_dibawah_id: satuan_id,
            satuan: {
              mv_satuan_with_top_parents_self: {
                third_top_parent_id: satuan_id,
              },
            },
          },
          mv_pangkat_terakhir: { isNot: null },
          tanggal_lahir: {
            gte: moment(new Date())
              .subtract(58, 'year')
              .subtract(1, 'month')
              .endOf('month')
              .toDate(),
          },
        },
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          mv_pangkat_terakhir: {
            select: {
              mv_pangkat_terakhir_pangkat_sekarang: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                  tingkat_id: true,
                },
              },
            },
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                  nivellering_id: true,
                  nivellering: {
                    select: {
                      nama: true,
                    },
                  },
                },
              },
              tmt_jabatan: true,
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: [
          {
            mv_pangkat_terakhir: {
              mv_pangkat_terakhir_pangkat_sekarang: { id: 'desc' },
            },
          },
          {
            mv_latest_jabatan_personel: {
              jabatan_sekarang: { nivellering_id: 'asc' },
            },
          },
          { tanggal_lahir: 'asc' },
        ],
        where: {
          mv_latest_jabatan_personel: {
            // satuan_dibawah_id: satuan_id,
            satuan: {
              mv_satuan_with_top_parents_self: {
                third_top_parent_id: satuan_id,
              },
            },
          },
          mv_pangkat_terakhir: { isNot: null },
          tanggal_lahir: {
            gte: moment(new Date())
              .subtract(58, 'year')
              .subtract(1, 'month')
              .endOf('month')
              .toDate(),
          },
        },
      }),
    ]);

    // //ORDER BY
    // personels.sort((a,b)=>{

    //   const getPangkatTingkatId = (personel: any) => personel.pangkat_personel[0]?.pangkat?.tingkat_id || null;
    //   const getJabatansNivelleringId = (personel: any) => personel.jabatan_personel[0]?.jabatans?.nivellering_id || null;
    //   const getBirthDate = (personel: any) => personel.tanggal_lahir || null;

    //   const pangkatA = getPangkatTingkatId(a);
    //   const pangkatB = getPangkatTingkatId(b);
    //   const nivelA = getJabatansNivelleringId(a);
    //   const nivelB = getJabatansNivelleringId(b);
    //   const birthA = getBirthDate(a);
    //   const birthB = getBirthDate(b);

    //   const pangkatOrd =
    //         pangkatA === null && pangkatB === null ? 0
    //       : pangkatA === null ? 1 : pangkatB === null ? -1
    //       : parseInt(pangkatA + '') - parseInt(pangkatB + '');

    //   const nivelOrd =
    //         nivelA === null && nivelB === null ? 0
    //         : nivelA === null ? 1 : nivelB === null ? -1
    //         : parseInt(nivelA + '') - parseInt(nivelB + '');

    //   const birthOrd =
    //         birthA === null && birthB === null ? 0
    //         : birthA === null ? 1 : birthB === null ? -1
    //         : +new Date(birthA) - +new Date(birthB);

    //   return pangkatOrd || nivelOrd || birthOrd;
    // })

    // //LIMIT, PAGINATION
    // personels = personels.slice(limit * (page - 1), limit*page)

    const personelIds = personels.map((p) => p.id);

    //Get gelar
    let gelars: any[] =
      personelIds.length === 0
        ? []
        : await this.prisma.$queryRawUnsafe(`
                  SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'diktuk_personel' AS source_table
                  FROM diktuk_personel
                           JOIN gelar ON diktuk_personel.gelar_id = gelar.id
                  WHERE diktuk_personel.personel_id IN (${personelIds.join(',')})

                  UNION

                  SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'dikum_personel' AS source_table
                  FROM dikum_personel
                           JOIN dikum_detail ON dikum_detail.id = dikum_personel.dikum_detail_id
                           JOIN gelar ON dikum_detail.gelar_id = gelar.id
                  WHERE dikum_personel.personel_id IN (${personelIds.join(',')})

                  UNION

                  SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'dikbangum_personel' AS source_table
                  FROM dikbangum_personel
                           JOIN gelar ON dikbangum_personel.gelar_id = gelar.id
                  WHERE dikbangum_personel.personel_id IN (${personelIds.join(',')});

        `);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = [];

    for (let personel of personels) {
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      );

      const result = {
        ...personel,
        id: Number(personel.id),
        tanggal_lahir: new Date(personel.tanggal_lahir)
          .toISOString()
          .split('T')[0], // YYYY-MM-DD
        pangkat:
          personel.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang ??
          null,
        jabatan: personel.jabatan_personel?.[0]?.jabatans
          ? {
            ...personel.jabatan_personel?.[0]?.jabatans,
            lama_jabatan: this.getAge(
              personel.jabatan_personel?.[0]?.tmt_jabatan,
              personel.jabatan_personel?.[1]?.tmt_jabatan ?? new Date(),
            ),
          }
          : null,
        satuan: personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
        foto_file: fotoFile,
        gelar: gelars.filter((j) => j.personel_id === personel.id),
      };

      // Delete nested properties
      delete result.mv_pangkat_terakhir;
      delete result.jabatan_personel;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      queryResult.push(result);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async analyticsPersentasePersonel(req: any) {
    const ppJabatan = await this.getPersentasePersonelStrukturalFungsional(req);
    const ppPenugasan = await this.getPersentasePersonelPenugasan(req);

    const queryResult = {
      persentase_jabatan: ppJabatan,
      persentase_penugasan: ppPenugasan,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getPersentasePersonelStrukturalFungsional(req: any) {
    try {
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = 'WHERE true';

      if (selectedUserRole.level === 'Level 3') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });
        satuanFilterQuery = `WHERE p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });
        satuanFilterQuery = `WHERE p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
      }

      let result = await this.prisma.$queryRawUnsafe<{}>(`
          SELECT count(*)                               personels,
                 count(jabatan.kategori_id = 1 OR NULL) struktural,
                 count(jabatan.kategori_id = 2 OR NULL) fungsional_umum,
                 count(jabatan.kategori_id = 3 OR NULL) fungsional_tertentu

          FROM mv_latest_jabatan_personel s
                   inner JOIN jabatan ON jabatan.id = s.jabatan_id
                   inner JOIN jabatan_kategori ON jabatan_kategori.id = jabatan.kategori_id
                   inner JOIN personel p ON s.personel_id = p.id
              ${satuanFilterQuery}
      `);

      return {
        personels: result[0].personels,
        struktural: result[0].struktural,
        fungsional: result[0].fungsional_umum + result[0].fungsional_tertentu,
      };
    } catch (err) {
      throw err;
    }
  }

  async getPersentasePersonelPenugasan(req: any) {
    try {
      //83  = INDONESIA
      let selectedUserRole = req['selected_user_role'];
      let satuanFilterQuery = '';

      if (selectedUserRole.level === 'Level 3') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
      } else if (selectedUserRole.level === 'Level 2') {
        let satuanTerakhir =
          await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
            where: {
              personel_id: req['user']['personel_id'],
            },
          });

        let satuanAtas =
          await this.prisma.mv_satuan_with_top_parents.findUnique({
            where: {
              id: satuanTerakhir.satuan_id,
            },
          });

        satuanFilterQuery = `AND p.id IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
      }

      let result = await this.prisma.$queryRaw<{}>(
        Prisma.raw(`
            SELECT count(*)                      personels,
                   count(negara_id = 83 OR NULL) dalam
            FROM penugasan_personel s
                     inner JOIN personel p ON s.personel_id = p.id
            WHERE (now() between tmt_mulai and tmt_selesai)
               OR (tmt_selesai IS NULL)
                ${satuanFilterQuery}
        `),
      );

      return {
        personels: result[0].personels,
        dalam: result[0].dalam,
        luar: result[0].personels - result[0].dalam,
      };
    } catch (err) {
      throw err;
    }
  }

  async getSearch(req, paginationData, searchandsortData) {
    let selectedUserRole = req['selected_user_role'];

    //SEMENTARA
    let satuanFilterQuery = '';

    if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanFilterQuery = `AND ("public"."personel"."id") IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.third_top_parent_id  = ${satuanAtas.third_top_parent_id}
      )`;
    } else if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanFilterQuery = `AND ("public"."personel"."id") IN (
        select personel_id
        from mv_latest_jabatan_personel sjstp
        inner join mv_satuan_with_top_parents swtp on swtp.id = sjstp.satuan_id 
        where swtp.second_top_parent_id  = ${satuanAtas.second_top_parent_id}
      )`;
    }

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let isStatusAktifSearched =
      search_text[search_column.indexOf('status_kedinasan')] === 'AKTIF';
    let additionalStatusAktifQuery = '';
    if (isStatusAktifSearched) {
      additionalStatusAktifQuery = ` AND tanggal_lahir > '${moment(new Date()).subtract(58, 'year').subtract(1, 'month').endOf('month').format('YYYY-MM-DD')}'`;
    }

    const WHERE = `
        SELECT personel.*

        FROM personel
                 INNER JOIN mv_pangkat_terakhir ON mv_pangkat_terakhir.personel_id = personel.id


        WHERE true
            ${search_text
        ? `
    ${search_text[search_column.indexOf('nrp')] ? `AND nrp LIKE '%${search_text[search_column.indexOf('nrp')]}%'` : ''}

    ${search_text[search_column.indexOf('nama')] ? `AND LOWER(nama_lengkap)  LIKE LOWER('%${search_text[search_column.indexOf('nama')].toUpperCase()}%')` : ''}
    ${search_text[search_column.indexOf('golongan_darah')] ? `AND golongan_darah  = '${search_text[search_column.indexOf('golongan_darah')]}'` : ''}
    ${search_text[search_column.indexOf('jenis_kelamin')] ? `AND jenis_kelamin  = '${search_text[search_column.indexOf('jenis_kelamin')]}'` : ''}

    ${search_text[search_column.indexOf('agama')] ? `AND agama_id = (SELECT id from agama WHERE nama  = '${search_text[search_column.indexOf('agama')]}' )` : ''}
    ${search_text[search_column.indexOf('suku')] ? `AND suku_id = (SELECT id from suku WHERE nama  = '${search_text[search_column.indexOf('suku')]}' )` : ''}
    ${search_text[search_column.indexOf('status_kedinasan')] ? `AND status_aktif_id = (SELECT id from status_aktif WHERE nama  = '${search_text[search_column.indexOf('status_kedinasan')]}' )` : ''}

    ${search_text[search_column.indexOf('bahasa')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."bahasa_personel" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."bahasa_personel" AS "t1" INNER JOIN "public"."bahasa" AS "j1" ON ("j1"."id") = ("t1"."bahasa_id") WHERE ("j1"."nama"  = '${search_text[search_column.indexOf('bahasa')]}' AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}
    
    ${search_text[search_column.indexOf('penugasan_luar')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."penugasan_personel" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."penugasan_personel" AS "t1" INNER JOIN "public"."penugasan_instansi" AS "j1" ON ("j1"."id") = ("t1"."instansi_id") WHERE ("j1"."nama"  = '${search_text[search_column.indexOf('penugasan_luar')]}' AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}
    ${search_text[search_column.indexOf('riwayat_jabatan')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."jabatan_personel" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."jabatan_personel" AS "t1" INNER JOIN "public"."jabatan" AS "j1" ON ("j1"."id") = ("t1"."jabatan_id") WHERE ("j1"."nama"  = '${search_text[search_column.indexOf('riwayat_jabatan')]}' AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}
    ${search_text[search_column.indexOf('pendidikan_spesialis')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."dikbangspes_personel" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."dikbangspes_personel" AS "t1" INNER JOIN "public"."dikbangspes" AS "j1" ON ("j1"."id") = ("t1"."dikbangspes_id") WHERE ("j1"."nama"  = '${search_text[search_column.indexOf('pendidikan_spesialis')]}' AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}
    ${search_text[search_column.indexOf('pendidikan_kepolisian')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."diktuk_personel" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."diktuk_personel" AS "t1" INNER JOIN "public"."diktuk" AS "j1" ON ("j1"."id") = ("t1"."diktuk_id") WHERE ("j1"."nama"  = '${search_text[search_column.indexOf('pendidikan_kepolisian')]}' AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}

    ${search_text[search_column.indexOf('pangkat')] ? `AND ("public"."personel"."id") IN (SELECT "t0"."id" FROM "public"."personel" AS "t0" INNER JOIN "public"."mv_pangkat_terakhir" AS "j0" ON ("j0"."personel_id") = ("t0"."id") WHERE (("j0"."id") IN (SELECT "t1"."id" FROM "public"."mv_pangkat_terakhir" AS "t1" INNER JOIN "public"."pangkat" AS "j1" ON ("j1"."id") = ("t1"."pangkat_id") WHERE ("j1"."nama"  IN ( ${search_text[search_column.indexOf('pangkat')].replace(/\[|\]/g, '').replaceAll('"', "'")} )  AND "t1"."id" IS NOT NULL)) AND "t0"."id" IS NOT NULL))` : ''}
     
    ${search_text[search_column.indexOf('pendidikan_umum')] ||
          search_text[search_column.indexOf('pendidikan_umum_jurusan')]
          ? `AND ("public"."personel"."id") IN (
    SELECT personel.id
    FROM personel
    INNER JOIN dikum_personel ON dikum_personel.personel_id = personel.id
    INNER JOIN dikum ON dikum.id = dikum_personel.dikum_id
    LEFT JOIN jurusan ON jurusan.id = dikum_personel.jurusan_id
    WHERE true  
    ${search_text[search_column.indexOf('pendidikan_umum')] ? `AND  dikum.nama = '${search_text[search_column.indexOf('pendidikan_umum')]}'` : ''}
    ${search_text[search_column.indexOf('pendidikan_umum_jurusan')] ? `AND jurusan.nama = '${search_text[search_column.indexOf('pendidikan_umum_jurusan')]}'` : ''}
    )
    `
          : ''
        }

    ${search_text[search_column.indexOf('nivellering')] ||
          search_text[search_column.indexOf('jabatan')] ||
          search_text[search_column.indexOf('lama_jabatan')] ||
          search_text[search_column.indexOf('lama_nivellering')]
          ? `
      AND ("public"."personel"."id") IN (
    select jabatan_personel.personel_id
    from(
    select personel_id,  max(tmt_jabatan) tmtmax
    from jabatan_personel
    group by personel_id
    )x,
    jabatan_personel
    LEFT join jabatan on jabatan.id = jabatan_personel.jabatan_id
	  LEFT join nivellering on nivellering.id = jabatan.nivellering_id
    where jabatan_personel.personel_id = x.personel_id AND jabatan_personel.tmt_jabatan = x.tmtmax
    ${search_text[search_column.indexOf('jabatan')] ? `AND jabatan.nama = '${search_text[search_column.indexOf('jabatan')]}'` : ''}
    ${search_text[search_column.indexOf('nivellering')] ? `AND nivellering.nama = '${search_text[search_column.indexOf('nivellering')]}'` : ''}
    ${search_text[search_column.indexOf('lama_jabatan')] ? `AND x.tmtmax + ${search_text[search_column.indexOf('lama_jabatan')]} < CURRENT_DATE` : ''}
    )
      `
          : ''
        }
    
    `
        : ''
      } ${satuanFilterQuery} ${additionalStatusAktifQuery}

        ORDER BY pangkat_id DESC, nrp ASC LIMIT ${limit}
        OFFSET ${limit * (page - 1)}
    `;

    //ORDER BY ${sort_column ? `personel.${sort_column}` : `pangkat.tingkat_id` } ${sort_desc ? `${sort_desc}` : `ASC` } LIMIT ${limit} OFFSET ${limit * (page - 1)}

    const countWHERE = WHERE.replace(
      `SELECT personel.*`,
      `SELECT COUNT(personel.*)`,
    ).replace(/ORDER BY .+/, '');

    let personels: any[] = await this.prisma.$queryRawUnsafe(WHERE);

    const personelIds = personels.map((p) => p.id);

    const countResult = await this.prisma.$queryRawUnsafe(countWHERE);

    const totalData = Number(countResult[0].count);

    const jabatans = await this.prisma.jabatan_personel.findMany({
      where: { personel_id: { in: personelIds } },
      include: {
        jabatans: {
          select: {
            nama: true,
            satuan: true,
            nivellering: { select: { nama: true } },
          },
        },
      },
      orderBy: { tmt_jabatan: 'desc' },
    });
    const pangkats = await this.prisma.pangkat_personel.findMany({
      where: { personel_id: { in: personelIds } },
      include: { pangkat: true },
    });

    // personels = personels.map(p=> new Object({...p,
    //   jabatan: jabatans.filter(j=> j.personel_id === p.id && j.jabatans).map((j,i,a)=> new Object({...j.jabatans, tmt_jabatan: j.tmt_jabatan, lama_jabatan:  getAge(j.tmt_jabatan, a[i-1]?.tmt_jabatan ?? new Date())  }) ),
    //   pangkat: pangkats.filter(j=> j.personel_id === p.id).map(j=> j.pangkat).sort((a,b)=>Number(a.tingkat_id) - Number(b.tingkat_id))
    // }))

    //Get gelar
    let gelars: any[] = [];

    if (personelIds.length > 0) {
      gelars = await this.prisma.$queryRawUnsafe(`
          SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'diktuk_personel' AS source_table
          FROM diktuk_personel
                   JOIN gelar ON diktuk_personel.gelar_id = gelar.id
          WHERE diktuk_personel.personel_id IN (${personelIds.join(',')})

          UNION

          SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'dikum_personel' AS source_table
          FROM dikum_personel
                   JOIN gelar ON dikum_personel.gelar_id = gelar.id
          WHERE dikum_personel.personel_id IN (${personelIds.join(',')})

          UNION


          SELECT personel_id, gelar.nama, gelar.is_gelar_belakang, 'dikbangum_personel' AS source_table
          FROM dikbangum_personel
                   JOIN gelar ON dikbangum_personel.gelar_id = gelar.id
          WHERE dikbangum_personel.personel_id IN (${personelIds.join(',')});

      `);
    }

    personels = await Promise.all(
      personels.map(async (personel) => {
        const result = {
          ...personel,
          foto_file: await this.minioService.checkFileExist(
            `${process.env.MINIO_BUCKET_NAME}`,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
          ),
          jabatan: jabatans
            .filter((j) => j.personel_id === personel.id && j.jabatans)
            .map(
              (j, i, a) =>
                new Object({
                  ...j.jabatans,
                  tmt_jabatan: j.tmt_jabatan,
                  lama_jabatan: getAge(
                    j.tmt_jabatan,
                    a[i - 1]?.tmt_jabatan ?? new Date(),
                  ),
                }),
            ),
          pangkat: pangkats
            .filter((j) => j.personel_id === personel.id)
            .map((j) => j.pangkat)
            .sort((a, b) => Number(b.id) - Number(a.id))?.[0],

          gelar: gelars.filter((j) => j.personel_id === personel.id),
        };

        // Delete nested properties
        delete result.pangkat_personel;
        delete result.jabatan_personel;
        if (result.jabatan) delete result.jabatan.satuan;

        return result;
      }),
    );

    const totalPage = Math.ceil(totalData / limit);

    return { totalPage, totalData, page, personels };
  }

  async getSearchPrisma(req, paginationData, searchandsortData) {
    let { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let selectedUserRole = req['selected_user_role'];

    const NOLOGMESSAGE = req['nologmessage'];
    const NOFOTOFILE = req['nofotofile'];

    let personalIDQuery = null;



    if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      personalIDQuery = {
        mv_latest_jabatan_personel: {
          satuan: {
            mv_satuan_with_top_parents_self: {
              third_top_parent_id: satuanAtas.third_top_parent_id,
              // every: {
              //   third_top_parent_id: satuanAtas.third_top_parent_id,
              // },
            },
          },
        },
      };



    } else if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      let isFungsi = selectedUserRole.isFungsi;
      let fungsi = await this.prisma.fungsi.findFirst({
        where :{
          // id : 19
          satuan_teratas_id : satuanAtas.second_top_parent_id
        }
      })


      personalIDQuery = {
        OR : [
          {
            mv_latest_jabatan_personel: {
              satuan: {
                mv_satuan_with_top_parents_self: {
                  // every: {
                  second_top_parent_id: satuanAtas.second_top_parent_id,
                  // },
                },
              },
            },
          }
        ]
      }

      if(isFungsi && fungsi){
        personalIDQuery.OR.push({
          mv_latest_jabatan_personel: {
            satuan: {
              fungsi_satuan: {
                some : {
                  fungsi_id : fungsi.id
                }
              }
            },
          },
        })
      }

      //   personelIds = await this.prisma.$queryRaw<[]>(Prisma.sql`
      //   select personel.id
      //   from mv_latest_jabatan_personel
      //   INNER join personel on personel.id = mv_latest_jabatan_personel.personel_id
      //   inner join mv_satuan_with_top_parents swtp on swtp.id = mv_latest_jabatan_personel.satuan_id
      //   where second_top_parent_id	 = ${satuanAtas.second_top_parent_id}
      // `)
    }

    search_text = search_text ?? { search_column: {} };

    if (search_text[search_column?.indexOf('satuanBasedOnUserLevel')]) {
      let idxsboul = search_column?.indexOf('satuanBasedOnUserLevel');

      if (selectedUserRole.level === 'Level 3')
        search_column[idxsboul] = `satuan`;
      else if (selectedUserRole.level === 'Level 2')
        search_column[idxsboul] = `satuan_level_3`;
      else search_column[idxsboul] = `satuan_level_2`;
    }

    let wAND = [
      // personelIds ? {id: {in: personelIds.map(p => p.id)}} : {},
      // in_jabatanterakhir_butnotin_jabatan.length !== 0 ? {NOT : { id: {in: in_jabatanterakhir_butnotin_jabatan.map((a:any)=> a.personel_id)} }} : {},
      personalIDQuery ? personalIDQuery : {},

      search_text[search_column?.indexOf('nama')]
        ? {
          AND: search_text[search_column?.indexOf('nama')].split(' ').map(
            (s) =>
              new Object({
                nama_lengkap: { contains: s, mode: 'insensitive' },
              }),
          ),
        }
        : {},

      {
        nrp: {
          contains: search_text[search_column?.indexOf('nrp')] ?? 'NULL',
          mode: 'insensitive',
        },
      },

      search_text[search_column?.indexOf('jenis_kelamin')]
        ? {
          jenis_kelamin: {
            in: JSON.parse(
              search_text[search_column?.indexOf('jenis_kelamin')],
            ),
          },
        }
        : {},

      search_text[search_column?.indexOf('golongan_darah')]
        ? {
          golongan_darah: {
            in: JSON.parse(
              search_text[search_column?.indexOf('golongan_darah')],
            ),
          },
        }
        : {},
      search_text[search_column?.indexOf('agama')]
        ? {
          agama: {
            nama: {
              in: JSON.parse(search_text[search_column?.indexOf('agama')]),
            },
          },
        }
        : {},
      search_text[search_column?.indexOf('suku')]
        ? {
          suku: {
            nama: {
              in: JSON.parse(search_text[search_column?.indexOf('suku')]),
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('status_kedinasan')] &&
        search_text[search_column?.indexOf('status_kedinasan')] === 'AKTIF'
        ? {
          AND: [
            {
              tanggal_lahir: {
                gte: moment(new Date())
                  .subtract(58, 'year')
                  .subtract(1, 'month')
                  .endOf('month')
                  .toDate(),
              },
            },
            {
              status_aktif: {
                status_pilihan:
                  search_text[search_column?.indexOf('status_kedinasan')],
              },
            },
          ],
        }
        : {},
      search_text[search_column?.indexOf('status_kedinasan')] &&
        search_text[search_column?.indexOf('status_kedinasan')] === 'TIDAK AKTIF'
        ? {
          OR: [
            {
              tanggal_lahir: {
                lte: moment(new Date())
                  .subtract(58, 'year')
                  .subtract(1, 'month')
                  .endOf('month')
                  .toDate(),
              },
            },
            {
              status_aktif: {
                status_pilihan:
                  search_text[search_column?.indexOf('status_kedinasan')],
              },
            },
          ],
        }
        : {},

      {},
      search_text[search_column?.indexOf('bahasa')]
        ? {
          bahasa_personel: {
            some: {
              bahasa: {
                nama: {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('bahasa')],
                  ),
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('pendidikan_spesialis')]
        ? {
          dikbangspes_personel: {
            some: {
              dikbangspes: {
                nama: search_text[
                  search_column?.indexOf('pendidikan_spesialis')
                ],
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('pendidikan_umum')]
        ? {
          dikum_personel: {
            some: {
              dikum_detail: {
                dikum: {
                  nama: {
                    in: JSON.parse(
                      search_text[search_column?.indexOf('pendidikan_umum')],
                    ),
                  },
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('pendidikan_umum_jurusan')]
        ? {
          dikum_personel: {
            some: {
              dikum_detail: {
                jurusan: {
                  OR: JSON.parse(
                    search_text[
                    search_column?.indexOf('pendidikan_umum_jurusan')
                    ],
                  ).map(
                    (j) =>
                      new Object({
                        nama: {
                          contains: j,
                          mode: 'insensitive',
                        },
                      }),
                  ),
                },
              }
            },
          },
        }
        : {},
      // {
      //   penugasan_personel: {
      //     some: {
      //       penugasan_instansi: {
      //         nama: search_text[search_column?.indexOf('penugasan_luar')],
      //       },
      //     },
      //   },
      // },

      search_text[search_column?.indexOf('penugasan_luar')]
        ? {
          penugasan_personel: {
            some: {
              penugasan_instansi: {
                nama: {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('penugasan_luar')],
                  ),
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('pangkat')] ||
        search_text[search_column?.indexOf('pangkat_kategori_id')]
        ? {
          mv_pangkat_terakhir: {
            mv_pangkat_terakhir_pangkat_sekarang: {
              nama_singkat: search_text[search_column?.indexOf('pangkat')]
                ? {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('pangkat')],
                  ),
                }
                : undefined,
              kategori_id:
                search_text[search_column?.indexOf('pangkat_kategori_id')] ??
                undefined,
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('riwayat_jabatan')]
        ? {
          AND: JSON.parse(
            search_text[search_column?.indexOf('riwayat_jabatan')],
          ).map(
            (a) =>
              new Object({
                jabatan_personel: {
                  [a.sudah ? 'some' : 'none']: {
                    AND: a.jabatan.split(' ').map(
                      (s) =>
                        new Object({
                          jabatans: {
                            nama: {
                              contains: s, mode: 'insensitive'
                            }
                          },
                        }),
                    ),
                  },
                },
              }),
          ),
        }
        : {},

      search_text[search_column?.indexOf('pendidikan_kepolisian')]
        ? {
          OR: [
            ...JSON.parse(
              search_text[search_column?.indexOf('pendidikan_kepolisian')],
            )
              .filter((a) => !a.dan)
              .map((a) => [
                {
                  dikbangum_personel: {
                    [a.sudah ? 'some' : 'none']: {
                      dikbangum: {
                        nama_alternatif: {
                          contains: a.diktuk,
                          mode: 'insensitive',
                        },
                      },
                      ...(a.tahun
                        ? {
                          tanggal_masuk: {
                            gte: new Date(a.tahun, 0, 1),
                            lte: new Date(a.tahun, 11, 31, 23, 59, 59, 999),
                          },
                        }
                        : {}),
                    },
                  },
                },
                {
                  diktuk_personel: {
                    [a.sudah ? 'some' : 'none']: {
                      diktuk: {
                        diktuk_kategori: {
                          nama: {
                            contains: a.diktuk,
                            mode: 'insensitive',
                          },
                        },
                      },
                      ...(a.tahun
                        ? {
                          tanggal_masuk: {
                            gte: new Date(a.tahun, 0, 1),
                            lte: new Date(a.tahun, 11, 31, 23, 59, 59, 999),
                          },
                        }
                        : {}),
                    },
                  },
                },
                {
                  diktuk_personel: {
                    [a.sudah ? 'some' : 'none']: {
                      diktuk: {
                        nama: {
                          contains: a.diktuk,
                          mode: 'insensitive',
                        },
                      },
                      ...(a.tahun
                        ? {
                          tanggal_masuk: {
                            gte: new Date(a.tahun, 0, 1),
                            lte: new Date(a.tahun, 11, 31, 23, 59, 59, 999),
                          },
                        }
                        : {}),
                    },
                  },
                },
              ])
              .flat(2),
            {
              AND: [
                ...JSON.parse(
                  search_text[
                  search_column?.indexOf('pendidikan_kepolisian')
                  ],
                )
                  .filter((a) => a.dan)
                  .map(
                    (a) =>
                      new Object({
                        OR: [
                          {
                            dikbangum_personel: {
                              [a.sudah ? 'some' : 'none']: {
                                dikbangum: {
                                  nama_alternatif: {
                                    contains: a.diktuk,
                                    mode: 'insensitive',
                                  },
                                },
                                ...(a.tahun
                                  ? {
                                    tanggal_masuk: {
                                      gte: new Date(a.tahun, 0, 1),
                                      lte: new Date(
                                        a.tahun,
                                        11,
                                        31,
                                        23,
                                        59,
                                        59,
                                        999,
                                      ),
                                    },
                                  }
                                  : {}),
                              },
                            },
                          },
                          {
                            diktuk_personel: {
                              [a.sudah ? 'some' : 'none']: {
                                diktuk: {
                                  diktuk_kategori: {
                                    nama: {
                                      contains: a.diktuk,
                                      mode: 'insensitive',
                                    },
                                  },
                                },
                                ...(a.tahun
                                  ? {
                                    tanggal_masuk: {
                                      gte: new Date(a.tahun, 0, 1),
                                      lte: new Date(
                                        a.tahun,
                                        11,
                                        31,
                                        23,
                                        59,
                                        59,
                                        999,
                                      ),
                                    },
                                  }
                                  : {}),
                              },
                            },
                          },
                          {
                            diktuk_personel: {
                              [a.sudah ? 'some' : 'none']: {
                                diktuk: {
                                  nama: {
                                    contains: a.diktuk,
                                    mode: 'insensitive',
                                  },
                                },
                                ...(a.tahun
                                  ? {
                                    tanggal_masuk: {
                                      gte: new Date(a.tahun, 0, 1),
                                      lte: new Date(
                                        a.tahun,
                                        11,
                                        31,
                                        23,
                                        59,
                                        59,
                                        999,
                                      ),
                                    },
                                  }
                                  : {}),
                              },
                            },
                          },
                        ],
                      }),
                  ),
              ],
            },
          ],
        }
        : {},

      search_text[search_column?.indexOf('nivellering')]
        ? {
          mv_latest_jabatan_personel: {
            jabatan_sekarang: {
              nivellering: {
                nama: {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('nivellering')],
                  ),
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('lama_nivellering')]
        ? search_text[search_column?.indexOf('lama_nivellering_less')] === '<'
          ? {
            mv_nivellering_terakhir: {
              some: {
                duration_day: {
                  lte: parseInt(
                    search_text[search_column?.indexOf('lama_nivellering')],
                  ),
                },
              }
            },
          }
          : {
            mv_nivellering_terakhir: {
              some: {
                duration_day: {
                  gte: parseInt(
                    search_text[search_column?.indexOf('lama_nivellering')],
                  ),
                },
              }
            },
          }
        : {},

      search_text[search_column?.indexOf('lama_jabatan')]
        ? search_text[search_column?.indexOf('lama_jabatan_less')] === '<'
          ? {
            mv_latest_jabatan_personel: {
              tmt_jabatan: {
                gte: new Date(
                  new Date().setDate(
                    new Date().getDate() -
                    parseInt(
                      search_text[search_column?.indexOf('lama_jabatan')],
                    ),
                  ),
                ),
              },
            },
          }
          : {
            mv_latest_jabatan_personel: {
              tmt_jabatan: {
                lte: new Date(
                  new Date().setDate(
                    new Date().getDate() -
                    parseInt(
                      search_text[search_column?.indexOf('lama_jabatan')],
                    ),
                  ),
                ),
              },
            },
          }
        : {},

      search_text[search_column?.indexOf('jabatan')]
        ? {
          mv_latest_jabatan_personel: {
            AND: search_text[search_column?.indexOf('jabatan')]
              .split(' ')
              .map(
                (s) =>
                  new Object({
                    jabatan: { contains: s, mode: 'insensitive' },
                  }),
              ),
          },
        }
        : {},

      search_text[search_column?.indexOf('mdp')]
        ? search_text[search_column?.indexOf('mdp_less')] === '<'
          ? {
            mv_mdp: {
              some: {
                lama_menjabat_hari: {
                  lte: parseInt(search_text[search_column?.indexOf('mdp')]),
                },
              }
            },
          }
          : {
            mv_mdp: {
              some: {
                lama_menjabat_hari: {
                  gte: parseInt(search_text[search_column?.indexOf('mdp')]),
                },
              }
            },
          }
        : {},

      search_text[search_column?.indexOf('mddp')]
        ? search_text[search_column?.indexOf('mddp_less')] === '<'
          ? {
            mv_mddp: {
              some: {
                lama_menjabat_hari: {
                  lte: parseInt(search_text[search_column?.indexOf('mddp')]),
                },
              }
            },
          }
          : {
            mv_mddp: {
              some: {
                lama_menjabat_hari: {
                  gte: parseInt(search_text[search_column?.indexOf('mddp')]),
                },
              }
            },
          }
        : {},

      search_text[search_column?.indexOf('satuan')]
        ? {
          mv_latest_jabatan_personel: {
            satuan: {
              nama: {
                in: JSON.parse(search_text[search_column?.indexOf('satuan')]),
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('satuan_level_2')]
        ? {
          mv_latest_jabatan_personel: {
            satuan: {
              mv_satuan_with_top_parents_self: {
                second_top_parent_nama: {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('satuan_level_2')],
                  ),
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('satuan_level_3')]
        ? {
          mv_latest_jabatan_personel: {
            satuan: {
              mv_satuan_with_top_parents_self: {
                third_top_parent_nama: {
                  in: JSON.parse(
                    search_text[search_column?.indexOf('satuan_level_3')],
                  ),
                },
              },
            },
          },
        }
        : {},

      search_text[search_column?.indexOf('status_kawin')]
        ? {
          status_kawin: {
            nama: {
              in: JSON.parse(
                search_text[search_column?.indexOf('status_kawin')],
              ),
            },
          },
        }
        : {},

      //Filter out personel with no pangkat
      { mv_pangkat_terakhir: { isNot: null } },
    ];

    wAND = wAND.filter(
      (ww) =>
        !(
          JSON.stringify(ww).includes('{}') ||
          JSON.stringify(ww).includes('NULL')
        ),
    );

    //Handle aktif. Don't show if already pensiun by formula
    let isStatusAktifSearched = wAND.find(
      (obj) => obj.status_aktif && obj.status_aktif?.nama === 'AKTIF',
    );
    if (isStatusAktifSearched) {
      wAND.push({
        tanggal_lahir: {
          gte: moment(new Date())
            .subtract(58, 'year')
            .subtract(1, 'month')
            .endOf('month')
            .toDate(),
        },
      });
    }

    // let testResult = await this.prisma.personel.count({ where: {
    //   mv_mddp : {
    //     some : {
    //       duration_day : {
    //         gte : 100
    //       }
    //     }
    //   }
    // } })

    // let testResult = await this.prisma.mv_nivellering_terakhir.findMany({ where: {
    //     personel_id : 68,
    //     duration_day: 3511
    // } })


    //

    let [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.personel.count({ where: { AND: [].concat(wAND) } }),

      // this.prisma.mv_latest_jabatan_personel.findMany({
      //   where : {
      //     jabatan_sekarang : {
      //       is : {
      //         nivellering : {
      //           is : {
      //             id : 1
      //           }
      //         }
      //         // nivellering: {
      //         //   nama: search_column?.indexOf('nivellering'),
      //         // },
      //       }
      //     }
      //   }
      // }),

      this.prisma.personel.findMany({
        where: {
          AND: [].concat(wAND),
        },
        // where : {
        //
        // },
        include: {
          jabatan_personel: {
            select: {
              jabatans: { include: { nivellering: true } },
              personel_id: true,
              tmt_jabatan: true,
              is_ps: true,
              keterangan: true,
            },
          },
          mv_pangkat_terakhir: {
            select: {
              mv_pangkat_terakhir_pangkat_sekarang: true,
              personel_id: true,
            },
          },
          dikum_personel: {
            select: {
              dikum_detail: {
                select: {
                  gelar: true,
                },
              },
              tanggal_lulus: true,
            },
          },
          dikbangum_personel: {
            select: { gelar: true, tanggal_selesai: true },
          },
          diktuk_personel: { select: { gelar: true, tanggal_selesai: true } },
          // mv_latest_jabatan_personel : {
          //   select : {
          //     jabatan_sekarang : true
          //   }
          // }
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: [
          { mv_pangkat_terakhir: { pangkat_id: 'desc' } },
          { tanggal_lahir: 'asc' },
        ],
      }),
    ]);

    queryResult = await Promise.all(
      queryResult.map(async (personel) => {
        const result = {
          ...personel,
          foto_file: NOFOTOFILE
            ? undefined
            : await this.minioService.checkFileExist(
              `${process.env.MINIO_BUCKET_NAME}`,
              `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
            ),
          jabatan: personel.jabatan_personel
            .filter((j) => j.personel_id === personel.id && j.jabatans)
            .sort(
              (a, b) =>
                (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity) -
                (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity),
            )
            .map(
              (j, i, a) =>
                new Object({
                  ...j.jabatans,
                  tmt_jabatan: j.tmt_jabatan,
                  lama_jabatan: getAge(
                    j.tmt_jabatan,
                    a[i - 1]?.tmt_jabatan ?? new Date(),
                  ),
                  is_ps: j.is_ps,
                  keterangan: j.keterangan,
                }),
            ),
          pangkat:
            personel.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang,

          gelar: [
            personel.dikum_personel.map(
              (d) =>
                new Object({
                  ...d.dikum_detail?.gelar,
                  from: 'dikum',
                  tanggal_selesai: d.tanggal_lulus,
                }),
            ),
            personel.dikbangum_personel.map(
              (d) =>
                new Object({
                  ...d.gelar,
                  from: 'dikbangum',
                  tanggal_selesai: d.tanggal_selesai,
                }),
            ),
            personel.diktuk_personel.map(
              (d) =>
                new Object({
                  ...d.gelar,
                  from: 'diktuk',
                  tanggal_selesai: d.tanggal_selesai,
                }),
            ),
          ]
            .flat(2)
            .filter((g: any) => g.id),
        };

        // Delete nested properties
        delete result.mv_pangkat_terakhir;
        // delete result.jabatan_personel;
        delete result.dikbangum_personel;
        delete result.diktuk_personel;
        delete result.dikum_personel;
        // if (result.jabatan) delete result.jabatan.satuan;

        return result;
      }),
    );

    const totalPage = Math.ceil(totalData / limit);

    let message = ``;

    if (NOLOGMESSAGE) {
      message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async updatePersonel(req, id, body: UpdatePersonelDTO) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (
        !(await this.prisma.personel.findFirst({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Personel id ${id} not found!`);

      let personelF: CreatePersonelDTO = {
        nrp: body.nrp,
        nama_lengkap: body.nama_lengkap,
        tanggal_lahir: body.tanggal_lahir
          ? new Date(body.tanggal_lahir)
          : undefined,
        jenis_kelamin:
          jenis_kelamin_enum[<jenis_kelamin_enum>body.jenis_kelamin],
        foto_file: body.foto_file,
        tempat_lahir: body.tempat_lahir,
        agama_id: body.agama_id,
        ktp_nomor: body.ktp_nomor,
        ktp_file: body.ktp_file,
        kk_nomor: body.kk_nomor,
        kk_file: body.kk_file,
        status_kawin_id: body.status_kawin_id,
        golongan_darah: body.golongan_darah,
        suku_id: body.suku_id,
        email: body.email,
        no_hp: body.no_hp,
        akta_kelahiran_file: body.akta_kelahiran_file,
        asabri_nomor: body.asabri_nomor,
        asabri_file: body.asabri_file,
        bpjs_nomor: body.bpjs_nomor,
        bpjs_file: body.bpjs_file,
        paspor_nomor: body.paspor_nomor,
        paspor_file: body.paspor_file,
        npwp_nomor: body.npwp_nomor,
        npwp_file: body.npwp_file,
        lhkpn_file: body.lhkpn_file,
        masa_dinas_surut_tmt: body.masa_dinas_surut_tmt
          ? new Date(body.masa_dinas_surut_tmt)
          : undefined,
        masa_dinas_surut_file: body.masa_dinas_surut_file,
        anak_ke: body.anak_ke,
        jumlah_saudara: body.jumlah_saudara,
        status_aktif_id: body.status_aktif_id,
      };

      let otherF: any = {
        jenis_rambut: body.jenis_rambut,
        warna_rambut: body.warna_rambut,
        warna_kulit: body.warna_kulit,
        warna_mata: body.warna_mata,
        nama_ibu: body.nama_ibu,
        telepon_orang_tua: body.telepon_orang_tua,
        alamat: body.alamat,
        tinggi_badan: body.tinggi_badan,
        berat_badan: body.berat_badan,
        ukuran_topi: body.ukuran_topi,
        ukuran_celana: body.ukuran_celana,
        ukuran_baju: body.ukuran_baju,
        sidik_jari_1: body.sidik_jari_1,
        sidik_jari_2: body.sidik_jari_2,
        alamat_orang_tua: body.alamat_orang_tua,
      };

      let alamatF = {
        alamat: otherF.alamat,
      };

      let keluargaF = {
        nama_ibu: otherF.nama_ibu,
        telepon_orang_tua: otherF.telepon_orang_tua,
        alamat_orang_tua: otherF.alamat_orang_tua,
      };

      let fisikF = {
        jenis_rambut: otherF.jenis_rambut,
        warna_rambut: otherF.warna_rambut,
        warna_kulit: otherF.warna_kulit,
        warna_mata: otherF.warna_mata,
        tinggi_badan: otherF.tinggi_badan,
        berat_badan: otherF.berat_badan,
        sidik_jari_1: otherF.sidik_jari_1,
        sidik_jari_2: otherF.sidik_jari_2,
      };

      for (let o of Object.keys(personelF))
        if (personelF[o] == null) delete personelF[o];
      for (let o of Object.keys(alamatF))
        if (alamatF[o] == null) delete alamatF[o];
      for (let o of Object.keys(keluargaF))
        if (keluargaF[o] == null) delete keluargaF[o];
      for (let o of Object.keys(fisikF))
        if (fisikF[o] == null) delete fisikF[o];

      const queryResult = [];

      if (Object.keys(personelF).length > 0)
        queryResult.push(
          await this.prisma.personel.update({
            where: { id: +id },
            data: {
              ...personelF,

              updated_at: new Date(),
            },
          }),
        );

      if (Object.keys(alamatF).length > 0)
        queryResult.push(
          await this.prisma.alamat.updateMany({
            where: { personel_id: +id },
            data: {
              alamat: alamatF.alamat,

              updated_at: new Date(),
            },
          }),
        );

      if (Object.keys(keluargaF).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.updateMany({
            where: { personel_id: +id, hubungan_keluarga_id: 109 },
            data: {
              nama_keluarga: keluargaF.nama_ibu,
              no_hp: keluargaF.telepon_orang_tua,
              alamat: keluargaF.alamat_orang_tua,

              updated_at: new Date(),
            },
          }),
        );

      if (Object.keys(fisikF).length > 0)
        queryResult.push(
          await this.prisma.personel_fisik.updateMany({
            where: { personel_id: +id },
            data: {
              jenis_rambut: fisikF.jenis_rambut,
              warna_rambut: fisikF.warna_rambut,
              warna_kulit: fisikF.warna_kulit,
              warna_mata: fisikF.warna_mata,
              tinggi_badan: fisikF.tinggi_badan,
              berat_badan: fisikF.berat_badan,
              sidik_jari_1: fisikF.sidik_jari_1,
              sidik_jari_2: fisikF.sidik_jari_2,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async uploadFile(req: any, file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      // if (!uploaded.ETag) return { rawFile: file };

      //     return {
      //       rawFile: file,
      //       uploaded,
      //     };

      const queryResult = {
        key: uploaded.Key,
        url: uploaded.Location,
        filename: uploaded.filename,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: file,
      };
    } catch (err) {
      throw err;
    }
  }

  async uploadFileWithNRP(req: any, file: Express.Multer.File, nrp: string) {
    try {
      const queryResult = await this.minioService.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      // if (!uploaded.ETag) return { rawFile: file };

      //     return {
      //       rawFile: file,
      //       uploaded,
      //     };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult.filename,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult.filename,
        url: queryResult.Location,
      };
    } catch (err) {
      throw err;
    }
  }

  async getStatistikPPPK(
    req: any,
    searchandsortData: SearchAndSortDTO,
    paginationData: PaginationDto,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;
    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'date' | 'boolean';
      };
    } = {};
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    let selectedUserRole = req['selected_user_role'];

    let satuanLv;

    if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let swtp = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanLv = parseInt(swtp.second_top_parent_id + '');
    } else if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let swtp = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanLv = parseInt(swtp.third_top_parent_id + '');
    }

    let formattedWhere = {
      ...where,
      satuan_id: satuanLv,
    }

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.p3k.count({
        where: formattedWhere,
      }),
      this.prisma.p3k.findMany({
        where: formattedWhere,
        take: limit,
        skip: limit * (page - 1),
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async statisticKelengkapanData(req: any) {
    const queryResult = await this.prisma.mv_kelengkapan_data.findMany({});

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async statisticKelengkapanDataWithPagination(
    req: any,
    paginationData,
    searchandsortData,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;
    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint';
      };
    } = {};
    let { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    delete where.deleted_at;

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.mv_kelengkapan_data.count({
        where: where,
      }),
      this.prisma.mv_kelengkapan_data.findMany({
        // this.prisma.pengajuan_pengakhiran_dinas.findMany({
        //   // select: {
        //   //   id: true,
        //   //   nama: true,
        //   // },
        take: limit,
        skip: limit * (page - 1),
        orderBy: {
          persentase: 'desc',
        },
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async statisticKelengkapanDataDetail(
    req: any,
    searchandsortData: SearchAndSortDTO,
    paginationData: PaginationDto,
    satker: string,
    lengkap: boolean,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;
    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'date' | 'boolean';
      };
    } = {};
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    let formattedWhere;
    /* let formattedWhere = {
        catatan : {
          not: null
        },
        satker : {
          contains : satker
        }
      }*/

    if (lengkap) {
      formattedWhere = {
        catatan: null,
        satker: {
          contains: satker,
        },
      };
    } else {
      formattedWhere = {
        catatan: {
          not: null,
        },
        satker: {
          contains: satker,
        },
      };
    }

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.mv_kelengkapan_data_detail.count({
        where: formattedWhere,
      }),
      this.prisma.mv_kelengkapan_data_detail.findMany({
        where: formattedWhere,
        take: limit,
        skip: limit * (page - 1),
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async getUserByAccess(req, uid) {
    let selectedUserRole = req['selected_user_role'];

    //SEMENTARA
    let satuanFilterQuery = '';

    if (selectedUserRole.level === 'Level 4') {
      let targetSatuanTerakhir = await this.prisma.personel.findFirstOrThrow({
        where: {
          uid: uid,
          id: req['user']['personel_id'],
        },
      });
      return targetSatuanTerakhir;
    } else if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      let targetSatuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel: {
              uid: uid,
            },
            // satuan_id : satuanTerakhir.satuan_id
            satuan: {
              mv_satuan_with_top_parents_self: {
                third_top_parent_id: satuanAtas.third_top_parent_id,
              },
            },
          },
        });

      return targetSatuanTerakhir;
    } else if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let satuanAtas = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      let targetSatuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel: {
              uid: uid,
            },
            // satuan_id : satuanTerakhir.satuan_id
            satuan: {
              mv_satuan_with_top_parents_self: {
                second_top_parent_id: satuanAtas.second_top_parent_id,
              },
            },
          },
        });

      return targetSatuanTerakhir;
    }

    //Yang perlu di handle cuma level 2 ama 3, soalnya level 1 dan super admin dari awal sudah di filter role lain

    return true;
  }

  async getNeededUserAccess(uid) {

    let satuanTerakhirOfSearchedUser =
      await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
        where: {
          personel : {
            uid : uid
          }
        },
      });

    let satuanAtasOfSearchedUser = await this.prisma.mv_satuan_with_top_parents.findUnique({
      where: {
        id: satuanTerakhirOfSearchedUser.satuan_id,
      },
    });

    return satuanAtasOfSearchedUser

  }

  async getNeededUserAccessById(id) {

    let satuanTerakhirOfSearchedUser =
      await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
        where: {
          personel : {
            id : id
          }
        },
      });

    let satuanAtasOfSearchedUser = await this.prisma.mv_satuan_with_top_parents.findUnique({
      where: {
        id: satuanTerakhirOfSearchedUser.satuan_id,
      },
    });

    return satuanAtasOfSearchedUser

  }

  async getUserFungsiByUserUid(uid) {

    let satuanTerakhirOfSearchedUser =
      await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
        where: {
          personel : {
            uid : uid
          }
        },
      });

    let fungsiOfSearchedUser = await this.prisma.fungsi_satuan.findFirst({
      select : {
        fungsi : true
      },
      where: {
        satuan_id: satuanTerakhirOfSearchedUser.satuan_id
      },
    });

    return fungsiOfSearchedUser?.fungsi

  }
  async getUserFungsiByUserId(id) {

    let satuanTerakhirOfSearchedUser =
      await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
        where: {
          personel : {
            id : id
          }
        },
      });

    let fungsiOfSearchedUser = await this.prisma.fungsi_satuan.findUnique({
      where: {
        id: satuanTerakhirOfSearchedUser.satuan_id
      },
    });

    return fungsiOfSearchedUser

  }

  async getListJabatan(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const search_method = req.query.search_method;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      // is_aktif: { field: 'is_aktif', type: 'boolean' },
    };

    let selectedUserRole = req['selected_user_role'];
    let satuanLv2;
    let satuanLv3;

    let fungsiQuery:any = null

    if (selectedUserRole.level === 'Level 2') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let swtp = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      let isFungsi = selectedUserRole.isFungsi;
      let fungsi = await this.prisma.fungsi.findFirst({
        where :{
          // id : 19
          satuan_teratas_id : swtp.second_top_parent_id
        }
      })

      if(isFungsi && fungsi){
        fungsiQuery = {
          satuan: {
            fungsi_satuan: {
              some : {
                fungsi_id : fungsi.id
              }
            }
          },
        }
      }


      satuanLv2 = parseInt(swtp.second_top_parent_id + '');
    } else if (selectedUserRole.level === 'Level 3') {
      let satuanTerakhir =
        await this.prisma.mv_latest_jabatan_personel.findFirstOrThrow({
          where: {
            personel_id: req['user']['personel_id'],
          },
        });

      let swtp = await this.prisma.mv_satuan_with_top_parents.findUnique({
        where: {
          id: satuanTerakhir.satuan_id,
        },
      });

      satuanLv3 = parseInt(swtp.third_top_parent_id + '');
    }

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      null,
      search_method,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.jabatan.count({
        where: {
          AND : [
            where,
            satuanLv2
              ? {
                OR : [
                  {
                    satuan: {
                      mv_satuan_with_top_parents_self: {
                        second_top_parent_id: satuanLv2,
                      },
                    },
                  },
                  fungsiQuery ? fungsiQuery : {}
                ]
              }
              : satuanLv3
                ? {
                  satuan: {
                    mv_satuan_with_top_parents_self: {
                      third_top_parent_id: satuanLv3,
                    },
                  },
                }
                : {},
          ]
        },
      }),
      this.prisma.jabatan.findMany({
        select: {
          id: true,
          nama: true,
          dsp: true,
          nivellering: {
            select: {
              id: true,
              nama: true,
            },
          },
          satuan: {
            select: {
              id: true,
              nama: true,
            },
          },
          atasan_jabatan: {
            select: {
              id: true,
              nama: true,
            },
          },
          jabatan_kategori: {
            select: {
              id: true,
              nama: true,
            },
          },
          jabatan_pangkat: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                },
              },
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          nivellering: {
            id: 'asc'
          }
        },
        where: {
          AND : [
            where,
            satuanLv2
              ? {
                OR : [
                  {
                    satuan: {
                      mv_satuan_with_top_parents_self: {
                        second_top_parent_id: satuanLv2,
                      },
                    },
                  },
                  fungsiQuery ? fungsiQuery : {}
                ]
              }
              : satuanLv3
                ? {
                  satuan: {
                    mv_satuan_with_top_parents_self: {
                      third_top_parent_id: satuanLv3,
                    },
                  },
                }
                : {},
          ]
        },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const formattedJabatans = queryResult.map(
      ({ jabatan_pangkat, ...data }) => ({
        ...data,
        pangkat: jabatan_pangkat.map((jp) => jp.pangkat),
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListDikum(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const search_method = req.query.search_method;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      // is_aktif: { field: 'is_aktif', type: 'boolean' },
    };

    let selectedUserRole = req['selected_user_role'];
    let satuanLv2;
    let satuanLv3;

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      null,
      search_method,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.dikum.count({
        where: {
          ...where,
        }
      }),
      this.prisma.dikum.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async getListInstitusi(req, dikum_id, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const search_method = req.query.search_method;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'institusi.nama', type: 'string' },
      // is_aktif: { field: 'is_aktif', type: 'boolean' },
    };

    let selectedUserRole = req['selected_user_role'];
    let satuanLv2;
    let satuanLv3;

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      null,
      search_method,
    );

    const formattedWhere = {
      ...where,
      dikum_id: dikum_id
    }

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.dikum_detail.count({
        where: formattedWhere
      }),
      this.prisma.dikum_detail.findMany({
        select: {
          id: true,
          institusi: {
            select: {
              id: true,
              nama: true
            }
          }
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        distinct: ['institusi_id'],
        where: formattedWhere
      }),
    ]);

    let formattedResult = queryResult.filter(el => el.institusi != null).map(el => {
      return {
        id: el.institusi.id,
        nama: el.institusi.nama
      }
    })

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        formattedResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: formattedResult,
    };
  }

  async getListJurusan(req, dikum_id, institusi_id, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const search_method = req.query.search_method;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'institusi.nama', type: 'string' },
      // is_aktif: { field: 'is_aktif', type: 'boolean' },
    };

    let selectedUserRole = req['selected_user_role'];
    let satuanLv2;
    let satuanLv3;

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      null,
      search_method,
    );

    const formattedWhere = {
      ...where,
      dikum_id: dikum_id,
      institusi_id: institusi_id
    }

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.dikum_detail.count({
        where: formattedWhere
      }),
      this.prisma.dikum_detail.findMany({
        select: {
          id: true,
          jurusan: {
            select: {
              id: true,
              nama: true
            }
          }
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        distinct: ['jurusan_id'],
        where: formattedWhere
      }),
    ]);

    let formattedResult = queryResult.filter(el => el.jurusan != null).map(el => {
      return {
        id: el.jurusan.id,
        nama: el.jurusan.nama
      }
    })

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        formattedResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: formattedResult,
    };
  }

  async getListPelatihan(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let selectedUserRole = req['selected_user_role'];

    const queryResult = await this.prisma.pelatihan.findMany();

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  getAge(startDate, endDate) {
    let start = new Date(startDate);
    let end = new Date(endDate);

    // Calculate the differences in years, months, and days
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    // Adjust for negative month or day differences
    if (days < 0) {
      months--;
      let previousMonth = new Date(
        end.getFullYear(),
        end.getMonth(),
        0,
      ).getDate(); // Days in the previous month
      days += previousMonth;
    }
    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Tahun ${months} Bulan ${days} Hari`;
  }

  async getListPendidikanKepolisian(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' };
    } = {
      nama: { field: 'nama', type: 'string' },
      kategori: { field: 'diktuk_kategori.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [diktuk, dikbangum] = await this.prisma.$transaction([
      this.prisma.diktuk.findMany({
        select: {
          id: true,
          nama: true,
          created_at: true,
          diktuk_kategori: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        where: {
          nama: {
            contains: search,
            mode: 'insensitive',
          },
        },
        orderBy: {
          nama: 'asc',
        },
      }),
      this.prisma.dikbangum.findMany({
        select: {
          id: true,
          nama_alternatif: true,
          created_at: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: {
          nama: 'asc',
        },
        where: {
          nama_alternatif: {
            contains: search,
            mode: 'insensitive',
          },
        },
      }),
    ]);

    let allResult = [
      ...diktuk.map((d) => {
        return { nama: d.diktuk_kategori.nama };
      }),
      ...dikbangum.map((d) => {
        return { nama: d.nama_alternatif };
      }),
    ];

    let queryResult = lodash.uniqBy(allResult, (d) => d.nama);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListOlahraga(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let selectedUserRole = req['selected_user_role'];

    const queryResult = await this.prisma.olahraga.findMany();

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getListHubunganKeluarga(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let selectedUserRole = req['selected_user_role'];

    const queryResult = await this.prisma.hubungan_keluarga.findMany();

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getDropdownStatusAktif(req, paginationData, searchandsortData) {
    const [data] = await this.prisma.$transaction([
      this.prisma.status_aktif.findMany({
        select: {
          status_pilihan: true,
        },
        where: {
          NOT: {
            status_pilihan: 'NOTHING',
          },
        },
        orderBy: {
          nama: 'asc',
        },
      }),
    ]);

    let uniqBy = lodash.uniqBy(data, (d) => d.status_pilihan);

    const queryResult = uniqBy.map((obj) => {
      return { nama: obj.status_pilihan };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  // async exportPersonelWithColumns(req, paginationData, searchandsortData) {
  //   let selectedUserRole = req['selected_user_role'];
  //   let { sort_column, sort_desc, search_column, search_text, search } =
  //     searchandsortData;

  //   const limit = paginationData.limit || 100;
  //   const page = paginationData.page || 1;

  //   let personalIDQuery = null;

  //   let headers = JSON.parse(search_text[search_column.indexOf('headers')]);
  //   let chosen_column = JSON.parse(
  //     search_text[search_column.indexOf('chosen_column')],
  //   );
  //   let nrps = JSON.parse(search_text[search_column.indexOf('nrps')]);
  //   let order_by = JSON.parse(search_text[search_column.indexOf('order_by')]);

  //   //SPECIFY A COLUMN OR MULTIPLE COLUMN, DON'T SPECIFY [RELATION]:true (ex: {pangkat_personel:true} )
  //   let columns = {
  //     nama: { nama_lengkap: true },
  //     nrp: { nrp: true },
  //     ktp_nomor: { ktp_nomor: true },
  //     email: { email: true },
  //     darah: { golongan_darah: true },
  //     tempat_lahir: { tempat_lahir: true },
  //     tgl_lahir: { tanggal_lahir: true },
  //     telp_hp: { no_hp: true },
  //     npwp: { npwp_nomor: true },
  //     asabri: { asabri_nomor: true },
  //     tmt_masa_dinas_surut: { masa_dinas_surut_tmt: true },
  //     jenis_kelamin: { jenis_kelamin: true },
  //     nik: { ktp_nomor: true },

  //     agama: { agama: { select: { nama: true } } },
  //     suku_bangsa: { suku: { select: { nama: true } } },
  //     bahasa: { bahasa_personel: { select: { bahasa: true } } },

  //     alamat: { alamat: { select: { alamat: true } } },

  //     pangkat: {
  //       pangkat_terakhir: {
  //         select: { pangkat_sekarang: { select: { nama: true, nama_singkat:true } } },
  //       },
  //     },
  //     jabatan: {
  //       mv_latest_jabatan_personel: {
  //         select: { jabatan_sekarang: { select: { nama: true } } },
  //       },
  //     },
  //     nivelering: {
  //       nivellering_terakhir: {
  //         select: { nivellering: { select: { nama: true } } },
  //       },
  //     },

  //     diktuk: {
  //       diktuk_personel: { select: { diktuk: { select: { nama: true } } } },
  //     },

  //     status_assessment: { assesment_personel: { select: { nama: true } } },

  //     tahun_diktuk: { diktuk_personel: { select: { tanggal_masuk: true } } },

  //     tmt_jabatan: {
  //       mv_latest_jabatan_personel: { select: { tmt_jabatan: true } },
  //     },

  //     tmt_pangkat: { pangkat_terakhir: { select: { tmt: true } } },
  //     lama_jabatan: {
  //       mv_latest_jabatan_personel: { select: { tmt_jabatan: true } },
  //     },
  //     dikbang_akhir: {
  //       dikbangum_personel: {
  //         select: {
  //           dikbangum: { select: { nama: true } },
  //           tanggal_masuk: true,
  //         },
  //       },
  //     },

  //     kep_pangkat: {
  //       pangkat_personel: { select: { tmt: true, kep_nomor: true } },
  //     },
  //     tahun_dikbang_akhir: {
  //       dikbangum_personel: {
  //         select: {
  //           dikbangum: { select: { nama: true } },
  //           tanggal_masuk: true,
  //         },
  //       },
  //     },
  //     status_personel: { status_aktif: { select: { nama: true } } },
  //     tinggi: { personel_fisik: { select: { tinggi_badan: true } } },
  //     riwayat_pangkat: {
  //       pangkat_personel: { select: { pangkat: { select: { nama: true, nama_singkat:true } } } },
  //     },
  //     dikjur: { dikum_personel: { select: { jurusan: true } } },
  //     penghargaan: {
  //       penghargaan_personel: {
  //         select: { penghargaan: { select: { nama: true } } },
  //       },
  //     },
  //     dikum: {
  //       dikum_personel: { select: { dikum: { select: { nama: true } } } },
  //     },
  //     tanda_kehormatan: {
  //       tanhor_personel: { select: { tanhor: { select: { nama: true } } } },
  //     },
  //     pangkat_pertama: {
  //       pangkat_personel: {
  //         select: { pangkat: { select: { nama: true, nama_singkat:true } }, tmt: true },
  //       },
  //     },
  //     riwayat_jabatan: {
  //       jabatan_personel: {
  //         select: { jabatans: { select: { nama: true } }, tmt_jabatan: true },
  //       },
  //     },

  //     tmt_perwira: {
  //       pangkat_personel: {
  //         select: { tmt: true, pangkat: { select: { nama_singkat: true } } },
  //       },
  //     },

  //     tmt_pangkat_pertama: {
  //       pangkat_personel: {
  //         select: { pangkat: { select: { nama: true, nama_singkat:true } }, tmt: true },
  //       },
  //     },
  //     jabatan_pertama: {
  //       jabatan_personel: {
  //         select: { jabatans: { select: { nama: true } }, tmt_jabatan: true },
  //       },
  //     },

  //     tmt_jabatan_pertama: {
  //       jabatan_personel: {
  //         select: {
  //           jabatans: { select: { nama: true } },
  //           tmt_jabatan: true,
  //         },
  //       },
  //     },
  //     satker: {
  //       mv_latest_jabatan_personel: {
  //         select: {
  //           satuan: {
  //             select: {
  //               mv_satuan_with_top_parents_self: {
  //                 select: { second_top_parent_nama: true },
  //               },
  //             },
  //           },
  //         },
  //       },
  //     },

  //     'diktuk_+_dikbang': {
  //       dikbangum_personel: {
  //         select: { dikbangum: { select: { nama: true } } },
  //       },
  //       diktuk_personel: { select: { diktuk: { select: { nama: true } } } },
  //     },
  //     'jabatan_+_nivelering': {
  //       mv_latest_jabatan_personel: {
  //         select: { jabatan_sekarang: { select: { nama: true } } },
  //       },
  //       nivellering_terakhir: {
  //         select: { nivellering: { select: { nama: true } } },
  //       },
  //     },
  //     'pangkat_+_nrp': {
  //       nrp: true,
  //       pangkat_terakhir: {
  //         select: { pangkat_sekarang: { select: { nama: true, nama_singkat:true } } },
  //       },
  //     },
  //     '3_jabatan_terakhir_(jabatan_+_tmt)': {
  //       jabatan_personel: {
  //         select: {
  //           jabatans: { select: { nama: true } },
  //           tmt_jabatan: true,
  //         },
  //       },
  //     },
  //     '(diktuk_+_dikbang)_+_tahun': {
  //       dikbangum_personel: {
  //         select: {
  //           dikbangum: { select: { nama: true } },
  //           tanggal_masuk: true,
  //         },
  //       },
  //       diktuk_personel: {
  //         select: { diktuk: { select: { nama: true } }, tanggal_masuk: true },
  //       },
  //     },
  //     'tempat_+_tanggal_lahir': { tempat_lahir: true, tanggal_lahir: true },
  //     'jabatan_+_tmt_jabatan': {
  //       mv_latest_jabatan_personel: {
  //         select: {
  //           jabatan_sekarang: { select: { nama: true } },
  //           tmt_jabatan: true,
  //         },
  //       },
  //     },

  //     'istri_/_suami': {
  //       keluarga_personel: {
  //         select: {
  //           nama_keluarga: true,
  //           hubungan_keluarga: { select: { hubungan: true } },
  //         },
  //       },
  //     },

  //     'no._kep_jabatan': {
  //       mv_latest_jabatan_personel: { select: { skep_no: true } },
  //     },
  //   };

  //   let propFunc = {
  //     nama: (r: any) => r.nama_lengkap,
  //     nrp: (r: any) => r.nrp,
  //     ktp_nomor: (r: any) => r.ktp_nomor,
  //     email: (r: any) => r.email,
  //     darah: (r: any) => r.golongan_darah,
  //     tempat_lahir: (r: any) => r.tempat_lahir,
  //     tgl_lahir: (r: any) => r.tanggal_lahir,
  //     telp_hp: (r: any) => r.no_hp,
  //     npwp: (r: any) => r.npwp_nomor,
  //     asabri: (r: any) => r.asabri_nomor,
  //     tmt_masa_dinas_surut: (r: any) => r.masa_dinas_surut_tmt,
  //     jenis_kelamin: (r: any) => r.jenis_kelamin,
  //     nik: (r: any) => r.ktp_nomor,

  //     agama: (r: any) => r.agama?.nama,
  //     suku_bangsa: (r: any) => r.suku?.nama,
  //     bahasa: (r: any) =>
  //       r.bahasa_personel.map((b) => b.bahasa?.nama).join('\n'),

  //     alamat: (r: any) => r.alamat[0]?.alamat,

  //     jabatan: (r: any) => r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama,
  //     nivelering: (r: any) => r.nivellering_terakhir?.nivellering?.nama,
  //     pangkat: (r: any) => r.pangkat_terakhir?.pangkat_sekarang?.nama_singkat,

  //     status_assessment: (r: any) =>
  //       r.assesment_personel.map((a) => a.nama).join('\n'),

  //     diktuk: (r: any) =>
  //       r.diktuk_personel.map((d) => d.diktuk?.nama).join('\n'),
  //     tmt_jabatan: (r: any) => r.mv_latest_jabatan_personel?.tmt_jabatan,
  //     tahun_diktuk: (r: any) =>
  //       r.diktuk_personel
  //         .map((a) => new Date(a.tanggal_masuk).getFullYear())
  //         .join('\n'),
  //     tmt_pangkat: (r: any) => r.pangkat_terakhir?.tmt,
  //     lama_jabatan: (r: any) =>
  //       getAge(
  //         r.mv_latest_jabatan_personel?.tmt_jabatan ?? new Date(),
  //         new Date(),
  //       ),
  //     dikbang_akhir: (r: any) =>
  //       r.dikbangum_personel.sort(
  //         (a, b) =>
  //           (a.tanggal_masuk ? a.tanggal_masuk.getTime() : Infinity) -
  //           (b.tanggal_masuk ? b.tanggal_masuk.getTime() : Infinity),
  //       )[r.dikbangum_personel.length - 1]?.dikbangum?.nama,
  //     kep_pangkat: (r: any) =>
  //       r.pangkat_personel?.sort(
  //         (a, b) =>
  //           (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
  //           (b.tmt ? new Date(b.tmt).getTime() : Infinity),
  //       )[0]?.kep_nomor,
  //     tahun_dikbang_akhir: (r: any) =>
  //       r.dikbangum_personel
  //         .map((a) => new Date(a.tanggal_masuk).getFullYear())
  //         .sort((a, b) => b - a)[0],
  //     status_personel: (r: any) => r.status_aktif.nama,
  //     tinggi: (r: any) => r.personel_fisik.map((f) => f.tinggi_badan).join('\n'),
  //     riwayat_pangkat: (r: any) =>
  //       r.pangkat_personel?.map((j) => j.pangkat?.nama_singkat).join('\n'),
  //     dikjur: (r: any) =>
  //       r.dikum_personel
  //         .map((a) => a.jurusan?.nama)
  //         .filter((a) => a)
  //         .join('\n'),
  //     penghargaan: (r: any) =>
  //       r.penghargaan_personel.map((p) => p.penghargaan?.nama).join('\n'),
  //     dikum: (r: any) => r.dikum_personel.map((d) => d.dikum?.nama).join('\n'),
  //     tanda_kehormatan: (r: any) =>
  //       r.tanhor_personel.map((t) => t.tanhor?.nama).join('\n'),
  //     pangkat_pertama: (r: any) =>
  //       r.pangkat_personel?.sort(
  //         (a, b) =>
  //           (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
  //           (b.tmt ? new Date(b.tmt).getTime() : Infinity),
  //       )[0]?.pangkat?.nama_singkat,
  //     riwayat_jabatan: (r: any) =>
  //       r.jabatan_personel
  //         ?.map((j) => j.jabatans?.nama)
  //         .filter((a) => a)
  //         .join('\n'),
  //     tmt_pangkat_pertama: (r: any) =>
  //       r.pangkat_personel?.sort(
  //         (a, b) =>
  //           (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
  //           (b.tmt ? new Date(b.tmt).getTime() : Infinity),
  //       )[0]?.tmt,
  //     jabatan_pertama: (r: any) =>
  //       r.jabatan_personel?.sort(
  //         (a, b) =>
  //           (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
  //           (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
  //       )[0].jabatans?.nama,
  //     tmt_jabatan_pertama: (r: any) =>
  //       r.jabatan_personel?.sort(
  //         (a, b) =>
  //           (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
  //           (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
  //       )[0].tmt_jabatan,

  //     tmt_perwira: (r: any) =>
  //       r.pangkat_personel
  //         ?.filter((a) => a.pangkat?.nama_singkat === `IPDA`)
  //         .sort(
  //           (a, b) =>
  //             (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
  //             (b.tmt ? new Date(b.tmt).getTime() : Infinity),
  //         )[0]?.tmt,
  //     satker: (r: any) =>
  //       r.mv_latest_jabatan_personel?.satuan?.mv_satuan_with_top_parents_self
  //         ?.second_top_parent_nama,

  //     'diktuk_+_dikbang': (r: any) =>
  //       `(${r.diktuk_personel.map((a) => a.diktuk?.nama).join(',')}) + (${r.dikbangum_personel.map((a) => a.dikbangum?.nama).join(',')})`,
  //     'jabatan_+_nivelering': (r: any) =>
  //       `${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama ?? ``}, ${r.nivellering_terakhir?.nivellering?.nama ?? ``}`,
  //     'pangkat_+_nrp': (r: any) =>
  //       `${r.pangkat_terakhir?.pangkat_sekarang?.nama_singkat}/${r.nrp}`,
  //     '3_jabatan_terakhir_(jabatan_+_tmt)': (r: any) =>
  //       r.jabatan_personel
  //         ?.sort(
  //           (a, b) =>
  //             (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
  //             (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
  //         )
  //         .reverse()
  //         .slice(0, 3)
  //         .filter((r) => r.jabatans?.nama && r.tmt_jabatan)
  //         .map(
  //           (r) =>
  //             `${r.jabatans?.nama} , ${moment(r.tmt_jabatan).format('YYYY-MM-DD')}`,
  //         )
  //         .join('\n'),
  //     '(diktuk_+_dikbang)_+_tahun': (r: any) =>
  //       `(${r.diktuk_personel.map((a) => `${a.diktuk?.nama}(${new Date(a.tanggal_masuk).getFullYear()})`).join(',')}) + (${r.dikbangum_personel.map((a) => `${a.dikbangum?.nama}(${new Date(a.tanggal_masuk).getFullYear()})`).join(',')})`,
  //     'tempat_+_tanggal_lahir': (r: any) =>
  //       `${r.tempat_lahir ?? ``},  ${moment(r.tanggal_lahir).format('YYYY-MM-DD') ?? ``}`,
  //     'jabatan_+_tmt_jabatan': (r: any) =>
  //       `${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama ?? ``},  ${moment(r.mv_latest_jabatan_personel?.tmt_jabatan).format('YYYY-MM-DD') ?? ``}`,

  //     'istri_/_suami': (r: any) =>
  //       r.keluarga_personel
  //         .filter(
  //           (k) =>
  //             k.hubungan_keluarga?.hubungan == 'SUAMI' ||
  //             k.hubungan_keluarga?.hubungan == 'ISTRI',
  //         )
  //         .map((k) => `${k.nama_keluarga} (${k.hubungan_keluarga?.hubungan})`)
  //         .join('\n'),
  //     'no._kep_jabatan': (r: any) => r.mv_latest_jabatan_personel?.skep_no,
  //   };

  //   let selected = chosen_column
  //     .map((c) => columns[c])
  //     .reduce((a, b) => deepMerge(a, b));

  //   try {
  //     let queryResult: any = await this.prisma.personel.findMany({
  //       select: selected,
  //       where: { nrp: { in: nrps } },
  //     });

  //     queryResult = queryResult.map((r) =>
  //       Object.fromEntries(chosen_column.map((k) => [k, propFunc[k](r)])),
  //     );
  //     queryResult = multiSortByKey(queryResult, order_by, ['lama_jabatan']);

  //     const workbook = new ExcelJS.Workbook();
  //     const worksheet = workbook.addWorksheet('SIPP', {properties:{defaultColWidth: 10}});

  //     headers.unshift("No. ")
  //     worksheet.columns = headers.map((h,i)=> new Object({header: h, key: chosen_column[i], style:{alignment:{wrapText:true}}}))

  //     let index = 1

  //     for (let r of queryResult) worksheet.addRow([index++, ...chosen_column.map((c) => r[c])]);

  //     autofitExcelJsColumns(worksheet, 100)

  //     // worksheet.addRow(headers);

  //     const buffer = await workbook.xlsx.writeBuffer();

  //     const message = convertToLogMessage(
  //       ConstantLogStatusEnum.SUCCESS,
  //       ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  //       ConstantLogDataTypeEnum.LIST,
  //       ConstantLogModuleEnum.SIPP3_MODULE,
  //     );
  //     await this.logsActivityService.addLogsActivity(
  //       convertToILogData(
  //         req,
  //         CONSTANT_LOG.SIPP3_WRITE_EXCEL as ConstantLogType,
  //         message,
  //         `Data length: ${queryResult.length}`,
  //       ),
  //     );

  //     return { buffer };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async exportPersonelWithColumnsWithAdvancedSearch(
    req,
    paginationData,
    searchandsortData,
  ) {
    req.nologMessage = true;
    req.nofotofile = true;
    let searchPrisma = await this.getSearchPrisma(
      req,
      paginationData,
      searchandsortData,
    );

    let selectedUserRole = req['selected_user_role'];
    let { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let personalIDQuery = null;

    let headers = JSON.parse(search_text[search_column.indexOf('headers')]);
    let chosen_column = JSON.parse(
      search_text[search_column.indexOf('chosen_column')],
    );
    let exclude_nrps = JSON.parse(
      search_text[search_column.indexOf('exclude_nrps')],
    );
    let nrps = searchPrisma.data
      .map((d) => d.nrp)
      .filter((d) => !exclude_nrps.includes(d));
    let order_by = JSON.parse(search_text[search_column.indexOf('order_by')]);

    //SPECIFY A COLUMN OR MULTIPLE COLUMN, DON'T SPECIFY [RELATION]:true (ex: {pangkat_personel:true} )
    let columns = {
      nama: { nama_lengkap: true },
      nrp: { nrp: true },
      ktp_nomor: { ktp_nomor: true },
      email: { email: true },
      darah: { golongan_darah: true },
      tempat_lahir: { tempat_lahir: true },
      tgl_lahir: { tanggal_lahir: true },
      telp_hp: { no_hp: true },
      npwp: { npwp_nomor: true },
      asabri: { asabri_nomor: true },
      tmt_masa_dinas_surut: { masa_dinas_surut_tmt: true },
      jenis_kelamin: { jenis_kelamin: true },
      nik: { ktp_nomor: true },

      agama: { agama: { select: { nama: true } } },
      suku_bangsa: { suku: { select: { nama: true } } },
      bahasa: { bahasa_personel: { select: { bahasa: true } } },

      alamat: { alamat: { select: { alamat: true } } },

      pangkat: {
        mv_pangkat_terakhir: {
          select: {
            mv_pangkat_terakhir_pangkat_sekarang: {
              select: { nama: true, nama_singkat: true },
            },
          },
        },
      },
      jabatan: {
        mv_latest_jabatan_personel: {
          select: { jabatan_sekarang: { select: { nama: true, nivellering: true } } },
        },
      },
      diktuk: {
        diktuk_personel: { select: { diktuk: { select: { nama: true } } } },
      },

      status_assessment: { assesment_personel: { select: { nama: true } } },

      tahun_diktuk: { diktuk_personel: { select: { tanggal_masuk: true } } },

      tmt_jabatan: {
        mv_latest_jabatan_personel: { select: { tmt_jabatan: true } },
      },

      tmt_pangkat: { mv_pangkat_terakhir: { select: { tmt: true } } },
      lama_jabatan: {
        mv_latest_jabatan_personel: { select: { tmt_jabatan: true } },
      },
      dikbang_akhir: {
        dikbangum_personel: {
          select: {
            dikbangum: { select: { nama: true } },
            tanggal_masuk: true,
          },
        },
      },

      kep_pangkat: {
        pangkat_personel: { select: { tmt: true, kep_nomor: true } },
      },
      tahun_dikbang_akhir: {
        dikbangum_personel: {
          select: {
            dikbangum: { select: { nama: true } },
            tanggal_masuk: true,
          },
        },
      },
      status_personel: { status_aktif: { select: { nama: true } } },
      tinggi: { personel_fisik: { select: { tinggi_badan: true } } },
      riwayat_pangkat: {
        pangkat_personel: {
          select: { pangkat: { select: { nama: true, nama_singkat: true } } },
        },
      },
      dikjur: { dikum_personel: { select: { jurusan: true } } },
      penghargaan: {
        penghargaan_personel: {
          select: { penghargaan: { select: { nama: true } } },
        },
      },
      dikum: {
        dikum_personel: { select: { dikum: { select: { nama: true } } } },
      },
      tanda_kehormatan: {
        tanhor_personel: { select: { tanhor: { select: { nama: true } } } },
      },
      pangkat_pertama: {
        pangkat_personel: {
          select: {
            pangkat: { select: { nama: true, nama_singkat: true } },
            tmt: true,
          },
        },
      },
      riwayat_jabatan: {
        jabatan_personel: {
          select: { jabatans: { select: { nama: true, nivellering: { select: { nama: true, } } } }, tmt_jabatan: true },
        },
      },

      tmt_perwira: {
        pangkat_personel: {
          select: { tmt: true, pangkat: { select: { nama_singkat: true } } },
        },
      },

      tmt_pangkat_pertama: {
        pangkat_personel: {
          select: {
            pangkat: { select: { nama: true, nama_singkat: true } },
            tmt: true,
          },
        },
      },
      jabatan_pertama: {
        jabatan_personel: {
          select: { jabatans: { select: { nama: true } }, tmt_jabatan: true },
        },
      },

      tmt_jabatan_pertama: {
        jabatan_personel: {
          select: {
            jabatans: { select: { nama: true } },
            tmt_jabatan: true,
          },
        },
      },
      satker: {
        mv_latest_jabatan_personel: {
          select: {
            satuan: {
              select: {
                mv_satuan_with_top_parents_self: {
                  select: { second_top_parent_nama: true },
                },
              },
            },
          },
        },
      },

      'diktuk_+_dikbang': {
        dikbangum_personel: {
          select: { dikbangum: { select: { nama: true } } },
        },
        diktuk_personel: { select: { diktuk: { select: { nama: true } } } },
      },
      'jabatan_+_nivelering': {
        mv_latest_jabatan_personel: {
          select: { jabatan_sekarang: { select: { nama: true, nivellering: true } } },
        },
      },
      'nivelering': {
        mv_latest_jabatan_personel: {
          select: { jabatan_sekarang: { select: { nama: true, nivellering: true } } },
        },
      },
      'pangkat_+_nrp': {
        nrp: true,
        mv_pangkat_terakhir: {
          select: {
            mv_pangkat_terakhir_pangkat_sekarang: {
              select: { nama: true, nama_singkat: true },
            },
          },
        },
      },
      '3_jabatan_terakhir_(jabatan_+_tmt)': {
        jabatan_personel: {
          select: {
            jabatans: { select: { nama: true, nivellering: { select: { nama: true, } } } },
            tmt_jabatan: true,
          },
        },
      },
      '(diktuk_+_dikbang)_+_tahun': {
        dikbangum_personel: {
          select: {
            dikbangum: { select: { nama: true } },
            tanggal_masuk: true,
          },
        },
        diktuk_personel: {
          select: { diktuk: { select: { nama: true } }, tanggal_masuk: true },
        },
      },
      'tempat_+_tanggal_lahir': { tempat_lahir: true, tanggal_lahir: true },
      'jabatan_+_tmt_jabatan': {
        mv_latest_jabatan_personel: {
          select: {
            jabatan_sekarang: { select: { nama: true } },
            tmt_jabatan: true,
          },
        },
      },

      'istri_/_suami': {
        keluarga_personel: {
          select: {
            nama_keluarga: true,
            hubungan_keluarga: { select: { hubungan: true } },
          },
        },
      },

      'no._kep_jabatan': {
        mv_latest_jabatan_personel: { select: { skep_no: true } },
      },
    };

    let propFunc = {
      nama: (r: any) => r.nama_lengkap,
      nrp: (r: any) => r.nrp,
      ktp_nomor: (r: any) => r.ktp_nomor,
      email: (r: any) => r.email,
      darah: (r: any) => r.golongan_darah,
      tempat_lahir: (r: any) => r.tempat_lahir,
      tgl_lahir: (r: any) => r.tanggal_lahir,
      telp_hp: (r: any) => r.no_hp,
      npwp: (r: any) => r.npwp_nomor,
      asabri: (r: any) => r.asabri_nomor,
      tmt_masa_dinas_surut: (r: any) => r.masa_dinas_surut_tmt,
      jenis_kelamin: (r: any) => r.jenis_kelamin,
      nik: (r: any) => r.ktp_nomor,

      agama: (r: any) => r.agama?.nama,
      suku_bangsa: (r: any) => r.suku?.nama,
      bahasa: (r: any) =>
        r.bahasa_personel.map((b) => b.bahasa?.nama).join('\n'),

      alamat: (r: any) => r.alamat[0]?.alamat,

      jabatan: (r: any) => `${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama ?? ``} ${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama ? `(${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama})` : ``}`,
      nivelering: (r: any) => r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama ? r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama : "",
      pangkat: (r: any) =>
        r.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang
          ?.nama_singkat,

      status_assessment: (r: any) =>
        r.assesment_personel.map((a) => a.nama).join('\n'),

      diktuk: (r: any) =>
        r.diktuk_personel.map((d) => d.diktuk?.nama).join('\n'),
      tmt_jabatan: (r: any) => r.mv_latest_jabatan_personel?.tmt_jabatan,
      tahun_diktuk: (r: any) =>
        r.diktuk_personel
          .map((a) => new Date(a.tanggal_masuk).getFullYear())
          .join('\n'),
      tmt_pangkat: (r: any) => r.mv_pangkat_terakhir?.tmt,
      lama_jabatan: (r: any) =>
        getAge(
          r.mv_latest_jabatan_personel?.tmt_jabatan ?? new Date(),
          new Date(),
        ),
      dikbang_akhir: (r: any) =>
        r.dikbangum_personel.sort(
          (a, b) =>
            (a.tanggal_masuk ? a.tanggal_masuk.getTime() : Infinity) -
            (b.tanggal_masuk ? b.tanggal_masuk.getTime() : Infinity),
        )[r.dikbangum_personel.length - 1]?.dikbangum?.nama,
      kep_pangkat: (r: any) =>
        r.pangkat_personel?.sort(
          (a, b) =>
            (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
            (b.tmt ? new Date(b.tmt).getTime() : Infinity),
        )[0]?.kep_nomor,
      tahun_dikbang_akhir: (r: any) =>
        r.dikbangum_personel
          .map((a) => new Date(a.tanggal_masuk).getFullYear())
          .sort((a, b) => b - a)[0],
      status_personel: (r: any) => r.status_aktif.nama,
      tinggi: (r: any) =>
        r.personel_fisik.map((f) => f.tinggi_badan).join('\n'),
      riwayat_pangkat: (r: any) =>
        r.pangkat_personel?.map((j) => j.pangkat?.nama_singkat).join('\n'),
      dikjur: (r: any) =>
        r.dikum_personel
          .map((a) => a.jurusan?.nama)
          .filter((a) => a)
          .join('\n'),
      penghargaan: (r: any) =>
        r.penghargaan_personel.map((p) => p.penghargaan?.nama).join('\n'),
      dikum: (r: any) => r.dikum_personel.map((d) => d.dikum?.nama).join('\n'),
      tanda_kehormatan: (r: any) =>
        r.tanhor_personel.map((t) => t.tanhor?.nama).join('\n'),
      pangkat_pertama: (r: any) =>
        r.pangkat_personel?.sort(
          (a, b) =>
            (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
            (b.tmt ? new Date(b.tmt).getTime() : Infinity),
        )[0]?.pangkat?.nama_singkat,
      riwayat_jabatan: (r: any) =>
        r.jabatan_personel
          ?.sort(
            (a, b) =>
              (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
              (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
          )
          .reverse()
          .filter((r) => r.jabatans?.nama && r.tmt_jabatan)
          .map(
            (r) =>
              `${r.jabatans?.nama} ${r.jabatans?.nivellering?.nama ? `(${r.jabatans?.nivellering?.nama}) ` : ``}(${moment(r.tmt_jabatan).format('DD-MM-YYYY')})`,
          )
          .join('\n'),
      tmt_pangkat_pertama: (r: any) =>
        r.pangkat_personel?.sort(
          (a, b) =>
            (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
            (b.tmt ? new Date(b.tmt).getTime() : Infinity),
        )[0]?.tmt,
      jabatan_pertama: (r: any) =>
        r.jabatan_personel?.sort(
          (a, b) =>
            (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
            (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
        )[0]?.jabatans?.nama,
      tmt_jabatan_pertama: (r: any) =>
        r.jabatan_personel?.sort(
          (a, b) =>
            (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
            (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
        )[0]?.tmt_jabatan,

      tmt_perwira: (r: any) =>
        r.pangkat_personel
          ?.filter((a) => a.pangkat?.nama_singkat === `IPDA`)
          .sort(
            (a, b) =>
              (a.tmt ? new Date(a.tmt).getTime() : Infinity) -
              (b.tmt ? new Date(b.tmt).getTime() : Infinity),
          )[0]?.tmt,
      satker: (r: any) =>
        r.mv_latest_jabatan_personel?.satuan?.mv_satuan_with_top_parents_self
          ?.second_top_parent_nama,

      'diktuk_+_dikbang': (r: any) =>
        `${r.diktuk_personel.map((a) => a.diktuk?.nama).join('\n')}  ${r.diktuk_personel.length < 2 ? `\n` : ``}${r.dikbangum_personel.map((a) => a.dikbangum?.nama).join('\n')} `,
      'jabatan_+_nivelering': (r: any) =>
        `${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama ?? ``} ${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama ? `(${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nivellering?.nama})` : ``}`,
      'pangkat_+_nrp': (r: any) =>
        `${r.mv_pangkat_terakhir?.mv_pangkat_terakhir_pangkat_sekarang?.nama_singkat}/${r.nrp}`,
      '3_jabatan_terakhir_(jabatan_+_tmt)': (r: any) =>
        r.jabatan_personel
          ?.sort(
            (a, b) =>
              (a.tmt_jabatan ? a.tmt_jabatan.getTime() : Infinity) -
              (b.tmt_jabatan ? b.tmt_jabatan.getTime() : Infinity),
          )
          .reverse()
          .slice(0, 3)
          .filter((r) => r.jabatans?.nama && r.tmt_jabatan)
          .map(
            (r) =>
              `${r.jabatans?.nama} ${r.jabatans?.nivellering?.nama ? `(${r.jabatans?.nivellering?.nama}) ` : ``}(${moment(r.tmt_jabatan).format('DD-MM-YYYY')})`,
          )
          .join('\n'),
      '(diktuk_+_dikbang)_+_tahun': (r: any) =>
        `${r.diktuk_personel.map((a) => `${a.diktuk?.nama} ${new Date(a.tanggal_masuk).getFullYear()}`).join('\n')} ${r.diktuk_personel.length < 2 ? `\n` : ``}${r.dikbangum_personel.map((a) => `${a.dikbangum?.nama} ${new Date(a.tanggal_masuk).getFullYear()} `).join('\n')}`,
      'tempat_+_tanggal_lahir': (r: any) =>
        `${r.tempat_lahir ?? ``},  ${moment(r.tanggal_lahir).format('YYYY-MM-DD') ?? ``}`,
      'jabatan_+_tmt_jabatan': (r: any) =>
        `${r.mv_latest_jabatan_personel?.jabatan_sekarang?.nama ?? ``},  ${moment(r.mv_latest_jabatan_personel?.tmt_jabatan).format('YYYY-MM-DD') ?? ``}`,

      'istri_/_suami': (r: any) =>
        r.keluarga_personel
          .filter(
            (k) =>
              k.hubungan_keluarga?.hubungan == 'SUAMI' ||
              k.hubungan_keluarga?.hubungan == 'ISTRI',
          )
          .map((k) => `${k.nama_keluarga} (${k.hubungan_keluarga?.hubungan})`)
          .join('\n'),
      'no._kep_jabatan': (r: any) => r.mv_latest_jabatan_personel?.skep_no,
    };

    let selected = chosen_column
      .map((c) => columns[c])
      .reduce((a, b) => deepMerge(a, b));

    try {
      let queryResult: any = await this.prisma.personel.findMany({
        select: selected,
        where: { nrp: { in: nrps } },
        orderBy: [
          { mv_pangkat_terakhir: { pangkat_id: 'desc' } },
          { tanggal_lahir: 'asc' },
        ],
      });

      queryResult = queryResult.map((r) =>
        Object.fromEntries(chosen_column.map((k) => [k, propFunc[k](r)])),
      );
      queryResult = multiSortByKey(queryResult, order_by, ['lama_jabatan']);

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('SIPP', {
        properties: { defaultColWidth: 10 },
      });

      headers.unshift('No. ');
      worksheet.columns = headers.map(
        (h, i) =>
          new Object({
            header: h,
            key: chosen_column[i],
            style: { alignment: { wrapText: true } },
          }),
      );

      let index = 1;

      for (let r of queryResult)
        worksheet.addRow([index++, ...chosen_column.map((c) => r[c])]);

      autofitExcelJsColumns(worksheet, 100);

      // worksheet.addRow(headers);

      const buffer = await workbook.xlsx.writeBuffer();

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_WRITE_EXCEL as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return { buffer };
    } catch (error) {
      throw error;
    }
  }

  async updatePangkatPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (
        !(await this.prisma.pangkat_personel.findUnique({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Pangkat personel id ${id} not found!`);

      if (
        body.pangkat_id != null &&
        !(await this.prisma.pangkat.findUnique({
          where: { id: body.pangkat_id },
        }))
      )
        throw new BadRequestException(
          `Pangkat id ${body.pangkat_id} not found!`,
        );

      let data = {
        pangkat_id: body.pangkat_id,
        tmt: body.tmt,
        kep_nomor: body.kep_nomor,
        kep_tanggal: body.kep_tanggal,
        kep_file: body.kep_file,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.pangkat_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePangkatPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = await this.prisma.pangkat_personel.findUnique({
        where: { id: +id },
      });

      if (!existing) {
        throw new BadRequestException(`Pangkat personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.pangkat_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createPangkatPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.pangkat.findUnique({
          where: { id: body.pangkat_id },
        }))
      )
        throw new BadRequestException(
          `Pangkat id ${body.pangkat_id} not found!`,
        );

      let data = {
        personel_id: personel_id,
        pangkat_id: body.pangkat_id,
        tmt: body.tmt,
        kep_nomor: body.kep_nomor,
        kep_tanggal: body.kep_tanggal,
        kep_file: body.kep_file,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.pangkat_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateJabatanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (
        !(await this.prisma.jabatan_personel.findUnique({
          where: { id: +id, deleted_at: null },
        }))
      )
        throw new BadRequestException(`Jabatan personel id ${id} not found!`);

      if (body.jabatan_id) {
        const jabatan = await this.prisma.jabatan.findUnique({
          where: { id: body.jabatan_id },
        });
        if (!jabatan)
          throw new BadRequestException(
            `Jabatan id ${body.jabatan_id} not found!`,
          );

        body.jabatan = jabatan.nama;
      }

      let data = {
        jabatan_id: body.jabatan_id,
        tmt_jabatan: body.tmt_jabatan,
        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,
        is_ps: body.is_ps,
        keterangan: body.keterangan,
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.jabatan_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteJabatanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = await this.prisma.jabatan_personel.findUnique({
        where: { id: +id },
      });

      if (!existing) {
        throw new BadRequestException(`Jabatan personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.jabatan_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createJabatanPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      const jabatan = await this.prisma.jabatan.findUnique({
        where: { id: body.jabatan_id },
      });

      if (!jabatan)
        throw new BadRequestException(
          `Jabatan id ${body.jabatan_id} not found!`,
        );

      let data = {
        personel_id: personel_id,
        jabatan_id: body.jabatan_id,
        tmt_jabatan: new Date(body.tmt_jabatan),
        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,
        is_ps: body.is_ps,
        keterangan: body.keterangan,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.jabatan_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createKeluargaPersonelOrangtua(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      const agama = await this.prisma.agama.findUnique({
        where: { id: body.agama_id },
      });

      if (!agama)
        throw new BadRequestException(`Agama id ${body.agama_id} not found!`);

      const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique({
        where: { id: body.hubungan_keluarga_id },
      });

      if (!hubunganKeluarga)
        throw new BadRequestException(
          `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
        );

      let data = {
        personel_id: personel_id,
        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        alamat: body.alamat,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateKeluargaPersonelOrangtua(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (body.agama_id) {
        const agama = await this.prisma.agama.findUnique({
          where: { id: body.agama_id },
        });

        if (!agama)
          throw new BadRequestException(`Agama id ${body.agama_id} not found!`);
      }

      if (body.hubungan_keluarga_id) {
        const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique(
          { where: { id: body.hubungan_keluarga_id } },
        );

        if (!hubunganKeluarga)
          throw new BadRequestException(
            `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
          );
      }

      let data = {
        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        alamat: body.alamat,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteKeluargaPersonelOrangtua(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.keluarga_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Keluarga personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.keluarga_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createKeluargaPersonelPasangan(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      // const agama = await this.prisma.agama.findUnique({where: {id: body.agama_id}})
      //
      // if (!agama) throw new BadRequestException(`Agama id ${body.agama_id} not found!`);

      const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique({
        where: { id: body.hubungan_keluarga_id },
      });

      if (!hubunganKeluarga)
        throw new BadRequestException(
          `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
        );

      let data = {
        personel_id: personel_id,
        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        // agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,
        tanggal_nikah: body.tanggal_nikah,
        kpis_nomor: body.kpis_nomor,
        kpis_file: body.kpis_file,
        buku_nikah_nomor: body.buku_nikah_nomor,
        buku_nikah_file: body.buku_nikah_file,
        status_nikah: body.status_nikah,
        status_pernikahan: body.status_pernikahan,
        cerai_file: body.cerai_file,
        no_ijinnikah: body.no_ijinnikah,
        file_ijinnikah: body.file_ijinnikah,
        ktp_nomor: body.ktp_nomor,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.create({
            data: {
              ...data,
              pekerjaan_keluarga: {
                create: { jenis_pekerjaan_id: body.jenis_pekerjaan_id },
              },
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateKeluargaPersonelPasangan(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (body.agama_id) {
        const agama = await this.prisma.agama.findUnique({
          where: { id: body.agama_id },
        });

        if (!agama)
          throw new BadRequestException(`Agama id ${body.agama_id} not found!`);
      }

      if (body.hubungan_keluarga_id) {
        const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique(
          { where: { id: body.hubungan_keluarga_id } },
        );

        if (!hubunganKeluarga)
          throw new BadRequestException(
            `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
          );
      }

      /* if(body.jenis_pekerjaan_id){
        const hubunganKeluarga = await this.prisma.pekerjaan_keluarga.create({data: })

        if (!hubunganKeluarga) throw new BadRequestException(`Hubungan Keluarga id ${body.hubungan_keluarga} not found!`);
      } */

      let data = {
        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,
        tanggal_nikah: body.tanggal_nikah,
        kpis_nomor: body.kpis_nomor,
        kpis_file: body.kpis_file,
        buku_nikah_nomor: body.buku_nikah_nomor,
        buku_nikah_file: body.buku_nikah_file,
        status_nikah: body.status_nikah,
        status_pernikahan: body.status_pernikahan,
        cerai_file: body.cerai_file,
        no_ijinnikah: body.no_ijinnikah,
        file_ijinnikah: body.file_ijinnikah,
        ktp_nomor: body.ktp_nomor,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.update({
            where: { id: +id },
            data: {
              ...data,
              pekerjaan_keluarga: {
                create: {
                  jenis_pekerjaan_id: body.jenis_pekerjaan_id,
                },
              },
              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteKeluargaPersonelPasangan(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.keluarga_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Keluarga personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.keluarga_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createKeluargaPersonelAnak(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      const agama = await this.prisma.agama.findUnique({
        where: { id: body.agama_id },
      });

      if (!agama)
        throw new BadRequestException(`Agama id ${body.agama_id} not found!`);

      const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique({
        where: { id: body.hubungan_keluarga_id },
      });

      if (!hubunganKeluarga)
        throw new BadRequestException(
          `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
        );

      let data = {
        personel_id: personel_id,

        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        alamat: body.alamat,

        agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateKeluargaPersonelAnak(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      if (body.agama_id) {
        const agama = await this.prisma.agama.findUnique({
          where: { id: body.agama_id },
        });

        if (!agama)
          throw new BadRequestException(`Agama id ${body.agama_id} not found!`);
      }

      if (body.hubungan_keluarga_id) {
        const hubunganKeluarga = await this.prisma.hubungan_keluarga.findUnique(
          { where: { id: body.hubungan_keluarga_id } },
        );

        if (!hubunganKeluarga)
          throw new BadRequestException(
            `Hubungan Keluarga id ${body.hubungan_keluarga} not found!`,
          );
      }

      let data = {
        nama_keluarga: body.nama_keluarga,
        jenis_kelamin: body.jenis_kelamin,
        status: body.status,
        tempat_lahir: body.tempat_lahir,
        tanggal_lahir: body.tanggal_lahir,
        alamat: body.alamat,

        agama_id: body.agama_id,
        hubungan_keluarga_id: body.hubungan_keluarga_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.keluarga_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteKeluargaPersonelAnak(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.keluarga_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Keluarga personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.keluarga_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createPenugasanLuarStruktur(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,
        negara_id: 83,
        sprin_nomor: body.sprin_nomor,
        sprin_file: body.sprin_file,
        sprin_penerbit: body.sprin_penerbit,
        jabatan_penugasan: body.jabatan_penugasan,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penugasan_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePenugasanLuarStruktur(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penugasan_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Penugasan personel id ${id} not found!`);
      }

      let data = {
        sprin_nomor: body.sprin_nomor,
        sprin_file: body.sprin_file,
        sprin_penerbit: body.sprin_penerbit,
        jabatan_penugasan: body.jabatan_penugasan,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penugasan_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePenugasanLuarStruktur(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penugasan_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Penugasan personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.penugasan_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createMisiPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      const misi = await this.prisma.misi.findUnique({
        where: { id: body.misi_id },
      });

      if (!misi)
        throw new BadRequestException(`Misi id ${body.misi_id} not found!`);

      let data = {
        personel_id: personel_id,

        misi_id: body.misi_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.misi_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateMisiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.misi_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Misi personel id ${id} not found!`);
      }

      if (body.misi_id) {
        const misi = await this.prisma.misi.findUnique({
          where: { id: body.misi_id },
        });

        if (!misi)
          throw new BadRequestException(`Misi id ${body.misi_id} not found!`);
      }

      let data = {
        misi_id: body.misi_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.misi_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteMisiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.misi_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Misi personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.misi_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createDikumPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.dikum.findUnique({ where: { id: body.dikum_id } }))
      )
        throw new BadRequestException(`Dikum id ${body.dikum_id} not found!`);

      if (
        body.institusi_id &&
        !(await this.prisma.institusi.findUnique({
          where: { id: body.institusi_id },
        }))
      )
        throw new BadRequestException(
          `Institusi id ${body.institusi_id} not found!`,
        );
      if (
        body.jurusan_id &&
        !(await this.prisma.jurusan.findUnique({
          where: { id: body.jurusan_id },
        }))
      )
        throw new BadRequestException(
          `Jurusan id ${body.jurusan_id} not found!`,
        );

      const detailDikum = await this.prisma.dikum_detail.findFirst({
        where: {
          dikum_id: body.dikum_id,
          institusi_id: body.institusi_id,
          jurusan_id: body.jurusan_id,
        },
      });

      let data = {
        tanggal_mulai: body.tanggal_mulai,
        tanggal_lulus: body.tanggal_lulus,
        dikum_detail_id: BigInt(detailDikum.id),
        personel_id: personel_id,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikum_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateDikumPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = await this.prisma.dikum_personel.findUnique({ where: { id: id } });
      if (!existing)
        throw new BadRequestException(`Dikum personel id ${id} not found!`);

      if (
        body.dikum_id &&
        !(await this.prisma.dikum.findUnique({
          where: { id: body.dikum_id },
        }))
      )
        throw new BadRequestException(`Dikum id ${body.dikum_id} not found!`);

      if (
        body.institusi_id &&
        !(await this.prisma.institusi.findUnique({
          where: { id: body.institusi_id },
        }))
      )
        throw new BadRequestException(
          `Institusi id ${body.institusi_id} not found!`,
        );
      if (
        body.jurusan_id &&
        !(await this.prisma.jurusan.findUnique({
          where: { id: body.jurusan_id },
        }))
      )
        throw new BadRequestException(
          `Jurusan id ${body.jurusan_id} not found!`,
        );

      const detailDikumBefore = await this.prisma.dikum_detail.findFirst({
        where: {
          id: existing.dikum_detail_id
        }
      })

      let dikumID = body.dikum_id || detailDikumBefore.dikum_id
      let institusiID = body.institusi_id || detailDikumBefore.institusi_id
      let jurusanID = body.jurusan_id || null

      const detailDikum = await this.prisma.dikum_detail.findFirst({
        where: {
          dikum_id: dikumID,
          institusi_id: institusiID,
          jurusan_id: jurusanID,
        },
      });

      let data = {
        tanggal_mulai: body.tanggal_mulai,
        tanggal_lulus: body.tanggal_lulus,
        dikum_detail_id: BigInt(detailDikum.id),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikum_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteDikumPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.dikum_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Dikum personel id ${id} not found!`);

      let queryResult = await this.prisma.dikum_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createDikbangumPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        body.dikbangum_id &&
        !(await this.prisma.dikbangum.findUnique({
          where: { id: body.dikbangum_id },
        }))
      )
        throw new BadRequestException(
          `Dikbangum id ${body.dikbangum_id} not found!`,
        );

      let data = {
        personel_id: personel_id,

        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        dikbangum_id: body.dikbangum_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikbangum_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateDikbangumPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.dikbangum_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Dikbangum personel id ${id} not found!`);

      if (
        body.dikbangum_id &&
        !(await this.prisma.dikbangum.findUnique({
          where: { id: body.dikbangum_id },
        }))
      )
        throw new BadRequestException(
          `Dikbangum id ${body.dikbangum_id} not found!`,
        );

      let data = {
        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        dikbangum_id: body.dikbangum_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikbangum_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteDikbangumPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.dikbangum_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Dikbangum personel id ${id} not found!`);

      let queryResult = await this.prisma.dikbangum_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createDiktukPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        body.diktuk_id &&
        !(await this.prisma.diktuk.findUnique({
          where: { id: body.diktuk_id },
        }))
      )
        throw new BadRequestException(`Diktuk id ${body.diktuk_id} not found!`);

      let data = {
        personel_id: personel_id,

        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        diktuk_id: body.diktuk_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.diktuk_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateDiktukPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.diktuk_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Diktuk personel id ${id} not found!`);

      if (
        body.diktuk_id &&
        !(await this.prisma.diktuk.findUnique({
          where: { id: body.diktuk_id },
        }))
      )
        throw new BadRequestException(`Diktuk id ${body.diktuk_id} not found!`);

      let data = {
        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        diktuk_id: body.diktuk_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.diktuk_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteDiktukPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.diktuk_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Diktuk personel id ${id} not found!`);

      let queryResult = await this.prisma.diktuk_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createDikbangspesPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.dikbangspes.findUnique({
          where: { id: body.dikbangspes_id },
        }))
      )
        throw new BadRequestException(
          `Dikbangspes id ${body.dikbangspes_id} not found!`,
        );

      let data = {
        personel_id: personel_id,

        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        dikbangspes_id: body.dikbangspes_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikbangspes_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateDikbangspesPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.dikbangspes_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Dikbangspes personel id ${id} not found!`,
        );

      if (
        body.dikbangspes_id &&
        !(await this.prisma.dikbangspes.findUnique({
          where: { id: body.dikbangspes_id },
        }))
      )
        throw new BadRequestException(
          `Dikbangspes id ${body.dikbangspes_id} not found!`,
        );

      let data = {
        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: parseFloat(body.nilai),
        transkrip_nilai_file: body.transkrip_nilai_file,
        ijazah_no: body.ijazah_no,
        ijazah_file: body.ijazah_file,
        ranking: parseInt(body.ranking),
        jumlah_siswa: parseInt(body.jumlah_siswa),

        dikbangspes_id: body.dikbangspes_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.dikbangspes_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteDikbangspesPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.dikbangspes_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Dikbangspes personel id ${id} not found!`,
        );

      let queryResult = await this.prisma.dikbangspes_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createPelatihanPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.pelatihan.findUnique({
          where: { id: body.pelatihan_id },
        }))
      )
        throw new BadRequestException(
          `Pelatihan id ${body.pelatihan_id} not found!`,
        );

      let data = {
        personel_id: personel_id,

        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: body.nilai,
        transkrip_nilai_file: body.transkrip_nilai_file,
        sertifikat_nomor: body.sertifikat_nomor,
        sertifikat_file: body.sertifikat_file,

        pelatihan_id: body.pelatihan_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.pelatihan_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePelatihanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.pelatihan_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Pelatihan personel id ${id} not found!`);

      if (
        !(await this.prisma.pelatihan.findUnique({
          where: { id: body.pelatihan_id },
        }))
      )
        throw new BadRequestException(
          `Pelatihan id ${body.pelatihan_id} not found!`,
        );

      let data = {
        tanggal_masuk: body.tanggal_masuk,
        tanggal_selesai: body.tanggal_selesai,
        nilai: body.nilai,
        transkrip_nilai_file: body.transkrip_nilai_file,
        sertifikat_nomor: body.sertifikat_nomor,
        sertifikat_file: body.sertifikat_file,

        pelatihan_id: body.pelatihan_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.pelatihan_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePelatihanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.pelatihan_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Pelatihan personel id ${id} not found!`);

      let queryResult = await this.prisma.pelatihan_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createTanhorPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.tanhor.findUnique({
          where: { id: body.tanhor_id },
        }))
      )
        throw new BadRequestException(`Tanhor id ${body.tanhor_id} not found!`);

      let data = {
        personel_id: personel_id,

        tgl_tanhor: body.tgl_tanhor,
        surat_no: body.surat_no,
        surat_file: body.surat_file,

        tanhor_id: body.tanhor_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.tanhor_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateTanhorPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.tanhor_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Tanhor personel id ${id} not found!`);

      if (
        body.tanhor_id &&
        !(await this.prisma.tanhor.findUnique({
          where: { id: body.tanhor_id },
        }))
      )
        throw new BadRequestException(`Tanhor id ${body.tanhor_id} not found!`);

      let data = {
        tgl_tanhor: body.tgl_tanhor,
        surat_no: body.surat_no,
        surat_file: body.surat_file,

        tanhor_id: body.tanhor_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.tanhor_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteTanhorPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.tanhor_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Tanhor personel id ${id} not found!`);

      let queryResult = await this.prisma.tanhor_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createPenghargaanPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.penghargaan.findUnique({
          where: { id: body.penghargaan_id },
        }))
      )
        throw new BadRequestException(
          `Penghargaan id ${body.penghargaan_id} not found!`,
        );

      if (
        !(await this.prisma.penghargaan_tingkat.findUnique({
          where: { id: body.tingkat_id },
        }))
      )
        throw new BadRequestException(
          `Penghargaan tingkat id ${body.jurusan_id} not found!`,
        );

      let data = {
        personel_id: personel_id,

        tgl_penghargaan: body.tgl_penghargaan,
        surat_no: body.surat_no,
        surat_file: body.surat_file,
        penghargaan_file: body.penghargaan_file,
        note: body.note,

        penghargaan_id: body.penghargaan_id,
        tingkat_id: body.tingkat_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penghargaan_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePenghargaanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penghargaan_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Penghargaan personel id ${id} not found!`,
        );

      if (
        body.penghargaan_id &&
        !(await this.prisma.penghargaan.findUnique({
          where: { id: body.penghargaan_id },
        }))
      )
        throw new BadRequestException(
          `Penghargaan id ${body.penghargaan_id} not found!`,
        );

      if (
        body.tingkat_id &&
        !(await this.prisma.penghargaan_tingkat.findUnique({
          where: { id: body.tingkat_id },
        }))
      )
        throw new BadRequestException(
          `Penghargaan tingkat id ${body.jurusan_id} not found!`,
        );

      let data = {
        tgl_penghargaan: body.tgl_penghargaan,
        surat_no: body.surat_no,
        surat_file: body.surat_file,
        penghargaan_file: body.penghargaan_file,
        note: body.note,

        penghargaan_id: body.penghargaan_id,
        tingkat_id: body.tingkat_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penghargaan_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePenghargaanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penghargaan_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Penghargaan personel id ${id} not found!`,
        );

      let queryResult = await this.prisma.penghargaan_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createBahasaPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.bahasa.findUnique({
          where: { id: body.bahasa_id },
        }))
      )
        throw new BadRequestException(`Bahasa id ${body.bahasa_id} not found!`);

      let data = {
        personel_id: personel_id,

        bahasa_id: body.bahasa_id,
        is_aktif: body.is_aktif,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.bahasa_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateBahasaPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.bahasa_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Bahasa personel id ${id} not found!`);

      if (
        body.bahasa_id &&
        !(await this.prisma.bahasa.findUnique({
          where: { id: body.bahasa_id },
        }))
      )
        throw new BadRequestException(`Bahasa id ${body.bahasa_id} not found!`);

      let data = {
        bahasa_id: body.bahasa_id,
        is_aktif: body.is_aktif
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.bahasa_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteBahasaPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.bahasa_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Bahasa personel id ${id} not found!`);

      let queryResult = await this.prisma.bahasa_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createOlahragaPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.olahraga.findUnique({
          where: { id: body.olahraga_id },
        }))
      )
        throw new BadRequestException(
          `Olahraga id ${body.olahraga_id} not found!`,
        );

      let data = {
        personel_id: personel_id,

        olahraga_id: body.olahraga_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.olahraga_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getListHobi(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    let selectedUserRole = req['selected_user_role'];

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const queryResult = await this.prisma.hobi.findMany({
      distinct: ['nama'],
      where: { nama: { contains: search, mode: 'insensitive' } },
      take: limit,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateOlahragaPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.olahraga_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Olahraga personel id ${id} not found!`);

      if (
        !(await this.prisma.olahraga.findUnique({
          where: { id: body.olahraga_id },
        }))
      )
        throw new BadRequestException(
          `Olahraga id ${body.olahraga_id} not found!`,
        );

      let data = {
        olahraga_id: body.olahraga_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.olahraga_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteOlahragaPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.olahraga_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Olahraga personel id ${id} not found!`);

      let queryResult = await this.prisma.olahraga_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createHobiPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (!(await this.prisma.hobi.findUnique({ where: { id: body.hobi_id } })))
        throw new BadRequestException(`Hobi id ${body.hobi_id} not found!`);

      let data = {
        personel_id: personel_id,

        hobi_id: body.hobi_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.hobi_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateHobiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.hobi_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Hobi personel id ${id} not found!`);

      if (!(await this.prisma.hobi.findUnique({ where: { id: body.hobi_id } })))
        throw new BadRequestException(`Hobi id ${body.hobi_id} not found!`);

      let data = {
        hobi_id: body.hobi_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.hobi_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteHobiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.hobi_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Hobi personel id ${id} not found!`);

      let queryResult = await this.prisma.hobi_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async getListBrevet(req, paginationData, searchandsortData) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const search_method = req.query.search_method;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'bigint' | 'number';
      };
    } = {
      // id: { field: 'id', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      // is_aktif: { field: 'is_aktif', type: 'boolean' },
    };

    let selectedUserRole = req['selected_user_role'];
    let satuanLv2;
    let satuanLv3;

    const { orderBy, where } = SortSearchColumnV2(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      null,
      search_method,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.brevet.count({
        where: { ...where },
      }),
      this.prisma.brevet.findMany({
        select: {
          id: true,
          nama: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: { ...where },
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPP3_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPP3_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryResult,
    };
  }

  async createBrevetPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.brevet.findUnique({
          where: { id: body.brevet_id },
        }))
      )
        throw new BadRequestException(`Brevet id ${body.brevet_id} not found!`);

      let data = {
        personel_id: personel_id,

        tmt_brevet: body.tmt_brevet,
        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,

        brevet_id: body.brevet_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.brevet_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateBrevetPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.brevet_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Brevet personel id ${id} not found!`);

      if (
        !(await this.prisma.brevet.findUnique({
          where: { id: body.brevet_id },
        }))
      )
        throw new BadRequestException(`Brevet id ${body.brevet_id} not found!`);

      let data = {
        tmt_brevet: body.tmt_brevet,
        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,

        brevet_id: body.brevet_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.brevet_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteBrevetPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.brevet_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Brevet personel id ${id} not found!`);

      let queryResult = await this.prisma.brevet_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createBKOPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (
        !(await this.prisma.satuan.findUnique({
          where: { id: body.satuan_id },
        }))
      )
        throw new BadRequestException(`Satuan id ${body.satuan_id} not found!`);

      let data = {
        personel_id: personel_id,

        tmt_bko: body.tmt_bko,
        sprin_nomor: body.sprin_nomor,
        sprin_file: body.sprin_file,

        satuan_id: body.satuan_id,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.bko_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateBKOPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.bko_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`BKO personel id ${id} not found!`);

      if (
        !(await this.prisma.satuan.findUnique({
          where: { id: body.satuan_id },
        }))
      )
        throw new BadRequestException(`Satuan id ${body.satuan_id} not found!`);

      let data = {
        tmt_bko: body.tmt_bko,
        sprin_no: body.sprin_no,
        sprin_file: body.sprin_file,

        satuan_id: body.satuan_id,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.bko_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteBKOPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.bko_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`BKO personel id ${id} not found!`);

      let queryResult = await this.prisma.bko_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createNilaiSMKPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        keterangan: body.keterangan,
        // score_nilai_smk: body.score_nilai_smk,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.smk_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateNilaiSMKPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.smk_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Nilai SMK personel id ${id} not found!`);

      let data = {
        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        keterangan: body.keterangan,
        score_nilai_smk: body.score_nilai_smk,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.smk_personel.update({
            where: { id: +id },
            data: {
              ...data,
              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteNilaiSMKPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.smk_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Nilai SMK personel id ${id} not found!`);

      let queryResult = await this.prisma.smk_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createJasmaniPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        tahun: body.tahun,
        nilai_akhir: body.nilai_akhir,
        keterangan: body.keterangan,
        nilai_lari_12_m: body.nilai_lari_12_m,
        hasil_lari_12_m: body.hasil_lari_12_m,
        hasil_pull_up: body.hasil_pull_up,
        nilai_pull_up: body.nilai_pull_up,
        nilai_sit_up: body.nilai_sit_up,
        hasil_sit_up: body.hasil_sit_up,
        semester: body.semester,
        nilai_file: body.nilai_file,
        nilai_shutle_run: body.nilai_shutle_run,
        hasil_shutle_run: body.hasil_shutle_run,
        nilai_chinning: body.nilai_chinning,
        hasil_chinning: body.hasil_chinning,
        nilai_a: body.nilai_a,
        nilai_b: body.nilai_b,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.jasmani_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateJasmaniPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.jasmani_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Jasmani personel id ${id} not found!`);

      let data = {
        tahun: body.tahun,
        nilai_akhir: body.nilai_akhir,
        keterangan: body.keterangan,
        nilai_lari_12_m: body.nilai_lari_12_m,
        hasil_lari_12_m: body.hasil_lari_12_m,
        hasil_pull_up: body.hasil_pull_up,
        nilai_pull_up: body.nilai_pull_up,
        nilai_sit_up: body.nilai_sit_up,
        hasil_sit_up: body.hasil_sit_up,
        semester: body.semester,
        nilai_file: body.nilai_file,
        nilai_shutle_run: body.nilai_shutle_run,
        hasil_shutle_run: body.hasil_shutle_run,
        nilai_chinning: body.nilai_chinning,
        hasil_chinning: body.hasil_chinning,
        nilai_a: body.nilai_a,
        nilai_b: body.nilai_b,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.jasmani_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteJasmaniPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.jasmani_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Jasmani personel id ${id} not found!`);

      let queryResult = await this.prisma.jasmani_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createRohaniPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.rohani_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateRohaniPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.rohani_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Rohani personel id ${id} not found!`);

      let data = {
        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.rohani_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteRohaniPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.rohani_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Rohani personel id ${id} not found!`);

      let queryResult = await this.prisma.rohani_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createKesehatanPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.rikkesla_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateKesehatanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.rikkesla_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Rikkesla personel id ${id} not found!`);

      let data = {
        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.rikkesla_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteKesehatanPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.rikkesla_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Rikkesla personel id ${id} not found!`);

      let queryResult = await this.prisma.rikkesla_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createPsikologiPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.psikologi_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePsikologiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.psikologi_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Psikologi personel id ${id} not found!`);

      let data = {
        tahun: body.tahun,
        semester: body.semester,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.psikologi_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePsikologiPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.psikologi_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`Psikologi personel id ${id} not found!`);

      let queryResult = await this.prisma.psikologi_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createAssessmentPersonel(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        nama: body.nama,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.assessment_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateAssessmentPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.assessment_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Assessment personel id ${id} not found!`,
        );

      let data = {
        nama: body.nama,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,
        nilai: body.nilai,
        nilai_file: body.nilai_file,
        keterangan: body.keterangan,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.assessment_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteAssessmentPersonel(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.assessment_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(
          `Assessment personel id ${id} not found!`,
        );

      let queryResult = await this.prisma.assessment_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createRiwayatPindahInstansi(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;
      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      if (body.instansi_id) {
        const instansi_id = (
          await this.prisma.personel.findFirst({
            where: { id: body.instansi_id },
          })
        ).id;
        if (!instansi_id)
          throw new BadRequestException(
            `Riwayat id ${body.instansi_id} not found!`,
          );
      }

      let data = {
        personel_id: personel_id,
        instansi_id: body.instansi_id,

        jabatan_penugasan: body.jabatan_penugasan,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penugasan_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateRiwayatPindahInstansi(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penugasan_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Penugasan personel id ${id} not found!`);
      }

      if (body.instansi_id) {
        const instansi_id = (
          await this.prisma.personel.findFirst({
            where: { id: body.instansi_id },
          })
        ).id;
        if (!instansi_id)
          throw new BadRequestException(
            `Riwayat id ${body.instansi_id} not found!`,
          );
      }

      let data = {
        instansi_id: body.instansi_id,
        jabatan_penugasan: body.jabatan_penugasan,
        tmt_mulai: body.tmt_mulai,
        tmt_selesai: body.tmt_selesai,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.penugasan_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteRiwayatPindahInstansi(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.penugasan_personel.findUnique({ where: id });

      if (!existing) {
        throw new BadRequestException(`Penugasan personel id ${id} not found!`);
      }

      let queryResult = await this.prisma.penugasan_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async createKGB(req, personel_uid, body) {
    try {
      if (personel_uid == null)
        throw new BadRequestException('UID cannot be empty!');

      const personel_id = (
        await this.prisma.personel.findFirst({ where: { uid: personel_uid } })
      ).id;

      if (!personel_id)
        throw new BadRequestException(`Personel id ${personel_uid} not found!`);

      let data = {
        personel_id: personel_id,

        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,
        tmt: body.tmt,

        created_at: new Date(),
        updated_at: new Date(),
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.kgb_personel.create({
            data: data,
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateKGB(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.kgb_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`KGB personel id ${id} not found!`);

      let data = {
        kep_nomor: body.kep_nomor,
        kep_file: body.kep_file,
        tmt: body.tmt,
      };

      const queryResult = [];

      if (Object.keys(data).length > 0)
        queryResult.push(
          await this.prisma.kgb_personel.update({
            where: { id: +id },
            data: {
              ...data,

              updated_at: new Date(),
            },
          }),
        );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async deleteKGB(req, id, body) {
    try {
      if (!id) throw new BadRequestException('Id cannot be empty!');

      let existing = this.prisma.kgb_personel.findUnique({ where: id });
      if (!existing)
        throw new BadRequestException(`KGB personel id ${id} not found!`);

      let queryResult = await this.prisma.kgb_personel.delete({
        where: { id: +id },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.SIPP3_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPP3_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }
}
