import { forwardRef, Module } from '@nestjs/common';
import { Sipp3Service } from './service/sipp-3.service';
import { Sipp3Controller } from './controller/sipp-3.controller';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [Sipp3Controller],
  providers: [Sipp3Service, MinioService],
})
export class Sipp3Module {}
