import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Delete,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Sipp3Service } from '../service/sipp-3.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { UpdatePersonelDTO } from 'src/personel/dto/personel.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express/multer/interceptors/file-fields.interceptor';
import { UserRoleGuard } from '../../../core/guards/user-role.guards';
import { RolePermissionNeeded } from '../../../core/decorators/role-permission.decorator';
import { Response } from 'express';

@Controller('sipp-3')
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class Sipp3Controller {
  private readonly logger = new Logger(Sipp3Controller.name);

  constructor(private readonly sipp3Service: Sipp3Service) {}

  @Get('/advanced-search')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getSearch(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getSearch.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getSearchPrisma(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getSearch.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/analytics/list-pppk')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async analyticsListPPPK(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.analyticsListPPPK.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getStatistikPPPK(
      req,
      searchandsortData,
      paginationData,
    );

    this.logger.log(
      `Leaving ${this.analyticsListPPPK.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/analytics/statistik-personel')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsStatistikPersonel(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsStatistikPersonel.name}`);
    const response = await this.sipp3Service.getStatisikPersonelData(req);
    this.logger.log(
      `Leaving ${this.analyticsStatistikPersonel.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-perpangkat/pati-pamen-patma')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsPangkatPatiPamenPatma(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsPangkatPatiPamenPatma.name}`);
    const response = await this.sipp3Service.getStatistikForPatiPamenPama(req);
    this.logger.log(
      `Leaving ${this.analyticsPangkatPatiPamenPatma.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-perpangkat/pns')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsPangkatPNS(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsPangkatPNS.name}`);
    const response = await this.sipp3Service.getStatistikForPersonelPNS(req);
    this.logger.log(
      `Leaving ${this.analyticsPangkatPNS.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-perpangkat/bintara-tamtama')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsPangkatBintaraTamtama(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsPangkatBintaraTamtama.name}`);
    const response = await this.sipp3Service.getStatistikForBintaraTamtama(req);
    this.logger.log(
      `Leaving ${this.analyticsPangkatBintaraTamtama.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-perpangkat/lanjutan')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsPangkatLanjutan(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsPangkatLanjutan.name}`);
    const response = await this.sipp3Service.getStatistikForLanjutan(req);
    this.logger.log(
      `Leaving ${this.analyticsPangkatLanjutan.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-persatker/:satuanId')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsPersatker(@Req() req: any, @Param('satuanId') satuanId: string) {
    this.logger.log(
      `Entering ${this.analyticsPersatker.name} with satuan id: ${satuanId}`,
    );
    const response = await this.sipp3Service.getStatistikForSatker(
      req,
      parseInt(satuanId),
    );
    this.logger.log(
      `Leaving ${this.analyticsPersatker.name} with satuan id: ${satuanId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/statistik-persatker-polda/:satuanId')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsPersatkerPolda(
    @Req() req: any,
    @Param('satuanId') satuanId: string,
  ) {
    this.logger.log(
      `Entering ${this.analyticsPersatker.name} with satuan id: ${satuanId}`,
    );
    const response = await this.sipp3Service.getStatistikForSatkerPolda(
      req,
      parseInt(satuanId),
    );
    this.logger.log(
      `Leaving ${this.analyticsPersatker.name} with satuan id: ${satuanId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }


  @Get('/analytics/daerah-overview')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsOverviewDaerah(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsOverviewDaerah.name}`);
    const response = await this.sipp3Service.getStatistikOverviewForDaerah(req);
    this.logger.log(
      `Leaving ${this.analyticsOverviewDaerah.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/satuan-dibawah/:satuanId')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async analyticsSatuanDibawah(
    @Param('satuanId') satuanId: string,
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.analyticsSatuanDibawah.name} with satuan id: ${satuanId}`,
    );
    const response = await this.sipp3Service.getStatistikBawahanForSatuan(
      req,
      paginationData,
      searchandsortData,
      parseInt(satuanId),
    );
    this.logger.log(
      `Leaving ${this.analyticsSatuanDibawah.name} with satuan id: ${satuanId} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/personel-satuan-dibawah/:satuanId')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async getPersonelBawahanForSatuan(
    @Param('satuanId') satuanId: string,
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getPersonelBawahanForSatuan.name} with satuan id: ${satuanId} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getPersonelBawahanForSatuan(
      req,
      paginationData,
      searchandsortData,
      parseInt(satuanId),
    );
    this.logger.log(
      `Leaving ${this.getPersonelBawahanForSatuan.name} with satuan id: ${satuanId} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/analytics/statistik-persentase-personel')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @Permission('PENGAJUAN_CUTI_GET')
  async analyticsPersentasePersonel(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsPersentasePersonel.name}`);
    const response = await this.sipp3Service.analyticsPersentasePersonel(req);
    this.logger.log(
      `Leaving ${this.analyticsPersentasePersonel.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/personel/:id')
  //@RolePermissionNeeded('LIST_PERSONEL', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard/* , UserRoleGuard */)
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdatePersonelDTO,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('upload_file')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  @UseGuards(JwtAuthGuard)
  async uploadFile(
    @Req() req: any,
    @UploadedFiles()
      files: {
      file?: Express.Multer.File[];
    },
  ) {
    this.logger.log(
      `Entering ${this.uploadFile.name} with file length: ${files.file.length}`,
    );
    const response = await this.sipp3Service.uploadFile(req, files.file[0]);
    this.logger.log(
      `Leaving ${this.uploadFile.name} with file length: ${files.file.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('upload_file_nrp/:nrp')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  @UseGuards(JwtAuthGuard)
  async uploadFileWithNRP(
    @Req() req: any,
    @Param('nrp') nrp: any,
    @UploadedFiles()
      files: {
      file?: Express.Multer.File[];
    },
  ) {
    this.logger.log(
      `Entering ${this.uploadFile.name} with file length: ${files.file.length}`,
    );
    const response = await this.sipp3Service.uploadFileWithNRP(req, files.file[0], nrp);
    this.logger.log(
      `Leaving ${this.uploadFile.name} with file length: ${files.file.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/kelengkapan-data')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsKelengkapanData(@Req() req: any) {
    this.logger.log(`Entering ${this.analyticsKelengkapanData.name}`);
    const response = await this.sipp3Service.statisticKelengkapanData(req);
    this.logger.log(
      `Leaving ${this.analyticsKelengkapanData.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/kelengkapan-data-pagination')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsKelengkapanDataPagination(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.analyticsKelengkapanDataPagination.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response =
      await this.sipp3Service.statisticKelengkapanDataWithPagination(
        req,
        paginationData,
        searchandsortData,
      );
    this.logger.log(
      `Leaving ${this.analyticsKelengkapanDataPagination.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/analytics/kelengkapan-data/:satker/:kelengkapan')
  // @Permission('PENGAJUAN_CUTI_GET')
  @UseGuards(JwtAuthGuard)
  async analyticsKelengkapanDataDetail(
    @Req() req: any,
    @Param('satker') satker: string,
    @Param('kelengkapan') kelengkapan: string,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.analyticsKelengkapanDataDetail.name} with satker: ${satker} and kelengkapan: ${kelengkapan} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.statisticKelengkapanDataDetail(
      req,
      searchandsortData,
      paginationData,
      satker,
      kelengkapan === 'Lengkap',
    );

    this.logger.log(
      `Leaving ${this.analyticsKelengkapanDataDetail.name} with satker: ${satker} and kelengkapan: ${kelengkapan} and pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/jabatan_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getJabatanList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListJabatan(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/dikum_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getDikumList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDikumList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListDikum(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/institusi_list/:dikum_id')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getInstitusiList(
    @Req() req: any,
    @Param('dikum_id') dikum_id: number,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDikumList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListInstitusi(
      req,
      dikum_id,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/jurusan_list/:dikum_id/:institusi_id')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getJurusanList(
    @Req() req: any,
    @Param('dikum_id') dikum_id: number,
    @Param('institusi_id') institusi_id: number,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDikumList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListJurusan(
      req,
      dikum_id,
      institusi_id,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/pelatihan_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getPelatihanList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListPelatihan(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getJabatanList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/pendidikan-polisi')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  async getDropdownPendidikanKepolisian(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDropdownPendidikanKepolisian.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListPendidikanKepolisian(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getDropdownPendidikanKepolisian.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/dropdown/status-aktif')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  async getDropdownStatusAktif(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getDropdownStatusAktif.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getDropdownStatusAktif(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getDropdownStatusAktif.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/hubungan_keluarga_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getHubunganKeluargaList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getHubunganKeluargaList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListHubunganKeluarga(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getHubunganKeluargaList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  // @Get('/export_excel')
  // @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  // @UseGuards(JwtAuthGuard, UserRoleGuard)
  // @HttpCode(200)
  // async exportSelectedDataPolda(
  //   @Req() req: any,
  //   @Res() res: Response,
  //   @Query() paginationData: PaginationDto,
  //   @Query() searchandsortData: SearchAndSortDTO,
  // ) {
  //   this.logger.log(
  //     `Entering ${this.exportSelectedDataPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
  //   );
  //   const { buffer } = await this.sipp3Service.exportPersonelWithColumns(
  //     req,
  //     paginationData,
  //     searchandsortData,
  //   );

  //   const filename = `Hasil Ekspor - SIPP`;

  //   res.setHeader(
  //     'Content-Type',
  //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //   );
  //   res.setHeader(
  //     `Content-Disposition`,
  //     `attachment; filename=${filename}.xlsx`,
  //   );
  //   res.setHeader('Content-Length', buffer.byteLength);
  //   this.logger.log(
  //     `Leaving ${this.exportSelectedDataPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
  //   );
  //   res.end(buffer);
  // }

  @Get('/export_excel_with_advanced_search')
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async exportExcelWithAdvancedSearch(
    @Req() req: any,
    @Res() res: Response,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.exportExcelWithAdvancedSearch.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const { buffer } = await this.sipp3Service.exportPersonelWithColumnsWithAdvancedSearch(
      req,
      paginationData,
      searchandsortData,
    );

    const filename = `Hasil Ekspor - SIPP`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.exportExcelWithAdvancedSearch.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    res.end(buffer);
  }

  @Put('/pangkat_personel/:id')
  @RolePermissionNeeded('KEPANGKATAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updatePangkatPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePangkatPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePangkatPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updatePangkatPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/pangkat_personel/:id')
  @RolePermissionNeeded('KEPANGKATAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deletePangkatPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deletePangkatPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deletePangkatPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deletePangkatPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/pangkat_personel/:uid')
  @RolePermissionNeeded('KEPANGKATAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createPangkatPersonel(
    @Req() req: any,
    @Param('uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createPangkatPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createPangkatPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createPangkatPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/jabatan_personel/:id')
  @RolePermissionNeeded('JABATAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateJabatanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateJabatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateJabatanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateJabatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/jabatan_personel/:id')
  @RolePermissionNeeded('JABATAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteJabatanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteJabatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteJabatanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteJabatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/jabatan_personel/:personel_uid')
  @RolePermissionNeeded('JABATAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createJabatanPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createJabatanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createJabatanPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createJabatanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/keluarga_personel_orangtua/:personel_uid')
  @RolePermissionNeeded('KELUARGA_ORANG_TUA', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createKeluargaPersonelOrangtua(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createKeluargaPersonelOrangtua.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createKeluargaPersonelOrangtua(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createKeluargaPersonelOrangtua.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/keluarga_personel_orangtua/:id')
  @RolePermissionNeeded('KELUARGA_ORANG_TUA', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateKeluargaPersonelOrangtua(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateKeluargaPersonelOrangtua.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateKeluargaPersonelOrangtua(req, id, body);
    this.logger.log(
      `Leaving ${this.updateKeluargaPersonelOrangtua.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/keluarga_personel_orangtua/:id')
  @RolePermissionNeeded('KELUARGA_ORANG_TUA', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteKeluargaPersonelOrangtua(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteKeluargaPersonelOrangtua.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteKeluargaPersonelOrangtua(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteKeluargaPersonelOrangtua.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/keluarga_personel_pasangan/:id')
  @RolePermissionNeeded('KELUARGA_PASANGAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateKeluargaPersonelPasangan(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateKeluargaPersonelPasangan.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateKeluargaPersonelPasangan(req, id, body);
    this.logger.log(
      `Leaving ${this.updateKeluargaPersonelPasangan.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/keluarga_personel_pasangan/:id')
  @RolePermissionNeeded('KELUARGA_PASANGAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteKeluargaPersonelPasangan(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteKeluargaPersonelPasangan.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteKeluargaPersonelPasangan(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteKeluargaPersonelPasangan.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/keluarga_personel_pasangan/:personel_uid')
  @RolePermissionNeeded('KELUARGA_PASANGAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createKeluargaPersonelPasangan(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createKeluargaPersonelPasangan.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createKeluargaPersonelPasangan(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createKeluargaPersonelPasangan.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/keluarga_personel_anak/:id')
  @RolePermissionNeeded('KELUARGA_ANAK', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateKeluargaPersonelAnak(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateKeluargaPersonelAnak.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateKeluargaPersonelAnak(req, id, body);
    this.logger.log(
      `Leaving ${this.updateKeluargaPersonelAnak.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/keluarga_personel_anak/:id')
  @RolePermissionNeeded('KELUARGA_ANAK', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteKeluargaPersonelAnak(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteKeluargaPersonelAnak.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteKeluargaPersonelAnak(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteKeluargaPersonelAnak.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/keluarga_personel_anak/:personel_uid')
  @RolePermissionNeeded('KELUARGA_ANAK', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createKeluargaPersonelAnak(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createKeluargaPersonelAnak.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createKeluargaPersonelAnak(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createKeluargaPersonelAnak.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/penugasan_luar_struktur/:id')
  @RolePermissionNeeded('PENUGASAN_LUAR_STRUKTUR', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updatePenugasanLuarStruktur(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePenugasanLuarStruktur.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePenugasanLuarStruktur(req, id, body);
    this.logger.log(
      `Leaving ${this.updatePenugasanLuarStruktur.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/penugasan_luar_struktur/:id')
  @RolePermissionNeeded('PENUGASAN_LUAR_STRUKTUR', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deletePenugasanLuarStruktur(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deletePenugasanLuarStruktur.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deletePenugasanLuarStruktur(req, id, body);
    this.logger.log(
      `Leaving ${this.deletePenugasanLuarStruktur.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/penugasan_luar_struktur/:personel_uid')
  @RolePermissionNeeded('PENUGASAN_LUAR_STRUKTUR', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createPenugasanLuarStruktur(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createPenugasanLuarStruktur.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createPenugasanLuarStruktur(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createPenugasanLuarStruktur.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/misi_personel/:id')
  @RolePermissionNeeded('MISI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateMisiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateMisiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateMisiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateMisiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/misi_personel/:id')
  @RolePermissionNeeded('MISI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteMisiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteMisiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteMisiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteMisiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/misi_personel/:personel_uid')
  @RolePermissionNeeded('MISI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createMisiPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createMisiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createMisiPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createMisiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/dikum_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_UMUM', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateDikumPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateDikumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateDikumPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateDikumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/dikum_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_UMUM', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteDikumPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteDikumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteDikumPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteDikumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/dikum_personel/:personel_uid')
  @RolePermissionNeeded('PENDIDIKAN_UMUM', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createDikumPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createDikumPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createDikumPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createDikumPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/dikbangum_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateDikbangumPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateDikbangumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateDikbangumPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateDikbangumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/dikbangum_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteDikbangumPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteDikbangumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteDikbangumPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteDikbangumPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/dikbangum_personel/:uid')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createDikbangumPersonel(
    @Req() req: any,
    @Param('uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createDikbangumPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createDikbangumPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createDikbangumPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/diktuk_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateDiktukPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateDiktukPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateDiktukPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateDiktukPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/diktuk_personel/:id')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteDiktukPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteDiktukPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteDiktukPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteDiktukPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/diktuk_personel/:uid')
  @RolePermissionNeeded('PENDIDIKAN_KEPOLISIAN_&_DIKBANGUM', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createDiktukPersonel(
    @Req() req: any,
    @Param('uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createDiktukPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createDiktukPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createDiktukPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/dikbangspes_personel/:id')
  @RolePermissionNeeded('DIKBANGSPES', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateDikbangspesPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateDikbangspesPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateDikbangspesPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateDikbangspesPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/dikbangspes_personel/:id')
  @RolePermissionNeeded('DIKBANGSPES', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteDikbangspesPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteDikbangspesPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteDikbangspesPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteDikbangspesPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/dikbangspes_personel/:uid')
  @RolePermissionNeeded('DIKBANGSPES', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createDikbangspesPersonel(
    @Req() req: any,
    @Param('uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createDikbangspesPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createDikbangspesPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createDikbangspesPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/pelatihan_personel/:id')
  @RolePermissionNeeded('PELATIHAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updatePelatihanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePelatihanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePelatihanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updatePelatihanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/pelatihan_personel/:id')
  @RolePermissionNeeded('PELATIHAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deletePelatihanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deletePelatihanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deletePelatihanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deletePelatihanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/pelatihan_personel/:personel_uid')
  @RolePermissionNeeded('PELATIHAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createPelatihanPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createPelatihanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createPelatihanPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createPelatihanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/tanhor_personel/:id')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateTanhorPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateTanhorPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateTanhorPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateTanhorPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/tanhor_personel/:id')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteTanhorPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteTanhorPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteTanhorPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteTanhorPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/tanhor_personel/:personel_uid')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createTanhorPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createTanhorPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createTanhorPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createTanhorPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/penghargaan_personel/:id')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updatePenghargaanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePenghargaanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePenghargaanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updatePenghargaanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/penghargaan_personel/:id')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deletePenghargaanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deletePenghargaanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deletePenghargaanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deletePenghargaanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/penghargaan_personel/:personel_uid')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createPenghargaanPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createPenghargaanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createPenghargaanPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createPenghargaanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/bahasa_personel/:id')
  @RolePermissionNeeded('BAHASA', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateBahasaPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateBahasaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateBahasaPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateBahasaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/bahasa_personel/:id')
  @RolePermissionNeeded('BAHASA', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteBahasaPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteBahasaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteBahasaPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteBahasaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/bahasa_personel/:personel_uid')
  @RolePermissionNeeded('BAHASA', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createBahasaPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createBahasaPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createBahasaPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createBahasaPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/olahraga_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getListOlahraga(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListOlahraga.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListOlahraga(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListOlahraga.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }


  @Put('/olahraga_personel/:id')
  @RolePermissionNeeded('OLAHRAGA', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateOlahragaPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateOlahragaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateOlahragaPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateOlahragaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/olahraga_personel/:id')
  @RolePermissionNeeded('OLAHRAGA', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteOlahragaPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteOlahragaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteOlahragaPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteOlahragaPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/olahraga_personel/:personel_uid')
  @RolePermissionNeeded('OLAHRAGA', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createOlahragaPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createOlahragaPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createOlahragaPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createOlahragaPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/hobi_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getListHobi(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListHobi.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListHobi(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListHobi.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/hobi_personel/:id')
  @RolePermissionNeeded('HOBI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateHobiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateHobiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateHobiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateHobiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/hobi_personel/:id')
  @RolePermissionNeeded('HOBI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteHobiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteHobiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteHobiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteHobiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/hobi_personel/:personel_uid')
  @RolePermissionNeeded('HOBI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createHobiPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createHobiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createHobiPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createHobiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/brevet_personel/:id')
  @RolePermissionNeeded('BREVET', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateBrevetPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateBrevetPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateBrevetPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateBrevetPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/brevet_personel/:id')
  @RolePermissionNeeded('BREVET', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteBrevetPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteBrevetPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteBrevetPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteBrevetPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/brevet_personel/:personel_uid')
  @RolePermissionNeeded('BREVET', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createBrevetPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createBrevetPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createBrevetPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createBrevetPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/brevet_list')
  @HttpCode(200)
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @RolePermissionNeeded('CARI_PERSONEL', ['PERMISSION_READ'])
  async getListBrevet(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getListBrevet.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.sipp3Service.getListBrevet(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getListBrevet.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/bko_personel/:id')
  @RolePermissionNeeded('BKO', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateBKOPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateBKOPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateBKOPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateBKOPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/bko_personel/:id')
  @RolePermissionNeeded('BKO', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteBKOPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteBKOPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteBKOPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteBKOPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/bko_personel/:personel_uid')
  @RolePermissionNeeded('BKO', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createBKOPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createBKOPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createBKOPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createBKOPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/nilai_smk_personel/:id')
  @RolePermissionNeeded('SMK', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateNilaiSMKPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateNilaiSMKPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateNilaiSMKPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateNilaiSMKPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/nilai_smk_personel/:id')
  @RolePermissionNeeded('SMK', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteNilaiSMKPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteNilaiSMKPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteNilaiSMKPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteNilaiSMKPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/nilai_smk_personel/:personel_uid')
  @RolePermissionNeeded('SMK', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createNilaiSMKPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createNilaiSMKPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createNilaiSMKPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createNilaiSMKPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/jasmani_personel/:id')
  @RolePermissionNeeded('JASMANI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateJasmaniPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateJasmaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateJasmaniPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateJasmaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/jasmani_personel/:id')
  @RolePermissionNeeded('JASMANI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteJasmaniPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteJasmaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteJasmaniPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteJasmaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/jasmani_personel/:personel_uid')
  @RolePermissionNeeded('JASMANI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createJasmaniPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createJasmaniPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createJasmaniPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createJasmaniPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/rohani_personel/:id')
  @RolePermissionNeeded('ROHANI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateRohaniPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateRohaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateRohaniPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateRohaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/rohani_personel/:id')
  @RolePermissionNeeded('ROHANI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteRohaniPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteRohaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteRohaniPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteRohaniPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/rohani_personel/:personel_uid')
  @RolePermissionNeeded('ROHANI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createRohaniPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createRohaniPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createRohaniPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createRohaniPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/kesehatan_personel/:id')
  @RolePermissionNeeded('KESEHATAN', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateKesehatanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateKesehatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateKesehatanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateKesehatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/kesehatan_personel/:id')
  @RolePermissionNeeded('KESEHATAN', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteKesehatanPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteKesehatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteKesehatanPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteKesehatanPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/kesehatan_personel/:personel_uid')
  @RolePermissionNeeded('KESEHATAN', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createKesehatanPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createKesehatanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createKesehatanPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createKesehatanPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/psikologi_personel/:id')
  @RolePermissionNeeded('PSIKOLOGI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updatePsikologiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updatePsikologiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updatePsikologiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updatePsikologiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/psikologi_personel/:id')
  @RolePermissionNeeded('PSIKOLOGI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deletePsikologiPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deletePsikologiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deletePsikologiPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deletePsikologiPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/psikologi_personel/:personel_uid')
  @RolePermissionNeeded('PSIKOLOGI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createPsikologiPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createPsikologiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createPsikologiPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createPsikologiPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/assessment_personel/:id')
  @RolePermissionNeeded('ASSESSMENT', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateAssessmentPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateAssessmentPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateAssessmentPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.updateAssessmentPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/assessment_personel/:id')
  @RolePermissionNeeded('ASSESSMENT', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteAssessmentPersonel(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteAssessmentPersonel.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteAssessmentPersonel(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteAssessmentPersonel.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/assessment_personel/:personel_uid')
  @RolePermissionNeeded('ASSESSMENT', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createAssessmentPersonel(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createAssessmentPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createAssessmentPersonel(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createAssessmentPersonel.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/riwayat_pindah_instansi/:id')
  @RolePermissionNeeded('RIWAYAT_PINDAH_INSTANSI', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateRiwayatPindahInstansi(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateRiwayatPindahInstansi.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateRiwayatPindahInstansi(req, id, body);
    this.logger.log(
      `Leaving ${this.updateRiwayatPindahInstansi.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/riwayat_pindah_instansi/:id')
  @RolePermissionNeeded('RIWAYAT_PINDAH_INSTANSI', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteRiwayatPindahInstansi(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteRiwayatPindahInstansi.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteRiwayatPindahInstansi(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteRiwayatPindahInstansi.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/riwayat_pindah_instansi/:personel_uid')
  @RolePermissionNeeded('RIWAYAT_PINDAH_INSTANSI', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createRiwayatPindahInstansi(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createRiwayatPindahInstansi.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createRiwayatPindahInstansi(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createRiwayatPindahInstansi.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/kgb/:id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_UPDATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async updateKGB(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.updateKGB.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.updateKGB(req, id, body);
    this.logger.log(
      `Leaving ${this.updateKGB.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('/kgb/:id')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_DELETE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async deleteKGB(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.deleteKGB.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.deleteKGB(req, id, body);
    this.logger.log(
      `Leaving ${this.deleteKGB.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/kgb/:personel_uid')
  @RolePermissionNeeded('KENAIKAN_GAJI_BERKALA', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async createKGB(
    @Req() req: any,
    @Param('personel_uid') personel_uid: string,
    @Body() body: any,
  ) {
    this.logger.log(
      `Entering ${this.createKGB.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.sipp3Service.createKGB(req, personel_uid, body);
    this.logger.log(
      `Leaving ${this.createKGB.name} with uid: ${personel_uid} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
