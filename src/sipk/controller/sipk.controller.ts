import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpCode,
  Logger,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Req,
  Res,
  UnprocessableEntityException,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Cron } from '@nestjs/schedule';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { Permission } from '../../core/decorators';
import {
  EditPenilaiDataDto,
  EditPersonelDataDto,
  GetDetailAydDto,
  GetJabatanListDto,
  GetJenisPenghargaan,
  GetPenilaianListDto,
  HukumanDto,
  KontrakKerjaDto,
  PenghargaanDto,
  PenilaianRekanDto,
  PilihPejabatPenilaiDto,
  TugasTambahanDto,
  VerifikasiDto,
  VerifikasiWithDataDto,
} from '../dto/sipk.dto';
import { SipkService } from '../service/sipk.service';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';

@Controller('sipk')
@UsePipes(
  new ValidationPipe({
    stopAtFirstError: true,
    transform: true,
    exceptionFactory: (errors) => {
      console.dir(errors, { depth: null });
      const mapped = {};
      errors.map((error) => {
        const msg = Object.values(error.constraints);
        mapped[error.property] = msg[0];
      });

      throw new UnprocessableEntityException({ status: false, error: mapped });
    },
  }),
)
// @module(MODULES)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class SipkController {
  private readonly logger = new Logger(SipkController.name);

  constructor(private readonly sipkService: SipkService) {}

  @Get('semester-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getSemesterList(@Req() req: any) {
    this.logger.log(`Entering ${this.getSemesterList.name}`);
    const response = await this.sipkService.getSemester(req);
    this.logger.log(`Leaving ${this.getSemesterList.name}`);

    return response;
  }

  @Get('semester-ayd-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getSemesterAydList(@Req() req: any) {
    this.logger.log(`Entering ${this.getSemesterAydList.name}`);
    const response = await this.sipkService.getSemesterAyd(req);
    this.logger.log(`Leaving ${this.getSemesterAydList.name}`);

    return response;
  }

  @Get('semester-rekan-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getSemesterRekanList(@Req() req: any) {
    this.logger.log(`Entering ${this.getSemesterRekanList.name}`);
    const response = await this.sipkService.getSemesterRekan(req);
    this.logger.log(`Leaving ${this.getSemesterRekanList.name}`);

    return response;
  }

  @Get('ayd-count')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getAydCount(@Req() req: any) {
    this.logger.log(`Entering ${this.getAydCount.name}`);
    const response = await this.sipkService.getAydCount(req);
    this.logger.log(
      `Leaving ${this.getAydCount.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('ayd-list/:semester_id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getAydList(@Req() req: any, @Param('semester_id') id: string) {
    this.logger.log(`Entering ${this.getAydList.name} with semester id: ${id}`);
    const response = await this.sipkService.getAyd(BigInt(id), req);
    this.logger.log(
      `Leaving ${this.getAydList.name} with semester id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('detail-saya/:semester_id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDetail(@Req() req: any, @Param('semester_id') id: string) {
    this.logger.log(`Entering ${this.getDetail.name} with semester id: ${id}`);

    const response = await this.sipkService.getDetail(
      BigInt(id),
      req.user?.['personel_id'],
      req,
      null,
    );
    this.logger.log(
      `Leaving ${this.getDetail.name} with semester id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('detail-ayd/:semester_id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDetailAyd(
    @Req() req: any,
    @Param('semester_id') id: string,
    @Query() query: GetDetailAydDto,
  ) {
    this.logger.log(
      `Entering ${this.getDetailAyd.name} with semester id: ${id} and query: ${JSON.stringify(query)}`,
    );
    const response = await this.sipkService.getDetail(
      BigInt(id),
      query.personel_id,
      req,
      req.user?.['personel_id'],
    );
    this.logger.log(
      `Leaving ${this.getDetailAyd.name} with semester id: ${id} and query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('rekan-dinilai/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async rekanDinilai(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.rekanDinilai.name} with id: ${id}`);
    const response = await this.sipkService.getRekan(BigInt(id), req);
    this.logger.log(
      `Leaving ${this.rekanDinilai.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('rekan-dinilai-count/:semester_id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async rekanDinilaiCount(@Req() req: any, @Param('semester_id') id: string) {
    this.logger.log(
      `Entering ${this.rekanDinilaiCount.name} with semester id: ${id}`,
    );
    const response = await this.sipkService.getRekanCount(BigInt(id), req);
    this.logger.log(
      `Leaving ${this.rekanDinilaiCount.name} with semester id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('jenis-penghargaan')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async jenisPenghargaan(@Req() req: any, @Query() query: GetJenisPenghargaan) {
    this.logger.log(
      `Entering ${this.jenisPenghargaan.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.sipkService.getJenisPenghargaan(query, req);
    this.logger.log(
      `Leaving ${this.jenisPenghargaan.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('penilaian-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async penilaianList(@Req() req: any, @Query() query: GetPenilaianListDto) {
    this.logger.log(
      `Entering ${this.penilaianList.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.sipkService.getPenilaianList(query, req);
    this.logger.log(
      `Leaving ${this.penilaianList.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('satker-filter-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async satkerList(@Req() req: any) {
    this.logger.log(`Entering ${this.satkerList.name}`);
    const response = await this.sipkService.getSatkerList(req);
    this.logger.log(
      `Leaving ${this.satkerList.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('semester-filter-list')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async semsterList(@Req() req: any) {
    this.logger.log(`Entering ${this.semsterList.name}`);
    const response = await this.sipkService.getSemesterList(req);
    this.logger.log(
      `Leaving ${this.semsterList.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('pangkat')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async pangkatList(@Req() req: any) {
    this.logger.log(`Entering ${this.pangkatList.name}`);
    const response = await this.sipkService.getPangkatList(req);
    this.logger.log(
      `Leaving ${this.pangkatList.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('jabatan')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async jabatanList(@Req() req: any, @Query() query: GetJabatanListDto) {
    this.logger.log(
      `Entering ${this.jabatanList.name} with query: ${JSON.stringify(query)}`,
    );
    const response = await this.sipkService.getJabatanList(query, req);
    this.logger.log(
      `Leaving ${this.jabatanList.name} with query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('detail/:penilaian_id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async detail(
    @Req() req: any,
    @Res() res: any,
    @Param('penilaian_id') id: string,
  ) {
    this.logger.log(`Entering ${this.detail.name} with penilaian id: ${id}`);
    const data = await this.sipkService.detail(BigInt(id), req);

    const filename = 'PDF Penilaian';

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.pdf`,
    );
    res.setHeader('Content-Length', data.byteLength);
    this.logger.log(`Leaving ${this.detail.name} with penilaian id: ${id}`);
    return res.end(data);
  }

  @Get('download-rekap')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async download(
    @Req() req: any,
    @Res() res: any,
    @Query() query: GetPenilaianListDto,
  ) {
    this.logger.log(
      `Entering ${this.download.name} with query: ${JSON.stringify(query)}`,
    );
    const data = await this.sipkService.downloadExcel(query, req);

    const filename = 'Data Penilaian';

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', data.byteLength);
    this.logger.log(
      `Leaving ${this.download.name} with query: ${JSON.stringify(query)}`,
    );
    return res.end(data);
  }

  @Post('set-pp/:semester_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async setPpp(
    @Req() req: any,
    @Body() body: PilihPejabatPenilaiDto,
    @Param('semester_id') id: string,
  ) {
    this.logger.log(
      `Entering ${this.setPpp.name} with semester id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.setPejabatPenilai(
      BigInt(id),
      body,
      req.user,
      req,
    );

    this.logger.log(
      `Leaving ${this.setPpp.name} with semester id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('send-to-verifier/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async sendToVerifier(@Req() req: any, @Param('penilaian_id') id: string) {
    this.logger.log(
      `Entering ${this.sendToVerifier.name} with penilaian id: ${id}`,
    );
    const response = await this.sipkService.sendToVerifier(
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.sendToVerifier.name} with penilaian id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('kontrak-kerja/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async createKontrakKerja(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: KontrakKerjaDto,
  ) {
    this.logger.log(
      `Entering ${this.createKontrakKerja.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.createKontrakKerja(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.createKontrakKerja.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Post('tugas-tambahan/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async createTugasTambahan(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: TugasTambahanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.createTugasTambahan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    body.personel_nrp = req.user?.['nrp'];
    const response = await this.sipkService.createTugasTambahan(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.createTugasTambahan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Post('penghargaan/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async penghargaan(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: PenghargaanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.penghargaan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    body.personel_nrp = req.user?.['nrp'];
    const response = await this.sipkService.createPenghargaan(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.penghargaan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Post('hukuman/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async hukuman(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: HukumanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: true,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.hukuman.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.createHukuman(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.hukuman.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('hukuman/submit/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async submitHukuman(@Req() req: any, @Param('penilaian_id') id: string) {
    this.logger.log(
      `Entering ${this.submitHukuman.name} with penilaian id: ${id}`,
    );
    const response = await this.sipkService.submitHukuman(
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.submitHukuman.name} with penilaian id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('set-done/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async done(@Req() req: any, @Param('penilaian_id') id: string) {
    this.logger.log(`Entering ${this.done.name} with penilaian id: ${id}`);
    const response = await this.sipkService.setDone(
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.done.name} with penilaian id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('penilaian-rekan/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async penilaianRekan(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: PenilaianRekanDto,
  ) {
    this.logger.log(
      `Entering ${this.penilaianRekan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.penilaianRekan(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.penilaianRekan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Permission('PERMISSION_CREATE')
  @Post('approval/kontrak-kerja/:penilaian_id')
  @HttpCode(200)
  async approvalKontrakKerja(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: VerifikasiDto,
  ) {
    this.logger.log(
      `Entering ${this.approvalKontrakKerja.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.verifyKontrakKerja(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.approvalKontrakKerja.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('approval/tugas-tambahan/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async approvalTugasTambahan(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: VerifikasiWithDataDto,
  ) {
    this.logger.log(
      `Entering ${this.approvalTugasTambahan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.verifyTugasTambahan(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.approvalTugasTambahan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('approval/penghargaan/:penilaian_id')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async approvalPenghargaan(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: VerifikasiWithDataDto,
  ) {
    this.logger.log(
      `Entering ${this.approvalPenghargaan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.verifyPenghargaan(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.approvalPenghargaan.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('edit-profile-saya/:penilaian_id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async editPersonelData(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: EditPersonelDataDto,
  ) {
    this.logger.log(
      `Entering ${this.editPersonelData.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.editPersonelData(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.editPersonelData.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('edit-profile-penilai/:penilaian_id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async editPenilaiData(
    @Req() req: any,
    @Param('penilaian_id') id: string,
    @Body() body: EditPenilaiDataDto,
  ) {
    this.logger.log(
      `Entering ${this.editPenilaiData.name} with penilaian id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.editPenilaiData(
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.editPenilaiData.name} with penilaian id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('kontrak-kerja/:penilaian_id/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateKontrakKerja(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
    @Body() body: KontrakKerjaDto,
  ) {
    this.logger.log(
      `Entering ${this.updateKontrakKerja.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.updateKontrakKerja(
      BigInt(penilaian_id),
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.updateKontrakKerja.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Put('tugas-tambahan/:penilaian_id/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateTugasTambahan(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
    @Body() body: TugasTambahanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.updateTugasTambahan.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    body.personel_nrp = req.user?.['nrp'];
    const response = await this.sipkService.updateTugasTambahan(
      BigInt(penilaian_id),
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.updateTugasTambahan.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Put('penghargaan/:penilaian_id/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updatePenghargaan(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
    @Body() body: PenghargaanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.updatePenghargaan.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    body.personel_nrp = req.user?.['nrp'];
    const response = await this.sipkService.updatePenghargaan(
      BigInt(penilaian_id),
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.updatePenghargaan.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @UseInterceptors(FileInterceptor('dokumen', { limits: { files: 1 } }))
  @Put('hukuman/:penilaian_id/:id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async updateHukuman(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
    @Body() body: HukumanDto,
    @UploadedFile(
      new ParseFilePipe({
        fileIsRequired: false,
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: Express.Multer.File,
  ) {
    this.logger.log(
      `Entering ${this.updateHukuman.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)}`,
    );
    body.dokumen = files;
    body.personel_id = req.user?.['personel_id'];
    const response = await this.sipkService.updateHukuman(
      BigInt(penilaian_id),
      BigInt(id),
      body,
      req,
    );
    this.logger.log(
      `Leaving ${this.updateHukuman.name} with penilaian id: ${penilaian_id} and id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('kontrak-kerja/:penilaian_id/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteKontrakKerja(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
  ) {
    this.logger.log(
      `Entering ${this.deleteKontrakKerja.name} with penilaian id: ${penilaian_id} and id: ${id}`,
    );
    const response = await this.sipkService.deleteKontrakKerja(
      BigInt(penilaian_id),
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.deleteKontrakKerja.name} with penilaian id: ${penilaian_id} and id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('tugas-tambahan/:penilaian_id/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteTugasTambahan(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
  ) {
    this.logger.log(
      `Entering ${this.deleteTugasTambahan.name} with penilaian id: ${penilaian_id} and id: ${id}`,
    );
    const response = await this.sipkService.deleteTugasTambahan(
      BigInt(penilaian_id),
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.deleteTugasTambahan.name} with penilaian id: ${penilaian_id} and id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('penghargaan/:penilaian_id/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deletePenghargaan(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
  ) {
    this.logger.log(
      `Entering ${this.deletePenghargaan.name} with penilaian id: ${penilaian_id} and id: ${id}`,
    );
    const response = await this.sipkService.deletePenghargaan(
      BigInt(penilaian_id),
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.deletePenghargaan.name} with penilaian id: ${penilaian_id} and id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete('hukuman/:penilaian_id/:id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async deleteHukuman(
    @Req() req: any,
    @Param('penilaian_id') penilaian_id: string,
    @Param('id') id: string,
  ) {
    this.logger.log(
      `Entering ${this.deleteHukuman.name} with penilaian id: ${penilaian_id} and id: ${id}`,
    );
    const response = await this.sipkService.deleteHukuman(
      BigInt(penilaian_id),
      BigInt(id),
      req.user?.['personel_id'],
      req,
    );
    this.logger.log(
      `Leaving ${this.deleteHukuman.name} with penilaian id: ${penilaian_id} and id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  // Cron job every end of month of march and december for generating semester data
  @Cron('0 0 30 3,12 *', { name: 'Generate Semester SIPK' })
  async generateSemester() {
    return this.sipkService.generateSemester(new Date());
  }
}
