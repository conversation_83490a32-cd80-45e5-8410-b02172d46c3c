import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from '../api-utils/minio/service/minio.service';
import { SipkController } from './controller/sipk.controller';
import { SipkService } from './service/sipk.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [SipkController],
  providers: [SipkService, MinioService],
})
export class SipkModule {}
