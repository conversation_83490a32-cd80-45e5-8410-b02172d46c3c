import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';
import * as moment from 'moment';
import * as puppeteer from 'puppeteer';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { arrayShuffling } from '../../core/utils/common.utils';
import {
  FooterTemplate,
  HeaderTemplate,
  PdfTemplate,
} from '../../core/templates/pdf-template';
import {
  EditPenilaiDataDto,
  EditPersonelDataDto,
  GetJabatanListDto,
  GetJenisPenghargaan,
  GetPenilaianList<PERSON>to,
  HukumanDto,
  Kontrak<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ian<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Tugas<PERSON>am<PERSON>,
  VerifikasiDto,
  VerifikasiWithDataDto,
} from '../dto/sipk.dto';
import { Workbook } from 'exceljs';
import {
  CONSTANT_HUKUMAN_SCORE,
  CONSTANT_PENILAIAN_STATUS,
} from '../../core/constants/sipk.constant';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class SipkService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly maxKontrakKerja = this.configService.get(
    'SIPK_MAX_KONTRAK_KERJA',
  );

  async getAydCount(req: any) {
    const personel_id = req.user?.['personel_id'];
    const queryResult = await this.prisma.sipk_penilaian.count({
      where: { penilai_id: personel_id },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAyd(semester_id: bigint, req: any) {
    const personel_id = req.user?.['personel_id'];

    const queryResult = await this.prisma.sipk_penilaian.findMany({
      where: { penilai_id: personel_id, semester_id },
      select: {
        id: true,
        status: true,
        personel_id: true,
        sipk_penilaian_rekan: {
          select: { nilai_total: true },
          where: { personel_id },
        },
        sipk_tugas_tambahan: {
          select: { status_verifikasi: true, id: true },
        },
        sipk_penghargaan: {
          select: { status_verifikasi: true, id: true },
          where: { source: { not: 'SIPP' } },
        },
      },
      orderBy: [{ id: 'asc' }],
    });

    await Promise.all(
      queryResult.map(async (penilaian) => {
        const [sipk_personel, personel] = await Promise.all([
          this.prisma.sipk_personel.findFirst({
            where: {
              penilaian_id: penilaian.id,
              personel_id: penilaian.personel_id,
            },
          }),
          this.prisma.personel.findFirst({
            where: { id: penilaian.personel_id },
            select: { nrp: true, foto_file: true },
          }),
        ]);

        penilaian['status_angka'] = penilaian.status;
        penilaian.status = CONSTANT_PENILAIAN_STATUS[penilaian.status];
        penilaian['nilai_perilaku'] =
          penilaian.sipk_penilaian_rekan?.reduce(
            (acc, curr) => acc + curr?.nilai_total,
            0,
          ) / penilaian.sipk_penilaian_rekan?.length || null;

        penilaian['personel'] = {
          ...sipk_personel,
          foto_file: await this.minioService.checkFileExist(
            process.env.MINIO_BUCKET_NAME!,
            `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
          ),
        };

        delete penilaian.sipk_penilaian_rekan;
      }),
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getSemester(req: any) {
    const personel_id = req.user?.['personel_id'];

    const [semester, penilaian] = await Promise.all([
      this.prisma.sipk_semester.findMany({
        orderBy: [{ tahun: 'desc' }, { semester: 'desc' }],
      }),
      this.prisma.sipk_penilaian.findMany({ where: { personel_id } }),
    ]);

    const penilaianMap = new Map();
    penilaian.map((p) => penilaianMap.set(p.semester_id, p));

    const res = semester.map((item, i) => {
      item['has_data'] = penilaianMap.has(item.id);

      if (!item['has_data'] && i > 0) return {};

      return item;
    });

    const queryResult = res.filter((d) => !!d?.['id']);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getSemesterAyd(req: any) {
    const personel_id = req.user?.['personel_id'];

    const [semester, penilaian] = await Promise.all([
      this.prisma.sipk_semester.findMany({
        orderBy: [{ tahun: 'desc' }, { semester: 'desc' }],
      }),
      this.prisma.sipk_penilaian.findMany({
        where: { penilai_id: personel_id },
      }),
    ]);

    const penilaianMap = new Map();
    penilaian.map((p) => {
      const exist = penilaianMap.get(p.semester_id);
      if (exist) penilaianMap.set(p.semester_id, exist + 1);
      else penilaianMap.set(p.semester_id, 1);
    });

    const res = [];
    semester.map((item) => {
      if (penilaianMap.has(item.id))
        res.push({ ...item, count: penilaianMap.get(item.id) || 0 });
    });

    const queryResult = res.filter((d) => !!d?.['id']);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getSemesterRekan(req: any) {
    const personel_id = req.user?.['personel_id'];

    const [semester, rekan] = await Promise.all([
      this.prisma.sipk_semester.findMany({
        orderBy: [{ tahun: 'desc' }, { semester: 'desc' }],
      }),
      this.prisma.sipk_penilaian_rekan.findMany({
        where: { personel_id },
        select: {
          id: true,
          sipk_penilaian: { select: { semester_id: true, penilai_id: true } },
        },
      }),
    ]);

    const dataMap = new Map();
    rekan.map((p) => {
      if (p.sipk_penilaian.penilai_id === personel_id) return;

      const exist = dataMap.get(p.sipk_penilaian.semester_id);
      if (exist) dataMap.set(p.sipk_penilaian.semester_id, exist + 1);
      else dataMap.set(p.sipk_penilaian.semester_id, 1);
    });

    const res = [];
    semester.map((item) => {
      if (dataMap.has(item.id))
        res.push({ ...item, count: dataMap.get(item.id) || 0 });
    });

    const queryResult = res.filter((d) => !!d?.['id']);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getRekan(id: bigint, req: any) {
    const personel_id = req.user?.['personel_id'];
    const queryResult = await this.prisma.sipk_penilaian_rekan.findFirst({
      where: { id, personel_id },
      include: {
        sipk_penilaian: {
          select: {
            id: true,
            sipk_semester: {
              select: { tahun: true, semester: true },
            },
          },
        },
        personel: { select: { foto_file: true } },
      },
    });

    const penilaian = await this.prisma.sipk_penilaian.findFirst({
      where: { id: queryResult.penilaian_id },
    });

    const personel = await this.prisma.sipk_personel.findFirst({
      where: { penilaian_id: penilaian.id, personel_id: penilaian.personel_id },
    });

    queryResult.personel = { ...queryResult.personel, ...personel };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getRekanCount(semester_id: bigint, req: any) {
    const personel_id = req.user?.['personel_id'];
    const queryResult = await this.prisma.sipk_penilaian_rekan.findFirst({
      where: { sipk_penilaian: { semester_id }, personel_id },
      select: { id: true },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getJenisPenghargaan(payload: GetJenisPenghargaan, req: any) {
    const where = {};
    if (payload.search) {
      where['nama'] = { contains: payload.search, mode: 'insensitive' };
    }

    const queryResult = await this.prisma.penghargaan.findMany({
      where,
      select: {
        id: true,
        nama: true,
        penghargaan_tingkat: { select: { nama: true } },
      },
      take: 15,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getPenilaianList(payload: GetPenilaianListDto, req: any) {
    const lastSemester = await this.prisma.sipk_semester.findFirst({
      orderBy: [{ tahun: 'desc' }, { semester: 'desc' }],
    });

    const fields = [
      { field: 's.tahun', value: '$1' },
      { field: 's.semester', value: '$2' },
    ];
    const values: Array<any> = [
      payload.tahun || lastSemester.tahun,
      payload.semester || lastSemester.semester,
    ];

    const limit = +payload.limit || 100;
    const page = +payload.page || 1;

    if (payload.keyword) {
      fields.push({
        field: `(personel.nama iLike '%${payload.keyword}%' OR personel.nrp like '%${payload.keyword}%' OR personel.pangkat iLike '%${payload.keyword}%')`,
        value: '',
      });
    }
    if (payload.satker) {
      fields.push({ field: 'fs.fungsi_id', value: '$3' });
      values.push(payload.satker);
    }
    if (payload.status) {
      if (payload.status === 'belum_mengisi') {
        fields.push({ field: 'penilaian.nilai_akhir = 0', value: '' });
      }
      if (payload.status === 'sudah_mengisi') {
        fields.push({ field: 'penilaian.nilai_akhir > 0', value: '' });
      }
    }

    const whereClause = Prisma.raw(
      `WHERE ${fields.map((f) => (f.value ? `${f.field} = ${f.value}` : f.field)).join(' AND ')}`,
    );

    let queryList = Prisma.sql`
        SELECT personel.*,
               penilaian.id,
               s.tahun,
               s.semester,
               penilaian.nilai_fs,
               penilaian.nilai_fa,
               penilaian.nilai_akhir
        FROM sipk_penilaian penilaian
                 INNER JOIN sipk_personel personel
                            ON personel.personel_id = penilaian.personel_id AND personel.penilaian_id = penilaian.id
                 LEFT JOIN fungsi_satuan fs
                           ON fs.satuan_id = personel.satuan_id
                 INNER JOIN sipk_semester s
                            ON s.id = penilaian.semester_id
            ${whereClause} ${Prisma.raw(`OFFSET $${values.length + 1}`)}
            ${Prisma.raw(`LIMIT $${values.length + 2}`)}
    `;

    let queryCount = Prisma.sql`
        SELECT COUNT(penilaian.id)
        FROM sipk_penilaian penilaian
                 INNER JOIN sipk_personel personel
                            ON personel.personel_id = penilaian.personel_id AND personel.penilaian_id = penilaian.id
                 LEFT JOIN fungsi_satuan fs
                           ON fs.satuan_id = personel.satuan_id
                 INNER JOIN sipk_semester s
                            ON s.id = penilaian.semester_id
            ${whereClause}
    `;

    queryCount = { ...queryCount, values } as any;

    values.push(limit * (page - 1));
    values.push(limit);

    queryList = { ...queryList, values } as any;

    const [totalData, data] = await this.prisma.$transaction([
      this.prisma.$queryRaw(queryCount),
      this.prisma.$queryRaw(queryList),
    ]);

    const totalPage = Math.ceil(Number(totalData[0]?.count) / limit);
    const queryResult = {
      data,
      page,
      totalPage,
      totalData: totalData[0]?.count,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getSatkerList(req: any) {
    const queryResult = await this.prisma.fungsi.findMany({
      select: { id: true, nama: true },
      orderBy: [{ nama: 'asc' }],
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getSemesterList(req: any) {
    const data = await this.prisma.sipk_semester.findMany({
      orderBy: [{ tahun: 'desc' }, { semester: 'desc' }],
    });

    const queryResult = {
      tahun: [...new Set(data.map((d) => d.tahun))],
      semester: [...new Set(data.map((d) => d.semester))],
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data,
    };
  }

  async getPangkatList(req: any) {
    const queryResult = await this.prisma.pangkat.findMany({
      orderBy: [{ nama_singkat: 'desc' }],
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getJabatanList(payload: GetJabatanListDto, req: any) {
    const where = {};
    if (payload.name) {
      where['nama'] = { contains: payload.name, mode: 'insensitive' };
    }

    const queryResult = await this.prisma.jabatan.findMany({
      where,
      select: { id: true, nama: true },
      take: 15,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async detail(id: bigint, req: any) {
    const penilaian = await this.prisma.sipk_penilaian.findFirst({
      where: { id },
      include: { sipk_semester: true },
    });

    const [
      dinilai,
      penilai,
      kontrak_kerja,
      tugas_tambahan,
      penilaian_rekan,
      penghargaan,
      hukuman,
    ] = await this.prisma.$transaction([
      this.prisma.sipk_personel.findFirst({
        where: { penilaian_id: id, personel_id: penilaian.personel_id },
      }),
      this.prisma.sipk_personel.findFirst({
        where: { penilaian_id: id, personel_id: penilaian.penilai_id },
      }),
      this.prisma.sipk_kontrak_kerja.findMany({
        where: { penilaian_id: id },
        orderBy: [{ id: 'desc' }],
      }),
      this.prisma.sipk_tugas_tambahan.findMany({
        where: { penilaian_id: id, status_verifikasi: 'APPROVE' },
        orderBy: [{ id: 'desc' }],
      }),
      this.prisma.sipk_penilaian_rekan.findMany({
        where: { penilaian_id: id },
      }),
      this.prisma.sipk_penghargaan.findMany({
        where: { penilaian_id: id, status_verifikasi: 'APPROVE' },
        orderBy: [{ id: 'desc' }],
      }),
      this.prisma.sipk_hukuman.findMany({
        where: { penilaian_id: id, poin: { not: null } },
        orderBy: [{ id: 'desc' }],
      }),
    ]);

    const fungsi_dinilai = await this.prisma.fungsi_satuan.findFirst({
      where: { satuan_id: dinilai.satuan_id },
    });
    const fungsi = await this.prisma.fungsi.findFirst({
      where: { id: fungsi_dinilai.fungsi_id },
    });
    dinilai['satuan'] = fungsi.nama;

    if (penilai.satuan_id) {
      const fungsi_penilai = await this.prisma.fungsi_satuan.findFirst({
        where: { satuan_id: penilai.satuan_id },
      });
      const fungsi = await this.prisma.fungsi.findFirst({
        where: { id: fungsi_penilai.fungsi_id },
      });
      penilai['satuan'] = fungsi.nama;
    }

    const summary = {
      tw1: 0,
      tw2: 0,
      kk: 0,
      tt: 0,
      pp: 0,
      rk: 0,
      har: 0,
      kum: 0,
      fs: penilaian.nilai_fs,
      fg: penilaian.nilai_fa,
      na: penilaian.nilai_akhir,
    };

    const kk = {
      tw1: { pencapaian: 0, target: 0 },
      tw2: { pencapaian: 0, target: 0 },
    };
    kontrak_kerja.map((d) => {
      kk.tw1.pencapaian += d.pencapaian_tw_1;
      kk.tw1.target += d.target_tw_1;
      kk.tw2.pencapaian += d.pencapaian_tw_2;
      kk.tw2.target += d.target_tw_2;
      summary.kk += d.poin;
    });

    summary.tw1 = (kk.tw1.pencapaian / kk.tw1.target) * 100;
    summary.tw2 = (kk.tw2.pencapaian / kk.tw2.target) * 100;

    tugas_tambahan.map((d) => {
      if (summary.tt >= 100) return;
      summary.tt += d.poin;
    });

    penilaian_rekan.map((d) => {
      if (d.personel_id === penilaian.penilai_id)
        summary.pp = d.nilai_total || 0;
      else summary.rk = d.nilai_total || 0;
    });

    penghargaan.map((d) => (summary.har += d.poin));
    hukuman.map((d) => (summary.kum += d.poin));

    const queryResult = {
      dinilai,
      penilai,
      kontrak_kerja,
      tugas_tambahan,
      penilaian_rekan,
      penghargaan,
      hukuman,
      summary,
      semester: penilaian.sipk_semester,
    };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_WRITE_PDF as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return await this._generatePdf(queryResult);
  }

  async downloadExcel(payload: GetPenilaianListDto, req: any) {
    const fields = [
      { field: 's.tahun', value: '$1' },
      { field: 's.semester', value: '$2' },
    ];
    const values: Array<any> = [payload.tahun, payload.semester];

    if (payload.keyword) {
      fields.push({
        field: `(personel.nama iLike '%${payload.keyword}%' OR personel.nrp like '%${payload.keyword}%' OR personel.pangkat iLike '%${payload.keyword}%')`,
        value: '',
      });
    }
    if (payload.satker) {
      fields.push({ field: 'fs.fungsi_id', value: '$3' });
      values.push(payload.satker);
    }
    if (payload.status) {
      if (payload.status === 'belum_mengisi') {
        fields.push({ field: 'penilaian.nilai_akhir = 0', value: '' });
      }
      if (payload.status === 'sudah_mengisi') {
        fields.push({ field: 'penilaian.nilai_akhir > 0', value: '' });
      }
    }

    let query = Prisma.sql`
        SELECT personel.*,
               penilaian.id,
               s.tahun,
               s.semester,
               penilaian.nilai_fs,
               penilaian.nilai_fa,
               penilaian.nilai_akhir
        FROM sipk_penilaian penilaian
                 INNER JOIN sipk_personel personel
                            ON personel.personel_id = penilaian.personel_id AND personel.penilaian_id = penilaian.id
                 LEFT JOIN fungsi_satuan fs
                           ON fs.satuan_id = personel.satuan_id
                 INNER JOIN sipk_semester s
                            ON s.id = penilaian.semester_id
            ${Prisma.raw(`WHERE ${fields.map((f) => (f.value ? `${f.field} = ${f.value}` : f.field)).join(' AND ')}`)}
    `;

    query = { ...query, values } as any;

    const queryResult = await this.prisma.$queryRaw<Array<any>>(query);

    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Penilaian');
    const header = [
      'No',
      'NRP',
      'Pangkat',
      'Nama',
      'Jabatan',
      'Tahun',
      'Semester',
      'FS',
      'FG',
      'NA',
    ];
    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 13,
      };
    });

    queryResult.map((data, index) => {
      worksheet.addRow([
        index + 1,
        data.nrp,
        data.pangkat,
        data.nama,
        data.jabatan,
        data.tahun,
        data.semester,
        data.nilai_fs,
        data.nilai_fa,
        data.nilai_akhir,
      ]);
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_WRITE_EXCEL as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return await workbook.xlsx.writeBuffer();
  }

  async getDetail(
    semester_id: bigint,
    personel_id: number,
    req: any,
    penilai_id?: number,
  ) {
    try {
      const penilaian = await this.prisma.sipk_penilaian.findFirst({
        where: { semester_id, personel_id },
        include: {
          sipk_semester: {
            select: {
              id: true,
              semester: true,
              tahun: true,
              tgl_mulai: true,
              tgl_selesai: true,
            },
          },
        },
      });

      if (!penilaian) return {};

      const queryResult = await this.prisma.$transaction(async (trx) => {
        const mock: Record<string, any> = {};
        await this._calculateScore(
          penilaian.id,
          penilaian.penilai_id,
          mock,
          trx,
        );
        // });
        // const mock: Record<string, any> = {};
        // await this._calculateScore(penilaian.id, penilaian.penilai_id, mock, trx);

        const updated = await this.prisma.sipk_penilaian.update({
          where: { id: penilaian.id },
          data: mock,
        });
        penilaian.nilai_fs = updated.nilai_fs;
        penilaian.nilai_fa = updated.nilai_fa;
        penilaian.nilai_akhir = updated.nilai_akhir;

        let wherePenilaianRekan;
        if (penilai_id) {
          wherePenilaianRekan = {
            personel_id: penilai_id,
            penilaian_id: penilaian.id,
          };
        } else {
          wherePenilaianRekan = {
            personel_id,
            sipk_penilaian: { semester_id },
          };
        }

        const [
          kontrak_kerja,
          tugas_tambahan,
          penghargaan,
          nilai_saya,
          penilaian_rekan,
          // hukuman,
          profile_dinilai,
          profile_penilai,
        ] = await Promise.all([
          trx.sipk_kontrak_kerja.findMany({
            where: { penilaian_id: penilaian.id },
            orderBy: [{ id: 'asc' }],
          }),
          trx.sipk_tugas_tambahan.findMany({
            where: { penilaian_id: penilaian.id },
            select: {
              id: true,
              penilaian_id: true,
              tgl_sprin: true,
              no_sprin: true,
              uraian_sprin: true,
              status_verifikasi: true,
              poin: true,
              rejected_note: true,
              sipk_tugas_tambahan_dokumen: {
                select: { id: true, url: true, filename: true },
              },
            },
            orderBy: [{ id: 'asc' }],
          }),
          trx.sipk_penghargaan.findMany({
            where: { penilaian_id: penilaian.id },
            select: {
              id: true,
              penilaian_id: true,
              no_kep: true,
              tgl_kep: true,
              uraian_kep: true,
              status_verifikasi: true,
              poin: true,
              surat_file: true,
              source: true,
              rejected_note: true,
              sipk_penghargaan_dokumen: {
                select: { id: true, url: true, filename: true },
              },
              jenis_penghargaan: {
                select: {
                  nama: true,
                  penghargaan_tingkat: { select: { nama: true } },
                },
              },
            },
            orderBy: [{ id: 'asc' }],
          }),
          trx.sipk_penilaian_rekan.findMany({
            where: { penilaian_id: penilaian.id },
          }),
          trx.sipk_penilaian_rekan.findMany({
            where: wherePenilaianRekan,
          }),
          // trx.sipk_hukuman.findMany({
          //   where: { penilaian_id: penilaian.id },
          //   select: {
          //     id: true,
          //     penilaian_id: true,
          //     jenis_hukuman: true,
          //     no_kep: true,
          //     tgl_kep: true,
          //     uraian_kep: true,
          //     poin: true,
          //     sipk_hukuman_dokumen: {
          //       select: { id: true, url: true, filename: true },
          //     },
          //   },
          //   orderBy: [{ id: 'asc' }],
          // }),
          trx.sipk_personel.findFirst({
            where: { penilaian_id: penilaian.id, personel_id },
            select: {
              id: true,
              nrp: true,
              nama: true,
              pangkat: true,
              pangkat_id: true,
              jabatan: true,
              jabatan_id: true,
              personel: { select: { foto_file: true } },
            },
          }),
          trx.sipk_personel.findFirst({
            where: {
              penilaian_id: penilaian.id,
              personel_id: penilaian.penilai_id,
            },
            select: {
              id: true,
              nrp: true,
              nama: true,
              pangkat: true,
              pangkat_id: true,
              jabatan: true,
              jabatan_id: true,
              personel: { select: { foto_file: true } },
            },
          }),
        ]);

        if (!penilaian_rekan?.length) {
          const rekan = await trx.sipk_penilaian.findMany({
            where: {
              penilai_id: penilaian.penilai_id,
              semester_id,
              NOT: { status: { in: [0, 2] } },
            },
          });

          if (rekan.length >= 3) {
            const personelIds = rekan.map((d) => d.personel_id);
            const shuffled = arrayShuffling(personelIds);

            await trx.sipk_penilaian_rekan.createMany({
              data: rekan.map((item, index) => ({
                penilaian_id: item.id,
                personel_id: shuffled[index],
              })),
              skipDuplicates: true,
            });
          }
          const newData = await trx.sipk_penilaian_rekan.findFirst({
            where: { personel_id, sipk_penilaian: { semester_id } },
          });
          if (!penilai_id && newData?.id) penilaian_rekan.push(newData);
        }

        let pencapaian_tw_1 = 0;
        let pencapaian_tw_2 = 0;
        let kontrak_kerja_length = 0;
        kontrak_kerja.map((item) => {
          pencapaian_tw_1 += (item.pencapaian_tw_1 / item.target_tw_1) * 100;
          pencapaian_tw_2 += (item.pencapaian_tw_2 / item.target_tw_2) * 100;
          kontrak_kerja_length++;
        });

        const rekan_saya = { sudah_dinilai: 0, total: 0 };
        penilaian_rekan.map((item) => {
          if (item?.nilai_total !== null) {
            rekan_saya.sudah_dinilai++;
          }
          rekan_saya.total++;
        });

        const summary = {
          kontrak_kerja: kontrak_kerja_length,
          pencapaian_tw_1: pencapaian_tw_1
            ? pencapaian_tw_1 / kontrak_kerja_length
            : 0,
          pencapaian_tw_2: pencapaian_tw_2
            ? pencapaian_tw_2 / kontrak_kerja_length
            : 0,
          tugas_tambahan: tugas_tambahan.length,
          perilaku_saya:
            nilai_saya.reduce((acc, curr) => acc + curr.nilai_total, 0) /
              nilai_saya.length || 0,
          penilaian_rekan: rekan_saya,
          penghargaan: penghargaan.length,
          // pelanggaran: hukuman.reduce((acc, curr) => acc + curr.poin, 0),
        };

        profile_dinilai['avatar'] = await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}${profile_dinilai.nrp}/${profile_dinilai.personel.foto_file}`,
        );
        profile_penilai['avatar'] = await this.minioService.checkFileExist(
          process.env.MINIO_BUCKET_NAME!,
          `${process.env.MINIO_PATH_FILE}${profile_penilai.nrp}/${profile_penilai.personel.foto_file}`,
        );
        profile_penilai['personel_id'] = penilaian.penilai_id;

        delete profile_dinilai.personel;
        delete profile_penilai.personel;

        const status = CONSTANT_PENILAIAN_STATUS[penilaian.status];
        const filled_other_data = [];
        if (/lainnya$/.test(status)) {
          const [isTugasTambahan, isPenghargaan] = await Promise.all([
            tugas_tambahan.find((d: any) =>
              ['WAITING', 'REJECT'].includes(d.status_verifikasi),
            ),
            penghargaan.find((d: any) =>
              ['WAITING', 'REJECT'].includes(d.status_verifikasi),
            ),
          ]);

          if (!!isTugasTambahan?.id) filled_other_data.push('tugas_tambahan');
          if (!!isPenghargaan?.id) filled_other_data.push('penghargaan');
        }

        return {
          penilaian_id: penilaian.id,
          total_poin: penilaian.nilai_akhir || 0,
          status,
          filled_other_data,
          rejected_reason: penilaian.rejected_reason,
          semester: penilaian.sipk_semester,
          summary,
          profile_dinilai,
          profile_penilai,
          kontrak_kerja_list: kontrak_kerja,
          tugas_tambahan_list: tugas_tambahan,
          nilai_saya_list: nilai_saya,
          penilaian_rekan_list: penilaian_rekan,
          penghargaan_list: penghargaan,
          // hukuman_list: hukuman,
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPK_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async setPejabatPenilai(
    semester_id: bigint,
    payload: PilihPejabatPenilaiDto,
    user: Record<string, any>,
    req: any,
  ) {
    try {
      const [exist, penilai_diluar_struktur, penilai] =
        await this.prisma.$transaction([
          this.prisma.sipk_penilaian.findFirst({
            where: { semester_id, personel_id: payload.personel_id },
            include: { sipk_semester: true },
          }),
          this.prisma.sipk_pejabat_diluar_struktur.findFirst({
            where: { nik: payload.penilai_nrp },
          }),
          this.prisma.personel.findFirst({
            where: { nrp: payload.penilai_nrp },
            select: {
              id: true,
              nama_lengkap: true,
              nrp: true,
            },
          }),
        ]);

      const pejabat = penilai || penilai_diluar_struktur;

      const [latestPenilaiPangkat, latestPenilaiJabatan] = await Promise.all([
        this.prisma.$queryRaw(Prisma.sql`
            SELECT pangkat_id, pangkat
            FROM mv_pangkat_terakhir
            WHERE personel_id = ${pejabat.id}
            ORDER BY tmt DESC
            LIMIT 1
          `),
        this.prisma.$queryRaw(Prisma.sql`
            SELECT jabatan_id, jabatan, satuan_id
            FROM mv_jabatan_terakhir
            WHERE personel_id = ${pejabat.id}
            ORDER BY tmt DESC
            LIMIT 1
          `),
      ]);

      if (!pejabat) {
        throw new BadRequestException(
          `Pejabat penilai dengan nrp/nik ${payload.penilai_nrp} tidak ditemukan`,
        );
      }

      const penilaiHandleCount = await this.prisma.sipk_penilaian.count({
        where: { penilai_id: pejabat.id, semester_id },
      });
      if (penilaiHandleCount === 3) {
        throw new BadRequestException(
          'Pejabat penilai sudah tidak dapat menerima anggota yang dinilai lagi',
        );
      }

      if (exist) {
        if (exist.status > 3) {
          throw new BadRequestException(
            'Anda tidak dapat mengganti pejabat penilai',
          );
        }

        return this.prisma.$transaction(
          async (trx) => {
            const [penilai_id, penghargaan] = await Promise.all([
              trx.sipk_personel.findFirst({
                where: {
                  penilaian_id: exist.id,
                  personel_id: { not: payload.personel_id },
                },
              }),
              trx.penghargaan_personel.findMany({
                where: {
                  personel_id: payload.personel_id,
                  tgl_penghargaan: {
                    gte: exist.sipk_semester.tgl_mulai,
                    lte: exist.sipk_semester.tgl_selesai,
                  },
                },
                include: {
                  penghargaan_tingkat: {
                    select: { nama: true, sipk_poin: true },
                  },
                  penghargaan: { select: { nama: true } },
                },
              }),
            ]);

            if (penghargaan?.length) {
              await trx.sipk_penghargaan.createMany({
                data: penghargaan.map((d) => ({
                  penilaian_id: exist.id,
                  jenis_penghargaan_id: d.penghargaan_id,
                  no_kep: d.surat_no,
                  tgl_kep: d.tgl_penghargaan,
                  uraian_kep:
                    d.penghargaan.nama + ' Oleh ' + d.penghargaan_tingkat.nama,
                  status_verifikasi: 'APPROVE',
                  poin: d.penghargaan_tingkat?.sipk_poin || 0,
                  source: 'SIPP',
                  surat_file: d.surat_file,
                })),
              });
            }

            await trx.sipk_personel.delete({ where: { id: penilai_id.id } });
            await trx.sipk_personel.create({
              data: {
                personel_id: pejabat.id,
                penilaian_id: exist.id,
                nama: pejabat['nama_lengkap'] || pejabat['nama'],
                nrp: pejabat['nrp'] || pejabat['nik'],
                satuan_id: latestPenilaiJabatan?.[0]?.satuan_id || null,
                pangkat_id: latestPenilaiPangkat?.[0]?.pangkat_id || null,
                pangkat: latestPenilaiPangkat?.[0]?.pangkat || null,
                jabatan_id: latestPenilaiJabatan?.[0]?.jabatan_id || null,
                jabatan: latestPenilaiJabatan?.[0]?.jabatan || null,
              },
            });

            return trx.sipk_penilaian.update({
              where: { id: exist.id },
              data: { penilai_id: pejabat.id },
            });
          },
          { timeout: 15000 },
        );
      }

      const queryResult = await this.prisma.$transaction(
        async (trx) => {
          const [penilaian, semester] = await Promise.all([
            trx.sipk_penilaian.create({
              data: {
                semester_id,
                personel_id: payload.personel_id,
                penilai_id: pejabat.id,
              },
            }),
            trx.sipk_semester.findFirst({ where: { id: semester_id } }),
          ]);

          const penghargaan = await trx.penghargaan_personel.findMany({
            where: {
              personel_id: payload.personel_id,
              tgl_penghargaan: {
                gte: semester.tgl_mulai,
                lte: semester.tgl_selesai,
              },
            },
            include: {
              penghargaan_tingkat: { select: { nama: true, sipk_poin: true } },
              penghargaan: { select: { nama: true } },
            },
          });

          if (penghargaan?.length) {
            await trx.sipk_penghargaan.createMany({
              data: penghargaan.map((d) => ({
                penilaian_id: penilaian.id,
                jenis_penghargaan_id: d.penghargaan_id,
                no_kep: d.surat_no,
                tgl_kep: d.tgl_penghargaan,
                uraian_kep:
                  d.penghargaan.nama + ' Oleh ' + d.penghargaan_tingkat.nama,
                status_verifikasi: 'APPROVE',
                poin: d.penghargaan_tingkat?.sipk_poin || 0,
                source: 'SIPP',
                surat_file: d.surat_file,
              })),
            });
          }

          await trx.sipk_personel.create({
            data: {
              personel_id: pejabat.id,
              penilaian_id: penilaian.id,
              nama: pejabat['nama_lengkap'] || pejabat['nama'],
              nrp: pejabat['nrp'] || pejabat['nik'],
              satuan_id: latestPenilaiJabatan?.[0]?.satuan_id || null,
              pangkat_id: latestPenilaiPangkat?.[0]?.pangkat_id || null,
              pangkat: latestPenilaiPangkat?.[0]?.pangkat || null,
              jabatan_id: latestPenilaiJabatan?.[0]?.jabatan_id || null,
              jabatan: latestPenilaiJabatan?.[0]?.jabatan || null,
            },
          });

          await trx.sipk_personel.create({
            data: {
              personel_id: user.personel_id,
              penilaian_id: penilaian.id,
              nama: user.nama_operator,
              nrp: user.nrp,
              satuan_id: user.satuan_id,
              pangkat_id: user.pangkat_id,
              pangkat: user.pangkat,
              jabatan_id: user.jabatan?.id,
              jabatan: user.jabatan?.nama,
            },
          });

          return penilaian;
        },
        { timeout: 15000 },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.SIPK_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.SIPK_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async sendToVerifier(penilaian_id: bigint, personel_id: number, req: any) {
    const exist = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      personel_id,
    );

    if (exist.status === 12) {
      throw new BadRequestException(
        'Penilaian sudah selesai, Anda tidak dapat merubah kembali',
      );
    }

    const [prefix] = CONSTANT_PENILAIAN_STATUS[exist.status].split('_');
    if (prefix === 'input') exist.status++;
    if (prefix === 'revisi') exist.status--;

    const queryResult = await this.prisma.sipk_penilaian.update({
      where: { id: exist.id },
      data: { status: exist.status },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async verifyKontrakKerja(
    penilaian_id: bigint,
    payload: VerifikasiDto,
    req: any,
  ) {
    const { exist, step } = await this._checkVerifyAuthorization(
      penilaian_id,
      payload.personel_id,
    );

    if (
      !['kontrak_kerja', 'pencapaian_tw_1', 'pencapaian_tw_2'].includes(step)
    ) {
      throw new BadRequestException(
        'Saat ini data penilaian tidak membutuhkan approval kontrak kerja',
      );
    }

    if (payload.status === 'approve') {
      exist.status += 2;
      exist.rejected_reason = '';
    }
    if (payload.status === 'reject') {
      exist.status++;
      exist.rejected_reason = payload.rejected_reason;
    }

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const data: Record<string, any> = {
        status: exist.status,
        rejected_reason: exist.rejected_reason,
      };
      if (payload.status === 'approve') {
        if (step === 'pencapaian_tw_2') {
          const kontrakKerjas = await trx.sipk_kontrak_kerja.findMany({
            where: { penilaian_id },
          });

          await Promise.all(
            kontrakKerjas.map(async (item) => {
              const pencapaian = item.pencapaian_tw_1 + item.pencapaian_tw_2;
              const target = item.target_tw_1 + item.target_tw_2;
              const percentage = pencapaian / target;

              return trx.sipk_kontrak_kerja.update({
                where: { id: item.id },
                data: { poin: Math.min(Math.round(percentage * 20), 20) },
              });
            }),
          );
        }
        if (step === 'kontrak_kerja') {
          await trx.sipk_penilaian_rekan.create({
            data: { penilaian_id, personel_id: payload.personel_id },
          });
        }
        await this._calculateScore(penilaian_id, exist.penilai_id, data, trx);
      }

      return trx.sipk_penilaian.update({
        where: { id: exist.id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async verifyTugasTambahan(
    penilaian_id: bigint,
    payload: VerifikasiWithDataDto,
    req: any,
  ) {
    const { exist, step } = await this._checkVerifyAuthorization(
      penilaian_id,
      payload.personel_id,
    );

    if (step !== 'lainnya') {
      throw new BadRequestException(
        'Saat ini data penilaian tidak membutuhkan approval tugas tambahan',
      );
    }

    const needVerifyData = await this.prisma.sipk_tugas_tambahan.findMany({
      where: { penilaian_id, status_verifikasi: 'WAITING' },
    });

    if (!needVerifyData?.length) {
      throw new BadRequestException(
        'Tidak ada data tugas tambahan yang perlu di verifikasi',
      );
    }

    const unverified = [];
    for (const item of needVerifyData) {
      if (!payload.data.find((d) => d.id === item.id)) unverified.push(item.id);
    }

    if (unverified.length) {
      throw new BadRequestException(
        'Masih terdapat data tugas tambahan yang belum diverifikasi. Silahkan pilih terlebih dahulu seluruh status verifikasi data penilaian',
      );
    }

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const mock = { status: 9, rejected_reason: '' };

      let hasReject = false;
      await Promise.all(
        payload.data.map(async (item) => {
          if (item.status === 'APPROVE') {
            return trx.sipk_tugas_tambahan.update({
              where: { id: item.id },
              data: { poin: 20, status_verifikasi: 'APPROVE' },
            });
          }
          if (item.status === 'REJECT') {
            hasReject = true;
            return trx.sipk_tugas_tambahan.update({
              where: { id: item.id },
              data: {
                poin: 0,
                status_verifikasi: 'REJECT',
                rejected_note: item.rejected_reason,
              },
            });
          }
        }),
      );

      if (hasReject) {
        mock.status = 11;
        // mock.rejected_reason = payload.rejected_reason;
      } else {
        await this._calculateScore(penilaian_id, exist.penilai_id, mock, trx);
      }

      return trx.sipk_penilaian.update({
        where: { id: penilaian_id },
        data: mock,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async verifyPenghargaan(
    penilaian_id: bigint,
    payload: VerifikasiWithDataDto,
    req: any,
  ) {
    const { exist, step } = await this._checkVerifyAuthorization(
      penilaian_id,
      payload.personel_id,
    );

    if (step !== 'lainnya') {
      throw new BadRequestException(
        'Saat ini data penilaian tidak membutuhkan approval penghargaan',
      );
    }

    const needVerifyData = await this.prisma.sipk_penghargaan.findMany({
      where: { penilaian_id, status_verifikasi: 'WAITING' },
    });

    if (!needVerifyData?.length) {
      throw new BadRequestException(
        'Tidak ada data penghargaan yang perlu di verifikasi',
      );
    }

    const semester = await this.prisma.sipk_semester.findFirst({
      where: { id: exist.semester_id },
    });

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const mock = { status: 9, rejected_reason: '' };

      let hasReject = false;
      await Promise.all(
        payload.data.map(async (item) => {
          if (item.status === 'APPROVE') {
            const penghargaan = await trx.sipk_penghargaan.findFirst({
              where: { id: item.id },
              include: {
                jenis_penghargaan: true,
                sipk_penghargaan_dokumen: true,
              },
            });
            if (penghargaan.source !== 'SIPP') {
              await trx.pengajuan_penghargaan.create({
                data: {
                  personel_id: exist.personel_id,
                  penghargaan_id: Number(penghargaan.jenis_penghargaan.id),
                  created_by: exist.personel_id,
                  files: penghargaan.sipk_penghargaan_dokumen.map((d) => ({
                    name: d.filename,
                    url: d.key,
                  })),
                  status: 'Data SIPK',
                  created_at: new Date(),
                },
              });
              await trx.penghargaan_personel.create({
                data: {
                  personel_id: exist.personel_id,
                  penghargaan_id: Number(penghargaan.jenis_penghargaan.id),
                  tingkat_id: penghargaan.jenis_penghargaan.tingkat_id,
                  tgl_penghargaan: penghargaan.tgl_kep,
                  surat_no: penghargaan.no_kep,
                  surat_file: penghargaan.sipk_penghargaan_dokumen?.[0]?.key,
                  penghargaan_file:
                    penghargaan.sipk_penghargaan_dokumen?.[0]?.key,
                  note: `Data SIPK semester ${semester.semester} tahun ${semester.tahun}`,
                },
              });
            }
            return trx.sipk_penghargaan.update({
              where: { id: item.id },
              data: { poin: 20, status_verifikasi: 'APPROVE' },
            });
          }
          if (item.status === 'REJECT') {
            hasReject = true;
            return trx.sipk_penghargaan.update({
              where: { id: item.id },
              data: {
                poin: 0,
                status_verifikasi: 'REJECT',
                rejected_note: item.rejected_reason,
              },
            });
          }
        }),
      );

      if (hasReject) {
        mock.status = 11;
        // mock.rejected_reason = payload.rejected_reason;
      } else {
        await this._calculateScore(penilaian_id, exist.penilai_id, mock, trx);
      }

      return trx.sipk_penilaian.update({
        where: { id: penilaian_id },
        data: mock,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async editPersonelData(
    penilaian_id: bigint,
    payload: EditPersonelDataDto,
    req: any,
  ) {
    await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    const exist = await this.prisma.sipk_personel.findFirst({
      where: { penilaian_id, personel_id: payload.personel_id },
    });

    const queryResult = await this.prisma.sipk_personel.update({
      where: { id: exist.id },
      data: {
        nama: payload.nama,
        jabatan: payload.jabatan,
        pangkat: payload.pangkat,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async editPenilaiData(
    penilaian_id: bigint,
    payload: EditPenilaiDataDto,
    req: any,
  ) {
    await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    const exist = await this.prisma.sipk_personel.findFirst({
      where: { penilaian_id, personel_id: { not: payload.personel_id } },
    });

    const queryResult = await this.prisma.sipk_personel.update({
      where: { id: exist.id },
      data: {
        nama: payload.nama,
        jabatan: payload.jabatan || exist.jabatan,
        jabatan_id: payload.jabatan_id || exist.jabatan_id,
        pangkat: payload.pangkat || exist.pangkat,
        pangkat_id: payload.pangkat_id || exist.pangkat_id,
        nrp: payload.nik,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createKontrakKerja(
    penilaian_id: bigint,
    payload: KontrakKerjaDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    const [prefix, ...step] =
      CONSTANT_PENILAIAN_STATUS[penilaian.status].split('_');
    const module = step.join('_');
    if (
      !['input', 'revisi'].includes(prefix) ||
      !['kontrak_kerja', 'pencapaian_tw_1', 'pencapaian_tw_2'].includes(module)
    ) {
      throw new BadRequestException(
        `Anda tidak dapat merubah ${module.replace(/_/g, ' ')} saat ini`,
      );
    }

    const existCount = await this.prisma.sipk_kontrak_kerja.count({
      where: { penilaian_id },
    });

    if (existCount > this.maxKontrakKerja) {
      throw new BadRequestException(
        `Kontrak kerja maksimal ${this.maxKontrakKerja}`,
      );
    }

    const queryResult = await this.prisma.sipk_kontrak_kerja.create({
      data: {
        penilaian_id,
        uraian: payload.uraian,
        indikator: payload.indikator,
        target_tw_1: payload.target_tw_1,
        pencapaian_tw_1: 0,
        target_tw_2: payload.target_tw_2,
        pencapaian_tw_2: 0,
        satuan: payload.satuan,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateKontrakKerja(
    penilaian_id: bigint,
    id: bigint,
    payload: KontrakKerjaDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    const [prefix, ...step] =
      CONSTANT_PENILAIAN_STATUS[penilaian.status].split('_');
    const module = step.join('_');
    if (
      !['input', 'revisi'].includes(prefix) ||
      !['kontrak_kerja', 'pencapaian_tw_1', 'pencapaian_tw_2'].includes(module)
    ) {
      throw new BadRequestException(
        `Anda tidak dapat merubah ${module.replace(/_/g, ' ')} saat ini`,
      );
    }

    const find = await this.prisma.sipk_kontrak_kerja.findFirst({
      where: { id },
    });

    if (!find) {
      throw new BadRequestException(`Data kontrak kerja ${id} tidak ditemukan`);
    }

    let data: Record<string, any> = { updated_at: new Date() };

    if (module === 'kontrak_kerja') {
      data = {
        uraian: payload.uraian,
        indikator: payload.indikator,
        target_tw_1: payload.target_tw_1,
        target_tw_2: payload.target_tw_2,
        satuan: payload.satuan,
      };
    }
    if (module === 'pencapaian_tw_1') {
      data.pencapaian_tw_1 = payload.pencapaian_tw_1;
    }
    if (module === 'pencapaian_tw_2') {
      data.pencapaian_tw_2 = payload.pencapaian_tw_2;
    }

    const queryResult = await this.prisma.sipk_kontrak_kerja.update({
      where: { id },
      data,
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteKontrakKerja(
    penilaian_id: bigint,
    id: bigint,
    personel_id: number,
    req: any,
  ) {
    await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      personel_id,
    );

    const queryResult = await this.prisma.sipk_kontrak_kerja.delete({
      where: { id },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createTugasTambahan(
    penilaian_id: bigint,
    payload: TugasTambahanDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    await this._validateInput(
      payload,
      penilaian.semester_id,
      'Tugas tambahan',
      'tgl_sprin',
    );
    this._checkStatus(penilaian.status, 'lainnya');

    const upload = await this._uploadFiles(
      payload.dokumen,
      payload.personel_nrp,
    );

    const queryResult = await this.prisma.sipk_tugas_tambahan.create({
      data: {
        no_sprin: payload.no_sprin,
        tgl_sprin: new Date(payload.tgl_sprin),
        uraian_sprin: payload.uraian_sprin,
        poin: null,
        sipk_tugas_tambahan_dokumen: {
          create: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        },
        sipk_penilaian: { connect: { id: penilaian_id } },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateTugasTambahan(
    penilaian_id: bigint,
    id: bigint,
    payload: TugasTambahanDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    await this._validateInput(
      { ...payload, id },
      penilaian.semester_id,
      'Tugas tambahan',
      'tgl_sprin',
      false,
    );
    this._checkStatus(penilaian.status, 'lainnya');

    const find = await this.prisma.sipk_tugas_tambahan.findFirst({
      where: { id },
    });

    if (!find) {
      throw new BadRequestException(
        `Data tugas tambahan ${id} tidak ditemukan`,
      );
    }

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const data: Record<string, any> = {
        no_sprin: payload.no_sprin,
        tgl_sprin: new Date(payload.tgl_sprin),
        uraian_sprin: payload.uraian_sprin,
        updated_at: new Date(),
        status_verifikasi: 'WAITING',
      };

      if (payload.dokumen) {
        const dokumen = await trx.sipk_tugas_tambahan_dokumen.findFirst({
          where: { tugas_tambahan_id: id },
        });
        const upload = await this._uploadFiles(
          payload.dokumen,
          payload.personel_nrp,
        );
        await trx.sipk_tugas_tambahan_dokumen.update({
          where: { id: dokumen.id },
          data: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        });
      }

      return trx.sipk_tugas_tambahan.update({
        where: { id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteTugasTambahan(
    penilaian_id: bigint,
    id: bigint,
    personel_id: number,
    req: any,
  ) {
    await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      personel_id,
    );

    const queryResult = await this.prisma.$transaction(async (trx) => {
      await trx.sipk_tugas_tambahan_dokumen.deleteMany({
        where: { tugas_tambahan_id: id },
      });
      await trx.sipk_tugas_tambahan.delete({ where: { id } });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createPenghargaan(
    penilaian_id: bigint,
    payload: PenghargaanDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    await this._validateInput(
      payload,
      penilaian.semester_id,
      'Penghargaan',
      'tgl_kep',
    );
    this._checkStatus(penilaian.status, 'lainnya');

    const upload = await this._uploadFiles(
      payload.dokumen,
      payload.personel_nrp,
    );

    const queryResult = await this.prisma.sipk_penghargaan.create({
      data: {
        no_kep: payload.no_kep,
        tgl_kep: new Date(payload.tgl_kep),
        uraian_kep: payload.uraian_kep,
        poin: 0,
        created_at: new Date(),
        updated_at: new Date(),
        sipk_penghargaan_dokumen: {
          create: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        },
        source: 'SIPK',
        sipk_penilaian: { connect: { id: penilaian_id } },
        jenis_penghargaan: { connect: { id: payload.jenis_penghargaan } },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updatePenghargaan(
    penilaian_id: bigint,
    id: bigint,
    payload: PenghargaanDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      payload.personel_id,
    );

    await this._validateInput(
      payload,
      penilaian.semester_id,
      'Penghargaan',
      'tgl_kep',
      false,
    );
    this._checkStatus(penilaian.status, 'lainnya');

    const find = await this.prisma.sipk_penghargaan.findFirst({
      where: { id },
    });

    if (!find) {
      throw new BadRequestException(`Data penghargaan ${id} tidak ditemukan`);
    }

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const data: Record<string, any> = {
        jenis_penghargaan: payload.jenis_penghargaan,
        no_kep: payload.no_kep,
        tgl_kep: new Date(payload.tgl_kep),
        uraian_kep: payload.uraian_kep,
        updated_at: new Date(),
      };

      if (payload.dokumen) {
        const dokumen = await trx.sipk_penghargaan_dokumen.findFirst({
          where: { penghargaan_id: id },
        });
        const upload = await this._uploadFiles(
          payload.dokumen,
          payload.personel_nrp,
        );
        await trx.sipk_penghargaan_dokumen.update({
          where: { id: dokumen.id },
          data: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        });
      }

      return trx.sipk_penghargaan.update({
        where: { id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deletePenghargaan(
    penilaian_id: bigint,
    id: bigint,
    personel_id: number,
    req: any,
  ) {
    await this._checkDataAuthorization(
      penilaian_id,
      'personel_id',
      personel_id,
    );

    const queryResult = await this.prisma.$transaction(async (trx) => {
      await trx.sipk_penghargaan_dokumen.deleteMany({
        where: { penghargaan_id: id },
      });
      await trx.sipk_penghargaan.delete({ where: { id } });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createHukuman(penilaian_id: bigint, payload: HukumanDto, req: any) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'penilai_id',
      payload.personel_id,
    );

    if (penilaian.status < 8) {
      throw new BadRequestException(
        'Personel belum menyelesaikan pencapaian triwulan 2',
      );
    }
    if (penilaian.status === 12) {
      throw new BadRequestException(
        'Data penilaian sudah selesai, tidak dapat dirubah kembali.',
      );
    }

    await this._validateInput(
      payload,
      penilaian.semester_id,
      'Hukuman',
      'tgl_kep',
    );

    const personel = await this.prisma.sipk_personel.findFirst({
      where: { penilaian_id, personel_id: { not: payload.personel_id } },
    });

    const upload = await this._uploadFiles(payload.dokumen, personel.nrp);

    const queryResult = await this.prisma.sipk_hukuman.create({
      data: {
        penilaian_id,
        jenis_hukuman: payload.jenis_hukuman,
        no_kep: payload.no_kep,
        tgl_kep: new Date(payload.tgl_kep),
        uraian_kep: payload.uraian_kep,
        created_at: new Date(),
        updated_at: new Date(),
        sipk_hukuman_dokumen: {
          create: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateHukuman(
    penilaian_id: bigint,
    id: bigint,
    payload: HukumanDto,
    req: any,
  ) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'penilai_id',
      payload.personel_id,
    );

    if (penilaian.status < 8) {
      throw new BadRequestException(
        'Personel belum menyelesaikan pencapaian triwulan 2',
      );
    }
    if (penilaian.status === 12) {
      throw new BadRequestException(
        'Data penilaian sudah selesai, tidak dapat dirubah kembali.',
      );
    }

    await this._validateInput(
      payload,
      penilaian.semester_id,
      'Hukuman',
      'tgl_kep',
      false,
    );

    const find = await this.prisma.sipk_hukuman.findFirst({
      where: { id },
    });

    if (!find) {
      throw new BadRequestException(`Data hukuman ${id} tidak ditemukan`);
    }

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const data: Record<string, any> = {
        jenis_hukuman: payload.jenis_hukuman,
        no_kep: payload.no_kep,
        tgl_kep: new Date(payload.tgl_kep),
        uraian_kep: payload.uraian_kep,
        updated_at: new Date(),
      };

      if (payload.dokumen) {
        const dokumen = await trx.sipk_hukuman_dokumen.findFirst({
          where: { hukuman_id: id },
        });
        const personel = await trx.sipk_personel.findFirst({
          where: { penilaian_id, personel_id: { not: payload.personel_id } },
        });

        const upload = await this._uploadFiles(payload.dokumen, personel.nrp);
        await trx.sipk_hukuman_dokumen.update({
          where: { id: dokumen.id },
          data: {
            ...upload.rawFile,
            key: upload.uploaded.Key,
            url: upload.uploaded.Location,
            filename: upload.uploaded.filename,
          },
        });
      }

      return trx.sipk_hukuman.update({
        where: { id },
        data,
      });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async submitHukuman(penilaian_id: bigint, personel_id: number, req: any) {
    const msg = 'Sukses mensubmit hukuman';
    const exist = await this._checkDataAuthorization(
      penilaian_id,
      'penilai_id',
      personel_id,
    );

    const queryResult = await this.prisma.$transaction(async (trx) => {
      const needUpdate = await trx.sipk_hukuman.findMany({
        where: { penilaian_id, poin: null },
      });

      if (!needUpdate?.length) return msg;

      const mock: Record<string, any> = { hukuman: 0 };
      await Promise.all(
        needUpdate.map(async (item) => {
          mock.hukuman += CONSTANT_HUKUMAN_SCORE[item.jenis_hukuman];
          await trx.sipk_hukuman.update({
            where: { id: item.id },
            data: {
              poin: CONSTANT_HUKUMAN_SCORE[item.jenis_hukuman],
              updated_at: new Date(),
            },
          });
        }),
      );

      await this._calculateScore(penilaian_id, exist.penilai_id, mock, trx);
      delete mock.hukuman;

      await trx.sipk_penilaian.update({
        where: { id: penilaian_id },
        data: mock,
      });

      return msg;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async deleteHukuman(
    penilaian_id: bigint,
    id: bigint,
    personel_id: number,
    req: any,
  ) {
    await this._checkDataAuthorization(penilaian_id, 'penilai_id', personel_id);

    const queryResult = await this.prisma.$transaction(async (trx) => {
      await trx.sipk_hukuman_dokumen.deleteMany({
        where: { hukuman_id: id },
      });
      await trx.sipk_hukuman.delete({ where: { id } });
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.DELETE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_DELETE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async setDone(penilaian_id: bigint, personel_id: number, req: any) {
    const penilaian = await this._checkDataAuthorization(
      penilaian_id,
      'penilai_id',
      personel_id,
    );

    const queryResult = await this.prisma.sipk_penilaian.update({
      where: { id: penilaian.id },
      data: { status: 16 },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async penilaianRekan(
    penilaian_id: bigint,
    payload: PenilaianRekanDto,
    req: any,
  ) {
    const exist = await this.prisma.sipk_penilaian.findFirst({
      where: { id: penilaian_id },
    });

    if (!exist) {
      throw new NotFoundException(
        `Penilaian id ${penilaian_id} tidak ditemukan`,
      );
    }

    const authorized = await this.prisma.sipk_penilaian_rekan.findFirst({
      where: { penilaian_id, personel_id: payload.personel_id },
    });

    if (!authorized) {
      throw new BadRequestException(
        'Anda tidak memiliki akses untuk menilai rekan Anda',
      );
    }

    const queryResult = await this.prisma.$transaction(
      async (trx) => {
        const data = {
          nilai_kepemimpinan: payload.nilai_kepemimpinan,
          nilai_pelayanan: payload.nilai_pelayanan,
          nilai_komunikasi: payload.nilai_komunikasi,
          nilai_pengendalian_emosi: payload.nilai_pengendalian_emosi,
          nilai_integritas: payload.nilai_integritas,
          nilai_empati: payload.nilai_empati,
          nilai_komitmen: payload.nilai_komitmen,
          nilai_inisiatif: payload.nilai_inisiatif,
          nilai_disiplin: payload.nilai_disiplin,
          nilai_kerjasama: payload.nilai_kerjasama,
          nilai_total:
            (payload.nilai_kepemimpinan +
              payload.nilai_pelayanan +
              payload.nilai_komunikasi +
              payload.nilai_pengendalian_emosi +
              payload.nilai_integritas +
              payload.nilai_empati +
              payload.nilai_komitmen +
              payload.nilai_inisiatif +
              payload.nilai_disiplin +
              payload.nilai_kerjasama) /
            10,
          updated_at: new Date(),
        };

        const mock: Record<string, any> = {};
        mock.perilaku_rekan = data.nilai_total;

        await this._calculateScore(penilaian_id, exist.penilai_id, mock, trx);
        delete mock.perilaku_rekan;

        await trx.sipk_penilaian.update({
          where: { id: penilaian_id },
          data: mock,
        });

        return trx.sipk_penilaian_rekan.update({
          where: { id: authorized.id },
          data,
        });
      },
      { timeout: 15000 },
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.SIPK_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.SIPK_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async generateSemester(date: Date) {
    const bulan = date.getMonth() + 1;
    const tahun = date.getFullYear();
    const semester = bulan === 3 ? 1 : 2;
    const mulaiBulan = semester === 1 ? 1 : 6;
    const selesaiBulan = semester === 1 ? 6 : 12;
    const selesaiTanggal = semester === 1 ? 30 : 31;

    try {
      return this.prisma.sipk_semester.create({
        data: {
          semester,
          tahun,
          tgl_mulai: new Date(tahun, mulaiBulan, 1),
          tgl_selesai: new Date(tahun, selesaiBulan, selesaiTanggal),
        },
      });
    } catch (err) {
      if (err.code === 'P2002') return;
      throw err;
    }
  }

  private async _checkDataAuthorization(
    penilaian_id: bigint,
    field: 'personel_id' | 'penilai_id',
    identifier: number,
  ) {
    const exist = await this.prisma.sipk_penilaian.findFirst({
      where: { id: penilaian_id },
    });

    if (!exist) {
      throw new NotFoundException(
        `Penilaian id ${penilaian_id} tidak ditemukan`,
      );
    }

    if (Number(exist[field]) != identifier) {
      throw new BadRequestException(
        'Anda tidak memiliki akses untuk penilaian ini',
      );
    }

    await this._checkPeriod(exist.semester_id);

    return exist;
  }

  private async _validateInput(
    payload: Record<string, any>,
    semester_id: bigint,
    errorMsg: string,
    dateField: string,
    requiredDocument = true,
  ) {
    const semester = await this.prisma.sipk_semester.findFirst({
      where: { id: semester_id },
    });

    if (!payload.id && !payload.dokumen && requiredDocument) {
      throw new BadRequestException(`Dokumen pada ${errorMsg} belum ada`);
    }
    if (
      new Date(payload[dateField]) < semester.tgl_mulai ||
      new Date(payload[dateField]) > semester.tgl_selesai
    ) {
      throw new BadRequestException(
        `Tanggal sprint hanya bisa antara ${moment(semester.tgl_mulai).format('DD-MMM-YYYY')} dan ${moment(semester.tgl_selesai).format('DD-MMM-YYYY')}`,
      );
    }
  }

  private _checkStatus(status: number, step: string) {
    const [prefix, ...module] = CONSTANT_PENILAIAN_STATUS[status].split('_');
    if (!['input', 'revisi'].includes(prefix) || module?.join('_') !== step) {
      throw new BadRequestException(
        `Anda tidak dapat merubah ${step.replace('_', ' ')} saat ini`,
      );
    }

    return true;
  }

  private async _checkPeriod(semester_id: bigint) {
    const semester = await this.prisma.sipk_semester.findFirst({
      where: { id: semester_id },
    });

    if (!semester) {
      throw new BadRequestException('Data semester tidak ditemukan');
    }

    if (semester.tgl_mulai > new Date()) {
      throw new BadRequestException('Semester penilaian belum dimulai');
    }

    if (semester.tgl_selesai < new Date()) {
      throw new BadRequestException(
        'Semester penilaian sudah berakhir. Data tidak dapat diubah',
      );
    }

    return true;
  }

  private async _checkVerifyAuthorization(
    penilaian_id: bigint,
    personel_id: number,
  ) {
    const exist = await this._checkDataAuthorization(
      penilaian_id,
      'penilai_id',
      personel_id,
    );

    const [prefix, ...step] =
      CONSTANT_PENILAIAN_STATUS[exist.status].split('_');
    if (prefix !== 'verifikasi') {
      throw new BadRequestException('Penilaian ini tidak membutuhkan approval');
    }

    return { exist, step: step.join('_') };
  }

  private async _uploadFiles(file: Express.Multer.File, nrp: string) {
    try {
      const uploaded = await this.minioService.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag) return { rawFile: file };

      return {
        rawFile: file,
        uploaded,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _calculateScore(
    penilaian_id: bigint,
    penilai_id: bigint,
    data: Record<string, any>,
    transaction: Prisma.TransactionClient,
  ) {
    const [
      kontrak_kerja_raw,
      tugas_tambahan_raw,
      penghargaan_raw,
      penilaian_rekan_raw,
      // hukuman_raw,
    ] = await Promise.all([
      transaction.$queryRaw<any>(
        Prisma.sql`SELECT SUM(poin) as poin FROM sipk_kontrak_kerja WHERE penilaian_id = ${penilaian_id}`,
      ),
      transaction.$queryRaw<any>(
        Prisma.sql`SELECT SUM(poin) as poin FROM sipk_tugas_tambahan WHERE penilaian_id = ${penilaian_id}`,
      ),
      transaction.$queryRaw<any>(
        Prisma.sql`SELECT SUM(poin) as poin FROM sipk_penghargaan WHERE penilaian_id = ${penilaian_id}`,
      ),
      transaction.$queryRaw<any>(
        Prisma.sql`SELECT nilai_total, personel_id FROM sipk_penilaian_rekan WHERE penilaian_id = ${penilaian_id}`,
      ),
      // transaction.$queryRaw<any>(
      //   Prisma.sql`SELECT SUM(poin) as poin FROM sipk_hukuman WHERE penilaian_id = ${penilaian_id}`,
      // ),
    ]);

    const kontrak_kerja = Number(kontrak_kerja_raw?.[0]?.poin || '0');
    const tugas_tambahan = Number(tugas_tambahan_raw?.[0]?.poin || '0');
    const penghargaan = Number(penghargaan_raw?.[0]?.poin || '0');
    let perilaku_atasan = 0;
    let perilaku_rekan = data.perilaku_rekan || 0;
    // let hukuman = Number(hukuman_raw?.[0]?.poin || '0');

    // if (data.hukuman) hukuman += data.hukuman;

    penilaian_rekan_raw.map((d) => {
      if (d.personel_id === penilai_id) {
        perilaku_atasan = d.nilai_total;
        return;
      }
      perilaku_rekan += d.nilai_total;
    });

    const point_kk = kontrak_kerja * 0.8;
    const point_tt = Math.min(tugas_tambahan, 100) * 0.2;
    const point_penghargaan = penghargaan * 0.2;
    const point_pa = perilaku_atasan * 0.6;
    const point_pr = perilaku_rekan * 0.2;

    data.nilai_fs = Math.round((point_kk + point_tt) * 0.6);
    data.nilai_fa = Math.round((point_penghargaan + point_pa + point_pr) * 0.4);
    data.nilai_akhir = data.nilai_fs + data.nilai_fa;
  }

  private async _generatePdf(data: Record<string, any>) {
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    try {
      const page = await browser.newPage();

      const header = HeaderTemplate();
      const footer = FooterTemplate();
      const body = PdfTemplate(data);

      await page.emulateMediaType('print');
      await page.setContent(body, { waitUntil: 'load' });

      const pdf = await page.pdf({
        format: 'a4',
        printBackground: true,
        headerTemplate: header,
        footerTemplate: footer,
        displayHeaderFooter: true,
        margin: { top: '20mm', left: '5mm', right: '5mm', bottom: '20mm' },
      });

      return Buffer.from(pdf.buffer);
    } catch (err) {
      console.log(err);
      throw new BadRequestException('Error while generating PDF');
    } finally {
      await browser.close();
    }
  }
}
