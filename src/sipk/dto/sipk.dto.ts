import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Matches,
  Max,
  <PERSON>ength,
  <PERSON>,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { PaginationDto } from '../../core/dtos';
import { CONSTANT_HUKUMAN_SCORE } from '../../core/constants/sipk.constant';

export const DateOnlyRegex = new RegExp(
  '\\d{4}-(1[0-2]|0[0-9])-(0[0-9]|[1-2][0-9]|3[0-1])',
  'g',
);

class PersonelData {
  personel_id: number;
  personel_nrp: string;
}

export class PilihPejabatPenilaiDto extends PersonelData {
  @IsNotEmpty()
  penilai_nrp: string;
}

export class KontrakKerjaDto extends PersonelData {
  @IsNotEmpty()
  uraian: string;

  @IsNotEmpty()
  indikator: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @ValidateIf((obj) => obj.satuan === 'persentase')
  @Max(100, { message: 'tidak boleh lebih dari 100' })
  target_tw_1: number;

  @IsNotEmpty()
  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  pencapaian_tw_1: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @ValidateIf((obj) => obj.satuan === 'persentase')
  @Max(100, { message: 'tidak boleh lebih dari 100' })
  target_tw_2: number;

  @IsNotEmpty()
  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  pencapaian_tw_2: number;

  @IsNotEmpty()
  satuan: string;
}

export class TugasTambahanDto extends PersonelData {
  @IsNotEmpty()
  @MaxLength(100)
  no_sprin: string;

  @IsNotEmpty()
  @Matches(DateOnlyRegex, { message: 'Harus berupa YYYY-MM-DD' })
  tgl_sprin: string;

  @IsNotEmpty()
  uraian_sprin: string;

  dokumen: Express.Multer.File;
}

export class PenghargaanDto extends PersonelData {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  jenis_penghargaan: number;

  @IsNotEmpty()
  @MaxLength(100)
  no_kep: string;

  @IsNotEmpty()
  @Matches(DateOnlyRegex, { message: 'Harus berupa YYYY-MM-DD' })
  tgl_kep: string;

  @IsNotEmpty()
  uraian_kep: string;

  dokumen: Express.Multer.File;
}

export class HukumanDto extends PersonelData {
  @IsNotEmpty()
  @MaxLength(100)
  @IsEnum(Object.keys(CONSTANT_HUKUMAN_SCORE), {
    message: `Harus antara ${Object.keys(CONSTANT_HUKUMAN_SCORE).toString()}`,
  })
  jenis_hukuman: string;

  @IsNotEmpty()
  @MaxLength(100)
  no_kep: string;

  @IsNotEmpty()
  @Matches(DateOnlyRegex, { message: 'Harus berupa YYYY-MM-DD' })
  tgl_kep: string;

  @IsNotEmpty()
  uraian_kep: string;

  dokumen: Express.Multer.File;
}

export class PenilaianRekanDto extends PersonelData {
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_kepemimpinan: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_pelayanan: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_komunikasi: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_pengendalian_emosi: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_integritas: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_empati: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_komitmen: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_inisiatif: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_disiplin: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  nilai_kerjasama: number;
}

export class EditPersonelDataDto extends PersonelData {
  @IsNotEmpty()
  nama: string;

  @IsNotEmpty()
  pangkat: string;

  @IsNotEmpty()
  jabatan: string;
}

export class EditPenilaiDataDto extends PersonelData {
  @IsNotEmpty()
  nik: string;

  @IsNotEmpty()
  nama: string;

  @IsNotEmpty()
  pangkat: string;

  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  pangkat_id: number;

  @IsNotEmpty()
  jabatan: string;

  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  jabatan_id: number;
}

export class VerifikasiDto extends PersonelData {
  @IsNotEmpty()
  @IsEnum(['approve', 'reject'], {
    message: 'Hanya bisa antara approve atau reject',
  })
  status: string;

  @ValidateIf((obj) => obj.status === 'reject')
  @IsNotEmpty()
  rejected_reason: string;
}

class DataVerfikasiDto {
  @IsNotEmpty()
  @Transform(({ value }) => BigInt(value))
  id: bigint;

  @IsNotEmpty()
  @IsEnum(['APPROVE', 'REJECT'])
  status: string;

  @ValidateIf((obj) => obj.status === 'REJECT')
  @IsNotEmpty()
  rejected_reason: string;
}

export class VerifikasiWithDataDto extends PersonelData {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => DataVerfikasiDto)
  data: DataVerfikasiDto[];
}

export class GetDetailAydDto {
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  personel_id: number;
}

export class GetJenisPenghargaan {
  @IsOptional()
  search: string;
}

export class GetPenilaianListDto extends PaginationDto {
  @IsOptional()
  keyword: string;

  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  satker: string;

  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  tahun: string;

  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  semester: string;

  @ValidateIf(({ value }) => !!value)
  @IsEnum(['sudah_mengisi', 'belum_mengisi'])
  status: string;
}

export class GetJabatanListDto extends PaginationDto {
  @IsOptional()
  name: string;
}
