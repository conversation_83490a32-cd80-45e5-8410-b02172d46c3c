import { forwardRef, Module } from '@nestjs/common';
import { GenerateNrpController } from './controller/generate-nrp.controller';
import { GenerateNrpService } from './service/generate-nrp.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [GenerateNrpController],
  providers: [GenerateNrpService],
})
export class GenerateNrpModule {}
