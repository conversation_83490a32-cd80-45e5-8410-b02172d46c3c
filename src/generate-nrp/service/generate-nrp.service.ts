import {
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { SortSearchColumn } from '../../core/utils/search.utils';
import { Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { GenerateNRPDto, NrpResult } from '../dto/generate-nrp.dto';
import { format } from 'date-fns';
import { id as idn } from 'date-fns/locale';
import * as moment from 'moment';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { getYearsRange } from '../../core/utils/common.utils';

const ExcelJS = require('exceljs');

@Injectable()
export class GenerateNrpService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getJenisDiktuk(req: any) {
    const queryResult = await this.prisma.jenis_diktuk.findMany({
      where: { deleted_at: null },
      select: { id: true, nama: true },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.GENERATE_NRP_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getDistinctGelombang(req: any) {
    const queryResult = [
      { gelombang: '-' },
      { gelombang: 'GEL 1' },
      { gelombang: 'GEL 2' },
    ];

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.GENERATE_NRP_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getDistinctTahun(req: any) {
    const queryResult = getYearsRange();

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    );

    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.GENERATE_NRP_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAllJenisDiktuk(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { page, limit } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      ta_pat_diktuk: { field: 'ta_pat_diktuk', type: 'string' },
      gelombang_pat_diktuk: { field: 'gelombang_pat_diktuk', type: 'string' },
      jenis_diktuk: { field: 'jenis_diktuk_id', type: 'bigint' },
    };

    const { where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const allSiswas = await this.prisma.data_siswa.findMany({
      where,
      select: {
        gelombang_pat_diktuk: true,
        ta_pat_diktuk: true,
        jenis_diktuk: { select: { id: true, nama: true } },
        validation: { select: { is_generate_nrp: true } },
      },
      orderBy: { ta_pat_diktuk: 'desc' },
    });

    const results = [];
    const setSiswaKey = new Set();
    for (const siswa of allSiswas) {
      const statusValidation = siswa.validation?.is_generate_nrp ?? false;

      const key = [
        siswa.jenis_diktuk.nama,
        siswa.gelombang_pat_diktuk,
        siswa.ta_pat_diktuk,
        statusValidation,
      ].join('-');

      if (!setSiswaKey.has(key)) {
        results.push({
          id: siswa.jenis_diktuk.id,
          jenis_diktuk: siswa.jenis_diktuk.nama,
          gelombang_pat_diktuk: siswa.gelombang_pat_diktuk,
          ta_pat_diktuk: siswa.ta_pat_diktuk,
          status_generate_nrp: statusValidation,
        });

        setSiswaKey.add(key);
      }
    }

    const totalData = results.length;
    const paginatedSiswas = results.slice((page - 1) * limit, page * limit);
    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: paginatedSiswas,
    };
  }

  async getAllSiswaByDiktukTahunGelombang(
    req: any,
    id: string,
    tahun: string,
    gelombang: string,
    status: string,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { page, limit } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const isStatusGenerateNRP = status === 'true';

    const columnMapping: IColumnMapping = {
      nrp: { field: 'nrp', type: 'string' },
      nama: { field: 'nama', type: 'string' },
      tanggal_lahir: { field: 'tanggal_lahir', type: 'string' },
      ta_pat_diktuk: { field: 'ta_pat_diktuk', type: 'string' },
      gelombang_pat_diktuk: { field: 'gelombang_pat_diktuk', type: 'string' },
      status_generate_nrp: {
        field: 'validation.is_generate_nrp',
        type: 'string',
      },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const whereClause = {
      ...where,
      jenis_diktuk_id: +id,
      ta_pat_diktuk: tahun,
      gelombang_pat_diktuk: gelombang,
      ...(isStatusGenerateNRP
        ? {
            validation: {
              is_generate_nrp: true,
            },
          }
        : {
            OR: [
              {
                validation: {
                  is_generate_nrp: false,
                },
              },
              {
                validation: null,
              },
            ],
          }),
    };

    const [totalData, listSiswa] = await this.prisma.$transaction([
      this.prisma.data_siswa.count({
        where: whereClause,
      }),
      this.prisma.data_siswa.findMany({
        select: {
          id: true,
          nama: true,
          nik: true,
          nrp: true,
          unique_id: true,
          ta_pat_diktuk: true,
          tanggal_lahir: true,
          jenis_diktuk_id: true,
          gelombang_pat_diktuk: true,
          jenis_diktuk: { select: { nama: true } },
          tempat_pendidikan: { select: { nama: true } },
        },
        where: whereClause,
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const result = listSiswa.map(
      ({ jenis_diktuk, tempat_pendidikan, ...siswa }) => {
        return {
          ...siswa,
          nama_jenis_diktuk: jenis_diktuk?.nama ?? null,
          nama_tmp_dik: tempat_pendidikan?.nama ?? null,
        };
      },
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: result,
    };
  }

  // generateNRPSiswa version 2
  async generateNrp(req: any, body: GenerateNRPDto) {
    const { id, tahun, gelombang, status } = body;

    if (status) throw new ConflictException('NRP Siswa sudah di generate');

    const students = await this.getStudents(id, tahun.toString(), gelombang);
    if (!students.length)
      throw new NotFoundException('Siswa dengan periode ini tidak ditemukan');

    const distinctPrefixes = this.getDistinctPrefixes(students);
    const lastNrpPersonel = await this.getLastNrpPersonel(distinctPrefixes);
    const prefixMap = this.buildPrefixMap(lastNrpPersonel);

    const listStudentWithNrp = students.map((siswa) =>
      this.assignNrp(siswa, prefixMap),
    );

    const queryResult = await this.saveStudents(listStudentWithNrp);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.GENERATE_NRP_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: queryResult,
    };
  }

  async exportExcel(
    req: any,
    id: string,
    tahun: string,
    gelombang: string,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc } = searchAndSortData;

    const where: Prisma.siswaWhereInput = {
      jenis_diktuk_id: +id,
      ta_pat_diktuk: tahun,
      gelombang_pat_diktuk: gelombang,
      status_generate_nrp: true,
    };

    const orderBy: Prisma.siswaOrderByWithRelationInput[] = [];

    if (Array.isArray(sort_column) && Array.isArray(sort_desc)) {
      for (let i = 0; i < sort_column.length; i++) {
        const column = sort_column[i];
        const direction = sort_desc[i]?.toLowerCase();

        if (column && ['asc', 'desc'].includes(direction)) {
          orderBy.push({ [column]: direction });
        }
      }
    }

    if (orderBy.length === 0) {
      orderBy.push({ id: 'desc' });
    }

    try {
      // Fetch users and total count in parallel
      const listSiswa = await this.prisma.siswa.findMany({
        select: {
          id: true,
          nama_lengkap: true,
          nik: true,
          unique_id: true,
          gelombang_pat_diktuk: true,
          ta_pat_diktuk: true,
          tanggal_lahir: true,
          jenis_diktuk_id: true,
          status_generate_nrp: true,
          siswa_patma: {
            select: {
              nrp: true,
            },
          },
          jenis_diktuk: {
            select: {
              nama: true,
            },
          },
          tmp_dik_id: true,
          tmpdik: {
            select: {
              nama: true,
            },
          },
        },
        where,
        orderBy,
      });

      const result = listSiswa.map(
        ({ siswa_patma, jenis_diktuk, tmpdik, ...siswa }) => {
          return {
            ...siswa,
            nama_jenis_diktuk: jenis_diktuk?.nama ?? null,
            nama_tmp_dik: tmpdik?.nama ?? null,
            nrp: siswa_patma?.nrp ?? null,
          };
        },
      );

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Generate NRP Data');

      worksheet.mergeCells('A1:J2');
      worksheet.getCell('A1').value = 'Data NRP Siswa';
      worksheet.getCell('A1').font = {
        size: 30,
        bold: true,
        color: { argb: 'FFFFFFFF' },
      };
      worksheet.getCell('A1').alignment = {
        vertical: 'middle',
        horizontal: 'center',
      };
      worksheet.getCell('A1').fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4d94ff' },
      };

      const now = new Date();
      const formattedDate = format(now, "eeee, d MMMM yyyy 'pukul' HH:mm:ss", {
        locale: idn,
      });

      worksheet.mergeCells('A3:J3');
      worksheet.getCell('A3').value = `Generated on: ${formattedDate}`;
      worksheet.getCell('A3').font = {
        italic: true,
        size: 12,
        color: { argb: 'FFFFFFFF' },
      };
      worksheet.getCell('A3').alignment = {
        vertical: 'middle',
        horizontal: 'center',
      };
      worksheet.getCell('A3').fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4d94ff' },
      };

      worksheet.getRow(5).values = [];

      const headerRow = worksheet.addRow([
        'No',
        'Nama Lengkap',
        'NIK',
        'Unique ID',
        'Jenis Diktuk',
        'Tahun',
        'Gelombang',
        'Tempat Diktuk',
        'Tanggal Lahir',
        'NRP',
      ]);

      // Format header
      headerRow.eachCell((cell) => {
        cell.font = { bold: true };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'CCCCCC' },
        };
      });

      worksheet.columns = [
        { key: 'no', width: 10 },
        { key: 'nama_lengkap', width: 30 },
        { key: 'nik', width: 30 },
        { key: 'unique_id', width: 30 },
        { key: 'jenis_diktuk', width: 20 },
        { key: 'tahun', width: 20 },
        { key: 'gelombang', width: 15 },
        { key: 'tempat_diktuk', width: 30 },
        { key: 'tgl_lahir', width: 30 },
        { key: 'nrp', width: 20 },
      ];

      // add data start from line 7
      result.forEach((siswa, index) => {
        worksheet.addRow({
          no: index + 1,
          nama_lengkap: siswa.nama_lengkap ?? '',
          nik: siswa.nik ?? '',
          unique_id: siswa.unique_id ?? '',
          jenis_diktuk: siswa.nama_jenis_diktuk ?? '',
          tahun: siswa.ta_pat_diktuk ?? '',
          gelombang: siswa.gelombang_pat_diktuk ?? '',
          tempat_diktuk: siswa.nama_tmp_dik ?? '',
          tgl_lahir: siswa.tanggal_lahir ?? '',
          nrp: siswa.nrp ?? '',
        });
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.GENERATE_NRP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.GENERATE_NRP_WRITE_EXCEL as ConstantLogType,
          message,
          result,
        ),
      );

      return await workbook.xlsx.writeBuffer();
    } catch (error) {
      throw new InternalServerErrorException(error.toString());
    }
  }

  private async saveStudents(students: any[]) {
    return this.prisma.$transaction(async (prisma) => {
      const insertPromises = students.map(async (siswa) => {
        await prisma.siswa.update({
          where: { id: siswa.id },
          data: { status_generate_nrp: true },
        });

        await prisma.siswa_patma.create({
          data: { siswa_id: siswa.id, nrp: siswa.nrp },
        });

        const personel = await prisma.personel.create({
          data: {
            nrp: siswa.nrp,
            uid: uuidv4(),
            nama_lengkap: siswa.nama_lengkap,
            tempat_lahir: siswa.tempat_lahir,
            tanggal_lahir: siswa.tanggal_lahir,
            jenis_kelamin: this.mappinggender_enum(siswa.jenis_kelamin),
            ktp_nomor: siswa.nik,
            email: siswa.email,
            agama_id: siswa.agama_id,
            suku_id: siswa.suku_id,
          },
        });

        const hobiSiswa = await prisma.siswa_hobi.findMany({
          where: { siswa_id: siswa.id },
        });

        if (hobiSiswa.length) {
          await prisma.hobi_personel.createMany({
            data: hobiSiswa.map((hobi) => ({
              personel_id: personel.id,
              hobi_id: BigInt(hobi.hobi_id),
              created_at: new Date(),
              updated_at: new Date(),
            })),
          });
        }

        await prisma.users.create({
          data: { password: siswa.password ?? '', personel_id: personel.id },
        });
      });

      await Promise.all(insertPromises);
    });
  }

  private async getStudents(id: number, tahun: string, gelombang: string) {
    return this.prisma.data_siswa.findMany({
      include: {
        jenis_diktuk: { select: { nama: true } },
        tempat_pendidikan: { select: { nama: true } },
      },
      where: {
        jenis_diktuk_id: id,
        ta_pat_diktuk: tahun.toString(),
        gelombang_pat_diktuk: gelombang,
        OR: [{ validation: { is_generate_nrp: false } }, { validation: null }],
      },
      orderBy: [{ tanggal_lahir: 'asc' }, { nama: 'asc' }],
    });
  }

  private mappinggender_enum(jenis: string) {
    const mapping = {
      PRIA: 'LAKI-LAKI',
      WANITA: 'PEREMPUAN',
    };
    return mapping[jenis] || null;
  }

  private getDistinctPrefixes(students: any[]): Set<string> {
    return new Set(
      students.map((student) => moment(student.tanggal_lahir).format('YYMM')),
    );
  }

  private async getLastNrpPersonel(distinctPrefixes: Set<string>) {
    return this.prisma.$queryRaw<NrpResult[]>`
      SELECT LEFT(nrp, 4) AS prefix, MAX(nrp) AS last_nrp
      FROM personel
      WHERE LEFT(nrp, 4) IN (${Prisma.join([...distinctPrefixes])})
      GROUP BY LEFT(nrp, 4)
    `;
  }

  private buildPrefixMap(lastNrpPersonel: NrpResult[]): Record<string, number> {
    return lastNrpPersonel.reduce(
      (acc, item) => {
        acc[item.prefix] = parseInt(item.last_nrp.slice(4), 10);
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  private assignNrp(siswa: any, prefixMap: Record<string, number>) {
    const prefix = moment(siswa.tanggal_lahir).format('YYMM');
    const newLastNumber = (prefixMap[prefix] ?? -1) + 1;
    prefixMap[prefix] = newLastNumber;

    return {
      ...siswa,
      nama_jenis_diktuk: siswa.jenis_diktuk.nama,
      nama_tmp_dik: siswa.tempat_pendidikan.nama,
      nrp: `${prefix}${newLastNumber.toString().padStart(4, '0')}`,
    };
  }
}
