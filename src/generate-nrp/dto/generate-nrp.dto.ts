import { IsBoolean, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsString } from 'class-validator';

export type NrpResult = {
  prefix: string;
  last_nrp: string;
};

export class GenerateNRPDto {
  @IsNotEmpty({ message: '<PERSON><PERSON> Diktuk ID tidak boleh kosong' })
  @IsNumber({}, { message: '<PERSON><PERSON> Di<PERSON>uk ID harus berupa angka' })
  id: number;

  @IsNotEmpty({ message: 'Tahun tidak boleh kosong' })
  @IsString({ message: 'Tahun harus berupa string' })
  tahun: string;

  @IsNotEmpty({ message: 'Tahun tidak boleh kosong' })
  @IsString({ message: 'Gelombang harus berupa string' })
  gelombang: string;

  @IsNotEmpty({ message: 'Status tidak boleh kosong' })
  @IsBoolean({ message: 'Status harus berupa boolean' })
  status: boolean;
}
