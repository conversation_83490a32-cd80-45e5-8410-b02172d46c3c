import {
  Body,
  Controller,
  Get,
  HttpCode,
  InternalServerErrorException,
  Logger,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { GenerateNrpService } from '../service/generate-nrp.service';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from '../../core/dtos';
import { GenerateNRPDto } from '../dto/generate-nrp.dto';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';
import { Response } from 'express';
import { MODULES } from '../../core/constants/module.constant';
import { Module, Permission } from '../../core/decorators';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';

@Controller()
@Module(MODULES.GENERATE_NRP)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class GenerateNrpController {
  private readonly logger = new Logger(GenerateNrpController.name);

  constructor(private readonly generateNrpService: GenerateNrpService) {}

  @Get('jenis-diktuk')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getJenisDiktuk(@Req() req: any) {
    this.logger.log(`Entering ${this.getJenisDiktuk.name}`);

    const response = await this.generateNrpService.getJenisDiktuk(req);

    this.logger.log(
      `Leaving ${this.getJenisDiktuk.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('generate-nrp/distinct-gelombang')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDistinctGelombang(@Req() req: any) {
    this.logger.log(`Entering ${this.getDistinctGelombang.name}`);
    const response = await this.generateNrpService.getDistinctGelombang(req);
    this.logger.log(
      `Leaving ${this.getDistinctGelombang.name} with response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('generate-nrp/distinct-tahun')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getDistinctTahun(@Req() req: any) {
    this.logger.log(`Entering ${this.getDistinctTahun.name}`);
    const response = await this.generateNrpService.getDistinctTahun(req);
    this.logger.log(
      `Leaving ${this.getDistinctTahun.name} with response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('generate-nrp')
  @Permission('PERMISSION_READ')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async getAllJenisDiktuk(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    const response = await this.generateNrpService.getAllJenisDiktuk(
      req,
      paginationData,
      searchAndSortData,
    );
    return response;
  }

  @Get('generate-nrp/:id/:tahun/:gelombang/:status')
  @Permission('PERMISSION_READ')
  @UsePipes(new ValidationPipe({ transform: true }))
  @HttpCode(200)
  async getAllSiswaByDiktukTahunGelombang(
    @Req() req: any,
    @Param('id') id: string,
    @Param('tahun') tahun: string,
    @Param('gelombang') gelombang: string,
    @Param('status') status: string,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAllSiswaByDiktukTahunGelombang.name} with id: ${id} and gelombang: ${gelombang} and status: ${status} and tahun: ${tahun} and pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)}`,
    );
    const response =
      await this.generateNrpService.getAllSiswaByDiktukTahunGelombang(
        req,
        id,
        tahun,
        gelombang,
        status,
        paginationData,
        searchAndSortData,
      );
    this.logger.log(
      `Leaving ${this.getAllSiswaByDiktukTahunGelombang.name} with id: ${id} and gelombang: ${gelombang} and status: ${status} and tahun: ${tahun} and pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchAndSortData)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('generate-nrp')
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async generateNRPSiswa(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: GenerateNRPDto,
  ) {
    this.logger.log(
      `Entering ${this.generateNRPSiswa.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.generateNrpService.generateNrp(req, body);
    this.logger.log(
      `Leaving ${this.generateNRPSiswa.name} with body: ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('export-generate-nrp/:id/:tahun/:gelombang')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async exportExcel(
    @Req() req: any,
    @Param('id') id: string,
    @Param('tahun') tahun: string,
    @Param('gelombang') gelombang: string,
    @Query() searchandsortData: SearchAndSortDTO,
    @Res() res: Response,
  ) {
    this.logger.log(
      `Entering ${this.exportExcel.name} with id: ${id} and tahun: ${tahun} and gelombang: ${gelombang} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    try {
      const buffer = await this.generateNrpService.exportExcel(
        req,
        id,
        tahun,
        gelombang,
        searchandsortData,
      );

      // Set response headers
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="siswa_${tahun}_${gelombang}.xlsx"`,
      );
      this.logger.log(
        `Leaving ${this.exportExcel.name} with id: ${id} and tahun: ${tahun} and gelombang: ${gelombang} and search and sort data: ${JSON.stringify(searchandsortData)} and response buffer length: ${buffer.length}`,
      );

      // Send buffer as response
      res.send(buffer);
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }
}
