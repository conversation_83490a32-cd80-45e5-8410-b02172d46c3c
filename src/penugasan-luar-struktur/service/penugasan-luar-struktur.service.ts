import {
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { CreatePenugasanLuarStrukturDto } from '../dto/penugasan-luar-struktur.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { is } from 'date-fns/locale';

@Injectable()
export class PenugasanLuarStrukturService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreatePenugasanLuarStrukturDto) {
    const { nama, is_aktif } = body;

    try {
      const penugasan = await this.prisma.penugasan_instansi.findFirst({
        select: {
          id: true,
          nama: true,
        },
        where: {
          nama: nama,
          deleted_at: null,
        },
      });

      if (penugasan) {
        throw new ConflictException('Nama already exists');
      }

      const queryResult = await this.prisma.penugasan_instansi.create({
        data: {
          nama,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENUGASAN_LUAR_STRUKTUR_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, body: CreatePenugasanLuarStrukturDto) {
    const { nama, is_aktif } = body;
    try {
      const penugasan = await this.prisma.penugasan_instansi.findFirst({
        select: {
          id: true,
          nama: true,
        },
        where: {
          id: BigInt(id),
          deleted_at: null,
        },
      });

      if (!penugasan) {
        throw new NotFoundException('Penugasan not found');
      }

      const checkIfExists = await this.prisma.penugasan_instansi.findFirst({
        select: {
          id: true,
          nama: true,
        },
        where: {
          nama: nama,
          NOT: {
            id,
          },
          deleted_at: null,
        },
      });

      if (checkIfExists) {
        throw new ConflictException('Nama already exists');
      }

      const update = await this.prisma.penugasan_instansi.update({
        where: { id },
        data: {
          nama,
          is_aktif: Boolean(is_aktif) ?? true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENUGASAN_LUAR_STRUKTUR_UPDATE as ConstantLogType,
          message,
          update,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: update,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const penugasan = await this.prisma.penugasan_instansi.findFirst({
        where: {
          id,
          deleted_at: null,
        },
      });

      if (!penugasan) {
        throw new NotFoundException('Penugasan not found');
      }

      const queryResult = await this.prisma.penugasan_instansi.update({
        where: { id },
        data: {
          deleted_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENUGASAN_LUAR_STRUKTUR_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOne(req: any, id: number) {
    try {
      const penugasan = await this.prisma.penugasan_instansi.findFirst({
        select: {
          id: true,
          nama: true,
          created_at: true,
          updated_at: true,
        },
        where: {
          id: BigInt(id),
          deleted_at: null,
        },
      });

      if (!penugasan) {
        throw new NotFoundException('Penugasan diluar struktur not found');
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENUGASAN_LUAR_STRUKTUR_READ as ConstantLogType,
          message,
          penugasan,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: penugasan,
      };
    } catch (err) {
      throw err;
    }
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: {
        field: string;
        type: 'string' | 'boolean' | 'number' | 'date';
      };
    } = {
      id: { field: 'id', type: 'number' },
      nama: { field: 'nama', type: 'string' },
      is_aktif: { field: 'is_aktif', type: 'boolean' },
      created_at: { field: 'created_at', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
      ['nama'],
    );

    try {
      const [totalData, penugasan] = await this.prisma.$transaction([
        this.prisma.penugasan_instansi.count({
          where,
        }),
        this.prisma.penugasan_instansi.findMany({
          select: {
            id: true,
            nama: true,
            is_aktif: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          where,
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENUGASAN_LUAR_STRUKTUR_READ as ConstantLogType,
          message,
          penugasan,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        page: +page,
        totalPage: totalPage,
        totalData: totalData,
        data: penugasan,
      };
    } catch (err) {
      throw err;
    }
  }
}
