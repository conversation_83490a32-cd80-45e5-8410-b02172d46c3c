import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { PenugasanLuarStrukturController } from './controller/penugasan-luar-struktur.controller';
import { PenugasanLuarStrukturService } from './service/penugasan-luar-struktur.service';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PenugasanLuarStrukturController],
  providers: [PenugasanLuarStrukturService],
})
export class PenugasanLuarStrukturModule {}
