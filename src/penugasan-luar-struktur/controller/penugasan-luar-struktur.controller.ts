import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { bracketToObject } from '../../core/utils/common.utils';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { PenugasanLuarStrukturService } from '../service/penugasan-luar-struktur.service';
import { CreatePenugasanLuarStrukturDto } from '../dto/penugasan-luar-struktur.dto';
import { MODULES } from '../../core/constants/module.constant';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';

@Controller('penugasan-luar-struktur')
@Module(MODULES.SPECIAL_ASSIGNMENTS)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PenugasanLuarStrukturController {
  private readonly logger = new Logger(PenugasanLuarStrukturController.name);

  constructor(
    private readonly penugasanLuarStrukrurService: PenugasanLuarStrukturService,
  ) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @HttpCode(201)
  async create(@Req() req: any, @Body() body: CreatePenugasanLuarStrukturDto) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );

    const response = await this.penugasanLuarStrukrurService.create(
      req,
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: CreatePenugasanLuarStrukturDto,
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.penugasanLuarStrukrurService.update(
      req,
      Number(id),
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.penugasanLuarStrukrurService.delete(
      req,
      Number(id),
    );
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.penugasanLuarStrukrurService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getOne(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getOne.name} with id: ${id}`);
    const response = await this.penugasanLuarStrukrurService.getOne(
      req,
      Number(id),
    );
    this.logger.log(
      `Leaving ${this.getOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
