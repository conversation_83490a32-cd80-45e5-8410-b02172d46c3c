import { PartialType } from '@nestjs/mapped-types';
import { IsOptional } from 'class-validator';

export class CreatePengajuanCutiDto {
  // @IsNotEmpty()
  // id: number;

  // @IsOptional()
  // personel_id: number;
  //
  // @IsOptional()
  // satuan: string;
  //

  @IsOptional()
  pengampu_personel_id: number;

  @IsOptional()
  jenis_cuti: string;
  //
  @IsOptional()
  tanggal_mulai: Date;
  //
  @IsOptional()
  tanggal_akhir: Date;
  //
  // @IsOptional()
  // durasi: string;
  //
  // @IsOptional()
  // status: string;
  //
  @IsOptional()
  alasan_cuti: string;

  @IsOptional()
  alamat_cuti: string;
  //
  @IsOptional()
  keterangan: string;

  @IsOptional()
  status: string;

  //
  // @IsOptional()
  // dto: JSON;
  //
  // @IsOptional()
  // alasan_penolakan: string;
  //
  // @IsOptional()
  // created_at: Date;
  //
  // @IsOptional()
  // updated_at: Date;
  //
  // @IsOptional()
  // deleted_at: Date;
}

export class UpdatePengajuanCutiDto extends PartialType(
  CreatePengajuanCutiDto,
) {}
