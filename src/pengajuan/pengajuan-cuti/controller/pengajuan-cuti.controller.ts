import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PengajuanCutiService } from '../service/pengajuan-cuti.service';
import {
  CreatePengajuanCutiDto,
  UpdatePengajuanCutiDto,
} from '../dto/pengajuan-cuti.dto';
import { Module, Permission, RolePermissionNeeded } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { MODULES } from '../../../core/constants/module.constant';
import { UserRoleGuard } from '../../../core/guards/user-role.guards';

@Controller('pengajuan-cuti')
@Module(MODULES.LEAVE_APPLICATION)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PengajuanCutiController {
  private readonly logger = new Logger(PengajuanCutiController.name);

  constructor(private readonly pengajuanCutiService: PengajuanCutiService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'files', maxCount: 10 },
    { name: 'usulan_kasatfung_file', maxCount: 1 },
    { name: 'surat_berdinas_file', maxCount: 1 },
    { name: 'surat_penugasan_file', maxCount: 1 },
    { name: 'sket_dokter_file', maxCount: 1 },
  ]))
  async create(
    @Req() req: any,
    @Body() createPengajuanCutiDto: CreatePengajuanCutiDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(createPengajuanCutiDto)}`,
    );
    const response = await this.pengajuanCutiService.create(
      createPengajuanCutiDto,
      req,
      files,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(createPengajuanCutiDto)} response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @RolePermissionNeeded('PENGAJUAN_CUTI', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanCutiService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/saya')
  @Permission('PERMISSION_READ')
  async getMyAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getMyAll.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanCutiService.findAllFromPersonel(
      req,
      paginationData,
      searchandsortData,
      req['user']['personel_id'],
    );
    this.logger.log(
      `Leaving ${this.getMyAll.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/:id')
  @Permission('PERMISSION_READ')
  async findAllPersonelCutiApproved(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Param('id') id: number
  ) {
    this.logger.log(
      `Entering ${this.findAllPersonelCutiApproved.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanCutiService.findAllPersonelCutiApproved(
      req,
      paginationData,
      searchandsortData,
      id,
    );
    this.logger.log(
      `Leaving ${this.findAllPersonelCutiApproved.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/pengampu-search')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanCutiService.getPengampuList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with body: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.pengajuanCutiService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/setujui/:id')
  @Permission('PERMISSION_UPDATE')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 1 }]))
  async setujuiPengajuan(
    @Param('id') id: string,
    @Body() updatePengajuanCutiDto: UpdatePengajuanCutiDto,
    @Req() req: any,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.setujuiPengajuan.name} with id: ${id} and body: ${JSON.stringify(updatePengajuanCutiDto)}`,
    );

    const response = await this.pengajuanCutiService.approve(req, +id, files);
    this.logger.log(
      `Leaving ${this.setujuiPengajuan.name} with id: ${id} and body: ${JSON.stringify(updatePengajuanCutiDto)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/tolak/:id')
  @Permission('PERMISSION_UPDATE')
  async tolakPengajuan(
    @Param('id') id: string,
    @Body() updatePengajuanCutiDto: UpdatePengajuanCutiDto,
    @Req() req: any,
  ) {
    this.logger.log(
      `Entering ${this.tolakPengajuan.name} with id: ${id} and body: ${JSON.stringify(updatePengajuanCutiDto)}`,
    );
    const response = await this.pengajuanCutiService.update(req, +id, <
      UpdatePengajuanCutiDto
    >{
      status: 'Ditolak',
      alasan_penolakan: updatePengajuanCutiDto['alasan_penolakan'],
    });
    this.logger.log(
      `Leaving ${this.tolakPengajuan.name} with id: ${id} and body: ${JSON.stringify(updatePengajuanCutiDto)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
