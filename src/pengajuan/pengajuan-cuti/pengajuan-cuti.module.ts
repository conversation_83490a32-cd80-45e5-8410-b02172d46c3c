import { forwardRef, Module } from '@nestjs/common';
import { PengajuanCutiService } from './service/pengajuan-cuti.service';
import { PengajuanCutiController } from './controller/pengajuan-cuti.controller';
import { MinioService } from '../../api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PengajuanCutiController],
  providers: [PengajuanCutiService, MinioService],
})
export class PengajuanCutiModule {}
