import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  CreatePengajuanCutiDto,
  UpdatePengajuanCutiDto,
} from '../dto/pengajuan-cuti.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { ISendNotificationMultiple } from '../../../core/interfaces/firebase.interface';
import { FirebaseEnum } from '../../../core/enums/firebase.enum';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { IColumnMapping } from 'src/core/interfaces/db.interface';

@Injectable()
export class PengajuanCutiService {
  constructor(
    private readonly minioService: MinioService,
    private prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) { }

  private readonly feUrl = this.configService.get<string>('FE_URL');

  async create(
    createPengajuanCutiDto: CreatePengajuanCutiDto,
    req: Request,
    files: any,
  ) {
    try {
      let uploadedFiles = []

      for (const file of files.files) {
        let res = await this.minioService.uploadFileWithNRP(file, req['user']['nrp']);
        uploadedFiles.push(res.filename)
      }

      let tanggalMulai = new Date(createPengajuanCutiDto.tanggal_mulai);
      let tanggalAkhir = new Date(createPengajuanCutiDto.tanggal_akhir);

      if (createPengajuanCutiDto.pengampu_personel_id) {
        createPengajuanCutiDto.pengampu_personel_id = parseInt(
          createPengajuanCutiDto.pengampu_personel_id + '',
        );
      }

      const listDokumen = [
        'usulan_kasatfung',
        'surat_berdinas',
        'surat_penugasan',
        'sket_dokter',
      ];

      for (let i = 0; i < listDokumen.length; i++) {
        const fileKey = `${listDokumen[i]}_file`;
        if (files[fileKey]) {
          const uploadedFile = await this.minioService.uploadFileWithNRP(
            files[fileKey][0],
            req['user']['nrp']);
          createPengajuanCutiDto[fileKey] = uploadedFile.filename;
        }
      }

      const queryResult = await this.prisma.pengajuan_cuti.create({
        data: {
          ...createPengajuanCutiDto,
          dokumen: uploadedFiles,
          personel_id: req['user']['personel_id'],
          status: 'Diajukan',
          tanggal_mulai: tanggalMulai,
          tanggal_akhir: tanggalAkhir,
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      if (createPengajuanCutiDto.pengampu_personel_id) {
        const target = await this.prisma.users_device_token.findMany({
          where: { user_id: req['user']['id'] },
        });

        const mock: ISendNotificationMultiple = {
          body: `Terdapat pengajuan cuti baru dari personel ${req['user']['nrp']}`,
          type: 'CUTI',
          data: { link: `${this.feUrl}/pengajuan-cuti` },
          title: 'Pengajuan Cuti Baru',
          tokens: target,
        };
        this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_UPLOAD as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    let roleType = req['user']['roles'][0].role_tipe;

    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: IColumnMapping = {
        nama: { field: 'pengajuan_cuti_personel.nama_lengkap', type: 'string' },
        nrp: { field: 'pengajuan_cuti_personel.nrp', type: 'string' },
        status: { field: 'status', type: 'string' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      if (orderBy.length === 0) {
        orderBy.push({ id: 'desc' });
      }

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuan_cuti] = await this.prisma.$transaction([
        this.prisma.pengajuan_cuti.count({
          where,
        }),
        this.prisma.pengajuan_cuti.findMany({
          // this.prisma.pengajuan-cuti.findMany({
          //   // select: {
          //   //   id: true,
          //   //   nama: true,
          //   // },
          select: {
            id: true,
            jenis_cuti: true,
            dokumen: true,
            tanggal_mulai: true,
            tanggal_akhir: true,
            alasan_cuti: true,
            alasan_penolakan: true,
            alamat_cuti: true,
            skep_cuti: true,
            status: true,
            pengampu_personel_id: true,
            usulan_kasatfung_file: true,
            surat_berdinas_file: true,
            surat_penugasan_file: true,
            sket_dokter_file: true,
            pengajuan_cuti_pengampu_pengajuan_cuti: {
              select: {
                nrp: true,
                nama_lengkap: true,
              },
            },
            pengajuan_cuti_personel: {
              select: {
                id: true,
                nrp: true,
                nama_lengkap: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    id: true,
                    pangkat_id: true,
                    tmt: true,
                    kep_file: true,
                    kep_nomor: true,
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: { tmt_jabatan: 'desc' },
                  take: 1,
                },
              },
            },
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      let queryResult = [];
      for (let obj of pengajuan_cuti) {
        let firstPangkat = await this.prisma.pangkat_personel.findFirst({
          select: {
            tmt: true,
            kep_file: true,
            kep_nomor: true,
          },
          where: {
            personel_id: obj.pengajuan_cuti_personel.id,
          },
          orderBy: {
            tmt: 'asc',
          },
        });

        let dokumenJSON = [];
        if (Array.isArray(obj.dokumen)) {
          dokumenJSON = obj.dokumen;
        }

        // let dokumenFormatted = dokumenJSON.map(obj=>{
        //   return this.minioService.convertFileKeyToURL(process.env.MINIO_BUCKET_NAME, obj)
        // })

        let dokumenFormatted = [];
        for (let doc of dokumenJSON) {
          dokumenFormatted.push(
            await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${doc}`,
            ),
          );
        }

        const firstPangkatKep = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${firstPangkat?.kep_file}`,
        );

        const pangkatTerbaruKep = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.pengajuan_cuti_personel.pangkat_personel?.[0]?.kep_file}`,
        );

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.pengajuan_cuti_personel.foto_file}`,
        );

        const skepCuti = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.skep_cuti}`,
        );

        const usulanKasatfung = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.usulan_kasatfung_file}`,
        );

        const suratBerdinas = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.surat_berdinas_file}`,
        );

        const suratPenugasan = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.surat_penugasan_file}`,
        );

        const sketDokter = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.sket_dokter_file}`,
        );

        queryResult.push({
          ...obj,
          pengampu_pengajuan_cuti: obj.pengajuan_cuti_pengampu_pengajuan_cuti,
          personel: {
            ...obj.pengajuan_cuti_personel,
            pangkat: obj.pengajuan_cuti_personel?.pangkat_personel?.[0]?.pangkat
              ? obj.pengajuan_cuti_personel?.pangkat_personel?.[0]?.pangkat.nama
              : null,
            jabatan: obj.pengajuan_cuti_personel?.jabatan_personel?.[0]
              ?.jabatans
              ? obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
                .nama
              : null,
            satuan: obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
              ? obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
                .satuan.nama
              : null,
            foto_file: fotoFile,
          },
          dokumen: dokumenFormatted,
          skep_cuti: skepCuti,
          skep_pertama: firstPangkatKep,
          skep_terakhir: pangkatTerbaruKep,
          usulan_kasatfung_file: usulanKasatfung,
          surat_berdinas_file: suratBerdinas,
          surat_penugasan_file: suratPenugasan,
          sket_dokter_file: sketDokter
        });
      }

      /* if (roleType === 'Admin') { */
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
      /* } else {
        throw new UnauthorizedException(
          'You are unauthorized to see this action',
        );
      } */
    } catch (err) {
      throw err;
    }
  }

  async findAllFromPersonel(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
    personelId: number,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean';
        };
      } = {};
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const formattedWhere = {
        ...where,
        personel_id: personelId,
      };

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuan_cuti] = await this.prisma.$transaction([
        this.prisma.pengajuan_cuti.count({
          where: formattedWhere,
        }),
        this.prisma.pengajuan_cuti.findMany({
          // this.prisma.pengajuan-cuti.findMany({
          //   // select: {
          //   //   id: true,
          //   //   nama: true,
          //   // },
          select: {
            jenis_cuti: true,
            dokumen: true,
            tanggal_mulai: true,
            tanggal_akhir: true,
            status: true,
            alasan_cuti: true,
            alasan_penolakan: true,
            alamat_cuti: true,
            skep_cuti: true,
            pengampu_personel_id: true,
            usulan_kasatfung_file: true,
            surat_berdinas_file: true,
            surat_penugasan_file: true,
            sket_dokter_file: true,
            pengajuan_cuti_pengampu_pengajuan_cuti: {
              select: {
                nrp: true,
                nama_lengkap: true,
              },
            },
            pengajuan_cuti_personel: {
              select: {
                nrp: true,
                nama_lengkap: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: { tmt_jabatan: 'desc' },
                  take: 1,
                },
              },
            },
          },
          where: formattedWhere,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      let formatted = [];
      for (let obj of pengajuan_cuti) {
        let dokumenJSON = [];
        if (Array.isArray(obj.dokumen)) {
          dokumenJSON = obj.dokumen;
        }

        let dokumenFormatted = [];
        for (let doc of dokumenJSON) {
          dokumenFormatted.push(
            await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${doc}`,
            ),
          );
        }

        let skepCuti = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.skep_cuti}`,
        );
        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.pengajuan_cuti_personel.foto_file}`,
        );

        const usulanKasatfung = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.usulan_kasatfung_file}`,
        );

        const suratBerdinas = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.surat_berdinas_file}`,
        );

        const suratPenugasan = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.surat_penugasan_file}`,
        );

        const sketDokter = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.sket_dokter_file}`,
        );

        formatted.push({
          ...obj,
          pengampu_pengajuan_cuti: obj.pengajuan_cuti_pengampu_pengajuan_cuti,
          personel: {
            ...obj.pengajuan_cuti_personel,
            pangkat: obj.pengajuan_cuti_personel.pangkat_personel?.[0]?.pangkat
              ? obj.pengajuan_cuti_personel.pangkat_personel?.[0]?.pangkat.nama
              : null,
            jabatan: obj.pengajuan_cuti_personel?.jabatan_personel?.[0]
              ?.jabatans
              ? obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
                .nama
              : null,
            satuan: obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
              ? obj.pengajuan_cuti_personel?.jabatan_personel?.[0]?.jabatans
                .satuan.nama
              : null,
            foto_file: fotoFile,
          },
          dokumen: dokumenFormatted,
          skep_cuti: skepCuti,
          usulan_kasatfung_file: usulanKasatfung,
          surat_berdinas_file: suratBerdinas,
          surat_penugasan_file: suratPenugasan,
          sket_dokter_file: sketDokter
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_READ as ConstantLogType,
          message,
          formatted,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: formatted,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllPersonelCutiApproved(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
    personel_id: number,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean';
        };
      } = {};
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuan_cuti] = await this.prisma.$transaction([
        this.prisma.pengajuan_cuti.count({
          where,
        }),
        this.prisma.pengajuan_cuti.findMany({
          select: {
            id: true,
            jenis_cuti: true,
            dokumen: true,
            tanggal_mulai: true,
            tanggal_akhir: true,
            alasan_cuti: true,
            alasan_penolakan: true,
            alamat_cuti: true,
            skep_cuti: true,
            status: true,
            pengampu_personel_id: true,
            pengajuan_cuti_pengampu_pengajuan_cuti: {
              select: {
                nrp: true,
                nama_lengkap: true,
              },
            },
            pengajuan_cuti_personel: {
              select: {
                nrp: true,
                nama_lengkap: true,
                foto_file: true,
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: { tmt_jabatan: 'desc' },
                  take: 1,
                },
              },
            },
          },
          where: {
            personel_id,
            status: 'Disetujui'
          },
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
        // this.prisma.pengajuan-cuti.findMany({
        //   // select: {
        //   //   id: true,
        //   //   nama: true,
        //   // },
        //   where,
        //   take: limit,
        //   skip: limit * (page - 1),
        //   orderBy,
        // }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      let queryResult = [];

      for (let obj of pengajuan_cuti) {
        let dokumenJSON = [];
        if (Array.isArray(obj.dokumen)) {
          dokumenJSON = obj.dokumen;
        }

        let dokumenFormatted = [];
        for (let doc of dokumenJSON) {
          dokumenFormatted.push(
            await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${doc}`,
            ),
          );
        }
        let skepCuti = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${obj.pengajuan_cuti_personel.nrp}/${obj.skep_cuti}`,
        );

        queryResult.push({
          ...obj,
          dokumen: dokumenFormatted,
          skep_cuti: skepCuti,
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const pengajuan_cuti = await this.prisma.pengajuan_cuti.findFirst({
        select: {
          id: true,
          jenis_cuti: true,
          dokumen: true,
          tanggal_mulai: true,
          tanggal_akhir: true,
          alasan_cuti: true,
          alasan_penolakan: true,
          alamat_cuti: true,
          skep_cuti: true,
          status: true,
          pengampu_personel_id: true,
          pengajuan_cuti_pengampu_pengajuan_cuti: {
            select: {
              nrp: true,
              nama_lengkap: true,
            },
          },
          pengajuan_cuti_personel: {
            select: {
              nrp: true,
              nama_lengkap: true,
              foto_file: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      nama: true,
                    },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: { tmt_jabatan: 'desc' },
                take: 1,
              },
            },
          },
        },
        where: { id, deleted_at: null },
        // select: {
        //   id: true,
        //   nama: true,
        // },
      });

      if (!pengajuan_cuti) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      let dokumenJSON = [];
      if (Array.isArray(pengajuan_cuti.dokumen)) {
        dokumenJSON = pengajuan_cuti.dokumen;
      }

      let dokumenFormatted = [];
      for (let doc of dokumenJSON) {
        dokumenFormatted.push(
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan_cuti.pengajuan_cuti_personel.nrp}/${doc}`,
          ),
        );
      }
      let skepCuti = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${pengajuan_cuti.pengajuan_cuti_personel.nrp}/${pengajuan_cuti.skep_cuti}`,
      );

      let formatted = {
        ...pengajuan_cuti,
        dokumen: dokumenFormatted,
        skep_cuti: skepCuti,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_READ as ConstantLogType,
          message,
          formatted,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: formatted,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updatePengajuanCutiDto: UpdatePengajuanCutiDto,
  ) {
    //CHECK PENGAMPU
    const pengajuanData = await this.findOne(req, id);
    if (pengajuanData.data.pengampu_personel_id) {
      if (
        pengajuanData.data.pengampu_personel_id != req['user']['personel_id']
      ) {
        throw new HttpException(
          `Pengajuan Cuti harus di setujui / ditolak oleh pengampu`,
          HttpStatus.FORBIDDEN,
        );
      }
    }
    try {
      const pengajuan_cuti = await this.prisma.pengajuan_cuti.findFirst({
        where: { id, deleted_at: null },
      });
      if (!pengajuan_cuti) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      // await this._validateSameName(updatePengajuanCutiDto.nama);

      const queryResult = await this.prisma.pengajuan_cuti.update({
        where: { id },
        data: {
          ...updatePengajuanCutiDto,
          updated_at: new Date(),
        },
      });

      if (updatePengajuanCutiDto.status === 'Ditolak') {
        const user = await this.prisma.users.findFirst({
          where: { personel_id: pengajuan_cuti.personel_id },
        });
        const target = await this.prisma.users_device_token.findMany({
          where: { user_id: user.id },
        });

        const mock: ISendNotificationMultiple = {
          body: 'Pengajuan cuti anda ditolak. Silahkan cek untuk detail.',
          tokens: target,
          data: { link: `${this.feUrl}/pengajuan-cuti/cuti-saya` },
          type: 'CUTI',
          title: 'Pengajuan Cuti Ditolak',
        };
        this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async approve(req: any, id: number, files: any) {
    //CHECK PENGAMPU
    const pengajuanData = await this.findOne(req, id);
    if (pengajuanData.data.pengampu_personel_id) {
      if (
        pengajuanData.data.pengampu_personel_id != req['user']['personel_id']
      ) {
        throw new HttpException(
          `Pengajuan Cuti harus di setujui / ditolak oleh pengampu`,
          HttpStatus.FORBIDDEN,
        );
      }
    }

    try {
      const pengajuan_cuti = await this.prisma.pengajuan_cuti.findFirst({
        include: {
          pengajuan_cuti_personel: {
            select: {
              nrp: true,
            },
          },
        },
        where: { id, deleted_at: null },
      });
      if (!pengajuan_cuti) {
        throw new NotFoundException(`Pengajuan Cuti tidak ditemukan`);
      }

      // await this._validateSameName(updatePengajuanCutiDto.nama);
      const uploadedFiles = await this.minioService.uploadFileWithNRP(files?.files[0], pengajuan_cuti.pengajuan_cuti_personel.nrp);

      const updatedPengajuanCuti = await this.prisma.pengajuan_cuti.update({
        where: { id },
        data: {
          skep_cuti: uploadedFiles.filename,
          status: 'Disetujui',
          updated_at: new Date(),
        },
      });

      const user = await this.prisma.users.findFirst({
        where: { personel_id: pengajuan_cuti.personel_id },
      });
      const target = await this.prisma.users_device_token.findMany({
        where: { user_id: user.id },
      });

      const mock: ISendNotificationMultiple = {
        body: 'Pengajuan cuti anda telah disetujui',
        tokens: target,
        data: { link: `${this.feUrl}/pengajuan-cuti/cuti-saya` },
        type: 'CUTI',
        title: 'Pengajuan Cuti Disetujui',
      };
      this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_CUTI_UPDATE as ConstantLogType,
          message,
          updatedPengajuanCuti,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: updatedPengajuanCuti,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(id: number) {
    try {
      const pengajuan_cuti = await this.prisma.pengajuan_cuti.findFirst({
        where: { id, deleted_at: null },
      });
      if (!pengajuan_cuti) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      await this.prisma.pengajuan_cuti.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });
    } catch (err) {
      throw err;
    }
  }

  async getPengampuList(req, paginationData, searchandsortData) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchandsortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'boolean' | 'bigint' };
    } = {
      nrp: { field: 'nrp', type: 'string' },
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      pangkat_nama: {
        field: 'pangkat_personel.some.pangkat.nama',
        type: 'string',
      },
      jabatan_nama: {
        field: 'jabatan_personel.some.jabatans.nama',
        type: 'string',
      },
      satuan_nama: {
        field: 'jabatan_personel.some.jabatans.satuan.nama',
        type: 'string',
      },
      'status_aktif.nama': { field: 'status_aktif.nama', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const formattedWhere = {
      ...where,
      id: {
        not: req['user']['personel_id'],
      },
      users: {
        users_role: {
          some: {
            role: {
              AND: [
                {
                  role_access: {
                    some: {
                      module_id: 28
                    }
                  }
                },
                {
                  OR: [
                    { level_id: 2 },
                    { level_id: null },
                  ]
                },
              ],
            }
          },
        },
      },
    };

    const [totalData, personels] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: formattedWhere,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        // where: where,
        where: formattedWhere,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    let formatted = [];
    for (let personel of personels) {
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      );

      const result = {
        ...personel,
        id: Number(personel.id),
        foto_file: fotoFile,
        tanggal_lahir: new Date(personel.tanggal_lahir)
          .toISOString()
          .split('T')[0], // YYYY-MM-DD
        pangkat: personel.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan: personel.jabatan_personel?.[0]?.jabatans ?? null,
        satuan: personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
      };

      // Delete nested properties
      delete result.pangkat_personel;
      delete result.jabatan_personel;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      formatted.push(result);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_CUTI_READ as ConstantLogType,
        message,
        formatted,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: formatted,
    };
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }
}
