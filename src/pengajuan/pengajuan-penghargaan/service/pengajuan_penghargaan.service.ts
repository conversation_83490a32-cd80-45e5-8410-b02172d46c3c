import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePengajuanPenghargaanDto,
  UpdatePengajuanPenghargaanDto,
} from '../dto/pengajuan_penghargaan.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import * as ExcelJS from 'exceljs';
import { Response } from 'express';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import * as dayjs from 'dayjs';

const TEMPLATE_PROPERTIES = {
  nrp: { header: 'NRP', key: 'nrp', index: 0, type: 'string' },
  penghargaan: { header: 'Penghargaan', key: 'penghargaan', index: 1, type: 'custom' },
  tingkat: { header: 'Tingkat', key: 'tingkat', index: 2, type: 'custom' },
  tanggal_pengajuan: { header: 'Tanggal Pengajuan', key: 'tanggal_pengajuan', index: 3, type: 'date' },
};

const TEMPLATE_HEADERS_LENGTH = Object.entries(TEMPLATE_PROPERTIES).length;

@Injectable()
export class PengajuanPenghargaanService {
  constructor(
    private readonly minioService: MinioService,
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) { }

  async createPengajuanPenghargaan(
    req: any,
    createPengajuanPenghargaanDto: CreatePengajuanPenghargaanDto,
    files: any,
  ) {
    let pengajuan_penghargaan;

    let uploadedFiles = await this.uploadFiles(files.files);

    try {
      await this.prisma.$transaction(async () => {
        const query = {
          data: {
            personel_id: parseInt(createPengajuanPenghargaanDto['personel_id']),
            penghargaan_id: parseInt(
              createPengajuanPenghargaanDto['penghargaan_id'],
            ),
            tingkat_id: parseInt(createPengajuanPenghargaanDto['tingkat_id']),
            created_by: parseInt(createPengajuanPenghargaanDto['created_by']),
            files: uploadedFiles.map((obj, idx) => {
              return { name: `file ${idx + 1}`, url: obj.uploaded.Key };
            }),
            status: 'Proses',
            created_at: new Date(),
          },
        };

        /* const listDokumen = [
          'kep_pengangkatan_pertama',
          'kep_pangkat_terakhir',
          'rh_singkat',
          'surat_perintah_pelaksanaan',
        ]; */

        /* const filesArray = [
          {
              name: "Surat keputusan pengangkatan pertama",
              url: "/dev/ssdm-object/1730776438684-Form Untuk Testing-2.pdf",
          },
          {
              name: "Surat keputusan pangkat terakhir",
              url: "/dev/ssdm-object/1730365127816-1728037271375-Form Permohonan EFIN (PDF isian)_2.pdf"
          },
          {
              name: "Riwayat hidup singkat",
              url: "/dev/ssdm-object/1730365127816-1728037271375-Form Permohonan EFIN (PDF isian)_2.pdf"
          },
          {
              name: "Surat perintah pelaksanaan tugas",
              url: "/dev/ssdm-object/1730776438684-Form Untuk Testing-2.pdf"
          },
        ] */

        /* for (let i = 0; i < listDokumen.length; i++) {
          const fileKey = listDokumen[i];
          if (files[fileKey]) {
            const uploadedFile = await this.uploadFile(files[fileKey][0]);
            const fileObj = {
              name: fileKey,
              url: uploadedFile.Key,
            }
            filesArray.push(fileObj)
          }
        }
        query.data['files'] = filesArray; */
        pengajuan_penghargaan =
          await this.prisma.pengajuan_penghargaan.create(query);
      });

      return pengajuan_penghargaan;
    } catch (err) {
      throw err;
    }
  }

  async updatePengajuanPenghargaan(
    req: any,
    id: number,
    updatePengajuanPenghargaanDto: UpdatePengajuanPenghargaanDto,
    files: any,
  ) {
    let queryResult;

    try {
      await this.prisma.$transaction(async () => {
        const pengajuan_penghargaan =
          await this.prisma.pengajuan_penghargaan.findFirst({
            where: { id },
          });
        if (!pengajuan_penghargaan) {
          throw new NotFoundException(`Pengajuan penghargaan tidak ditemukan`);
        }

        const query = {
          where: { id },
          data: {
            ...updatePengajuanPenghargaanDto,
          },
        };

        const pengajuan_penghargaan_existing =
          await this.prisma.pengajuan_penghargaan.findFirst({
            select: {
              personel: {
                select: {
                  nrp: true,
                },
              },
            },
            where: { id },
          });
        if (!pengajuan_penghargaan_existing) {
          throw new NotFoundException(`Pengajuan penghargaan tidak ditemukan`);
        }

        if (updatePengajuanPenghargaanDto['approved_by']) {
          if (files['kep_penghargaan']) {
            const uploadedFile = await this.uploadFileWithNRP(
              files['kep_penghargaan'][0],
              pengajuan_penghargaan_existing.personel.nrp,
            );
            query.data['kep_penghargaan'] = uploadedFile.Key;
          }
          query.data['approved_by'] = parseInt(
            updatePengajuanPenghargaanDto['approved_by'],
          );
        }

        if (updatePengajuanPenghargaanDto['rejected_by']) {
          query.data['rejected_by'] = parseInt(
            updatePengajuanPenghargaanDto['rejected_by'],
          );
        }

        /* if (files['kep_penghargaan']) {
          const uploadedFile = await this.uploadFile(files['kep_penghargaan'][0]);
          query.data['kep_penghargaan'] = uploadedFile.Key;
        } */
        /* query.data['kep_penghargaan'] = "/dev/ssdm-object/1730946155701-1728445040196-satusdm.pdf"; */
        queryResult = await this.prisma.pengajuan_penghargaan.update(query);
      });

      let penghargaan_personel;
      if (updatePengajuanPenghargaanDto['approved_by']) {
        penghargaan_personel =
          await this.createPenghargaanPersonel(queryResult);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async updatePelengkapanPengajuanPenghargaan(
    req: any,
    id: number,
    files: any,
  ) {
    let uploadedFiles = await this.uploadFiles(files.files);
    try {
      const queryResult = await this.prisma.$transaction(async () => {
        const pengajuan_penghargaan =
          await this.prisma.pengajuan_penghargaan.findFirst({
            where: { id },
          });
        if (!pengajuan_penghargaan) {
          throw new NotFoundException(`Pengajuan penghargaan tidak ditemukan`);
        }

        const query = {
          where: { id },
          data: {
            /* penghargaan_id: parseInt(
              updatePengajuanPenghargaanDto['penghargaan_id'],
            ), */
            files: uploadedFiles.map((obj, idx) => {
              return { name: `file ${idx + 1}`, url: obj.uploaded.Key };
            }),
            status: 'Proses',
          },
        };

        /* if (files['kep_penghargaan']) {
          const uploadedFile = await this.uploadFile(files['kep_penghargaan'][0]);
          query.data['kep_penghargaan'] = uploadedFile.Key;
        } */
        /* query.data['kep_penghargaan'] = "/dev/ssdm-object/1730946155701-1728445040196-satusdm.pdf"; */

        return await this.prisma.pengajuan_penghargaan.update(query);
      });

      /* let penghargaan_personel;
      if (updatePengajuanPenghargaanDto['approved_by']) {
        penghargaan_personel = await this.createPenghargaanPersonel(pengajuan_penghargaan_update)
      } */
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllPengajuanV2Process(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    let roleType = req['user']['roles'][0].role_tipe;

    let queryResult;
    if (req['user']['level'] === null || req['user']['level'] === 'Superadmin' || req['user']['level'] === 'Level 1') {
      queryResult = await this.findAllPengajuanMabes(
        paginationData,
        searchandsortData,
      );
    } else {
      queryResult = await this.findAllPengajuanPolda(
        req,
        paginationData,
        searchandsortData,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGHARGAAN_READ as ConstantLogType,
        message,
        queryResult.formattedResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult.formattedResult,
      page: queryResult.page,
      totalPage: queryResult.totalPage,
      totalData: queryResult.totalData,
    };
  }

  async findAllPengajuan(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'bigint' | 'boolean';
        };
      } = {
        nama: { field: 'personel.nama_lengkap', type: 'string' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        false,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuanPenghargaan] = await this.prisma.$transaction([
        this.prisma.pengajuan_penghargaan.count({
          where,
        }),
        this.prisma.pengajuan_penghargaan.findMany({
          include: {
            personel: {
              select: {
                id: true,
                uid: true,
                nrp: true,
                nama_lengkap: true,
                tanggal_lahir: true,
                jenis_kelamin: true,
                no_hp: true,
                email: true,
                foto_file: true,
                status_aktif: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                status_kawin: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
            penghargaan: {
              select: {
                id: true,
                nama: true,
              },
            },
            penghargaan_tingkat: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const queryResult = [];
      for (const el of pengajuanPenghargaan) {
        let dokumenJSON = [];
        if (Array.isArray(el.files)) {
          dokumenJSON = el.files;
        }

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${el.personel.nrp}/${el.personel.foto_file}`,
        );
        const kepPenghargaan = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          el.kep_penghargaan,
        );

        const formattedFileJSON = [];
        for (let file of dokumenJSON) {
          formattedFileJSON.push({
            ...file,
            url: await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              file.url,
            ),
          });
        }

        const result = {
          ...el,
          id: Number(el.id),
          tanggal_pengajuan: new Date(el.created_at)
            .toISOString()
            .split('T')[0], // YYYY-MM-DD
          pangkat: el.personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: el.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: el.personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
          nama_lengkap: el.personel.nama_lengkap ?? null,
          nrp: el.personel.nrp ?? null,
          status_aktif: el.personel.status_aktif.nama ?? null,
          jenis_kelamin: el.personel.jenis_kelamin ?? null,
          foto_personel: fotoFile,
          kep_penghargaan: kepPenghargaan,
          files: formattedFileJSON,
        };

        // Delete nested properties
        /* delete result.personel.pangkat_personel;
        delete result.personel.jabatan_personel; */
        delete result.personel;

        if (result.jabatan) {
          delete result.jabatan.satuan;
        }

        queryResult.push(result);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllPengajuanMabes(
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'bigint' | 'boolean';
        };
      } = {
        nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
        status: { field: 'status', type: 'string' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        false,
      );

      if (orderBy.length === 0) {
        orderBy.push({ id: 'desc' });
      }

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const formattedWhere = {
        ...where,
        /* status: {
          not: 'Pelengkapan Data',
        }, */
      };

      const [totalData, pengajuanPenghargaan] = await this.prisma.$transaction([
        this.prisma.pengajuan_penghargaan.count({
          where: formattedWhere,
        }),
        this.prisma.pengajuan_penghargaan.findMany({
          include: {
            personel: {
              select: {
                id: true,
                uid: true,
                nrp: true,
                nama_lengkap: true,
                tanggal_lahir: true,
                jenis_kelamin: true,
                no_hp: true,
                email: true,
                foto_file: true,
                status_aktif: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                status_kawin: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
            penghargaan: {
              select: {
                id: true,
                nama: true,
              },
            },
            penghargaan_tingkat: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where: formattedWhere,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const formattedResult = [];

      for (const el of pengajuanPenghargaan) {
        let dokumenJSON = [];
        if (Array.isArray(el.files)) {
          dokumenJSON = el.files;
        }

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${el.personel.nrp}/${el.personel.foto_file}`,
        );
        const kepPenghargaan = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          el.kep_penghargaan,
        );

        const formattedFileJSON = [];
        for (let file of dokumenJSON) {
          formattedFileJSON.push({
            ...file,
            url: await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              file.url,
            ),
          });
        }

        const result = {
          ...el,
          id: Number(el.id),
          tanggal_pengajuan: new Date(el.created_at)
            .toISOString()
            .split('T')[0], // YYYY-MM-DD
          pangkat: el.personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: el.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: el.personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
          nama_lengkap: el.personel.nama_lengkap ?? null,
          nrp: el.personel.nrp ?? null,
          status_aktif: el.personel.status_aktif.nama ?? null,
          jenis_kelamin: el.personel.jenis_kelamin ?? null,
          foto_personel: fotoFile,
          kep_penghargaan: kepPenghargaan,
          files: formattedFileJSON,
        };

        // Delete nested properties
        /* delete result.personel.pangkat_personel;
        delete result.personel.jabatan_personel; */
        delete result.personel;

        if (result.jabatan) {
          delete result.jabatan.satuan;
        }

        formattedResult.push(result);
      }

      return {
        formattedResult,
        page,
        totalPage,
        totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllPengajuanPolda(
    req: Request,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      let adminId = req['user'].personel_id;
      let satuanAdmin = req['user'].satuan_id;

      let adminSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: satuanAdmin,
          },
        });

      //let satuanId = adminSatuan.satuan_id;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'bigint' | 'boolean';
        };
      } = {
        nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
        status: { field: 'status', type: 'string' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        false,
      );

      if (orderBy.length === 0) {
        orderBy.push({ id: 'desc' });
      }

      const formattedWhere = {
        ...where,
        personel: {
          mv_latest_jabatan_personel: {
            satuan: {
              mv_satuan_with_top_parents_self: {
                second_top_parent_id: adminSatuan.second_top_parent_id,
              },
            },
          },
        }
      };

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuanPenghargaan] = await this.prisma.$transaction([
        this.prisma.pengajuan_penghargaan.count({
          where: formattedWhere,
        }),
        this.prisma.pengajuan_penghargaan.findMany({
          include: {
            personel: {
              select: {
                id: true,
                uid: true,
                nrp: true,
                nama_lengkap: true,
                tanggal_lahir: true,
                jenis_kelamin: true,
                no_hp: true,
                email: true,
                foto_file: true,
                status_aktif: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                status_kawin: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
            penghargaan: {
              select: {
                id: true,
                nama: true,
              },
            },
            penghargaan_tingkat: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          where: formattedWhere,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const formattedResult = [];
      for (let el of pengajuanPenghargaan) {
        let dokumenJSON = [];
        if (Array.isArray(el.files)) {
          dokumenJSON = el.files;
        }

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${el.personel.nrp}/${el.personel.foto_file}`,
        );
        const kepPenghargaan = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          el.kep_penghargaan,
        );

        const formattedFileJSON = [];
        for (let file of dokumenJSON) {
          formattedFileJSON.push({
            ...file,
            url: await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              file.url,
            ),
          });
        }

        const result = {
          ...el,
          id: Number(el.id),
          tanggal_pengajuan: new Date(el.created_at)
            .toISOString()
            .split('T')[0], // YYYY-MM-DD
          pangkat: el.personel.pangkat_personel?.[0]?.pangkat ?? null,
          jabatan: el.personel.jabatan_personel?.[0]?.jabatans ?? null,
          satuan: el.personel.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
          nama_lengkap: el.personel.nama_lengkap ?? null,
          nrp: el.personel.nrp ?? null,
          status_aktif: el.personel.status_aktif.nama ?? null,
          jenis_kelamin: el.personel.jenis_kelamin ?? null,
          foto_personel: fotoFile,
          kep_penghargaan: kepPenghargaan,
          files: formattedFileJSON,
        };

        // Delete nested properties
        /* delete result.personel.pangkat_personel;
        delete result.personel.jabatan_personel; */
        delete result.personel;

        if (result.jabatan) {
          delete result.jabatan.satuan;
        }

        formattedResult.push(result);
      }

      return {
        formattedResult,
        page,
        totalPage,
        totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async getPenghargaanLogs(req: any, id: any) {
    try {
      let queryResult = await this.prisma.$transaction(async (trx) => {
        let personel = await trx.personel.findUnique({
          select: {
            nrp: true,
          },
          where: {
            id: id,
          },
        });

        let existingData = await trx.penghargaan_personel.findMany({
          where: {
            personel_id: id,
          },
          include: {
            penghargaan: {
              select: {
                nama: true,
              },
            },
          },
          orderBy: {
            created_at: 'desc',
          },
        });
        const formatted = [];
        for (let obj of existingData) {
          formatted.push({
            ...obj,
            surat_file: await this.minioService.convertFileKeyToURL(
              process.env.MINIO_BUCKET_NAME,
              obj.surat_file,
            ),
          });
        }

        return formatted;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async createPenghargaanPersonel(dataPersonel: any) {
    let penghargaan_personel;
    try {
      await this.prisma.$transaction(async () => {
        const query = {
          data: {
            personel_id: parseInt(dataPersonel.personel_id),
            penghargaan_id: parseInt(dataPersonel.penghargaan_id),
            tingkat_id: parseInt(dataPersonel.tingkat_id),
            tgl_penghargaan: new Date(),
            surat_no: dataPersonel.nomor_surat_kep,
            surat_file: dataPersonel.kep_penghargaan,
            penghargaan_file: dataPersonel.kep_penghargaan,
          },
        };

        penghargaan_personel =
          await this.prisma.penghargaan_personel.create(query);
      });
      return penghargaan_personel;
    } catch (err) {
      throw err;
    }
  }

  private async uploadFiles(files: Express.Multer.File[]) {
    try {
      const uploadedFiles = await Promise.all(
        files.map(async (file) => {
          const uploaded = await this.minioService.uploadFile(file);
          delete file.buffer;
          delete file.fieldname;

          if (!uploaded.ETag) return { rawFile: file };

          return {
            rawFile: file,
            uploaded,
          };
        }),
      );

      return uploadedFiles;
    } catch (err) {
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }

  private async uploadFileWithNRP(file: Express.Multer.File, nrp: string) {
    try {
      const uploaded = await this.minioService.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }

  async generateExcelTemplate(req: any, response: Response) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Template Pengajuan Penghargaan';
    workbook.created = new Date();
    const worksheet = workbook.addWorksheet('Template');
    const dropdownSheetName = 'Dropdown'
    const dropdownSheet = workbook.addWorksheet(dropdownSheetName);
    dropdownSheet.state = 'hidden';

    // Define columns in the templates
    const columns = []
    for (const [key, value] of Object.entries(TEMPLATE_PROPERTIES)) {
      columns.push({ header: value.header, width: 20, key });
    }
    worksheet.columns = columns;

    const listPenghargaan = await this.prisma.penghargaan.findMany({
      select: {
        id: true,
        nama: true,
        is_aktif: true,
      },
      where: {
        deleted_at: null,
      }
    })

    const listTingkat = await this.prisma.penghargaan_tingkat.findMany({
      select: {
        id: true,
        nama: true,
        is_aktif: true
      },
      where: {
        deleted_at: null,
      }
    })

    listPenghargaan.forEach((penghargaan, index) => {
      dropdownSheet.getCell(`A${index + 1}`).value = penghargaan.nama;
    });

    listTingkat.forEach((tingkat, index) => {
      dropdownSheet.getCell(`B${index + 1}`).value = tingkat.nama;
    });

    // Add data validation
    const startRow = 2; // Start from row 2 (row 1 is header)
    const endRow = 500; // Enough rows for most cases

    const startDate = new Date('1971-01-01T00:00:00');
    const endDate = new Date('3025-12-31T23:59:59');

    for (let i = startRow; i <= endRow; i++) {
      for (const [key, value] of Object.entries(TEMPLATE_PROPERTIES)) {
        const column = worksheet.getColumn(value.key);
        const cell = worksheet.getCell(i, column.number);

        if (value.type === 'string') {
          cell.dataValidation = {
            type: 'custom',
            allowBlank: false,
            formulae: [`LEN(${cell.address})>0`], // Ensure it's not empty
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: `${value.header} Tidak Valid`,
            error: `${value.header} tidak boleh kosong`,
          };
        } else if (value.type === 'date') {
          cell.dataValidation = {
            type: 'date',
            operator: 'between',
            allowBlank: false,
            formulae: [startDate, endDate],
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: `${value.header} Tidak Valid`,
            error: `${value.header} harus diisi dengan tanggal`,
          };
        } else if (value.type === 'custom') {
          if (key === 'penghargaan') {
            cell.dataValidation = {
              type: 'list',
              allowBlank: false,
              showErrorMessage: true,
              errorStyle: 'stop',
              errorTitle: `${value.header} Tidak Valid`,
              formulae: [`'${dropdownSheetName}'!$A$1:$A$${listPenghargaan.length}`],
              error: `Harap pilih ${value.header} dari daftar yang tersedia`,
            };
          } else if (key === 'tingkat') {
            cell.dataValidation = {
              type: 'list',
              allowBlank: false,
              showErrorMessage: true,
              errorStyle: 'stop',
              errorTitle: `${value.header} Tidak Valid`,
              formulae: [`'${dropdownSheetName}'!$B$1:$B$${listTingkat.length}`],
              error: `Harap pilih ${value.header} dari daftar yang tersedia`,
            };
          }
        }
      }
    }

    // Optional: You can add sample data or leave empty rows
    /* const queryResult = {
      nrp: '1234567890',
      // pangkat: 'IPTU',
      // satuan: 'Polda Aceh',
      tingkat : "KEPALA SATUAN",
      penghargaan: 'KPLB',
      tanggal_pengajuan: dayjs().format('YYYY-MM-DD'),
    };
    worksheet.addRow(queryResult); */

    // Set the response headers and send the file
    response.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    response.setHeader(
      'Content-Disposition',
      'attachment; filename=Template-Pengajuan-Penghargaan.xlsx',
    );
    await workbook.xlsx.write(response);
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_WRITE_EXCEL as ConstantLogType,
        message,
        [listPenghargaan, listTingkat],
      ),
    );

    response.end();
  }

  async createBulk(req, body: CreatePengajuanPenghargaanDto[]) {
    const queryResult = [];

    let formattedWhere = {}

    if (req['user']['level'] === null || req['user']['level'] === 'Superadmin' || req['user']['level'] === 'Level 1') {
      formattedWhere = {}
    } else if (req['user']['level'] === 'Level 2' || req['user']['level'] === 'Level 3') {
      let satuanAdmin = req['user'].satuan_id;

      let adminSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: satuanAdmin,
          },
        });

      formattedWhere = {
        mv_latest_jabatan_personel: {
          satuan: {
            mv_satuan_with_top_parents_self: {
              second_top_parent_id: adminSatuan.second_top_parent_id,
            },
          },
        },
      }
    } else {
      return
    }
    const errorMessages = []

    for (const entryIndex in body) {

      const entry = body[entryIndex]
      // Convert `Nrp` to string for the query
      const personel = await this.prisma.personel.findFirst({
        where: {
          nrp: String(entry['NRP']),
          /* nama_lengkap: entry['Nama'], */
          ...formattedWhere
        },
      });

      const penghargaan = await this.prisma.penghargaan.findFirst({
        where: {
          nama: entry['Penghargaan'],
        },
      });

      const tingkat = await this.prisma.penghargaan_tingkat.findFirst({
        where: {
          nama: entry['Tingkat'],
        },
      });

      if (!personel || !penghargaan || !tingkat) {
        // Skip or handle the case where personel is not found
        if (!personel) {
          errorMessages.push(`NRP dengan nomor ${entry['NRP']} pada baris ${parseInt(entryIndex) + 1} tidak ditemukan`)
        }
        if (!penghargaan) {
          errorMessages.push(`Penghargaan dengan nama ${entry['Tanhor']} pada baris ${parseInt(entryIndex) + 1} tidak ditemukan`)
        }
        if (!tingkat) {
          errorMessages.push(`Tingkat dengan nama ${entry['Tingkat']} pada baris ${parseInt(entryIndex) + 1} tidak ditemukan`)
        }
        continue;
      }

      // Create the record
      const pengajuanPenghargaan =
        await this.prisma.pengajuan_penghargaan.create({
          data: {
            personel_id: personel.id,
            penghargaan_id: penghargaan.id,
            tingkat_id: tingkat.id,
            created_by: req['user']['personel_id'],
            status: 'Pelengkapan Data',
            created_at: new Date(entry['Tanggal Pengajuan']),
          },
        });

      // Add created record to the list
      queryResult.push(pengajuanPenghargaan);
    }



    // Return all created records after the loop finishes
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: {
        createdRecords: queryResult,
        errorMessages: errorMessages
      },
    };
  }
}
