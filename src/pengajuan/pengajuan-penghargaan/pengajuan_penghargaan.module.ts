import { forwardRef, Module } from '@nestjs/common';
import { PengajuanPenghargaanService } from './service/pengajuan_penghargaan.service';
import { PengajuanPenghargaanController } from './controller/pengajuan_penghargaan.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PengajuanPenghargaanController],
  providers: [PengajuanPenghargaanService, MinioService],
})
export class PengajuanPenghargaanModule {}
