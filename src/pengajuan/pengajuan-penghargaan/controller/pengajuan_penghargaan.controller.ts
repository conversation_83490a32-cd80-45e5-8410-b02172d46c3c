import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PengajuanPenghargaanService } from '../service/pengajuan_penghargaan.service';
import {
  CreatePengajuanPenghargaanDto,
  UpdatePengajuanPenghargaanDto,
} from '../dto/pengajuan_penghargaan.dto';
import { Module, Permission, RolePermissionNeeded } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Response } from 'express';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { UserRoleGuard } from 'src/core/guards/user-role.guards';

@Controller('pengajuan-penghargaan')
@Module(MODULES.HONOR_SUBMISSIONS)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PengajuanPenghargaanController {
  private readonly logger = new Logger(PengajuanPenghargaanController.name);

  constructor(
    private readonly pengajuanPenghargaanService: PengajuanPenghargaanService,
  ) {}

  @Get()
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async findAllPengajuan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanPenghargaanService.findAllPengajuan(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Exiting ${this.findAllPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post()
  @RolePermissionNeeded('PENGHARGAAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async createPengajuanPenghargaan(
    @Req() req: any,
    @Body() body: CreatePengajuanPenghargaanDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.createPengajuanPenghargaan.name} with body: ${JSON.stringify(body)}`,
    );

    const response =
      await this.pengajuanPenghargaanService.createPengajuanPenghargaan(
        req,
        body,
        files,
      );

    this.logger.log(
      `Leaving ${this.createPengajuanPenghargaan.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/logs/:id')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getPenghargaanLogs(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getPenghargaanLogs.name} with id: ${id}`);
    const response = await this.pengajuanPenghargaanService.getPenghargaanLogs(
      req,
      +id,
    );
    this.logger.log(
      `Leaving ${this.getPenghargaanLogs.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/create/bulk')
  @RolePermissionNeeded('PENGHARGAAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(201)
  async createBulk(
    @Req() req: any,
    @Body() body: CreatePengajuanPenghargaanDto[],
  ) {
    this.logger.log(
      `Entering ${this.createBulk.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanPenghargaanService.createBulk(
      req,
      body,
    );
    this.logger.log(
      `Leaving ${this.createBulk.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/pelengkapan/:id')
  @RolePermissionNeeded('PENGHARGAAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async updatePelengkapanPengajuanPenghargaan(
    @Req() req: any,
    @Param('id') id: number,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updatePelengkapanPengajuanPenghargaan.name} with id: ${id}`,
    );
    const response =
      await this.pengajuanPenghargaanService.updatePelengkapanPengajuanPenghargaan(
        req,
        +id,
        files,
      );
    this.logger.log(
      `Leaving ${this.updatePelengkapanPengajuanPenghargaan.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/v2')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async findAllPengajuanV2(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuanV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response =
      await this.pengajuanPenghargaanService.findAllPengajuanV2Process(
        req,
        paginationData,
        searchandsortData,
      );
    this.logger.log(
      `Leaving ${this.findAllPengajuanV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/download-template')
  @RolePermissionNeeded('PENGHARGAAN_AJUKAN_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async downloadTemplate(
    @Req() req: any,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Entering ${this.downloadTemplate.name}`);
    await this.pengajuanPenghargaanService.generateExcelTemplate(req, response);
    this.logger.log(`Leaving ${this.downloadTemplate.name}`);
  }

  @Put(':id')
  @RolePermissionNeeded('PENGHARGAAN', ['PERMISSION_APPROVAL', 'PERMISSION_REJECT'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(
    FileFieldsInterceptor([{ name: 'kep_penghargaan', maxCount: 1 }]),
  )
  async updatePengajuanPenghargaan(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdatePengajuanPenghargaanDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updatePengajuanPenghargaan.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.pengajuanPenghargaanService.updatePengajuanPenghargaan(
        req,
        +id,
        body,
        files,
      );
    this.logger.log(
      `Leaving ${this.updatePengajuanPenghargaan.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
