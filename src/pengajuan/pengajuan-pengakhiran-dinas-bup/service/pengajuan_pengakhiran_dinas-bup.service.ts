import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import * as dayjs from 'dayjs';
import { PersonelPengakhiranDinasBUPDto } from '../dto/pengajuan_pengakhiran_dinas-bup.dto';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';

@Injectable()
export class PengajuanPengakhiranDinasBupService {
  constructor(
    private readonly minioService: MinioService,
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getOne(req: any, id: number) {
    try {
      //Get personel Data
      let personelData: any = await this.prisma.personel.findUniqueOrThrow({
        select: {
          kk_file: true,
          kk_nomor: true,
          ktp_nomor: true,
          ktp_file: true,
          keluarga_personel: {
            select: {
              buku_nikah_file: true,
              foto_file: true,
              hubungan_keluarga: {
                select: {
                  hubungan: true,
                },
              },
            },
          },
          asabri_file: true,
          asabri_nomor: true,
          npwp_file: true,
          npwp_nomor: true,
          tanggal_lahir: true,
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          jenis_kelamin: true,
          no_hp: true,
          email: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
              status_pilihan: true,
            },
          },
          status_kawin: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
          tanhor_personel: {
            select: {
              tanhor: {
                select: {
                  nama: true,
                },
              },
              tanhor_id: true,
              surat_file: true,
            },
          },
          kgb_personel: {
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
        },
        where: {
          id,
        },
      });

      let ktpFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.ktp_file}`,
      );
      let kkFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.kk_file}`,
      );
      let npwpFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.npwp_file}`,
      );
      let asabriFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.asabri_file}`,
      );

      let nikah =
        personelData.jenis_kelamin == 'LAKI_LAKI'
          ? personelData.keluarga_personel.filter(
              (el) => el.hubungan_keluarga?.hubungan == 'ISTRI',
            )
          : personelData.keluarga_personel.filter(
              (el) => el.hubungan_keluarga?.hubungan == 'SUAMI',
            );
      if (nikah.length > 0) {
        nikah[0] = {
          ...nikah[0],
          buku_nikah_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${nikah[0].buku_nikah_file}`,
          ),
          foto_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${nikah[0].foto_file}`,
          ),
        };
      }

      let nararya = personelData.tanhor_personel.filter(
        (el) => el.tanhor_id == 95,
      );
      if (nararya.length > 0) {
        nararya[0] = {
          ...nararya[0],
          surat_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${nararya[0].surat_file}`,
          ),
        };
      }

      const kgbPersonel = personelData.kgb_personel
      if (kgbPersonel.length > 0) {
        kgbPersonel[0] = {
          ...kgbPersonel[0],
          kep_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${kgbPersonel[0].kep_file}`,
          ),
        };
      }

      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${personelData.foto_file}`,
      );

      personelData = {
        ...personelData,
        pangkat: personelData.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan: personelData.jabatan_personel?.[0]?.jabatans ?? null,
        satuan: personelData.jabatan_personel[0]?.jabatans.satuan ?? null,
        nikah: nikah,
        nararya: nararya,
        ktp_file: ktpFile,
        kk_file: kkFile,
        npwp_file: npwpFile,
        asabri_file: asabriFile,
        foto_file: fotoFile,
        kgb_personel : kgbPersonel
      };

      //Calculate personel Age
      const ageYear = dayjs(new Date()).diff(
        personelData.tanggal_lahir,
        'year',
      );
      const ageMonth =
        dayjs(new Date()).diff(personelData.tanggal_lahir, 'month') % 12;

      //Get pengajuan Data
      const pengajuanData =
        await this.prisma.pengajuan_pengakhiran_dinas_bup.findFirst({
          where: {
            personel_id: id,
          },
        });

      const queryResult: {
        personel;
        eligibleForBUP: boolean;
        ageYear: number;
        ageMonth: number;
        pengajuan?;
      } = {
        personel: personelData,
        eligibleForBUP: (ageYear >= 57 && ageMonth >= 6) || ageYear > 57,
        ageYear,
        ageMonth,
      };

      if (pengajuanData) {
        // let pensiunSkep = await this.minioService.convertFileKeyToURL(process.env.MINIO_BUCKET_NAME, pengajuanData.skep_pensiun_file)
        // let pensiunSkep = await this.minioService.convertFileKeyToURL(process.env.MINIO_BUCKET_NAME, `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${pengajuanData.skep_pensiun_file}`)
        let pensiunSkep = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${personelData.nrp}/${pengajuanData.skep_pensiun_file}`,
        );

        queryResult.pengajuan = {
          ...pengajuanData,
          skep_pensiun_file: pensiunSkep,
        };
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateDokumenPribadi(
    req: any,
    id: number,
    files: any,
    data: PersonelPengakhiranDinasBUPDto,
  ) {
    try {
      const queryResult = await this.prisma.$transaction(async () => {
        const query = {
          where: { id },
          data: {
            updated_at: new Date(),
          },
        };

        const listDokumen = ['kk_file', 'ktp_file', 'asabri_file', 'npwp_file'];

        for (let i = 0; i < listDokumen.length; i++) {
          const fileKey = listDokumen[i];
          if (files[fileKey]) {
            const uploadedFile = await this.uploadFile(
              files[fileKey][0],
              req['user'].nrp,
            );
            query.data[fileKey] = uploadedFile.filename;
          }
        }

        const listNomor = [
          'kk_nomor',
          'ktp_nomor',
          'asabri_nomor',
          'npwp_nomor',
        ];

        for (let i = 0; i < listNomor.length; i++) {
          const fileKey = listNomor[i];
          if (data[fileKey]) {
            query.data[fileKey] = data[fileKey];
          }
        }

        const updatedPengajuanPengakhiranDinas =
          await this.prisma.personel.update(query);

        return updatedPengajuanPengakhiranDinas;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File, nrp: String) {
    try {


      const uploaded = await this.minioService.uploadFileWithNRP(file, nrp);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }

  async getUpcomingProcess(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    const level = req['selected_user_role']['level']
    let isMabes = level == 'Superadmin' || level == "Level 1"

    let queryResult;
    if (!isMabes) {
      queryResult = await this.getUpcomingForSatker(
        req,
        paginationData,
        searchandsortData,
      );
    } else {
      queryResult = await this.getUpcoming(
        req,
        paginationData,
        searchandsortData,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult.pengajuan_pengakhiran_dinas,
      page: queryResult.page,
      totalPage: queryResult.totalPage,
      totalData: queryResult.totalData,
    };
  }

  async createPengajuanBUP(req: any, id: number) {
    try {
      //CHECK EXISTING
      let existingData =
        await this.prisma.pengajuan_pengakhiran_dinas_bup.findFirst({
          where: {
            personel_id: id,
          },
        });

      let queryResult;
      if (existingData) {
        queryResult = await this.prisma.pengajuan_pengakhiran_dinas_bup.update({
          data: {
            status: 'DIAJUKAN',
            tanggal_pengajuan: new Date(),
            updated_at: new Date(),
          },
          where: {
            id: existingData.id,
          },
        });
      } else {
        queryResult = await this.prisma.pengajuan_pengakhiran_dinas_bup.create({
          data: {
            personel_id: id,
            status: 'DIAJUKAN',
            tanggal_pengajuan: new Date(),
            created_at: new Date(),
            updated_at: new Date(),
          },
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPSERT_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPSERT as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getUpcoming(req: any, paginationData, searchandsortData) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama: { field: 'nama_lengkap', type: 'string' },
        nrp: { field: 'nrp', type: 'string' },
        pangkat_nama: {
          field: 'pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        },
        /* status_pengajuan: {
          field: 'pengajuan_pengakhiran_dinas_bup_personel.status_pengajuan',
          type: 'string',
        }, */
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const eligigibleForBUPDate = dayjs()
        .subtract(57, 'year')
        .subtract(6, 'month');

      const formattedWhere = {
        ...where,
        tanggal_lahir: {
          lte: eligigibleForBUPDate.toDate(),
        },
        status_aktif: {
          status_pilihan: 'AKTIF',
        },
        mv_pangkat_terakhir : {
          mv_pangkat_terakhir_pangkat_sekarang : {
            kategori_id : 2
          }
        }
      };

      const [totalData, pengajuan_pengakhiran_dinas] =
        await this.prisma.$transaction([
          this.prisma.personel.count({
            where: {
              ...formattedWhere,

            },
          }),
          this.prisma.personel.findMany({
            include: {
              pengajuan_pengakhiran_dinas_bup_personel: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
            // include: {
            //   personel: {
            //     select: {
            //       id: true,
            //       uid: true,
            //       nrp: true,
            //       nama_lengkap: true,
            //       tanggal_lahir: true,
            //       jenis_kelamin: true,
            //       no_hp: true,
            //       email: true,
            //       foto_file: true,
            //       status_aktif: {
            //         select: {
            //           id: true,
            //           nama: true,
            //         },
            //       },
            //       status_kawin: {
            //         select: {
            //           id: true,
            //           nama: true,
            //         },
            //       },
            //       pangkat_personel: {
            //         select: {
            //           pangkat: {
            //             select: {
            //               id: true,
            //               nama: true,
            //               nama_singkat: true,
            //             },
            //           },
            //         },
            //         orderBy: {
            //           tmt: 'desc',
            //         },
            //         take: 1,
            //       },
            //       jabatan_personel: {
            //         select: {
            //           jabatans: {
            //             select: {
            //               id: true,
            //               nama: true,
            //               satuan: {
            //                 select: {
            //                   id: true,
            //                   nama: true,
            //                 },
            //               },
            //             },
            //           },
            //         },
            //         orderBy: {
            //           tmt_jabatan: 'desc',
            //         },
            //         take: 1,
            //       },
            //     },
            //   },
            // },
            take: limit,
            skip: limit * (page - 1),
            orderBy: [

              {
                pengajuan_pengakhiran_dinas_bup_personel: {
                  _count: 'desc',
                },
              },
              {
                tanggal_lahir: 'asc',
              },
              ...orderBy,
            ],
            where: {
              ...formattedWhere
            },
          }),
        ]);

      const totalPage = Math.ceil(totalData / limit);

      // let formatted = pengajuan_pengakhiran_dinas.map(obj=>{
      //   return {
      //     ...obj,
      //     bup : obj.bup.length > 0 ? obj.bup[0] : null
      //   }
      // })

      let formatted = await Promise.all(
        pengajuan_pengakhiran_dinas.map(async (obj) => {
          let bup =
            obj.pengajuan_pengakhiran_dinas_bup_personel.length > 0
              ? obj.pengajuan_pengakhiran_dinas_bup_personel[0]
              : null;
          if (bup) {
            bup = {
              ...bup,
              skep_pensiun_file: await this.minioService.convertFileKeyToURL(
                process.env.MINIO_BUCKET_NAME,
                `${process.env.MINIO_PATH_FILE}${obj.nrp}/${bup.skep_pensiun_file}`,
              ),
            };
          }

          const result = {
            ...obj,

            foto_file: await this.minioService.checkFileExist(
              `${process.env.MINIO_BUCKET_NAME}`,
              `${process.env.MINIO_PATH_FILE}${obj.nrp}/${obj.foto_file}`,
            ),
            bup: bup,
          };

          return result;
        }),
      );

      return {
        pengajuan_pengakhiran_dinas: formatted,
        page,
        totalPage,
        totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async getUpcomingForSatker(req: any, paginationData, searchandsortData) {
    try {
      let adminId = req['user'].personel_id;
      
      let satuanAdmin = req['user'].satuan_id;
      let adminSatuan =
      await this.prisma.mv_satuan_with_top_parents.findFirst({
        where: {
          id: satuanAdmin,
        },
      });

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama: { field: 'nama_lengkap', type: 'string' },
        nrp: { field: 'nrp', type: 'string' },
        pangkat_nama: {
          field: 'pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const eligigibleForBUPDate = dayjs()
        .subtract(57, 'year')
        .subtract(6, 'month');

      const formattedWhere = {
        ...where,
        tanggal_lahir: {
          lte: eligigibleForBUPDate.toDate(),
        },
        status_aktif: {
          status_pilihan: 'AKTIF',
        },
        mv_pangkat_terakhir : {
          mv_pangkat_terakhir_pangkat_sekarang : {
            kategori_id : 2
          }
        },
        mv_latest_jabatan_personel: {
          satuan: {
            mv_satuan_with_top_parents_self: {
              second_top_parent_id   : adminSatuan.second_top_parent_id,
            },
          },
        },
        // sipp_jabatan_satuan_terakhir_personel : {
        //   every :{
        //     satuan_id : satuanId,
        //   }
        // },
      };

      const [totalData, pengajuan_pengakhiran_dinas] =
        await this.prisma.$transaction([
          this.prisma.personel.count({
            where: {
              
              ...formattedWhere,
            },
          }),
          this.prisma.personel.findMany({
            include: {
              pengajuan_pengakhiran_dinas_bup_personel: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
            take: limit,
            skip: limit * (page - 1),
            orderBy: [
              {
                pengajuan_pengakhiran_dinas_bup_personel: {
                  _count: 'desc',
                },
              },
              {
                tanggal_lahir: 'asc',
              },
              ...orderBy,
            ],
            where: {
              ...formattedWhere,
            },
          }),
        ]);

      const totalPage = Math.ceil(totalData / limit);

      // let formatted = pengajuan_pengakhiran_dinas.map(obj=>{
      //   return {
      //     ...obj,

      //     bup : obj.bup.length > 0 ? obj.bup[0] : null
      //   }
      // })

      let formatted = await Promise.all(
        pengajuan_pengakhiran_dinas.map(async (obj) => {
          let bup =
            obj.pengajuan_pengakhiran_dinas_bup_personel.length > 0
              ? obj.pengajuan_pengakhiran_dinas_bup_personel[0]
              : null;
          if (bup) {
            bup = {
              ...bup,
              skep_pensiun_file: await this.minioService.convertFileKeyToURL(
                process.env.MINIO_BUCKET_NAME,
                `${process.env.MINIO_PATH_FILE}${obj.nrp}/${bup.skep_pensiun_file}`,
              ),
            };
          }

          const result = {
            ...obj,

            foto_file: await this.minioService.checkFileExist(
              `${process.env.MINIO_BUCKET_NAME}`,
              `${process.env.MINIO_PATH_FILE}${obj.nrp}/${obj.foto_file}`,
            ),
            bup: bup,
          };

          return result;
        }),
      );

      return {
        // "heheheh",
        pengajuan_pengakhiran_dinas: formatted,
        page,
        totalPage,
        totalData,
      };
    } catch (err) {
      console.error(err);
      throw err;
    }
  }

  async getUserComplete(req, uid) {
    const personel = await this.prisma.personel.findFirst({
      select: {
        id: true,
        uid: true,
        nrp: true,
        nama_lengkap: true,
        tanggal_lahir: true,
        jenis_kelamin: true,
        no_hp: true,
        email: true,
        foto_file: true,
        npwp_file: true,
        ktp_file: true,
        kk_file: true,
        asabri_file: true,
        status_aktif: {
          select: {
            id: true,
            nama: true,
          },
        },
        status_kawin: {
          select: {
            id: true,
            nama: true,
          },
        },
        pangkat_personel: {
          select: {
            pangkat: {
              select: {
                id: true,
                nama: true,
                nama_singkat: true,
              },
            },
            kep_file: true,
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                id: true,
                nama: true,
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
              },
            },
            kep_file: true,
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        kgb_personel: {
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
        keluarga_personel: {
          select: {
            buku_nikah_file: true,
            foto_file: true,
            hubungan_keluarga: {
              select: {
                hubungan: true,
              },
            },
          },
        },
        tanhor_personel: {
          select: {
            tanhor: {
              select: {
                nama: true,
              },
            },
            tanhor_id: true,
            surat_file: true,
          },
        },
      },
      where: { uid: uid },
    });

    if (!personel) {
      throw new NotFoundException(`personel uid ${uid} not found`);
    }

    const currentDate = new Date();
    const birthDate = new Date(personel.tanggal_lahir);

    // Calculate age
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDifference = currentDate.getMonth() - birthDate.getMonth();
    if (
      monthDifference < 0 ||
      (monthDifference === 0 && currentDate.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    const retirementDate = new Date(
      birthDate.getFullYear() + 58,
      birthDate.getMonth(),
      birthDate.getDate(),
    );

    let sisa_dinas = null;
    if (currentDate < retirementDate) {
      const diffTime = Math.abs(
        retirementDate.getTime() - currentDate.getTime(),
      );
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const years = Math.floor(diffDays / 365);
      const remainingDaysAfterYears = diffDays % 365;

      const months = Math.floor(remainingDaysAfterYears / 30);
      const remainingDays = remainingDaysAfterYears % 30;

      if (years > 0) {
        sisa_dinas = `${years} tahun ${months} bulan ${remainingDays} hari`;
      } else if (months > 0) {
        sisa_dinas = `${months} bulan ${remainingDays} hari`;
      } else {
        sisa_dinas = `${remainingDays} hari`;
      }
    } else {
      sisa_dinas = 'Pensiun';
    }

    const result = {
      ...personel,
      id: Number(personel.id),
      tanggal_lahir: new Date(personel.tanggal_lahir)
        .toISOString()
        .split('T')[0], // YYYY-MM-DD
      sisa_dinas: sisa_dinas,
      umur: age,

      // foto_file:

      //production pake ini sementara
      foto_file: await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${personel.nrp}/${personel.foto_file}`,
      ),

      nikah:
        personel.jenis_kelamin == 'LAKI_LAKI'
          ? personel.keluarga_personel.filter(
              (el) => el.hubungan_keluarga.hubungan == 'ISTRI',
            )
          : personel.keluarga_personel.filter(
              (el) => el.hubungan_keluarga.hubungan == 'SUAMI',
            ),
      nararya: personel.tanhor_personel.filter(
        (el) => parseInt(el.tanhor_id + '') == 95,
      ),

      /* pangkat: personel.pangkat_personel?.[0]?.pangkat ?? null,
      jabatan: personel.jabatan_personel?.[0]?.jabatans ?? null,
      satuan: personel.jabatan_personel[0]?.jabatans.satuan ?? null, */
    };

    /* delete result.pangkat_personel;
    delete result.jabatan_personel;
    delete result.jabatan.satuan; */

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: result,
    };
  }

  async approveProcess(req: any, id: number) {

    const level = req['selected_user_role']['level']
    let isMabes = level == 'Superadmin' || level == "Level 1"

    let queryResult;
    if (!isMabes) {
      queryResult = await this.approveSatker(
        req,
        +id,
        req['user']['personel_id'],
      );
    } else {
      queryResult = await this.approve(req, +id, req['user']['personel_id']);
    }
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async approve(req: any, id: number, operatorId: number) {
    const updatedPengajuanPengakhiranDinas =
      await this.prisma.pengajuan_pengakhiran_dinas_bup.update({
        data: {
          status: 'DISETUJUI',
          updated_at: new Date(),
          tanggal_waktu_diterima: new Date(),
          diterima_oleh: operatorId,
        },
        where: {
          id: id,
        },
      });

    return updatedPengajuanPengakhiranDinas;
  }

  async approveSatker(req: any, id: number, operatorId: number) {
    const updatedPengajuanPengakhiranDinas =
      await this.prisma.pengajuan_pengakhiran_dinas_bup.update({
        data: {
          status: 'DISETUJUI_SATKER',
          updated_at: new Date(),
          tanggal_waktu_diterima: new Date(),
          diterima_oleh: operatorId,
        },
        where: {
          id: id,
        },
      });

    return updatedPengajuanPengakhiranDinas;
  }

  async reject(
    req: any,
    id: number,
    operatorId: number,
    pesanPenolakan: string,
  ) {
    const queryResult =
      await this.prisma.pengajuan_pengakhiran_dinas_bup.update({
        data: {
          status: 'DITOLAK',
          tanggal_waktu_ditolak: new Date(),
          diterima_oleh: operatorId,
          detail_penolakan: pesanPenolakan,
        },
        where: {
          id: id,
        },
      });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async uploadSkepPensiun(
    req: any,
    id: number,
    nomorSurat: string,
    file: Express.Multer.File,
  ) {
    const user =
      await this.prisma.pengajuan_pengakhiran_dinas_bup.findUniqueOrThrow({
        select: {
          pengajuan_pengakhiran_dinas_bup_personel: {
            select: {
              id: true,
              nrp: true,
            },
          },
        },
        where: {
          id: id,
        },
      });

    const uploadedFile = await this.uploadFile(
      file,
      user.pengajuan_pengakhiran_dinas_bup_personel.nrp,
    );

    let [queryResult, updatedUser] = await this.prisma.$transaction(
      async (trx) => {
        const updatedPengajuanPengakhiranDinas =
          await trx.pengajuan_pengakhiran_dinas_bup.update({
            data: {
              status: 'SELESAI',
              skep_pensiun_file: uploadedFile.filename,
              skep_pensiun_no: nomorSurat,
              updated_at: new Date(),
            },
            where: {
              id: id,
            },
          });

        const updatedUser = await trx.personel.update({
          data: {
            status_aktif_id: 4,
          },
          where: {
            id: user.pengajuan_pengakhiran_dinas_bup_personel?.id,
          },
        });

        return [updatedPengajuanPengakhiranDinas, updatedUser];
      },
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
