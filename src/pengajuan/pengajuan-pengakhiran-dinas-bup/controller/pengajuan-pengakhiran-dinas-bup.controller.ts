import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PengajuanPengakhiranDinasBupService } from '../service/pengajuan_pengakhiran_dinas-bup.service';
import { PersonelPengakhiranDinasBUPDto } from '../dto/pengajuan_pengakhiran_dinas-bup.dto';
import { Module, Permission, RolePermissionNeeded } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import {
  RejectPengakhiranDinasDto,
  SelesaikanPengakhiranDinasDto,
} from '../../pengajuan-pengakhiran-dinas/dto/pengajuan_pengakhiran_dinas.dto';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { UserRoleGuard } from 'src/core/guards/user-role.guards';

@Controller('pengajuan_pengakhiran_dinas-bup')
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PengajuanPengakhiranDinasBupController {
  private readonly logger = new Logger(
    PengajuanPengakhiranDinasBupController.name,
  );

  constructor(
    private readonly pengajuanPengakhiranDinasBupService: PengajuanPengakhiranDinasBupService,
  ) {}

  @Get('/saya')
  async saya(@Req() req: any) {
    this.logger.log(`Entering ${this.saya.name}`);
    const response = await this.pengajuanPengakhiranDinasBupService.getOne(
      req,
      req['user']['personel_id'],
    );
    this.logger.log(
      `Leaving ${this.saya.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/dto-pribadi')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'asabri_file', maxCount: 1 },
      { name: 'npwp_file', maxCount: 1 },
      { name: 'kk_file', maxCount: 1 },
      { name: 'ktp_file', maxCount: 1 },
    ]),
  )
  async updateDokumenPribadi(
    @Req() req: any,
    @Body() body: PersonelPengakhiranDinasBUPDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updateDokumenPribadi.name} with body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.pengajuanPengakhiranDinasBupService.updateDokumenPribadi(
        req,
        req['user']['personel_id'],
        files,
        body,
      );
    this.logger.log(
      `Leaving ${this.updateDokumenPribadi.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/')
  @Module(MODULES.SERVICE_TERMINATION)
  @Permission('PERMISSION_CREATE')
  async createPengajuanBUP(@Req() req: any) {
    this.logger.log(`Entering ${this.createPengajuanBUP.name}`);
    const response =
      await this.pengajuanPengakhiranDinasBupService.createPengajuanBUP(
        req,
        req['user']['personel_id'],
      );
    this.logger.log(
      `Leaving ${this.createPengajuanBUP.name} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/upcoming')
  @RolePermissionNeeded('SUBBAGSIUN_LURJA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async getUpcoming(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response =
      this.pengajuanPengakhiranDinasBupService.getUpcomingProcess(
        req,
        paginationData,
        searchandsortData,
      );
    this.logger.log(
      `Leaving ${this.getUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/bup-detailed/:id')
  async bupDetailed(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.bupDetailed.name} with id: ${id}`);
    const response = await this.pengajuanPengakhiranDinasBupService.getOne(
      req,
      parseInt(id),
    );
    this.logger.log(
      `Leaving ${this.bupDetailed.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/user-detailed/:uid')
  @HttpCode(200)
  async getSelf(@Req() req: any, @Param('uid') uid: string) {
    this.logger.log(`Entering ${this.getSelf.name} with uid: ${uid}`);
    const response =
      await this.pengajuanPengakhiranDinasBupService.getUserComplete(req, uid);
    this.logger.log(
      `Leaving ${this.getSelf.name} with uid: ${uid} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/approve/:id')
  @Module(MODULES.SERVICE_TERMINATION)
  @RolePermissionNeeded('SUBBAGSIUN_LURJA', ['PERMISSION_APPROVAL'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @Permission('PERMISSION_APPROVAL')
  async approve(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.approve.name} with id: ${id}`);
    const response =
      await this.pengajuanPengakhiranDinasBupService.approveProcess(req, +id);
    this.logger.log(
      `Leaving ${this.approve.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/reject/:id')
  @Module(MODULES.SERVICE_TERMINATION)
  @Permission('PERMISSION_APPROVAL')
  async reject(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: RejectPengakhiranDinasDto,
  ) {
    this.logger.log(
      `Entering ${this.reject.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanPengakhiranDinasBupService.reject(
      req,
      +id,
      req['user']['personel_id'],
      body.alasan_penolakan,
    );
    this.logger.log(
      `Leaving ${this.reject.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/skep-pensiun/:id')
  @Module(MODULES.SERVICE_TERMINATION)
  @Permission('PERMISSION_UPDATE')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'file', maxCount: 1 }]))
  async uploadSkepPensiun(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: SelesaikanPengakhiranDinasDto,
    @UploadedFiles() file: any,
  ) {
    this.logger.log(
      `Entering ${this.uploadSkepPensiun.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response =
      await this.pengajuanPengakhiranDinasBupService.uploadSkepPensiun(
        req,
        +id,
        body.nomor_surat,
        file.file[0],
      );
    this.logger.log(
      `Leaving ${this.uploadSkepPensiun.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }
}
