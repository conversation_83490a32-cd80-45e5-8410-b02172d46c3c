import { forwardRef, Module } from '@nestjs/common';
import { PengajuanPengakhiranDinasBupService } from './service/pengajuan_pengakhiran_dinas-bup.service';
import { PengajuanPengakhiranDinasBupController } from './controller/pengajuan-pengakhiran-dinas-bup.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PengajuanPengakhiranDinasBupController],
  providers: [PengajuanPengakhiranDinasBupService, MinioService],
})
export class PengajuanPengakhiranDinasBupModule {}
