import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PengajuanTanhorService } from '../service/pengajuan_tanhor.service';
import {
  CreatePengajuanTanhorDto,
  UpdatePengajuanTanhorDto,
} from '../dto/pengajuan_tanhor.dto';
import { Module, Permission, RolePermissionNeeded } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Response } from 'express';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';
import { UserRoleGuard } from 'src/core/guards/user-role.guards';

@Controller('pengajuan-tanhor')
@Module(MODULES.HONOR_SUBMISSIONS)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class PengajuanTanhorController {
  private readonly logger = new Logger(PengajuanTanhorController.name);

  constructor(
    private readonly pengajuanTanhorService: PengajuanTanhorService,
  ) {}

  @Get()
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async findAllPengajuan(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)}`,
    );
    const response = await this.pengajuanTanhorService.findAllPengajuan(
      req,
      paginationData,
      searchAndSortData,
    );
    this.logger.log(
      `Exiting ${this.findAllPengajuan.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchAndSortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post()
  @RolePermissionNeeded('TANDA_KEHORMATAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async createPengajuanTanhor(
    @Req() req: any,
    @Body() body: CreatePengajuanTanhorDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.createPengajuanTanhor.name} with body: ${JSON.stringify(body)} and files length: ${JSON.stringify(files.length)}`,
    );

    const response = await this.pengajuanTanhorService.createPengajuanTanhor(
      req,
      body,
      files,
    );

    this.logger.log(
      `Leaving ${this.createPengajuanTanhor.name} with body: ${JSON.stringify(body)} and files length: ${JSON.stringify(files.length)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/logs/:id')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async getTanhorLogs(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getTanhorLogs.name} with id: ${id}`);
    const response = await this.pengajuanTanhorService.getTanhorLogs(
      req,
      Number(id),
    );
    this.logger.log(
      `Leaving ${this.getTanhorLogs.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/create/bulk')
  @RolePermissionNeeded('TANDA_KEHORMATAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async createBulk(@Req() req: any, @Body() body: CreatePengajuanTanhorDto[]) {
    this.logger.log(
      `Entering ${this.createBulk.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanTanhorService.createBulk(req, body);
    this.logger.log(
      `Leaving ${this.createBulk.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );


    return response;
  }

  @Put('/pelengkapan/:id')
  @RolePermissionNeeded('TANDA_KEHORMATAN_AJUKAN_PERSONEL', ['PERMISSION_UPLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async updatePelengkapanPengajuanTanhor(
    @Req() req: any,
    @Param('id') id: number,
    @Body() body: UpdatePengajuanTanhorDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updatePelengkapanPengajuanTanhor.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${JSON.stringify(files.length)}`,
    );
    const response =
      await this.pengajuanTanhorService.updatePelengkapanPengajuanTanhor(
        req,
        +id,
        body,
        files,
      );
    this.logger.log(
      `Leaving ${this.updatePelengkapanPengajuanTanhor.name} with id: ${id} and body: ${JSON.stringify(body)} and files length: ${JSON.stringify(files.length)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/v2')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async findAllPengajuanV2(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuanV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response =
      await this.pengajuanTanhorService.findAllPengajuanV2Process(
        req,
        paginationData,
        searchandsortData,
      );
    this.logger.log(
      `Leaving ${this.findAllPengajuanV2.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/mabes')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllPengajuanMabes(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuanMabes.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.pengajuanTanhorService.findAllPengajuanMabes(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.findAllPengajuanMabes.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/polda')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllPengajuanPolda(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPengajuanPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanTanhorService.findAllPengajuanPolda(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAllPengajuanPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/download-template')
  @RolePermissionNeeded('TANDA_KEHORMATAN_AJUKAN_PERSONEL', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async downloadTemplate(
    @Req() req: any,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Entering ${this.downloadTemplate.name}`);
    await this.pengajuanTanhorService.generateExcelTemplate(req, response);
    this.logger.log(`Leaving ${this.downloadTemplate.name}`);
  }

  @Put(':id')
  @RolePermissionNeeded('TANDA_KEHORMATAN', ['PERMISSION_APPROVAL', 'PERMISSION_REJECT'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'kep_tanhor', maxCount: 1 }]))
  async updatePengajuanTanhor(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePengajuanTanhorDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updatePengajuanTanhor.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanTanhorService.updatePengajuanTanhor(
      req,
      Number(id),
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.updatePengajuanTanhor.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/upcoming-satya-lencana')
  @RolePermissionNeeded('TANDA_KEHORMATAN_BERKALA', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async getUpcoming(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response =
      this.pengajuanTanhorService.getUpcomingUpcomingSatyaLencanaProcess(
        req,
        paginationData,
        searchandsortData,
      );
    this.logger.log(
      `Leaving ${this.getUpcoming.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
