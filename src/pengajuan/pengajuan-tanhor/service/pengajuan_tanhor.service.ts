import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePengajuanTanhorDto,
  UpdatePengajuanTanhorDto,
} from '../dto/pengajuan_tanhor.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import * as ExcelJS from 'exceljs';
import { Response } from 'express';
import * as dayjs from 'dayjs';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

const TEMPLATE_PROPERTIES = {
  nrp: { header: 'NRP', key: 'nrp', index: 0, type: 'string' },
  tanhor: { header: 'Tanhor', key: 'tanhor', index: 1, type: 'custom' },
  tanggal_pengajuan: { header: 'Tanggal Pengajuan', key: 'tanggal_pengajuan', index: 2, type: 'date' },
};

const TEMPLATE_HEADERS_LENGTH = Object.entries(TEMPLATE_PROPERTIES).length;

@Injectable()
export class PengajuanTanhorService {
  constructor(
    private readonly minioService: MinioService,
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) { }

  async createPengajuanTanhor(
    req: any,
    createPengajuanTanhorDto: CreatePengajuanTanhorDto,
    files: any,
  ) {
    let tanhorId = createPengajuanTanhorDto['tanhor_id'];
    let personelId = createPengajuanTanhorDto['personel_id'];

    let existingTanhor = await this.prisma.tanhor_personel.findFirst({
      where: {
        tanhor_id: parseInt(tanhorId),
        personel_id: parseInt(personelId),
      },
    });

    if (existingTanhor) {
      throw new BadRequestException(
        'Personel telah memiliki Tanhor yang dipilih',
        'Personel telah memiliki Tanhor yang dipilih',
      );
    }

    let uploadedFiles = [];
    if (files.files) {
      uploadedFiles = await this.uploadFiles(files.files);
    }

    const queryResult = await this.prisma.$transaction(async () => {
      const query = {
        data: {
          personel_id: parseInt(createPengajuanTanhorDto['personel_id']),
          tanhor_id: parseInt(createPengajuanTanhorDto['tanhor_id']),
          created_by: parseInt(createPengajuanTanhorDto['created_by']),
          files: uploadedFiles.map((obj, idx) => {
            return { name: `file ${idx + 1}`, url: obj.uploaded.Key };
          }),
          status: 'Proses',
          created_at: createPengajuanTanhorDto['created_at'] || new Date(),
        },
      };

      return await this.prisma.pengajuan_tanhor.create(query);
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updatePengajuanTanhor(
    req: any,
    id: number,
    updatePengajuanTanhorDto: UpdatePengajuanTanhorDto,
    files: any,
  ) {
    if (isNaN(id)) throw new BadRequestException('ID is required');

    let queryResult;
    let adminId = req['user'].personel_id;

    const query = {
      where: { id },
      data: {
        ...updatePengajuanTanhorDto,
        approved_by: updatePengajuanTanhorDto.approved_by ? adminId : undefined,
        rejected_by: updatePengajuanTanhorDto.rejected_by ? adminId : undefined,
      },
    };
    await this.prisma.$transaction(async () => {
      const pengajuan_tanhor = await this.prisma.pengajuan_tanhor.findFirst({
        select: {
          pengajuan_tanhor_personel: {
            select: {
              nrp: true,
            },
          },
        },
        where: { id },
      });
      if (!pengajuan_tanhor) {
        throw new NotFoundException(`Pengajuan tanhor tidak ditemukan`);
      }

      if (updatePengajuanTanhorDto['approved_by']) {
        if (files['kep_tanhor']) {
          const uploadedFile = await this.uploadFileWithNRP(
            files['kep_tanhor'][0],
            pengajuan_tanhor.pengajuan_tanhor_personel.nrp,
          );
          query.data['kep_tanhor'] = uploadedFile.Key;
        }
      }

      /* query.data['kep_tanhor'] = "/dev/ssdm-object/1730946155701-1728445040196-satusdm.pdf"; */

      queryResult = await this.prisma.pengajuan_tanhor.update(query);
    });

    let tanhor_personel;
    if (updatePengajuanTanhorDto['approved_by']) {
      tanhor_personel = await this.createTanhorPersonel(queryResult);
    }
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updatePelengkapanPengajuanTanhor(
    req: any,
    id: number,
    updatePengajuanTanhorDto: UpdatePengajuanTanhorDto,
    files: any,
  ) {
    let queryResult;

    let uploadedFiles = await this.uploadFiles(files.files);
    try {
      await this.prisma.$transaction(async () => {
        const pengajuan_tanhor = await this.prisma.pengajuan_tanhor.findFirst({
          where: { id },
        });
        if (!pengajuan_tanhor) {
          throw new NotFoundException(`Pengajuan tanhor tidak ditemukan`);
        }

        const query = {
          where: { id },
          data: {
            /* tanhor_id: parseInt(
              updatePengajuanTanhorDto['tanhor_id'],
            ), */
            files: uploadedFiles.map((obj, idx) => {
              return { name: `file ${idx + 1}`, url: obj.uploaded.Key };
            }),
            status: 'Proses',
          },
        };

        /* if (files['kep_tanhor']) {
          const uploadedFile = await this.uploadFile(files['kep_tanhor'][0]);
          query.data['kep_tanhor'] = uploadedFile.Key;
        } */
        /* query.data['kep_tanhor'] = "/dev/ssdm-object/1730946155701-1728445040196-satusdm.pdf"; */
        queryResult = await this.prisma.pengajuan_tanhor.update(query);
      });

      let tanhor_personel;
      if (updatePengajuanTanhorDto['approved_by']) {
        tanhor_personel = await this.createTanhorPersonel(queryResult);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_TANHOR_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAllPengajuanV2Process(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    let roleType = req['user']['roles'][0].role_tipe;

    let queryResult;
    if (req['user']['level'] === null || req['user']['level'] === 'Superadmin' || req['user']['level'] === 'Level 1') {
      queryResult = await this.findAllPengajuanMabes(
        req,
        paginationData,
        searchandsortData,
      );
    } else {
      queryResult = await this.findAllPengajuanPolda(
        req,
        paginationData,
        searchandsortData,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult.data,
      page: queryResult.page,
      totalPage: queryResult.totalPage,
      totalData: queryResult.totalData,
    };
  }

  async findAllPengajuan(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const columnMapping: IColumnMapping = {
      nama: { field: 'pengajuan_tanhor_personel.nama_lengkap', type: 'string' },
    };
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    const [totalData, pengajuanTanhor] = await this.prisma.$transaction([
      this.prisma.pengajuan_tanhor.count({
        where,
      }),
      this.prisma.pengajuan_tanhor.findMany({
        include: {
          pengajuan_tanhor_personel: {
            select: {
              id: true,
              uid: true,
              nrp: true,
              nama_lengkap: true,
              tanggal_lahir: true,
              jenis_kelamin: true,
              no_hp: true,
              email: true,
              foto_file: true,
              status_aktif: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              status_kawin: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          pengajuan_tanhor_tanhor: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        where,
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = [];
    for (let el of pengajuanTanhor) {
      let dokumenJSON = [];
      if (Array.isArray(el.files)) {
        dokumenJSON = el.files;
      }

      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${el.pengajuan_tanhor_personel.nrp}/${el.pengajuan_tanhor_personel.foto_file}`,
      );

      const kepFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        el.kep_tanhor,
      );
      const formattedFileJSON = [];
      for (let file of dokumenJSON) {
        formattedFileJSON.push({
          ...file,
          url: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            file.url,
          ),
        });
      }

      const result = {
        ...el,
        id: Number(el.id),
        tanggal_pengajuan: new Date(el.created_at).toISOString().split('T')[0], // YYYY-MM-DD
        pangkat:
          el.pengajuan_tanhor_personel.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans ?? null,
        satuan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans
            ?.satuan ?? null,
        nama_lengkap: el.pengajuan_tanhor_personel.nama_lengkap ?? null,
        nrp: el.pengajuan_tanhor_personel.nrp ?? null,
        status_aktif: el.pengajuan_tanhor_personel.status_aktif.nama ?? null,
        jenis_kelamin: el.pengajuan_tanhor_personel.jenis_kelamin ?? null,
        foto_personel: fotoFile,
        kep_tanhor: kepFile,
        files: formattedFileJSON,
      };

      // Delete nested properties
      /* delete result.personel.pangkat_personel;
      delete result.personel.jabatan_personel; */
      delete result.pengajuan_tanhor_personel;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      queryResult.push(result);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findAllPengajuanMabes(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const columnMapping: IColumnMapping = {
      nama_lengkap: { field: 'pengajuan_tanhor_personel.nama_lengkap', type: 'string' },
      nrp: { field: 'pengajuan_tanhor_personel.nrp', type: 'string' },
      status: { field: 'status', type: 'string' },
    };
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    if (orderBy.length === 0) {
      orderBy.push({ id: 'desc' });
    }

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    const formattedWhere = {
      ...where,
      /* status: {
        not: 'Pelengkapan Data',
      }, */
    };

    const [totalData, pengajuanTanhor] = await this.prisma.$transaction([
      this.prisma.pengajuan_tanhor.count({
        where: formattedWhere,
      }),
      this.prisma.pengajuan_tanhor.findMany({
        include: {
          pengajuan_tanhor_personel: {
            select: {
              id: true,
              uid: true,
              nrp: true,
              nama_lengkap: true,
              tanggal_lahir: true,
              jenis_kelamin: true,
              no_hp: true,
              email: true,
              foto_file: true,
              status_aktif: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              status_kawin: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          pengajuan_tanhor_tanhor: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        where: formattedWhere,
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);
    const queryResult = [];
    for (let el of pengajuanTanhor) {
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${el.pengajuan_tanhor_personel.nrp}/${el.pengajuan_tanhor_personel.foto_file}`,
      );

      let dokumenJSON = [];
      if (Array.isArray(el.files)) {
        dokumenJSON = el.files;
      }

      const kepFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        el.kep_tanhor,
      );
      const formattedFileJSON = [];
      for (let file of dokumenJSON) {
        formattedFileJSON.push({
          ...file,
          url: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            file.url,
          ),
        });
      }

      const result = {
        ...el,
        id: Number(el.id),
        tanggal_pengajuan: new Date(el.created_at).toISOString().split('T')[0], // YYYY-MM-DD
        pangkat:
          el.pengajuan_tanhor_personel.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans ?? null,
        satuan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans
            ?.satuan ?? null,
        nama_lengkap: el.pengajuan_tanhor_personel.nama_lengkap ?? null,
        nrp: el.pengajuan_tanhor_personel.nrp ?? null,
        status_aktif: el.pengajuan_tanhor_personel?.status_aktif?.nama ?? null,
        jenis_kelamin: el.pengajuan_tanhor_personel.jenis_kelamin ?? null,
        foto_personel: fotoFile,
        kep_tanhor: kepFile,
        files: formattedFileJSON,
      };

      // Delete nested properties
      /* delete result.personel.pangkat_personel;
      delete result.personel.jabatan_personel; */
      delete result.pengajuan_tanhor_personel;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      queryResult.push(result);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findAllPengajuanPolda(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    let adminId = req['user'].personel_id;
    let satuanAdmin = req['user'].satuan_id;

    let adminSatuan =
      await this.prisma.mv_satuan_with_top_parents.findFirst({
        where: {
          id: satuanAdmin,
        },
      });

    //const satuanId = adminSatuan.satuan_id;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const columnMapping: IColumnMapping = {
      nama_lengkap: { field: 'pengajuan_tanhor_personel.nama_lengkap', type: 'string' },
      nrp: { field: 'pengajuan_tanhor_personel.nrp', type: 'string' },
      status: { field: 'status', type: 'string' },
    };
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    if (orderBy.length === 0) {
      orderBy.push({ id: 'desc' });
    }

    const formattedWhere = {
      ...where,
      pengajuan_tanhor_personel: {
        mv_latest_jabatan_personel: {
          satuan: {
            mv_satuan_with_top_parents_self: {
              second_top_parent_id: adminSatuan.second_top_parent_id,
            },
          },
        },
      },
    };

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    const [totalData, pengajuanTanhor] = await this.prisma.$transaction([
      this.prisma.pengajuan_tanhor.count({
        where: formattedWhere,
      }),
      this.prisma.pengajuan_tanhor.findMany({
        include: {
          pengajuan_tanhor_personel: {
            select: {
              id: true,
              uid: true,
              nrp: true,
              nama_lengkap: true,
              tanggal_lahir: true,
              jenis_kelamin: true,
              no_hp: true,
              email: true,
              foto_file: true,
              status_aktif: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              status_kawin: {
                select: {
                  id: true,
                  nama: true,
                },
              },
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          pengajuan_tanhor_tanhor: {
            select: {
              id: true,
              nama: true,
            },
          },
        },
        where: formattedWhere,
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);
    const queryResult = [];
    for (let el of pengajuanTanhor) {
      let dokumenJSON = [];
      if (Array.isArray(el.files)) {
        dokumenJSON = el.files;
      }
      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${el.pengajuan_tanhor_personel.nrp}/${el.pengajuan_tanhor_personel.foto_file}`,
      );

      const kepFile = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        el.kep_tanhor,
      );
      const formattedFileJSON = [];
      for (let file of dokumenJSON) {
        formattedFileJSON.push({
          ...file,
          url: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            file.url,
          ),
        });
      }

      const result = {
        ...el,
        id: Number(el.id),
        tanggal_pengajuan: new Date(el.created_at).toISOString().split('T')[0], // YYYY-MM-DD
        pangkat:
          el.pengajuan_tanhor_personel.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans ?? null,
        satuan:
          el.pengajuan_tanhor_personel.jabatan_personel?.[0]?.jabatans
            ?.satuan ?? null,
        nama_lengkap: el.pengajuan_tanhor_personel.nama_lengkap ?? null,
        nrp: el.pengajuan_tanhor_personel.nrp ?? null,
        status_aktif: el.pengajuan_tanhor_personel.status_aktif.nama ?? null,
        jenis_kelamin: el.pengajuan_tanhor_personel.jenis_kelamin ?? null,
        foto_personel: fotoFile,
        kep_tanhor: kepFile,
        files: formattedFileJSON,
      };

      // Delete nested properties
      /* delete result.personel.pangkat_personel;
      delete result.personel.jabatan_personel; */
      delete result.pengajuan_tanhor_personel;

      if (result.jabatan) {
        delete result.jabatan.satuan;
      }

      queryResult.push(result);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async getTanhorLogs(req: any, id: number) {
    if (isNaN(id)) throw new BadRequestException('ID is required');
    const queryResult = await this.prisma.$transaction(async (trx) => {
      let personel = await trx.personel.findUnique({
        select: { nrp: true },
        where: { id: id },
      });

      let existingData = await trx.tanhor_personel.findMany({
        where: {
          personel_id: id,
        },
        include: {
          tanhor: {
            select: {
              nama: true,
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const formatted = [];

      for (let obj of existingData) {
        formatted.push({
          ...obj,
          surat_file: await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            obj.surat_file,
          ),
        });
      }

      return formatted;
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async createTanhorPersonel(dataPersonel: any) {
    return await this.prisma.$transaction(async () => {
      const query = {
        data: {
          personel_id: parseInt(dataPersonel.personel_id),
          tanhor_id: parseInt(dataPersonel.tanhor_id),
          tgl_tanhor: new Date(),
          surat_no: dataPersonel.nomor_surat_kep,
          surat_file: dataPersonel.kep_tanhor,
        },
      };

      return await this.prisma.tanhor_personel.create(query);
    });
  }

  private async uploadFiles(files: Express.Multer.File[]) {
    return await Promise.all(
      files.map(async (file) => {
        const uploaded = await this.minioService.uploadFile(file);
        delete file.buffer;
        delete file.fieldname;

        if (!uploaded.ETag) return { rawFile: file };

        return {
          rawFile: file,
          uploaded,
        };
      }),
    );
  }

  private async uploadFileWithNRP(file: Express.Multer.File, nrp: string) {
    const uploaded = await this.minioService.uploadFileWithNRP(file, nrp);
    delete file.buffer;
    delete file.fieldname;

    if (!uploaded.ETag)
      throw new InternalServerErrorException('Return from Minio Invalid');

    return {
      Key: uploaded.Key,
      ETag: uploaded.ETag,
      Location: uploaded.Location,
      filename: uploaded.filename,
    };
  }

  async generateExcelTemplate(req: any, response: Response) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Template Pengajuan Tanhor';
    workbook.created = new Date();
    const worksheet = workbook.addWorksheet('Template');
    const dropdownSheetName = 'Dropdown'
    const dropdownSheet = workbook.addWorksheet(dropdownSheetName);
    dropdownSheet.state = 'hidden';

    // Define columns in the templates
    const columns = []
    for (const [key, value] of Object.entries(TEMPLATE_PROPERTIES)) {
      columns.push({ header: value.header, width: 20, key });
    }
    worksheet.columns = columns;

    const listTanhor = await this.prisma.tanhor.findMany({
      select: {
        id: true,
        nama: true,
        is_aktif: true,
      },
      where: {
        deleted_at: null,
      }
    })

    // Add tanhor names to dropdown sheet
    listTanhor.forEach((tanhor, index) => {
      dropdownSheet.getCell(`A${index + 1}`).value = tanhor.nama;
    });

    // Define named range for tanhor dropdown
    /* workbook.definedNames.add(
      `${dropdownSheet}!$A$1:$A${listTanhor.length}`,
      'TanhorList'
    ); */

    // Add data validation
    const startRow = 2; // Start from row 2 (row 1 is header)
    const endRow = 500; // Enough rows for most cases

    const startDate = new Date('1971-01-01T00:00:00');
		const endDate = new Date('3025-12-31T23:59:59');

    for (let i = startRow; i <= endRow; i++) {
      for (const [key, value] of Object.entries(TEMPLATE_PROPERTIES)) {
        const column = worksheet.getColumn(value.key);
        const cell = worksheet.getCell(i, column.number);

        if (value.type === 'string') {
          cell.dataValidation = {
            type: 'custom',
            allowBlank: false,
            formulae: [`LEN(${cell.address})>0`], // Ensure it's not empty
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: `${value.header} Tidak Valid`,
            error: `${value.header} tidak boleh kosong`,
          };
        } else if (value.type === 'date') {
          cell.dataValidation = {
            type: 'date',
            operator: 'between',
            allowBlank: false,
            formulae: [startDate, endDate],
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: `${value.header} Tidak Valid`,
            error: `${value.header} harus diisi dengan tanggal`,
          };
        } else if (value.type === 'custom') {
          cell.dataValidation = {
            type: 'list',
            allowBlank: false,
            showErrorMessage: true,
            errorStyle: 'stop',
            errorTitle: `${value.header} Tidak Valid`,
            formulae: [`'${dropdownSheetName}'!$A$1:$A$${listTanhor.length}`],
            error: `Harap pilih ${value.header} dari daftar yang tersedia`,
          };
        }
      }
    }

    // Optional: You can add sample data or leave empty rows
    /* const queryResult = {
      nrp: '1234567890',
      /* pangkat: 'IPTU',
      satuan: 'Polda Aceh', 
      tanhor: 'BINTANG JASA',
      tanggal_pengajuan: dayjs().format('YYYY-MM-DD'),
    };
    worksheet.addRow(queryResult); */

    // Set the response headers and send the file
    response.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    response.setHeader(
      'Content-Disposition',
      'attachment; filename=Template-Pengajuan-Tanhor.xlsx',
    );
    await workbook.xlsx.write(response);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_WRITE_EXCEL as ConstantLogType,
        message,
        listTanhor,
      ),
    );

    response.end();
  }

  async createBulk(req, body: CreatePengajuanTanhorDto[]) {
    const createdRecords = [];

    let formattedWhere = {}



    if (req['user']['level'] === null || req['user']['level'] === 'Superadmin' || req['user']['level'] === 'Level 1') {
      formattedWhere = {}
    } else if (req['user']['level'] === 'Level 2' || req['user']['level'] === 'Level 3') {
      let satuanAdmin = req['user'].satuan_id;

      let adminSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: satuanAdmin,
          },
        });

      formattedWhere = {
        mv_latest_jabatan_personel: {
          satuan: {
            mv_satuan_with_top_parents_self: {
              second_top_parent_id: adminSatuan.second_top_parent_id,
            },
          },
        },
      }
    } else {
      return
    }


    const errorMessages = []

    for (const entryIndex in body) {

      const entry = body[entryIndex]

      // Convert `Nrp` to string for the query
      const personel = await this.prisma.personel.findFirst({
        where: {
          nrp: String(entry['NRP']),
          /* nama_lengkap: entry['Nama'], */
          ...formattedWhere
        },
      });

      const tanhor = await this.prisma.tanhor.findFirst({
        where: {
          nama: entry['Tanhor'],
        },
      });

      if (!personel || !tanhor) {
        // Skip or handle the case where personel is not found
        if (!personel) {
          errorMessages.push(`NRP dengan nomor ${entry['NRP']} pada baris ${parseInt(entryIndex) + 1} tidak ditemukan`)
        }
        if (!tanhor) {
          errorMessages.push(`Tanhor dengan nama ${entry['Tanhor']} pada baris ${parseInt(entryIndex) + 1} tidak ditemukan`)
        }
        continue;
      }

      // Create the record
      const pengajuanTanhor = await this.prisma.pengajuan_tanhor.create({
        data: {
          personel_id: personel.id,
          tanhor_id: tanhor.id,
          created_by: req['user']['personel_id'],
          status: 'Pelengkapan Data',
          created_at: new Date(entry['Tanggal Pengajuan']),
        },
      });

      // Add created record to the list
      createdRecords.push(pengajuanTanhor);
    }


    // Return all created records after the loop finishes

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_CREATE as ConstantLogType,
        message,
        createdRecords,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: {
        createdRecords,
        errorMessages: errorMessages
      },
    };
  }

  async getUpcomingUpcomingSatyaLencanaProcess(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    let levelName = req['user'].level_name;
    let satuanID = req['user']?.satuan_id;
    let userRoles = req['user'].roles;
    let queryResult = null;

    if (userRoles.find(el => el.nama === "Operator Level 2 Pengajuan Tanda Kehormatan & Penghargaan") || userRoles.find(el => el.nama === "Operator Level 3 Pengajuan Tanda Kehormatan & Penghargaan")) {
      queryResult = await this.getUpcomingAutomaticTanhorSatker(
        paginationData,
        searchandsortData,
        Number(satuanID),
      );
    } else if (userRoles.find(el => el.nama === "Admin Pengajuan Tanda Kehormatan & Penghargaan") || userRoles.find(el => el.nama === "Operator Level 1 Pengajuan Tanda Kehormatan & Penghargaan") || userRoles.find(el => el.nama === "Superadmin")) {
      queryResult = await this.getUpcomingAutomaticTanhor(
        paginationData,
        searchandsortData,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PENGAJUAN_TANHOR_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult.upcoming,
      page: queryResult.page,
      totalPage: queryResult.totalPage,
      totalData: queryResult.totalData,
    };
  }

  async getUpcomingAutomaticTanhorSatker(
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
    satuanId: number,
  ) {
    if (isNaN(satuanId)) throw new BadRequestException('Satuan ID is Required');

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const columnMapping: IColumnMapping = {
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      nrp: { field: 'nrp', type: 'string' },
    };
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const eligibleFor8Date = dayjs().subtract(8, 'year').add(3, 'month');
    const eligibleFor16Date = dayjs().subtract(16, 'year').add(3, 'month');
    const eligibleFor32Date = dayjs().subtract(32, 'year').add(3, 'month');

    let adminSatuan =
      await this.prisma.mv_satuan_with_top_parents.findFirst({
        where: {
          id: satuanId,
        },
      });

    const formattedWhere = {
      ...where,
      AND: [
        {
          mv_latest_jabatan_personel: {
            satuan: {
              mv_satuan_with_top_parents_self: {
                second_top_parent_id: adminSatuan.second_top_parent_id,
              },
            },
          },
        },
        {
          OR: [
            {
              masa_dinas_surut_tmt: {
                lte: eligibleFor32Date.toDate(),
              },
              tanhor_personel: {
                none: {
                  id: {
                    in: [23],
                  },
                },
              },
              pengajuan_tanhor: {
                none: { id: { gte: 0 } },
              },
            },
            {
              masa_dinas_surut_tmt: {
                lte: eligibleFor16Date.toDate(),
              },
              tanhor_personel: {
                none: {
                  id: {
                    in: [22],
                  },
                },
              },
              pengajuan_tanhor: {
                none: { id: { gte: 0 } },
              },
            },
            {
              masa_dinas_surut_tmt: {
                lte: eligibleFor8Date.toDate(),
              },
              tanhor_personel: {
                none: {
                  id: {
                    in: [21],
                  },
                },
              },
              pengajuan_tanhor: {
                none: { id: { gte: 0 } },
              },
            },
          ],
        },
      ],
    };

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    let [totalData, data] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: formattedWhere,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          masa_dinas_surut_tmt: true,
          no_hp: true,
          email: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          status_kawin: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
          tanhor_personel: {
            where: {
              tanhor_id: {
                in: [21, 22, 23],
              },
            },
          },
          pengajuan_tanhor: true
        },
        where: {
          ...formattedWhere,
        },
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    let formatted = [];

    for (let el of data) {
      let duration = dayjs(new Date())
        .subtract(3, 'month')
        .diff(el.masa_dinas_surut_tmt, 'year');

      let isEligibleFor8Year = true;
      let isEligibleFor16Year = duration >= 16;
      let isEligibleFor32Year = duration >= 32;

      let have8YearReward = el.tanhor_personel.find(
        (obj) => parseInt(obj.id + '') === 21,
      );
      let have16YearReward = el.tanhor_personel.find(
        (obj) => parseInt(obj.id + '') === 22,
      );
      let have32YearReward = el.tanhor_personel.find(
        (obj) => parseInt(obj.id + '') === 23,
      );

      let eligibleFor = [];

      let tanhorToSolveId = 23;
      let tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
        .add(32, 'year')
        .toDate();

      if (isEligibleFor8Year && !have8YearReward) {
        eligibleFor.push('8YEAR');
        tanhorToSolveId = 21;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(8, 'year')
          .toDate();
      }

      if (isEligibleFor16Year && !have16YearReward) {
        eligibleFor.push('16YEAR');
        tanhorToSolveId = 22;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(16, 'year')
          .toDate();
      }

      if (isEligibleFor32Year && !have32YearReward) {
        eligibleFor.push('32YEAR');
        tanhorToSolveId = 23;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(32, 'year')
          .toDate();
      }

      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${el.nrp}/${el.foto_file}`,
      );

      formatted.push({
        ...el,
        pangkat: el.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan: el.jabatan_personel?.[0]?.jabatans ?? null,
        satuan: el.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
        nama_lengkap: el.nama_lengkap ?? null,
        nrp: el.nrp ?? null,
        status_aktif: el.status_aktif.nama ?? null,
        jenis_kelamin: el.jenis_kelamin ?? null,
        foto_personel: fotoFile,
        eligible_for: eligibleFor,
        tanhor_to_solve_id: tanhorToSolveId,
        tanggal_tanhor: tanggalSeharusnyaDapat,
      });
    }

    const totalPage = Math.ceil(totalData / limit);

    return {
      upcoming: formatted,
      page,
      totalPage,
      totalData,
    };
  }

  async getUpcomingAutomaticTanhor(
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;
    const columnMapping: IColumnMapping = {
      nama_lengkap: { field: 'nama_lengkap', type: 'string' },
      nrp: { field: 'nrp', type: 'string' },
    };
    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc as any,
      search_column,
      search_text,
      search,
      columnMapping,
      false,
    );

    const eligibleFor8Date = dayjs().subtract(8, 'year').add(3, 'month');
    const eligibleFor16Date = dayjs().subtract(16, 'year').add(3, 'month');
    const eligibleFor32Date = dayjs().subtract(32, 'year').add(3, 'month');


    const formattedWhere = {
      ...where,
      OR: [
        {
          masa_dinas_surut_tmt: {
            lte: eligibleFor32Date.toDate(),
          },
          tanhor_personel: {
            none: {
              id: {
                in: [23],
              },
            },
          },
        },
        {
          masa_dinas_surut_tmt: {
            lte: eligibleFor16Date.toDate(),
          },
          tanhor_personel: {
            none: {
              id: {
                in: [22],
              },
            },
          },
        },
        {
          masa_dinas_surut_tmt: {
            lte: eligibleFor8Date.toDate(),
          },
          tanhor_personel: {
            none: {
              id: {
                in: [21],
              },
            },
          },
        },
      ],
    };

    const limit = +paginationData.limit || 100;
    const page = +paginationData.page || 1;

    let [totalData, data] = await this.prisma.$transaction([
      this.prisma.personel.count({
        where: formattedWhere,
      }),
      this.prisma.personel.findMany({
        select: {
          id: true,
          uid: true,
          nrp: true,
          nama_lengkap: true,
          tanggal_lahir: true,
          jenis_kelamin: true,
          masa_dinas_surut_tmt: true,
          no_hp: true,
          email: true,
          foto_file: true,
          status_aktif: {
            select: {
              id: true,
              nama: true,
            },
          },
          status_kawin: {
            select: {
              id: true,
              nama: true,
            },
          },
          pangkat_personel: {
            select: {
              pangkat: {
                select: {
                  id: true,
                  nama: true,
                  nama_singkat: true,
                },
              },
            },
            orderBy: {
              tmt: 'desc',
            },
            take: 1,
          },
          jabatan_personel: {
            select: {
              jabatans: {
                select: {
                  id: true,
                  nama: true,
                  satuan: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              tmt_jabatan: 'desc',
            },
            take: 1,
          },
          tanhor_personel: {
            where: {
              tanhor_id: {
                in: [21, 22, 23],
              },
            },
          },
        },
        where: formattedWhere,
        take: limit,
        skip: limit * (page - 1),
        orderBy,
      }),
    ]);

    const formatted = [];
    for (let el of data) {
      let duration = dayjs(new Date())
        .subtract(3, 'month')
        .diff(el.masa_dinas_surut_tmt, 'year');

      let isEligibleFor8Year = true;
      let isEligibleFor16Year = duration >= 16;
      let isEligibleFor32Year = duration >= 32;

      let have8YearReward = el.tanhor_personel.find((obj) => parseInt(obj.id + '') === 21);
      let have16YearReward = el.tanhor_personel.find((obj) => parseInt(obj.id + '') === 22);
      let have32YearReward = el.tanhor_personel.find((obj) => parseInt(obj.id + '') === 23);

      let eligibleFor = [];

      let tanhorToSolveId = 23;
      let tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
        .add(32, 'year')
        .toDate();

      if (isEligibleFor8Year && !have8YearReward) {
        eligibleFor.push('8YEAR');
        tanhorToSolveId = 21;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(8, 'year')
          .toDate();
      }

      if (isEligibleFor16Year && !have16YearReward) {
        eligibleFor.push('16YEAR');
        tanhorToSolveId = 22;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(16, 'year')
          .toDate();
      }

      if (isEligibleFor32Year && !have32YearReward) {
        eligibleFor.push('32YEAR');
        tanhorToSolveId = 23;
        tanggalSeharusnyaDapat = dayjs(el.masa_dinas_surut_tmt)
          .add(32, 'year')
          .toDate();
      }

      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${el.nrp}/${el.foto_file}`,
      );

      const result = {
        ...el,
        pangkat: el.pangkat_personel?.[0]?.pangkat ?? null,
        jabatan: el.jabatan_personel?.[0]?.jabatans ?? null,
        satuan: el.jabatan_personel?.[0]?.jabatans?.satuan ?? null,
        nama_lengkap: el.nama_lengkap ?? null,
        nrp: el.nrp ?? null,
        status_aktif: el.status_aktif.nama ?? null,
        jenis_kelamin: el.jenis_kelamin ?? null,
        foto_personel: fotoFile ?? null,
        eligible_for: eligibleFor,
        tanhor_to_solve_id: tanhorToSolveId,
        tanggal_tanhor: tanggalSeharusnyaDapat,
      };
      formatted.push(result);
    };

    const totalPage = Math.ceil(totalData / limit);

    return {
      upcoming: formatted,
      page,
      totalPage,
      totalData,
    };
  }
}
