import { forwardRef, Module } from '@nestjs/common';
import { PengajuanTanhorService } from './service/pengajuan_tanhor.service';
import { PengajuanTanhorController } from './controller/pengajuan_tanhor.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PengajuanTanhorController],
  providers: [PengajuanTanhorService, MinioService],
})
export class PengajuanTanhorModule {}
