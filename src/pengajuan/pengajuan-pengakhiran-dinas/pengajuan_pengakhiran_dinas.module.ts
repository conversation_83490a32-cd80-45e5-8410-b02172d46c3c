import { forwardRef, Module } from '@nestjs/common';
import { PengajuanPengakhiranDinasService } from './service/pengajuan_pengakhiran_dinas.service';
import { PengajuanPengakhiranDinasController } from './controller/pengajuan_pengakhiran_dinas.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PengajuanPengakhiranDinasController],
  providers: [PengajuanPengakhiranDinasService, MinioService],
})
export class PengajuanPengakhiranDinasModule {}
