import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PengajuanPengakhiranDinasService } from '../service/pengajuan_pengakhiran_dinas.service';
import {
  CreatePengajuanPengakhiranDinasDto,
  UpdatePengajuanPengakhiranDinasDto,
} from '../dto/pengajuan_pengakhiran_dinas.dto';
import { Permission, RolePermissionNeeded } from 'src/core/decorators';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { UserRoleGuard } from 'src/core/guards/user-role.guards';

@Controller('pengajuan_pengakhiran_dinas')
@UseGuards(JwtAuthGuard)
export class PengajuanPengakhiranDinasController {
  private readonly logger = new Logger(
    PengajuanPengakhiranDinasController.name,
  );

  constructor(
    private readonly pengajuanPengakhiranDinasService: PengajuanPengakhiranDinasService,
  ) {}

  @Post()
  @RolePermissionNeeded('SUBBAG_HENTIF', ['PERMISSION_CREATE'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 1 }]))
  async create(
    @Req() req: any,
    @Body() body: CreatePengajuanPengakhiranDinasDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanPengakhiranDinasService.create(
      req,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('polda')
  @RolePermissionNeeded('SUBBAG_HENTIF', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async findForPolda(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findForPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanPengakhiranDinasService.findForPolda(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findForPolda.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get()
  @RolePermissionNeeded('SUBBAG_HENTIF', ['PERMISSION_READ'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.pengajuanPengakhiranDinasService.findAll(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.pengajuanPengakhiranDinasService.findOne(
      req,
      +id,
    );
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'dokumen_putusan_kkep_file', maxCount: 1 },
      { name: 'dokumen_surat_usulan_file', maxCount: 1 },
      { name: 'dokumen_berkas_pemeriksaan_file', maxCount: 1 },
      { name: 'dokumen_kep_pengangkatan_pertama_file', maxCount: 1 },
      { name: 'dokumen_keputusan_pangkat_terakhir_file', maxCount: 1 },
      { name: 'dokumen_kartu_tanda_peserta_file', maxCount: 1 },
      { name: 'dokumen_surat_keterangan_tidak_layak_file', maxCount: 1 },
      { name: 'dokumen_putusan_pengadilan_file', maxCount: 1 },
      { name: 'dokumen_pengambilan_barang_file', maxCount: 1 },
      { name: 'dokumen_kkep_tingkat_banding_file', maxCount: 1 },
      { name: 'dokumen_bap_file', maxCount: 1 },
      { name: 'dokumen_ptdh_file', maxCount: 1 },
      { name: 'dokumen_petikan_ptdh_file', maxCount: 1 },
      { name: 'dokumen_salinan_ptdh_file', maxCount: 1 },
    ]),
  )
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePengajuanPengakhiranDinasDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanPengakhiranDinasService.update(
      req,
      +id,
      body,
      files,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/existing-files/:id')
  @Permission('PERMISSION_READ')
  async getExistingFiles(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getExistingFiles.name} with id: ${id}`);
    const response = await this.pengajuanPengakhiranDinasService.getExistingFiles(
      req,
      +id,
    );
    this.logger.log(
      `Leaving ${this.getExistingFiles.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/acceptReject/:id')
  @Permission('PERMISSION_UPDATE')
  async acceptReject(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePengajuanPengakhiranDinasDto,
  ) {
    this.logger.log(
      `Entering ${this.acceptReject.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const response = await this.pengajuanPengakhiranDinasService.update(
      req,
      +id,
      <UpdatePengajuanPengakhiranDinasDto>{
        status_pengajuan: body['status_pengajuan'],
      },
      {},
    );
    this.logger.log(
      `Leaving ${this.acceptReject.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/dto/:id')
  @Permission('PERMISSION_UPDATE')
  @UseInterceptors(FileFieldsInterceptor([{ name: 'files', maxCount: 10 }]))
  async updateDokumen(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePengajuanPengakhiranDinasDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    this.logger.log(
      `Entering ${this.updateDokumen.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const dokumen = body['dto'];
    const response = await this.pengajuanPengakhiranDinasService.updateDocument(
      req,
      +id,
      body,
      dokumen,
      files,
    );
    this.logger.log(
      `Leaving ${this.updateDokumen.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put('/dto/acceptReject/:id')
  @Permission('PERMISSION_UPDATE')
  async acceptRejectDokumen(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePengajuanPengakhiranDinasDto,
  ) {
    this.logger.log(
      `Entering ${this.acceptRejectDokumen.name} with id: ${id} and body: ${JSON.stringify(body)}`,
    );
    const dokumen = body['dto'];
    const response = await this.pengajuanPengakhiranDinasService.update(
      req,
      +id,
      <UpdatePengajuanPengakhiranDinasDto>{
        [`dokumen_${dokumen}_status`]: body['status_pengajuan'],
      },
      {},
    );
    this.logger.log(
      `Leaving ${this.acceptRejectDokumen.name} with id: ${id} and body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/export/query')
  @RolePermissionNeeded('SUBBAG_HENTIF', ['PERMISSION_DOWNLOAD'])
  @UseGuards(JwtAuthGuard, UserRoleGuard)
  @HttpCode(200)
  async exportSelectedData(
    @Req() req: any,
    @Res() res: Response,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.exportSelectedData.name} with search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const buffer = await this.pengajuanPengakhiranDinasService.getAllForExport(
      req,
      searchandsortData,
    );

    const filename = `Hasil Ekspor - PTDH`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.exportSelectedData.name} with search and sort data: ${JSON.stringify(searchandsortData)} and buffer length: ${buffer.byteLength}`,
    );

    res.end(buffer);
  }

  @Get('/export/polda/query')
  // @Permission('DATA_KELULUSAN_REKRUTMEN_GET_UPLOAD_DATA_SISWA_TEMPLATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async exportSelectedDataPolda(
    @Req() req: any,
    @Res() res: Response,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.exportSelectedDataPolda.name} with search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const buffer =
      await this.pengajuanPengakhiranDinasService.getAllForExportPolda(
        req,
        searchandsortData,
      );

    const filename = `Hasil Ekspor - PTDH`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      `Content-Disposition`,
      `attachment; filename=${filename}.xlsx`,
    );
    res.setHeader('Content-Length', buffer.byteLength);
    this.logger.log(
      `Leaving ${this.exportSelectedDataPolda.name} with search and sort data: ${JSON.stringify(searchandsortData)} and buffer length: ${buffer.byteLength}`,
    );
    res.end(buffer);
  }
}
