import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePengajuanPengakhiranDinasDto,
  UpdatePengajuanPengakhiranDinasDto,
} from '../dto/pengajuan_pengakhiran_dinas.dto';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import * as exceljs from 'exceljs';

@Injectable()
export class PengajuanPengakhiranDinasService {
  constructor(
    private readonly minioService: MinioService,
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(
    req: any,
    createPengajuanPengakhiranDinasDto: CreatePengajuanPengakhiranDinasDto,
    files: any,
  ) {
    try {
      let queryResult = null;
      const deadline = new Date();
      const numToAdd = 2;

      for (let i = 1; i <= numToAdd; i++) {
        deadline.setDate(deadline.getDate() + 1);
        if (deadline.getDay() === 6) {
          deadline.setDate(deadline.getDate() + 2);
        } else if (deadline.getDay() === 0) {
          deadline.setDate(deadline.getDate() + 1);
        }
      }

      await this.prisma.$transaction(async () => {
        const query = {
          data: {
            ...createPengajuanPengakhiranDinasDto,
            personel_id: parseInt(
              createPengajuanPengakhiranDinasDto['personel_id'],
            ),
            pengakhiran_dinas: 'PTDH',
            status_pengajuan: 'Pengajuan',
            created_at: new Date(),
            updated_at: new Date(),
            submit_deadline_date: deadline,
          },
        };

        const personelData = await this.prisma.personel.findFirst({
          select: {
            nrp: true
          },
          where: {
            id: query.data.personel_id
          }
        })

        if (files?.files[0]) {
          const uploadedFile = await this.minioService.uploadFileWithNRP(
            files?.files[0],
            personelData.nrp);
          query.data['surat_kkep_file'] = uploadedFile.filename;
        }
        queryResult =
          await this.prisma.pengajuan_pengakhiran_dinas.create(query);
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async findAll(req: any, paginationData, searchandsortData) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama_lengkap: { field: 'pengajuan_pengakhiran_dinas_personel.nama_lengkap', type: 'string' },
        nrp: { field: 'pengajuan_pengakhiran_dinas_personel.nrp', type: 'string' },
        /* pangkat_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        jabatan_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.jabatan_personel.some.jabatans.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        }, */
        status_pengajuan: {
          field: 'status_pengajuan',
          type: 'string',
        },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuan_pengakhiran_dinas] =
        await this.prisma.$transaction([
          this.prisma.pengajuan_pengakhiran_dinas.count({
            where: where,
          }),
          this.prisma.pengajuan_pengakhiran_dinas.findMany({
            include: {
              pengajuan_pengakhiran_dinas_personel: {
                select: {
                  id: true,
                  uid: true,
                  nrp: true,
                  nama_lengkap: true,
                  tanggal_lahir: true,
                  jenis_kelamin: true,
                  no_hp: true,
                  email: true,
                  foto_file: true,
                  status_aktif: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                  status_kawin: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                  pangkat_personel: {
                    select: {
                      pangkat: {
                        select: {
                          id: true,
                          nama: true,
                          nama_singkat: true,
                        },
                      },
                    },
                    orderBy: {
                      tmt: 'desc',
                    },
                    take: 1,
                  },
                  jabatan_personel: {
                    select: {
                      jabatans: {
                        select: {
                          id: true,
                          nama: true,
                          satuan: {
                            select: {
                              id: true,
                              nama: true,
                            },
                          },
                        },
                      },
                    },
                    orderBy: {
                      tmt_jabatan: 'desc',
                    },
                    take: 1,
                  },
                },
              },
            },
            take: limit,
            skip: limit * (page - 1),
            orderBy: orderBy,
            where: where,
          }),
        ]);

      const totalPage = Math.ceil(totalData / limit);

      const queryResult = [];


      for (let pengajuan of pengajuan_pengakhiran_dinas) {
        let surat_kkep_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.surat_kkep_file}`,
        );
        let dokumen_putusan_kkep_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_putusan_kkep_file}`,
          );
        let dokumen_surat_usulan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_surat_usulan_file}`,
          );
        let dokumen_berkas_pemeriksaan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_berkas_pemeriksaan_file}`,
          );
        let dokumen_kep_pengangkatan_pertama_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_kep_pengangkatan_pertama_file}`,
          );
        let dokumen_keputusan_pangkat_terakhir_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_keputusan_pangkat_terakhir_file}`,
          );
        let dokumen_kartu_tanda_peserta_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${ pengajuan.dokumen_kartu_tanda_peserta_file}`,
          );
        let dokumen_surat_keterangan_tidak_layak_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_surat_keterangan_tidak_layak_file}`,
          );
        let dokumen_putusan_pengadilan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_putusan_pengadilan_file}`,
          );
        let dokumen_pengambilan_barang_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_pengambilan_barang_file}`,
          );
        let dokumen_kkep_tingkat_banding_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_kkep_tingkat_banding_file}`,
          );
        let dokumen_bap_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_bap_file}`,
        );
        let dokumen_ptdh_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_ptdh_file}`,
        );
        let dokumen_petikan_ptdh_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_petikan_ptdh_file}`,
          );
        let dokumen_salinan_ptdh_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_salinan_ptdh_file}`,
          );

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.pengajuan_pengakhiran_dinas_personel.foto_file}`,
        );

        queryResult.push({
          ...pengajuan,
          personel: {
            ...pengajuan.pengajuan_pengakhiran_dinas_personel,
            foto_file: fotoFile,
          },
          surat_kkep_file: surat_kkep_file,
          dokumen_putusan_kkep_file: dokumen_putusan_kkep_file,
          dokumen_surat_usulan_file: dokumen_surat_usulan_file,
          dokumen_berkas_pemeriksaan_file: dokumen_berkas_pemeriksaan_file,
          dokumen_kep_pengangkatan_pertama_file:
            dokumen_kep_pengangkatan_pertama_file,
          dokumen_keputusan_pangkat_terakhir_file:
            dokumen_keputusan_pangkat_terakhir_file,
          dokumen_kartu_tanda_peserta_file: dokumen_kartu_tanda_peserta_file,
          dokumen_surat_keterangan_tidak_layak_file:
            dokumen_surat_keterangan_tidak_layak_file,
          dokumen_putusan_pengadilan_file: dokumen_putusan_pengadilan_file,
          dokumen_pengambilan_barang_file: dokumen_pengambilan_barang_file,
          dokumen_kkep_tingkat_banding_file: dokumen_kkep_tingkat_banding_file,
          dokumen_bap_file: dokumen_bap_file,
          dokumen_ptdh_file: dokumen_ptdh_file,
          dokumen_petikan_ptdh_file: dokumen_petikan_ptdh_file,
          dokumen_salinan_ptdh_file: dokumen_salinan_ptdh_file,
        });
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
      };
    } catch (err) {
      throw err;
    }
  }

  async findForPolda(req, paginationData, searchandsortData) {
    try {
      let adminId = req['user'].personel_id;
      let satuanAdmin = req['user'].satuan_id;

      let adminSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: satuanAdmin,
          },
        });


      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama_lengkap: { field: 'pengajuan_pengakhiran_dinas_personel.nama_lengkap', type: 'string' },
        nrp: { field: 'pengajuan_pengakhiran_dinas_personel.nrp', type: 'string' },
        /* pangkat_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        jabatan_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.jabatan_personel.some.jabatans.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'pengajuan_pengakhiran_dinas_personel.jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        }, */
        status_pengajuan: {
          field: 'status_pengajuan',
          type: 'string',
        },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      let formattedWhere = {
        ...where,
      };

      let formattedIncomplete: any = {
        OR: [
          {
            dokumen_putusan_kkep_file: null,
          },
          {
            dokumen_surat_usulan_file: null,
          },
          {
            dokumen_berkas_pemeriksaan_file: null,
          },
          {
            dokumen_kep_pengangkatan_pertama_file: null,
          },
          {
            dokumen_keputusan_pangkat_terakhir_file: null,
          },
          {
            dokumen_kartu_tanda_peserta_file: null,
          },
          {
            dokumen_surat_keterangan_tidak_layak_file: null,
          },
          {
            dokumen_putusan_pengadilan_file: null,
          },
          {
            dokumen_pengambilan_barang_file: null,
          },
          {
            dokumen_kkep_tingkat_banding_file: null,
          },
        ],
      };

      if (adminSatuan) {
        formattedWhere = {
          ...where,
          pengajuan_pengakhiran_dinas_personel: {
            mv_latest_jabatan_personel: {
              satuan: {
                mv_satuan_with_top_parents_self: {
                  second_top_parent_id   : adminSatuan.second_top_parent_id,
                },
              },
            },
          },
        };
        formattedIncomplete = {
          ...formattedIncomplete,
          pengajuan_pengakhiran_dinas_personel: {
            mv_latest_jabatan_personel: {
              satuan: {
                mv_satuan_with_top_parents_self: {
                  second_top_parent_id   : adminSatuan.second_top_parent_id,
                },
              },
            },
          },
        };
      }

      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, pengajuan_pengakhiran_dinas, totalIncomplete] =
        await this.prisma.$transaction([
          this.prisma.pengajuan_pengakhiran_dinas.count({
            where: formattedWhere,
          }),
          this.prisma.pengajuan_pengakhiran_dinas.findMany({
            // this.prisma.pengajuan_pengakhiran_dinas.findMany({
            //   // select: {
            //   //   id: true,
            //   //   nama: true,
            //   // },
            include: {
              pengajuan_pengakhiran_dinas_personel: {
                select: {
                  id: true,
                  uid: true,
                  nrp: true,
                  nama_lengkap: true,
                  tanggal_lahir: true,
                  jenis_kelamin: true,
                  no_hp: true,
                  email: true,
                  foto_file: true,
                  status_aktif: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                  status_kawin: {
                    select: {
                      id: true,
                      nama: true,
                    },
                  },
                  pangkat_personel: {
                    select: {
                      pangkat: {
                        select: {
                          id: true,
                          nama: true,
                          nama_singkat: true,
                        },
                      },
                    },
                    orderBy: {
                      tmt: 'desc',
                    },
                    take: 1,
                  },
                  jabatan_personel: {
                    select: {
                      jabatans: {
                        select: {
                          id: true,
                          nama: true,
                          satuan: {
                            select: {
                              id: true,
                              nama: true,
                            },
                          },
                        },
                      },
                    },
                    orderBy: {
                      tmt_jabatan: 'desc',
                    },
                    take: 1,
                  },
                },
              },
            },
            take: limit,
            skip: limit * (page - 1),
            orderBy: orderBy,
            where: formattedWhere,
          }),
          this.prisma.pengajuan_pengakhiran_dinas.count({
            where: formattedIncomplete,
          }),
        ]);

      const totalPage = Math.ceil(totalData / limit);

      const queryResult = [];

      for (let pengajuan of pengajuan_pengakhiran_dinas) {
        let surat_kkep_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.surat_kkep_file}`,
        );
        let dokumen_putusan_kkep_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_putusan_kkep_file}`,
          );
        let dokumen_surat_usulan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_surat_usulan_file}`,
          );
        let dokumen_berkas_pemeriksaan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_berkas_pemeriksaan_file}`,
          );
        let dokumen_kep_pengangkatan_pertama_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_kep_pengangkatan_pertama_file}`,
          );
        let dokumen_keputusan_pangkat_terakhir_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_keputusan_pangkat_terakhir_file}`,
          );
        let dokumen_kartu_tanda_peserta_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${ pengajuan.dokumen_kartu_tanda_peserta_file}`,
          );
        let dokumen_surat_keterangan_tidak_layak_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_surat_keterangan_tidak_layak_file}`,
          );
        let dokumen_putusan_pengadilan_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_putusan_pengadilan_file}`,
          );
        let dokumen_pengambilan_barang_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_pengambilan_barang_file}`,
          );
        let dokumen_kkep_tingkat_banding_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_kkep_tingkat_banding_file}`,
          );
        let dokumen_bap_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_bap_file}`,
        );
        let dokumen_ptdh_file = await this.minioService.convertFileKeyToURL(
          process.env.MINIO_BUCKET_NAME,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_ptdh_file}`,
        );
        let dokumen_petikan_ptdh_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_petikan_ptdh_file}`,
          );
        let dokumen_salinan_ptdh_file =
          await this.minioService.convertFileKeyToURL(
            process.env.MINIO_BUCKET_NAME,
            `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.dokumen_salinan_ptdh_file}`,
          );

        const fotoFile = await this.minioService.checkFileExist(
          `${process.env.MINIO_BUCKET_NAME}`,
          `${process.env.MINIO_PATH_FILE}${pengajuan.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan.pengajuan_pengakhiran_dinas_personel.foto_file}`,
        );

        queryResult.push({
          ...pengajuan,
          personel: {
            ...pengajuan.pengajuan_pengakhiran_dinas_personel,
            foto_file: fotoFile,
          },
          surat_kkep_file: surat_kkep_file,
          dokumen_putusan_kkep_file: dokumen_putusan_kkep_file,
          dokumen_surat_usulan_file: dokumen_surat_usulan_file,
          dokumen_berkas_pemeriksaan_file: dokumen_berkas_pemeriksaan_file,
          dokumen_kep_pengangkatan_pertama_file:
            dokumen_kep_pengangkatan_pertama_file,
          dokumen_keputusan_pangkat_terakhir_file:
            dokumen_keputusan_pangkat_terakhir_file,
          dokumen_kartu_tanda_peserta_file: dokumen_kartu_tanda_peserta_file,
          dokumen_surat_keterangan_tidak_layak_file:
            dokumen_surat_keterangan_tidak_layak_file,
          dokumen_putusan_pengadilan_file: dokumen_putusan_pengadilan_file,
          dokumen_pengambilan_barang_file: dokumen_pengambilan_barang_file,
          dokumen_kkep_tingkat_banding_file: dokumen_kkep_tingkat_banding_file,
          dokumen_bap_file: dokumen_bap_file,
          dokumen_ptdh_file: dokumen_ptdh_file,
          dokumen_petikan_ptdh_file: dokumen_petikan_ptdh_file,
          dokumen_salinan_ptdh_file: dokumen_salinan_ptdh_file,
        });
      }
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        page: page,
        totalPage: totalPage,
        totalData: totalData,
        totalIncomplete: totalIncomplete,
      };
    } catch (err) {
      throw err;
    }
  }

  async findOne(req: any, id: number) {
    try {
      const pengajuan_pengakhiran_dinas =
        await this.prisma.pengajuan_pengakhiran_dinas.findFirst({
          include: {
            pengajuan_pengakhiran_dinas_personel: true,
          },
          where: { id, deleted_at: null },
        });

      if (!pengajuan_pengakhiran_dinas) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      const fotoFile = await this.minioService.checkFileExist(
        `${process.env.MINIO_BUCKET_NAME}`,
        `${process.env.MINIO_PATH_FILE}${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.foto_file}`,
      );

      const queryResult = {
        ...pengajuan_pengakhiran_dinas,
        personel: {
          ...pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel,
          foto_file: fotoFile,
        },
        surat_kkep_file: `${process.env.MINIO_PATH_FILE}/${pengajuan_pengakhiran_dinas.surat_kkep_file}`,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getExistingFiles(req: any, id: number) {
    try {
      const pengajuan_pengakhiran_dinas =
        await this.prisma.pengajuan_pengakhiran_dinas.findFirst({
          include: {
            pengajuan_pengakhiran_dinas_personel: {
              select: {
                id: true,
                nrp: true,
                pangkat_personel: {
                  select: {
                    id: true,
                    pangkat_id: true,
                    tmt: true,
                    kep_file: true,
                    kep_nomor: true,
                    pangkat: {
                      select: {
                        nama: true,
                      },
                    },
                  },
                  orderBy: { tmt: 'desc' },
                  take: 1,
                },
                asabri_file: true,
                asabri_nomor: true,
              }
            },
          },
          where: { id, deleted_at: null },
        });

      if (!pengajuan_pengakhiran_dinas) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      let firstPangkat = await this.prisma.pangkat_personel.findFirst({
        select: {
          tmt: true,
          kep_file: true,
          kep_nomor: true,
        },
        where: {
          personel_id: pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.id,
        },
        orderBy: {
          tmt: 'asc',
        },
      });

      const pangkatTerbaruKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.pangkat_personel?.[0].kep_file}`,
      );

      const firstPangkatKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.nrp}/${firstPangkat.kep_file}`,
      );

      const asabriKep = await this.minioService.convertFileKeyToURL(
        process.env.MINIO_BUCKET_NAME,
        `${process.env.MINIO_PATH_FILE}${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.nrp}/${pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.asabri_file}`,
      );

      let queryResult = {}

      if(pangkatTerbaruKep) {
        queryResult = {
          ...queryResult,
          pangkat_terakhir: {
            kep_file: pangkatTerbaruKep,
            kep_nomor: pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.pangkat_personel?.[0].kep_nomor,
            file_key: pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.pangkat_personel?.[0].kep_file
          },
        }
      }

      if(firstPangkatKep) {
        queryResult = {
          ...queryResult,
          pangkat_pertama: {
            kep_file: firstPangkatKep,
            kep_nomor: firstPangkat.kep_nomor,
            file_key: firstPangkat.kep_file
          },
        }
      }

      if(asabriKep) {
        queryResult = {
          ...queryResult,
          asabri: {
            kep_file: asabriKep,
            asabri_nomor: pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.asabri_nomor,
            file_key: pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.asabri_file
          }
        }
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(
    req: any,
    id: number,
    updatePengajuanPengakhiranDinasDto: UpdatePengajuanPengakhiranDinasDto,
    files: any,
  ) {
    try {
      const queryResult = await this.prisma.$transaction(async () => {
        const pengajuan_pengakhiran_dinas =
          await this.prisma.pengajuan_pengakhiran_dinas.findFirst({
            include: {
              pengajuan_pengakhiran_dinas_personel: {
                select: {
                  nrp: true,
                },
              },
            },
            where: { id, deleted_at: null },
          });
        if (!pengajuan_pengakhiran_dinas) {
          throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
        }

        const query = {
          where: { id },
          data: {
            ...updatePengajuanPengakhiranDinasDto,
            updated_at: new Date(),
          },
        };

        const listDokumen = [
          'putusan_kkep',
          'surat_usulan',
          'berkas_pemeriksaan',
          'kep_pengangkatan_pertama',
          'keputusan_pangkat_terakhir',
          'kartu_tanda_peserta',
          'surat_keterangan_tidak_layak',
          'putusan_pengadilan',
          'pengambilan_barang',
          'kkep_tingkat_banding',
          'bap',
          'ptdh',
          'petikan_ptdh',
          'salinan_ptdh',
        ];

        for (let i = 0; i < listDokumen.length; i++) {
          const fileKey = `dokumen_${listDokumen[i]}_file`;
          const existingFileKey = `dokumen_${listDokumen[i]}_file_key`;
          if (files[fileKey]) {
            const uploadedFile = await this.minioService.uploadFileWithNRP(
              files[fileKey][0],
              pengajuan_pengakhiran_dinas.pengajuan_pengakhiran_dinas_personel.nrp);
            query.data[fileKey] = uploadedFile.filename;
          } else if (query.data[existingFileKey]) {
            query.data[fileKey] = query.data[existingFileKey]
            delete query.data[existingFileKey]
          }

          const dateKey = `dokumen_${listDokumen[i]}_date`;
          if (updatePengajuanPengakhiranDinasDto[dateKey]) {
            query.data[dateKey] = new Date(
              updatePengajuanPengakhiranDinasDto[dateKey],
            );
          } else {
            query.data[dateKey] = new Date()
          }
        }

        const updatedPengajuanPengakhiranDinas =
          await this.prisma.pengajuan_pengakhiran_dinas.update(query);

        const user =
          await this.prisma.pengajuan_pengakhiran_dinas.findUniqueOrThrow({
            select: {
              pengajuan_pengakhiran_dinas_personel: {
                select: {
                  id: true,
                  nrp: true,
                },
              },
            },
            where: {
              id: id,
            },
          });

        if (updatePengajuanPengakhiranDinasDto.status_pengajuan === 'Selesai') {
          const updatedUser = await this.prisma.personel.update({
            data: {
              status_aktif_id: 15,
            },
            where: {
              id: user.pengajuan_pengakhiran_dinas_personel?.id,
            },
          });
        }

        return updatedPengajuanPengakhiranDinas;
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async updateDocument(
    req: any,
    id: number,
    updatePengajuanPengakhiranDinasDto: UpdatePengajuanPengakhiranDinasDto,
    dokumen: string,
    files: any,
  ) {
    try {
      const queryResult = await this.prisma.$transaction(async () => {
        const pengajuan_pengakhiran_dinas =
          await this.prisma.pengajuan_pengakhiran_dinas.findFirst({
            where: { id, deleted_at: null },
          });
        if (!pengajuan_pengakhiran_dinas) {
          throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
        }

        const query = {
          where: { id },
          data: {
            [`dokumen_${dokumen}_nama`]:
              updatePengajuanPengakhiranDinasDto['nama'],
            [`dokumen_${dokumen}_date`]:
              updatePengajuanPengakhiranDinasDto['date'],
            [`dokumen_${dokumen}_status`]:
              updatePengajuanPengakhiranDinasDto['status'],
            [`dokumen_${dokumen}_alasan_penolakan`]:
              updatePengajuanPengakhiranDinasDto['alasan_penolakan'],
            updated_at: new Date(),
          },
        };

        if (!!files.files) {
          const uploadedFiles = await this.uploadFiles(files?.files || []);
          query.data[`dokumen_${dokumen}_file`] = JSON.stringify(
            uploadedFiles.map((file) => file.uploaded.filename),
          );
        }

        const updatedPengajuanPengakhiranDinas =
          await this.prisma.pengajuan_pengakhiran_dinas.update(query);

        return updatedPengajuanPengakhiranDinas;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async remove(id: number) {
    try {
      const pengajuan_pengakhiran_dinas =
        await this.prisma.pengajuan_pengakhiran_dinas.findFirst({
          where: { id, deleted_at: null },
        });
      if (!pengajuan_pengakhiran_dinas) {
        throw new NotFoundException(`Tanda Kehormatan tidak ditemukan`);
      }

      await this.prisma.pengajuan_pengakhiran_dinas.update({
        where: { id },
        data: {
          updated_at: new Date(),
          deleted_at: new Date(),
        },
      });
    } catch (err) {
      throw err;
    }
  }

  private async uploadFiles(files: Express.Multer.File[]) {
    try {
      const uploadedFiles = await Promise.all(
        files.map(async (file) => {
          const uploaded = await this.minioService.uploadFile(file);
          delete file.buffer;
          delete file.fieldname;

          if (!uploaded.ETag) return { rawFile: file };

          return {
            rawFile: file,
            uploaded,
          };
        }),
      );

      return uploadedFiles;
    } catch (err) {
      throw err;
    }
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploaded = await this.minioService.uploadFile(file);
      delete file.buffer;
      delete file.fieldname;

      if (!uploaded.ETag)
        throw new InternalServerErrorException('Return from Minio Invalid');

      return {
        Key: uploaded.Key,
        ETag: uploaded.ETag,
        Location: uploaded.Location,
        filename: uploaded.filename,
      };
    } catch (err) {
      throw err;
    }
  }

  async getAllForExport(req: any, searchandsortData: SearchAndSortDTO) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
        pangkat_nama: {
          field: 'personel.pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        jabatan_nama: {
          field: 'personel.jabatan_personel.some.jabatans.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'personel.jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        },
        status_pengajuan: {
          field: 'status_pengajuan',
          type: 'string',
        },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [pengajuan_pengakhiran_dinas] = await this.prisma.$transaction([
        this.prisma.pengajuan_pengakhiran_dinas.findMany({
          include: {
            pengajuan_pengakhiran_dinas_personel: {
              select: {
                id: true,
                uid: true,
                nrp: true,
                nama_lengkap: true,
                tanggal_lahir: true,
                jenis_kelamin: true,
                no_hp: true,
                email: true,
                foto_file: true,
                status_aktif: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                status_kawin: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
          },
          where,
          orderBy,
        }),
      ]);

      const workbook = new exceljs.Workbook();
      const worksheet = workbook.addWorksheet('PTDH');
      const header = [
        'No',
        'Nama',
        'NRP',
        'Pangkat',
        'Satuan',
        'Jenis Pengakhiran Dinas',
        'Tanggal KKEP/Banding KKEP',
        'Tanggal Selesai',
        'Status Pengajuan',
      ];

      const headerRow = worksheet.addRow(header);

      let idx = 1
      for (const khirdin of pengajuan_pengakhiran_dinas) {
        const tanggalKKEP = new Date(khirdin?.tanggal_diterima);
        const numToAdd = 30;

        for (let i = 1; i <= numToAdd; i++) {
          tanggalKKEP.setDate(tanggalKKEP.getDate() + 1);
          if (tanggalKKEP.getDay() === 6) {
            tanggalKKEP.setDate(tanggalKKEP.getDate() + 2);
          } else if (tanggalKKEP.getDay() === 0) {
            tanggalKKEP.setDate(tanggalKKEP.getDate() + 1);
          }
        }

        worksheet.addRow([
          idx,
          khirdin.pengajuan_pengakhiran_dinas_personel?.nama_lengkap,
          khirdin.pengajuan_pengakhiran_dinas_personel?.nrp,
          khirdin?.pengajuan_pengakhiran_dinas_personel?.pangkat_personel[0]
            ?.pangkat?.nama,
          khirdin?.pengajuan_pengakhiran_dinas_personel?.jabatan_personel[0]
            .jabatans?.satuan?.nama,
          'PTDH',
          khirdin?.status_pengajuan === 'Pengajuan' ? tanggalKKEP : '-',
          khirdin?.status_pengajuan === 'Selesai' ? khirdin.completed_at : '-',
          khirdin?.status_pengajuan,
        ]);

        idx++;

      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_WRITE_EXCEL as ConstantLogType,
          message,
          pengajuan_pengakhiran_dinas,
        ),
      );

      return await workbook.xlsx.writeBuffer();
    } catch (err) {
      throw err;
    }
  }

  async getAllForExportPolda(
    req: Request,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      let adminId = req['user'].personel_id;

      let satuanAdmin = req['user'].satuan_id;
      let adminSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: satuanAdmin,
          },
        });
      const satuanId = adminSatuan.second_top_parent_id;
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'boolean' | 'bigint';
        };
      } = {
        nama_lengkap: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
        pangkat_nama: {
          field: 'personel.pangkat_personel.some.pangkat.nama',
          type: 'string',
        },
        jabatan_nama: {
          field: 'personel.jabatan_personel.some.jabatans.nama',
          type: 'string',
        },
        satuan_nama: {
          field: 'personel.jabatan_personel.some.jabatans.satuan.nama',
          type: 'string',
        },
        status_pengajuan: {
          field: 'status_pengajuan',
          type: 'string',
        },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const formattedWhere = {
        ...where,
        pengajuan_pengakhiran_dinas_personel: {
          mv_latest_jabatan_personel: {
            satuan: {
              mv_satuan_with_top_parents_self: {
                second_top_parent_id: satuanId,
              },
            },
          },
        },
      };

      const [pengajuan_pengakhiran_dinas] = await this.prisma.$transaction([
        this.prisma.pengajuan_pengakhiran_dinas.findMany({
          include: {
            pengajuan_pengakhiran_dinas_personel: {
              select: {
                id: true,
                uid: true,
                nrp: true,
                nama_lengkap: true,
                tanggal_lahir: true,
                jenis_kelamin: true,
                no_hp: true,
                email: true,
                foto_file: true,
                status_aktif: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                status_kawin: {
                  select: {
                    id: true,
                    nama: true,
                  },
                },
                pangkat_personel: {
                  select: {
                    pangkat: {
                      select: {
                        id: true,
                        nama: true,
                        nama_singkat: true,
                      },
                    },
                  },
                  orderBy: {
                    tmt: 'desc',
                  },
                  take: 1,
                },
                jabatan_personel: {
                  select: {
                    jabatans: {
                      select: {
                        id: true,
                        nama: true,
                        satuan: {
                          select: {
                            id: true,
                            nama: true,
                          },
                        },
                      },
                    },
                  },
                  orderBy: {
                    tmt_jabatan: 'desc',
                  },
                  take: 1,
                },
              },
            },
          },
          where: formattedWhere,
          orderBy,
        }),
      ]);

      const workbook = new exceljs.Workbook();
      const worksheet = workbook.addWorksheet('PTDH');
      const header = [
        'No',
        'Nama',
        'NRP',
        'Pangkat',
        'Satuan',
        'Jenis Pengakhiran Dinas',
        'Tanggal KKEP/Banding KKEP',
        'Tanggal Selesai',
        'Status Pengajuan',
      ];

      const headerRow = worksheet.addRow(header);

      for (const khirdin of pengajuan_pengakhiran_dinas) {
        const tanggalKKEP = new Date(khirdin?.tanggal_diterima);
        const numToAdd = 30;

        for (let i = 1; i <= numToAdd; i++) {
          tanggalKKEP.setDate(tanggalKKEP.getDate() + 1);
          if (tanggalKKEP.getDay() === 6) {
            tanggalKKEP.setDate(tanggalKKEP.getDate() + 2);
          } else if (tanggalKKEP.getDay() === 0) {
            tanggalKKEP.setDate(tanggalKKEP.getDate() + 1);
          }
        }

        worksheet.addRow([
          khirdin.id,
          khirdin.pengajuan_pengakhiran_dinas_personel?.nama_lengkap,
          khirdin.pengajuan_pengakhiran_dinas_personel?.nrp,
          khirdin?.pengajuan_pengakhiran_dinas_personel?.pangkat_personel[0]
            ?.pangkat?.nama,
          khirdin?.pengajuan_pengakhiran_dinas_personel?.jabatan_personel[0]
            .jabatans?.satuan?.nama,
          'PTDH',
          khirdin?.status_pengajuan === 'Pengajuan' ? tanggalKKEP : '-',
          khirdin?.status_pengajuan === 'Selesai' ? khirdin.completed_at : '-',
          khirdin?.status_pengajuan,
        ]);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PENGAJUAN_PENGAKHIRAN_DINAS_WRITE_EXCEL as ConstantLogType,
          message,
          pengajuan_pengakhiran_dinas,
        ),
      );
      return await workbook.xlsx.writeBuffer();
    } catch (err) {
      throw err;
    }
  }
}
