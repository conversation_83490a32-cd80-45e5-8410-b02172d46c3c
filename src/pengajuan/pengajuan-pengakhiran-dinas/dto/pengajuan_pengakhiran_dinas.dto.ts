import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty } from 'class-validator';

export class CreatePengajuanPengakhiranDinasDto {
  @IsNotEmpty()
  id: number;
  status_pengajuan?: string;
}

export class SelesaikanPengakhiranDinasDto {
  nomor_surat: string;
}

export class RejectPengakhiranDinasDto {
  alasan_penolakan: string;
}

export class UpdatePengajuanPengakhiranDinasDto extends PartialType(
  CreatePengajuanPengakhiranDinasDto,
) {}
