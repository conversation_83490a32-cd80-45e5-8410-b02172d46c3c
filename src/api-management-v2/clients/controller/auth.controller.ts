import { Body, Controller, Post, Req } from '@nestjs/common';

import { LoginClientDTO } from 'src/api-management-v2/dto/client.dto';
import { AuthClientService } from '../service/auth.service';

@Controller('client/auth')
export class AuthClientController {
  constructor(private authService: AuthClientService) {}

  @Post('login')
  async login(@Req() req: any, @Body() body: LoginClientDTO) {
    return this.authService.login(req, body);
  }

  @Post('logout')
  async logout(@Req() req: any) {
    return this.authService.logout(req.user);
  }
}
