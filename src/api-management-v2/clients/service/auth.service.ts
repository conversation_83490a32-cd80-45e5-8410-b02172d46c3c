import { HttpStatus, Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { LoginClientDTO } from 'src/api-management-v2/dto/client.dto';
import { decrypt } from 'src/core/utils/encryption.utils';

@Injectable()
export class AuthClientService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
  ) {}

  async login(req: any, body: LoginClientDTO): Promise<any> {
    const { username, password } = body;

    const client = await this.prisma.api_management_client_auth.findUnique({
      where: { username, deleted_at: null },
      select: {
        id: true,
        username: true,
        password: true,
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            uid: true,
            is_active: true,
          },
        },
      },
    });

    if (!client)
      throw new UnauthorizedException(
        'Username atau password yang Anda masukkan salah',
      );

    const decryptedPassword = decrypt(client.password);
    if (decryptedPassword !== password) {
      throw new UnauthorizedException(
        'Username atau password yang Anda masukkan salah',
      );
    }

    const accessToken = this.jwtService.sign({ unique_id: client.client.uid });
    const results = {
      user: {
        id: client.id,
        nama: client.client.name,
        username: client.username,
        email: client.client.email,
        unique_id: client.client.uid,
        is_active: client.client.is_active,
      },
      accessToken,
    };

    return {
      statusCode: HttpStatus.OK,
      message: 'Login successful',
      data: results,
    };
  }

  async validateUser(uniqueID: string) {
    const client = await this.prisma.api_management_client_auth.findFirst({
      where: {
        client: {
          uid: uniqueID,
          deleted_at: null,
          is_active: true,
        },
      },
      select: {
        id: true,
        username: true,
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            uid: true,
            is_active: true,
          },
        },
      },
    });

    if (!client) throw new UnauthorizedException();
    return { user: client };
  }

  async logout(req: any): Promise<any> {
    return { statusCode: HttpStatus.OK, message: 'Logout successful' };
  }
}
