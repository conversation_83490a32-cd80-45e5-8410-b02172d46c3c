import { Global, Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthClientService } from './service/auth.service';
import { ClientJwtStrategy } from 'src/core/configs/client-jwt.strategy';
import { PrismaModule } from 'src/api-utils/prisma/prisma.module';
import { AuthClientController } from './controller/auth.controller';

@Global()
@Module({
  imports: [
    PrismaModule,
    PassportModule.register({ defaultStrategy: 'jwt-client' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET_KEY_CLIENT'),
        signOptions: {
          expiresIn: config.get<string>('JWT_EXPIRED_TIME_CLIENT'),
        },
      }),
    }),
  ],
  controllers: [AuthClientController],
  providers: [AuthClientService, ClientJwtStrategy],
  exports: [AuthClientService],
})
export class ClientAPIManagementV2Module {}
