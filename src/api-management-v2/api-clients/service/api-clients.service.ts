import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';

@Injectable()
export class ApiServiceV2 {
  constructor(private readonly prisma: PrismaService) {}

  async get(req: any, apiUrl: string, paginationData: any) {
    const filters = { ...req.query };
    delete filters.page;
    delete filters.limit;

    const page = paginationData.page ?? 1;
    const limit = paginationData.limit ?? 10;

    const client = await this.prisma.api_management_clients.findFirst({
      where: { id: 7, deleted_at: null },
    });
    if (!client) throw new UnauthorizedException();

    const masterEndpoint = await this.prisma.api_management_endpoints.findFirst(
      {
        select: {
          table: true,
          clients: {
            select: { response_fields: true },
            where: { client_id: client.id },
          },
        },
        where: {
          path: `/${apiUrl}`,
          method: 'GET',
          clients: { some: { client_id: client.id } },
        },
      },
    );
    if (!masterEndpoint) throw new NotFoundException();

    const { rawQuery, params, whereSQL, joinSQL } = await this.generateRawQuery(
      masterEndpoint,
      paginationData,
      filters,
    );

    console.log(
      '\n🕵️ Full SQL:',
      rawQuery.replace(/\$\d+/g, (m) => {
        const index = parseInt(m.slice(1)) - 1;
        const val = params[index];
        return typeof val === 'string' ? `'${val}'` : val;
      }),
    );

    const [rows, countResArray] = await this.prisma.$transaction([
      this.prisma.$queryRawUnsafe(rawQuery, ...params),
      this.prisma.$queryRawUnsafe(
        `SELECT COUNT(*)::int AS count FROM ${masterEndpoint.table} ${joinSQL} ${whereSQL}`,
        ...params,
      ),
    ]);

    const transform = (data) => {
      return data.map((item) => {
        const result = {};

        for (const [key, value] of Object.entries(item)) {
          const [parent, child] = key.split('.');

          if (!result[parent]) result[parent] = {};
          result[parent][child] = value;
        }

        return result;
      });
    };

    const transformed = transform(rows);

    const totalData = (countResArray[0] as any).count as number;
    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: 200,
      message: 'Success',
      data: transformed,
      page,
      totalPage,
      totalData,
    };
  }

  async create(body: Record<string, Record<string, any>>) {
    return await this.prisma.$transaction(async (tx) => {
      const insertedIds: Record<string, any> = {};
      const insertResults: Record<string, any> = {};
      const responseFieldConfigs: Record<string, string[]> = {};

      for (const [table, fields] of Object.entries(body)) {
        const endpoint = await tx.api_management_endpoints.findFirst({
          where: { table, method: 'POST' },
        });
        if (!endpoint) {
          throw new NotFoundException(
            `Endpoint for table '${table}' not found`,
          );
        }

        const clientEndpoint =
          await tx.api_management_client_endpoints.findFirst({
            where: { client_id: 7, endpoint_id: endpoint.id },
          });
        if (!clientEndpoint) {
          throw new NotFoundException(
            `Client endpoint configuration for table '${table}' not found`,
          );
        }

        const requestFields = (
          clientEndpoint.request_fields as Record<string, string[]>
        )[table];
        if (!requestFields) {
          throw new BadRequestException(
            `No request field rules found for table '${table}'`,
          );
        }

        const validationResult = this.validateRequestFields(
          fields,
          requestFields,
        );
        if (!validationResult.isValid) {
          throw new BadRequestException(validationResult.message);
        }

        const responseFields = (
          clientEndpoint.response_fields as Record<string, string[]>
        )[table];
        if (responseFields) {
          responseFieldConfigs[table] = responseFields;
        }

        const dataToInsert = { ...fields };

        const columnInfo = await tx.$queryRawUnsafe<any[]>(
          `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1`,
          table,
        );

        const columnTypes = Object.fromEntries(
          columnInfo.map((c) => [c.column_name, c.data_type]),
        );

        for (const [col, val] of Object.entries(dataToInsert)) {
          if (typeof val === 'string' && val.startsWith('__USE_ID_FROM__:')) {
            const targetTable = val.split(':')[1];
            if (insertedIds[targetTable]) {
              dataToInsert[col] = insertedIds[targetTable];
            } else {
              throw new Error(
                `Missing inserted ID for table '${targetTable}' needed by '${table}.${col}'`,
              );
            }
          }

          const columnType = columnTypes[col];
          if (
            columnType &&
            ['date', 'timestamp', 'timestamp without time zone'].includes(
              columnType,
            ) &&
            typeof val === 'string'
          ) {
            const date = new Date(val);
            if (!isNaN(date.getTime())) {
              dataToInsert[col] = date;
            } else {
              throw new BadRequestException(
                `Invalid date format for field '${col}': ${val}`,
              );
            }
          }
        }

        const inserted = await tx[table].create({ data: { ...dataToInsert } });
        insertResults[table] = inserted;

        if ('id' in inserted) {
          insertedIds[table] = inserted.id;
        } else if ('uid' in inserted) {
          insertedIds[table] = inserted.uid;
        }
      }

      const filteredResponseData: Record<string, any> = {};
      for (const [table, record] of Object.entries(insertResults)) {
        const allowedFields = responseFieldConfigs[table];
        if (allowedFields && Array.isArray(allowedFields)) {
          const filteredRecord: Record<string, any> = {};
          for (const field of allowedFields) {
            if (record[field] !== undefined) {
              filteredRecord[field] = record[field];
            }
          }
          filteredResponseData[table] = filteredRecord;
        } else {
          filteredResponseData[table] = record;
        }
      }

      return {
        message: 'Success',
        data: filteredResponseData,
      };
    });
  }

  private async generateRawQuery(
    masterEndpoint: any,
    paginationData: any,
    filters: Record<string, string | string[]>,
  ) {
    const tableName = masterEndpoint.table;
    const responseFields = masterEndpoint.clients[0].response_fields;

    const columnsResult = await this.prisma.$queryRawUnsafe<any[]>(
      `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1`,
      tableName,
    );

    const columnTypes = Object.fromEntries(
      columnsResult.map((c) => [c.column_name, c.data_type]),
    );
    const tableColumns = Object.keys(columnTypes);

    const selectFields: string[] = [];
    const joinStatements = new Set<string>();
    const usedAlias = new Set<string>();
    const joinedTables = new Set<string>();
    const failedJoins = new Set<string>();

    const addJoin = async (tbl: string) => {
      if (tbl === tableName || usedAlias.has(tbl)) return;

      // FK dari base table ke target table
      const fk = [`${tbl}_id`, `id_${tbl}`, `${tbl}Id`, `${tbl}ID`].find((f) =>
        tableColumns.includes(f),
      );
      if (fk) {
        joinStatements.add(
          `LEFT JOIN ${tbl} ON ${tbl}.id = ${tableName}.${fk}`,
        );
        usedAlias.add(tbl);
        joinedTables.add(tbl);
        return;
      }

      // FK dari target table ke base table (reverse join)
      const reverseColumns = await this.prisma.$queryRawUnsafe<any[]>(
        `SELECT column_name FROM information_schema.columns WHERE table_name = $1`,
        tbl,
      );
      const reverseFK = [
        `${tableName}_id`,
        `id_${tableName}`,
        `${tableName}Id`,
        `${tableName}ID`,
      ].find((f) => reverseColumns.some((col) => col.column_name === f));
      if (reverseFK) {
        joinStatements.add(
          `LEFT JOIN ${tbl} ON ${tbl}.${reverseFK} = ${tableName}.id`,
        );
        usedAlias.add(tbl);
        joinedTables.add(tbl);
        return;
      }

      console.warn(
        `⚠️ Tidak ada FK antara '${tableName}' dan '${tbl}', skip JOIN`,
      );
      failedJoins.add(tbl);
    };

    // JOIN untuk setiap tabel dari response_fields
    for (const tbl of Object.keys(responseFields)) {
      await addJoin(tbl);
    }

    // JOIN untuk setiap tabel dari filters (jika belum)
    for (const rawKey of Object.keys(filters)) {
      const tbl = rawKey.split('.')[0];
      if (!usedAlias.has(tbl)) {
        await addJoin(tbl);
      }
    }

    // SELECT field hanya jika table sudah sukses di-JOIN
    for (const [tbl, fields] of Object.entries(responseFields)) {
      if (tbl === tableName || joinedTables.has(tbl)) {
        for (const field of fields as string[]) {
          selectFields.push(`${tbl}.${field} AS "${tbl}.${field}"`);
        }
      } else {
        console.warn(
          `⚠️ Skip SELECT field dari tabel '${tbl}' karena tidak berhasil di-JOIN`,
        );
      }
    }

    // WHERE clause
    const whereClauses: string[] = [];
    const params: any[] = [];
    let idx = 1;
    for (const [rawKey, rawVal] of Object.entries(filters)) {
      const [tbl, fld] = rawKey.split('.');
      if (!tbl || !fld) continue;

      const val = Array.isArray(rawVal) ? rawVal[0] : rawVal.trim();
      if (val === '') continue;

      const dt = columnTypes[fld];
      if (dt && dt.match(/int|numeric|bigint/)) {
        whereClauses.push(`${tbl}.${fld} = $${idx}::bigint`);
        params.push(val);
      } else {
        whereClauses.push(`${tbl}.${fld} ILIKE $${idx}`);
        params.push(`%${val}%`);
      }
      idx++;
    }

    const whereSQL = whereClauses.length
      ? `WHERE ${whereClauses.join(' AND ')}`
      : '';

    const limit = paginationData.limit ?? 10;
    const offset = ((paginationData.page ?? 1) - 1) * limit;

    const joinSQL = Array.from(joinStatements).join('\n');

    const rawQuery = [
      `SELECT ${selectFields.join(', ') || `${tableName}.*`}`,
      `FROM ${tableName}`,
      joinSQL,
      whereSQL,
      `LIMIT ${limit}`,
      `OFFSET ${offset}`,
    ].join('\n');

    return { rawQuery, params, whereSQL, joinSQL };
  }

  private validateRequestFields(
    dataFields: Record<string, any>,
    allowedFields: string[],
  ) {
    const dataKeys = Object.keys(dataFields);

    const missingField = allowedFields.find(
      (field) => !dataKeys.includes(field),
    );
    if (missingField) {
      return {
        isValid: false,
        message: `Validasi gagal: Field '${missingField}' harus ada.`,
      };
    }

    const extraField = dataKeys.find((field) => !allowedFields.includes(field));
    if (extraField) {
      return {
        isValid: false,
        message: `Validasi gagal: Field '${extraField}' tidak terbaca atau tidak ada di request field.`,
      };
    }

    return {
      isValid: true,
      message: 'Validasi berhasil: Semua field sesuai.',
    };
  }
}
