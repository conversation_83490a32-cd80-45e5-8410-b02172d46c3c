import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiManagementPaginationDto } from '../../../core/dtos';
import { ApiServiceV2 } from '../service/api-clients.service';
import { ClientJwtAuthGuard } from 'src/core/guards/client-jwt-auth.guard';

@Controller('v2/api')
export class APIControllerV2 {
  private readonly logger = new Logger(APIControllerV2.name);

  constructor(private readonly apiService: ApiServiceV2) {}

  @Get('/get/*')
  @UseGuards(ClientJwtAuthGuard)
  @HttpCode(200)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async apiGet(
    @Req() req: Request,
    @Param() params: Record<string, string>,
    @Query() paginationData: ApiManagementPaginationDto,
  ) {
    this.logger.log(
      `Entering ${this.apiGet.name} with params ${JSON.stringify(params)} and query pagination data ${JSON.stringify(paginationData)}`,
    );

    const api_url = params[0];
    const response = await this.apiService.get(req, api_url, paginationData);

    this.logger.log(
      `Leaving ${this.apiGet.name} with params ${JSON.stringify(params)} and query pagination data ${JSON.stringify(paginationData)} and response ${JSON.stringify(response)} `,
    );

    return response;
  }

  @Post('/post/*')
  @UseGuards(ClientJwtAuthGuard)
  @HttpCode(201)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async apiPost(
    @Body() body: Record<string, any>,
    @Param() params: Record<string, string>,
  ) {
    this.logger.log(
      `Entering ${this.apiPost.name} with params ${JSON.stringify(params)} and body ${JSON.stringify(body)}`,
    );

    const api_url = params[0];
    const result = await this.apiService.create(body);

    this.logger.log(
      `Leaving ${this.apiPost.name} with params ${JSON.stringify(params)} and result ${JSON.stringify(result)}`,
    );

    return result;
  }
}
