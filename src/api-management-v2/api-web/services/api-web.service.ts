import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../../../api-utils/prisma/service/prisma.service';
import {
  CreateClientDto,
  CreateEndpointClientDto,
  ShowPasswordDto,
} from '../../dto/api-web.dto';
import { v4 } from 'uuid';
import { makeRandomString } from '../../../core/utils/common.utils';
import { decrypt, encrypt } from '../../../core/utils/encryption.utils';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { IColumnMapping } from '../../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../../core/utils/search.utils';
import { IValidateAuthByPersonelNRP } from '../../../core/interfaces/users.interface';
import { UsersService } from '../../../access-management/users/service/users.service';
import { AuthService } from '../../../access-management/auth/service/auth.service';
import {
  ForeignKeyRelation,
  FormatRelationTables,
  RelationTables,
  RelationTablesForMethodGet,
} from '../../../core/interfaces/api-management.interface';

@Injectable()
export class ApiWebService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly usersService: UsersService,
    private readonly authService: AuthService,
  ) {}

  async createClient(req: any, body: CreateClientDto) {
    const { name, username, status, email } = body;
    const { user } = req;

    const checkUsername =
      await this.prismaService.api_management_client_auth.findFirst({
        where: { deleted_at: null, username: username },
      });

    if (checkUsername) throw new ConflictException('Username sudah terpakai');

    // generate uuid
    const clientUid = v4();

    // generate password
    const password = makeRandomString({
      length: 10,
      isUpperCase: true,
      isLowerCase: true,
      isNumeric: true,
    });

    // encrypt password
    const encryptedPassword = encrypt(password);

    const result = await this.prismaService.$transaction(async (tx) => {
      const client = await tx.api_management_clients.create({
        data: {
          uid: clientUid,
          name: name,
          email: email,
          is_active: status,
          created_by: user.id,
        },
      });

      await tx.api_management_client_auth.create({
        data: {
          client_id: client.id,
          username: username,
          password: encryptedPassword,
        },
      });

      return client;
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: {
        ...result,
        password,
      },
    };
  }

  async findClients(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortDTO,
  ) {
    const { user } = req;
    const { page, limit } = paginationData;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      status: { field: 'is_active', type: 'boolean' },
      nama: { field: 'name', type: 'string' },
      created_at: { field: 'created_at', type: 'date' },
      total_endpoint: { field: 'endpoints._count', type: 'number' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, rawClients] = await this.prismaService.$transaction([
      this.prismaService.api_management_clients.count({ where }),
      this.prismaService.api_management_clients.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          uid: true,
          name: true,
          email: true,
          is_active: true,
          created_at: true,
          auth: {
            select: {
              username: true,
            },
          },
          _count: {
            select: {
              endpoints: true,
            },
          },
        },
      }),
    ]);

    const clients = rawClients.map((item) => ({
      ...item,
      username: item.auth?.username ?? null,
      auth: undefined, // remove nested auth object
    }));

    const totalPage = Math.ceil(totalData / limit);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: clients,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findEndpoints(req: any, searchAndSortData: SearchAndSortDTO) {
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      path: { field: 'path', type: 'string' },
    };

    const { where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
    );

    const endpoints =
      await this.prismaService.api_management_endpoints.findMany({
        where: {
          ...where,
          deleted_at: null,
        },
      });

    const results = await Promise.all(
      endpoints.map(async (endpoint) => {
        const relationTables = await this.getRelationTables(endpoint.table);
        const nestedStructure = await this.buildNestedStructure(
          relationTables,
          endpoint.table,
          endpoint.method,
        );

        return {
          id: endpoint.id,
          name: endpoint.name,
          path: endpoint.path,
          method: endpoint.method,
          created_at: endpoint.created_at,
          columns: nestedStructure,
        };
      }),
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: results,
    };
  }

  async clientDetail(req: any, clientUid: string) {
    const client = await this.validateClient(clientUid);

    const { auth, ...rest } = client;

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: {
        ...rest,
        auth: {
          username: auth.username,
          password: '**********',
        },
      },
    };
  }

  async showPasswordClient(req: any, clientUid: string, body: ShowPasswordDto) {
    const { user_password } = body;
    const { user } = req;

    const userLogin: IValidateAuthByPersonelNRP =
      await this.usersService.validateAuthPersonelByNrp(user.nrp);

    await this.authService.validatePasswordClient(
      user.nrp,
      user_password,
      userLogin,
    );

    const client = await this.validateClient(clientUid);

    const { auth } = client;
    const decryptedPassword = decrypt(auth.password);

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: {
        password: decryptedPassword,
      },
    };
  }

  async createEndpointClient(
    req: any,
    clientUid: string,
    body: CreateEndpointClientDto,
  ) {
    const { user } = req;
    const { master_endpoint_id, request_fields, response_fields } = body;

    const [endpoint, client] = await Promise.all([
      this.prismaService.api_management_endpoints.findFirst({
        where: {
          deleted_at: null,
          id: master_endpoint_id,
        },
      }),
      this.prismaService.api_management_clients.findFirst({
        where: {
          deleted_at: null,
          uid: clientUid,
        },
        select: {
          id: true,
          endpoints: {
            where: {
              deleted_at: null,
              endpoint_id: master_endpoint_id,
            },
          },
        },
      }),
    ]);

    if (!endpoint) throw new NotFoundException('Endpoint tidak ditemukan');
    if (!client) throw new NotFoundException('Client tidak ditemukan');
    if (client.endpoints.length > 0)
      throw new ConflictException('Endpoint sudah ada pada client');

    const relationTables = await this.getRelationTables(endpoint.table);

    const relationMap = this.formatRelationTables(relationTables);

    const areFieldsValid = this.areFieldsValid(request_fields, relationMap);

    if (!areFieldsValid)
      throw new NotFoundException(
        'Field tidak ditemukan di tabel yang Anda pilih',
      );

    // const requestFieldsObj = this.mapFieldsToObject(
    //   request_fields,
    //   relationMap,
    // );

    // const responseFieldObj = this.mapFieldsToObject(
    //   response_fields,
    //   relationMap,
    // );

    const result =
      await this.prismaService.api_management_client_endpoints.create({
        data: {
          client_id: client.id,
          endpoint_id: master_endpoint_id,
          request_fields: request_fields,
          response_fields: response_fields,
          created_by: user.id,
        },
      });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: result,
    };
  }

  async updateData(
    req: any,
    clientUid: string,
    endpointId: number | undefined,
    type: string,
    body: any,
  ) {
    const client = await this.validateClient(clientUid);

    switch (type) {
      case 'client':
        return this.updateClientData(client.id, body);
      case 'password-client':
        return this.updateClientPassword(client.id, body.password);
      case 'status-client':
        return this.updateClientStatus(client.id, body.status);
      case 'status-endpoint-client':
        return this.updateEndpointStatus(client.id, endpointId, body.status);
      default:
        throw new BadRequestException('Tipe status tidak valid');
    }
  }

  async deleteClients(clientId: string) {
    try {
      const client = await this.prismaService.api_management_clients.findFirst({
        where: { uid: clientId, deleted_at: null },
      });

      if (!client) throw new NotFoundException('Client tidak ditemukan');

      await this.prismaService.$transaction(async (tx) => {
        await tx.api_management_client_auth.updateMany({
          where: { client_id: client.id, deleted_at: null },
          data: { deleted_at: new Date() },
        });

        await tx.api_management_clients.update({
          where: { uid: clientId },
          data: { deleted_at: new Date() },
        });
      });

      return {
        statusCode: HttpStatus.OK,
        message: 'Client berhasil dihapus',
        data: null,
      };
    } catch (error) {
      throw error;
    }
  }

  private async updateClientData(clientId: bigint, body: any) {
    const { name, email, username } = body;

    await this.prismaService.$transaction(async (tx) => {
      const clientUpdate = await tx.api_management_clients.update({
        where: { id: clientId },
        data: { name, email },
      });

      await tx.api_management_client_auth.update({
        where: { client_id: clientUpdate.id },
        data: { username },
      });
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private async updateClientPassword(clientId: bigint, password: string) {
    const encryptedPassword = encrypt(password);

    await this.prismaService.api_management_client_auth.update({
      where: { client_id: clientId },
      data: { password: encryptedPassword },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private async updateClientStatus(clientId: bigint, status: boolean) {
    await this.prismaService.api_management_clients.update({
      where: { id: clientId },
      data: { is_active: status },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private async updateEndpointStatus(
    clientId: bigint,
    endpointId: number,
    status: boolean,
  ) {
    if (isNaN(endpointId))
      throw new BadRequestException('Endpoint ID tidak valid');

    const endpoint = await this.getEndpointById(BigInt(endpointId), clientId);
    if (!endpoint) throw new NotFoundException('Endpoint tidak ditemukan');

    await this.prismaService.api_management_client_endpoints.update({
      where: { id: endpointId },
      data: { is_active: status },
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Success',
      data: null,
    };
  }

  private async getEndpointById(endpointId: bigint, clientId: bigint) {
    const endpoint =
      await this.prismaService.api_management_client_endpoints.findFirst({
        where: {
          deleted_at: null,
          id: endpointId,
          client_id: clientId,
        },
      });

    if (!endpoint) {
      throw new NotFoundException('Endpoint tidak ditemukan');
    }

    return endpoint;
  }

  mapFieldsToObject(requestFields: Record<string, string[]>, relationMap: any) {
    const result = {};
    for (const table in requestFields) {
      if (!relationMap[table]) continue;
      result[table] = requestFields[table].filter((field) =>
        relationMap[table].includes(field),
      );
    }
    return result;
  }

  private async getRelationTables(
    table: string,
  ): Promise<RelationTablesForMethodGet[]> {
    return this.prismaService.$queryRaw<RelationTablesForMethodGet[]>`
    WITH RECURSIVE fk_info AS (
      SELECT
        tc.constraint_name,
        kcu.table_schema AS source_schema,
        kcu.table_name AS source_table,
        kcu.column_name AS source_column,
        ccu.table_schema AS target_schema,
        ccu.table_name AS target_table,
        ccu.column_name AS target_column
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.constraint_schema = kcu.constraint_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.constraint_schema = tc.constraint_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
    ),
    related_tables AS (
      -- Base table
      SELECT ${table} AS table_name, 0 as level, 'base' as relation_type
      
      UNION
      
      -- Tables that reference base table (child tables)
      SELECT source_table AS table_name, 1 as level, 'child' as relation_type
      FROM fk_info
      WHERE target_table = ${table}
      
      UNION
      
      -- Tables referenced by base table (parent tables)  
      SELECT target_table AS table_name, 1 as level, 'parent' as relation_type
      FROM fk_info
      WHERE source_table = ${table}
      
      UNION
      
      -- Tables referenced by child tables (lookup tables)
      SELECT fk2.target_table AS table_name, 2 as level, 'lookup' as relation_type
      FROM fk_info fk1
      JOIN fk_info fk2 ON fk1.source_table = fk2.source_table
      WHERE fk1.target_table = ${table}
        AND fk2.target_table != ${table}
        AND fk2.source_table != ${table}
    )
    SELECT DISTINCT
      c.table_name,
      c.column_name,
      c.data_type,
      c.ordinal_position,
      c.is_nullable,
      rt.level,
      rt.relation_type
    FROM information_schema.columns c
    JOIN related_tables rt ON c.table_name = rt.table_name
    ORDER BY c.table_name, c.ordinal_position;
  `;
  }

  private async buildNestedStructure(
    relations: RelationTablesForMethodGet[],
    baseTable: string,
    baseMethod: string,
  ): Promise<any> {
    const grouped: Record<string, RelationTablesForMethodGet[]> = {};
    for (const rel of relations) {
      if (!grouped[rel.table_name]) {
        grouped[rel.table_name] = [];
      }
      grouped[rel.table_name].push(rel);
    }

    // Method selain GET → tampilkan hanya kolom table utamanya
    if (baseMethod !== 'GET') {
      const baseColumns = grouped[baseTable] ?? [];
      const structure: Record<string, boolean> = {};

      baseColumns.forEach((column) => {
        structure[column.column_name] =
          baseMethod === 'POST' ? column.is_nullable === 'NO' : false;
      });

      return {
        [baseTable]: structure,
      };
    }

    // Jika GET, lanjutkan ke nested seperti sebelumnya
    const fkRelations = await this.getForeignKeyRelations();

    const buildTableStructure = (
      tableName: string,
      processed = new Set<string>(),
    ): any => {
      if (processed.has(tableName) || !grouped[tableName]) {
        return null;
      }

      processed.add(tableName);
      const tableColumns = grouped[tableName];
      const structure: any = {};

      tableColumns.forEach((column) => {
        structure[column.column_name] = false; // GET semua false (readonly)

        const fkRelation = fkRelations.find(
          (fk) =>
            fk.source_table === tableName &&
            fk.source_column === column.column_name,
        );

        if (fkRelation && grouped[fkRelation.target_table]) {
          const nestedPropertyName = column.column_name
            .replace(/_id$/, '')
            .replace(/Id$/, '');

          const nestedStructure = buildTableStructure(
            fkRelation.target_table,
            new Set(processed),
          );
          if (nestedStructure) {
            structure[nestedPropertyName] = nestedStructure;
          }
        }
      });

      // Tambahkan child table yang mengarah ke current table
      fkRelations.forEach((fk) => {
        if (
          fk.target_table === tableName &&
          grouped[fk.source_table] &&
          !processed.has(fk.source_table)
        ) {
          const childStructure = buildTableStructure(
            fk.source_table,
            new Set(processed),
          );
          if (childStructure) {
            structure[fk.source_table] = childStructure;
          }
        }
      });

      return structure;
    };

    const baseStructure = buildTableStructure(baseTable);
    return {
      [baseTable]: baseStructure,
    };
  }

  private async getForeignKeyRelations(): Promise<ForeignKeyRelation[]> {
    return this.prismaService.$queryRaw<ForeignKeyRelation[]>`
    SELECT
      kcu.table_name AS source_table,
      kcu.column_name AS source_column,
      ccu.table_name AS target_table,
      ccu.column_name AS target_column
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.constraint_schema = kcu.constraint_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.constraint_schema = tc.constraint_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
  `;
  }

  private formatRelationTables(tables: RelationTables[]): FormatRelationTables {
    return tables.reduce<FormatRelationTables>(
      (acc, { table_name, column_name }) => {
        acc[table_name] ||= [];
        acc[table_name].push(column_name);
        return acc;
      },
      {},
    );
  }

  private areFieldsValid(
    requestFields: Record<string, string[]>,
    relationMap: any,
  ) {
    for (const table in requestFields) {
      if (!relationMap[table]) return false;
      const fields = requestFields[table];
      for (const field of fields) {
        if (!relationMap[table].includes(field)) return false;
      }
    }
    return true;
  }

  private async validateClient(clientUid: string) {
    const client = await this.prismaService.api_management_clients.findFirst({
      where: {
        deleted_at: null,
        uid: clientUid,
      },
      select: {
        id: true,
        uid: true,
        name: true,
        email: true,
        is_active: true,
        created_at: true,
        auth: {
          where: { deleted_at: null },
          select: {
            username: true,
            password: true,
          },
        },
        endpoints: {
          where: { deleted_at: null, endpoint: { deleted_at: null } },
          select: {
            id: true,
            request_fields: true,
            response_fields: true,
            is_active: true,
            created_at: true,
            endpoint: {
              select: {
                id: true,
                name: true,
                path: true,
                method: true,
                table: true,
                created_at: true,
              },
            },
          },
        },
      },
    });

    if (!client || !client.auth)
      throw new NotFoundException('Client tidak ditemukan');

    return client;
  }
}
