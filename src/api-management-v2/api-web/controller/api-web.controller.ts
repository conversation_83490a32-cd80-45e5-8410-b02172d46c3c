import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiWebService } from '../services/api-web.service';
import {
  CreateClientDto,
  CreateEndpointClientDto,
} from '../../dto/api-web.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from '../../../core/dtos';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';

@Controller('api-web')
@UseGuards(JwtAuthGuard)
export class ApiWebController {
  constructor(private readonly apiWebService: ApiWebService) {}

  @Get('/endpoints')
  @UsePipes(new ValidationPipe({ transform: true }))
  async findEndpoints(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.apiWebService.findEndpoints(
      req,
      // paginationData,
      searchAndSortData,
    );
  }

  @Get('/clients')
  @UsePipes(new ValidationPipe({ transform: true }))
  async findClients(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchAndSortData: SearchAndSortDTO,
  ) {
    return await this.apiWebService.findClients(
      req,
      paginationData,
      searchAndSortData,
    );
  }

  @Get('/clients/:client_uid')
  async clientDetail(@Req() req: any, @Param('client_uid') clientUid: string) {
    return await this.apiWebService.clientDetail(req, clientUid);
  }

  @Patch('/clients/:client_uid/password')
  @UsePipes(new ValidationPipe())
  async showPasswordClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Body() body: any,
  ) {
    return await this.apiWebService.showPasswordClient(req, clientUid, body);
  }

  @Post('/clients')
  @UsePipes(new ValidationPipe())
  async createClient(@Req() req: any, @Body() body: CreateClientDto) {
    return await this.apiWebService.createClient(req, body);
  }

  @Post('/clients/:client_uid/endpoints')
  @UsePipes(new ValidationPipe())
  async createEndpointClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Body(new FieldValidatorPipe())
    body: CreateEndpointClientDto,
  ) {
    return await this.apiWebService.createEndpointClient(req, clientUid, body);
  }

  @Put('/clients/:client_uid')
  async updateClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Body() body: any,
  ) {
    return await this.apiWebService.updateData(
      req,
      clientUid,
      null,
      'client',
      body,
    );
  }

  @Put('/clients/:client_uid/password')
  async updatePasswordClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Body() body: any,
  ) {
    return await this.apiWebService.updateData(
      req,
      clientUid,
      null,
      'password-client',
      body,
    );
  }

  @Put('/clients/:client_uid/status')
  async updateStatusClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Body() body: any,
  ) {
    return await this.apiWebService.updateData(
      req,
      clientUid,
      null,
      'status-client',
      body,
    );
  }

  @Put('/clients/:client_uid/endpoints/:endpoint_id/status')
  async updateStatusEndpointClient(
    @Req() req: any,
    @Param('client_uid') clientUid: string,
    @Param('endpoint_id') endpointId: string,
    @Body() body: any,
  ) {
    return await this.apiWebService.updateData(
      req,
      clientUid,
      Number(endpointId),
      'status-endpoint-client',
      body,
    );
  }

  @Delete('/clients/:client_uid')
  async deleteClient(@Req() req: any, @Param('client_uid') clientUid: string) {
    return await this.apiWebService.deleteClients(clientUid);
  }
}
