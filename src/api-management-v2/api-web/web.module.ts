import { Module } from '@nestjs/common';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { ApiWebController } from './controller/api-web.controller';
import { ApiWebService } from './services/api-web.service';
import { AuthModule } from '../../access-management/auth/auth.module';
import { UsersModule } from '../../access-management/users/users.module';

@Module({
  imports: [PrismaModule, AuthModule, UsersModule],
  providers: [ApiWebService],
  exports: [ApiWebService],
  controllers: [ApiWebController],
})
export class ApiWebModule {}
