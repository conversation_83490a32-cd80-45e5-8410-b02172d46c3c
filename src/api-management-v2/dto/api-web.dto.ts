import {
  IsBoolean,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateClientDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsNotEmpty()
  @IsString()
  username: string;

  @IsOptional()
  @IsBoolean()
  status?: boolean = true;
}

export class ShowPasswordDto {
  @IsNotEmpty()
  @IsString()
  user_password: string;
}

export class CreateEndpointClientDto {
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  master_endpoint_id: number;

  @IsNotEmpty()
  @IsObject()
  request_fields: Record<string, string[]>;

  @IsNotEmpty()
  @IsObject()
  response_fields: Record<string, string[]>;
}
