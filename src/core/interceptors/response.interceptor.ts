import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { rethrow } from '@nestjs/core/helpers/rethrow';
import { parse, stringify } from 'flatted';
import { catchError, Observable, tap } from 'rxjs';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  LOG_ACTIVITY_TEXT,
  MASK_RESPONSE,
  MODULE_DECORATOR,
} from '../decorators';
import { capitalizeFirstChar, maskData } from '../utils/common.utils';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    private reflector: Reflector,
    private logActivity: LogsActivityService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();

    let module = this.reflector.getAllAndOverride(MODULE_DECORATOR, [
      context.getClass(),
      context.getHandler(),
    ]);

    const maskResponse = this.reflector.getAllAndOverride(MASK_RESPONSE, [
      context.getClass(),
      context.getHandler(),
    ]);

    let activity: string = this.reflector.getAllAndOverride(LOG_ACTIVITY_TEXT, [
      context.getClass(),
      context.getHandler(),
    ]);

    const { method, url } = req;

    if (!module) {
      const splitUrl = (url as string).split('/');
      module = splitUrl[1]?.replace(/\?.*/, '');
    }

    if (!activity) {
      if (method === 'POST') activity = `Pengajuan ${module}`;
      if (method === 'PUT') activity = `Perbaruan ${module}`;
      if (method === 'PATCH') activity = `Perbaruan ${module}`;
      if (method === 'DELETE') activity = `Penghapusan ${module}`;
    }
    return next.handle().pipe(
      tap(async (res) => {
        if (!res) {
          return;
        }

        const message = res.message;
        if (activity && req.user?.id) {
          // delete res.message;
          const copy = parse(stringify(res));

          if (maskResponse && copy?.data) {
            copy.data = maskData(copy.data, maskResponse);
          }

          await this.logActivity.addLogsActivity({
            user_id: req.user?.id,
            activity: capitalizeFirstChar('Hasil ' + activity) as any,
            payload: copy,
            message,
            headers: req.headers,
            url: `${req.protocol}://${req.get('Host')}${req.originalUrl}`,
            method,
            module,
          });
        }
      }),
      catchError(async (error) => {
        const payload = {
          message: error?.message || 'internal server error',
          statusCode: error?.getStatus?.() || 500,
        };

        if (req.user?.id) {
          await this.logActivity.addLogsActivity({
            user_id: req.user?.id,
            activity: capitalizeFirstChar('Hasil ' + activity) as any,
            payload: payload,
            message: payload.message,
            headers: req.headers,
            url: `${req.protocol}://${req.get('Host')}${req.originalUrl}`,
            method,
            module,
          });
        }

        rethrow(error);
      }),
    );
  }
}
