import { Workbook } from 'exceljs';
import Worksheet from 'exceljs/index';

export interface ApplyValidation {
  workbook: Workbook;
  worksheet: IWorksheetData;
  type: 'list' | 'whole' | 'decimal' | 'date' | 'textLength' | 'custom';
  operator?: string | undefined;
  referenceSheet?: IWorksheetData | undefined;
  formulae?: any | undefined;
  errorTitle?: string | undefined;
  error?: string | undefined;
  startRow?: number | undefined;
  endRow?: number | undefined;
}

export interface IWorksheetData {
  key: string;
  minKey?: string | undefined;
  maxKey?: string | undefined;
  columnKey?: string | undefined;
  gerakan?: string | undefined;
}

export interface IExcelColumns {
  header: string;
  key: string;
  width?: number | undefined;
  hidden?: boolean | undefined;
  value?: IExcelCellValue | undefined;
  // dataValidation?: DataValidation | undefined;
  dataValidation?: any | undefined;
}

export interface IExcelMasterData {
  data: any[];
  sheet: Worksheet;
  key: string;
  hidden: boolean;
}

export interface IExcelCellValue {
  formula?: string | undefined;
  result?: any | undefined;
  columnKey?: string[] | undefined;
  tableArray?: string[] | undefined;
  tableNumRow?: string | number | undefined;
  value?: string | undefined;
}
