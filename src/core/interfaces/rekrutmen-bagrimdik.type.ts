import {
  bagrimdik_draft_siap_latsar_asn,
  bagrimdik_peserta_rekrutmen_asn,
  status_rekrutmen_asn_enum,
  status_rekrutmen_pppk_enum,
} from '@prisma/client';

export type TStatusSeleksiBagrimdikASN = status_rekrutmen_asn_enum;

export type TStatusSeleksiBagrimdikPPPK = status_rekrutmen_pppk_enum;

export type T_bagrimdik_draft_siap_latsar_asnWithRelations =
  bagrimdik_draft_siap_latsar_asn & {
    bagrimdik_peserta_rekrutmen_asn?: bagrimdik_peserta_rekrutmen_asn | null;
  };
export type TDataImport = {
  headerMap: string[];
  data: string[][];
};

export type TListField = {
  [key: string]: {
    field: string;
    type?: string;
  };
};
