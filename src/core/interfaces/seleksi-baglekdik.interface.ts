import { seleksi_baglekdik_participant_status_enum } from '@prisma/client';

export interface IBaglekdikSelections {
  id: bigint;
  title: string;
  description?: string;
  type: string;
  logo?: string;
  status: string;
  stageName: string;
  startDate: Date;
  endDate: Date;
  isQualified?: boolean;
  isRegisterOpen?: boolean;
  isRegistered?: boolean;
}

interface IBaseFile {
  id: bigint;
  status?: string;
  reason?: string | null;
}

export interface IBaglekdikSelectionsParticipantFiles extends IBaseFile {
  fileId: bigint;
}

export interface IFileUploadsParticipant extends IBaseFile {
  urlFile: string;
}

export interface IParticipantResult {
  id: bigint;
  registrationNumber: string;
  examNumber?: string | null;
  fileUploads: IBaglekdikSelectionsParticipantFiles[];
  status: seleksi_baglekdik_participant_status_enum;
  participantNrp?: string | null;
  participantName?: string | null;
  participantRank?: string | null;
  participantUnit?: string | null;
  participantPosition?: string | null;
}

export interface IResponseDetailSelection
  extends Omit<IParticipantResult, 'fileUploads'> {
  files: IFileUploadsParticipant[];
}

export interface IParticipantSelectionDetail {
  fullName: string;
  photoFile: string;
  nrp: string;
  age: number;
  rankId: bigint;
  rank: string;
  physicalScore: number;
  spiritualScore: number;
  mentalScore: number;
  healthScore: number;
  educationLevel: string;
  mddp: number;
  requirementFiles: any[];
  isAllowRegister: boolean;
  selectionId: Pick<{ stage_id: bigint; id: bigint }, 'id' | 'stage_id'>[];
}
