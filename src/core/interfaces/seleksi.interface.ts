import { gender_enum } from '@prisma/client';
import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { PromosiJabatanParticipantStatusEnum } from '../enums/promosi-jabatan.enum';

export interface IPersonelData {
  id: bigint;
  nrp: string;
  nama_lengkap: string;
  tanggal_lahir: Date;
  jenis_kelamin: gender_enum;
  foto_file: string;
  satuan: Record<string, any>;
  pangkat: Record<string, any>;
  jabatan: Record<string, any>;
}

export interface ISeleksiResult {
  id: bigint;
  title: string;
  total_stages: number;
  description: string;
  banner: string;
  stages: Record<string, any>;
  requirements: Array<Record<string, any>>;
  file_requirement: Array<Record<string, any>>;
  personel: IPersonelData;
  message: string;
  eligible: boolean;
  status: string;
  uploaded_file: Record<string, any>[];
  history_stages: Record<string, any>[];
  registration_number: string;
}

export interface IRestructureParticipantList {
  data: Array<any>;
  fileMap: Map<string, Record<string, any>>;
  fileRequirementMap: Map<string, Record<string, any>>;
  fotoMap: Map<string, string>;
  jabatanMap: Map<string, any>;
  pangkatMap: Map<string, any>;
  stage: any;
}

export interface IFileUpload {
  requirement_file_id: number;
  file_id: number;
  created_at: string;
  status?: PromosiJabatanParticipantStatusEnum | null;
  reason?: string | null;
}

interface IExcelData {
  data: Array<any>;
  header: Array<any>;
}

export interface IProceedExcel {
  req: Request;
  file: Express.Multer.File;
  callback: ({ data, header }: IExcelData) => Promise<any>;
}

export interface IFilteredKesehatanExcel {
  nrp: string;
  status: string;
  instansi?: string;
}

export interface IValidateRequirement {
  personelData: Record<string, any>;
  requirement: Record<string, any>;
  result: Record<string, any>;
  prisma: PrismaService;
}
