import { ConstantLogType } from './log.type';
import { ConstantRestMethodType } from './api-management.type';

export interface ILog {
  user_id: bigint;
  activity: ConstantLogType;
  message: string;
  payload: any;
  headers: string;
  url: string;
  method: string;
  module?: string;
}

export interface ILogApi {
  api_id: bigint;
  user_agent: string;
  method: ConstantRestMethodType;
  activity: ConstantLogType;
  table: string;
  payload: any;
  headers: string;
  url: string;
}
