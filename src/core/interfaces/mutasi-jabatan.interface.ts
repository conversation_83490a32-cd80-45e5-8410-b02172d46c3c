import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

export interface IMutasiJabatan {
  nama: string;
  nip: number;
  pangkat: number;
  kesatuan_lama: string;
  kesatuan_baru: string;
  keterangan: string;
  tmt: any;
}

export interface IGetAllMutasiJabatan {
  pagination: PaginationDto;
  searchAndShort: SearchAndSortDTO;
  type?: string;
}

export interface IUpdateDocument {
  file_renskep: Express.Multer.File[];
  file_skep: Express.Multer.File[];
  file_telegram: Express.Multer.File[];
  file_winjak: Express.Multer.File[];
  file_petikan: Express.Multer.File[];
}
