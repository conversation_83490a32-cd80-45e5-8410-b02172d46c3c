export interface IRole {
  id: number;
  nama: string;
  created_at?: Date;
  level?: {
    id: number;
    nama: string;
  };
  bagian?: {
    id: number;
    nama: string;
  };
  satuan?: {
    id: bigint;
    nama: string;
  };
  atasan_role?: {
    id: number;
    nama: string;
  };
  role_tipe?: {
    id: number;
    nama: string;
  };
}

export interface IModule {
  id: number;
  nama: string;
  name_long: string;
  parent_id?: number | null;
}

export interface IModuleV2 {
  id: number;
  nama: string;
  name_long: string;
  chain_parent?: string;
  permissions: any[];
  modules: IModuleV2[];
}

export interface IPermission {
  id: number;
  nama: string;
  desc: string;
}

export interface IPermissionV2 {
  id: number;
  label: string;
  nama: string;
}

export interface IPortal {
  id: number;
  nama: string;
}

export interface IRoleAccess {
  role: IRole;
  modules: IModule;
  permission?: IPermission;
  portal?: IPortal;
}

export interface IRecursiveModule {
  id: number;
  nama: string;
  name_long: string;
  parent_id?: number;
  chain_parent?: string;
  permissions: any[];
  level?: number;
}

export interface IDefaultRecord {
  id: number;
  nama: string;
}

export interface IRecursiveRoles {
  total_data: number;
  role_id: number;
  role_name: string;
  created_at: Date;
  level?: IDefaultRecord;
  role_tipe: IDefaultRecord;
  is_fungsi: boolean;
  atasan_role?: { id: number; nama: string; atasan_id?: number };
  module_name: string;
  module_id: number;
  module_name_long: string;
  permission_label?: string;
  permission_id?: number;
  permission_name?: string;
  chain_parent: string;
  level_child: number;
}

export interface IRoleAccessModules {
  id: number;
  nama: string;
  name_long: string;
  chain_parent: string;
  permissions: IPermissionV2[];
  modules: Record<string, IRoleAccessModules>;
}

export interface IRoleAccessV2 {
  id: number;
  nama: string;
  level?: IDefaultRecord;
  bagian?: IDefaultRecord;
  satuan?: IDefaultRecord;
  atasan_role?: IDefaultRecord;
  role_tipe: IDefaultRecord;
  is_fungsi: boolean;
  created_at: Date;
  modules: IConvertRoleAccessModules[];
}

export interface IConvertRoleAccessModules {
  id: number;
  nama: string;
  name_long: string;
  chain_parent: string;
  permissions: IPermissionV2[];
  modules: IConvertRoleAccessModules[];
}
