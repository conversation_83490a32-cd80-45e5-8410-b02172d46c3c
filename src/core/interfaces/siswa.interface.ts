import { PrismaService } from '../../api-utils/prisma/service/prisma.service';
import { IColumnMapping } from './db.interface';

export interface IPayloadToken {
  unique_id: string;
  iat: number;
  exp: number;
}

export interface MasterDataOptions {
  model: keyof PrismaService;
  columns: IColumnMapping;
  select: Record<string, boolean>;
}

export interface IKompetensiDiktukResult {
  total_data: number;
  id: number;
  nama: string;
  diktuk_name: string;
  parent_id?: number | null;
  chain_parent: string;
  level: number;
}

export interface IConvertedKompetensiDiktukResult {
  id: number;
  nama: string;
  nama_diktuk: string;
  chain_parent: string;
  children: IConvertedKompetensiDiktukResult[];
}

export interface IDocumentRules {
  title: string;
  isRequired: boolean;
  mimeType: string[];
  maxSize?: number;
}
