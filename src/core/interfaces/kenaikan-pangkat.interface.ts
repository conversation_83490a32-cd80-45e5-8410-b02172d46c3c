import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

export interface IGetAllKenaikanPangkatInterface {
  pagination: PaginationDto;
  searchAndShort: SearchAndSortDTO;
}

export interface IPangkatPersonelInterface {
  id: bigint;
  pangkat_id: bigint;
  tmt: string;
  kep_nomor: string;
  kep_file: string;
  kep_tanggal: string;
  personel_id: bigint;
  created_at: any;
  updated_at: any;
  deleted_at: any | null;
  pangkat: IPangkatInterface;
}

export interface IPangkatInterface {
  id: bigint;
  nama: string;
  nama_singkat: string;
  tingkat_id: bigint;
  kategori_id: bigint;
  golongan_id: bigint;
  created_at: any;
  updated_at: any;
  deleted_at: any | null;
}
