export interface IToken {
  token: string;
  user_id: bigint;
}

export interface ISendNotificationMultiple {
  tokens: Array<IToken>;
  title: string;
  body: string;
  imageUrl?: string;
  data?: { [key: string]: string };
  type: string;
}

export interface ISendNotification {
  token: { token: string; user_id: bigint };
  title: string;
  body: string;
  imageUrl?: string;
  data?: { [key: string]: string };
  type: string;
}
