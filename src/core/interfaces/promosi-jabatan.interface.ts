import {
  PromosiJabatanParticipantStageStatusEnum,
  PromosiJabatanParticipantStatusEnum,
} from '../enums/promosi-jabatan.enum';
import { comparison_type_enum } from '@prisma/client';

export interface IParamQuerySelectSelection {
  withStages?: boolean;
  withRequirements?: boolean;
  withRequirementFiles?: boolean;
  withParticipants?: boolean;
  participantId?: bigint;
  withUser?: boolean;
  where?: {};
  take?: number;
  skip?: number;
}

export interface IFileBanner {
  original_name: string;
  url?: string;
  file_name?: string;
}

export interface IJobPromotionRequirement {
  name: string;
  input_type: string;
  url_data: string | null;
  table_foreign: string;
}

export interface IJobPromotionSelectionRequirement {
  id: bigint;
  is_required: boolean;
  value: string;
  comparison_type: comparison_type_enum;
  promosi_jabatan_requirements: IJobPromotionRequirement;
}

export interface IJobPromotionSelectionRequirementFile {
  id: bigint;
  is_required: boolean;
  title: string;
  extensions?: string[] | null;
  max_size?: number | null;
  min_size?: number | null;
}

export interface IPersonel {
  id: bigint;
  nrp: string;
  nama_lengkap: string;
}

export interface IUser {
  id: bigint;
  personel: IPersonel;
}

export interface IJobPromotionSelectionStage {
  id: bigint;
  stage: number;
  name: string;
  location: string;
  status: string;
  start_date: Date;
  end_date: Date;
  promosi_jabatan_files_banner_stage?: IFileBanner | null;
}

export interface IHistoryStage {
  stage_id?: number;
  stage: number;
  created_at: string;
  status?: PromosiJabatanParticipantStageStatusEnum | string;
}

export interface IFileUpload {
  requirement_file_id: number;
  file_id: number;
  created_at: string;
  status?: PromosiJabatanParticipantStatusEnum | null;
  reason?: string | null;
}

export interface IJobPromotionSelectionParticipant {
  registration_number: string;
  exam_number?: string | null;
  history_stages: IHistoryStage[];
  file_uploads?: any;
}

export interface IJobPromotionSelection {
  id: bigint;
  title: string;
  description: string;
  total_stages: number;
  promosi_jabatan_files_banner_selection?: IFileBanner | null;
  promosi_jabatan_selection_requirement_files?:
    | IJobPromotionSelectionRequirementFile[]
    | null;
  promosi_jabatan_selection_requirements?:
    | IJobPromotionSelectionRequirement[]
    | null;
  created_by_user?: IUser | null;
  promosi_jabatan_selection_stages?: IJobPromotionSelectionStage[];
  promosi_jabatan_selection_participants?: IJobPromotionSelectionParticipant[];
}

export interface IValidationRequirement {
  is_valid: boolean;
  value: string;
}
