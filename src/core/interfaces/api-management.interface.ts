export interface RelationTables {
  table_name: string;
  column_name: string;
  data_type: string;
  ordinal_position: number;
}

export interface RelationTablesForMethodGet extends RelationTables {
  is_nullable: 'YES' | 'NO';
}

export interface FormatRelationTables {
  [key: string]: string[];
}

export interface ForeignKeyRelation {
  source_table: string;
  source_column: string;
  target_table: string;
  target_column: string;
}
