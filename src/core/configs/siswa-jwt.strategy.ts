import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthSiswaService } from '../../e-patma-v2/siswa/services/auth.service';
import { IPayloadToken } from '../interfaces/siswa.interface';

@Injectable()
export class SiswaJwtStrategy extends PassportStrategy(Strategy, 'jwt-siswa') {
  constructor(
    private readonly configService: ConfigService,
    private readonly authSiswaService: AuthSiswaService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('JWT_SECRET_KEY_SISWA'),
    });
  }

  async validate(payload: IPayloadToken) {
    return await this.authSiswaService.validateUser(payload.unique_id);
  }
}
