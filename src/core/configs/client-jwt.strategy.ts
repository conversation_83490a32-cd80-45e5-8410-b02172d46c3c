import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { IPayloadToken } from '../interfaces/siswa.interface';
import { AuthClientService } from 'src/api-management-v2/clients/service/auth.service';

@Injectable()
export class ClientJwtStrategy extends PassportStrategy(
  Strategy,
  'jwt-client',
) {
  constructor(
    private readonly configService: ConfigService,
    private readonly authClientService: AuthClientService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('JWT_SECRET_KEY_CLIENT'),
    });
  }

  async validate(payload: IPayloadToken) {
    return await this.authClientService.validateUser(payload.unique_id);
  }
}
