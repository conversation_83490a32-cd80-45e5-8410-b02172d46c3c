//src/auth/siswa-jwt-auth.guard.ts
import {
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthJwtService } from '../../access-management/auth/service/auth-jwt.service';
import { lastValueFrom, Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { OtpException } from '../exceptions/internal-otp.exceptions';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt-web') {
  private static readonly BEARER_AUTHORIZATION = 'authorization';

  constructor(
    @Inject(AuthJwtService)
    private readonly authJwtService: AuthJwtService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers[JwtAuthGuard.BEARER_AUTHORIZATION];
    if (!authHeader) {
      throw new UnauthorizedException(
        'Tidak ada JWT yang disediakan dari client',
      );
    }
    await this.switchGuard(this.configService.get('APP_ENV'), authHeader);
    const parentCanActivate = super.canActivate(context);

    if (parentCanActivate instanceof Observable) {
      return lastValueFrom(parentCanActivate);
    }

    return parentCanActivate as boolean;
  }

  async switchGuard(env: string, authHeader: string) {
    switch (env) {
      case 'local':
      case 'development':
      case 'staging':
        break;
      case 'production':
        const token = authHeader.split(' ')[1];
        const usersJwtToken = await this.authJwtService.findToken(token);
        if (!usersJwtToken) {
          throw new UnauthorizedException('Silahkan login kembali');
        }
        break;
      default:
        throw new OtpException(
          `Tidak ditemukan JWT guard environment ${this.configService.get('APP_ENV')}`,
        );
    }
  }

  handleRequest(err: any, user: any, info: any) {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
