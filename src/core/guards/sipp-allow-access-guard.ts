import {
  CanActivate,
  ExecutionContext, ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Sipp3Service } from '../../sipp/sipp-3/service/sipp-3.service';

@Injectable()
export class SippAllowAccessGuard implements CanActivate {
  private RequestContext: any;

  constructor(
    private reflector: Reflector,
    private readonly sipp3Service: Sipp3Service,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    let request = context.switchToHttp().getRequest();

    let uid = request.params.uid;
    let id = request.params.id;

    let idMode = !uid

    //SET DEFAULT
    request["require_fungsi"] = false

    try {

      if(request['user']['uid'] === uid){
        request["required_min_level"] = 4
        return true;
      }

      let loggedUserSatuan
      let targetedUserSatuan

      if(idMode){
        loggedUserSatuan = await this.sipp3Service.getNeededUserAccessById(id);
        targetedUserSatuan = await this.sipp3Service.getNeededUserAccessById(request['user']['id']);
      }else{
        loggedUserSatuan = await this.sipp3Service.getNeededUserAccess(uid);
        targetedUserSatuan = await this.sipp3Service.getNeededUserAccess(request['user']['uid']);
      }



      if(loggedUserSatuan.third_top_parent_id === targetedUserSatuan.third_top_parent_id){
        request["required_min_level"] = 3
        return true;
      }

      if(loggedUserSatuan.second_top_parent_id === targetedUserSatuan.second_top_parent_id){
        request["required_min_level"] = 2
        return true;
      }

      //Check can be search by fungsi or not
      let loggedUserFungsi

      if(idMode){
        loggedUserFungsi = await this.sipp3Service.getUserFungsiByUserId(request['user']['id']);
      }else{
        loggedUserFungsi = await this.sipp3Service.getUserFungsiByUserUid(request['user']['uid']);
      }

      if(targetedUserSatuan.second_top_parent_id === loggedUserFungsi?.satuan_teratas_id){
        request["required_min_level"] = 2
        request["require_fungsi"] = true
        return true;
      }

      request["required_min_level"] = 1
      return true;

    } catch (e) {
      console.error(e)
      throw new ForbiddenException(
        `Error saat penentuan izin akses`,
      );
    }
  }
}
