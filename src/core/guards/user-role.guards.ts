import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  ForbiddenException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { groupBy } from 'lodash';


@Injectable()
export class UserRoleGuard implements CanActivate {
  private RequestContext: any;

  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const { module, permissions } = this.reflector.get<{
      module: string;
      permissions: string[];
    }>('module_permission', context.getHandler());
    // const permissions = this.reflector.get<string[]>('permissions', context.getHandler())

    let request = context.switchToHttp().getRequest();

    let roles = request['user']['roles'];
    let modules = request['user']['modules'];
    let rawRoles = request['user']['raw_roles'];

    let requiredLevel = request["required_min_level"]
    let requireFungsi = request["require_fungsi"]

    let sippModule = modules.find(obj=>obj.id===5)
    let cutiModule = modules.find(obj=>obj.id===28)

    // for (let r of roles) {
    //   if (r.nama === 'User Level 1') {
    //     roles = [
    //       ...roles,
    //       {
    //         role_module: [
    //           {
    //             module: {
    //               nama: 'LIST_PERSONEL',
    //             },
    //           },
    //         ],
    //         level: {
    //           nama: 'Level 1',
    //         },
    //         role_permission: [
    //           {
    //             permission: {
    //               nama: 'PERMISSION_READ',
    //             },
    //           },
    //         ],
    //       },
    //     ];
    //   }
    //
    //   if (r.nama === 'User Level 2') {
    //     roles = [
    //       ...roles,
    //       {
    //         role_module: [
    //           {
    //             module: {
    //               nama: 'LIST_PERSONEL',
    //             },
    //           },
    //         ],
    //         level: {
    //           nama: 'Level 2',
    //         },
    //         role_permission: [
    //           {
    //             permission: {
    //               nama: 'PERMISSION_READ',
    //             },
    //           },
    //         ],
    //       },
    //     ];
    //   }
    //
    //   if (r.nama === 'User Level 3') {
    //     roles = [
    //       ...roles,
    //       {
    //         role_module: [
    //           {
    //             module: {
    //               nama: 'LIST_PERSONEL',
    //             },
    //           },
    //         ],
    //         level: {
    //           nama: 'Level 3',
    //         },
    //         role_permission: [
    //           {
    //             permission: {
    //               nama: 'PERMISSION_READ',
    //             },
    //           },
    //         ],
    //       },
    //     ];
    //   }
    // }

    modules.push({
      name: 'LIST_PERSONEL',
      permission: [ {nama : "PERMISSION_READ"} ],
      portal: []
    })

    // let isPermissionFound = this.checkPermissionRecursive({ permissions : [], modules : modules }, module, permissions)

    let isPermissionFound = false;


    let permissionFound = permissions.map(obj=>{
      return {
        name : obj,
        found : false,
      }
    })

    let foundLevel = 4
    let isFungsi = false;


    for(let roleModule of rawRoles){
      if(roleModule.module_name === module){

        //Checking levelnya oke atau engga
        let levelNumber = !roleModule.level ? 1 : parseInt(roleModule.level.nama.split(" ")[1])
        if(levelNumber > requiredLevel){
          continue
        }

        //Kalau oke permissionnya di assign
        for(let p of permissions){
          if(roleModule.permission_name === p){

            for(let pf of permissionFound){


              if(requireFungsi && !roleModule.is_fungsi){

                continue
              }

              if(pf.name === p){
                //TODO: kalau level nya lebih tinggi pake lebih tinggi
                if(levelNumber < foundLevel){
                  foundLevel = levelNumber
                }
                pf.found = true
                isFungsi = roleModule.is_fungsi
              }
            }
          }
        }
      }
    }

    //Check if all permission is found
    for(let pf of permissionFound){
      if(!pf.found){
        throw new ForbiddenException(
          `Izin mengakses ${module.replace("_", " ")} dengan izin '${permissions.join(',')}' tidak ditemukan untuk personel ini`,
        );
      }
    }

    request['selected_user_role'] = {
      type: roles[0].role_tipe?.id,
      id: roles[0].id,
      level: `Level ${foundLevel}`,
      isFungsi : isFungsi
    };
    return true

    // OLD LOGIC
    // for (let m of modules) {
    //   if (
    //     m?.name === module ||
    //     (m?.name === 'LIST_PERSONEL' && module === 'PERSONEL_VIEW')
    //   ) {
    //     let formattedPermissions = m.permission.map((obj) => obj.nama);
    //     let allTrue = true;
    //
    //     for (let permissionNeeded of permissions) {
    //       if (!formattedPermissions.includes(permissionNeeded)) {
    //         allTrue = false;
    //       }
    //     }
    //     if (allTrue) {
    //       request['selected_user_role'] = {
    //         type: roles[0].role_tipe?.id,
    //         id: roles[0].id,
    //         level: roles[0].level?.nama,
    //       };
    //       return true;
    //     }
    //   }
    // }

    // if (!scopes || scopes.length === 0) return true


    // if (!scopes.map(s => this.RequestContext.hasOneOfTheScopes(s)).find(valid => !valid)) {
    //     throw new HttpException(`Missing at least one scope of '${scopes.join(',')}'`, HttpStatus.FORBIDDEN)
    // }
    // return true
  }

  checkPermissionRecursive(module:any, moduleNeeded:string, permissionsNeeded:string[]){

    //Check dulu semua yang ada di module
    if(!module.permissions){
      return false
    }else{

    }
    let formattedPermissions = module.permissions.map((obj) => obj.nama);
    let allTrue = true;

    if(module.nama === moduleNeeded ){
      for (let permissionNeeded of permissionsNeeded) {
        if (!formattedPermissions.includes(permissionNeeded)) {
          allTrue = false;
        }
      }
      if (allTrue) {
        return true;
      }
    }


    //Kalau enggak ada baru check childrennya
    if(module.modules){
      for(let m of module.modules){
        let permissionCheck = this.checkPermissionRecursive(m,moduleNeeded,permissionsNeeded)
        if(permissionCheck){
          return true
        }
      }
    }

    //Kalau gk ketemu apa2
    return false
  }

}
