import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionType } from 'src/core/constants/permission.constant';
import { ModuleType } from '../constants/module.constant';
import { CHECK_MODULE_PERMISSION, MODULE_DECORATOR } from '../decorators';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    return true;

    const handler = context.getHandler();
    const moduleAccess = this.reflector.get<ModuleType>(
      MODULE_DECORATOR,
      handler,
    );
    const checkPermission = this.reflector.get<ModuleType>(
      CHECK_MODULE_PERMISSION,
      handler,
    );
    const permission = this.reflector.get<PermissionType>(
      'permission',
      handler,
    );

    if (!moduleAccess || !checkPermission) return true;

    const { user } = context.switchToHttp().getRequest();
    if (!user) throw new ForbiddenException('User tidak memiliki role');

    const userModuleAccess = new Map(
      user.modules.map((mod) => [mod.name, mod]),
    );
    const moduleData: any = userModuleAccess.get(moduleAccess);

    if (!moduleData)
      throw new ForbiddenException('User tidak memiliki akses ke modul ini');

    if (!permission) return true;

    const userPermissions = new Set(moduleData.permission.map((p) => p.nama));
    if (!userPermissions.has(permission))
      throw new ForbiddenException('User tidak dapat melakukan aksi ini');

    return true;
  }
}