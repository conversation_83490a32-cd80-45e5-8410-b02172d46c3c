import {
  IsIn,
  <PERSON><PERSON>otEmpty,
  IsO<PERSON>al,
  IsString,
  ValidateIf,
} from 'class-validator';
import { TSortOrderType } from '../interfaces/db.type';

export class SearchAndSortDTO {
  @IsString()
  @IsOptional()
  public search: string;

  @IsOptional()
  sort_column?: string;

  @ValidateIf((_, value) => !!value) // Validate only if sort_desc is provided
  @IsNotEmpty({
    message: 'sort_desc is required when sort_column is provided',
    groups: ['sort'],
  })
  @IsIn(['asc', 'desc'], { message: 'sort_desc must be "asc" or "desc"' })
  sort_desc?: TSortOrderType;

  @IsOptional()
  search_column?: string | string[];

  @IsOptional()
  search_text?: string | string[];
}
