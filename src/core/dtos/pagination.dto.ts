import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

export class PaginationDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform((value) => Number(value.value))
  limit?: number = 10;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform((value) => Number(value.value))
  page?: number = 1;
}

export class ApiManagementPaginationDto {
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  limit?: number = 10;

  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  page?: number = 1;

  @Type(() => String)
  @IsOptional()
  @IsString()
  readonly search?: string;
}
