import { SetMetadata } from '@nestjs/common';

export const LOG_ACTIVITY_TEXT = 'LOG_ACTIVITY_TEXT';
export const USE_LOG_ACTIVITY = 'USE_LOG_ACTIVITY';
export const MASK_RESPONSE = 'MASK_RESPONSE';

export const LogActivityText = (activity: string) =>
  SetMetadata(LOG_ACTIVITY_TEXT, activity);

export const UseLogActivity = (active: boolean = true) =>
  SetMetadata(USE_LOG_ACTIVITY, active);

export const MaskResponseLog = (...field: string[]) =>
  SetMetadata(MASK_RESPONSE, field);
