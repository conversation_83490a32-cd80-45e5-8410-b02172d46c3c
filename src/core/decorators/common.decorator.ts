import { Transform } from 'class-transformer';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';

export function TransformBoolean() {
  return Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === true) return true;
    if (value === false) return false;
    return undefined;
  });
}

export function MatchArrayLength(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'matchArrayLength',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [property],
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          // Ensure round_selection_dates exists and is an array, and round is a number
          if (Array.isArray(value) && typeof relatedValue === 'number') {
            return value.length === relatedValue;
          }
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          return `${args.property} must have the same length as ${relatedPropertyName}.`;
        },
      },
    });
  };
}

export function Match(property: string, validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: {
        ...validationOptions,
        message:
          validationOptions?.message ||
          `${propertyName} dan ${property} tidak cocok. Silakan pastikan kedua kata sandi sesuai.`,
      },
      constraints: [property],
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return value.toLowerCase() === relatedValue.toLowerCase();
        },
      },
    });
  };
}
