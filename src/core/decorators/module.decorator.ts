import { SetMetadata } from '@nestjs/common';
import { ModuleType } from '../constants/module.constant';

export const MODULE_DECORATOR = 'module';
export const CHECK_MODULE_PERMISSION = 'check-module-permission';

export const Module = (module: ModuleType) =>
  SetMetadata(MODULE_DECORATOR, module);

export const CheckModulePermission = (check: boolean = true) =>
  SetMetadata(CHECK_MODULE_PERMISSION, check);
