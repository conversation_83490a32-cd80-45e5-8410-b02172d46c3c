import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';

@Catch()
export class GlobalFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const { status, message, data, error } =
      this.extractErrorDetails(exception);

    // Log error details
    this.logError(exception, request.url);
    
    // Send error response
    response.status(status).json({
      statusCode: status,
      message,
      data,
      error,
    });
  }

  /**
   * Extracts error details based on the type of exception.
   * @param exception The exception to process.
   */
  private extractErrorDetails(exception: unknown): {
    status: number;
    message: string;
    data: any;
    error: string | null;
  } {
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const response = exception.getResponse();
      const isInternalError = status === HttpStatus.INTERNAL_SERVER_ERROR;

      return {
        status,
        message: this.getMessageFromResponse(response, isInternalError),
        data: null,
        error: isInternalError ? this.getErrorFromResponse(response) : null,
      };
    }

    return {
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'There is an something wrong with SSDM Application',
      data: null,
      error:
        (exception as any)?.message ||
        (typeof exception === 'object'
          ? JSON.stringify(exception)
          : (exception as any).toString()),
    };
  }

  /**
   * Extracts the error message from an HttpException response.
   * @param response The exception response.
   * @param isInternalError Flag to determine if it is an internal server error.
   */
  private getMessageFromResponse(
    response: any,
    isInternalError: boolean,
  ): string {
    if (isInternalError)
      return 'There is an something wrong with SSDM Application';
    return response?.message || 'Unknown error';
  }

  /**
   * Extracts the error details from an HttpException response.
   * @param response The exception response.
   */
  private getErrorFromResponse(response: any): string | null {
    return response?.message || null;
  }

  /**
   * Logs the error details for debugging purposes.
   * @param exception The exception to log.
   * @param url The URL where the error occurred.
   */
  private logError(exception: unknown, url: string): void {
    const errorDetails =
      exception instanceof Error
        ? exception.stack
        : (exception as any)?.toString();
    this.logger.error(`Error occurred at ${url}: ${errorDetails}`);
  }
}
