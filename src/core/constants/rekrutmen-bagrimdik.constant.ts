import { TListField } from '../interfaces/rekrutmen-bagrimdik.type';

export const listReserveWordNameHeadersASN: string[] = [
  'No. Peserta',
  'NIK',
  '<PERSON>a',
  'Kode',
  'Tempat Lahir',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Lokasi Formasi',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Pendidikan',
  '<PERSON><PERSON> Sele<PERSON> Kompetensi',
  'Teknis Murni',
  'EPTP',
  'B. Ing',
  'PPM',
  'Dpsi',
  'Afirmasi Teknis',
  'Total Teknis',
  'Manajerial',
  'Sosiokultural',
  'Wawancara',
  'Total',
  'Keterangan',
  'Afirmasi',
  'Lok<PERSON> Ujian',
  'Status',
  'No Peserta',
  'Nama Peserta',
  'Urutan',
  'Nama Komponen SKT',
  '<PERSON>lai',
  'Bobot (%)',
  '<PERSON>kor',
  '<PERSON>lai SKD',
  'TWK',
  'TIU',
  'TKP',
  'Total (Skala 100)',
  'Skor SKD',
  'Skor SKB (60 %)',
  'Skor SKB',
  '<PERSON><PERSON>',
];

export const listFieldPesertaASN: TListField = {
  no_peserta: { field: 'no_peserta' },
  nik: { field: 'nik' },
  nama: { field: 'nama' },
  tempat_lahir: { field: 'tempat_lahir' },
  tanggal_lahir: { field: 'tanggal_lahir', type: 'date' },
  'instansi.kode': { field: 'instansi_kode' },
  'instansi.nama': { field: 'instansi_nama' },
  'lokasi_formasi.kode': { field: 'lokasi_formasi_kode' },
  'lokasi_formasi.nama': { field: 'lokasi_formasi_nama' },
  'jenis_formasi.kode': { field: 'jenis_formasi_kode' },
  'jenis_formasi.nama': { field: 'jenis_formasi_nama' },
  'jabatan.kode': { field: 'jabatan_kode' },
  'jabatan.nama': { field: 'jabatan_nama' },
  'nilai_seleksi_kompetensi.teknis_murni': {
    field: 'nilai_sk_teknis_murni',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.etpt': { field: 'nilai_sk_eptp', type: 'float' },
  'nilai_seleksi_kompetensi.b_ing': { field: 'nilai_sk_bing', type: 'float' },
  'nilai_seleksi_kompetensi.ppm': { field: 'nilai_sk_ppm', type: 'float' },
  'nilai_seleksi_kompetensi.dpsi': { field: 'nilai_sk_dpsi', type: 'float' },
  'nilai_seleksi_kompetensi.afirmasi_teknis': {
    field: 'nilai_sk_afirmasi_teknis',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.total_teknis': {
    field: 'nilai_sk_total_teknis',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.manajerial': {
    field: 'nilai_sk_manajerial',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.sosiokultural': {
    field: 'nilai_sk_sosiokultural',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.wawancara': {
    field: 'nilai_sk_wawancara',
    type: 'float',
  },
  'nilai_seleksi_kompetensi.total': { field: 'nilai_sk_total', type: 'float' },
  'nilai_skd.twk': { field: 'nilai_skd_twk', type: 'float' },
  'nilai_skd.tiu': { field: 'nilai_skd_tiu', type: 'float' },
  'nilai_skd.tkp': { field: 'nilai_skd_tkp', type: 'float' },
  'nilai_skd.total': { field: 'nilai_skd_total', type: 'float' },
  'nilai_skd.total_skala_100': {
    field: 'nilai_skd_total_skala_100',
    type: 'float',
  },
  skor_skd: { field: 'skor_skd', type: 'float' },
  skor_skd_60_persen: { field: 'skor_skb_60_persen', type: 'float' },
  skor_skb: { field: 'skor_skb', type: 'float' },
  nilai_akhir: { field: 'nilai_akhir', type: 'float' },
  keterangan: { field: 'keterangan' },
  afirmasi: { field: 'afirmasi' },
  status: { field: 'status_peserta' },
  lokasi_ujian: { field: 'lokasi_ujian' },
};

export const listFieldNilaiASN: TListField = {
  no_peserta: { field: 'no_peserta' },
  nama_peserta: { field: 'nama_peserta' },
  'jabatan.kode': { field: 'jabatan_kode' },
  'jabatan.nama': { field: 'jabatan_nama' },
  urutan: { field: 'urutan', type: 'int' },
  nama_komponen_skt: { field: 'nama_komponen' },
  nama_komponen_skb: { field: 'nama_komponen' },
  nilai: { field: 'nilai', type: 'float' },
  nilai_skala_100: { field: 'nilai_skala_100', type: 'float' },
  bobot_persen: { field: 'bobot', type: 'float' },
  skor: { field: 'skor', type: 'float' },
  status: { field: 'status_peserta' },
};

export const listReserveWordNameHeadersPPPK: string[] = [
  'No Peserta',
  'Nama',
  'NIP',
  'JENIS FORMASI NAMA',
  'PERIODE',
  'TGL_USULAN',
  'TGL_PERTEK',
  'NO_PERTEK',
  'STATUS_USULAN',
  'TAHAPAN',
  'TEMPAT LAHIR',
  'TANGGAL_LAHIR',
  'PENDIDIKAN_PERTAMA_NAMA',
  'KODE DIK',
  'TGL_TAHUN_LULUS',
  'NOMOR_IJAZAH',
  'JABATAN_FUNGSIONAL_UMUM_NAMA',
  'JABATAN_FUNGSIONAL_NAMA',
  'SUB_JABATAN_FUNGSIONAL_NAMA',
  'GAJI_POKOK',
  'KPKN_NAMA',
  'GOLONGAN_NAMA',
  'AGAMA_ID',
  'AGAMA_NAMA',
  'JENIS_KELAMIN',
  'jenis_kawin_id',
  'jenis_kawin_nama',
  'unor_nama',
  'unor_induk_nama',
  'satuan_kerja_nama',
  'rencana_perjanjian_kontrak_atau_tmt',
  'ket_sehat_dokter',
  'ket_sehat_tanggal',
  'ket_sehat_nomor',
  'ket_bebas_narkoba_nomor',
  'ket_bebas_narkoba_tanggal',
  'ket_kelakuanbaik_nomor',
  'ket_kelakuanbaik_pejabat',
  'ket_kelakuanbaik_tanggal',
  'tgl_kontrak_mulai',
  'tgl_kontrak_akhir',
  'KODE POLDA',
  'POLDA',
  'KODE CEK',
  'KET',
];

export const listFieldPesertaPPPK: TListField = {
  no_peserta: { field: 'no_peserta' },
  nama: { field: 'nama' },
  nip: { field: 'nip' },
  jenis_formasi_nama: { field: 'jenis_formasi_nama' },
  periode: { field: 'periode', type: 'int' },
  tgl_usulan: { field: 'tanggal_usulan', type: 'date' },
  tgl_pertek: { field: 'tanggal_pertek', type: 'date' },
  no_pertek: { field: 'no_pertek' },
  status_usulan: { field: 'status_usulan' },
  tahapan: { field: 'tahapan' },
  tempat_lahir: { field: 'tempat_lahir' },
  tanggal_lahir: { field: 'tanggal_lahir', type: 'date' },
  pendidikan_pertama_nama: { field: 'pendidikan_pertama_nama' },
  kode_dik: { field: 'kode_dik' },
  tgl_tahun_lulus: { field: 'tanggal_tahun_lulus', type: 'date' },
  nomor_ijazah: { field: 'nomor_ijazah' },
  jabatan_fungsional_umum_nama: { field: 'jabatan_fungsional_umum_nama' },
  jabatan_fungsional_nama: { field: 'jabatan_fungsional_nama' },
  sub_jabatan_fungsional_nama: { field: 'sub_jabatan_fungsional_nama' },
  gaji_pokok: { field: 'gaji_pokok', type: 'int' },
  kpkn_nama: { field: 'kpkn_nama' },
  golongan_nama: { field: 'golongan_nama' },
  agama_id: { field: 'agama_id' },
  agama_nama: { field: 'agama_nama' },
  jenis_kelamin: { field: 'jenis_kelamin' },
  jenis_kawin_id: { field: 'jenis_kawin_id' },
  jenis_kawin_nama: { field: 'jenis_kawin_nama' },
  unor_nama: { field: 'unor_nama' },
  unor_induk_nama: { field: 'unor_induk_nama' },
  satuan_kerja_nama: { field: 'satuan_kerja_nama' },
  rencana_perjanjian_kontrak_atau_tmt: {
    field: 'rencana_perjanjian_kontrak_atau_tmt',
    type: 'date',
  },
  ket_sehat_dokter: { field: 'ket_sehat_dokter' },
  ket_sehat_tanggal: { field: 'ket_sehat_tanggal', type: 'date' },
  ket_sehat_nomor: { field: 'ket_sehat_nomor' },
  ket_bebas_narkoba_nomor: { field: 'ket_bebas_narkoba_nomor' },
  ket_bebas_narkoba_tanggal: {
    field: 'ket_bebas_narkoba_tanggal',
    type: 'date',
  },
  ket_kelakuanbaik_nomor: { field: 'ket_kelakuanbaik_nomor' },
  ket_kelakuanbaik_pejabat: { field: 'ket_kelakuanbaik_pejabat' },
  ket_kelakuanbaik_tanggal: { field: 'ket_kelakuanbaik_tanggal', type: 'date' },
  tgl_kontrak_mulai: { field: 'tgl_kontrak_mulai', type: 'date' },
  tgl_kontrak_akhir: { field: 'tgl_kontrak_akhir', type: 'date' },
  kode_polda: { field: 'kode_polda' },
  polda: { field: 'polda' },
  kode_cek: { field: 'kode_cek' },
  ket: { field: 'ket' },
};
