import { OrderByEnum } from '../enums/seleksi-bagrimdik-pns.enum';

export const staticFields = {
  [OrderByEnum.no_pendaftaran]: 'no_pendaftaran',
  [OrderByEnum.nama_peserta]: 'personel.nama_lengkap',
  [OrderByEnum.nrp]: 'personel.nrp',
  [OrderByEnum.pangkat]: 'personel.pangkat_personel.pangkat.nama_singkat',
  [OrderByEnum.jabatan]: 'personel.jabatan_personel.jabatans.nama',
  [OrderByEnum.satuan]: 'personel.jabatan_personel.jabatans.satuan.nama',
  [OrderByEnum.status_dokumen]: 'status_dokumen',
  [OrderByEnum.status]: 'status',
};

export const listReserveWordNameHeadersSeleksiPNS: string[] = [
  'No Pendaftaran',
  'No Ujian',
  'NRP',
  'Nama <PERSON>',
  'Pangkat',
  'Jabatan',
  'Kesatuan',
  'Status',
];
