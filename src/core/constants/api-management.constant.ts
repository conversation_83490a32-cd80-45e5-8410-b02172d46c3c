export const METHOD_COLUMN_RULES = {
  GET: { column: [], isShowNullable: false },
  POST: { column: ['id'], isShowNullable: true },
  PUT: { column: ['id', 'nrp'], isShowNullable: false },
};

export const getMethodRules: (method: string) => {
  column: string[];
  isShowNullable: boolean;
} = (method: string) => {
  const key = method.toUpperCase() as keyof typeof METHOD_COLUMN_RULES;
  return METHOD_COLUMN_RULES[key];
};

export const CONSTANT_REST_METHOD = {
  GET: 'get',
  POST: 'post',
  PUT: 'put',
} as const;
