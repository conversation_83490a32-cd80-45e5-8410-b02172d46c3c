import { createConstantActivity } from '../utils/log.util';
import { ConstantLogModuleEnum, ConstantLogTypeEnum } from '../enums/log.enum';

export const CONSTANT_LOG = {
  // API
  API_CREATE: createConstantActivity(
    ConstantLogModuleEnum.API_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  API_READ: createConstantActivity(
    ConstantLogModuleEnum.API_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  API_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.API_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  API_DELETE: createConstantActivity(
    ConstantLogModuleEnum.API_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // API GENERATOR
  API_GENERATOR_CREATE: createConstantActivity(
    ConstantLogModuleEnum.API_GENERATOR_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  API_GENERATOR_READ: createConstantActivity(
    ConstantLogModuleEnum.API_GENERATOR_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  API_GENERATOR_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.API_GENERATOR_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  API_GENERATOR_DELETE: createConstantActivity(
    ConstantLogModuleEnum.API_GENERATOR_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // API KEY
  API_KEY_CREATE: createConstantActivity(
    ConstantLogModuleEnum.API_KEY_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  API_KEY_READ: createConstantActivity(
    ConstantLogModuleEnum.API_KEY_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  API_KEY_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.API_KEY_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  API_KEY_UPSERT: createConstantActivity(
    ConstantLogModuleEnum.API_KEY_MODULE,
    ConstantLogTypeEnum.UPSERT_LOG_TYPE,
  ),
  API_KEY_DELETE: createConstantActivity(
    ConstantLogModuleEnum.API_KEY_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // AGAMA
  AGAMA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.AGAMA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  AGAMA_READ: createConstantActivity(
    ConstantLogModuleEnum.AGAMA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  AGAMA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.AGAMA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  AGAMA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.AGAMA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // ASSESSMENT
  ASSESSMENT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.ASSESSMENT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  ASSESSMENT_READ: createConstantActivity(
    ConstantLogModuleEnum.ASSESSMENT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  ASSESSMENT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.ASSESSMENT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  ASSESSMENT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.ASSESSMENT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // AUTHENTICATION
  AUTHENTICATION_CREATE: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  AUTHENTICATION_LOGIN: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.LOGIN_LOG_TYPE,
  ),
  AUTHENTICATION_RESEND_OTP: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.RESEND_OTP_PASSWORD_LOG_TYPE,
  ),
  AUTHENTICATION_VERIFY_OTP: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.VERIFY_OTP_LOG_TYPE,
  ),
  AUTHENTICATION_ADD_DEVICE_TOKEN: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.ADD_DEVICE_TOKEN,
  ),
  AUTHENTICATION_LOGOUT: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.LOGOUT_LOG_TYPE,
  ),
  AUTHENTICATION_FORGOT_PASSWORD: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.RESET_PASSWORD_LOG_TYPE,
  ),
  AUTHENTICATION_RESET_PASSWORD: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.RESET_PASSWORD_LOG_TYPE,
  ),
  AUTHENTICATION_READ: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  AUTHENTICATION_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  AUTHENTICATION_DELETE: createConstantActivity(
    ConstantLogModuleEnum.AUTHENTICATION_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BAGASSUS
  BAGASSUS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BAGASSUS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BAGASSUS_UPLOAD: createConstantActivity(
    ConstantLogModuleEnum.BAGASSUS_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  BAGASSUS_READ: createConstantActivity(
    ConstantLogModuleEnum.BAGASSUS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BAGASSUS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BAGASSUS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BAGASSUS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BAGASSUS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BAGIAN
  BAGIAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BAGIAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BAGIAN_READ: createConstantActivity(
    ConstantLogModuleEnum.BAGIAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BAGIAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BAGIAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BAGIAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BAGIAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BAGSIPERS
  BAGSIPERS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BAGSIPERS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BAGSIPERS_READ: createConstantActivity(
    ConstantLogModuleEnum.BAGSIPERS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BAGSIPERS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BAGSIPERS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BAGSIPERS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BAGSIPERS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BAHASA
  BAHASA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BAHASA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BAHASA_READ: createConstantActivity(
    ConstantLogModuleEnum.BAHASA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BAHASA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BAHASA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BAHASA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BAHASA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BEASISWA DIKUM DINAS
  BEASISWA_DIKUM_DINAS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BEASISWA_DIKUM_DINAS_UPLOAD: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  BEASISWA_DIKUM_DINAS_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  BEASISWA_DIKUM_DINAS_READ: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BEASISWA_DIKUM_DINAS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BEASISWA_DIKUM_DINAS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BEASISWA_DIKUM_DINAS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // BERITA
  BERITA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.BERITA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  BERITA_READ: createConstantActivity(
    ConstantLogModuleEnum.BERITA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  BERITA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.BERITA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  BERITA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.BERITA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKBANGSPES
  DIKBANGSPES_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKBANGSPES_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKBANGSPES_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKBANGSPES_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKBANGSPES LOKASI
  DIKBANGSPES_LOKASI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_LOKASI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKBANGSPES_LOKASI_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_LOKASI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKBANGSPES_LOKASI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_LOKASI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKBANGSPES_LOKASI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_LOKASI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKBANGSPES TINGKAT
  DIKBANGSPES_TINGKAT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKBANGSPES_TINGKAT_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKBANGSPES_TINGKAT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKBANGSPES_TINGKAT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGSPES_TINGKAT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKBANGUM
  DIKBANGUM_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKBANGUM_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKBANGUM_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKBANGUM_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKBANGUM KATEGORI
  DIKBANGUM_KATEGORI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_KATEGORI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKBANGUM_KATEGORI_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_KATEGORI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKBANGUM_KATEGORI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_KATEGORI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKBANGUM_KATEGORI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKBANGUM_KATEGORI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKTUK
  DIKTUK_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKTUK_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKTUK_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKTUK_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKTUK KATEGORI
  DIKTUK_KATEGORI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKTUK_KATEGORI_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKTUK_KATEGORI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKTUK_KATEGORI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKTUK_KATEGORI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // DIKUM
  DIKUM_CREATE: createConstantActivity(
    ConstantLogModuleEnum.DIKUM_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  DIKUM_READ: createConstantActivity(
    ConstantLogModuleEnum.DIKUM_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  DIKUM_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.DIKUM_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  DIKUM_DELETE: createConstantActivity(
    ConstantLogModuleEnum.DIKUM_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // MDM - DUPLIKASI JABATAN
  MDM_DUPLIKASI_JABATAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.MDM_DUPLIKASI_JABATAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  MDM_DUPLIKASI_JABATAN_READ: createConstantActivity(
    ConstantLogModuleEnum.MDM_DUPLIKASI_JABATAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),

  // MDM - DUPLIKASI SATUAN
  MDM_DUPLIKASI_SATUAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.MDM_DUPLIKASI_SATUAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  MDM_DUPLIKASI_SATUAN_READ: createConstantActivity(
    ConstantLogModuleEnum.MDM_DUPLIKASI_SATUAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),

  // E-KANDIDAT
  E_KANDIDAT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.E_KANDIDAT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  E_KANDIDAT_READ: createConstantActivity(
    ConstantLogModuleEnum.E_KANDIDAT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  E_KANDIDAT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.E_KANDIDAT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  E_KANDIDAT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.E_KANDIDAT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EKTA BATCH
  E_KTA_BATCH_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_BATCH_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  E_KTA_BATCH_READ: createConstantActivity(
    ConstantLogModuleEnum.EKTA_BATCH_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  E_KTA_BATCH_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_BATCH_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  E_KTA_BATCH_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_BATCH_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EKTA V1
  E_KTA_V1_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V1_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  E_KTA_V1_READ: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V1_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  E_KTA_V1_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V1_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  E_KTA_V1_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V1_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EKTA V2
  E_KTA_V2_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V2_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  E_KTA_V2_READ: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V2_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  E_KTA_V2_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V2_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  E_KTA_V2_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EKTA_V2_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // E-ROHANI
  E_ROHANI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.E_ROHANI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  E_ROHANI_READ: createConstantActivity(
    ConstantLogModuleEnum.E_ROHANI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  E_ROHANI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.E_ROHANI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  E_ROHANI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.E_ROHANI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // ESELON
  ESELON_CREATE: createConstantActivity(
    ConstantLogModuleEnum.ESELON_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  ESELON_READ: createConstantActivity(
    ConstantLogModuleEnum.ESELON_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  ESELON_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.ESELON_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  ESELON_DELETE: createConstantActivity(
    ConstantLogModuleEnum.ESELON_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EXCEL
  EXCEL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EXCEL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EXCEL_READ: createConstantActivity(
    ConstantLogModuleEnum.EXCEL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EXCEL_READ_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.EXCEL_MODULE,
    ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
  ),
  EXCEL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EXCEL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EXCEL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EXCEL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // GENERATE NRP
  GENERATE_NRP_CREATE: createConstantActivity(
    ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  GENERATE_NRP_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  GENERATE_NRP_READ: createConstantActivity(
    ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  GENERATE_NRP_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  GENERATE_NRP_DELETE: createConstantActivity(
    ConstantLogModuleEnum.GENERATE_NRP_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // GOLONGAN
  GOLONGAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.GOLONGAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  GOLONGAN_READ: createConstantActivity(
    ConstantLogModuleEnum.GOLONGAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  GOLONGAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.GOLONGAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  GOLONGAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.GOLONGAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // JABATAN
  JABATAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  JABATAN_READ: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  JABATAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  JABATAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // JABATAN KATEGORI
  JABATAN_KATEGORI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  JABATAN_KATEGORI_READ: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  JABATAN_KATEGORI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  JABATAN_KATEGORI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.JABATAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // JASMANI PERSONEL
  JASMANI_PERSONEL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  JASMANI_PERSONEL_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  JASMANI_PERSONEL_READ: createConstantActivity(
    ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  JASMANI_PERSONEL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  JASMANI_PERSONEL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.JASMANI_PERSONEL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // JURUSAN
  JURUSAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.JURUSAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  JURUSAN_READ: createConstantActivity(
    ConstantLogModuleEnum.JURUSAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  JURUSAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.JURUSAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  JURUSAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.JURUSAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KABUPATEN
  KABUPATEN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KABUPATEN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KABUPATEN_READ: createConstantActivity(
    ConstantLogModuleEnum.KABUPATEN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KABUPATEN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KABUPATEN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KABUPATEN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KABUPATEN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KEMAMPUAN BAHASA
  KEMAMPUAN_BAHASA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KEMAMPUAN_BAHASA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KEMAMPUAN_BAHASA_READ: createConstantActivity(
    ConstantLogModuleEnum.KEMAMPUAN_BAHASA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KEMAMPUAN_BAHASA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KEMAMPUAN_BAHASA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KEMAMPUAN_BAHASA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KEMAMPUAN_BAHASA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KENAIKAN GAJI BERKALA
  KENAIKAN_GAJI_BERKALA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KENAIKAN_GAJI_BERKALA_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  KENAIKAN_GAJI_BERKALA_UPLOAD_FILE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  KENAIKAN_GAJI_BERKALA_READ: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KENAIKAN_GAJI_BERKALA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KENAIKAN_GAJI_BERKALA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_GAJI_BERKALA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KENAIKAN PANGKAT
  KENAIKAN_PANGKAT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KENAIKAN_PANGKAT_READ: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KENAIKAN_PANGKAT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KENAIKAN_PANGKAT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KERJASAMA
  KERJASAMA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KERJASAMA_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  KERJASAMA_WRITE_PDF: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
  ),
  KERJASAMA_READ: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KERJASAMA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KERJASAMA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KERJASAMA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // KHIRDIN
  KHIRDIN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.KHIRDIN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  KHIRDIN_READ: createConstantActivity(
    ConstantLogModuleEnum.KHIRDIN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  KHIRDIN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.KHIRDIN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  KHIRDIN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.KHIRDIN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // LEVEL
  LEVEL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.LEVEL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  LEVEL_READ: createConstantActivity(
    ConstantLogModuleEnum.LEVEL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  LEVEL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.LEVEL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  LEVEL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.LEVEL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // LOGS ACTIVITY
  LOGS_ACTIVITY_CREATE: createConstantActivity(
    ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  LOGS_ACTIVITY_READ: createConstantActivity(
    ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  LOGS_ACTIVITY_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  LOGS_ACTIVITY_DELETE: createConstantActivity(
    ConstantLogModuleEnum.LOGS_ACTIVITY_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // MISI
  MISI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.MISI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  MISI_READ: createConstantActivity(
    ConstantLogModuleEnum.MISI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  MISI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.MISI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  MISI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.MISI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // MUTASI JABATAN
  MUTASI_JABATAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  MUTASI_JABATAN_UPLOAD: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  MUTASI_JABATAN_READ: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  MUTASI_JABATAN_READ_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
  ),
  MUTASI_JABATAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  MUTASI_JABATAN_UPSERT: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.UPSERT_LOG_TYPE,
  ),
  MUTASI_JABATAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // NCR
  NCR_CREATE: createConstantActivity(
    ConstantLogModuleEnum.NCR_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  NCR_UPLOAD: createConstantActivity(
    ConstantLogModuleEnum.NCR_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  NCR_READ: createConstantActivity(
    ConstantLogModuleEnum.NCR_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  NCR_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.NCR_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  NCR_DELETE: createConstantActivity(
    ConstantLogModuleEnum.NCR_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // NIVELLERING
  NIVELLERING_CREATE: createConstantActivity(
    ConstantLogModuleEnum.NIVELLERING_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  NIVELLERING_READ: createConstantActivity(
    ConstantLogModuleEnum.NIVELLERING_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  NIVELLERING_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.NIVELLERING_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  NIVELLERING_DELETE: createConstantActivity(
    ConstantLogModuleEnum.NIVELLERING_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // NOTIFIKASI
  NOTIFIKASI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  NOTIFIKASI_READ: createConstantActivity(
    ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  NOTIFIKASI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  NOTIFIKASI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.NOTIFIKASI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PANGKAT
  PANGKAT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PANGKAT_READ: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PANGKAT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PANGKAT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PANGKAT KATEGORI
  PANGKAT_KATEGORI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PANGKAT_KATEGORI_READ: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PANGKAT_KATEGORI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PANGKAT_KATEGORI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PANGKAT_KATEGORI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PEKERJAAN
  PEKERJAAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PEKERJAAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PEKERJAAN_READ: createConstantActivity(
    ConstantLogModuleEnum.PEKERJAAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PEKERJAAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PEKERJAAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PEKERJAAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PEKERJAAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PELATIHAN
  PELATIHAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PELATIHAN_READ: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PELATIHAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PELATIHAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGAJUAN CUTI
  PENGAJUAN_CUTI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGAJUAN_CUTI_UPLOAD: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
  ),
  PENGAJUAN_CUTI_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGAJUAN_CUTI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGAJUAN_CUTI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_CUTI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PELATIHAN KATEGORI
  PELATIHAN_KATEGORI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PELATIHAN_KATEGORI_READ: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PELATIHAN_KATEGORI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PELATIHAN_KATEGORI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PELATIHAN_KATEGORI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGAJUAN PENGAKHIRAN DINAS
  PENGAJUAN_PENGAKHIRAN_DINAS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGAJUAN PENGAKHIRAN DINAS BUP
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_UPSERT: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.UPSERT_LOG_TYPE,
  ),
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGAJUAN PENGHARGAAN
  PENGAJUAN_PENGHARGAAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGHARGAAN_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGAJUAN_PENGHARGAAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGAJUAN_PENGHARGAAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGAJUAN TANHOR
  PENGAJUAN_TANHOR_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGAJUAN_TANHOR_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  PENGAJUAN_TANHOR_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGAJUAN_TANHOR_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGAJUAN_TANHOR_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGAJUAN_TANHOR_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENGHARGAAN
  PENGHARGAAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENGHARGAAN_READ: createConstantActivity(
    ConstantLogModuleEnum.PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENGHARGAAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENGHARGAAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PENUGASAN LUAR STRUKTUR
  PENUGASAN_LUAR_STRUKTUR_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PENUGASAN_LUAR_STRUKTUR_READ: createConstantActivity(
    ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PENUGASAN_LUAR_STRUKTUR_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PENUGASAN_LUAR_STRUKTUR_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PENUGASAN_LUAR_STRUKTUR_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PERATURAN POLRI
  PERATURAN_POLRI_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PERATURAN_POLRI_READ: createConstantActivity(
    ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PERATURAN_POLRI_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PERATURAN_POLRI_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PERMISSION
  PERMISSION_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PERMISSION_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PERMISSION_READ: createConstantActivity(
    ConstantLogModuleEnum.PERMISSION_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PERMISSION_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PERMISSION_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PERMISSION_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PERMISSION_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PERSONEL
  PERSONEL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PERSONEL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PERSONEL_READ: createConstantActivity(
    ConstantLogModuleEnum.PERSONEL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PERSONEL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PERSONEL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PERSONEL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PERSONEL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA
  EPATMA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA SISWA
  EPATMA_SISWA_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_SISWA_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_SISWA_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_SISWA_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_SISWA_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA PENGOLAHAN DATA DOKUMEN
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_READ_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA DISTRIBUSI DATA PENDIDIKAN
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_WRITE_PDF: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_READ_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA DATA PENDIDIKAN PEMBENTUKAN
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_READ_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // EPATMA DATA KELULUSAN REKRUTMEN
  EPATMA_DATA_KELULUSAN_REKRUTMEN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_READ: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_READ_MINIO: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.READ_MINIO_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  EPATMA_DATA_KELULUSAN_REKRUTMEN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PRESTASI OLAHRAGA PERSONEL
  PRESTASI_OLAHRAGA_PERSONEL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PRESTASI_OLAHRAGA_PERSONEL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PRESTASI_OLAHRAGA_PERSONEL_READ: createConstantActivity(
    ConstantLogModuleEnum.PRESTASI_OLAHRAGA_PERSONEL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PRESTASI_OLAHRAGA_PERSONEL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PRESTASI_OLAHRAGA_PERSONEL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PRESTASI_OLAHRAGA_PERSONEL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PRESTASI_OLAHRAGA_PERSONEL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PROMOSI JABATAN
  PROMOSI_JABATAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PROMOSI_JABATAN_READ: createConstantActivity(
    ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PROMOSI_JABATAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PROMOSI_JABATAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PROMOSI_JABATAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PSIKOLOGI E MENTAL
  PSIKOLOGI_E_MENTAL_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_MENTAL_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PSIKOLOGI_E_MENTAL_READ: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_MENTAL_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PSIKOLOGI_E_MENTAL_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_MENTAL_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PSIKOLOGI_E_MENTAL_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_MENTAL_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PSIKOLOGI E KONSELING
  PSIKOLOGI_E_KONSELING_CREATE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  PSIKOLOGI_E_KONSELING_READ: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  PSIKOLOGI_E_KONSELING_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  PSIKOLOGI_E_KONSELING_DELETE: createConstantActivity(
    ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),
  // REKRUTMEN BAGRIMDIK
  REKRUTMEN_BAGRIMDIK_CREATE: createConstantActivity(
    ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  REKRUTMEN_BAGRIMDIK_READ: createConstantActivity(
    ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  REKRUTMEN_BAGRIMDIK_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  REKRUTMEN_BAGRIMDIK_DELETE: createConstantActivity(
    ConstantLogModuleEnum.REKRUTMEN_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // RIWAYAT LENGKAP
  RIWAYAT_LENGKAP_CREATE: createConstantActivity(
    ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  RIWAYAT_LENGKAP_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  RIWAYAT_LENGKAP_READ: createConstantActivity(
    ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  RIWAYAT_LENGKAP_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  RIWAYAT_LENGKAP_DELETE: createConstantActivity(
    ConstantLogModuleEnum.RIWAYAT_LENGKAP_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // ROLES
  ROLES_CREATE: createConstantActivity(
    ConstantLogModuleEnum.ROLES_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  ROLES_READ: createConstantActivity(
    ConstantLogModuleEnum.ROLES_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  ROLES_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.ROLES_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  ROLES_DELETE: createConstantActivity(
    ConstantLogModuleEnum.ROLES_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // ROLE TIPE
  ROLE_TIPE_CREATE: createConstantActivity(
    ConstantLogModuleEnum.ROLE_TIPE_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  ROLE_TIPE_READ: createConstantActivity(
    ConstantLogModuleEnum.ROLE_TIPE_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  ROLE_TIPE_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.ROLE_TIPE_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  ROLE_TIPE_DELETE: createConstantActivity(
    ConstantLogModuleEnum.ROLE_TIPE_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SATUAN
  SATUAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SATUAN_READ: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SATUAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SATUAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SATUAN JABATAN
  SATUAN_JABATAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SATUAN_JABATAN_READ: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SATUAN_JABATAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SATUAN_JABATAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JABATAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SATUAN JENIS
  SATUAN_JENIS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SATUAN_JENIS_READ: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SATUAN_JENIS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SATUAN_JENIS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SATUAN_JENIS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // PROMOSI JABATAN
  SELEKSI_BAGASSUS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SELEKSI_BAGASSUS_READ: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SELEKSI_BAGASSUS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SELEKSI_BAGASSUS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGASSUS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SELEKSI BAGLEKDIK
  SELEKSI_BAGLEKDIK_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SELEKSI_BAGLEKDIK_READ: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SELEKSI_BAGLEKDIK_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SELEKSI_BAGLEKDIK_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGLEKDIK_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SELEKSI BAGRIMDIK
  SELEKSI_BAGRIMDIK_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_READ: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SELEKSI BAGRIMDIK PNS
  SELEKSI_BAGRIMDIK_PNS_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_PNS_READ: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_PNS_READ_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    ConstantLogTypeEnum.READ_EXCEL_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_PNS_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SELEKSI_BAGRIMDIK_PNS_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SELEKSI_BAGRIMDIK_PNS_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SERTIFIKAT
  SERTIFIKAT_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SERTIFIKAT_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SERTIFIKAT_READ: createConstantActivity(
    ConstantLogModuleEnum.SERTIFIKAT_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SERTIFIKAT_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SERTIFIKAT_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SERTIFIKAT_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SERTIFIKAT_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SIPK
  SIPK_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SIPK_WRITE_PDF: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.WRITE_PDF_LOG_TYPE,
  ),
  SIPK_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  SIPK_READ: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SIPK_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SIPK_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SIPK_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SIPP 3
  SIPP3_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SIPP3_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  SIPP3_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  SIPP3_READ: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SIPP3_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SIPP3_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SIPP3_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SIPP REKAP
  SIPP_REKAP_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SIPP_REKAP_WRITE_MINIO: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.WRITE_MINIO_LOG_TYPE,
  ),
  SIPP_REKAP_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  SIPP_REKAP_READ: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SIPP_REKAP_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SIPP_REKAP_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SIPP_REKAP_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // STATUS KAWIN
  STATUS_KAWIN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.STATUS_KAWIN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  STATUS_KAWIN_READ: createConstantActivity(
    ConstantLogModuleEnum.STATUS_KAWIN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  STATUS_KAWIN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.STATUS_KAWIN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  STATUS_KAWIN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.STATUS_KAWIN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SUKU
  SUKU_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SUKU_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SUKU_READ: createConstantActivity(
    ConstantLogModuleEnum.SUKU_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SUKU_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SUKU_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SUKU_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SUKU_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // SURVEY
  SURVEY_CREATE: createConstantActivity(
    ConstantLogModuleEnum.SURVEY_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  SURVEY_WRITE_EXCEL: createConstantActivity(
    ConstantLogModuleEnum.SURVEY_MODULE,
    ConstantLogTypeEnum.WRITE_EXCEL_LOG_TYPE,
  ),
  SURVEY_READ: createConstantActivity(
    ConstantLogModuleEnum.SURVEY_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  SURVEY_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.SURVEY_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  SURVEY_DELETE: createConstantActivity(
    ConstantLogModuleEnum.SURVEY_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // TANHOR
  TANHOR_CREATE: createConstantActivity(
    ConstantLogModuleEnum.TANHOR_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  TANHOR_READ: createConstantActivity(
    ConstantLogModuleEnum.TANHOR_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  TANHOR_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.TANHOR_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  TANHOR_DELETE: createConstantActivity(
    ConstantLogModuleEnum.TANHOR_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // TINGKAT PENGHARGAAN
  TINGKAT_PENGHARGAAN_CREATE: createConstantActivity(
    ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  TINGKAT_PENGHARGAAN_READ: createConstantActivity(
    ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  TINGKAT_PENGHARGAAN_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  TINGKAT_PENGHARGAAN_DELETE: createConstantActivity(
    ConstantLogModuleEnum.TINGKAT_PENGHARGAAN_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),
  // USER
  USER_V1_CREATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V1_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  USER_V1_READ: createConstantActivity(
    ConstantLogModuleEnum.USERS_V1_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  USER_V1_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V1_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  USER_V1_DELETE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V1_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  USER_V2_CREATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V2_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  USER_V2_READ: createConstantActivity(
    ConstantLogModuleEnum.USERS_V2_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  USER_V2_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V2_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  USER_V2_DELETE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V2_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  USER_V3_CREATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V3_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  USER_V3_READ: createConstantActivity(
    ConstantLogModuleEnum.USERS_V3_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  USER_V3_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V3_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  USER_V3_DELETE: createConstantActivity(
    ConstantLogModuleEnum.USERS_V3_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),

  // WORKFLOW
  WORKFLOW_CREATE: createConstantActivity(
    ConstantLogModuleEnum.WORKFLOW_MODULE,
    ConstantLogTypeEnum.CREATE_LOG_TYPE,
  ),
  WORKFLOW_READ: createConstantActivity(
    ConstantLogModuleEnum.WORKFLOW_MODULE,
    ConstantLogTypeEnum.READ_LOG_TYPE,
  ),
  WORKFLOW_UPDATE: createConstantActivity(
    ConstantLogModuleEnum.WORKFLOW_MODULE,
    ConstantLogTypeEnum.UPDATE_LOG_TYPE,
  ),
  WORKFLOW_DELETE: createConstantActivity(
    ConstantLogModuleEnum.WORKFLOW_MODULE,
    ConstantLogTypeEnum.DELETE_LOG_TYPE,
  ),
};
