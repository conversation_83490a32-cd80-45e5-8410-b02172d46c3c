import * as _ from 'lodash';
import {
  IConvertRoleAccessModules,
  IRecursiveRoles,
  IRoleAccess,
  IRoleAccessModules,
  IRoleAccessV2,
} from '../interfaces/roles.interface';
import { capitalizeEachWord } from './common.utils';

export const mapValidatedDataForGeneralModule = (
  data: {
    validatedAtasan: number;
    validatedModuleId: number;
    validatedPortalIdLs: number[];
    validatedLevel: number;
    validatedPermissionIdLs: number[];
  }[],
  role_id: number,
) => {
  return data.flatMap(
    ({ validatedModuleId: module_id, validatedPermissionIdLs }) => {
      return validatedPermissionIdLs.map((el) => {
        return {
          role_id,
          module_id,
          permission_id: el,
          portal_id: null,
        };
      });
    },
  );
};

export const mapValidatedDataForPortalModule = (
  data: {
    validatedAtasan: number;
    validatedModuleId: number;
    validatedPortalIdLs: number[];
    validatedLevel: number;
    validatedPermissionIdLs: number[];
  }[],
  role_id: number,
) => {
  return data.flatMap(
    ({
      validatedModuleId: module_id,
      validatedPortalIdLs,
      validatedPermissionIdLs,
    }) =>
      validatedPortalIdLs.flatMap((portal_id) => {
        const permissionIds =
          validatedPermissionIdLs && validatedPermissionIdLs.length > 0
            ? validatedPermissionIdLs
            : [null];

        return permissionIds.map((permission_id) => ({
          role_id,
          module_id,
          permission_id,
          portal_id,
        }));
      }),
  );
};

export const convertRoleAccess = (data: IRoleAccess[]) => {
  if (!data.length) return {};

  const groupedModule = _.groupBy(data, (item) => item.modules?.id);
  const modules = Object.keys(groupedModule).map((moduleId) => {
    if (moduleId !== 'undefined') {
      const items = groupedModule[moduleId];
      return {
        id: Number(moduleId),
        nama: items[0].modules?.nama,
        permission: _.uniqBy(
          items.map((item) => item.permission).filter(Boolean),
          'id',
        ),
        portal: _.uniqBy(
          items.map((item) => item.portal).filter(Boolean),
          'id',
        ),
      };
    }
  });

  return {
    id: data[0].role.id,
    nama: data[0].role.nama,
    level: data[0].role.level || null,
    bagian: data[0].role.bagian || null,
    satuan: data[0].role.satuan || null,
    atasan_role: data[0].role.atasan_role || null,
    role_tipe: data[0].role.role_tipe || null,
    created_at: data[0].role.created_at || null,
    module: modules,
  };
};

export const convertRoleAccessV2 = (
  data: IRecursiveRoles[],
): IRoleAccessV2[] => {
  if (!data.length) return null;

  const results: Record<
    number,
    IRoleAccessV2 & { modules: IConvertRoleAccessModules[] }
  > = {};
  const orderedRoleIds: number[] = [];

  for (const item of data) {
    if (!results[item.role_id]) {
      orderedRoleIds.push(item.role_id);

      results[item.role_id] = {
        id: item.role_id,
        nama: capitalizeEachWord(item.role_name),
        level: item.level ?? null,
        bagian: null,
        satuan: null,
        atasan_role: item.atasan_role,
        role_tipe: item.role_tipe,
        is_fungsi: item.is_fungsi ?? false,
        created_at: item.created_at,
        modules: [],
      };
    }

    mappingModule(results[item.role_id].modules, item);
  }

  return orderedRoleIds.map((id) => results[id]);
};

export const mappingModule = (
  currentModules: IConvertRoleAccessModules[],
  item: IRecursiveRoles,
) => {
  const splitParent = item.chain_parent?.split(' - ');
  if (!splitParent) return;

  let currentLevel = currentModules;

  for (let x = splitParent.length - 1; x >= 0; x--) {
    const currentChain = splitParent.slice(x).join(' - ');

    let existing = currentLevel.find(
      (mod) => mod.chain_parent === currentChain,
    );

    if (!existing) {
      existing = {
        id: item.module_id,
        nama: item.module_name,
        name_long: capitalizeEachWord(item.module_name_long),
        chain_parent: currentChain,
        permissions: [],
        modules: [],
      };
      currentLevel.push(existing);
    }

    if (x === 0 && item.permission_id && item.permission_label) {
      const alreadyAdded = existing.permissions.find(
        (p) => p.id === item.permission_id,
      );
      if (!alreadyAdded) {
        existing.permissions.push({
          id: item.permission_id,
          label: capitalizeEachWord(item.permission_label),
          nama: item.permission_name,
        });
      }
    }

    currentLevel = existing.modules;
  }
};
export const convertModuleToArray = (
  modules: Record<string, IRoleAccessModules>,
) => {
  let result: IConvertRoleAccessModules[] = [];
  for (const [_, module] of Object.entries(modules)) {
    result.push({
      id: module.id,
      nama: module.nama,
      name_long: capitalizeEachWord(module.name_long),
      chain_parent: module.chain_parent,
      permissions: module.permissions,
      modules: convertModuleToArray(module?.modules),
    });
  }

  return result;
};
export const convertToFormatCreate = (data: any[]) => {
  const existingData = [];
  const seenKeys = new Set<string>();

  for (const item of Object.values(data)) {
    const uniqueKey = `${item.role.id}-${item.modules.id}-${item.permission?.id ?? ''}-${item.portal?.id ?? ''}`;

    if (!seenKeys.has(uniqueKey)) {
      existingData.push({
        role_id: item.role.id,
        module_id: item.modules.id,
        permission_id: item.permission?.id ?? null,
        portal_id: item.portal?.id ?? null,
      });

      seenKeys.add(uniqueKey);
    }
  }

  return existingData;
};
