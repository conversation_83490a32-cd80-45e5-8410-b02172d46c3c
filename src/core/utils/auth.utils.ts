import { IRecursiveRoles } from "../interfaces/roles.interface";

export function convertUserToPangkatShortName(user: any): string | null {
  return user.pangkat_personel[0]?.pangkat.nama_singkat || null;
}

export function convertUserToRoleId(user: any): any | null {
  return user.users?.users_role[0]?.role.id || null;
}

export function convertUserToRoleName(user: any): string {
  return user.users?.users_role[0]?.role.nama || 'Personel';
}

export function convertUserToRoleTipe(user: any): string | null {
  return user.users?.users_role[0]?.role?.role_tipe?.nama || null;
}

export function convertUserToLevelId(user: any): any | null {
  return (
    user.users?.users_role[0]?.level?.id ||
    user.users?.users_role[0]?.role?.level?.id ||
    null
  );
}

export function convertUserToLevelName(user: any): string | null {
  return (
    user.users?.users_role[0]?.level?.nama ||
    user.users?.users_role[0]?.role?.level?.nama ||
    null
  );
}

export function convertUserToBagianId(user: any): any | null {
  return user.users?.users_role[0]?.role?.bagian?.id || null;
}

export function convertUserToSatuanId(user: any): any | null {
  return user.jabatan_personel[0]?.jabatans?.satuan?.id || null;
}

export function convertUserToSatuanName(user: any): string | null {
  return user.jabatan_personel[0]?.jabatans?.satuan?.nama || null;
}

export function convertUserToJabatan(user: any): any {
  return user.jabatan_personel?.length
    ? {
        id: user.jabatan_personel[0]?.jabatans.id,
        nama: user.jabatan_personel[0]?.jabatans.nama,
        satuan: user.jabatan_personel[0]?.jabatans.satuan,
      }
    : null;
}

export function convertUserToBagianName(user: any): string | null {
  return user.users?.users_role[0]?.role?.bagian?.nama || null;
}

export function convertUserToPermissionNameLs(user: any): string[] {
  return user.users?.users_role[0]?.role.role_permission.map(
    (item) => item.permission.nama,
  );
}

export function convertUserToRoleLs(
  user: any,
): { nama: string; level: string }[] {
  return (
    user.users?.users_role.map((item) => ({
      nama: item.role.nama,
      level: item.role.level?.nama || null,
    })) || []
  );
}

export function convertUserToUserManagement(user_role: IRecursiveRoles[]): string | null {
  const roleUser = user_role[0] || null;
  let userManagement = null;
  if (
    roleUser?.atasan_role === null &&
    roleUser?.level?.nama.toLowerCase() === 'superadmin'
  ) {
    userManagement = 'superadmin_baginfopers';
  } else if (
    roleUser?.atasan_role?.atasan_id === null &&
    roleUser?.level?.nama.toLowerCase() === 'level 1'
  ) {
    userManagement = 'level1_baginfopers';
  } else if (
    roleUser?.atasan_role?.atasan_id !== null &&
    roleUser?.level?.nama.toLowerCase() === 'level 1'
  ) {
    userManagement = 'level1_bagianlain';
  }
  return userManagement;
}
