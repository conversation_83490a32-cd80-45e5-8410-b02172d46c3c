import { BadRequestException } from '@nestjs/common';
import * as moment from 'moment';
import { comparison_type_enum } from '@prisma/client';
import { joinDifferentOnLastChar } from './common.utils';

export function validatePath(url: string) {
  const isVerifiedPath = url.match(/^\/([a-zA-Z0-9/-]+\/?)*$/g);
  return isVerifiedPath !== null;
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateNewPassword(password: string) {
  const validationRules = [
    {
      regex: /.{8,}/,
      message: '8 karakter',
    },
    {
      regex: /[A-Z]/,
      message: 'huruf kapital',
    },
    {
      regex: /[a-z]/,
      message: 'huruf kecil',
    },
    {
      regex: /\d/,
      message: 'angka',
    },
    {
      regex: /[!@#$%^&*(),.?":{}|<>]/,
      message: 'simbol',
    },
    {
      regex: /^\S*$/,
      message: 'Tidak boleh mengandung spasi',
    },
  ];

  const errorMessages: string[] = [];

  for (const { regex, message } of validationRules) {
    if (!regex.test(password)) {
      errorMessages.push(message);
    }
  }

  if (errorMessages.length > 0) {
    const messages = joinDifferentOnLastChar(errorMessages);
    throw new BadRequestException(`Password Anda harus memiliki ${messages}`);
  }
}

export const validationValue = (type: string, value: any): string => {
  switch (type.toUpperCase()) {
    case 'CHECKBOX':
      return String(value);
    case 'RADIO':
    case 'TEXT':
    case 'TEXTAREA':
    case 'PASSWORD':
    case 'EMAIL':
    case 'TEL':
    case 'URL':
      return String(value);
    case 'NUMBER':
      if (isNaN(Number(value)))
        throw new BadRequestException('Format angka tidak valid');
      return String(Number(value));
    case 'DATE':
      if (!moment(value, moment.ISO_8601, true).isValid())
        throw new BadRequestException('Format tanggal tidak valid');
      return moment(value).format('YYYY-MM-DD');
    default:
      throw new BadRequestException('Input tipe tidak support: ' + type);
  }
};

export const compareValues = (
  a: any,
  b: any,
  comparison_type: comparison_type_enum,
  isValidateDate: boolean = false,
): boolean => {
  if (isValidateDate) {
    const momentA = moment(a);
    const momentB = moment(b);
    return (
      {
        MIN: momentA.isAfter(momentB),
        MAX: momentA.isBefore(momentB),
        EQUAL: momentA.isSame(momentB),
      }[comparison_type] ?? false
    );
  }

  return (
    {
      MIN: a > b,
      MAX: a < b,
      EQUAL: a === b,
    }[comparison_type] ?? false
  );
};

export const validateFile = (
  file: Express.Multer.File,
  fieldName: string,
  maxSizeMB?: number | null,
  allowedExtensions?: string[] | null,
): void => {
  const extensions = allowedExtensions?.map((ext) => ext.toLowerCase()) ?? [];

  if (extensions.includes('jpg') && !extensions.includes('jpeg')) {
    extensions.push('jpeg');
  }

  if (
    extensions.length &&
    !extensions.some((ext) => file.mimetype.toLowerCase().includes(ext))
  ) {
    throw new BadRequestException(
      `Hanya format (${extensions.join(', ')}) yang diizinkan untuk file ${fieldName.toUpperCase()}!`,
    );
  }

  if (maxSizeMB != null && file.size > maxSizeMB * 1024 * 1024) {
    throw new BadRequestException(
      `Ukuran file ${fieldName} harus kurang dari ${maxSizeMB}MB!`,
    );
  }
};
