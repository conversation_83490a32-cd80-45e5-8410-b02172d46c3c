import {
  isBoolean,
  isDate,
  isFloat,
  isNumeric,
  isTime,
  isUUID,
} from 'validator';
import { BadRequestException } from '@nestjs/common';
import { OrderEnum } from '../enums/rekrutmen-bagrimdik.enum';
import { isNaN } from 'lodash';
import { IWhere } from '../interfaces/db.interface';

export const validateNumericType = (fieldName: string, value: any) => {
  if (!isNumeric(value)) {
    throw new BadRequestException(
      `${fieldName} must be number only, example : "9007199254740991".`,
    );
  }
};

const isValid = (validator, value, errorMessage, fieldName) => {
  try {
    return validator(value);
  } catch (e) {
    throw new BadRequestException(errorMessage.replace('{field}', fieldName));
  }
};
const validateAndConvert = (
  fieldName,
  value,
  validator,
  converter,
  errorMessage,
) => {
  if (!isValid(validator, value, errorMessage, fieldName))
    throw new BadRequestException(errorMessage.replace('{field}', fieldName));
  return converter(value);
};

export const convertStringToPostgresValue = (
  fieldName: string,
  dataType,
  value,
) => {
  if (!value) return null;

  const typeHandlers = {
    bigint: () =>
      validateAndConvert(
        fieldName,
        value,
        isNumeric,
        (v) => BigInt(v),
        `${fieldName} must be a valid number.`,
      ),
    integer: () =>
      validateAndConvert(
        fieldName,
        value,
        isNumeric,
        (v) => parseInt(v, 10),
        `${fieldName} must be number only, example : "900".`,
      ),
    smallint: () =>
      validateAndConvert(
        fieldName,
        value,
        isNumeric,
        (v) => parseInt(v, 10),
        `${fieldName} must be number only, example : "900".`,
      ),
    decimal: () =>
      validateAndConvert(
        fieldName,
        value,
        isFloat,
        (v) => parseFloat(v),
        `${fieldName} must be float only, example : "4.567".`,
      ),
    numeric: () =>
      validateAndConvert(
        fieldName,
        value,
        isFloat,
        (v) => parseFloat(v),
        `${fieldName} must be float only, example : "4.567".`,
      ),
    real: () =>
      validateAndConvert(
        fieldName,
        value,
        isFloat,
        (v) => parseFloat(v),
        `${fieldName} must be float only, example : "4.567".`,
      ),
    'double precision': () =>
      validateAndConvert(
        fieldName,
        value,
        isFloat,
        (v) => parseFloat(v),
        `${fieldName} must be float only, example : "4.567".`,
      ),
    'character varying': () => value.toString(),
    text: () => value.toString(),
    char: () => value.toString(),
    uuid: () => {
      if (!isUUID(value, '4')) {
        throw new BadRequestException(
          `${fieldName} must be uuid v4 only, example : "8b274a99-1826-4912-a697-ba882669a10a".`,
        );
      }
      return value.toString();
    },
    boolean: () =>
      validateAndConvert(
        fieldName,
        value,
        isBoolean,
        (v) => v === 'true',
        `${fieldName} must be boolean only, example : "true" / "false".`,
      ),
    timestamp: () =>
      validateAndConvert(
        fieldName,
        value,
        (v) => !isNaN(new Date(v)),
        (v) => new Date(v),
        `${fieldName} must be a valid datetime (YYYY-MM-DD HH:mm:sss).`,
      ),
    timestamptz: () =>
      validateAndConvert(
        fieldName,
        value,
        (v) => !isNaN(new Date(v)),
        (v) => new Date(v),
        `${fieldName} must be a valid datetime (YYYY-MM-DD HH:mm:ssss).`,
      ),
    'timestamp without time zone': () =>
      validateAndConvert(
        fieldName,
        value,
        (v) => !isNaN(new Date(v)),
        (v) => new Date(v),
        `${fieldName} must be a valid datetime (YYYY-MM-DD HH:mm:sssssa).`,
      ),
    time: () =>
      validateAndConvert(
        fieldName,
        value,
        isTime,
        (v) => new Date(v),
        `${fieldName} must be a valid time (HH:mm:ss).`,
      ),
    'time with time zone': () =>
      validateAndConvert(
        fieldName,
        value,
        isTime,
        (v) => new Date(v),
        `${fieldName} must be a valid time (HH:mm:ss).`,
      ),
    date: () =>
      validateAndConvert(
        fieldName,
        value,
        (v) => isDate(v, { format: 'YYYY-MM-DD' }),
        (v) => new Date(v),
        `${fieldName} must be a valid date (YYYY-MM-DD).`,
      ),
    interval: () => value.trim(),
    cidr: () => value.trim(),
    inet: () => value.trim(),
    macaddr: () => value.trim(),
    point: () => value.trim(),
    line: () => value.trim(),
    lseg: () => value.trim(),
    box: () => value.trim(),
    path: () => value.trim(),
    polygon: () => value.trim(),
    circle: () => value.trim(),
    xml: () => value.trim(),
    json: () => JSON.parse(value),
    jsonb: () => JSON.parse(value),
    array: () => value.split(','),
    bytea: () => Buffer.from(value, 'base64'),
  };

  // Default case
  if (typeHandlers[dataType]) {
    return typeHandlers[dataType]();
  }

  return value; // Return original value for unsupported types
};

export const convertToPostgresValue = (
  datatypePostgres: any,
  inputObject: any,
) => {
  const convertedObject = {};
  datatypePostgres.forEach(({ column_name, data_type }) => {
    const value = inputObject[column_name];
    convertedObject[column_name] = convertStringToPostgresValue(
      column_name,
      data_type,
      value,
    );
  });

  return convertedObject;
};

/**
 * Create dynamic query orm for search.
 * @param name - The name field from orm want to search on orm.
 * @param search - The string want to search.
 * @param normal - Mode of search string want to search.
 */
export function createSearchQuery({
  name,
  search,
  normal = true,
}: {
  name: string;
  search: string;
  normal?: boolean;
}) {
  // Handle the case when name is a single string
  return {
    [name]: normal
      ? { contains: search, mode: 'default' }
      : { contains: search, mode: 'insensitive' },
  };
}

export function buildOrderBy(
  field: string,
  order: string,
): Record<string, any> {
  // Validate and convert the order direction
  const orderDirection: OrderEnum =
    order === 'asc' || order === 'desc' ? (order as OrderEnum) : OrderEnum.asc; // Default to 'asc' if invalid

  // Dynamically construct the `orderBy` object
  return field.split('.').reduceRight(
    (acc, currentField) => {
      return { [currentField]: orderDirection };
    },
    { [field]: orderDirection },
  );
}

export function buildWhereClause(where?: IWhere | null): string {
  if (!where || !where['OR']?.length) return '';
  const conditions = where['OR'].map((condition) => {
    const [key, value] = Object.entries(condition)[0];
    const match = key.match('created_at');
    const column = match?.length
      ? `${key.replace('|', '.')}::text`
      : key.replace('|', '.');
    const searchText = (value as any)?.contains || '';
    return `${column} ILIKE '%${searchText}%'`;
  });
  return `AND (${conditions.join(' OR ')})`;
}
