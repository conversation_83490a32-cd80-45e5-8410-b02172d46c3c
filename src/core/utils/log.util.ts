import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../enums/log.enum';
import { ILog, ILogApi } from '../interfaces/log.interface';
import { ConstantLogType } from '../interfaces/log.type';
import { capitalizeFirstChar } from './common.utils';
import { ConstantRestMethodType } from '../interfaces/api-management.type';

export function createConstantActivity(
  module: ConstantLogModuleEnum,
  logType: ConstantLogTypeEnum,
  userConstantActivity: string = null,
): string {
  if (userConstantActivity) {
    return userConstantActivity;
  } else {
    return capitalizeFirstChar(`${logType} lewat ${module} service`);
  }
}

export function convertToLogMessage(
  logStatus: ConstantLogStatusEnum,
  logType: ConstantLogTypeEnum,
  logDataType: ConstantLogDataTypeEnum,
  logModule: ConstantLogModuleEnum,
  userLogMessage: string = null,
) {
  if (userLogMessage) {
    return userLogMessage;
  } else {
    return capitalizeFirstChar(
      `${logStatus} ${logType} lewat ${logModule} dengan tipe data ${logDataType}`,
    );
  }
}

export function convertToILogData(
  req: any,
  activity: ConstantLogType,
  message: string,
  payload: any,
): ILog {
  return {
    user_id: req.user?.id,
    activity,
    message,
    payload,
    headers: JSON.stringify(req.headers),
    url: `${req.protocol}://${req.get('Host')}${req.originalUrl}`,
    method: req.method,
  };
}

export function convertToILogApiData(
  req: any,
  api: any,
  method: ConstantRestMethodType,
  activity: ConstantLogType,
  payload: any,
): ILogApi {
  return {
    api_id: api?.id,
    user_agent: req.headers['user-agent'],
    method,
    activity,
    table: api?.table,
    payload,
    headers: JSON.stringify(req.headers),
    url: `${req.protocol}://${req.get('Host')}${req.originalUrl}`,
  };
}
