interface IBatchingProcess {
  data: Array<any>;
  limit: number;
  callback: (data: Array<any>) => Promise<Array<any>>;
}

export const BatchingProcess = async (params: IBatchingProcess) => {
  let { data } = params;
  const { callback, limit } = params;

  const chunk = Math.ceil(data.length / limit);
  let i = -1;
  while (++i < chunk) {
    const res = await callback(data.splice(i * limit, limit));

    data = [...res, ...data];
  }

  return data;
};
