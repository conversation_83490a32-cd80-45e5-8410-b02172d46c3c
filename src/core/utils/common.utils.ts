import { BadRequestException } from '@nestjs/common';
import { parse as parseDateFns } from 'date-fns';
import { parse as parseFlatted, stringify } from 'flatted';
import { mkdirSync } from 'fs';
import * as moment from 'moment/moment';
import { diskStorage } from 'multer';
import { NullValueException } from '../exceptions/null-value.exceptions';

export function maskData(
  data: unknown,
  maskedPath: string[],
  path: string = '',
): unknown {
  const parsedData = parseFlatted(stringify(data));

  if (maskedPath.includes(path)) {
    return '****';
  }

  if (Array.isArray(parsedData)) {
    return parsedData.map((item: unknown): unknown =>
      maskData(item, maskedPath, path),
    );
  }

  if (typeof parsedData === 'object' && parsedData !== null) {
    return Object.keys(parsedData).reduce<object>(
      (maskedObject: object, key: string): object => {
        const nestedPath = path ? `${path}.${key}` : key;

        return {
          ...maskedObject,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          [key]: maskData((parsedData as any)[key], maskedPath, nestedPath),
        };
      },
      {},
    );
  }

  return parsedData;
}

export function _calculateSisaWaktu(tanggalSelesai: Date) {
  if (new Date() > tanggalSelesai) return 0;

  return moment(tanggalSelesai).diff(moment(), 'days');
}

export function capitalizeFirstChar(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function capitalizeEachWord(str: string) {
  return str.replace(/[^()]+|(\([^)]*\))/g, (match, insideBracket) => {
    if (insideBracket) return insideBracket;

    return match
      .split(' ')
      .map((word) => capitalizeFirstChar(word))
      .join(' ');
  });
}

export function getDayDifference(date1: Date, date2: Date): number {
  const diffTime = Math.abs(date1.getTime() - date2.getTime());
  return Math.floor(diffTime / (1000 * 60 * 60 * 24)); // in days
}

export const checkStringOrReturnNullValue = (value: string): any => {
  return value !== 'null' && value !== 'undefined' ? value : null;
};
export const getUniqueDataToArrayObject = (objectList: any[]) => {
  const seen = new Set<string>();
  return objectList.filter((item) => {
    const serialized = JSON.stringify(item);
    if (seen.has(serialized)) {
      return false;
    }
    seen.add(serialized);
    return true;
  });
};

export const parseSearchString = (searchString: string) => {
  const searchParams = searchString.split(','); // Split the string by commas
  const parsedObject = {};
  searchParams.forEach((param) => {
    const [key, value] = param.split(':');
    if (key && !value) {
      throw new NullValueException(
        `Accessing null value for key ${key}, please provide value for given key`,
      );
    }
    if (key && value) {
      parsedObject[key.trim()] = value.trim();
    }
  });

  return parsedObject;
};

export const maskEmail = (email: string) => {
  const [name, domain] = email.split('@');
  const maskedName =
    name.length > 5
      ? name.slice(0, 3) + '*'.repeat(name.length - 5) + name.slice(-2)
      : name.charAt(0) + '*'.repeat(name.length - 2) + name.slice(-1);

  const environmentAccess = ['staging', 'production', 'development'];
  const maskedDomain = environmentAccess.includes(process.env.APP_ENV)
    ? domain
    : domain.replace(/(?<=.).(?=.*\.)/g, '*');
  return maskedName + '@' + maskedDomain;
};

export const chunkedUpdate = async (
  items: any[],
  handler: (item: any) => Promise<any>,
  size = 3,
) => {
  for (let i = 0; i < items.length; i += size) {
    await Promise.all(items.slice(i, i + size).map(handler));
  }
};
export const calculateAge = (dateOfBirth: string | Date): number => {
  if (!dateOfBirth) return 0;
  return moment().diff(moment(dateOfBirth), 'years');
};

export const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((acc, key) => acc?.[key], obj);
};

export const joinDifferentOnLastChar = (arr) => {
  if (arr.length === 0) return '';
  if (arr.length === 1) return arr[0];
  if (arr.length === 2) return arr[0] + ' dan ' + arr[1];
  return arr.slice(0, -1).join(', ') + ', dan ' + arr[arr.length - 1];
};

export const calculateDateDifference = (
  from: string | Date,
  to: string | Date = moment().toDate(),
): string => {
  const diff = moment(from).diff(to);
  const duration = moment.duration(diff);

  const years = duration.years() > 0 ? `${duration.years()} tahun` : '';
  const months = duration.months() > 0 ? ` ${duration.months()} bulan` : '';
  const days = duration.days() > 0 ? ` ${duration.days()} hari` : '';

  return `${years}${months}${days}`;
};

export const wait = async (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const parsingValueOfFormulaExcel = (value: any) => {
  if (value === '-') return '';

  if (typeof value !== 'object' || value === null || typeof value === 'boolean')
    return String(value).trim();

  if (value instanceof Date) return moment(value).toISOString();

  if ('result' in value) {
    if (typeof value.result !== 'object') return String(value.result).trim();
    return null;
  }

  return null;
};

export const toCamelCase = (str: string) => {
  return str.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
};

const setNestedProperty = (
  obj: Record<string, any>,
  path: string,
  value: any,
) => {
  const keys = path.replace(/\[(\d+|\w+)]/g, '.$1').split('.');
  keys.reduce((o, k, i) => {
    if (i === keys.length - 1) {
      o[k] = { ...(o[k] || {}), ...value };
    } else if (!o[k]) {
      o[k] = /^\d+$/.test(keys[i + 1]) ? [] : {};
    }

    return o[k];
  }, obj);
};

export const bracketToObject = (
  data: Record<string, any>,
): Record<string, any> => {
  const result = { ...data };
  for (const key in data) {
    if (key.includes('[')) {
      setNestedProperty(result, key, data[key]);
      delete result[key];
    }
  }
  return result;
};

export const romanMonth = () => {
  const romanMonths = [
    'I',
    'II',
    'III',
    'IV',
    'V',
    'VI',
    'VII',
    'VIII',
    'IX',
    'X',
    'XI',
    'XII',
  ];

  const currentMonth = new Date().getMonth();

  return romanMonths[currentMonth];
};

export const formatDate = (date) => {
  const d = new Date(date);
  return d.toISOString().split('T')[0]; // Mengambil bagian tanggal saja
};

export function fileUpload(folder = null, filter = []) {
  const fileStorage = diskStorage({
    destination: (req, file, cb) => {
      const nameFolder = `public/${folder || ''}`;
      mkdirSync(nameFolder, { recursive: true });
      cb(null, nameFolder);
    },
    filename: (req, file, cb) => {
      cb(null, new Date().getTime() + '-' + file.originalname);
    },
  });

  const fileFilter = (req, file, cb) => {
    if (!filter.length) return cb(null, true);
    if (filter.includes(file.mimetype.split('/')[1])) {
      cb(null, true);
    } else {
      cb(null, false);
    }
  };
  return {
    storage: fileStorage,
    fileFilter: fileFilter,
  };
}

export function exclude(object, keys) {
  return Object.fromEntries(
    Object.entries(object).filter(([key]) => !keys.includes(key)),
  );
}

export function arrayShuffling(arr: Array<any>) {
  const n = arr.length;

  const res = [];
  for (let i = 0; i < n - 1; i++) {
    res[i] = arr[i + 1];
  }

  res[n - 1] = arr[0];

  return res;
}

export function shuffleString(str: string): string {
  const array = str.split('');
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]]; // swap
  }
  return array.join('');
}

export function makeRandomString({
  length = 8,
  isLowerCase = true,
  isUpperCase = false,
  isNumeric = false,
  isSpecialChar = false,
}: {
  length: number;
  isLowerCase?: boolean;
  isUpperCase?: boolean;
  isNumeric?: boolean;
  isSpecialChar?: boolean;
}) {
  const lowerCase = isLowerCase ? 'abcdefghijklmnopqrstuvwxyz' : '';
  const upperCase = isUpperCase ? 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' : '';
  const numeric = isNumeric ? '0123456789' : '';
  const specialChar = isSpecialChar ? '!@#$%^&*()_+[]{}|;:,.<>?' : '';

  const characters = shuffleString(
    lowerCase + upperCase + numeric + specialChar,
  );
  const charactersLength = characters.length;

  let counter = 0;
  let result = '';

  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }

  return result;
}

export function makeRegistrationNumber(lastNumber: number): string {
  const randomString = makeRandomString({ length: 5, isNumeric: true });
  const dateNow = moment().unix().toString();
  return dateNow + randomString + (lastNumber + 1).toString().padStart(5, '0');
}

export function parseNumber(value: any, defaultValue: number = 10): number {
  // Check if the value is a number or a numeric string
  if (!isNaN(Number(value)) && value !== '') {
    return Number(value); // Convert to a number
  }
  return defaultValue; // Return the default if not numeric
}

export function parseDate(dateString: string): Date | null {
  try {
    if (!dateString) return null;

    // Try parsing as YYYY-MM-DD
    let parsedDate = parseDateFns(dateString, 'yyyy-MM-dd', new Date());
    if (!isNaN(parsedDate.getTime())) return parsedDate;

    // Try parsing as DD-MM-YYYY
    parsedDate = parseDateFns(dateString, 'dd-MM-yyyy', new Date());
    if (!isNaN(parsedDate.getTime())) return parsedDate;

    return null; // Return null if format is invalid
  } catch (err) {
    throw new BadRequestException('Failed to parse date');
  }
}

export function splitIndonesianName(fullName) {
  const nameParts = fullName.trim().split(/\s+/); // Split by spaces

  const namaDepan = nameParts[0]; // First word as Nama Depan
  const namaBelakang =
    nameParts.length > 1 ? nameParts.slice(1).join(' ') : null; // Rest as Nama Belakang or null if only one word

  return [namaDepan, namaBelakang];
}

export const BracketToObject = (
  data: Record<string, any>,
): Record<string, any> => {
  const result = { ...data };
  for (const key in data) {
    if (key.includes('[')) {
      setNestedProperty(result, key, data[key]);
      delete result[key];
    }
  }
  return result;
};

export const getYearsRange = (
  startYear?: number,
  endYear?: number,
): number[] => {
  const currentYear = endYear || new Date().getFullYear();
  const years = [];

  startYear = startYear || 2015;
  while (startYear <= currentYear) {
    years.push(startYear++);
  }

  return years;
};

export const toPercentage = (value: number, total: number): string => {
  return total ? ((value * 100) / total).toFixed(2) + ' %' : '0 %';
};

export const translateDotNotation = (data: Record<string, any>) => {
  const result: Record<string, any> = {};
  Object.keys(data).map((key) => {
    if (!key.includes('.')) {
      result[key] = data[key];
      return;
    }

    const firstKey = key.split('.')[0];
    const restKey = key.split('.').slice(1);
    if (!result[firstKey]) result[firstKey] = {};
    for (const k of restKey) {
      result[firstKey][k] = data[key];
    }
  });

  return result;
};
