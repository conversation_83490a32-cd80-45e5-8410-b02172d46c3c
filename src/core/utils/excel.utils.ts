// import Worksheet from 'exceljs/index';
import { IExcelColumns } from '../interfaces/excel.interface';
import * as ExcelJS from 'exceljs';
import { Workbook, Worksheet } from 'exceljs';

export function getColumnLetterByKey(
  worksheet: Worksheet,
  key: string,
): string {
  const columnIndex = worksheet.columns?.findIndex(
    (col: any) => col.key === key,
  );

  if (columnIndex === -1 || columnIndex === undefined)
    throw new Error(`Column with key "${key}" not found`);

  return getColumnLetter(columnIndex + 1);
}

export function getColumnLetter(columnNumber: number): string {
  let letter = '';
  while (columnNumber > 0) {
    const remainder = (columnNumber - 1) % 26;
    letter = String.fromCharCode(65 + remainder) + letter;
    columnNumber = Math.floor((columnNumber - 1) / 26);
  }
  return letter;
}

export function getCellStyleByType(
  type?: 'integer' | 'number' | 'string' | 'date' | 'currency',
): Partial<ExcelJS.Style> {
  switch (type) {
    case 'integer':
      return { numFmt: '#,##0;(#,##0)' };
    case 'number':
      return { numFmt: '#,##0.00;(#,##0.00)' };
    case 'currency':
      return { numFmt: '"Rp"#,##0;[Red]\-"Rp"#,##0' };
    case 'date':
      return { numFmt: 'dd/mm/yyyy' };
    case 'string':
    default:
      return { numFmt: '@' };
  }
}

export function addDataMasterToSheet(
  data: any[],
  sheet: Worksheet,
  hidden: boolean,
) {
  sheet.columns = data.map((item) => ({
    header: item.header,
    key: item.header,
    width: 20,
  }));

  data.forEach(({ header, values }) => {
    const columnLetter = getColumnLetterByKey(sheet, header);
    values.forEach((value, i) => {
      sheet.getCell(`${columnLetter}${i + 2}`).value = value;
    });
  });

  sheet.state = hidden ? 'hidden' : 'visible';
}

export function generateFormula(
  rawFormula: string,
  {
    row,
    columnKey,
    tableArray,
    tableNumRow,
    sheet,
  }: {
    row: number;
    columnKey?: string[];
    tableArray?: string[];
    tableNumRow: string | number;
    sheet: Worksheet;
  },
): string {
  let formula = rawFormula.replaceAll('|num_row|', `${tableNumRow}`);

  columnKey?.forEach((key, idx) => {
    const letter = getColumnLetterByKey(sheet, key);
    formula = formula.replaceAll(`|cell_${idx + 1}|`, `${letter}${row}`);
  });

  tableArray?.forEach((range, idx) => {
    formula = formula.replaceAll(`|table_array_${idx + 1}|`, range);
  });

  return formula;
}

export function applyFormulaIfAny(sheet: Worksheet, column: IExcelColumns) {
  if (!column.value) return;

  const { formula, result, columnKey, tableArray, tableNumRow } = column.value;
  const colLetter = getColumnLetterByKey(sheet, column.key);

  for (let row = 2; row <= 10; row++) {
    const finalFormula = generateFormula(formula, {
      row,
      columnKey,
      tableArray,
      tableNumRow,
      sheet,
    });

    const cell = sheet.getCell(`${colLetter}${row}`);
    cell.value = { formula: finalFormula, result };
    cell.model.result = undefined;
  }
}

export function applyValidationIfAny(
  workbook: Workbook,
  mainSheet: Worksheet,
  column: IExcelColumns,
) {
  const validation = column.dataValidation;
  if (!validation) return;

  const validationSheet = workbook.getWorksheet(validation.sheet);
  const colLetter = getColumnLetterByKey(validationSheet, column.key);

  if (validation.type === 'custom') {
    applyCustomValidation(
      workbook,
      mainSheet,
      column,
      colLetter,
      validationSheet,
    );
  } else if (validation.type === 'list') {
    applyListValidation(workbook, column, colLetter, validationSheet);
  }
}

function applyCustomValidation(
  workbook: Workbook,
  mainSheet: Worksheet,
  column: IExcelColumns,
  colLetter: string,
  sheet: Worksheet,
) {
  const { gerakan, reference } = column.dataValidation!;
  const refSheet = workbook.getWorksheet(reference.name);
  const min = getColumnLetterByKey(refSheet, reference.min);
  const max = getColumnLetterByKey(refSheet, reference.max);

  for (let row = 2; row <= 10; row++) {
    const criteria = reference.groupBy
      .map((g) => {
        const col = getColumnLetterByKey(refSheet, g);
        const val =
          g === 'gerakan'
            ? `"${gerakan}"`
            : getColumnLetterByKey(mainSheet, g) + row;
        return [`'${refSheet.name}'!${col}:${col}`, val];
      })
      .map((g) => g.join(','))
      .join(',');

    const formula = `=IFERROR(AND(${colLetter}${row}>=MINIFS('${reference.name}'!${min}:${min},${criteria}),${colLetter}${row}<=MAXIFS('${reference.name}'!${max}:${max},${criteria})),FALSE)`;

    setDataValidation(sheet.getCell(`${colLetter}${row}`), {
      type: 'custom',
      formulae: [formula],
      promptTitle: 'PENTING!',
      prompt: 'Blok kolom ini, lalu klik Data → Data Validation → OK',
      errorTitle: 'Gagal!',
      error: 'Nilai tidak sesuai. Lihat sheet Formula',
    });
  }
}

function applyListValidation(
  workbook: Workbook,
  column: IExcelColumns,
  colLetter: string,
  sheet: Worksheet,
) {
  const ref = column.dataValidation!.reference;
  const refSheet = workbook.getWorksheet(ref.name);
  const refCol = getColumnLetterByKey(refSheet, ref.key);
  const lastRow = refSheet.rowCount;

  for (let row = 2; row <= 10; row++) {
    const formula = `'${ref.name}'!${refCol}2:${refCol}${lastRow}`;
    setDataValidation(sheet.getCell(`${colLetter}${row}`), {
      type: 'list',
      formulae: [formula],
    });
  }
}

function setDataValidation(
  cell: any,
  options: {
    type: string;
    formulae: string[];
    allowBlank?: boolean;
    showInputMessage?: boolean;
    promptTitle?: string;
    prompt?: string;
    showErrorMessage?: boolean;
    errorTitle?: string;
    error?: string;
  },
) {
  cell.dataValidation = {
    allowBlank: true,
    showInputMessage: true,
    showErrorMessage: true,
    ...options,
  };
  cell.model.result = undefined;
}
