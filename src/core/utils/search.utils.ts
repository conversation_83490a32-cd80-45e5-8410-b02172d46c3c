import { CustomErrorException } from '../exceptions/custom-error.exception';
import { IColumnMapping, IOrderBy, IWhere } from '../interfaces/db.interface';
import { TSortOrderType } from '../interfaces/db.type';

export const findAgeGroup = (age: number): string => {
  switch (true) {
    case age >= 18 && age <= 30:
      return 'GOLONGAN I';
    case age >= 31 && age <= 40:
      return 'GOLONGAN II';
    case age >= 41 && age <= 50:
      return 'GOLONGAN III';
    case age >= 51 && age <= 58:
      return 'GOLONGAN IV';
    default:
      return 'Tidak sesuai kriteria';
  }
};

function buildNestedObject(fieldPath: string, value: any) {
  return fieldPath
    .split('.')
    .reverse()
    .reduce((acc, curr) => ({ [curr]: acc }), value);
}

export function SortSearchColumn(
  sort_column: string | undefined,
  sort_desc: TSortOrderType | undefined,
  search_column: string | string[],
  search_text: string | string[],
  search: string,
  columnMapping: IColumnMapping,
  hasDeletedAt: boolean = false,
  searchFields?: string[], // New optional parameter
): { orderBy: IOrderBy[]; where: IWhere } {
  const orderBy: IOrderBy[] = [];
  const where: IWhere = {};
  const sort =
    sort_desc && ['asc', 'desc'].includes(sort_desc) ? sort_desc : 'desc';

  // Handle sorting
  if (sort_column && columnMapping[sort_column]) {
    const orderObj = buildNestedObject(columnMapping[sort_column].field, sort);
    orderBy.push(orderObj);
  } else {
    // Default sorting by created_at desc if exist
    const createdAt = columnMapping?.['created_at']?.field;
    if (createdAt) {
      orderBy.push({ [createdAt]: 'desc' });
    }
  }

  // Handle specific searching
  if (
    Array.isArray(search_column) &&
    Array.isArray(search_text) &&
    search_column.length === search_text.length
  ) {
    const searchConditions = {};
    search_column.forEach((column, index) => {
      if (columnMapping[column]) {
        const searchValue = search_text[index];
        if (!searchConditions[column]) {
          searchConditions[column] = [];
        }
        searchConditions[column].push(searchValue);
      }
    });

    where.AND = [];
    for (const column in searchConditions) {
      const values = searchConditions[column];
      if (columnMapping[column]) {
        if (columnMapping[column].type === 'boolean') {
          const searchField = buildNestedObject(
            columnMapping[column].field,
            values[0] === 'true',
          );
          where.AND.push(searchField);
        } else {
          const searchField = values.map((value: string) => {
            if (['bigint', 'number'].includes(columnMapping[column].type)) {
              return buildNestedObject(
                columnMapping[column].field,
                value === 'null' ? null : BigInt(value),
              );
            } else {
              return buildNestedObject(columnMapping[column].field, {
                contains: value,
                mode: 'insensitive',
              });
            }
          });
          // const searchField = buildNestedObject(columnMapping[column].field, {
          //   // in: values.map((value) => {
          //   //   if (columnMapping[column].type === 'bigint') {
          //   //     return BigInt(value);
          //   //   } else {
          //   //     return value;
          //   //   }
          //   // }),
          // });
          where.AND.push(...searchField);
        }
      }
    }
  } else if (
    typeof search_column === 'string' &&
    typeof search_text === 'string' &&
    columnMapping[search_column]
  ) {
    let searchField;
    if (columnMapping[search_column].type === 'boolean') {
      searchField = buildNestedObject(
        columnMapping[search_column].field,
        search_text === 'true',
      );
    } else if (columnMapping[search_column].type === 'bigint') {
      searchField = buildNestedObject(
        columnMapping[search_column].field,
        BigInt(search_text),
      );
    } else if (columnMapping[search_column].type === 'enum') {
      const search = columnMapping[search_column].enums?.[search_text];

      if (!search) throw new CustomErrorException('invalid search text');

      searchField = buildNestedObject(
        columnMapping[search_column].field,
        search,
      );
    } else {
      searchField = buildNestedObject(columnMapping[search_column].field, {
        contains: search_text,
        mode: 'insensitive',
      });
    }
    where.AND = [searchField];
  }

  // Handle global search
  if (search) {
    const globalSearch = [];
    const fieldsToSearch = searchFields || Object.keys(columnMapping);
    for (const column of fieldsToSearch) {
      if (columnMapping[column] && columnMapping[column].type === 'string') {
        const searchField = buildNestedObject(columnMapping[column].field, {
          contains: search,
          mode: 'insensitive',
          // startsWith: search, // Similar to 'searchString%'
        });
        globalSearch.push(searchField);
      }
    }
    if (!where.OR) {
      where.OR = globalSearch;
    } else {
      where.OR.push(...globalSearch);
    }
  }

  // Add default where condition if deleted_at column exists
  if (hasDeletedAt) {
    where.deleted_at = null;
  }

  return { orderBy, where };
}

export function SortSearchColumnV2(
  sort_column: string | undefined,
  sort_desc: TSortOrderType | undefined,
  search_column: string | string[],
  search_text: string | string[],
  search: string,
  columnMapping: IColumnMapping,
  hasDeletedAt: boolean = false,
  searchFields?: string[], // New optional parameter
  searchMethod?: string,
): { orderBy: IOrderBy[]; where: IWhere } {
  const orderBy: IOrderBy[] = [];
  const where: IWhere = {};
  const sort =
    sort_desc && ['asc', 'desc'].includes(sort_desc) ? sort_desc : 'desc';

  searchMethod = searchMethod === 'startsWith' ? 'startsWith' : 'contains';

  // Handle sorting
  if (sort_column && columnMapping[sort_column]) {
    const orderObj = buildNestedObject(columnMapping[sort_column].field, sort);
    orderBy.push(orderObj);
  } else {
    // Default sorting by id desc
    orderBy.push({ id: 'desc' });
  }

  // Handle specific searching
  if (
    Array.isArray(search_column) &&
    Array.isArray(search_text) &&
    search_column.length === search_text.length
  ) {
    const searchConditions = {};
    search_column.forEach((column, index) => {
      if (columnMapping[column]) {
        const searchValue = search_text[index];
        if (!searchConditions[column]) {
          searchConditions[column] = [];
        }
        searchConditions[column].push(searchValue);
      }
    });

    where.AND = [];
    for (const column in searchConditions) {
      const values = searchConditions[column];
      if (columnMapping[column]) {
        if (columnMapping[column].type === 'boolean') {
          const searchField = buildNestedObject(
            columnMapping[column].field,
            values[0] === 'true',
          );
          where.AND.push(searchField);
        } else {
          const searchField = buildNestedObject(columnMapping[column].field, {
            in: values.map((value) => {
              if (columnMapping[column].type === 'bigint') {
                return BigInt(value);
              } else {
                return value;
              }
            }),
          });
          where.AND.push(searchField);
        }
      }
    }
  } else if (
    typeof search_column === 'string' &&
    typeof search_text === 'string' &&
    columnMapping[search_column]
  ) {
    let searchField;
    if (columnMapping[search_column].type === 'boolean') {
      searchField = buildNestedObject(
        columnMapping[search_column].field,
        search_text === 'true',
      );
    } else if (columnMapping[search_column].type === 'bigint') {
      searchField = buildNestedObject(
        columnMapping[search_column].field,
        BigInt(search_text),
      );
    } else if (columnMapping[search_column].type === 'enum') {
      const search = columnMapping[search_column].enums?.[search_text];

      if (!search) throw new CustomErrorException('invalid search text');

      searchField = buildNestedObject(
        columnMapping[search_column].field,
        search,
      );
    } else {
      searchField = buildNestedObject(columnMapping[search_column].field, {
        [searchMethod]: search_text,
        mode: 'insensitive',
      });
    }
    where.AND = [searchField];
  }

  // Handle global search
  if (search) {
    const globalSearch = [];
    const fieldsToSearch = searchFields || Object.keys(columnMapping);
    for (const column of fieldsToSearch) {
      if (columnMapping[column] && columnMapping[column].type === 'string') {
        const searchField = buildNestedObject(columnMapping[column].field, {
          [searchMethod]: search,
          mode: 'insensitive',
        });
        globalSearch.push(searchField);
      }
    }
    if (!where.OR) {
      where.OR = globalSearch;
    } else {
      where.OR.push(...globalSearch);
    }
  }

  // Add default where condition if deleted_at column exists
  if (hasDeletedAt) {
    where.deleted_at = null;
  }

  return { orderBy, where };
}

export function SortSearchColumnV3(
  sort_column: string | undefined,
  sort_desc: TSortOrderType | undefined,
  search_column: string | string[],
  search_text: string | string[],
  search: string,
  columnMapping: IColumnMapping,
  hasDeletedAt: boolean = false,
  searchFields?: string[],
): { orderBy: IOrderBy[]; where: IWhere } {
  const orderBy: IOrderBy[] = [];
  const where: IWhere = {};
  const sort =
    sort_desc && ['asc', 'desc'].includes(sort_desc) ? sort_desc : 'desc';

  // Handle sorting
  if (sort_column && columnMapping[sort_column]) {
    const orderObj = buildNestedObject(columnMapping[sort_column].field, sort);
    orderBy.push(orderObj);
  } else {
    // Default sorting by id desc
    orderBy.push({ id: 'desc' });
  }

  // Handle specific searching
  if (
    Array.isArray(search_column) &&
    Array.isArray(search_text) &&
    search_column.length === search_text.length
  ) {
    const searchConditions = {};
    search_column.forEach((column, index) => {
      if (columnMapping[column]) {
        const searchValue = search_text[index];
        if (!searchConditions[column]) {
          searchConditions[column] = [];
        }
        searchConditions[column].push(searchValue);
      }
    });

    where.AND = [];
    for (const column in searchConditions) {
      const values = searchConditions[column];
      const mapping = columnMapping[column];
      const field = mapping.field;

      let searchField;

      if (mapping.type === 'boolean') {
        searchField = buildNestedObject(field, values[0] === 'true');
        where.AND.push(searchField);
      } else if (mapping.type === 'bigint') {
        searchField = buildNestedObject(field, {
          in: values.map((v) => BigInt(v)),
        });
        where.AND.push(searchField);
      } else if (mapping.type === 'date') {
        for (const val of values) {
          const day = new Date(val);
          const nextDay = new Date(day);
          nextDay.setDate(day.getDate() + 1);

          const dateCondition = buildNestedObject(field, {
            gte: day,
            lt: nextDay,
          });
          where.AND.push(dateCondition);
        }
      } else if (mapping.type === 'enum') {
        for (const val of values) {
          const enumValue = mapping.enums?.[val];
          if (!enumValue) throw new CustomErrorException('invalid search text');
          searchField = buildNestedObject(field, enumValue);
          where.AND.push(searchField);
        }
      } else {
        searchField = buildNestedObject(field, {
          in: values,
        });
        where.AND.push(searchField);
      }
    }
  } else if (
    typeof search_column === 'string' &&
    typeof search_text === 'string' &&
    columnMapping[search_column]
  ) {
    const mapping = columnMapping[search_column];
    const field = mapping.field;

    let searchField;

    if (mapping.type === 'boolean') {
      searchField = buildNestedObject(field, search_text === 'true');
    } else if (mapping.type === 'bigint') {
      searchField = buildNestedObject(field, BigInt(search_text));
    } else if (mapping.type === 'date') {
      const day = new Date(search_text);
      const nextDay = new Date(day);
      nextDay.setDate(day.getDate() + 1);

      searchField = buildNestedObject(field, {
        gte: day,
        lt: nextDay,
      });
    } else if (mapping.type === 'enum') {
      const search = mapping.enums?.[search_text];
      if (!search) throw new CustomErrorException('invalid search text');
      searchField = buildNestedObject(field, search);
    } else {
      searchField = buildNestedObject(field, {
        contains: search_text,
        mode: 'insensitive',
      });
    }

    where.AND = [searchField];
  }

  // Handle global search
  if (search) {
    const globalSearch = [];
    const fieldsToSearch = searchFields || Object.keys(columnMapping);

    for (const column of fieldsToSearch) {
      if (columnMapping[column] && columnMapping[column].type === 'string') {
        const searchField = buildNestedObject(columnMapping[column].field, {
          contains: search,
          mode: 'insensitive',
        });
        globalSearch.push(searchField);
      }
    }

    if (!where.OR) {
      where.OR = globalSearch;
    } else {
      where.OR.push(...globalSearch);
    }
  }

  // Handle soft delete filter
  if (hasDeletedAt) {
    where.deleted_at = null;
  }

  return { orderBy, where };
}
