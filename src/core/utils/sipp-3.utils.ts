export function getAge(startDate, endDate) {
  let start = new Date(startDate);
  let end = new Date(endDate);

  // Calculate the differences in years, months, and days
  let years = end.getFullYear() - start.getFullYear();
  let months = end.getMonth() - start.getMonth();
  let days = end.getDate() - start.getDate();

  // Adjust for negative month or day differences
  if (days < 0) {
    months--;
    let previousMonth = new Date(
      end.getFullYear(),
      end.getMonth(),
      0,
    ).getDate(); // Days in the previous month
    days += previousMonth;
  }
  if (months < 0) {
    years--;
    months += 12;
  }

  return `${years} Tahun ${months} Bulan ${days} Hari`;
}

export const extractAge = (str) => {
  const match = str.match(/(\d+) Tahun? (\d+) Bulan? (\d+) Hari?/);
  return match
    ? [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])]
    : [0, 0, 0];
};

export const customSort = {
  lama_jabatan: (a, b) => {
    const [yearsA, monthsA, daysA] = extractAge(a);
    const [yearsB, monthsB, daysB] = extractAge(b);

    return yearsA - yearsB || monthsA - monthsB || daysA - daysB;
  },
};

export function multiSortByKey(array, columns, use_custom_sort = []) {
  return array.sort((a, b) => {
    for (let { key, order } of columns) {
      let result = 0;

      if (use_custom_sort.includes(key))
        result = customSort[key](a[key], b[key]);
      else if (a[key] instanceof Date && b[key] instanceof Date)
        result = a[key].getTime() - b[key].getTime();
      else if (typeof a[key] === 'string')
        result = a[key].localeCompare(b[key]);
      else if (typeof a[key] === 'number') result = a[key] - b[key];

      if (result !== 0) return order === 'desc' ? -result : result;
    }
    return 0;
  });
}

export function deepMerge(obj1, obj2) {
  return Object.keys(obj2).reduce(
    (acc, key) => {
      acc[key] =
        obj1[key] &&
        typeof obj1[key] === 'object' &&
        typeof obj2[key] === 'object'
          ? deepMerge(obj1[key], obj2[key])
          : (obj2[key] ?? obj1[key]);
      return acc;
    },
    { ...obj1 },
  );
}

/**
 * Auto-adjusts column widths in a worksheet based on cell content.
 * Optionally caps the maximum width to prevent overly wide columns.
 *
 * @param {Worksheet} ws - The ExcelJS worksheet object.
 * @param {number} maxWidthCap - Optional. The maximum width allowed per column.
 */

export function autofitExcelJsColumns(ws, maxWidthCap) {
  for (let col = 1; col <= ws.columnCount; col++) {
    const column = ws.getColumn(col);
    let maxWidth = 10;

    column.eachCell({ includeEmpty: false }, cell => {
      if (cell.isMerged || !cell.value) return;

      // Extract plain text from the cell, handling both normal and richText formats
      let text = typeof cell.value === 'object' ?  (cell.value.richText ? cell.value.richText.map(rt => rt.text).join('') : '') : cell.value.toString();

      // Measure each line of text (for wrapped cells)
      for (let line of text.split(/[\n\r]+/)) {
        let width = line.length;

        // Bold text usually takes up slightly more space — adjust for that
        if (cell.font?.bold) width *= 1.08;

        maxWidth = Math.max(maxWidth, width);
      }
    });

    // Add buffer to match Excel's actual rendering: 0.71 for Excel's internal offset + 1 for visual breathing room
    // Then cap the width if it exceeds the maximum allowed
    column.width = Math.min(maxWidth + 1.71, maxWidthCap);
  }
}

