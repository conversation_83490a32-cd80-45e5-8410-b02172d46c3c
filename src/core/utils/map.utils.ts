import * as moment from 'moment';
import { InternalServerErrorException } from '@nestjs/common';

export const extractUnique = <T>(items: T[], key: keyof T): T[keyof T][] => {
  return Array.from(new Set(items.map((item) => item[key])));
};

export const createMap = <T>(items: T[], key: keyof T): Record<string, T> => {
  return items.reduce(
    (map, item) => {
      map[item[key] as unknown as string] = item;
      return map;
    },
    {} as Record<string, T>,
  );
};

export const mapUserRoles = (roles: any[]): Record<string, any> => {
  return roles.reduce(
    (map, role) => {
      const roleName =
        role.role?.level?.nama?.toLowerCase() ||
        role.level?.nama?.toLowerCase();
      if (roleName) {
        map[roleName] = role;
      }
      return map;
    },
    {} as Record<string, any>,
  );
};

export const hasExcludedKeys = (
  data: Record<string, any>,
  searchKey: any[],
): boolean => {
  return Object.keys(data).some((key) => !searchKey.includes(key));
};

export const createMapHubunganKeluarga = (data: any[], nameService: string) => {
  if (data.length === 0) return {};

  return data.reduce((map, item) => {
    const hubunganId = Number(item.hubungan_keluarga_id);
    const nameKey = getHubunganKey(hubunganId, nameService);

    const keluargaColumnMap = {
      getDetailV2: item,
      createCeraiRujuk: createKeluarga(item, false),
    };

    const keluarga =
      map[nameKey] || (map[nameKey] = keluargaColumnMap[nameService]);

    if (!isNaN(Number(item?.agama_id))) {
      keluarga.agama_id = Number(item?.agama_id);
    }

    if (nameKey === 'pasangan') {
      addPasanganDetails(keluarga, item);
    }

    return map;
  }, {});
};

const getHubunganKey = (hubunganId: number, nameService: string): string => {
  const hubunganMap: Record<string, Record<string, number[]>> = {
    getDetailV2: {
      bapak: [108, 112, 110],
      ibu: [109, 113, 111],
      pasangan: [101, 116, 117, 102, 118, 119],
    },
    createCeraiRujuk: { bapak: [114], ibu: [115], pasangan: [101, 102] },
  };

  const mapping = hubunganMap[nameService];
  if (!mapping)
    throw new InternalServerErrorException(
      'terjadi kesalahan ketika memanggil function getHubunganKey',
    );

  return Object.keys(mapping).find((key) => mapping[key].includes(hubunganId));
};

const createKeluarga = (item: any, isSameColumn: boolean) => ({
  [isSameColumn ? 'nama_keluarga' : 'nama']: item?.nama_keluarga || null,
  pekerjaan_id: null,
  agama_id: Number(item?.agama_id) || null,
  alamat: item?.alamat || null,
});

const addPasanganDetails = (keluarga: any, item: any) => {
  keluarga.nik = item?.ktp_nomor || null;
  keluarga.tempat_lahir = item?.tempat_lahir?.toString() || null;
  keluarga.tanggal_lahir = moment(item?.tanggal_lahir).toISOString() || null;
  keluarga.status_kawin_id = item?.status_nikah || null;
  keluarga.email = null;
  keluarga.no_hp = item?.no_hp || null;
  keluarga.pekerjaan_keluarga = item?.pekerjaan_keluarga || null;
};
