import { HttpStatus } from '@nestjs/common';

export function buildResponse(data: any, message: string) {
  return {
    statusCode: HttpStatus.OK,
    message,
    data,
  };
}

export function buildPaginatedResponse(
  { result, page, totalData, totalPage, stage = undefined, count_status_participants = undefined }: any,
  message: string,
) {
  return {
    statusCode: HttpStatus.OK,
    message,
    data: result,
    stage,
    count_status_participants,
    totalPage,
    totalData,
    page,
  };
}
