import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class PortalPipe implements PipeTransform {
  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const object = plainToInstance(metatype, value);
    const errors = await validate(object);
    if (errors.length > 0) {
      throw new BadRequestException(this.formatErrors(errors));
    }
    return value;
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  private toValidate(metatype: Function): boolean {
    // eslint-disable-next-line @typescript-eslint/ban-types
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }

  private formatErrors(errors: ValidationError[]) {
    const result = [];
    errors.forEach((error) => {
      const err = this.findErrorConstraintObject(error);
      const errMsg = `${Object.values(err.constraints).join(', ')}`;
      let unique = `on no_urut_asalrim ${err.target.no_urut_asalrim}`;
      if (String(err.property).toLowerCase() == 'no_urut_asalrim') {
        unique = `on unique_id ${err.target.unique_id}`;
      }

      result.push(
        errMsg.replace('conforming to the specified constraints', unique),
      );
    });

    return result;
  }

  private findErrorConstraintObject(error: ValidationError) {
    function iter(e) {
      if (e.children.length == 0) {
        result = e;
        return true;
      }

      return Array.isArray(e.children) && e.children.some(iter);
    }

    let result: any;
    error.children.some(iter);
    return result;
  }
}
