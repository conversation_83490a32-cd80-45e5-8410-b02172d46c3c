import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import * as moment from 'moment-timezone';

@ValidatorConstraint({
  name: 'isNotPastDate',
  async: false,
})
export class IsNotPastDateConstraint implements ValidatorConstraintInterface {
  validate(value: string, args: ValidationArguments) {
    const today = moment().startOf('days');
    const inputDate = moment(value, 'YYYY-MM-DD', true);

    return inputDate.isValid() && inputDate.isSameOrAfter(today);
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} harus minimal hari ini atau setelahnya!`;
  }
}

export function IsNotPastDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsNotPastDateConstraint,
    });
  };
}
