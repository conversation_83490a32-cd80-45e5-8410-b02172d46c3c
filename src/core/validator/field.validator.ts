import {
  ArgumentMetadata,
  HttpStatus,
  Injectable,
  PipeTransform,
  UnprocessableEntityException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

@Injectable()
export class FieldValidatorPipe implements PipeTransform {
  async transform(value: any, { metatype, type }: ArgumentMetadata) {
    const errors = {};

    // Validate body
    if (type === 'body') {
      if (metatype && this.toValidate(metatype)) {
        value = this.parseNestedFormData(value); // ini digunakan untuk parsing dari form-data menjadi json

        const object = plainToInstance(metatype, value, {
          enableImplicitConversion: true,
        });
        const validationErrors = await validate(object, {
          whitelist: true,
          forbidNonWhitelisted: true,
          transform: true,
        });

        if (validationErrors.length > 0) {
          const formattedErrors = this.formatErrors(validationErrors);
          Object.assign(errors, formattedErrors);
        }
      }
    }

    // Validate file
    if (type === 'custom' && !value) {
      errors['file'] = 'File upload wajib diunggah.';
    }

    // If there are errors, throw an exception
    if (Object.keys(errors).length > 0) {
      throw new UnprocessableEntityException({
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        error: 'Unprocessable Entity',
        message: errors,
      });
    }

    return value;
  }

  private parseNestedFormData(data: Record<string, any>): Record<string, any> {
    const result = {};

    for (const [key, value] of Object.entries(data)) {
      const keys = key.replace(/\]/g, '').split(/\[|\./);
      keys.reduce((acc, current, index) => {
        if (!acc[current]) {
          if (index === keys.length - 1) {
            // Jika nilai kosong (""), ubah menjadi undefined
            if (value === '') {
              acc[current] = undefined;
            } else if (value === 'true' || value === 'false') {
              acc[current] = value === 'true';
            } else if (!isNaN(Number(value))) {
              if (
                current === 'fungsi_satuan_id' ||
                current === 'id_array' ||
                current === 'konsentrasi_id' ||
                current === 'pangkat_id'
              ) {
                acc[current] = [Number(value)];
              } else {
                acc[current] = Number(value);
              }
            } else if (
              typeof value === 'string' &&
              value.startsWith('[') &&
              value.endsWith(']')
            ) {
              acc[current] = JSON.parse(value);
            } else {
              acc[current] = value;
            }
          } else {
            acc[current] = isNaN(Number(keys[index + 1])) ? {} : [];
          }
        }

        return acc[current];
      }, result);
    }

    return result;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }

  formatErrors(errors: any[]) {
    const result = {};
    errors.forEach((error) => {
      const property = error.property;

      if (error.constraints != undefined) {
        result[property] = Object.values(error.constraints).join(', ');
      }

      if (error.children != undefined) {
        error.children.forEach((errChildren) => {
          const indexProperty = errChildren.property;

          if (errChildren.children != undefined) {
            errChildren.children.forEach((err) => {
              const propertyName = `${property}[${indexProperty}]`;
              result[propertyName] = Object.values(err.constraints).join(', ');
            });
          }
        });
      }
    });
    return result;
  }
}
