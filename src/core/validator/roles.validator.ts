import { BadRequestException, Injectable } from '@nestjs/common';
import { RolesRepository } from '../../access-management/roles/repository/roles.repository';
import { CreateRoleDataV4Dto } from '../../access-management/roles/dto/roles.dto';
import * as _ from 'lodash';
import { checkStringOrReturnNullValue } from '../utils/common.utils';

@Injectable()
export class RolesValidator {
  private static readonly ROLE_TIPE_ADMIN = 'admin';
  private static readonly ROLE_TIPE_OPERATOR = 'operator';

  constructor(private readonly rolesRepository: RolesRepository) {}

  async validateRoleV3(
    nama: string,
    atasanId: number,
    roleTipeId: number,
    levelId: number,
    user: any,
  ) {
    await this.checkRoleOrThrowError(nama);

    const { isAdmin, isOperator } =
      await this.validateRoleTipeOrThrowError(roleTipeId);

    const { atasan, level } = await this.checkRoleTipeRequirementOrThrowError(
      levelId,
      atasanId,
      isAdmin,
      isOperator,
      user,
      true,
      true,
    );

    return {
      isAdmin,
      isOperator,
      level,
      atasan,
    };
  }

  async validateRoleTipeOrThrowError(roleTipeId: number): Promise<{
    isAdmin: boolean;
    isOperator: boolean;
    validatedRoleTipeNama: string;
    validatedRoleTipeId: number;
  }> {
    const roleTipe = await this.checkRoleTipeOrThrowError(roleTipeId);

    const validatedRoleTipeNama = roleTipe.nama.toLowerCase();
    const isOperator =
      validatedRoleTipeNama === RolesValidator.ROLE_TIPE_OPERATOR;
    const isAdmin = validatedRoleTipeNama === RolesValidator.ROLE_TIPE_ADMIN;

    return {
      isAdmin,
      isOperator,
      validatedRoleTipeNama,
      validatedRoleTipeId: roleTipe.id,
    };
  }

  async checkRoleTipeRequirementOrThrowError(
    levelId: number,
    atasanId: number,
    isAdmin: boolean,
    isOperator: boolean,
    user: any,
    isAtasanRequired: boolean,
    isLevelRequired: boolean,
  ): Promise<{ atasan: any | null; level: any | null }> {
    if (!isOperator && isAdmin) {
      return {
        atasan: user.role_id,
        level: null,
      };
    } else {
      return {
        atasan: await this.validateRequestForAtasanId(
          isAtasanRequired,
          atasanId,
        ).then((data) => data.id),
        level: await this.validateRequestForLevelId(
          isLevelRequired,
          levelId,
        ).then((data) => data?.id ?? null),
      };
    }
  }

  async validateRequestForAtasanId(
    isAtasanRequired: boolean,
    atasanId: number,
  ) {
    if (!isAtasanRequired) {
      return null;
    }

    if (!atasanId) {
      throw new BadRequestException('Atasan id is required');
    }

    return await this.checkAtasanOrThrowError(atasanId);
  }

  async validateRequestForLevelId(isLevelRequired: boolean, levelId: number) {
    if (!isLevelRequired) {
      return null;
    }

    if (!levelId) {
      throw new BadRequestException(`Level id is required`);
    }

    return await this.checkLevelOrThrowError(levelId);
  }

  async checkRoleOrThrowError(nama: string, id?: number | undefined) {
    await this.rolesRepository.validateBadRequestEntity(
      'role',
      {
        id: { not: id },
        nama: { equals: nama, mode: 'insensitive' },
        deleted_at: null,
      },
      'Role name already exists',
    );
    return nama;
  }

  async checkModuleOrThrowError(moduleId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'modules',
      {
        id: moduleId,
        deleted_at: null,
      },
      'Module not found',
    );
  }

  async checkPortalIdLsOrThrowError(portalIdLs: number[]) {
    const data = await Promise.all(
      portalIdLs.map((portalId) => this.checkPortalOrThrowError(portalId)),
    );
    return data.map((el) => el.id);
  }

  async checkPortalOrThrowError(portalId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'portal',
      {
        id: portalId,
        deleted_at: null,
      },
      'Portal not found',
    );
  }

  async checkPermissionIdLsOrThrowError(permissionIdLs: number[]) {
    const data = await Promise.all(
      permissionIdLs.map((permissionId) =>
        this.checkPermissionOrThrowError(permissionId),
      ),
    );
    return data.map((el) => el.id);
  }

  async checkPermissionOrThrowError(permissionId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'permission',
      {
        id: permissionId,
        deleted_at: null,
      },
      'Permission not found',
    );
  }

  async checkLevelOrThrowError(levelId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'level',
      {
        id: levelId,
        deleted_at: null,
      },
      'Level not found',
    );
  }

  async checkRoleTipeOrThrowError(roleTipeId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'role_tipe',
      {
        id: roleTipeId,
        deleted_at: null,
      },
      'Role type not found',
    );
  }

  async checkAtasanOrThrowError(atasanId: number) {
    return await this.rolesRepository.validateNotFoundEntity(
      'role',
      { id: atasanId, deleted_at: null },
      'Atasan role not found',
    );
  }

  async processValidationForPortalModule(
    portalIdLs: number[],
    atasanId: number,
    moduleId: number,
    permissionIdLs: number[],
    isAdmin: boolean,
    isOperator: boolean,
    user: any,
    isAtasanRequired: boolean,
    isLevelRequired: boolean,
  ): Promise<{
    validatedAtasan: number;
    validatedModuleId: number;
    validatedPortalIdLs: number[];
    validatedLevel: number;
    validatedPermissionIdLs: number[];
  }> {
    const { atasan: validatedAtasan, level: validatedLevel } =
      await this.checkRoleTipeRequirementOrThrowError(
        null,
        atasanId,
        isAdmin,
        isOperator,
        user,
        isAtasanRequired,
        isLevelRequired,
      );

    return {
      validatedAtasan,
      validatedModuleId: await this.checkModuleOrThrowError(moduleId).then(
        (data) => data.id,
      ),
      validatedPortalIdLs: await this.checkPortalIdLsOrThrowError(portalIdLs),
      validatedLevel,
      validatedPermissionIdLs:
        await this.checkPermissionIdLsOrThrowError(permissionIdLs),
    };
  }

  async processValidationForGeneralModule(
    levelId: number,
    atasanId: number,
    moduleId: number,
    permissionIdLs: number[],
    isAdmin: boolean,
    isOperator: boolean,
    user: any,
    isAtasanRequired: boolean,
    isLevelRequired: boolean,
  ): Promise<{
    validatedAtasan: number;
    validatedModuleId: number;
    validatedPortalIdLs: number[];
    validatedLevel: number;
    validatedPermissionIdLs: number[];
  }> {
    const { atasan: validatedAtasan, level: validatedLevel } =
      await this.checkRoleTipeRequirementOrThrowError(
        levelId,
        atasanId,
        isAdmin,
        isOperator,
        user,
        isAtasanRequired,
        isLevelRequired,
      );

    return {
      validatedAtasan,
      validatedModuleId: await this.checkModuleOrThrowError(moduleId).then(
        (data) => data.id,
      ),
      validatedPortalIdLs: null,
      validatedLevel,
      validatedPermissionIdLs:
        await this.checkPermissionIdLsOrThrowError(permissionIdLs),
    };
  }

  async processValidationToCreateRole(
    atasanId: number,
    levelId: number | null,
    el: CreateRoleDataV4Dto,
    isAdmin: boolean,
    isOperator: boolean,
    user: any,
  ): Promise<{
    validatedAtasan: number;
    validatedModuleId: number;
    validatedPortalIdLs: number[];
    validatedLevel: number;
    validatedPermissionIdLs: number[];
  }> {
    const {
      module_id: moduleId,
      permission_id: permissionIdLs,
      portal_id: portalIdLs,
    } = el;

    if (this.hasPortal(portalIdLs)) {
      return await this.processValidationForPortalModule(
        portalIdLs,
        atasanId,
        moduleId,
        permissionIdLs,
        isAdmin,
        isOperator,
        user,
        true,
        false,
      );
    } else {
      return await this.processValidationForGeneralModule(
        levelId,
        atasanId,
        moduleId,
        permissionIdLs,
        isAdmin,
        isOperator,
        user,
        true,
        true,
      );
    }
  }

  async validateRoleAccess(
    isUpdate: boolean,
    nama: string,
    roleTipeId: number,
    atasanId: number,
    levelId: number | null,
    data: CreateRoleDataV4Dto[],
    user: any,
  ): Promise<{
    validatedNama: string;
    validatedRoleTipeId: number;
    isAdmin: boolean;
    isOperator: boolean;
    validatedAtasanId: number;
    validatedLevelId: number;
    validatedDataLs: {
      validatedAtasan: number;
      validatedModuleId: number;
      validatedPortalIdLs: number[];
      validatedLevel: number;
      validatedPermissionIdLs: number[];
    }[];
  }> {
    let validatedNama: string;
    if (!isUpdate) validatedNama = await this.checkRoleOrThrowError(nama);

    const { isAdmin, isOperator, validatedRoleTipeId } =
      await this.validateRoleTipeOrThrowError(roleTipeId);

    const validatedData = await Promise.all(
      data.map((el) => {
        return this.processValidationToCreateRole(
          atasanId,
          levelId,
          el,
          isAdmin,
          isOperator,
          user,
        );
      }),
    );
    const groupByValidatedAtasan = _.groupBy(validatedData, (item) =>
      String(item.validatedAtasan),
    );
    const validatedAtasanId = Object.keys(groupByValidatedAtasan)[0];

    const groupByValidatedLevel = _.groupBy(validatedData, (item) =>
      String(item.validatedLevel),
    );
    const validatedLevelId = Object.keys(groupByValidatedLevel)[0];

    return {
      validatedNama,
      validatedRoleTipeId,
      isAdmin,
      isOperator,
      validatedAtasanId: parseInt(
        checkStringOrReturnNullValue(validatedAtasanId),
      ),
      validatedLevelId: parseInt(
        checkStringOrReturnNullValue(validatedLevelId),
      ),
      validatedDataLs: groupByValidatedAtasan[validatedAtasanId] as {
        validatedAtasan: number;
        validatedModuleId: number;
        validatedPortalIdLs: number[];
        validatedLevel: number;
        validatedPermissionIdLs: number[];
      }[],
    };
  }

  hasPortal(portalIdLs: number[]) {
    return portalIdLs && portalIdLs.length > 0;
  }
}
