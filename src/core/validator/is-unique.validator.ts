import { Injectable } from '@nestjs/common';
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { IValidationArguments } from '../interfaces/common.interface';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsUniqueConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prismaService: PrismaService) {}

  async validate(value: any, args: IValidationArguments): Promise<boolean> {
    const [model, property, idField] = args.constraints;
    const entityId = args.object[idField];

    const modelDelegate = (this.prismaService as any)[model];

    if (!modelDelegate) {
      throw new Error(`Model ${model} tidak ditemukan di PrismaService`);
    }

    const record = await modelDelegate.findFirst({
      where: {
        AND: [{ [property]: value }, { id: { not: entityId } }],
      },
    });

    return !record; // Mengembalikan true jika record tidak ditemukan (artinya unik)
  }

  defaultMessage(args: IValidationArguments): string {
    const [model, property] = args.constraints;
    return `${property} must be unique in ${model}`;
  }
}
