import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import * as moment from 'moment';

@ValidatorConstraint({
  name: 'isSequentialDateRange',
  async: false,
})
@Injectable()
export class IsSequentialDateRange implements ValidatorConstraintInterface {
  validate(data: any[], args: ValidationArguments) {
    for (let i = 0; i < data?.length; i++) {
      const startDate = moment(data[i].start_date, 'YYYY-MM-DD', true);
      const endDate = moment(data[i].end_date, 'YYYY-MM-DD', true);

      if (!startDate.isValid() || !endDate.isValid()) return false;

      if (!endDate.isAfter(startDate)) return false;

      if (i > 0) {
        const prevEndDate = moment(data[i - 1].end_date, 'YYYY-MM-DD', true);
        if (!startDate.isAfter(prevEndDate)) return false;
      }
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Setiap tanggal selesai harus lebih besar dari tanggal mulai, dan tanggal mulai harus lebih besar dari tanggal selesai sebelumnya!';
  }
}
