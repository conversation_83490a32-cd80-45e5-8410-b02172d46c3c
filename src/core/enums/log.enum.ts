export enum ConstantLogModuleEnum {
  AGAMA_MODULE = 'AGAMA',
  API_MODULE = 'API',
  API_GENERATOR_MODULE = 'API GENERATOR',
  API_KEY_MODULE = 'API KEY',
  ASSESSMENT_MODULE = 'ASSESSMENT',
  AUTHENTICATION_MODULE = 'AUTHENTICATION',
  BAGASSUS_MODULE = 'BAGASSUS',
  BAGIAN_MODULE = 'BA<PERSON>AN',
  BAGSIPERS_MODULE = 'BAGSIPERS',
  BAHASA_MODULE = 'BAHASA',
  BEASISWA_DIKUM_DINAS_MODULE = 'BEASISWA DIKUM DINAS',
  BERITA_MODULE = 'BERITA',
  DIKBANGSPES_LOKASI_MODULE = 'DIKBANGSPES LOKASI',
  DIKBANGSPES_TINGKAT_MODULE = 'DIKBANGSPES TINGKAT',
  DIKBANGSPES_MODULE = 'DIKBANGSPES_MODULE',
  DIKBANGUM_MODULE = 'DIKBANGUM_MODULE',
  DIKBANGUM_KATEGORI_MODULE = 'DIKBANGUM KATEGORI',
  DIKTUK_MODULE = 'DIKTUK',
  DIKTUK_KATEGORI_MODULE = 'DIKTUK KATEGORI',
  DIKUM_MODULE = 'DIKUM',
  MDM_DUPLIKASI_JABATAN_MODULE = 'MDM DUPLIKASI JABATAN',
  MDM_DUPLIKASI_SATUAN_MODULE = 'MDM DUPLIKASI SATUAN',
  E_KANDIDAT_MODULE = 'E KANDIDAT',
  EKTA_BATCH_MODULE = 'E KTA BATCH',
  EKTA_V1_MODULE = 'E KTA V1',
  EKTA_V2_MODULE = 'E KTA V2',
  E_ROHANI_MODULE = 'E-ROHANI',
  ESELON_MODULE = 'ESELON',
  EXCEL_MODULE = 'EXCEL',
  GENERATE_NRP_MODULE = 'GENERATE NRP',
  GOLONGAN_MODULE = 'GOLONGAN',
  JABATAN_MODULE = 'JABATAN',
  JABATAN_KATEGORI_MODULE = 'JABATAN KATEGORI',
  JASMANI_PERSONEL_MODULE = 'JASMANI PERSONEL',
  JURUSAN_MODULE = 'JURUSAN',
  KABUPATEN_MODULE = 'KABUPATEN',
  KEMAMPUAN_BAHASA_MODULE = 'KEMAMPUAN BAHASA',
  KENAIKAN_GAJI_BERKALA_MODULE = 'KENAIKAN GAJI BERKALA',
  KENAIKAN_PANGKAT_MODULE = 'KENAIKAN PANGKAT',
  KERJASAMA_MODULE = 'KERJASAMA',
  KHIRDIN_MODULE = 'KHIRDIN',
  LEVEL_MODULE = 'LEVEL',
  LOGS_ACTIVITY_MODULE = 'LOGS ACTIVITY',
  MISI_MODULE = 'MISI',
  MUTASI_JABATAN_MODULE = 'MUTASI JABATAN',
  NCR_MODULE = 'NCR',
  NIVELLERING_MODULE = 'NIVELLERING',
  NOTIFIKASI_MODULE = 'NOTIFIKASI',
  PANGKAT_MODULE = 'PANGKAT',
  PANGKAT_KATEGORI_MODULE = 'PANGKAT KATEGORI',
  PEKERJAAN_MODULE = 'PEKERJAAN',
  PELATIHAN_MODULE = 'PELATIHAN',
  PELATIHAN_KATEGORI_MODULE = 'PELATIHAN KATEGORI',
  PENGAJUAN_CUTI_MODULE = 'PENGAJUAN CUTI',
  PENGAJUAN_PENGAKHIRAN_DINAS_MODULE = 'PENGAJUAN PENGAKHIRAN DINAS',
  PENGAJUAN_PENGAKHIRAN_DINAS_BUP_MODULE = 'PENGAJUAN PENGAKHIRAN DINAS BUP',
  PENGAJUAN_PENGHARGAAN_MODULE = 'PENGAJUAN PENGHARGAAN',
  PENGAJUAN_TANHOR_MODULE = 'PENGAJUAN TANHOR',
  PENGHARGAAN_MODULE = 'PENGHARGAAN',
  PENUGASAN_LUAR_STRUKTUR_MODULE = 'PENUGASAN LUAR STRUKTUR',
  PERATURAN_POLRI_MODULE = 'PERATURAN POLRI',
  PERMISSION_MODULE = 'PERMISSION',
  PERSONEL_MODULE = 'PERSONEL',
  EPATMA_MODULE = 'E PATMA',
  EPATMA_SISWA_MODULE = 'E PATMA SISWA',
  EPATMA_PENGOLAHAN_DATA_DOKUMEN_MODULE = 'E PATMA PENGOLAHAN DATA DOKUMEN',
  EPATMA_DISTRIBUSI_DATA_DOKUMEN_MODULE = 'E PATMA DISTRIBUSI DATA PENDIDIKAN',
  EPATMA_DATA_PENDIDIKAN_PEMBENTUKAN_MODULE = 'E PATMA DATA PENDIDIKAN PEMBENTUKAN',
  EPATMA_DATA_KELULUSAN_REKRUTMEN_MODULE = 'E PATMA DATA KELULUSAN REKRUTMEN',
  PRESTASI_OLAHRAGA_PERSONEL_MODULE = 'PRESTASI OLAHRAGA PERSONEL',
  PROMOSI_JABATAN_MODULE = 'PROMOSI JABATAN',
  PSIKOLOGI_E_MENTAL_MODULE = 'PSIKOLOGI E MENTAL',
  PSIKOLOGI_E_KONSELING_MODULE = 'PSIKOLOGI E KONSELING',
  REKRUTMEN_BAGRIMDIK_MODULE = 'REKRUTMEN BAGRIMDIK',
  RIWAYAT_LENGKAP_MODULE = 'RIWAYAT LENGKAP',
  ROLES_MODULE = 'ROLES',
  ROLE_TIPE_MODULE = 'ROLE TIPE',
  SATUAN_MODULE = 'SATUAN',
  SATUAN_JABATAN_MODULE = 'SATUAN JABATAN',
  SATUAN_JENIS_MODULE = 'SATUAN JENIS',
  SELEKSI_BAGASSUS_MODULE = 'SELEKSI BAGASSUS',
  SELEKSI_BAGLEKDIK_MODULE = 'SELEKSI BAGLEKDIK',
  SELEKSI_BAGRIMDIK_MODULE = 'SELEKSI BAGRIMDIK',
  SELEKSI_BAGRIMDIK_PNS_MODULE = 'SELEKSI BAGRIMDIK PNS',
  SERTIFIKAT_MODULE = 'SERTIFIKAT',
  SIPK_MODULE = 'SIPK',
  SIPP3_MODULE = 'SIPP 3',
  SIPP_REKAP_MODULE = 'SIPP REKAP',
  STATUS_KAWIN_MODULE = 'STATUS KAWIN',
  SUKU_MODULE = 'SUKU',
  SURVEY_MODULE = 'SURVEY',
  TANHOR_MODULE = 'TANHOR',
  TINGKAT_PENGHARGAAN_MODULE = 'TINGKAT PENGHARGAAN',
  USERS_V1_MODULE = 'USERS V1',
  USERS_V2_MODULE = 'USERS V2',
  USERS_V3_MODULE = 'USERS V3',
  WORKFLOW_MODULE = 'WORKFLOW',
}

export enum ConstantLogTypeEnum {
  CREATE_LOG_TYPE = 'MENAMBAH DATA',
  READ_LOG_TYPE = 'MELIHAT DATA',
  LOGIN_LOG_TYPE = 'MASUK APLIKASI',
  RESEND_OTP_PASSWORD_LOG_TYPE = 'MENGIRIM ULANG DATA OTP',
  RESET_PASSWORD_LOG_TYPE = 'MENGATUR ULANG DATA PASSWORD',
  VERIFY_OTP_LOG_TYPE = 'VERIFIKASI DATA OTP',
  ADD_DEVICE_TOKEN = 'ADD DEVICE TOKEN',
  LOGOUT_LOG_TYPE = 'KELUAR APLIKASI',
  UPDATE_LOG_TYPE = 'MENUKAR DATA',
  UPSERT_LOG_TYPE = 'MENUKAR/MENAMBAH DATA',
  UPLOAD_LOG_TYPE = 'UPLOAD DATA',
  DELETE_LOG_TYPE = 'MENGHAPUS DATA',
  WRITE_EXCEL_LOG_TYPE = 'MENULIS DATA EXCEL',
  READ_EXCEL_LOG_TYPE = 'MELIHAT DATA EXCEL',
  WRITE_PDF_LOG_TYPE = 'MENULIS DATA PDF',
  READ_MINIO_LOG_TYPE = 'MELIHAT DATA MINIO',
  WRITE_MINIO_LOG_TYPE = 'MENAMBAH DATA MINIO',
}

export enum ConstantLogStatusEnum {
  SUCCESS = 'BERHASIL',
  ERROR = 'TIDAK BERHASIL',
}

export enum ConstantLogDataTypeEnum {
  OBJECT = 'OBJECT',
  LIST = 'LIST',
}
