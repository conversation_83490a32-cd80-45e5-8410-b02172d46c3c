import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { KonselingOperatorService } from '../service/konseling.operator.service';
import { KonselingKonselorService } from '../service/konseling.konselor.service';
import { KonselingPersonelService } from '../service/konseling.personel.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import {
  CreateHasilTest,
  CreateKonselingKonselor,
  CreateKonselingPersonel,
} from '../dto/konseling.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { KonselingService } from '../service/konseling.service';
import { Module, Permission } from '../../../core/decorators';
import { MODULES } from '../../../core/constants/module.constant';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('konseling')
@Module(MODULES.E_COUNSELING)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class KonselingController {
  private readonly logger = new Logger(KonselingController.name);

  constructor(
    private readonly konselingService: KonselingService,
    private readonly konselingOperatorService: KonselingOperatorService,
    private readonly konselingKonselorService: KonselingKonselorService,
    private readonly konselingPersonelService: KonselingPersonelService,
  ) {}

  //SECTION GENERAL
  @Get('/general/jenis-konsultasi')
  @Permission('PERMISSION_READ')
  async getJenisKonsultasi(@Req() req: any) {
    this.logger.log(`Entering ${this.getJenisKonsultasi.name}`);
    const response = await this.konselingService.getJenisKonsultasi(req);
    this.logger.log(
      `Leaving ${this.getJenisKonsultasi.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/general/status-konselor')
  @Permission('PERMISSION_READ')
  async getStatusPersonel(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatusPersonel.name}`);
    const response = await this.konselingService.getStatusPersonel(req);
    this.logger.log(
      `Leaving ${this.getStatusPersonel.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  //END OF SECTION GENERAL

  //SECTION PERSONEL

  @Post('/personel')
  async createPengajuanKonseling(
    @Req() req: any,
    @Body() body: CreateKonselingPersonel,
  ) {
    this.logger.log(
      `Entering ${this.createPengajuanKonseling.name} with body: ${JSON.stringify(body)}`,
    );
    const response = await this.konselingPersonelService.create(req, body);
    this.logger.log(
      `Leaving ${this.createPengajuanKonseling.name} with body: ${JSON.stringify(body)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/status')
  async getStatus(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatus.name}`);
    const response = await this.konselingPersonelService.getStatus(req);
    this.logger.log(
      `Leaving ${this.getStatus.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel')
  async getRiwayat(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getRiwayat.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.konselingPersonelService.getRiwayat(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getRiwayat.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/personel/hasil/:id')
  async getHasil(@Req() req: any, @Param('id') konsultasi_id: string) {
    this.logger.log(
      `Entering ${this.getHasil.name} with konsultasi id: ${konsultasi_id}`,
    );
    const response = await this.konselingPersonelService.getHasilKonsultasi(
      req,
      Number(konsultasi_id),
    );

    this.logger.log(
      `Leaving ${this.getHasil.name} with konsultasi id: ${konsultasi_id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  //END OF SECTION PERSONEL

  //SECTION KONSELOR
  @Get('/konselor')
  @Permission('PERMISSION_READ')
  async getKonsultasiListKonselor(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getKonsultasiListKonselor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.konselingKonselorService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getKonsultasiListKonselor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/konselor/riwayat')
  @Permission('PERMISSION_READ')
  async getKonsultasiRiwayatKonselor(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getKonsultasiRiwayatKonselor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.konselingKonselorService.getRiwayat(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getKonsultasiRiwayatKonselor.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/konselor/detail/:id')
  @Permission('PERMISSION_READ')
  async getKonsultasiKonselor(@Req() req: any, @Param('id') id: string) {
    this.logger.log(
      `Entering ${this.getKonsultasiKonselor.name} with id: ${id}`,
    );
    const response = await this.konselingKonselorService.getDetail(req, +id);
    this.logger.log(
      `Leaving ${this.getKonsultasiKonselor.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/konselor/hasil')
  @Permission('PERMISSION_READ')
  @UseInterceptors(
    FileFieldsInterceptor([
      {
        name: 'file',
        maxCount: 3,
      },
    ]),
  )
  async createKonsultasiHasilKonselor(
    @Req() req: any,
    @Body() createHasilTest: CreateHasilTest,
    @UploadedFiles()
    files: { [key: string]: Express.Multer.File[] },
  ) {
    this.logger.log(
      `Entering ${this.createKonsultasiHasilKonselor.name} with body: ${JSON.stringify(createHasilTest)}`,
    );
    const response = await this.konselingKonselorService.createHasil(
      req,
      createHasilTest,
      files,
    );
    this.logger.log(
      `Leaving ${this.createKonsultasiHasilKonselor.name} with body: ${JSON.stringify(createHasilTest)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Post('/konselor/konsultasi')
  @Permission('PERMISSION_CREATE')
  async createKonselingKonselor(
    @Req() req: any,
    @Body() data: CreateKonselingKonselor,
  ) {
    this.logger.log(
      `Entering ${this.createKonselingKonselor.name} with body: ${JSON.stringify(data)}`,
    );
    const response =
      await this.konselingKonselorService.createKonselingKonselor(req, data);
    this.logger.log(
      `Leaving ${this.createKonselingKonselor.name} with body: ${JSON.stringify(data)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/konselor/start-chat/:konsultasi_id')
  @Permission('PERMISSION_CREATE')
  async startChatKonselor(
    @Req() req: any,
    @Param('konsultasi_id') konsultasi_id: string,
  ) {
    this.logger.log(
      `Entering ${this.startChatKonselor.name} with konsultasi id: ${konsultasi_id}`,
    );
    const response = await this.konselingKonselorService.startChat(
      req,
      +konsultasi_id,
    );

    this.logger.log(
      `Leaving ${this.startChatKonselor.name} with konsultasi id: ${konsultasi_id} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/konselor/akhiri-chat/:chat_id')
  @Permission('PERMISSION_CREATE')
  async akhiriChatKonselor(@Req() req: any, @Param('chat_id') chat_id: string) {
    this.logger.log(
      `Entering ${this.akhiriChatKonselor.name} with chat id: ${chat_id}`,
    );
    const response = await this.konselingKonselorService.akhiriChat(
      req,
      chat_id,
    );
    this.logger.log(
      `Leaving ${this.akhiriChatKonselor.name} with chat id: ${chat_id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/konselor/list-chat')
  @Permission('PERMISSION_READ')
  async listChatKonselor(@Req() req: any, @Query('search') search: string) {
    this.logger.log(
      `Entering ${this.listChatKonselor.name} with search: ${search}`,
    );
    const response = await this.konselingKonselorService.listChat(req, search);
    this.logger.log(
      `Leaving ${this.listChatKonselor.name} with search: ${search} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  //END OF SECTION KONSELOR

  //SECTION OPERATOR
  @Get('/operator')
  @Permission('PERMISSION_READ')
  async getKonsultasiListOperator(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getKonsultasiListOperator.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.konselingOperatorService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getKonsultasiListOperator.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/operator/riwayat')
  @Permission('PERMISSION_READ')
  async getKonsultasiRiwayatOperator(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getKonsultasiRiwayatOperator.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.konselingOperatorService.getRiwayat(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getKonsultasiRiwayatOperator.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/operator/detail/:id')
  @Permission('PERMISSION_READ')
  async getKonsultasiOperator(@Req() req: any, @Param('id') id: string) {
    this.logger.log(
      `Entering ${this.getKonsultasiOperator.name} with id: ${id}`,
    );
    const response = await this.konselingOperatorService.get(req, +id);
    this.logger.log(
      `Leaving ${this.getKonsultasiOperator.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/operator/rekap')
  @Permission('PERMISSION_READ')
  async getKonsultasiRekapOperator(
    @Req() req: any,
    @Query('polda_id') polda_id: string,
    @Query('tahun') tahun: string,
  ) {
    this.logger.log(
      `Entering ${this.getKonsultasiRekapOperator.name} with polda id: ${polda_id} and tahun: ${tahun}`,
    );
    const response = await this.konselingOperatorService.getRekapKonsultasi(
      req,
      +polda_id,
      +tahun,
    );
    this.logger.log(
      `Leaving ${this.getKonsultasiRekapOperator.name} with polda id: ${polda_id} and tahun: ${tahun} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  //END OF SECTION OPERATOR
}
