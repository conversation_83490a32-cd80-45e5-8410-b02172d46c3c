import { IsNotEmpty, <PERSON>N<PERSON><PERSON>, IsString } from 'class-validator';

export enum StatusKonseling {
  PENGAJUAN = 'PENGAJUAN',
  SEDANG_BERLANGSUNG = 'SEDANG_BERLANGSUNG',
  MENUNGGU_HASIL = 'MENUNGGU_HASIL',
  SELESAI = 'SELESAI',
}

export class CreateKonselingPersonel {
  @IsNumber()
  @IsNotEmpty()
  konsultasi_jenis_id: number;

  @IsString()
  keluhan: string;
}

export class CreateKonselingKonselor {
  @IsNumber()
  konsultasi_id: number;

  @IsString()
  chat_id: string;
}

export class CreateHasilTest {
  @IsString()
  konsultasi_id: string;

  @IsString()
  is_tindakan: string;

  @IsString()
  no_hp: string;

  @IsString()
  catatan: string;
}
