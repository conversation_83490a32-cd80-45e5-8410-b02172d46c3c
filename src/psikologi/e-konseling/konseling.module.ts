import { forwardRef, Module } from '@nestjs/common';
import { KonselingOperatorService } from './service/konseling.operator.service';
import { KonselingController } from './controller/konseling.controller';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { KonselingKonselorService } from './service/konseling.konselor.service';
import { KonselingPersonelService } from './service/konseling.personel.service';
import { KonselingService } from './service/konseling.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { UsersModule } from '../../access-management/users/users.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KonselingController],
  providers: [
    KonselingService,
    KonselingOperatorService,
    KonselingKonselorService,
    KonselingPersonelService,
    MinioService,
  ],
})
export class KonselingModule {}
