import { ForbiddenException, HttpStatus, Injectable } from '@nestjs/common';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { StatusKonseling } from '../dto/konseling.dto';
import { IColumnMapping } from 'src/core/interfaces/db.interface';
import { status_konsultasi_enum } from '@prisma/client';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from 'src/core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class KonselingOperatorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(req, paginationData, searchandsortData) {
    try {
      const user = req.user;
      if (
        user.role_tipe != null &&
        !['admin', 'operator'].includes(user.role_tipe?.toLowerCase())
      )
        throw new ForbiddenException('tidak memiliki akses');

      let polda_id = null;
      if (user.role_tipe?.toLowerCase() === 'operator') {
        polda_id = await this.getSatuanPolda(req.user.satuan_id);
      }
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        status: { field: 'status', type: 'enum', enums: StatusKonseling },
        nama: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      where.OR = [
        {
          status: status_konsultasi_enum.PENGAJUAN,
        },
        {
          status: status_konsultasi_enum.SEDANG_BERLANGSUNG,
        },
        {
          status: status_konsultasi_enum.MENUNGGU_HASIL,
        },
      ];

      if (polda_id) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            polda_id,
          };
        });
      }

      if (search) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            personel: {
              OR: [
                {
                  nama_lengkap: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
                {
                  nrp: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          };
        });
      }

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi.count({
          where: where,
        }),
        this.prisma.konsultasi.findMany({
          select: {
            id: true,
            created_at: true,
            status: true,
            konselor: {
              select: {
                id: true,
                personel: {
                  select: {
                    nama_lengkap: true,
                  },
                },
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
              },
            },
            konsultasi_jenis: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getRiwayat(req, paginationData, searchandsortData) {
    try {
      const user = req.user;
      if (
        user.role_tipe != null &&
        !['admin', 'operator'].includes(user.role_tipe?.toLowerCase())
      )
        throw new ForbiddenException('tidak memiliki akses');

      let polda_id = null;
      if (user.role_tipe?.toLowerCase() === 'operator') {
        polda_id = await this.getSatuanPolda(req.user.satuan_id);
      }
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        status: { field: 'status', type: 'enum', enums: StatusKonseling },
        nama: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      where.OR = [
        {
          status: status_konsultasi_enum.SELESAI,
        },
      ];

      if (polda_id) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            polda_id,
          };
        });
      }

      if (search) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            personel: {
              OR: [
                {
                  nama_lengkap: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
                {
                  nrp: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          };
        });
      }

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi.count({
          where: where,
        }),
        this.prisma.konsultasi.findMany({
          select: {
            id: true,
            created_at: true,
            status: true,
            konselor: {
              select: {
                id: true,
                personel: {
                  select: {
                    nama_lengkap: true,
                  },
                },
              },
            },
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
              },
            },
            konsultasi_jenis: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async get(req, id: number) {
    try {
      const user = req.user;
      if (
        user.role_tipe != null &&
        !['admin', 'operator'].includes(user.role_tipe?.toLowerCase())
      )
        throw new ForbiddenException('tidak memiliki akses');

      const queryResult = await this.prisma.konsultasi.findFirst({
        where: {
          id: id,
          deleted_at: null,
        },
        select: {
          id: true,
          status: true,
          keluhan: true,
          chat_id: true,
          created_at: true,
          personel: {
            select: {
              nama_lengkap: true,
              ktp_nomor: true,
              tempat_lahir: true,
              tanggal_lahir: true,
              nrp: true,
              email: true,
              no_hp: true,
              alamat: true,
              agama: {
                select: {
                  nama: true,
                },
              },
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      id: true,
                      nama: true,
                      satuan: {
                        select: {
                          id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          konselor: {
            select: {
              personel: {
                select: {
                  nama_lengkap: true,
                },
              },
            },
          },
          konsultasi_file: {
            select: {
              id: true,
              originalname: true,
              url: true,
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getRekapKonsultasi(req, polda_id: number, tahun: number) {
    try {
      const user = req.user;
      if (
        user.role_tipe != null &&
        !['admin', 'operator'].includes(user.role_tipe?.toLowerCase())
      )
        throw new ForbiddenException('tidak memiliki akses');

      const where = {};

      if (Number(polda_id)) {
        where['konsultasi'] = {
          some: {
            polda_id: polda_id,
          },
        };
      }

      if (Number(tahun)) {
        where['created_at'] = {
          gte: new Date(`${Number(tahun)}-01-01T00:00:00.000Z`),
          lt: new Date(`${Number(tahun) + 1}-01-01T00:00:00.000Z`),
        };
      }

      const [rekapKonsultasi, konsultasiJenis] = await this.prisma.$transaction(
        [
          this.prisma.konsultasi_jenis.findMany({
            select: {
              id: true,
              nama: true,
              _count: {
                select: {
                  konsultasi: true,
                },
              },
            },
            where,
          }),
          this.prisma.konsultasi_jenis.findMany({
            select: {
              id: true,
              nama: true,
            },
          }),
        ],
      );

      const queryResult = konsultasiJenis.map((item) => {
        const rekapKonsul = rekapKonsultasi.find(
          (rekap) => rekap.id === item.id,
        );

        return {
          nama: item.nama,
          _count: {
            konsultasi: rekapKonsul?._count?.konsultasi || 0,
          },
        };
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  private async getSatuanPolda(satuan_id: number) {
    const satuan = await this.prisma.satuan.findFirst({
      where: {
        id: satuan_id,
        deleted_at: null,
      },
    });

    if (
      satuan.nama.toLowerCase().split(' ')[0].includes('polda') ||
      satuan.atasan_id == null
    ) {
      return satuan.id;
    }

    return await this.getSatuanPolda(Number(satuan.atasan_id));
  }
}
