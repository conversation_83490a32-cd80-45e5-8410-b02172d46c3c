import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateKonselingPersonel, StatusKonseling } from '../dto/konseling.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { Prisma } from '@prisma/client';
import { FirebaseEnum } from '../../../core/enums/firebase.enum';
import { ISendNotificationMultiple } from '../../../core/interfaces/firebase.interface';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class KonselingPersonelService {
  private readonly logger = new Logger(KonselingPersonelService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly feUrl = this.configService.get<string>('FE_URL');

  async create(req, body: CreateKonselingPersonel) {
    try {
      const user = req['user'];
      if (!user.satuan_id)
        throw new ForbiddenException(
          'User tidak bisa melakukan konsultasi, di karenakan satuan_id tidak ada',
        );

      const konsultasi = await this.prisma.konsultasi.findFirst({
        where: {
          personel_id: user.personel_id,
          status: {
            in: [
              StatusKonseling.PENGAJUAN,
              StatusKonseling.SEDANG_BERLANGSUNG,
              StatusKonseling.MENUNGGU_HASIL,
            ],
          },
          deleted_at: null,
        },
      });

      if (konsultasi)
        throw new BadRequestException('Konsultasi sedang berlangsung!');

      const { keluhan, konsultasi_jenis_id } = body;
      const polda_id = await this.getSatuanPolda(user.satuan_id);

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const addKonsultasi = await tx.konsultasi.create({
          data: {
            personel_id: user.personel_id,
            status: StatusKonseling.PENGAJUAN,
            konsultasi_jenis_id: konsultasi_jenis_id,
            keluhan: keluhan,
            polda_id: polda_id,
          },
        });
        return addKonsultasi;
      });

      const target = await this.prisma.$queryRaw<any>(Prisma.sql`
             SELECT DISTINCT(udt.id) AS id, udt.user_id, udt.token
             FROM users_device_token udt
                      JOIN users u ON u.id = udt.user_id
                      JOIN jabatan_personel jp ON jp.personel_id = udt.user_id
                      JOIN jabatan j ON j.id = jp.jabatan_id
             WHERE j.satuan_id = ${polda_id}
         `);

      const mock: ISendNotificationMultiple = {
        tokens: target,
        type: 'KONSELING',
        title: 'Pengajuan Konseling Baru',
        body: 'Terdapat personel yang ingin berkonsultasi',
        data: { link: `${this.feUrl}/psikologi/konseling` },
      };
      this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.CREATED,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getStatus(req: any) {
    try {
      const queryResult = await this.prisma.konsultasi.findFirst({
        where: {
          ...(req.query.konsultasi_id
            ? { id: Number(req.query.konsultasi_id) }
            : { personel_id: req.user.personel_id }),
          status: {
            in: [
              StatusKonseling.PENGAJUAN,
              StatusKonseling.SEDANG_BERLANGSUNG,
              StatusKonseling.MENUNGGU_HASIL,
            ],
          },
          deleted_at: null,
        },
        include: {
          personel: true,
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getRiwayat(req: any, paginationData, searchandsortData) {
    try {
      const user = req.user;
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'enum' };
      } = {
        status: { field: 'status', type: 'enum' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      where.personel_id = user.personel_id;

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi.count({
          where: where,
        }),
        this.prisma.konsultasi.findMany({
          select: {
            id: true,
            chat_id: true,
            created_at: true,
            status: true,
            personel: {
              select: {
                nama_lengkap: true,
              },
            },
            konsultasi_jenis: {
              select: {
                id: true,
                nama: true,
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getHasilKonsultasi(req: any, konsultasi_id: number) {
    try {
      const user = req.user;
      const getHasil = await this.prisma.konsultasi.findFirst({
        where: {
          id: konsultasi_id,
          personel_id: user.personel_id,
          deleted_at: null,
        },
        select: {
          catatan: true,
          is_tindakan: true,
          no_hp_tindakan: true,
          status: true,
          personel: {
            select: {
              nama_lengkap: true,
            },
          },
          konsultasi_file: {
            select: {
              url: true,
            },
          },
        },
      });

      if (
        getHasil.status.toLowerCase() !== StatusKonseling.SELESAI.toLowerCase()
      )
        throw new ForbiddenException('Konsultasi belum selesai!');

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          getHasil,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: getHasil,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  private async getSatuanPolda(satuan_id: number) {
    const satuan = await this.prisma.satuan.findFirst({
      where: {
        id: satuan_id,
        deleted_at: null,
      },
    });

    if (
      satuan.nama.toLowerCase().split(' ')[0].includes('polda') ||
      satuan.atasan_id == null
    ) {
      return satuan.id;
    }

    return await this.getSatuanPolda(Number(satuan.atasan_id));
  }
}
