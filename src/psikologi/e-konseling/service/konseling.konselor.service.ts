import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  CreateHasilTest,
  CreateKonselingKonselor,
  StatusKonseling,
} from '../dto/konseling.dto';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { status_konsultasi_enum } from '@prisma/client';
import { IColumnMapping } from 'src/core/interfaces/db.interface';
import { FirebaseEnum } from '../../../core/enums/firebase.enum';
import { ISendNotificationMultiple } from '../../../core/interfaces/firebase.interface';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';

@Injectable()
export class KonselingKonselorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  private readonly feUrl = this.configService.get<string>('FE_URL');

  async createHasil(req, data: CreateHasilTest, files: any) {
    try {
      const user = req.user;
      const { catatan, is_tindakan, konsultasi_id, no_hp } = data;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });

      const konsultasi = await this.prisma.konsultasi.findFirst({
        where: {
          id: +konsultasi_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses');
      if (!konsultasi)
        throw new BadRequestException('Konsultasi tidak ditemukan!');
      if (
        konsultasi.status.toLowerCase() !==
        StatusKonseling.MENUNGGU_HASIL.toLowerCase()
      )
        throw new ForbiddenException(
          'Konsultasi belum berlangsung atau sudah selesai!',
        );
      if (!konsultasi_id && Number(konsultasi_id))
        throw new BadRequestException('Konsultasi id tidak boleh kosong');
      if (!['true', 'false'].includes(is_tindakan))
        throw new BadRequestException('Is tindakan invalid');
      if (is_tindakan === 'true') {
        if (!no_hp) {
          throw new BadRequestException('No hp tidak boleh kosong');
        }
      }
      if (!files.file.length)
        throw new BadRequestException('File tidak boleh kosong!');

      const queryResult = await this.prisma.$transaction(async (tx) => {
        const uplFiles = [];
        for (const i in files.file) {
          const dokumen = files.file[i];
          const upload = await this.minioService.uploadFile(dokumen);
          delete dokumen.buffer;
          delete dokumen.fieldname;
          uplFiles.push({
            ...dokumen,
            key: upload.Key,
            url: upload.Location,
            filename: upload.filename,
          });
        }

        const updKonsultasi = await tx.konsultasi.update({
          where: {
            id: konsultasi.id,
          },
          data: {
            is_tindakan: is_tindakan == 'true' && true,
            no_hp_tindakan: is_tindakan == 'true' ? no_hp : null,
            catatan: catatan,
            status: StatusKonseling.SELESAI,
            konsultasi_file: uplFiles.length
              ? {
                  create: uplFiles,
                }
              : {},
          },
        });

        return updKonsultasi;
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createKonselingKonselor(req, data: CreateKonselingKonselor) {
    try {
      const user = req.user;
      const { chat_id, konsultasi_id } = data;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses');
      if (!Number(konsultasi_id))
        throw new BadRequestException('Konsultasi id invalid!');

      const polda_id = await this.getSatuanPolda(user.satuan_id);
      const konsultasi = await this.prisma.konsultasi.findFirst({
        where: {
          id: +konsultasi_id,
          deleted_at: null,
        },
      });

      if (!konsultasi)
        throw new BadRequestException('Konsultasi tidak ditemukan!');
      if (
        konsultasi.polda_id !== polda_id ||
        konsultasi.status.toLowerCase() !==
          StatusKonseling.PENGAJUAN.toLowerCase()
      )
        throw new ForbiddenException('Invalid konsultasi!');

      const updKonsultasi = await this.prisma.konsultasi.update({
        where: {
          id: konsultasi.id,
        },
        data: {
          status: StatusKonseling.SEDANG_BERLANGSUNG,
          konselor_id: konselor.id,
          chat_id: chat_id,
        },
      });

      const getPersonel = await this.prisma.users_device_token.findMany({
        where: { user: { personel_id: konsultasi.personel_id } },
        select: { token: true, user_id: true },
      });

      if (getPersonel?.length) {
        const mock: ISendNotificationMultiple = {
          tokens: getPersonel,
          data: { link: `${this.feUrl}/psikologi/konseling` },
          type: 'KONSELING',
          title: 'Konsultasi Dimulai',
          body: 'Konselor memulai sesi konsultasi',
        };
        this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
        // this.firebaseService.sendNotification(
        //   getPersonel.token,
        //   'Konsultasi',
        //   'Konsultasi sedang berlangsung',
        //   '',
        //   {},
        // );
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_UPDATE as ConstantLogType,
          message,
          updKonsultasi,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: updKonsultasi,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async listChat(req, search: string) {
    const user = req.user;
    const konselor = await this.prisma.konsultasi_konselor.findFirst({
      where: {
        personel_id: user.personel_id,
        deleted_at: null,
      },
    });

    if (!konselor) throw new UnauthorizedException('Tidak ada memiliki akses');

    const queryResult = await this.prisma.konsultasi.findMany({
      where: {
        status: StatusKonseling.SEDANG_BERLANGSUNG,
        konselor_id: konselor.id,
        deleted_at: null,
        personel: {
          nama_lengkap: search
            ? {
                contains: search,
                mode: 'insensitive',
              }
            : {},
        },
      },
      select: {
        chat_id: true,
        personel: {
          select: {
            nama_lengkap: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async startChat(req, konsultasi_id: number) {
    if (!konsultasi_id)
      throw new BadRequestException('Id konsultasi tidak boleh kosong');

    const user = req.user;
    const konselor = await this.prisma.konsultasi_konselor.findFirst({
      where: {
        personel_id: user.personel_id,
        deleted_at: null,
      },
    });

    if (!konselor) throw new UnauthorizedException('Tidak ada memiliki akses');

    const getDetailKonsultasi = await this.prisma.konsultasi.findFirst({
      where: {
        id: konsultasi_id,
        status: StatusKonseling.PENGAJUAN,
        konselor_id: null,
        deleted_at: null,
      },
      select: {
        status: true,
        personel: {
          select: {
            uid: true,
          },
        },
      },
    });

    if (!getDetailKonsultasi)
      throw new BadRequestException('Konsultasi tidak ditemukan!');
    if (
      getDetailKonsultasi.status.toLowerCase() !==
      StatusKonseling.PENGAJUAN.toLowerCase()
    )
      throw new ForbiddenException(
        'Konsultasi belum berlangsung atau sudah selesai!',
      );

    const chatId = `${getDetailKonsultasi.personel.uid}-${Date.now()}`;

    const queryResult = { chat_id: chatId };

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async akhiriChat(req, chat_id: string) {
    try {
      if (!chat_id) throw new BadRequestException('Chat id tidak boleh kosong');

      const user = req.user;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });
      const konsultasi = await this.prisma.konsultasi.findFirst({
        where: {
          chat_id: chat_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses');
      if (!konsultasi)
        throw new BadRequestException('Konsultasi tidak ditemukan!');
      if (!konsultasi.konselor_id)
        throw new BadRequestException('Invalid konsultasi');
      if (
        konsultasi.status.toLowerCase() !==
        StatusKonseling.SEDANG_BERLANGSUNG.toLowerCase()
      )
        throw new ForbiddenException(
          'Konsultasi belum berlangsung atau sudah selesai!',
        );

      const updKonsultasi = await this.prisma.konsultasi.update({
        where: {
          id: konsultasi.id,
        },
        data: {
          status: status_konsultasi_enum.MENUNGGU_HASIL,
        },
      });

      const getPersonel = await this.prisma.users_device_token.findMany({
        where: { user: { personel_id: konsultasi.personel_id } },
        select: { token: true, user_id: true },
      });

      if (getPersonel?.length) {
        const mock: ISendNotificationMultiple = {
          tokens: getPersonel,
          data: { link: `${this.feUrl}/psikologi/konseling` },
          type: 'KONSELING',
          title: 'Konsultasi Selesai',
          body: 'Konselor mengakhiri sesi konsultasi',
        };
        this.eventEmitter.emit(FirebaseEnum.SEND_MULTIPLE, mock);
        // this.firebaseService.sendNotification(
        //   getPersonel.token,
        //   'Konsultasi',
        //   'Konsultasi selesai dan menunggu hasil',
        //   '',
        //   {},
        // );
      }
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_UPDATE as ConstantLogType,
          message,
          updKonsultasi,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: updKonsultasi,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getRiwayat(req, paginationData, searchandsortData) {
    try {
      const user = req.user;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses');

      const polda_id = await this.getSatuanPolda(user.satuan_id);
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        status: { field: 'status', type: 'enum', enums: StatusKonseling },
        nama: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      where.OR = [
        {
          status: status_konsultasi_enum.SELESAI,
          polda_id: polda_id,
          deleted_at: null,
          konselor: {
            id: konselor.id,
          },
        },
      ];

      if (search) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            personel: {
              OR: [
                {
                  nama_lengkap: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
                {
                  nrp: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          };
        });
      }

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi.count({
          where: where,
        }),
        this.prisma.konsultasi.findMany({
          select: {
            id: true,
            created_at: true,
            status: true,
            personel: {
              select: {
                nrp: true,
                nama_lengkap: true,
              },
            },
            konsultasi_jenis: {
              select: {
                nama: true,
              },
            },
            konselor: {
              select: {
                personel: {
                  select: {
                    nama_lengkap: true,
                  },
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );
      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getList(req, paginationData, searchandsortData) {
    try {
      const user = req.user;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses');

      const polda_id = await this.getSatuanPolda(user.satuan_id);
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: IColumnMapping = {
        status: { field: 'status', type: 'enum', enums: StatusKonseling },
        nama: { field: 'personel.nama_lengkap', type: 'string' },
        nrp: { field: 'personel.nrp', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      where.OR = [
        {
          status: status_konsultasi_enum.PENGAJUAN,
          polda_id: polda_id,
          konselor_id: null,
        },
        {
          status: status_konsultasi_enum.SEDANG_BERLANGSUNG,
          polda_id: polda_id,
          konselor: {
            id: konselor.id,
          },
        },
        {
          status: status_konsultasi_enum.MENUNGGU_HASIL,
          polda_id: polda_id,
          konselor: {
            id: konselor.id,
          },
        },
      ];
      if (search) {
        where.OR = where.OR.map((item) => {
          return {
            ...item,
            personel: {
              OR: [
                {
                  nama_lengkap: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
                {
                  nrp: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          };
        });
      }

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi.count({
          where: where,
        }),
        this.prisma.konsultasi.findMany({
          select: {
            id: true,
            created_at: true,
            status: true,
            personel: {
              select: {
                nrp: true,
                nama_lengkap: true,
              },
            },
            konsultasi_jenis: {
              select: {
                nama: true,
              },
            },
            konselor: {
              select: {
                personel: {
                  select: {
                    nama_lengkap: true,
                  },
                },
              },
            },
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getDetail(req, konsultasi_id: number) {
    try {
      const user = req.user;
      const konselor = await this.prisma.konsultasi_konselor.findFirst({
        where: {
          personel_id: user.personel_id,
          deleted_at: null,
        },
      });
      const konsultasi = await this.prisma.konsultasi.findFirst({
        where: {
          id: konsultasi_id,
          deleted_at: null,
        },
      });

      if (!konselor)
        throw new UnauthorizedException('Tidak ada memiliki akses!');
      if (!konsultasi)
        throw new BadRequestException('Konsultasi tidak ditemukan!');

      const queryResult = await this.prisma.konsultasi.findFirst({
        where: {
          id: konsultasi.id,
          deleted_at: null,
        },
        select: {
          chat_id: true,
          keluhan: true,
          status: true,
          created_at: true,
          personel: {
            select: {
              nrp: true,
              ktp_nomor: true,
              nama_lengkap: true,
              tempat_lahir: true,
              tanggal_lahir: true,
              email: true,
              no_hp: true,
              alamat: {
                select: {
                  alamat: true,
                },
                orderBy: {
                  created_at: 'desc',
                },
                take: 1,
              },
              agama: {
                select: {
                  nama: true,
                },
              },
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: {
                      // id: true,
                      nama: true,
                      nama_singkat: true,
                    },
                  },
                },
                orderBy: {
                  tmt: 'desc',
                },
                take: 1,
              },
              jabatan_personel: {
                select: {
                  jabatans: {
                    select: {
                      // id: true,
                      nama: true,
                      satuan: {
                        select: {
                          // id: true,
                          nama: true,
                        },
                      },
                    },
                  },
                },
                orderBy: {
                  tmt_jabatan: 'desc',
                },
                take: 1,
              },
            },
          },
          konsultasi_jenis: {
            select: {
              nama: true,
            },
          },
          konsultasi_file: {
            select: {
              id: true,
              originalname: true,
              url: true,
            },
          },
          konselor: {
            select: {
              personel: {
                select: {
                  nama_lengkap: true,
                },
              },
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PSIKOLOGI_E_KONSELING_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PSIKOLOGI_E_KONSELING_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  private async getSatuanPolda(satuan_id: number) {
    const satuan = await this.prisma.satuan.findFirst({
      where: {
        id: satuan_id,
        deleted_at: null,
      },
    });

    if (
      satuan.nama.toLowerCase().split(' ')[0].includes('polda') ||
      satuan.atasan_id == null
    ) {
      return satuan.id;
    }

    return await this.getSatuanPolda(Number(satuan.atasan_id));
  }
}
