import { Modu<PERSON> } from '@nestjs/common';
import { EMentalController } from './controller/e-mental.controller';
import { EmentalService } from './service/e-mental.service';
import { PrismaModule } from '../../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [EMentalController],
  providers: [EmentalService],
})
export class EMentalModule {}
