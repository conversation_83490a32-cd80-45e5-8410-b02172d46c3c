import { HttpStatus, Injectable } from '@nestjs/common';
import { PaginationDto } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { LogsActivityService } from '../../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../../core/constants/log.constant';
import { ConstantLogType } from '../../../core/interfaces/log.type';
import { SearchAndSortRoleDto } from '../../../access-management/roles/dto/roles.dto';

@Injectable()
export class EmentalService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const limit = paginationData.limit || 10;
    const page = paginationData.page || 1;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: {
      [key: string]: { field: string; type: 'string' | 'number' };
    } = {
      tahun: { field: 'tahun', type: 'number' },
      semester: { field: 'semester', type: 'number' },
      nilai: { field: 'nilai', type: 'number' },
      nama: { field: 'personel.nama_lengkap', type: 'string' },
      nrp: { field: 'personel.nrp', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, psikologiData] = await Promise.all([
      this.prisma.psikologi_personel.count({ where }),
      this.prisma.psikologi_personel.findMany({
        select: {
          id: true,
          tahun: true,
          semester: true,
          nilai: true,
          keterangan: true,
          personel: {
            select: {
              nama_lengkap: true,
              nrp: true,
              pangkat_personel: {
                select: {
                  pangkat: {
                    select: { nama: true },
                  },
                },
                orderBy: { tmt: 'desc' },
                take: 1,
              },
            },
          },
        },
        where,
        orderBy,
        take: +limit,
        skip: +limit * (+page - 1),
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = psikologiData.map((psikologi) => {
      const { personel, ...restData } = psikologi;

      return {
        ...restData,
        nama: personel.nama_lengkap,
        nrp: personel.nrp,
        pangkat: personel.pangkat_personel[0].pangkat.nama,
      };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.PSIKOLOGI_E_MENTAL_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.PSIKOLOGI_E_MENTAL_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }
}
