import { Controller, Get, Logger, Query, Req, UseGuards } from '@nestjs/common';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { EmentalService } from '../service/e-mental.service';
import { Module, Permission } from '../../../core/decorators';
import { MODULES } from '../../../core/constants/module.constant';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../../core/guards/permission-auth.guard';

@Controller('e-mental')
@Module(MODULES.E_MENTAL)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class EMentalController {
  private readonly logger = new Logger(EMentalController.name);

  constructor(private readonly eMentalService: EmentalService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(
        paginationData,
      )} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.eMentalService.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(
        paginationData,
      )} and search and sort data: ${JSON.stringify(
        searchandsortData,
      )} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
