import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { CreateKonsultasi } from '../dto/bagsipers.dto';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { StatusPengajuanEnum } from '../../core/enums/bagsipers.enum';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { LogsActivityService } from 'src/api-utils/logs-activity/service/logs-activity.service';

@Injectable()
export class BagsipersService {
  constructor(
    private readonly logsActivityService: LogsActivityService,
    private readonly prisma: PrismaService,
  ) {}

  async create(req: any, body: CreateKonsultasi) {
    const user = req.user;
    if (!body.konsultasi)
      throw new BadRequestException('Konsultasi tidak boleh kosong!');

    if (
      await this.prisma.konsultasi_personel.findFirst({
        where: {
          personel_id: user.personel_id,
          status: {
            in: [
              StatusPengajuanEnum.BELUM_DIPERIKSA,
              StatusPengajuanEnum.SEDANG_KONSULTASI,
            ],
          },
          deleted_at: null,
        },
      })
    ) {
      throw new BadRequestException('Konsultasi sedang berlangsung!');
    }

    const queryResult = await this.prisma.konsultasi_personel.create({
      data: {
        personel_id: user.personel_id,
        konsultasi: body.konsultasi,
        konselor: body.konselor,
        status: StatusPengajuanEnum.BELUM_DIPERIKSA,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_CREATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getStatus(req: any) {
    const user = req.user;
    const queryResult = await this.prisma.konsultasi_personel.findFirst({
      where: {
        personel_id: user.personel_id,
        status: {
          in: [
            StatusPengajuanEnum.BELUM_DIPERIKSA,
            StatusPengajuanEnum.SEDANG_KONSULTASI,
          ],
        },
        deleted_at: null,
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAllKonsultasi(req: any, paginationData, searchandsortData) {
    try {
      const limit = paginationData.limit || 100;
      const page = paginationData.page || 1;

      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;

      const columnMapping: {
        [key: string]: { field: string; type: 'string' };
      } = {
        nama: { field: 'personel.nama_lengkap', type: 'string' },
      };

      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );

      const [totalData, queryResult] = await this.prisma.$transaction([
        this.prisma.konsultasi_personel.count({
          where: where,
        }),
        this.prisma.konsultasi_personel.findMany({
          select: {
            id: true,
            konsultasi: true,
            konselor: true,
            status: true,
            personel: {
              select: {
                nama_lengkap: true,
                nrp: true,
              },
            },
            created_at: true,
          },
          take: +limit,
          skip: +limit * (+page - 1),
          orderBy: orderBy,
          where: where,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGSIPERS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGSIPERS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
        totalPage,
        totalData,
        page,
      };
    } catch (error) {
      throw error;
    }
  }

  async getKonsultasi(req: any, id: number) {
    try {
      if (!id)
        throw new BadRequestException('Id konsultasi tidak boleh kosong');

      const queryResult = await this.prisma.konsultasi_personel.findFirst({
        where: {
          id: id,
          deleted_at: null,
        },
        include: { personel: true },
      });

      if (!queryResult)
        throw new BadRequestException('Konsultasi tidak ditemukan!');

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.BAGSIPERS_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.BAGSIPERS_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (error) {
      throw error;
    }
  }

  async startChat(req: any, id: number) {
    if (!id) throw new BadRequestException('Id konsultasi tidak boleh kosong');

    const getDetailKonsultasi = await this.prisma.konsultasi_personel.findFirst(
      {
        where: {
          id: id,
          status: StatusPengajuanEnum.BELUM_DIPERIKSA,
          deleted_at: null,
        },
        select: {
          id: true,
          personel: {
            select: {
              id: true,
              nama_lengkap: true,
              uid: true,
            },
          },
        },
      },
    );

    if (!getDetailKonsultasi)
      throw new BadRequestException('Konsultasi tidak ditemukan!');

    const chatId = `${getDetailKonsultasi.personel.uid}-${Date.now()}`;
    await this.prisma.konsultasi_personel.update({
      where: {
        id: id,
      },
      data: { chat_id: chatId, status: StatusPengajuanEnum.SEDANG_KONSULTASI },
    });

    const queryResult = { konsultasi_id: id, chat_id: chatId };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async endChat(req: any, chatId: string) {
    if (!chatId.length)
      throw new BadRequestException('Id konsultasi tidak boleh kosong');

    const getDetailKonsultasi = await this.prisma.konsultasi_personel.findFirst(
      {
        where: {
          chat_id: chatId,
          status: StatusPengajuanEnum.SEDANG_KONSULTASI,
          deleted_at: null,
        },
        select: {
          id: true,
          personel: {
            select: {
              id: true,
              nama_lengkap: true,
            },
          },
        },
      },
    );

    if (!getDetailKonsultasi)
      throw new BadRequestException('Konsultasi tidak ditemukan!');

    await this.prisma.konsultasi_personel.update({
      where: {
        id: getDetailKonsultasi.id,
      },
      data: { status: StatusPengajuanEnum.SELESAI },
    });

    const queryResult = {
      konsultasi_id: getDetailKonsultasi.id,
      chat_id: chatId,
    };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPDATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async listChat(req: any) {
    const queryResult = await this.prisma.konsultasi_personel.findMany({
      where: {
        status: StatusPengajuanEnum.SEDANG_KONSULTASI,
        deleted_at: null,
      },
      select: {
        id: true,
        chat_id: true,
        konsultasi: true,
        konselor: true,
        personel: {
          select: {
            id: true,
            nama_lengkap: true,
          },
        },
      },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getDetailChat(req: any, chatId: string) {
    const queryResult = await this.prisma.konsultasi_personel.findFirst({
      where: {
        chat_id: chatId,
        status: StatusPengajuanEnum.SEDANG_KONSULTASI,
        deleted_at: null,
      },
      select: {
        id: true,
        chat_id: true,
        konselor: true,
        konsultasi: true,
        personel: {
          select: {
            nama_lengkap: true,
          },
        },
      },
    });

    if (!queryResult)
      throw new BadRequestException('Detail chat tidak ditemukan!');

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.BAGSIPERS_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.BAGSIPERS_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
}
