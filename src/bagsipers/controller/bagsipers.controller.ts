import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { BagsipersService } from '../service/bagsipers.service';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { CreateKonsultasi } from '../dto/bagsipers.dto';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('bagsipers')
@UseGuards(JwtAuthGuard)
export class BagsipersController {
  private readonly logger = new Logger(BagsipersController.name);

  constructor(private readonly bagsipersService: BagsipersService) {}

  @Post('/')
  async create(@Req() req: any, @Body() body: CreateKonsultasi) {
    this.logger.log(
      `Entering ${this.create.name} with body ${JSON.stringify(body)}`,
    );

    const response = await this.bagsipersService.create(req, body);

    this.logger.log(
      `Leaving ${this.create.name} with body ${JSON.stringify(body)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/personel/status')
  async getStatus(@Req() req: any) {
    this.logger.log(`Entering ${this.getStatus.name}`);

    const response = await this.bagsipersService.getStatus(req);

    this.logger.log(
      `Leaving ${this.getStatus.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/konsultasi')
  async getAllKonsultasi(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getAllKonsultasi.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.bagsipersService.getAllKonsultasi(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getAllKonsultasi.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/konsultasi/:id')
  async getKonsultasi(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.getKonsultasi.name} with id ${id}`);

    const response = await this.bagsipersService.getKonsultasi(req, +id);

    this.logger.log(
      `Leaving ${this.getKonsultasi.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/startchat/:id')
  async startChat(@Req() req: Request, @Param('id') id: string) {
    this.logger.log(`Entering ${this.startChat.name} with id ${id}`);

    const response = await this.bagsipersService.startChat(req, +id);

    this.logger.log(
      `Leaving ${this.startChat.name} with id ${id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/endchat/:chat_id')
  async endChat(@Req() req: Request, @Param('chat_id') chat_id: string) {
    this.logger.log(`Entering ${this.endChat.name} with param ${chat_id}`);

    const response = await this.bagsipersService.endChat(req, chat_id);

    this.logger.log(
      `Leavingg ${this.endChat.name} with param ${chat_id} and response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/listchat')
  async listChat(@Req() req: any) {
    this.logger.log(`Entering ${this.listChat.name}`);

    const response = await this.bagsipersService.listChat(req);

    this.logger.log(
      `Leaving ${this.listChat.name} with response ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/detailchat/:chatid')
  async detailChat(@Req() req: Request, @Param('chatid') chatId: string) {
    this.logger.log(`Entering ${this.detailChat.name} with chat id ${chatId}`);

    const response = await this.bagsipersService.getDetailChat(req, chatId);

    this.logger.log(
      `Leaving ${this.detailChat.name} with chat id ${chatId} and response ${JSON.stringify(response)}`,
    );

    return response;
  }
}
