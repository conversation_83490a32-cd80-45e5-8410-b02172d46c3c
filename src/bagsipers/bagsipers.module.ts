import { forwardRef, Module } from '@nestjs/common';
import { BagsipersService } from './service/bagsipers.service';
import { BagsipersController } from './controller/bagsipers.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [BagsipersController],
  providers: [BagsipersService],
})
export class BagsipersModule {}
