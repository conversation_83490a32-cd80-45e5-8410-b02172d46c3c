import {
  HttpStatus,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import {
  IGetAllKenaikanPangkatInterface,
  IPangkatInterface,
  IPangkatPersonelInterface,
} from '../../core/interfaces/kenaikan-pangkat.interface';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../core/utils/search.utils';
import { CreateKenaikanPangkatDTO } from '../dto/kenaikan-pangkat.dto';

@Injectable()
export class KenaikanPangkatService {
  constructor(
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async findAll(req: any, params: IGetAllKenaikanPangkatInterface) {
    const { searchAndShort, pagination } = params;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndShort;

    const limit = pagination.limit || 10;
    const page = pagination.page || 1;

    const columnMapping: IColumnMapping = {
      nama: {
        field: 'personel.nama_lengkap',
        type: 'string',
      },
      nrp: { field: 'personel.nrp', type: 'string' },
      pangkat: { field: 'pangkat.nama', type: 'string' },
      tmt: { field: 'tmt', type: 'date' },
      tgl_skep: { field: 'kep_tanggal', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.pangkat_personel.count({ where }),
      this.prisma.pangkat_personel.findMany({
        include: {
          pangkat: {
            select: {
              nama: true,
              nama_singkat: true,
            },
          },
          personel: {
            select: {
              nama_lengkap: true,
              nrp: true,
            },
          },
        },
        take: +limit,
        skip: +limit * (+page - 1),
        where,
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_PANGKAT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findOne(req: any, id: number) {
    const personel = await this.prisma.personel.findFirst({
      where: { id, deleted_at: null },
      include: {
        status_aktif: true,
        jabatan_personel: {
          select: {
            tmt_jabatan: true,
            jabatans: {
              select: {
                nama: true,
              },
              include: {
                satuan: {
                  select: {
                    nama: true,
                  },
                },
              },
            },
          },
          orderBy: {
            tmt_jabatan: 'desc',
          },
          take: 1,
        },
        pangkat_personel: {
          include: {
            pangkat: true,
          },
          orderBy: {
            tmt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!personel) {
      throw new NotFoundException(`Personel tidak ditemukan`);
    }

    let kenaikanPangkatSelanjutnya = [];

    const tingkat_id = personel.pangkat_personel[0]?.pangkat?.tingkat_id;

    if (tingkat_id) {
      const nextPromotion = await this.prisma.pangkat.findMany({
        where: { tingkat_id },
        orderBy: { id: 'desc' },
        select: {
          id: true,
          nama: true,
          nama_singkat: true,
        },
      });

      if (nextPromotion) {
        kenaikanPangkatSelanjutnya = nextPromotion.reverse();
      }
    }

    const mappedPromotion = this.generatePromotionHirarcy(
      personel.pangkat_personel[0],
      kenaikanPangkatSelanjutnya,
    );

    const queryResult = {
      ...personel,
      kenaikan_pangkat_selanjutnya: mappedPromotion,
    };
    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        null,
        CONSTANT_LOG.KENAIKAN_PANGKAT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );
    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  generatePromotionHirarcy(
    currentRank: IPangkatPersonelInterface,
    promotionRanks: IPangkatInterface[],
  ) {
    if (!currentRank || !promotionRanks.length) return [];

    const tmtDate = new Date(currentRank.tmt);
    const tmtYear = tmtDate.getFullYear();
    const tmtDayMonth = tmtDate.toISOString().slice(5);
    const currentRankIndex = promotionRanks.findIndex(
      (rank) => rank.nama === currentRank.pangkat.nama,
    );

    const result = promotionRanks.map((rank, index) => {
      const promotionYear = tmtYear + (index - currentRankIndex) * 4;
      return {
        ...rank,
        promotion_year: `${promotionYear}-${tmtDayMonth}`,
        status: index <= currentRankIndex ? 1 : 0, //
      };
    });

    return result.reverse();
  }

  async create(req: any, body: CreateKenaikanPangkatDTO) {
    const { pangkat_id, tmt, kep_nomor, kep_file, kep_tanggal, personel_id } =
      body;

    const existingPromotion = await this.prisma.pangkat_personel.findFirst({
      where: {
        personel_id,
        deleted_at: null,
        is_aktif: true,
      },
    });

    if (existingPromotion) {
      const existingTMT = new Date(existingPromotion.tmt);
      const newTMT = new Date(tmt);

      if (newTMT <= existingTMT) {
        throw new UnprocessableEntityException(
          'Tanggal TMT baru harus lebih besar dari TMT yang sudah ada',
        );
      }
    }

    const personel = await this.prisma.personel.findFirst({
      select: {
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                satuan: {
                  select: {
                    id: true,
                    nama: true,
                    jenis_id: true,
                  },
                },
              },
            },
          },
          where: {
            is_aktif: true,
          },
        },
      },
      where: {
        id: personel_id,
        deleted_at: null,
      },
    });

    if (personel.jabatan_personel.length === 0) {
      throw new NotFoundException(`Personel tidak memiliki jabatan aktif`);
    }

    const levelSatuan = await this.prisma.mv_satuan_with_top_parents.findFirst({
      where: {
        id: personel.jabatan_personel[0].jabatans.satuan.id,
      },
    });

    let operator;
    if (
      levelSatuan.third_top_parent_nama !==
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 3 E-KTA';
    } else if (
      levelSatuan.third_top_parent_nama ===
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 2 E-KTA';
    } else if (
      levelSatuan.third_top_parent_nama ===
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama === levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 1 E-KTA';
    }

    const approvalHistory = [
      {
        approval: operator,
        status: 'PENDING',
        comment: '',
        created_date: new Date().toISOString(),
        created_by: req.user.id,
      },
    ];

    const existPengajuanEkta = await this.prisma.e_kta.findFirst({
      select: {
        id: true,
        personel_id: true,
      },
      where: {
        personel_id,
        status: false,
        deleted_at: null,
      },
    });

    const result = await this.prisma.$transaction(
      async (prisma: PrismaService) => {
        if (existPengajuanEkta) {
          await prisma.e_kta.update({
            where: {
              id: existPengajuanEkta.id,
            },
            data: {
              personel_id: BigInt(personel_id),
              status: false,
              jenis_permintaan: 1,
              approval_history: approvalHistory,
              updated_at: new Date(),
              updated_by: req.user.id,
            },
          });
        } else {
          await prisma.e_kta.create({
            data: {
              tanggal: new Date(),
              personel_id: BigInt(personel_id),
              status: false,
              jenis_permintaan: 1,
              approval_history: approvalHistory,
              created_by: req.user.id,
            },
          });
        }

        if (existingPromotion) {
          await prisma.pangkat_personel.update({
            where: {
              id: existingPromotion.id,
            },
            data: {
              is_aktif: false,
              updated_at: new Date(),
            },
          });
        }

        const newPromotion = await prisma.pangkat_personel.create({
          data: {
            pangkat_id,
            tmt: String(tmt),
            kep_nomor,
            kep_file,
            kep_tanggal: String(kep_tanggal),
            tanggal_perubahan: new Date(),
            personel_id,
            is_aktif: true,
          },
        });

        return newPromotion;
      },
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.KENAIKAN_PANGKAT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.KENAIKAN_PANGKAT_CREATE as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.CREATED,
      message,
      data: result,
    };
  }
}
