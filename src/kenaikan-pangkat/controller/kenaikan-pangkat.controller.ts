import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { KenaikanPangkatService } from '../service/kenaikan-pangkat.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Module, Permission } from '../../core/decorators';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { MODULES } from '../../core/constants/module.constant';
import { IGetAllKenaikanPangkatInterface } from '../../core/interfaces/kenaikan-pangkat.interface';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';

@Controller('kenaikan-pangkat')
@Module(MODULES.PROMOTIONS)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class KenaikanPangkatController {
  private readonly logger = new Logger(KenaikanPangkatController.name);

  constructor(
    private readonly kenaikanPangkatService: KenaikanPangkatService,
  ) {}

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.kenaikanPangkatService.findAll(req, {
      pagination: paginationData,
      searchAndShort: searchandsortData,
    } as IGetAllKenaikanPangkatInterface);
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.kenaikanPangkatService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  async create(@Req() req: any, @Body(new FieldValidatorPipe()) body: any) {
    const response = await this.kenaikanPangkatService.create(req, body);
    return response;
  }
}
