import {
  IsDateS<PERSON>,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
} from 'class-validator';

export class GetAllKenaikanPangkatDTO {
  @IsOptional()
  type: string;
}

export class CreateKenaikanPangkatDTO {
  @IsNotEmpty()
  @IsNumber()
  pangkat_id: number;

  @IsNotEmpty()
  @IsDateString()
  tmt: Date;

  @IsNotEmpty()
  @IsString()
  kep_nomor: string;

  @IsOptional()
  @IsString()
  kep_file: string;

  @IsNotEmpty()
  @IsDateString()
  kep_tanggal: Date;

  @IsNotEmpty()
  @IsNumber()
  personel_id: number;
}
