import { forwardRef, Module } from '@nestjs/common';
import { KenaikanPangkatService } from './service/kenaikan-pangkat.service';
import { KenaikanPangkatController } from './controller/kenaikan-pangkat.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [KenaikanPangkatController],
  providers: [KenaikanPangkatService],
})
export class KenaikanPangkatModule {}
