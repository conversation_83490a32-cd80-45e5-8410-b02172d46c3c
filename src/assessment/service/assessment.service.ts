import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { PaginationDto } from '../../core/dtos';
import { SearchAndSortRoleDto } from '../../access-management/roles/dto/roles.dto';

@Injectable()
export class AssessmentService {
  constructor(
    private prisma: PrismaService,
    private logsActivityService: LogsActivityService,
  ) {}

  async get(req, id) {
    const queryResult = await this.prisma.assessment_personel.findFirst({
      select: {
        id: true,
        personel: {
          select: {
            id: true,
            nama_lengkap: true,
          },
        },
        nama: true,
        tmt_mulai: true,
        tmt_selesai: true,
        nilai: true,
        nilai_file: true,
        keterangan: true,
      },
      where: {
        id: +id,
        deleted_at: null,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(
        `assessment personel id ${id} tidak di temukan`,
      );
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.ASSESSMENT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ASSESSMENT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async getAssessmentPersonel(req, uid) {
    const assessmentPersonel = await this.prisma.assessment_personel.findMany({
      select: {
        id: true,
        personel: {
          select: {
            id: true,
            nama_lengkap: true,
          },
        },
        nama: true,
        tmt_mulai: true,
        tmt_selesai: true,
        nilai: true,
        nilai_file: true,
        keterangan: true,
      },
      where: {
        personel: {
          uid: uid,
        },
        deleted_at: null,
      },
    });

    if (!assessmentPersonel) {
      throw new NotFoundException(
        `assessment personel id ${uid} tidak di temukan`,
      );
    }

    const result = assessmentPersonel.map((item) => {
      const tmtStart = new Date(item.tmt_mulai);
      const dayMulai = String(tmtStart.getDate()).padStart(2, '0');
      const monthMulai = String(tmtStart.getMonth() + 1).padStart(2, '0');
      const yearMulai = tmtStart.getFullYear();
      const tmtMulai =
        item.tmt_mulai == null
          ? null
          : `${monthMulai}-${dayMulai}-${yearMulai}`;

      const tmtEnd = new Date(item.tmt_selesai);
      const daySelesai = String(tmtEnd.getDate()).padStart(2, '0');
      const monthSelesai = String(tmtEnd.getMonth() + 1).padStart(2, '0');
      const yearSelesai = tmtEnd.getFullYear();
      const tmtSelesai =
        item.tmt_selesai == null
          ? null
          : `${monthSelesai}-${daySelesai}-${yearSelesai}`;
      return {
        jabatan: item.nama,
        nilai: item.nilai,
        tmt_mulai: tmtMulai,
        tmt_selesai: tmtSelesai,
      };
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ASSESSMENT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ASSESSMENT_READ as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: result,
    };
  }

  async getList(
    req: any,
    paginationData: PaginationDto,
    searchAndSortData: SearchAndSortRoleDto,
  ) {
    const limit = paginationData.limit || 100;
    const page = paginationData.page || 1;

    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndSortData;

    const columnMapping: IColumnMapping = {
      'personel.nama_lengkap': { field: 'personel.nama_lengkap', type: 'string' },
      'nama': { field: 'nama', type: 'string' },
      'nilai': { field: 'nilai', type: 'number' },
      'nilai_file': { field: 'nilai_file', type: 'number' },
      'tmt_mulai': { field: 'tmt_mulai', type: 'number' },
      'tmt_selesai': { field: 'tmt_selesai', type: 'number' },
      'keterangan': { field: 'keterangan', type: 'number' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );
    
    const [totalData, queryData] = await this.prisma.$transaction([
      this.prisma.assessment_personel.count({
        where: where,
      }),
      this.prisma.assessment_personel.findMany({
        select: {
          id: true,
          personel: {
            select: {
              id: true,
              nama_lengkap: true,
            },
          },
          nama: true,
          tmt_mulai: true,
          tmt_selesai: true,
          nilai: true,
          nilai_file: true,
          keterangan: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const queryResult = queryData.map((item) => ({
      ...item,
      tmt_mulai: item.tmt_mulai
        ? item.tmt_mulai.toISOString().split('T')[0]
        : null,
      tmt_selesai: item.tmt_selesai
        ? item.tmt_selesai.toISOString().split('T')[0]
        : null,
    }));

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.ASSESSMENT_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.ASSESSMENT_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
      data: queryData,
    };
  }
}
