import { forwardRef, Module } from '@nestjs/common';
import { AssessmentService } from './service/assessment.service';
import { AssessmentController } from './controller/assessment.controller';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [AssessmentController],
  providers: [AssessmentService],
})
export class AssessmentModule {}
