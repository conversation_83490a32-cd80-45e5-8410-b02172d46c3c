import {
  <PERSON>,
  Get,
  HttpC<PERSON>,
  Lo<PERSON>,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AssessmentService } from '../service/assessment.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { Module, Permission } from 'src/core/decorators';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { MODULES } from '../../core/constants/module.constant';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';

@Controller('assessment')
@Module(MODULES.ASSESSMENT_DATA)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class AssessmentController {
  private readonly logger = new Logger(AssessmentController.name);

  constructor(private readonly assessmentService: AssessmentService) {}

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async get(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.get.name} with id ${id}`);

    const response = await this.assessmentService.get(req, id);

    this.logger.log(
      `Leaving ${this.get.name} with id ${id} data and data ${JSON.stringify(response)} `,
    );

    return response;
  }

  @Get('/personel/:uid')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getPersonelAssessment(@Req() req: Request, @Param('uid') uid: string) {
    this.logger.log(
      `Entering ${this.getPersonelAssessment.name} with uid ${uid}`,
    );

    const response = await this.assessmentService.getAssessmentPersonel(
      req,
      uid,
    );

    this.logger.log(
      `Leaving ${this.getPersonelAssessment.name} with uid ${uid} data and data ${JSON.stringify(response)} `,
    );

    return response;
  }

  @Get('/')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: Request,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );

    const response = await this.assessmentService.getList(
      req,
      paginationData,
      searchandsortData,
    );

    this.logger.log(
      `Leaving ${this.getList.name} with response ${JSON.stringify(response)} `,
    );

    return response;
  }
}
