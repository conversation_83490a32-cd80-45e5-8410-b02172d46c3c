import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  IGetAllMutasiJabatan,
  IMutasiJabatan,
  IUpdateDocument,
} from '../../core/interfaces/mutasi-jabatan.interface';
import { PersonelService } from 'src/personel/service/personel.service';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { SatuanService } from 'src/mdm/satuan/satuan/service/satuan.service';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import fs from 'fs';
import { ExcelService } from '../../api-utils/excel/service/excel.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';
import { IColumnMapping } from '../../core/interfaces/db.interface';
import { SortSearchColumn } from '../../core/utils/search.utils';
import {
  CreateMutasiJabatanDto,
  CreateMutasiSatuanAsnDto,
} from '../dto/mutasi-jabatan.dto';

@Injectable()
export class MutasiJabatanService {
  constructor(
    private readonly personelService: PersonelService,
    private readonly satuanService: SatuanService,
    private readonly minioService: MinioService,
    private readonly excelService: ExcelService,
    private prisma: PrismaService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async processExcelFile(req: any, file: Express.Multer.File): Promise<any> {
    const tmpFilePath = file.originalname;
    try {
      fs.writeFileSync(tmpFilePath, file.buffer);
      const json = await this.excelService.extractToJson(
        req,
        tmpFilePath,
        false,
      );
      const header = await this.excelService.getHeader(tmpFilePath);
      const queryResult = { json, header };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MUTASI_JABATAN_READ_EXCEL as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } finally {
      fs.unlinkSync(tmpFilePath);
    }
  }

  private async sendNotifications(results: any[]) {
    try {
      const notifications = results
        .filter((result) => result.user_id)
        .map((result) => ({
          title: 'Mutasi Jabatan',
          description: `Anda telah dimutasi.`,
          type: 'MUTASI JABATAN',
          users_id: result.user_id,
          is_read: false,
        }));

      if (notifications.length > 0) {
        await this.prisma.notifikasi.createMany({ data: notifications });
        console.log('Notifikasi berhasil dibuat untuk semua personel.');
      } else {
        console.log('Tidak ada user_id yang valid, notifikasi tidak dibuat.');
      }
    } catch (error) {
      console.error('Gagal membuat notifikasi:', error.message);
    }
  }

  async importAsn(req: any, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File is required.');
    }

    const result = await this.processExcelFile(req, file);

    const expectedHeader = [
      'nama',
      'nip',
      'pangkat',
      'kesatuan_lama',
      'kesatuan_baru',
      'keterangan',
      'tmt',
    ];

    if (!this.excelService.isHeaderValid(result.header, expectedHeader)) {
      throw new BadRequestException(
        'Format template tidak valid. harap gunakan template yang disediakan.',
      );
    }

    const rows: IMutasiJabatan[] = result.json;
    const queryData = await this.prisma.$transaction(async (prisma) => {
      const promises = rows.map(async (row, index) => {
        const [personel, satuan_lama, satuan_baru] = await Promise.all([
          this.personelService.findByNrp(row.nip.toString()),
          this.satuanService.findByName(row.kesatuan_lama),
          this.satuanService.findByName(row.kesatuan_baru),
        ]);

        if (!personel) {
          throw new NotFoundException(
            `Terdapat error pada baris ke ${index + 1}: Personel dengan NRP ${row.nip} tidak ditemukan.`,
          );
        }
        if (!satuan_lama) {
          throw new NotFoundException(
            `Terdapat error pada baris ke ${index + 1}: Satuan Lama '${row.kesatuan_lama}' tidak ditemukan untuk NRP ${row.nip}.`,
          );
        }
        if (!satuan_baru) {
          throw new NotFoundException(
            `Terdapat error pada baris ke ${index + 1}: Satuan Baru '${row.kesatuan_baru}' tidak ditemukan untuk NRP ${row.nip}.`,
          );
        }

        return {
          personel_id: personel.id,
          satuan_lama_id: satuan_lama.id,
          satuan_baru_id: satuan_baru.id,
          keterangan: row.keterangan,
          tmt: row.tmt,
          type: 'ASN',
          user_id: personel.users[0]?.id || '',
        };
      });

      const rowsToSave = await Promise.all(promises);

      await prisma.mutasi_jabatan.createMany({
        data: rowsToSave.map(({ user_id, ...rest }) => rest),
      });

      return rowsToSave;
    });

    setImmediate(() => this.sendNotifications(queryData));

    const queryResult = {
        count: queryData.length,
        personel: queryData,
      },
      message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPLOAD_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
      );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_UPLOAD as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async findAll(req, params: IGetAllMutasiJabatan) {
    const { searchAndShort, pagination, type } = params;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndShort;

    const limit = pagination.limit || 10;
    const page = pagination.page || 1;

    const columnMapping: IColumnMapping = {
      nama: {
        field: 'personel.nama_lengkap',
        type: 'string',
      },
      nrp: { field: 'personel.nrp', type: 'string' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.mutasi_jabatan.count({ where }),
      this.prisma.mutasi_jabatan.findMany({
        select: {
          id: true,
          type: true,
          keterangan: true,
          tmt: true,
          personel: {
            select: {
              nama_lengkap: true,
              nrp: true,
              pangkat_personel: {
                include: {
                  pangkat: true,
                },
              },
            },
          },
          mutasi_jabatan_satuan_lama: {
            select: {
              nama: true,
            },
          },
          mutasi_jabatan_satuan_baru: {
            select: {
              nama: true,
            },
          },
          mutasi_jabatan_dokumen: true,
          created_at: true,
        },
        take: +limit,
        skip: +limit * (+page - 1),
        orderBy: orderBy,
        where: where,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findOne(req: any, id: number) {
    const queryResult = await this.prisma.mutasi_jabatan.findFirst({
      where: { id, deleted_at: null },
      include: {
        personel: {
          include: {
            pangkat_personel: {
              include: {
                pangkat: true,
              },
            },
          },
        },
        mutasi_jabatan_satuan_baru: true,
        mutasi_jabatan_satuan_lama: true,
        mutasi_jabatan_dokumen: true,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(`mutasi tidak ditemukan`);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }

  async updateDocument(req: any, files: IUpdateDocument, id: number | string) {
    const requiredFiles = [
      'file_renskep',
      'file_skep',
      'file_telegram',
      'file_winjak',
      'file_petikan',
    ];
    const missingFiles = requiredFiles.filter(
      (fileKey) => !files[fileKey] || files[fileKey].length === 0,
    );

    if (missingFiles.length > 0) {
      throw new BadRequestException(
        `Missing required files: ${missingFiles.join(', ')}`,
      );
    }

    const mutasiJabatanId = BigInt(id);

    const existingMutasiJabatan = await this.prisma.mutasi_jabatan.findUnique({
      where: { id: mutasiJabatanId },
    });

    if (!existingMutasiJabatan) {
      throw new NotFoundException(
        `Mutasi Jabatan dengan ID ${id} tidak ditemukan.`,
      );
    }

    const existingDocument =
      await this.prisma.mutasi_jabatan_dokumen.findUnique({
        where: { mutasi_jabatan_id: mutasiJabatanId },
      });

    const uploadedFiles = {};

    try {
      for (const [key, fileArray] of Object.entries(files)) {
        if (fileArray && fileArray.length > 0) {
          try {
            const uploaded = await this.uploadDokumen(fileArray[0]);
            uploadedFiles[key] = {
              file_name: uploaded.originalname,
              file_type: key,
              path: uploaded.url,
            };
          } catch (uploadError) {
            console.error(`Error uploading file ${key}:`, uploadError);
            throw new InternalServerErrorException(
              `Terjadi kesalahan saat mengupload file ${key}.`,
            );
          }
        }
      }
    } catch (error) {
      console.error('Error during file upload:', error);
      throw new InternalServerErrorException(
        'Terjadi kesalahan saat mengupload file.',
      );
    }
    let queryResult;
    if (existingDocument) {
      queryResult = await this.prisma.mutasi_jabatan_dokumen.update({
        where: { mutasi_jabatan_id: mutasiJabatanId },
        data: {
          ...uploadedFiles,
          updated_at: new Date(),
        },
      });
    } else {
      queryResult = await this.prisma.mutasi_jabatan_dokumen.create({
        data: {
          mutasi_jabatan_id: mutasiJabatanId,
          ...uploadedFiles,
        },
      });
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.UPSERT_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_UPDATE as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: document,
    };
  }

  async uploadDokumen(file: Express.Multer.File) {
    const dokumen = file;
    const upload = await this.minioService.uploadFile(dokumen);
    delete dokumen.buffer;
    delete dokumen.fieldname;

    return {
      ...dokumen,
      key: upload.Key,
      url: upload.Location,
      filename: upload.filename,
    };
  }

  async getLastUpdateAsnPolri(req: any) {
    const queryResult = await this.prisma.mutasi_jabatan.findFirst({
      where: { deleted_at: null, type: 'ASN' },
      orderBy: { created_at: 'desc' },
    });

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult.created_at,
    };
  }

  async findAllPolri(req: any, params: IGetAllMutasiJabatan) {
    const { searchAndShort, pagination } = params;
    const { sort_column, sort_desc, search_column, search_text, search } =
      searchAndShort;

    const limit = pagination.limit || 10;
    const page = pagination.page || 1;

    const columnMapping: IColumnMapping = {
      nama: {
        field: 'personel.nama_lengkap',
        type: 'string',
      },
      nrp: { field: 'personel.nrp', type: 'string' },
      jabatan: { field: 'jabatan', type: 'string' },
      tmt_jabatan: { field: 'tmt_jabatan', type: 'date' },
    };

    const { orderBy, where } = SortSearchColumn(
      sort_column,
      sort_desc,
      search_column,
      search_text,
      search,
      columnMapping,
      true,
    );

    const [totalData, queryResult] = await this.prisma.$transaction([
      this.prisma.jabatan_personel.count({ where }),
      this.prisma.jabatan_personel.findMany({
        select: {
          id: true,
          personel_id: true,
          kep_file: true,
          kep_nomor: true,
          tmt_jabatan: true,
          personel: {
            select: {
              nama_lengkap: true,
              nrp: true,
            },
          },
          jabatans: {
            select: {
              nama: true,
              satuan: {
                select: {
                  nama: true,
                },
              },
            },
          },
        },
        take: +limit,
        skip: +limit * (page - 1),
        where,
        orderBy,
      }),
    ]);

    const totalPage = Math.ceil(totalData / limit);

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.LIST,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
      page: page,
      totalPage: totalPage,
      totalData: totalData,
    };
  }

  async findOnePolri(req: any, id: number) {
    const queryResult = await this.prisma.personel.findFirst({
      where: { id, deleted_at: null },
      include: {
        jabatan_personel: true,
      },
    });

    if (!queryResult) {
      throw new NotFoundException(`personel tidak ditemukan`);
    }

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.READ_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_READ as ConstantLogType,
        message,
        queryResult,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: queryResult,
    };
  }
  async createMutasiAsn(req: any, body: CreateMutasiSatuanAsnDto) {
    const { nrp, satuan_baru_id, tmt_mutasi, keterangan } = body;

    const existingPersonel = await this.validatePersonel(nrp);

    if (existingPersonel.jabatan_personel.length === 0) {
      throw new NotFoundException(
        `Tidak ditemukan jabatan di dalam personel nrp ${nrp}`,
      );
    }

    const existingValidatedSatuanBaru =
      await this.validateSatuan(satuan_baru_id);

    const existingPersonelSatuan =
      existingPersonel.jabatan_personel[0].jabatans.satuan;
    const levelSatuan = await this.prisma.mv_satuan_with_top_parents.findFirst({
      where: {
        id: existingPersonelSatuan.id,
      },
    });

    let operator;
    if (
      levelSatuan.third_top_parent_nama !==
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 3 E-KTA';
    } else if (
      levelSatuan.third_top_parent_nama ===
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 2 E-KTA';
    } else if (
      levelSatuan.third_top_parent_nama ===
        levelSatuan.second_top_parent_nama &&
      levelSatuan.second_top_parent_nama === levelSatuan.top_parent_nama
    ) {
      operator = 'Operator Level 1 E-KTA';
    }

    const approvalHistory = [
      {
        approval: operator,
        status: 'PENDING',
        comment: '',
        created_date: new Date().toISOString(),
        created_by: req.user.id,
      },
    ];

    const existPengajuanEkta = await this.prisma.e_kta.findFirst({
      select: {
        id: true,
        personel_id: true,
      },
      where: {
        personel_id: existingPersonel.id,
        status: false,
        deleted_at: null,
      },
    });

    const result = await this.prisma.$transaction(
      async (prisma: PrismaService) => {
        await prisma.mutasi_jabatan.create({
          data: {
            personel_id: existingPersonel.id,
            satuan_lama_id: existingPersonelSatuan.id,
            satuan_baru_id: existingValidatedSatuanBaru.id,
            tmt: new Date(tmt_mutasi),
            keterangan,
            type: 'ASN',
          },
        });
        const updatedJabatan = await prisma.jabatan.update({
          where: {
            id: existingPersonel.jabatan_personel[0].jabatans.id,
            satuan_id: existingPersonelSatuan.id,
          },
          data: {
            satuan_id: satuan_baru_id,
          },
        });

        if (existPengajuanEkta) {
          await prisma.e_kta.update({
            where: {
              id: existPengajuanEkta.id,
            },
            data: {
              personel_id: BigInt(existingPersonel.id),
              status: false,
              jenis_permintaan: 1,
              approval_history: approvalHistory,
              updated_at: new Date(),
              updated_by: req.user.id,
            },
          });
        } else {
          await prisma.e_kta.create({
            data: {
              tanggal: new Date(),
              personel_id: BigInt(existingPersonel.id),
              status: false,
              jenis_permintaan: 1,
              approval_history: approvalHistory,
              created_by: req.user.id,
            },
          });
        }

        return updatedJabatan;
      },
    );

    const message = convertToLogMessage(
      ConstantLogStatusEnum.SUCCESS,
      ConstantLogTypeEnum.CREATE_LOG_TYPE,
      ConstantLogDataTypeEnum.OBJECT,
      ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
    );
    await this.logsActivityService.addLogsActivity(
      convertToILogData(
        req,
        CONSTANT_LOG.MUTASI_JABATAN_CREATE as ConstantLogType,
        message,
        result,
      ),
    );

    return {
      statusCode: HttpStatus.OK,
      message,
      data: result,
    };
  }

  async validateSatuan(satuanId: number) {
    const validatedSatuan = await this.prisma.satuan.findFirst({
      where: {
        id: satuanId,
        deleted_at: null,
      },
    });

    if (!validatedSatuan) {
      throw new NotFoundException(
        `Satuan tidak ditemukan untuk satuan id ${satuanId}`,
      );
    }

    return validatedSatuan;
  }

  async validatePersonel(nrp: string) {
    const validatedPersonel = await this.prisma.personel.findFirst({
      select: {
        id: true,
        jabatan_personel: {
          select: {
            jabatans: {
              select: {
                id: true,
                satuan: true,
              },
            },
          },
        },
      },
      where: {
        nrp,
        deleted_at: null,
      },
    });

    if (!validatedPersonel) {
      throw new NotFoundException(`Personel tidak ditemukan untuk nrp ${nrp}`);
    }

    return validatedPersonel;
  }

  async createMutasiPolri(req, body: CreateMutasiJabatanDto) {
    const {
      personel_id,
      jabatan_id,
      tmt_jabatan,
      kep_nomor,
      kep_file,
      is_ps,
      st_no,
      st_file,
      keterangan,
    } = body;

    try {
      const existingPromotion = await this.prisma.jabatan_personel.findFirst({
        where: {
          personel_id,
          deleted_at: null,
          is_aktif: true,
        },
      });

      if (existingPromotion) {
        const existingTMT = new Date(existingPromotion.tmt_jabatan);
        const newTMT = new Date(tmt_jabatan);

        if (newTMT <= existingTMT) {
          throw new UnprocessableEntityException(
            'Tanggal TMT baru harus lebih besar dari TMT yang sudah ada',
          );
        }
      }

      const personel = await this.prisma.personel.findUnique({
        where: { id: personel_id, deleted_at: null },
      });

      if (!personel) {
        throw new NotFoundException(
          `Personel dengan ID ${personel_id} tidak ditemukan.`,
        );
      }

      const jabatan = await this.prisma.jabatan.findUnique({
        select: {
          id: true,
          nama: true,
          satuan: {
            select: {
              id: true,
            },
          },
        },
        where: { id: jabatan_id, deleted_at: null },
      });

      if (!jabatan) {
        throw new NotFoundException(
          `Jabatan dengan ID ${jabatan_id} tidak ditemukan.`,
        );
      }

      const levelSatuan =
        await this.prisma.mv_satuan_with_top_parents.findFirst({
          where: {
            id: jabatan.satuan.id,
          },
        });

      let operator;
      if (
        levelSatuan.third_top_parent_nama !==
          levelSatuan.second_top_parent_nama &&
        levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
      ) {
        operator = 'Operator Level 3 E-KTA';
      } else if (
        levelSatuan.third_top_parent_nama ===
          levelSatuan.second_top_parent_nama &&
        levelSatuan.second_top_parent_nama !== levelSatuan.top_parent_nama
      ) {
        operator = 'Operator Level 2 E-KTA';
      } else if (
        levelSatuan.third_top_parent_nama ===
          levelSatuan.second_top_parent_nama &&
        levelSatuan.second_top_parent_nama === levelSatuan.top_parent_nama
      ) {
        operator = 'Operator Level 1 E-KTA';
      }

      const approvalHistory = [
        {
          approval: operator,
          status: 'PENDING',
          comment: '',
          created_date: new Date().toISOString(),
          created_by: req.user.id,
        },
      ];

      const existPengajuanEkta = await this.prisma.e_kta.findFirst({
        select: {
          id: true,
          personel_id: true,
        },
        where: {
          personel_id,
          status: false,
          deleted_at: null,
        },
      });

      const result = await this.prisma.$transaction(
        async (prisma: PrismaService) => {
          if (existPengajuanEkta) {
            await prisma.e_kta.update({
              where: {
                id: existPengajuanEkta.id,
              },
              data: {
                personel_id: BigInt(personel_id),
                status: false,
                jenis_permintaan: 1,
                approval_history: approvalHistory,
                updated_at: new Date(),
                updated_by: req.user.id,
              },
            });
          } else {
            await prisma.e_kta.create({
              data: {
                tanggal: new Date(),
                personel_id: BigInt(personel_id),
                status: false,
                jenis_permintaan: 1,
                approval_history: approvalHistory,
                created_by: req.user.id,
              },
            });
          }

          if (existingPromotion) {
            await prisma.jabatan_personel.update({
              where: {
                id: existingPromotion.id,
              },
              data: {
                is_aktif: false,
                updated_at: new Date(),
              },
            });
          }

          const newPromotion = await this.prisma.jabatan_personel.create({
            data: {
              personel_id,
              jabatan_id,
              tmt_jabatan: new Date(tmt_jabatan),
              kep_nomor: kep_nomor,
              kep_file: kep_file,
              is_ps: Boolean(is_ps),
              st_no,
              st_file,
              keterangan,
              is_aktif: true,
            },
          });

          return newPromotion;
        },
      );

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.MUTASI_JABATAN_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.MUTASI_JABATAN_CREATE as ConstantLogType,
          message,
          result,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: result,
      };
    } catch (error) {
      throw error;
    }
  }
}
