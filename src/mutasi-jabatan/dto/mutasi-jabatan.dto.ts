import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';

export class GetAllMutasiJabatanDto {
  @IsOptional()
  type: string;
}

export class CreateMutasiJabatanDto {
  @IsNotEmpty()
  @IsNumber()
  personel_id: number;

  @IsNotEmpty()
  @IsNumber()
  jabatan_id: number;

  @IsNotEmpty()
  @IsDateString()
  tmt_jabatan: string;

  @IsNotEmpty()
  @IsString()
  kep_nomor: string;

  @IsOptional()
  @IsString()
  kep_file: string;

  @IsNotEmpty()
  @IsBoolean()
  is_ps: boolean;

  @IsOptional()
  @IsString()
  st_no: string;

  @IsOptional()
  @IsString()
  st_file: string;

  @IsOptional()
  @IsString()
  keterangan: string;
}

export class CreateMutasiSatuanAsnDto {

  @IsNotEmpty()
  @IsString()
  nrp: string;

  @IsNotEmpty()
  @IsDateString()
  tmt_mutasi: string;

  @IsNotEmpty()
  @IsBoolean()
  satuan_baru_id: number;

  @IsOptional()
  @IsString()
  keterangan: string;
}

