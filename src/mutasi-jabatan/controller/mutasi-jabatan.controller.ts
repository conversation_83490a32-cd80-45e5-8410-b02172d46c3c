import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { MutasiJabatanService } from '../service/mutasi-jabatan.service';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import {
  CreateMutasiSatuanAsnDto,
  CreateMutasiJabatanDto,
  GetAllMutasiJabatanDto,
} from '../dto/mutasi-jabatan.dto';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { Module, Permission } from '../../core/decorators';
import { MODULES } from '../../core/constants/module.constant';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { FieldValidatorPipe } from 'src/core/validator/field.validator';

@Controller('mutasi-jabatan')
@Module(MODULES.POSITION_TRANSFER)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class MutasiJabatanController {
  private readonly logger = new Logger(MutasiJabatanController.name);

  constructor(private readonly mutasiJabatanService: MutasiJabatanService) {}

  private async processExcelFile(
    req: any,
    file: Express.Multer.File,
  ): Promise<any> {
    this.logger.log(
      `Entering ${this.processExcelFile.name} with file buffer: ${file.buffer.length}`,
    );
    const response = await this.mutasiJabatanService.processExcelFile(
      req,
      file,
    );
    this.logger.log(
      `Leaving ${this.processExcelFile.name} with file buffer: ${file.buffer.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/asn')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async createMutasiAsn(
    @Req() req: any,
    @Body() body: CreateMutasiSatuanAsnDto,
  ) {
    const response = await this.mutasiJabatanService.createMutasiAsn(req, body);

    return response;
  }

  @Post('/asn/import')
  @Permission('PERMISSION_CREATE')
  @HttpCode(200)
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(xls|xlsx)$/)) {
          return callback(
            new BadRequestException('Only Excel files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async importAsn(@Req() req: any, @UploadedFile() file: Express.Multer.File) {
    this.logger.log(
      `Entering ${this.importAsn.name} with file buffer: ${file.buffer.length}`,
    );

    const response = await this.mutasiJabatanService.importAsn(req, file);

    this.logger.log(
      `Leaving ${this.importAsn.name} with file buffer: ${file.buffer.length} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAll(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
    @Query() query: GetAllMutasiJabatanDto,
  ) {
    this.logger.log(
      `Entering ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)}, search and sort data: ${JSON.stringify(searchandsortData)}, query: ${JSON.stringify(query)}`,
    );
    const response = await this.mutasiJabatanService.findAll(req, {
      pagination: paginationData,
      searchAndShort: searchandsortData,
      type: query.type,
    });
    this.logger.log(
      `Leaving ${this.findAll.name} with pagination data: ${JSON.stringify(paginationData)}, search and sort data: ${JSON.stringify(searchandsortData)}, query: ${JSON.stringify(query)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get('/polri')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findAllPolri(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.findAllPolri.name} with pagination data: ${JSON.stringify(paginationData)}, search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.mutasiJabatanService.findAllPolri(req, {
      pagination: paginationData,
      searchAndShort: searchandsortData,
    });

    this.logger.log(
      `Leaving ${this.findAllPolri.name} with pagination data: ${JSON.stringify(paginationData)}, search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findOne(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOne.name} with id: ${id}`);
    const response = await this.mutasiJabatanService.findOne(req, +id);
    this.logger.log(
      `Leaving ${this.findOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('polri/:id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async findOnePolri(@Req() req: any, @Param('id') id: string) {
    this.logger.log(`Entering ${this.findOnePolri.name} with id: ${id}`);
    const response = await this.mutasiJabatanService.findOnePolri(req, +id);
    this.logger.log(
      `Leaving ${this.findOnePolri.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Put('/:id/document')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: 'file_renskep', maxCount: 1 },
        { name: 'file_skep', maxCount: 1 },
        { name: 'file_telegram', maxCount: 1 },
        { name: 'file_winjak', maxCount: 1 },
        { name: 'file_petikan', maxCount: 1 },
      ],
      {
        fileFilter: (req, file, callback) => {
          if (!file.originalname.match(/\.(pdf|doc|docx)$/)) {
            return callback(
              new BadRequestException('Only PDF or Word files are allowed!'),
              false,
            );
          }
          callback(null, true);
        },
      },
    ),
  )
  async update(
    @Req() req: any,
    @Param('id') id: number,
    @UploadedFiles()
    files: {
      file_renskep: Express.Multer.File[];
      file_skep: Express.Multer.File[];
      file_telegram: Express.Multer.File[];
      file_winjak: Express.Multer.File[];
      file_petikan: Express.Multer.File[];
    },
  ) {
    this.logger.log(`Entering ${this.update.name} with id: ${id}`);
    const response = await this.mutasiJabatanService.updateDocument(
      req,
      files,
      id,
    );
    this.logger.log(
      `Leaving ${this.update.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get('/asn/last-update')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async lastUpdateAsn(@Req() req: any) {
    this.logger.log(`Entering ${this.lastUpdateAsn.name}`);
    const response = await this.mutasiJabatanService.getLastUpdateAsnPolri(req);
    this.logger.log(
      `Leaving ${this.lastUpdateAsn.name} with response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Post('/polri')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async createMutasiPolri(
    @Req() req: any,
    @Body(new FieldValidatorPipe()) body: CreateMutasiJabatanDto,
  ) {
    const response = await this.mutasiJabatanService.createMutasiPolri(
      req,
      body,
    );

    return response;
  }
}
