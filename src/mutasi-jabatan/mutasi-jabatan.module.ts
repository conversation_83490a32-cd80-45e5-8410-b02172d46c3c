import { forwardRef, Module } from '@nestjs/common';
import { MutasiJabatanService } from './service/mutasi-jabatan.service';
import { MutasiJabatanController } from './controller/mutasi-jabatan.controller';
import { PersonelModule } from 'src/personel/personel.module';
import { SatuanModule } from 'src/mdm/satuan/satuan/satuan.module';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { NotifikasiModule } from 'src/api-utils/notifikasi/notifikasi.module';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { ExcelModule } from '../api-utils/excel/excel.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [
    PrismaModule,
    PersonelModule,
    SatuanModule,
    NotifikasiModule,
    ExcelModule,
    LogsActivityModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [MutasiJabatanController],
  providers: [MutasiJabatanService, MinioService],
})
export class MutasiJabatanModule {}
