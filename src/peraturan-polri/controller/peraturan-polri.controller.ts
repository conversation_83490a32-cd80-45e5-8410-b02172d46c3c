import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpCode,
  Logger,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { bracketToObject } from '../../core/utils/common.utils';
import {
  CreatePeraturanPolriDto,
  UpdatePeraturanPolriDto,
} from '../dto/peraturan-polri.dto';
import { PeraturanPolriService } from '../service/peraturan-polri.service';
import { Permission } from 'src/core/decorators';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/core/guards/jwt-auth.guard';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';

@Controller('peraturan-polri')
@UseGuards(JwtAuthGuard)
@UsePipes(new ValidationPipe({ stopAtFirstError: true, transform: true }))
export class PeraturanPolriController {
  private readonly logger = new Logger(PeraturanPolriController.name);

  constructor(private readonly peraturanPolriService: PeraturanPolriService) {}

  @Post()
  @Permission('PERMISSION_CREATE')
  @UseGuards(JwtAuthGuard)
  @HttpCode(201)
  @UseInterceptors(AnyFilesInterceptor())
  async create(
    @Req() req: any,
    @Body() body: CreatePeraturanPolriDto,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    this.logger.log(
      `Entering ${this.create.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    const response = await this.peraturanPolriService.create(
      req,
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.create.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Put(':id')
  @Permission('PERMISSION_UPDATE')
  @HttpCode(200)
  @UseInterceptors(AnyFilesInterceptor())
  async update(
    @Req() req: any,
    @Param('id') id: string,
    @Body() body: UpdatePeraturanPolriDto,
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'application/pdf' }),
        ],
      }),
    )
    files: any,
  ) {
    files.map((file) => {
      const key = file.fieldname;
      delete file.fieldname;
      body[key] = file;
    });

    this.logger.log(
      `Entering ${this.update.name} with body: ${JSON.stringify(body)} and files length: ${files.length}`,
    );
    const response = await this.peraturanPolriService.update(
      req,
      Number(id),
      bracketToObject(body) as any,
    );
    this.logger.log(
      `Leaving ${this.update.name} with body: ${JSON.stringify(body)} and files length: ${files.length} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Delete(':id')
  @Permission('PERMISSION_DELETE')
  @HttpCode(200)
  async delete(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.delete.name} with id: ${id}`);
    const response = await this.peraturanPolriService.delete(req, Number(id));
    this.logger.log(
      `Leaving ${this.delete.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }

  @Get()
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.peraturanPolriService.getMany(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data: ${JSON.stringify(paginationData)} and search and sort data: ${JSON.stringify(searchandsortData)} and response: ${JSON.stringify(response)}`,
    );

    return response;
  }

  @Get(':id')
  @Permission('PERMISSION_READ')
  @HttpCode(200)
  async getOne(@Req() req: any, @Param('id') id: number) {
    this.logger.log(`Entering ${this.getOne.name} with id: ${id}`);
    const response = await this.peraturanPolriService.getOne(req, Number(id));
    this.logger.log(
      `Leaving ${this.getOne.name} with id: ${id} and response: ${JSON.stringify(response)}`,
    );
    return response;
  }
}
