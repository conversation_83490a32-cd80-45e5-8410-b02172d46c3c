import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/api-utils/prisma/service/prisma.service';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { SortSearchColumn } from 'src/core/utils/search.utils';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import {
  CreatePeraturanPolriDto,
  UpdatePeraturanPolriDto,
} from '../dto/peraturan-polri.dto';
import { LogsActivityService } from '../../api-utils/logs-activity/service/logs-activity.service';
import {
  convertToILogData,
  convertToLogMessage,
} from '../../core/utils/log.util';
import {
  ConstantLogDataTypeEnum,
  ConstantLogModuleEnum,
  ConstantLogStatusEnum,
  ConstantLogTypeEnum,
} from '../../core/enums/log.enum';
import { CONSTANT_LOG } from '../../core/constants/log.constant';
import { ConstantLogType } from '../../core/interfaces/log.type';

@Injectable()
export class PeraturanPolriService {
  constructor(
    private prisma: PrismaService,
    private readonly minioService: MinioService,
    private readonly logsActivityService: LogsActivityService,
  ) {}

  async create(req: any, body: CreatePeraturanPolriDto) {
    try {
      const uploadedFile = await this._uploadFiles(body.file_peraturan as any);

      const mockFilePeraturan = body.file_peraturan.map((_, i) => {
        const file = uploadedFile[i];
        return {
          ...file.rawFile,
          tanggal_penetapan: new Date(file.rawFile['tanggal_penetapan']),
          tanggal_berlaku: new Date(file.rawFile['tanggal_berlaku']),
          key: file.uploaded.Key,
          url: file.uploaded.Location,
          filename: file.uploaded.filename,
          created_at: new Date(),
          updated_at: new Date(),
        };
      });

      const queryResult = await this.prisma.peraturan_polri.create({
        data: {
          judul: body.judul,
          bentuk: body.bentuk,
          no_surat: body.no_surat,
          bentuk_singkat: body.bentuk_singkat,
          peraturan_polri_file: {
            createMany: { data: mockFilePeraturan as any },
          },
          created_at: new Date(),
          updated_at: new Date(),
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.CREATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERATURAN_POLRI_CREATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async update(req: any, id: number, body: UpdatePeraturanPolriDto) {
    try {
      const exist = await this.prisma.peraturan_polri.findFirst({
        where: { id },
      });
      if (!exist) {
        throw new NotFoundException(`peraturan ${id} tidak ditemukan`);
      }

      const {
        bentuk_singkat,
        bentuk,
        no_surat,
        deleted_file_id,
        file_peraturan,
        judul,
      } = body;

      const fileQuery: Record<string, any> = {};
      if (file_peraturan.length) {
        const uploadedFile = await this._uploadFiles(
          body.file_peraturan?.filter((d) => !!d?.['buffer']) as any,
        );

        body.file_peraturan.map((item, i) => {
          const file = uploadedFile[i];
          if (item.id) {
            delete file.rawFile['id'];

            fileQuery.update ||= [];
            fileQuery.update.push({
              where: { id: item.id },
              data: {
                ...file.rawFile,
                tanggal_penetapan: new Date(file.rawFile['tanggal_penetapan']),
                tanggal_berlaku: new Date(file.rawFile['tanggal_berlaku']),
                key: file.uploaded.Key,
                url: file.uploaded.Location,
                filename: file.uploaded.filename,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          } else {
            fileQuery.create ||= [];
            fileQuery.create.push({
              ...file.rawFile,
              tanggal_penetapan: new Date(file.rawFile['tanggal_penetapan']),
              tanggal_berlaku: new Date(file.rawFile['tanggal_berlaku']),
              key: file.uploaded.Key,
              url: file.uploaded.Location,
              filename: file.uploaded.filename,
              created_at: new Date(),
              updated_at: new Date(),
            });
          }
        });
      }

      if (deleted_file_id) {
        fileQuery.deleteMany = { id: { in: deleted_file_id } };
      }

      const queryResult = await this.prisma.peraturan_polri.update({
        where: { id },
        data: {
          bentuk_singkat,
          bentuk,
          no_surat,
          judul,
          peraturan_polri_file: fileQuery,
          updated_at: new Date(),
        },
      });
      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.UPDATE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERATURAN_POLRI_UPDATE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async delete(req: any, id: number) {
    try {
      const exist = await this.prisma.peraturan_polri.findFirst({
        where: { id },
      });
      if (!exist) {
        throw new NotFoundException(`peraturan ${id} tidak ditemukan`);
      }

      const queryResult = await this.prisma.peraturan_polri.update({
        where: { id },
        data: {
          deleted_at: new Date(),
          peraturan_polri_file: {
            updateMany: {
              where: { id_peraturan_polri: id },
              data: { deleted_at: new Date() },
            },
          },
        },
      });

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.DELETE_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERATURAN_POLRI_DELETE as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getOne(req: any, id: number) {
    try {
      const queryResult = await this.prisma.peraturan_polri.findFirst({
        select: {
          id: true,
          bentuk: true,
          judul: true,
          no_surat: true,
          bentuk_singkat: true,
          created_at: true,
          peraturan_polri_file: {
            select: {
              id: true,
              url: true,
              filename: true,
              tanggal_berlaku: true,
              tanggal_penetapan: true,
            },
          },
        },
        where: { id, deleted_at: null },
      });

      if (!queryResult) {
        throw new NotFoundException(`peraturan ${id} tidak ditemukan`);
      }

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.OBJECT,
        ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERATURAN_POLRI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        data: queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  async getMany(
    req: any,
    paginationData: PaginationDto,
    searchandsortData: SearchAndSortDTO,
  ) {
    try {
      const { sort_column, sort_desc, search_column, search_text, search } =
        searchandsortData;
      const columnMapping: {
        [key: string]: {
          field: string;
          type: 'string' | 'date' | 'boolean' | 'number';
        };
      } = {
        judul: { field: 'judul', type: 'string' },
        bentuk: { field: 'bentuk', type: 'date' },
        bentuk_singkat: { field: 'bentuk_singkat', type: 'date' },
      };
      const { orderBy, where } = SortSearchColumn(
        sort_column,
        sort_desc as any,
        search_column,
        search_text,
        search,
        columnMapping,
        true,
      );
      const limit = +paginationData.limit || 100;
      const page = +paginationData.page || 1;

      const [totalData, data] = await this.prisma.$transaction([
        this.prisma.peraturan_polri.count({ where }),
        this.prisma.peraturan_polri.findMany({
          select: {
            id: true,
            bentuk: true,
            judul: true,
            no_surat: true,
            bentuk_singkat: true,
            created_at: true,
            peraturan_polri_file: {
              select: {
                id: true,
                url: true,
                filename: true,
                tanggal_berlaku: true,
                tanggal_penetapan: true,
              },
            },
          },
          where,
          take: limit,
          skip: limit * (page - 1),
          orderBy,
        }),
      ]);

      const totalPage = Math.ceil(totalData / limit);
      data.forEach((item) => {
        const lowestTgl = { penetapan: null, berlaku: null };

        for (const file of item.peraturan_polri_file) {
          if (lowestTgl.penetapan < file.tanggal_penetapan) {
            lowestTgl.penetapan = file.tanggal_penetapan;
          }

          if (lowestTgl.berlaku < file.tanggal_berlaku) {
            lowestTgl.berlaku = file.tanggal_berlaku;
          }

          delete file.tanggal_berlaku;
          delete file.tanggal_penetapan;
        }

        item['active'] = lowestTgl.berlaku && new Date() > lowestTgl.berlaku;
        item['tanggal_penetapan'] = lowestTgl.penetapan;
        item['tanggal_berlaku'] = lowestTgl.berlaku;
      });

      const queryResult = {
        data,
        page,
        totalPage,
        totalData,
      };

      const message = convertToLogMessage(
        ConstantLogStatusEnum.SUCCESS,
        ConstantLogTypeEnum.READ_LOG_TYPE,
        ConstantLogDataTypeEnum.LIST,
        ConstantLogModuleEnum.PERATURAN_POLRI_MODULE,
      );
      await this.logsActivityService.addLogsActivity(
        convertToILogData(
          req,
          CONSTANT_LOG.PERATURAN_POLRI_READ as ConstantLogType,
          message,
          queryResult,
        ),
      );

      return {
        statusCode: HttpStatus.OK,
        message,
        ...queryResult,
      };
    } catch (err) {
      throw err;
    }
  }

  private async _uploadFiles(files: Express.Multer.File[]) {
    try {
      return await Promise.all(
        files.map(async (file) => {
          const uploaded = await this.minioService.uploadFile(file);
          delete file.buffer;
          delete file.fieldname;

          if (!uploaded.ETag) return { rawFile: file };

          return {
            rawFile: file,
            uploaded,
          };
        }),
      );
    } catch (err) {
      throw err;
    }
  }
}
