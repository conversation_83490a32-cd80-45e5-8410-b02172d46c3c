import { PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsString,
  Matches,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export const DateOnlyRegex = new RegExp(
  '\\d{4}-(1[0-2]|0[0-9])-(0[0-9]|[1-2][0-9]|3[0-1])',
  'g',
);

class FilePeraturanDto {
  @ValidateIf(({ value }) => !!value)
  @IsNumber()
  @Type(() => Number)
  id: number;

  @Matches(DateOnlyRegex, { message: 'Harus berupa format YYYY-MM-DD' })
  @IsNotEmpty()
  tanggal_penetapan: string;

  @Matches(DateOnlyRegex, { message: 'Harus berupa format YYYY-MM-DD' })
  @IsNotEmpty()
  tanggal_berlaku: string;
}

export class CreatePeraturanPolriDto {
  @IsNotEmpty()
  @IsString()
  judul: string;

  @IsNotEmpty()
  @IsString()
  no_surat: string;

  @IsNotEmpty()
  bentuk: string;

  @MaxLength(150)
  @IsNotEmpty()
  bentuk_singkat: string;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => FilePeraturanDto)
  file_peraturan: Array<FilePeraturanDto>;

  files?: Array<any>;
}

export class UpdatePeraturanPolriDto extends PartialType(
  CreatePeraturanPolriDto,
) {
  @ValidateIf(({ value }) => !!value)
  @IsArray()
  @IsNumber(
    { allowNaN: false, allowInfinity: false, maxDecimalPlaces: 0 },
    { each: true },
  )
  @Type(() => Number)
  deleted_file_id: Array<number>;
}
