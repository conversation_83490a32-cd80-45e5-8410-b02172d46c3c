import { forwardRef, Module } from '@nestjs/common';
import { MinioService } from 'src/api-utils/minio/service/minio.service';
import { PeraturanPolriController } from './controller/peraturan-polri.controller';
import { PeraturanPolriService } from './service/peraturan-polri.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { UsersModule } from '../access-management/users/users.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule, forwardRef(() => UsersModule)],
  controllers: [PeraturanPolriController],
  providers: [PeraturanPolriService, MinioService],
})
export class PeraturanPolriModule {}
