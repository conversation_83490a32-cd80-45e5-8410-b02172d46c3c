import { Module } from '@nestjs/common';
import { ERohaniController } from './controller/e-rohani.controller';
import { ERohaniService } from './service/e-rohani.service';
import { PrismaModule } from '../api-utils/prisma/prisma.module';
import { LogsActivityModule } from '../api-utils/logs-activity/logs-activity.module';

@Module({
  imports: [PrismaModule, LogsActivityModule],
  controllers: [ERohaniController],
  providers: [ERohaniService],
})
export class ERohaniModule {}
