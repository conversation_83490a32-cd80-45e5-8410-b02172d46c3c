import { Controller, Get, Logger, Query, Req, UseGuards } from '@nestjs/common';
import { PaginationDto, SearchAndSortDTO } from 'src/core/dtos';
import { ERohaniService } from '../service/e-rohani.service';
import { Module, Permission } from '../../core/decorators';
import { JwtAuthGuard } from '../../core/guards/jwt-auth.guard';
import { PermissionGuard } from '../../core/guards/permission-auth.guard';
import { MODULES } from '../../core/constants/module.constant';

@Controller('e-rohani')
@Module(MODULES.E_SPIRITUAL)
@UseGuards(JwtAuthGuard, PermissionGuard)
export class ERohaniController {
  private readonly logger = new Logger(ERohaniController.name);

  constructor(private readonly eRohani: ERohaniService) {}

  @Get('/')
  @Permission('PERMISSION_READ')
  async getList(
    @Req() req: any,
    @Query() paginationData: PaginationDto,
    @Query() searchandsortData: SearchAndSortDTO,
  ) {
    this.logger.log(
      `Entering ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)}`,
    );
    const response = await this.eRohani.getList(
      req,
      paginationData,
      searchandsortData,
    );
    this.logger.log(
      `Leaving ${this.getList.name} with pagination data ${JSON.stringify(paginationData)} and search and sort data ${JSON.stringify(searchandsortData)} and response ${JSON.stringify(response)}`,
    );
    return response;
  }
}
