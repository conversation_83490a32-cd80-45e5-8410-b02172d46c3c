import { PrismaClient } from '@prisma/client';

// initialize the Prisma Client
const prisma = new PrismaClient();

// const roundsOfHashing = 10;

async function main() {
  await Promise.all([
    prisma.level.createMany({
      data: [
        { nama: 'Superadmin' },
        { nama: 'Level 1' },
        { nama: 'Level 2' },
        { nama: 'Level 3' },
      ],
      skipDuplicates: true,
    }),
    // prisma.permission.createMany({
    //   data: [
    //     { nama: 'PERMISSION_CREATE', desc: 'Permission Create' },
    //     { nama: 'PERMISSION_READ', desc: 'Permission Read' },
    //     { nama: 'PERMISSION_UPDATE', desc: 'Permission Update' },
    //     { nama: 'PERMISSION_DELETE', desc: 'Permission Delete' },
    //   ],
    //   skipDuplicates: true,
    // }),
    prisma.role_tipe.createMany({
      data: [
        { nama: 'Admin' },
        { nama: 'Operator' },
        { nama: 'Operator AKPOL' },
        { nama: 'Operator SIPSS' },
      ],
      skipDuplicates: true,
    }),
  ]);

  const user = await prisma.users.create({
    data: {
      password: '$2b$10$EPq4781gl7.7rzl8BK8EXev5dT1JoqUAeojG9NPnec0vx2cNW2tzO', //password
      personel: {
        create: {
          uid: '1ef62c08-d9b1-4d0c-9cdc-3b4513502b3e',
          nrp: '12345678',
          nama_lengkap: 'SUPER ADMIN',
          tanggal_lahir: new Date(),
          jenis_kelamin: 'LAKI_LAKI',
        },
      },
    },
  });

  const role = await prisma.role.create({
    data: {
      is_admin: true,
      nama: 'Admin Baginfopers',
      bagian: {
        create: {
          nama: 'BAGINFOPERS',
        },
      },
      role_tipe: { connect: { id: 1 } },
    },
  });

  await prisma.role_permission.createMany({
    data: [
      { role_id: role.id, permission_id: 1 },
      { role_id: role.id, permission_id: 2 },
      { role_id: role.id, permission_id: 3 },
      { role_id: role.id, permission_id: 4 },
    ],
  });

  await prisma.users_role.create({
    data: {
      level_id: 1,
      role_id: role.id,
      users_id: user.id,
    },
  });

  await prisma.survey_agg.upsert({
    where: { id: BigInt(1) },
    update: {},
    create: {
      total: 0,
      respondens: 0,
      responses: 0,
    },
  });

  console.log('Data seeding completed successfully.');
}

// execute the main function
main()
  .catch((e) => {
    console.error(e);
    process.exit(1); // Exit with failure
  })
  .finally(async () => {
    await prisma.$disconnect(); // Disconnect Prisma Client
  });
