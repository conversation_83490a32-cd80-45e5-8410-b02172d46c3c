-- CreateEnum
CREATE TYPE "kategori_kerjasama_file" AS ENUM ('MOU', 'PKS');

-- CreateEnum
CREATE TYPE "Method" AS ENUM ('POST', 'GET');

-- CreateEnum
CREATE TYPE "StatusParent" AS ENUM ('HIDUP', 'MENINGGAL');

-- CreateEnum
CREATE TYPE "JenisKelamin" AS ENUM ('LAKI-LAKI', 'PEREMPUAN');

-- CreateEnum
CREATE TYPE "StatusPengajuan" AS ENUM ('PROSES', 'DISETUJUI', 'DITOLAK', 'SELESAI');

-- CreateEnum
CREATE TYPE "JenisPengajuan" AS ENUM ('NIKAH', 'CERAI', 'RUJUK');

-- CreateEnum
CREATE TYPE "workflow_jenis_surat" AS ENUM ('SURAT_MASUK', 'SURAT_KELUAR');

-- CreateEnum
CREATE TYPE "workflow_personel_status" AS ENUM ('WAITING', 'NEED_SIGN', 'REJECTED', 'DONE');

-- C<PERSON>Enum
CREATE TYPE "workflow_status" AS ENUM ('WAITING', 'ON_PROCESS', 'REJECTED', 'DONE');

-- CreateEnum
CREATE TYPE "JenisSeleksiBagrimdik" AS ENUM ('LATSAR', 'DIKBANGUM');

-- CreateEnum
CREATE TYPE "StatusKonsultasi" AS ENUM ('PENGAJUAN', 'SEDANG_BERLANGSUNG', 'MENUNGGU_HASIL', 'SELESAI');

-- CreateTable
CREATE TABLE "personel"
(
    "id"                    BIGSERIAL NOT NULL,
    "uid"                   UUID      NOT NULL,
    "nrp"                   VARCHAR(255),
    "nama_lengkap"          VARCHAR(255),
    "tanggal_lahir"         DATE,
    "jenis_kelamin"         VARCHAR(255),
    "foto_file"             VARCHAR(255),
    "tempat_lahir"          VARCHAR(255),
    "agama_id"              BIGINT,
    "ktp_nomor"             VARCHAR(255),
    "ktp_file"              VARCHAR(255),
    "kk_nomor"              VARCHAR(255),
    "kk_file"               VARCHAR(255),
    "status_kawin_id"       BIGINT,
    "golongan_darah"        VARCHAR(255),
    "suku_id"               BIGINT,
    "email"                 VARCHAR(255),
    "no_hp"                 VARCHAR(255),
    "akta_kelahiran_file"   VARCHAR(255),
    "asabri_nomor"          VARCHAR(255),
    "asabri_file"           VARCHAR(255),
    "bpjs_nomor"            VARCHAR(255),
    "bpjs_file"             VARCHAR(255),
    "paspor_nomor"          VARCHAR(255),
    "paspor_file"           VARCHAR(255),
    "npwp_nomor"            VARCHAR(255),
    "npwp_file"             VARCHAR(255),
    "lhkpn_file"            VARCHAR(255),
    "masa_dinas_surut_tmt"  DATE,
    "masa_dinas_surut_file" VARCHAR(255),
    "anak_ke"               INTEGER,
    "jumlah_saudara"        INTEGER,
    "status_aktif_id"       BIGINT,
    "created_at"            TIMESTAMP(0),
    "updated_at"            TIMESTAMP(0),
    "deleted_at"            TIMESTAMP(0),

    CONSTRAINT "personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agama"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "agama_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "berita"
(
    "id"            BIGSERIAL    NOT NULL,
    "title"         TEXT         NOT NULL,
    "description"   TEXT         NOT NULL,
    "satuan_id"     BIGINT,
    "kategori_id"   BIGINT,
    "is_publish"    BOOLEAN      NOT NULL DEFAULT false,
    "publish_at"    TIMESTAMP(0),
    "created_by_id" BIGINT       NOT NULL,
    "created_at"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "berita_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "berita_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "berita_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "berita_foto"
(
    "id"            BIGSERIAL    NOT NULL,
    "id_berita"     BIGINT       NOT NULL,
    "jenis_dokumen" TEXT,
    "originalname"  TEXT,
    "encoding"      TEXT,
    "mimetype"      TEXT,
    "destination"   TEXT,
    "filename"      TEXT,
    "path"          TEXT,
    "size"          BIGINT,
    "key"           TEXT,
    "url"           TEXT,
    "created_at"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "berita_foto_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "lokasi_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangspes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes_lokasi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangspes_lokasi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "dikbangspes_id"       BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),

    CONSTRAINT "dikbangspes_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum"
(
    "id"                    BIGSERIAL    NOT NULL,
    "nama"                  VARCHAR(255) NOT NULL,
    "nama_alternatif"       VARCHAR(255) NOT NULL,
    "dikbangum_kategori_id" BIGINT,
    "created_at"            TIMESTAMP(0),
    "updated_at"            TIMESTAMP(0),
    "deleted_at"            TIMESTAMP(0),

    CONSTRAINT "dikbangum_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangum_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "dikbangum_id"         BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),
    "gelar_id"             BIGINT,
    "is_tampil_gelar"      BOOLEAN,

    CONSTRAINT "dikbangum_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk"
(
    "id"          BIGSERIAL    NOT NULL,
    "nama"        VARCHAR(255) NOT NULL,
    "kategori_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "diktuk_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "diktuk_id"            BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "gelar_id"             BIGINT,
    "is_tampil_gelar"      BOOLEAN DEFAULT true,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),

    CONSTRAINT "diktuk_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "eselon"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "eselon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gelar"
(
    "id"                BIGSERIAL    NOT NULL,
    "nama"              VARCHAR(255) NOT NULL,
    "is_gelar_belakang" BOOLEAN      NOT NULL DEFAULT true,
    "created_at"        TIMESTAMP(0),
    "updated_at"        TIMESTAMP(0),
    "deleted_at"        TIMESTAMP(0),

    CONSTRAINT "gelar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "golongan"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "golongan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan"
(
    "id"             BIGSERIAL NOT NULL,
    "nama"           VARCHAR(255),
    "dsp"            INTEGER,
    "nivellering_id" BIGINT,
    "satuan_id"      BIGINT    NOT NULL,
    "atasan_id"      BIGINT,
    "kategori_id"    BIGINT    NOT NULL,
    "created_at"     TIMESTAMP(0),
    "updated_at"     TIMESTAMP(0),
    "deleted_at"     TIMESTAMP(0),
    "is_aktif"       BOOLEAN,

    CONSTRAINT "jabatan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "jabatan_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan_personel"
(
    "id"          BIGSERIAL NOT NULL,
    "personel_id" BIGINT,
    "jabatan_id"  BIGINT,
    "tmt_jabatan" DATE,
    "jabatan"     VARCHAR(255),
    "skep_no"     VARCHAR(255),
    "skep_file"   VARCHAR(255),
    "is_ps"       BOOLEAN,
    "keterangan"  VARCHAR(255),
    "st_no"       VARCHAR(255),
    "st_file"     VARCHAR(255),
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "test_jabatan_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kabupaten"
(
    "id"          BIGSERIAL    NOT NULL,
    "nama"        VARCHAR(255) NOT NULL,
    "provinsi_id" BIGINT,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "kabupaten_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "negara"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "negara_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nivellering"
(
    "id"            BIGSERIAL    NOT NULL,
    "nama"          VARCHAR(255) NOT NULL,
    "eselon_id"     BIGINT       NOT NULL,
    "nivel_up_id"   BIGINT,
    "nivel_down_id" BIGINT,
    "created_at"    TIMESTAMP(0),
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "nivellering_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat"
(
    "id"           BIGSERIAL    NOT NULL,
    "nama"         VARCHAR(255) NOT NULL,
    "nama_singkat" VARCHAR(255) NOT NULL,
    "kategori_id"  BIGINT,
    "golongan_id"  BIGINT,
    "created_at"   TIMESTAMP(0),
    "updated_at"   TIMESTAMP(0),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "pangkat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "pangkat_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat_personel"
(
    "id"          BIGSERIAL NOT NULL,
    "pangkat_id"  BIGINT    NOT NULL,
    "tmt"         VARCHAR(255),
    "kep_nomor"   VARCHAR(255),
    "kep_file"    VARCHAR(255),
    "kep_tanggal" VARCHAR(255),
    "personel_id" BIGINT    NOT NULL,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "pangkat_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pengajuan_pengakhiran_dinas"
(
    "id"                                                    BIGSERIAL NOT NULL,
    "personel_id"                                           BIGINT,
    "pengakhiran_dinas"                                     TEXT,
    "status_pengajuan"                                      TEXT,
    "surat_kkep_file"                                       TEXT,
    "tanggal_surat"                                         DATE,
    "tanggal_diterima"                                      DATE,
    "kasus"                                                 TEXT,
    "dokumen_putusan_kkep_nama"                             TEXT,
    "dokumen_putusan_kkep_file"                             TEXT,
    "dokumen_putusan_kkep_date"                             DATE,
    "dokumen_putusan_kkep_status"                           TEXT,
    "dokumen_putusan_kkep_alasan_penolakan"                 TEXT,
    "dokumen_surat_usulan_nama"                             TEXT,
    "dokumen_surat_usulan_file"                             TEXT,
    "dokumen_surat_usulan_date"                             DATE,
    "dokumen_surat_usulan_status"                           TEXT,
    "dokumen_surat_usulan_alasan_penolakan"                 TEXT,
    "dokumen_berkas_pemeriksaan_nama"                       TEXT,
    "dokumen_berkas_pemeriksaan_file"                       TEXT,
    "dokumen_berkas_pemeriksaan_date"                       DATE,
    "dokumen_berkas_pemeriksaan_status"                     TEXT,
    "dokumen_berkas_pemeriksaan_alasan_penolakan"           TEXT,
    "dokumen_kep_pengangkatan_pertama_nama"                 TEXT,
    "dokumen_kep_pengangkatan_pertama_file"                 TEXT,
    "dokumen_kep_pengangkatan_pertama_date"                 DATE,
    "dokumen_kep_pengangkatan_pertama_status"               TEXT,
    "dokumen_kep_pengangkatan_pertama_alasan_penolakan"     TEXT,
    "dokumen_keputusan_pangkat_terakhir_nama"               TEXT,
    "dokumen_keputusan_pangkat_terakhir_file"               TEXT,
    "dokumen_keputusan_pangkat_terakhir_date"               DATE,
    "dokumen_keputusan_pangkat_terakhir_status"             TEXT,
    "dokumen_keputusan_pangkat_terakhir_alasan_penolakan"   TEXT,
    "dokumen_kartu_tanda_peserta_nama"                      TEXT,
    "dokumen_kartu_tanda_peserta_file"                      TEXT,
    "dokumen_kartu_tanda_peserta_date"                      DATE,
    "dokumen_kartu_tanda_peserta_status"                    TEXT,
    "dokumen_kartu_tanda_peserta_alasan_penolakan"          TEXT,
    "dokumen_surat_keterangan_tidak_layak_nama"             TEXT,
    "dokumen_surat_keterangan_tidak_layak_file"             TEXT,
    "dokumen_surat_keterangan_tidak_layak_date"             DATE,
    "dokumen_surat_keterangan_tidak_layak_status"           TEXT,
    "dokumen_surat_keterangan_tidak_layak_alasan_penolakan" TEXT,
    "dokumen_putusan_pengadilan_nama"                       TEXT,
    "dokumen_putusan_pengadilan_file"                       TEXT,
    "dokumen_putusan_pengadilan_date"                       DATE,
    "dokumen_putusan_pengadilan_status"                     TEXT,
    "dokumen_putusan_pengadilan_alasan_penolakan"           TEXT,
    "dokumen_pengambilan_barang_nama"                       TEXT,
    "dokumen_pengambilan_barang_file"                       TEXT,
    "dokumen_pengambilan_barang_date"                       DATE,
    "dokumen_pengambilan_barang_status"                     TEXT,
    "dokumen_pengambilan_barang_alasan_penolakan"           TEXT,
    "dokumen_kkep_tingkat_banding_nama"                     TEXT,
    "dokumen_kkep_tingkat_banding_file"                     TEXT,
    "dokumen_kkep_tingkat_banding_date"                     DATE,
    "dokumen_kkep_tingkat_banding_status"                   TEXT,
    "dokumen_kkep_tingkat_banding_alasan_penolakan"         TEXT,
    "dokumen_bap_nama"                                      TEXT,
    "dokumen_bap_file"                                      TEXT,
    "dokumen_bap_date"                                      DATE,
    "dokumen_bap_status"                                    TEXT,
    "dokumen_bap_alasan_penolakan"                          TEXT,
    "dokumen_ptdh_nama"                                     TEXT,
    "dokumen_ptdh_file"                                     TEXT,
    "dokumen_ptdh_date"                                     DATE,
    "dokumen_ptdh_status"                                   TEXT,
    "dokumen_ptdh_alasan_penolakan"                         TEXT,
    "dokumen_petikan_ptdh_nama"                             TEXT,
    "dokumen_petikan_ptdh_file"                             TEXT,
    "dokumen_petikan_ptdh_date"                             DATE,
    "dokumen_petikan_ptdh_status"                           TEXT,
    "dokumen_petikan_ptdh_alasan_penolakan"                 TEXT,
    "dokumen_salinan_ptdh_nama"                             TEXT,
    "dokumen_salinan_ptdh_file"                             TEXT,
    "dokumen_salinan_ptdh_date"                             DATE,
    "dokumen_salinan_ptdh_status"                           TEXT,
    "dokumen_salinan_ptdh_alasan_penolakan"                 TEXT,
    "submit_deadline_date"                                  DATE,
    "completed_at"                                          DATE,
    "created_at"                                            DATE,
    "updated_at"                                            DATE,
    "deleted_at"                                            DATE,

    CONSTRAINT "pengajuan_pengakhiran_dinas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provinsi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "negara_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "provinsi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "satuan"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "alamat"     TEXT,
    "jenis_id"   BIGINT       NOT NULL,
    "is_aktif"   BOOLEAN      NOT NULL,
    "atasan_id"  BIGINT,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "satuan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "satuan_hirarki"
(
    "satuan_id" INTEGER NOT NULL,
    "atasan_id" INTEGER[],

    CONSTRAINT "satuan_hirarki_pkey" PRIMARY KEY ("satuan_id")
);

-- CreateTable
CREATE TABLE "satuan_jenis"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "satuan_jenis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "status_aktif"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "status_aktif_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "status_kawin"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "status_kawin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "suku"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "suku_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users"
(
    "id"          BIGSERIAL    NOT NULL,
    "password"    VARCHAR(255) NOT NULL,
    "personel_id" BIGINT,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users_role"
(
    "id"         BIGSERIAL NOT NULL,
    "users_id"   BIGINT,
    "level_id"   INTEGER,
    "role_id"    INTEGER,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "users_role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "level"
(
    "id"         SERIAL       NOT NULL,
    "nama"       TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "level_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role"
(
    "id"           SERIAL       NOT NULL,
    "nama"         TEXT         NOT NULL,
    "bagian_id"    INTEGER      NOT NULL,
    "is_admin"     BOOLEAN      NOT NULL,
    "satuan_id"    BIGINT,
    "role_tipe_id" INTEGER      NOT NULL,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_tipe"
(
    "id"         SERIAL       NOT NULL,
    "nama"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "role_tipe_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagian"
(
    "id"         SERIAL       NOT NULL,
    "nama"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "bagian_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permission"
(
    "id"         SERIAL       NOT NULL,
    "nama"       TEXT         NOT NULL,
    "desc"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permission"
(
    "id"            SERIAL       NOT NULL,
    "permission_id" INTEGER      NOT NULL,
    "role_id"       INTEGER      NOT NULL,
    "created_at"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "role_permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "logs_activity"
(
    "id"         BIGSERIAL    NOT NULL,
    "user_id"    BIGINT       NOT NULL,
    "activity"   TEXT         NOT NULL,
    "detail"     TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "logs_activity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kerjasama"
(
    "id"                 BIGSERIAL    NOT NULL,
    "institusi"          VARCHAR(255) NOT NULL,
    "tanggal_mulai"      DATE         NOT NULL,
    "tanggal_selesai"    DATE,
    "durasi_hari"        INTEGER,
    "status_id"          INTEGER      NOT NULL,
    "kategori_file"      "kategori_kerjasama_file",
    "jenis_institusi_id" INTEGER      NOT NULL,
    "created_at"         TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"         TIMESTAMP(3),
    "deleted_at"         TIMESTAMP(0),

    CONSTRAINT "kerjasama_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kerjasama_file_pks"
(
    "id"              BIGSERIAL    NOT NULL,
    "id_kerjasama"    BIGINT       NOT NULL,
    "originalname"    TEXT,
    "encoding"        TEXT,
    "mimetype"        TEXT,
    "destination"     TEXT,
    "filename"        TEXT,
    "path"            TEXT,
    "size"            BIGINT,
    "key"             TEXT,
    "url"             TEXT,
    "tanggal_mulai"   DATE         NOT NULL,
    "tanggal_selesai" DATE         NOT NULL,
    "created_at"      TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"      TIMESTAMP(0),
    "deleted_at"      TIMESTAMP(0),

    CONSTRAINT "kerjasama_file_pks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kerjasama_file_mou"
(
    "id"           BIGSERIAL    NOT NULL,
    "id_kerjasama" BIGINT       NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         BIGINT,
    "key"          TEXT,
    "url"          TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "kerjasama_file_mou_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kerjasama_durasi_bulan"
(
    "id"         SERIAL       NOT NULL,
    "durasi"     INTEGER      NOT NULL,
    "display"    VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "kerjasama_durasi_bulan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kerjasama_status"
(
    "id"         SERIAL       NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "kerjasama_status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jenis_institusi"
(
    "id"         SERIAL       NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "jenis_institusi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tanhor"
(
    "id"         SERIAL       NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "tanhor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tanhor_personel"
(
    "id"          BIGSERIAL    NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "tanhor_id"   INTEGER      NOT NULL,
    "tgl_tanhor"  DATE         NOT NULL,
    "surat_no"    VARCHAR(255) NOT NULL,
    "surat_file"  VARCHAR(255) NOT NULL,
    "created_at"  TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "tanhor_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "penghargaan"
(
    "id"         SERIAL       NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "penghargaan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "penghargaan_tingkat"
(
    "id"         SERIAL       NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "penghargaan_tingkat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_mddp_personel"
(
    "id"                  SERIAL           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "lama_menjabat_bulan" INTEGER          NOT NULL,
    "score_mddp"          DOUBLE PRECISION NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "score_mddp_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_nilai_smk_personel"
(
    "id"              BIGSERIAL        NOT NULL,
    "personel_id"     BIGINT           NOT NULL,
    "tahun"           INTEGER          NOT NULL,
    "semester"        INTEGER          NOT NULL,
    "nilai"           DOUBLE PRECISION NOT NULL,
    "score_nilai_smk" DOUBLE PRECISION NOT NULL,
    "created_at"      TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"      TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "score_nilai_smk_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_nieve_personel"
(
    "id"                  SERIAL           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "pangkat_id"          BIGINT           NOT NULL,
    "tmt"                 TIMESTAMP(3)     NOT NULL,
    "lama_menjabat_bulan" INTEGER          NOT NULL,
    "score_nievelering"   DOUBLE PRECISION NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "score_nieve_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_rank_tahun_dikpol"
(
    "id"                 SERIAL       NOT NULL,
    "personel_id"        BIGINT       NOT NULL,
    "diktuk_id"          BIGINT       NOT NULL,
    "dikbangum_id"       BIGINT       NOT NULL,
    "diklat_tingkat"     TEXT         NOT NULL,
    "tanggal_masuk"      TIMESTAMP(3) NOT NULL,
    "tanggal_selesai"    TIMESTAMP(3) NOT NULL,
    "ranking"            INTEGER      DEFAULT 0,
    "jumlah_siswa"       INTEGER      NOT NULL,
    "lama_dikpol_bulan"  INTEGER      NOT NULL,
    "score_tahun_dikpol" DOUBLE PRECISION,
    "score_rank_dikpol"  DOUBLE PRECISION,
    "created_at"         TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"         TIMESTAMP(3),
    "deleted_at"         TIMESTAMP(0),

    CONSTRAINT "score_rank_tahun_dikpol_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_sespimma_personel"
(
    "id"                           SERIAL           NOT NULL,
    "dikbangum_id"                 BIGINT           NOT NULL,
    "personel_id"                  BIGINT           NOT NULL,
    "tanggal_masuk"                TIMESTAMP(3)     NOT NULL,
    "tanggal_selesai"              TIMESTAMP(3)     NOT NULL,
    "ranking"                      INTEGER          NOT NULL,
    "jumlah_siswa"                 INTEGER          NOT NULL,
    "lama_dikpol_bulan"            INTEGER          NOT NULL,
    "bbt_dikpol"                   INTEGER          NOT NULL,
    "pembagi_bbt_dikpol"           INTEGER          NOT NULL,
    "bbt_final_dikpol"             DOUBLE PRECISION NOT NULL,
    "std_dikpol"                   INTEGER          NOT NULL,
    "score_sespimma"               DOUBLE PRECISION,
    "konstanta_rank"               DOUBLE PRECISION NOT NULL,
    "bbt_rank"                     DOUBLE PRECISION NOT NULL,
    "std_rank"                     INTEGER          NOT NULL,
    "std_js"                       INTEGER          NOT NULL,
    "score_rank_sespimma"          DOUBLE PRECISION,
    "konst_rank_nivel_up"          DOUBLE PRECISION NOT NULL,
    "bbt_rank_nivel_up"            DOUBLE PRECISION NOT NULL,
    "score_rank_sespimma_nivel_up" DOUBLE PRECISION NOT NULL,
    "created_at"                   TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                   TIMESTAMP(3),
    "deleted_at"                   TIMESTAMP(0),

    CONSTRAINT "score_sespimma_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_sespimmen_personel"
(
    "id"                            SERIAL           NOT NULL,
    "dikbangum_id"                  BIGINT           NOT NULL,
    "personel_id"                   BIGINT           NOT NULL,
    "tanggal_masuk"                 TIMESTAMP(3)     NOT NULL,
    "tanggal_selesai"               TIMESTAMP(3)     NOT NULL,
    "ranking"                       INTEGER          NOT NULL,
    "jumlah_siswa"                  INTEGER          NOT NULL,
    "lama_dikpol_bulan"             INTEGER          NOT NULL,
    "bbt_dikpol"                    INTEGER          NOT NULL,
    "pembagi_bbt_dikpol"            INTEGER          NOT NULL,
    "bbt_final_dikpol"              DOUBLE PRECISION NOT NULL,
    "std_dikpol"                    INTEGER          NOT NULL,
    "score_sespimmen"               DOUBLE PRECISION,
    "konstanta_rank"                DOUBLE PRECISION,
    "bbt_rank"                      DOUBLE PRECISION,
    "std_rank"                      INTEGER,
    "std_js"                        INTEGER,
    "score_rank_sespimmen"          DOUBLE PRECISION,
    "konst_rank_nivel_up"           DOUBLE PRECISION,
    "bbt_rank_nivel_up"             DOUBLE PRECISION,
    "score_rank_sespimmen_nivel_up" DOUBLE PRECISION,
    "created_at"                    TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                    TIMESTAMP(3),
    "deleted_at"                    TIMESTAMP(0),

    CONSTRAINT "score_sespimmen_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_sespimti_personel"
(
    "id"                           SERIAL           NOT NULL,
    "dikbangum_id"                 BIGINT           NOT NULL,
    "personel_id"                  BIGINT           NOT NULL,
    "tanggal_masuk"                TIMESTAMP(3)     NOT NULL,
    "tanggal_selesai"              TIMESTAMP(3)     NOT NULL,
    "ranking"                      INTEGER          NOT NULL,
    "jumlah_siswa"                 INTEGER          NOT NULL,
    "lama_dikpol_bulan"            INTEGER          NOT NULL,
    "bbt_dikpol"                   INTEGER          NOT NULL,
    "pembagi_bbt_dikpol"           INTEGER          NOT NULL,
    "bbt_final_dikpol"             DOUBLE PRECISION NOT NULL,
    "std_dikpol"                   INTEGER          NOT NULL,
    "score_sespimti"               DOUBLE PRECISION,
    "konstanta_rank"               DOUBLE PRECISION NOT NULL,
    "bbt_rank"                     DOUBLE PRECISION NOT NULL,
    "std_rank"                     INTEGER          NOT NULL,
    "std_js"                       INTEGER          NOT NULL,
    "score_rank_sespimti"          DOUBLE PRECISION,
    "konst_rank_nivel_up"          DOUBLE PRECISION NOT NULL,
    "bbt_rank_nivel_up"            DOUBLE PRECISION,
    "score_rank_sespimti_nivel_up" DOUBLE PRECISION,
    "created_at"                   TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                   TIMESTAMP(3),
    "deleted_at"                   TIMESTAMP(0),

    CONSTRAINT "score_sespimti_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_dikbangspes_personel"
(
    "id"                     SERIAL           NOT NULL,
    "personel_id"            BIGINT           NOT NULL,
    "dikbangspes_tingkat_id" BIGINT           NOT NULL,
    "lama_dikbangspes_hari"  INTEGER          NOT NULL,
    "score_dikbangspes"      DOUBLE PRECISION NOT NULL,
    "created_at"             TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"             TIMESTAMP(3),
    "deleted_at"             TIMESTAMP(0),

    CONSTRAINT "score_dikbangspes_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_penghargaan_personel"
(
    "id"                 SERIAL           NOT NULL,
    "personel_id"        BIGINT           NOT NULL,
    "level_penghargaan"  CHAR(255)        NOT NULL,
    "jumlah_penghargaan" INTEGER          NOT NULL,
    "score_penghargaan"  DOUBLE PRECISION NOT NULL,
    "created_at"         TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"         TIMESTAMP(3),
    "deleted_at"         TIMESTAMP(0),

    CONSTRAINT "score_penghargaan_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_lama_dinas_personel"
(
    "id"                  SERIAL           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "jabatan"             TEXT             NOT NULL,
    "fungsi"              TEXT             NOT NULL,
    "tempat_dinas"        TEXT,
    "lama_menjabat_bulan" BIGINT           NOT NULL,
    "tmt_jabatan"         TIMESTAMP(3)     NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "score_lama_dinas_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_lama_dinas_personel_kawaka_fungsi"
(
    "id"                  SERIAL           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "jabatan"             TEXT             NOT NULL,
    "tmt_jabatan"         TIMESTAMP(3)     NOT NULL,
    "fungsi"              TEXT             NOT NULL,
    "tempat_dinas"        TEXT,
    "lama_menjabat_bulan" BIGINT           NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "score_lama_dinas_personel_kawaka_fungsi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score_lama_dinas_personel_kawaka"
(
    "id"                  SERIAL           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "jabatan"             TEXT             NOT NULL,
    "tmt_jabatan"         TIMESTAMP(3)     NOT NULL,
    "tempat_dinas"        TEXT,
    "lama_menjabat_bulan" BIGINT           NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "score_lama_dinas_personel_kawaka_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_score_compilation_complete"
(
    "id"                  BIGINT           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "nrp"                 TEXT             NOT NULL,
    "nama_lengkap"        TEXT             NOT NULL,
    "nievelering_terkini" TEXT,
    "pangkat_terkini"     TEXT,
    "jabatan_terkini"     TEXT             NOT NULL,
    "fungsi"              TEXT,
    "score_nilai_smk"     DOUBLE PRECISION NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "score_nievelering"   DOUBLE PRECISION NOT NULL,
    "score_mddp"          DOUBLE PRECISION NOT NULL,
    "score_tahun_dikpol"  DOUBLE PRECISION NOT NULL,
    "score_rank_dikpol"   DOUBLE PRECISION NOT NULL,
    "score_dikbangspes"   DOUBLE PRECISION NOT NULL,
    "score_penghargaan"   DOUBLE PRECISION NOT NULL,
    "total_score"         DOUBLE PRECISION NOT NULL,

    CONSTRAINT "personel_score_compilation_complete_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_score_compilation_complete_kawaka"
(
    "id"                  BIGINT           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "nrp"                 TEXT             NOT NULL,
    "nama_lengkap"        TEXT             NOT NULL,
    "nievelering_terkini" TEXT,
    "pangkat_terkini"     TEXT,
    "jabatan_terkini"     TEXT             NOT NULL,
    "score_nilai_smk"     DOUBLE PRECISION NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "score_nievelering"   DOUBLE PRECISION NOT NULL,
    "score_mddp"          DOUBLE PRECISION NOT NULL,
    "score_tahun_dikpol"  DOUBLE PRECISION NOT NULL,
    "score_rank_dikpol"   DOUBLE PRECISION NOT NULL,
    "score_dikbangspes"   DOUBLE PRECISION NOT NULL,
    "score_penghargaan"   DOUBLE PRECISION NOT NULL,
    "total_score"         DOUBLE PRECISION NOT NULL,

    CONSTRAINT "personel_score_compilation_complete_kawaka_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_score_compilation_complete_kawaka_fungsi"
(
    "id"                  BIGINT           NOT NULL,
    "personel_id"         BIGINT           NOT NULL,
    "nrp"                 TEXT             NOT NULL,
    "nama_lengkap"        TEXT             NOT NULL,
    "nievelering_terkini" TEXT,
    "pangkat_terkini"     TEXT,
    "fungsi"              TEXT,
    "tempat_dinas"        TEXT             NOT NULL,
    "jabatan_terkini"     TEXT             NOT NULL,
    "lokasi_kerja"        TEXT,
    "score_nilai_smk"     DOUBLE PRECISION NOT NULL,
    "score_lama_dinas"    DOUBLE PRECISION NOT NULL,
    "score_nievelering"   DOUBLE PRECISION NOT NULL,
    "score_mddp"          DOUBLE PRECISION NOT NULL,
    "score_tahun_dikpol"  DOUBLE PRECISION NOT NULL,
    "score_rank_dikpol"   DOUBLE PRECISION NOT NULL,
    "score_dikbangspes"   DOUBLE PRECISION NOT NULL,
    "score_penghargaan"   DOUBLE PRECISION NOT NULL,
    "total_score"         DOUBLE PRECISION NOT NULL,

    CONSTRAINT "personel_score_compilation_complete_kawaka_fungsi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "penghargaan_personel"
(
    "id"               SERIAL       NOT NULL,
    "personel_id"      BIGINT       NOT NULL,
    "penghargaan_id"   INTEGER      NOT NULL,
    "tingkat_id"       INTEGER      NOT NULL,
    "tgl_penghargaan"  TIMESTAMP(3) NOT NULL,
    "surat_no"         CHAR(255)    NOT NULL,
    "surat_file"       TEXT         NOT NULL,
    "penghargaan_file" TEXT         NOT NULL,
    "created_at"       TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(3),
    "deleted_at"       TIMESTAMP(0),

    CONSTRAINT "penghargaan_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lama_mddp_personel"
(
    "id"                  SERIAL       NOT NULL,
    "personel_id"         BIGINT       NOT NULL,
    "pangkat_id"          BIGINT       NOT NULL,
    "tmt"                 TIMESTAMP(3) NOT NULL,
    "lama_menjabat_bulan" INTEGER      NOT NULL,
    "created_at"          TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "lama_mddp_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lama_dikbangspes_personel"
(
    "id"                    SERIAL       NOT NULL,
    "personel_id"           BIGINT       NOT NULL,
    "tingkat_id"            INTEGER      NOT NULL,
    "dikbangspes_id"        BIGINT       NOT NULL,
    "lama_dikbangspes_hari" INTEGER      NOT NULL,
    "tanggal_masuk"         TIMESTAMP(3) NOT NULL,
    "tanggal_selesai"       TIMESTAMP(3) NOT NULL,
    "created_at"            TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"            TIMESTAMP(3),
    "deleted_at"            TIMESTAMP(0),

    CONSTRAINT "lama_dikbangspes_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes_tingkat"
(
    "id"         SERIAL NOT NULL,
    "nama"       TEXT   NOT NULL,
    "created_at" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangspes_tingkat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bahasa"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "bahasa_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bahasa_personel"
(
    "id"                BIGSERIAL    NOT NULL,
    "bahasa_id"         BIGINT       NOT NULL,
    "personel_id"       BIGINT       NOT NULL,
    "created_at"        TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"        TIMESTAMP(3),
    "tanggal_perubahan" TIMESTAMP(3),
    "deleted_at"        TIMESTAMP(0),

    CONSTRAINT "bahasa_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "management_api"
(
    "id"            SERIAL NOT NULL,
    "nama_aplikasi" TEXT   NOT NULL,
    "username"      TEXT   NOT NULL,
    "password"      TEXT   NOT NULL,
    "ip_whitelist"  TEXT   NOT NULL,
    "created_at"    TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(3),
    "deleted_at"    TIMESTAMP(0),
    "created_by_id" BIGINT NOT NULL,

    CONSTRAINT "management_api_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "management_api_detail"
(
    "id"                       SERIAL  NOT NULL,
    "management_api_id"        INTEGER NOT NULL,
    "management_api_source_id" INTEGER NOT NULL,
    "response"                 JSONB   NOT NULL,

    CONSTRAINT "management_api_detail_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "management_api_source"
(
    "id"           SERIAL   NOT NULL,
    "source_table" TEXT     NOT NULL,
    "nama"         TEXT     NOT NULL,
    "method"       "Method" NOT NULL,

    CONSTRAINT "management_api_source_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_group"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "survey_group_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_group_personel"
(
    "group_id"    BIGINT NOT NULL,
    "personel_id" BIGINT NOT NULL,

    CONSTRAINT "survey_group_personel_pkey" PRIMARY KEY ("group_id", "personel_id")
);

-- CreateTable
CREATE TABLE "survey_question_type"
(
    "id"            BIGSERIAL    NOT NULL,
    "display_name"  TEXT         NOT NULL,
    "question_type" TEXT         NOT NULL,
    "answer_type"   TEXT         NOT NULL,
    "created_at"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(3),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "survey_question_type_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_file_extensions"
(
    "id"           BIGSERIAL    NOT NULL,
    "display_name" TEXT         NOT NULL,
    "extensions"   TEXT         NOT NULL,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "survey_file_extensions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_question_status"
(
    "id"         BIGSERIAL    NOT NULL,
    "name"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "survey_question_status_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_question"
(
    "id"                  BIGSERIAL    NOT NULL,
    "title"               TEXT         NOT NULL,
    "description"         TEXT,
    "start_date"          TIMESTAMP(3) NOT NULL,
    "end_date"            TIMESTAMP(3),
    "countdown_second"    INTEGER,
    "status_id"           BIGINT       NOT NULL,
    "creator_personel_id" BIGINT       NOT NULL,
    "question_list"       JSONB        NOT NULL,
    "question_count"      INTEGER               DEFAULT 0,
    "created_at"          TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "survey_question_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_destination"
(
    "id"              BIGSERIAL    NOT NULL,
    "survey_id"       BIGINT       NOT NULL,
    "satuan_id"       BIGINT,
    "pangkat_id"      BIGINT,
    "survey_group_id" BIGINT,
    "created_at"      TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"      TIMESTAMP(3),
    "deleted_at"      TIMESTAMP(0),

    CONSTRAINT "survey_destination_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_agg"
(
    "id"         BIGSERIAL NOT NULL,
    "total"      INTEGER   NOT NULL,
    "respondens" INTEGER   NOT NULL,
    "responses"  INTEGER   NOT NULL,

    CONSTRAINT "survey_agg_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_answers"
(
    "id"               BIGSERIAL    NOT NULL,
    "personel_id"      BIGINT       NOT NULL,
    "question_id"      BIGINT       NOT NULL,
    "submission_start" TIMESTAMP(0) NOT NULL,
    "submission_end"   TIMESTAMP(3),
    "list"             JSONB,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(3),
    "deleted_at"       TIMESTAMP(0),

    CONSTRAINT "survey_answers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_files"
(
    "id"           BIGSERIAL    NOT NULL,
    "survey_id"    BIGINT       NOT NULL,
    "answer_id"    BIGINT       NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         BIGINT,
    "key"          TEXT,
    "url"          TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(3),

    CONSTRAINT "survey_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "survey_view_log"
(
    "personel_id" BIGINT       NOT NULL,
    "survey_id"   BIGINT       NOT NULL,
    "viewed_at"   TIMESTAMP(0) NOT NULL,

    CONSTRAINT "survey_view_log_pkey" PRIMARY KEY ("personel_id", "survey_id")
);

-- CreateTable
CREATE TABLE "survey_personel"
(
    "personel_id" BIGINT       NOT NULL,
    "survey_id"   BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "survey_personel_pkey" PRIMARY KEY ("personel_id", "survey_id")
);

-- CreateTable
CREATE TABLE "workflow"
(
    "id"          BIGSERIAL    NOT NULL,
    "nama"        VARCHAR(255) NOT NULL,
    "jenis_surat" VARCHAR(50)  NOT NULL,
    "klasifikasi" VARCHAR(50)  NOT NULL,
    "created_by"  BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "workflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_flow"
(
    "id"          BIGSERIAL    NOT NULL,
    "workflow_id" BIGINT       NOT NULL,
    "step"        INTEGER      NOT NULL,
    "satuan_id"   BIGINT       NOT NULL,
    "jabatan_id"  BIGINT       NOT NULL,
    "note"        VARCHAR(255),
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "workflow_flow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_personel"
(
    "id"               BIGSERIAL    NOT NULL,
    "workflow_flow_id" BIGINT,
    "personel_id"      BIGINT       NOT NULL,
    "tanda_tangan"     BOOLEAN      NOT NULL DEFAULT false,
    "paraf"            BOOLEAN      NOT NULL DEFAULT false,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(3),

    CONSTRAINT "workflow_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_transaction"
(
    "id"           BIGSERIAL    NOT NULL,
    "personel_id"  BIGINT       NOT NULL,
    "workflow_id"  BIGINT       NOT NULL,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(0),
    "status"       "workflow_status"     DEFAULT 'ON_PROCESS',
    "current_step" INTEGER               DEFAULT 1,
    "note"         VARCHAR(255),

    CONSTRAINT "workflow_transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_transaction_flow"
(
    "id"                      BIGSERIAL    NOT NULL,
    "workflow_transaction_id" BIGINT       NOT NULL,
    "step"                    INTEGER      NOT NULL,
    "satuan_id"               BIGINT       NOT NULL,
    "jabatan_id"              BIGINT       NOT NULL,
    "status"                  "workflow_status"     DEFAULT 'WAITING',
    "next_step"               INTEGER,
    "sign_length"             INTEGER      NOT NULL DEFAULT 0,
    "must_sign_length"        INTEGER      NOT NULL,
    "note"                    VARCHAR(255),
    "created_at"              TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"              TIMESTAMP(3),
    "deleted_at"              TIMESTAMP(0),

    CONSTRAINT "workflow_transaction_flow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_transaction_personel"
(
    "id"                           BIGSERIAL    NOT NULL,
    "workflow_transaction_flow_id" BIGINT       NOT NULL,
    "personel_id"                  BIGINT       NOT NULL,
    "tanda_tangan"                 BOOLEAN      NOT NULL DEFAULT false,
    "paraf"                        BOOLEAN      NOT NULL DEFAULT false,
    "has_paraf"                    BOOLEAN      NOT NULL DEFAULT false,
    "order"                        INTEGER      NOT NULL,
    "has_tanda_tangan"             BOOLEAN      NOT NULL DEFAULT false,
    "created_at"                   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                   TIMESTAMP(3),
    "deleted_at"                   TIMESTAMP(0),

    CONSTRAINT "workflow_transaction_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_dokumen"
(
    "id"                      BIGSERIAL    NOT NULL,
    "workflow_transaction_id" BIGINT       NOT NULL,
    "originalname"            TEXT,
    "encoding"                TEXT,
    "mimetype"                TEXT,
    "destination"             TEXT,
    "filename"                TEXT,
    "path"                    TEXT,
    "size"                    BIGINT,
    "key"                     TEXT,
    "url"                     TEXT,
    "used_in_step"            INTEGER[],
    "current_step"            INTEGER      NOT NULL,
    "created_at"              TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"              TIMESTAMP(0),
    "deleted_at"              TIMESTAMP(0),

    CONSTRAINT "workflow_dokumen_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_approval_log"
(
    "id"                           BIGSERIAL    NOT NULL,
    "approver_id"                  BIGINT       NOT NULL,
    "workflow_transaction_flow_id" BIGINT       NOT NULL,
    "created_at"                   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                   TIMESTAMP(3),

    CONSTRAINT "workflow_approval_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_validation_log"
(
    "id"                  BIGSERIAL    NOT NULL,
    "workflow_dokumen_id" BIGINT       NOT NULL,
    "signed_by"           BIGINT       NOT NULL,
    "created_at"          TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),

    CONSTRAINT "workflow_validation_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jenis_pekerjaan"
(
    "id"         BIGSERIAL    NOT NULL,
    "jenis"      VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "jenis_pekerjaan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pekerjaan_keluarga"
(
    "id"                   BIGSERIAL    NOT NULL,
    "nama_institusi"       VARCHAR(255),
    "telepon"              VARCHAR(255),
    "alamat"               VARCHAR(255),
    "status_terakhir"      BOOLEAN,
    "jenis_pekerjaan_id"   BIGINT,
    "keluarga_personel_id" BIGINT,
    "tanggal_perubahan"    TIMESTAMP(0),
    "created_at"           TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),

    CONSTRAINT "pekerjaan_keluarga_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hubungan_keluarga"
(
    "id"         BIGSERIAL    NOT NULL,
    "hubungan"   VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "hubungan_keluarga_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "keluarga_personel"
(
    "id"                          BIGSERIAL      NOT NULL,
    "nama_keluarga"               VARCHAR(255)   NOT NULL,
    "jenis_kelamin"               "JenisKelamin" NOT NULL,
    "alamat"                      VARCHAR(255),
    "no_hp"                       VARCHAR(255),
    "status"                      "StatusParent" NOT NULL,
    "nrp_keluarga"                VARCHAR(255),
    "tempat_lahir"                VARCHAR(255),
    "tanggal_lahir"               DATE,
    "agama_id"                    BIGINT,
    "personel_id"                 BIGINT,
    "hubungan_keluarga_id"        BIGINT,
    "personel_keluarga_id"        BIGINT,
    "golongan_id"                 BIGINT,
    "tanggal_nikah"               TIMESTAMP(0),
    "kpis_nomor"                  VARCHAR(255),
    "kpis_file"                   VARCHAR(255),
    "buku_nikah_nomor"            VARCHAR(255),
    "buku_nikah_file"             VARCHAR(255),
    "foto_file"                   VARCHAR(255),
    "tanggal_perubahan"           TIMESTAMP(0),
    "status_nikah"                BOOLEAN,
    "status_pernikahan"           BOOLEAN,
    "tanggal_cerai"               TIMESTAMP(0),
    "cerai_file"                  VARCHAR(255),
    "parent_hubungan_keluarga_id" BIGINT,
    "status_personel"             BOOLEAN,
    "no_ijinnikah"                VARCHAR(255),
    "file_ijinnikah"              VARCHAR(255),
    "ktp_nomor"                   VARCHAR(255),
    "ktp_file"                    VARCHAR(255),
    "created_at"                  TIMESTAMP(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"                  TIMESTAMP(0),
    "deleted_at"                  TIMESTAMP(0),

    CONSTRAINT "keluarga_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "alamat_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "alamat_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "alamat"
(
    "id"          BIGSERIAL    NOT NULL,
    "alamat"      TEXT,
    "no_rt"       VARCHAR(255),
    "no_rw"       VARCHAR(255),
    "kelurahan"   VARCHAR(255),
    "kecamatan"   VARCHAR(255),
    "kabupaten"   VARCHAR(255),
    "provinsi"    VARCHAR(255),
    "kategori_id" BIGINT       NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "alamat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "history_status_ncr"
(
    "id"               BIGSERIAL         NOT NULL,
    "status"           "StatusPengajuan" NOT NULL,
    "created_by_id"    BIGINT,
    "created_at"       TIMESTAMP(3)      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(0),
    "pengajuan_ncr_id" BIGINT,

    CONSTRAINT "history_status_ncr_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pengajuan_cuti"
(
    "id"               BIGSERIAL NOT NULL,
    "personel_id"      BIGINT,
    "satuan"           TEXT,
    "jenis_cuti"       TEXT,
    "tanggal_mulai"    DATE,
    "tanggal_akhir"    DATE,
    "durasi"           TEXT,
    "status"           TEXT,
    "alasan_cuti"      TEXT,
    "keterangan"       TEXT,
    "dokumen"          JSON,
    "alasan_penolakan" TEXT,
    "created_at"       DATE,
    "updated_at"       DATE,
    "deleted_at"       DATE,

    CONSTRAINT "pengajuan_cuti_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pengajuan_ncr"
(
    "id"                       BIGSERIAL         NOT NULL,
    "satuan_id"                BIGINT            NOT NULL,
    "tempat_pernikah"          VARCHAR(255)      NOT NULL,
    "tanggal_pernikah"         DATE,
    "tanggal_sidang"           DATE,
    "status_pengajuan"         "StatusPengajuan" NOT NULL,
    "alamat_personel_id"       BIGINT            NOT NULL,
    "status_kawin_pemohon_id"  BIGINT            NOT NULL,
    "status_kawin_pasangan_id" BIGINT            NOT NULL,
    "jenis_pengajuan"          "JenisPengajuan"  NOT NULL,
    "nomor_surat_izin_nikah"   VARCHAR(255),
    "surat_izin_nikah"         BIGINT,
    "surat_akta_ncr"           BIGINT,
    "personel_id"              BIGINT,
    "pasangan_personel_id"     BIGINT,
    "data_calon_pasangan"      JSONB             NOT NULL,
    "created_at"               TIMESTAMP(3)      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"               TIMESTAMP(0),
    "deleted_at"               TIMESTAMP(0),

    CONSTRAINT "pengajuan_ncr_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dokumen_ncr"
(
    "id"               BIGSERIAL    NOT NULL,
    "pengajuan_ncr_id" BIGINT,
    "jenis_dokumen"    TEXT,
    "originalname"     TEXT,
    "encoding"         TEXT,
    "mimetype"         TEXT,
    "destination"      TEXT,
    "filename"         TEXT,
    "path"             TEXT,
    "size"             BIGINT,
    "key"              TEXT,
    "url"              TEXT,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(0),
    "deleted_at"       TIMESTAMP(0),

    CONSTRAINT "dokumen_ncr_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permohonan_cerai"
(
    "id" BIGSERIAL NOT NULL,

    CONSTRAINT "permohonan_cerai_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikum"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikum_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikum_personel"
(
    "id"           BIGSERIAL    NOT NULL,
    "personel_id"  BIGINT       NOT NULL,
    "institusi_id" BIGINT,
    "dikum_id"     BIGINT       NOT NULL,
    "jurusan_id"   BIGINT,
    "gelar_id"     BIGINT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "dikum_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "institusi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "institusi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jurusan"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "jurusan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_sign"
(
    "id"           BIGSERIAL    NOT NULL,
    "personel_id"  BIGINT       NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         BIGINT,
    "key"          TEXT,
    "url"          TEXT,
    "qr_data"      TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),

    CONSTRAINT "personel_sign_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_personel"
(
    "id"               BIGSERIAL    NOT NULL,
    "nama_jabatan"     TEXT         NOT NULL,
    "personel_id"      BIGINT       NOT NULL,
    "instansi_id"      BIGINT       NOT NULL,
    "location_type_id" BIGINT       NOT NULL,
    "tanggal_mulai"    TIMESTAMP(3) NOT NULL,
    "tanggal_selesai"  TIMESTAMP(3) NOT NULL,
    "durasi_hari"      INTEGER      NOT NULL,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(0),
    "sprin_file_id"    BIGINT       NOT NULL,
    "kep_file_id"      BIGINT       NOT NULL,

    CONSTRAINT "bagassus_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "penugasan_instansi"
(
    "id"               BIGSERIAL    NOT NULL,
    "nama"             TEXT         NOT NULL,
    "location_type_id" BIGINT       NOT NULL,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(0),

    CONSTRAINT "penugasan_instansi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_location_type"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),

    CONSTRAINT "bagassus_location_type_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_sprin_file"
(
    "id"           BIGSERIAL    NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         INTEGER,
    "key"          TEXT,
    "url"          TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),

    CONSTRAINT "bagassus_sprin_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_kep_file"
(
    "id"           BIGSERIAL    NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         INTEGER,
    "key"          TEXT,
    "url"          TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),

    CONSTRAINT "bagassus_kep_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_seleksi"
(
    "id"               BIGSERIAL    NOT NULL,
    "title"            TEXT         NOT NULL,
    "description"      TEXT         NOT NULL,
    "location_type_id" BIGINT       NOT NULL,
    "penugasan_id"     BIGINT       NOT NULL,
    "tanggal_mulai"    TIMESTAMP(3) NOT NULL,
    "tanggal_selesai"  TIMESTAMP(3) NOT NULL,
    "submission_start" TIMESTAMP(3) NOT NULL,
    "submission_end"   TIMESTAMP(3) NOT NULL,
    "has_been_ended"   BOOLEAN,
    "logo_file_id"     BIGINT       NOT NULL,
    "document_file_id" BIGINT       NOT NULL,
    "tahapan_seleksi"  INTEGER      NOT NULL,
    "created_at"       TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(0),

    CONSTRAINT "bagassus_seleksi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_tahap_seleksi"
(
    "id"              BIGSERIAL    NOT NULL,
    "seleksi_id"      BIGINT       NOT NULL,
    "tahap"           INTEGER      NOT NULL,
    "tanggal_mulai"   TIMESTAMP(3) NOT NULL,
    "tanggal_selesai" TIMESTAMP(3) NOT NULL,
    "created_at"      TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"      TIMESTAMP(0),

    CONSTRAINT "bagassus_tahap_seleksi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_seleksi_syarat"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),

    CONSTRAINT "bagassus_seleksi_syarat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_seleksi_syarat_value"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT         NOT NULL,
    "syarat_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(0),

    CONSTRAINT "bagassus_seleksi_syarat_value_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_seleksi_file"
(
    "id"           BIGSERIAL    NOT NULL,
    "originalname" TEXT,
    "encoding"     TEXT,
    "mimetype"     TEXT,
    "destination"  TEXT,
    "filename"     TEXT,
    "path"         TEXT,
    "size"         INTEGER,
    "key"          TEXT,
    "url"          TEXT,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(0),

    CONSTRAINT "bagassus_seleksi_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bagassus_seleksi_personel"
(
    "id"          BIGSERIAL    NOT NULL,
    "seleksi_id"  BIGINT       NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(0),

    CONSTRAINT "bagassus_seleksi_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "seleksi_bagrimdik"
(
    "id"               BIGSERIAL               NOT NULL,
    "judul"            VARCHAR(255)            NOT NULL,
    "deskripsi"        VARCHAR(255)            NOT NULL,
    "jenis"            "JenisSeleksiBagrimdik" NOT NULL,
    "startdate"        TIMESTAMP(0)            NOT NULL,
    "enddate"          TIMESTAMP(0)            NOT NULL,
    "logo_file"        VARCHAR(255),
    "dokumen_file"     VARCHAR(255),
    "tahap"            INTEGER                 NOT NULL,
    "persyaratan_list" JSONB                   NOT NULL,
    "tahapan_seleksi"  INTEGER                 NOT NULL,
    "created_at"       TIMESTAMP(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(3),
    "deleted_at"       TIMESTAMP(0),

    CONSTRAINT "seleksi_bagrimdik_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "seleksi_bagrimdik_tahap"
(
    "id"                   BIGSERIAL NOT NULL,
    "seleksi_bagrimdik_id" BIGINT    NOT NULL,
    "startdate"            TIMESTAMP(0),
    "enddate"              TIMESTAMP(0),
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),
    "tahap"                BIGINT    NOT NULL,

    CONSTRAINT "seleksi_bagrimdik_tahap_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "seleksi_persyaratan"
(
    "id"         BIGSERIAL    NOT NULL,
    "name"       VARCHAR(255),
    "type"       VARCHAR,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "seleksi_persyaratan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users_device_token"
(
    "id"      BIGSERIAL    NOT NULL,
    "user_id" BIGINT       NOT NULL,
    "token"   VARCHAR(255) NOT NULL,

    CONSTRAINT "users_device_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fungsi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT         NOT NULL,
    "kode"       TEXT         NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "fungsi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fungsi_satuan"
(
    "id"         BIGSERIAL    NOT NULL,
    "fungsi_id"  BIGINT       NOT NULL,
    "satuan_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "fungsi_satuan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "konsultasi_personel"
(
    "id"          BIGSERIAL    NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "chat_id"     VARCHAR(255),
    "status"      VARCHAR(255),
    "konsultasi"  VARCHAR(255) NOT NULL,
    "konselor"    VARCHAR(255),
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "konsultasi_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jasmani_personel"
(
    "id"               BIGSERIAL NOT NULL,
    "personel_id"      BIGINT    NOT NULL,
    "tahun"            SMALLINT,
    "semester"         SMALLINT,
    "nilai_akhir"      DECIMAL(65, 30),
    "nilai_file"       VARCHAR(255),
    "keterangan"       VARCHAR(255),
    "created_at"       TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updated_at"       TIMESTAMP(3),
    "deleted_at"       TIMESTAMP(0),
    "nilai_lari_12_m"  DECIMAL(65, 30),
    "hasil_lari_12_m"  DECIMAL(65, 30),
    "nilai_pull_up"    DECIMAL(65, 30),
    "hasil_pull_up"    DECIMAL(65, 30),
    "nilai_push_up"    DECIMAL(65, 30),
    "hasil_push_up"    DECIMAL(65, 30),
    "nilai_sit_up"     DECIMAL(65, 30),
    "hasil_sit_up"     DECIMAL(65, 30),
    "nilai_shutle_run" DECIMAL(65, 30),
    "hasil_shutle_run" DECIMAL(65, 30),
    "nilai_chinning"   DECIMAL(65, 30),
    "hasil_chinning"   DECIMAL(65, 30),
    "nilai_a"          DECIMAL(65, 30),
    "nilai_b"          DECIMAL(65, 30),

    CONSTRAINT "jasmani_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "binjas_formula"
(
    "id"            SERIAL          NOT NULL,
    "golongan"      TEXT            NOT NULL,
    "jenis_kelamin" TEXT            NOT NULL,
    "gerakan"       TEXT            NOT NULL,
    "batas_atas"    DECIMAL(65, 30) NOT NULL,
    "batas_bawah"   DECIMAL(65, 30) NOT NULL,
    "nilai"         DECIMAL(65, 30) NOT NULL,
    "keterangan"    TEXT,
    "created_at"    TIMESTAMP(0),
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "binjas_formula_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessment_personel"
(
    "id"          BIGSERIAL    NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "nama"        TEXT         NOT NULL,
    "tmt_mulai"   TIMESTAMP(3) NOT NULL,
    "tmt_selesai" TIMESTAMP(3),
    "nilai"       DOUBLE PRECISION      DEFAULT 0,
    "nilai_file"  TEXT,
    "keterangan"  TEXT,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "assessment_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa"
(
    "id"                             BIGSERIAL    NOT NULL,
    "uid"                            TEXT         NOT NULL,
    "nama_lengkap"                   VARCHAR(255),
    "gelar"                          VARCHAR(255),
    "nama_dengan_gelar"              VARCHAR(255),
    "nama_panggilan"                 VARCHAR(255),
    "tanggal_lahir"                  DATE,
    "jenis_kelamin"                  VARCHAR(255) NOT NULL,
    "tempat_lahir"                   VARCHAR,
    "agama_id"                       BIGINT,
    "nik"                            VARCHAR(255),
    "status_kawin_id"                BIGINT,
    "golongan_darah"                 VARCHAR(255),
    "email"                          VARCHAR(255),
    "no_hp"                          VARCHAR(255),
    "suku"                           VARCHAR(255),
    "jenis_pekerjaan"                VARCHAR(255),
    "hobi"                           TEXT,
    "medsos_instagram"               VARCHAR(255),
    "medsos_facebook"                VARCHAR(255),
    "medsos_twitter"                 VARCHAR(255),
    "medsos_tiktok"                  VARCHAR,
    "no_ujian_polda"                 VARCHAR(255),
    "no_registrasi_online"           VARCHAR(255),
    "no_ak_nosis"                    VARCHAR(255),
    "jenis_diktuk_id"                BIGINT,
    "ta_rim_diktuk"                  BIGINT,
    "gelombang_rim_masuk_diktuk"     VARCHAR(255),
    "kompetensi_diktuk_id"           BIGINT,
    "sub_kompetensi_diktuk_id"       BIGINT,
    "sub_sub_kompetensi_diktuk_id"   BIGINT,
    "ket_jalur_rekpro"               VARCHAR(255),
    "tmp_dik_id"                     BIGINT,
    "asal_rim_polda_id"              BIGINT,
    "asal_rim_polres_id"             BIGINT,
    "ta_pat_diktuk"                  VARCHAR(255),
    "gelombang_pat_diktuk"           VARCHAR(255),
    "ijazah_dikum_seleksi_rim"       VARCHAR(255),
    "th_lulus_dikum_gun_seleksi_rim" VARCHAR(255),
    "created_at"                     TIMESTAMP(0),
    "updated_at"                     TIMESTAMP(0),
    "deleted_at"                     TIMESTAMP(0),
    "pers_id"                        VARCHAR,
    "no_urut_asalrim"                BIGINT,
    "unique_id"                      TEXT,
    "username"                       VARCHAR,
    "password"                       VARCHAR,
    "oap_orang_asli_papua"           VARCHAR,

    CONSTRAINT "siswa_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jenis_diktuk"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "jenis_diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kompetensi_diktuk"
(
    "id"        BIGSERIAL    NOT NULL,
    "nama"      VARCHAR(255) NOT NULL,
    "diktuk_id" BIGINT,

    CONSTRAINT "kompetensi_diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sub_kompetensi_diktuk"
(
    "id"            BIGSERIAL    NOT NULL,
    "nama"          VARCHAR(255) NOT NULL,
    "kompetensi_id" BIGINT,

    CONSTRAINT "sub_kompetensi_diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sub_sub_kompetensi_diktuk"
(
    "id"                BIGSERIAL    NOT NULL,
    "nama"              VARCHAR(255) NOT NULL,
    "sub_kompetensi_id" BIGINT,

    CONSTRAINT "sub_sub_kompetensi_diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tmpdik"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "tmpdik_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_penerimaan"
(
    "id"                                   SERIAL NOT NULL,
    "siswa_id"                             BIGINT NOT NULL,
    "n_kualitatif_rikmin_awal"             VARCHAR,
    "ket_rikmin_awal"                      VARCHAR,
    "n_kualitatif_rikmin_akhir"            VARCHAR,
    "n_kuantitatif_rikmin_akhir"           DECIMAL(65, 30),
    "ket_rikmin_akhir"                     VARCHAR,
    "n_kualitatif_rikkes_1"                VARCHAR,
    "n_kuantitatif_rikkes_1"               DECIMAL(65, 30),
    "diagnosa_rikkes_1"                    VARCHAR,
    "n_kualitatif_rikkes_2"                VARCHAR,
    "n_kuantitatif_rikkes_2"               DECIMAL(65, 30),
    "diagnosa_rikkes_2"                    VARCHAR,
    "n_kualitatif_rikpsi_1"                VARCHAR,
    "n_kuantitatif_rikpsi_1"               VARCHAR,
    "ket_rikpsi_1"                         VARCHAR,
    "n_kualitatif_rikpsi_2"                VARCHAR,
    "n_kuantitatif_rikpsi_2"               DECIMAL(65, 30),
    "temuan_rikpsi_2"                      VARCHAR,
    "ket_rikpsi_2"                         VARCHAR,
    "n_peng_u_akademik"                    DECIMAL(65, 30),
    "n_wwsn_k_akademik"                    DECIMAL(65, 30),
    "n_mtk_akademik"                       DECIMAL(65, 30),
    "n_b_ing_akademik"                     DECIMAL(65, 30),
    "n_b_ind_akademik"                     DECIMAL(65, 30),
    "n_tkk_pengetahuan_sipss_akademik"     DECIMAL(65, 30),
    "n_tkm_sipss_akademik"                 DECIMAL(65, 30),
    "n_tkk_pengetahuan_bakomsus_akademik"  DECIMAL(65, 30),
    "n_tkk_keterampilan_bakomsus_akademik" DECIMAL(65, 30),
    "n_tkk_perilaku_bakomsus_akademik"     DECIMAL(65, 30),
    "n_gbg_tkk_bakomsus_akademik"          DECIMAL(65, 30),
    "n_gbgakhir_akademik"                  DECIMAL(65, 30),
    "ket_akademik"                         DECIMAL(65, 30),
    "hasil_lari_12_m_ukj"                  DECIMAL(65, 30),
    "n_a_lari_12_menit_ukj"                DECIMAL(65, 30),
    "hasil_pull_up_ukj"                    DECIMAL(65, 30),
    "n_pull_up_ukj"                        DECIMAL(65, 30),
    "hasil_situp_ukj"                      DECIMAL(65, 30),
    "n_situp_ukj"                          DECIMAL(65, 30),
    "hasil_pushup_ukj"                     DECIMAL(65, 30),
    "n_pushup_ukj"                         DECIMAL(65, 30),
    "hasil_shuttlerun_ukj"                 DECIMAL(65, 30),
    "n_shuttle_run_ukj"                    DECIMAL(65, 30),
    "rata2_n_b_ukj"                        DECIMAL(65, 30),
    "n_ab_ukj"                             DECIMAL(65, 30),
    "jarak_renang_ukj"                     DECIMAL(65, 30),
    "waktu_renang_ukj"                     VARCHAR,
    "n_renang_ukj"                         DECIMAL(65, 30),
    "n_kualitatif_antro_ukj"               VARCHAR,
    "n_kuantitatif_antro_ukj"              DECIMAL(65, 30),
    "kelainan_antro_ukj"                   VARCHAR,
    "n_kuantitatif_akhir_ukj"              DECIMAL(65, 30),
    "n_kualitatif_akhir_ukj"               VARCHAR,
    "ket_ukj"                              VARCHAR,
    "n_kualitatif_pmk"                     VARCHAR,
    "n_kuantitatif_pmk"                    DECIMAL(65, 30),
    "temuan_pmk"                           VARCHAR,
    "ket_pmk"                              VARCHAR,
    "data_deteksi_dini_densus"             VARCHAR,
    "ket_deteksi_dini_densus"              VARCHAR,
    "prestasi_seleksi_rim"                 VARCHAR,
    "catatan_khusus_seleksi_rim"           VARCHAR,
    "created_at"                           TIMESTAMP(3),
    "updated_at"                           TIMESTAMP(3),
    "deleted_at"                           TIMESTAMP(3),

    CONSTRAINT "siswa_penerimaan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "filter_siswa"
(
    "id"           SERIAL       NOT NULL,
    "nama"         VARCHAR,
    "is_siswa"     BOOLEAN      NOT NULL DEFAULT false,
    "is_rekrutmen" BOOLEAN      NOT NULL DEFAULT false,
    "created_at"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"   TIMESTAMP(3) NOT NULL,

    CONSTRAINT "filter_siswa_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "candidate_pangkat_filter"
(
    "id"             INTEGER      NOT NULL,
    "nivellering_id" BIGINT       NOT NULL,
    "nivellering"    TEXT         NOT NULL,
    "pangkat_id"     BIGINT       NOT NULL,
    "pangkat"        TEXT         NOT NULL,
    "kategori_id"    BIGINT       NOT NULL,
    "kategori"       TEXT         NOT NULL,
    "golongan_id"    BIGINT,
    "golongan"       TEXT,
    "created_at"     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"     TIMESTAMP(3) NOT NULL,

    CONSTRAINT "candidate_pangkat_filter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_fisik"
(
    "id"            BIGSERIAL NOT NULL,
    "siswa_id"      BIGINT    NOT NULL,
    "tb_cm"         INTEGER,
    "bb_kg"         INTEGER,
    "warna_kulit"   VARCHAR,
    "warna_mata"    VARCHAR,
    "warna_rambut"  VARCHAR,
    "jenis_rambut"  VARCHAR,
    "ukuran_topi"   VARCHAR,
    "ukuran_celana" VARCHAR,
    "ukuran_baju"   VARCHAR,
    "ukuran_sepatu" VARCHAR,
    "created_at"    TIMESTAMP(3),
    "updated_at"    TIMESTAMP(3),
    "deleted_at"    TIMESTAMP(3),

    CONSTRAINT "siswa_fisik_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_keahlian"
(
    "id"                         BIGSERIAL NOT NULL,
    "siswa_id"                   BIGINT    NOT NULL,
    "puan_bhs_daerah"            VARCHAR,
    "ket_puan_bhs_daerah"        VARCHAR,
    "puan_bhs_asing"             VARCHAR,
    "ket_puan_bhs_asing"         VARCHAR,
    "keahlian_aplikasi_komputer" VARCHAR,
    "keahlian_mengemudi"         VARCHAR,
    "kepemilikan_sim"            VARCHAR,
    "created_at"                 TIMESTAMP(0),
    "updated_at"                 TIMESTAMP(0),
    "deleted_at"                 TIMESTAMP(0),
    "keahlian_beladiri"          VARCHAR,
    "sabuk_tingkatan_beladiri"   VARCHAR,

    CONSTRAINT "siswa_keahlian_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_keluarga"
(
    "id"               BIGSERIAL NOT NULL,
    "siswa_id"         BIGINT    NOT NULL,
    "anak_ke"          VARCHAR,
    "jumlah_saudara"   VARCHAR,
    "nama_ayah"        VARCHAR,
    "pek_ayah"         VARCHAR,
    "status_ayah"      VARCHAR,
    "gol_pangkat_ayah" VARCHAR,
    "jabatan_ayah"     VARCHAR,
    "nama_ibu"         VARCHAR,
    "pek_ibu"          VARCHAR,
    "status_ibu"       VARCHAR,
    "gol_pangkat_ibu"  VARCHAR,
    "jabatan_ibu"      VARCHAR,
    "no_hp_ortu"       VARCHAR,
    "created_at"       TIMESTAMP(3),
    "updated_at"       TIMESTAMP(3),
    "deleted_at"       TIMESTAMP(3),
    "umur_ayah"        INTEGER   NOT NULL,
    "umur_ibu"         INTEGER   NOT NULL,

    CONSTRAINT "siswa_keluarga_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kecamatan"
(
    "id"           BIGINT  NOT NULL,
    "nama"         VARCHAR NOT NULL,
    "kabupaten_id" BIGINT,
    "dukcapil_id"  VARCHAR,
    "created_at"   TIMESTAMP(3),
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(3),

    CONSTRAINT "kecamatan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "kelurahan"
(
    "id"           BIGINT  NOT NULL,
    "nama"         VARCHAR NOT NULL,
    "kecamatan_id" BIGINT,
    "dukcapil_id"  VARCHAR,
    "created_at"   TIMESTAMP(3),
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(3),

    CONSTRAINT "kelurahan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_alamat"
(
    "id"           BIGSERIAL NOT NULL,
    "siswa_id"     BIGINT    NOT NULL,
    "alamat"       TEXT,
    "no_rt"        VARCHAR,
    "no_rw"        VARCHAR,
    "kecamatan_id" BIGINT,
    "kelurahan_id" BIGINT,
    "kabupaten_id" BIGINT,
    "provinsi_id"  BIGINT,
    "created_at"   TIMESTAMP(3),
    "updated_at"   TIMESTAMP(3),
    "deleted_at"   TIMESTAMP(3),

    CONSTRAINT "siswa_alamat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "konsultasi"
(
    "id"                  BIGSERIAL    NOT NULL,
    "personel_id"         BIGINT       NOT NULL,
    "chat_id"             TEXT,
    "status"              "StatusKonsultasi",
    "konselor_id"         BIGINT,
    "konsultasi_jenis_id" BIGINT       NOT NULL,
    "keluhan"             TEXT,
    "polda_id"            BIGINT       NOT NULL,
    "is_tindakan"         BOOLEAN      NOT NULL DEFAULT false,
    "no_hp_tindakan"      TEXT,
    "catatan"             TEXT,
    "created_at"          TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"          TIMESTAMP(3),
    "deleted_at"          TIMESTAMP(0),

    CONSTRAINT "konsultasi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "konsultasi_file"
(
    "id"            BIGSERIAL    NOT NULL,
    "konsultasi_id" BIGINT       NOT NULL,
    "originalname"  TEXT         NOT NULL,
    "encoding"      TEXT         NOT NULL,
    "mimetype"      TEXT         NOT NULL,
    "destination"   TEXT,
    "filename"      TEXT,
    "path"          TEXT,
    "size"          BIGINT,
    "key"           TEXT,
    "url"           TEXT,
    "created_at"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"    TIMESTAMP(0),
    "deleted_at"    TIMESTAMP(0),

    CONSTRAINT "konsultasi_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "konsultasi_jenis"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "konsultasi_jenis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "konsultasi_konselor"
(
    "id"          BIGSERIAL    NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(3),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "konsultasi_konselor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_patma"
(
    "id"                    BIGSERIAL NOT NULL,
    "siswa_id"              BIGINT    NOT NULL,
    "nrp"                   TEXT,
    "urut_no_patma_gbg"     BIGINT,
    "urut_no_patma_per_spn" BIGINT,
    "patma"                 TEXT,
    "sub_patma"             TEXT,
    "sub_sub_patma"         TEXT,
    "satker_patma"          BIGINT,
    "jab_patma"             TEXT,
    "ket_jab_patma"         TEXT,
    "patma_mabes_polda"     TEXT,
    "data_penarikan_satker" TEXT,
    "catatan_khusus_patma"  TEXT,
    "nama_kasubbag"         TEXT,
    "nama_kabag"            TEXT,
    "nrp_kabag"             TEXT,
    "nama_karo"             TEXT,
    "nama_as_sdm"           TEXT,
    "nama_kapolri"          TEXT,
    "pangkat"               TEXT,
    "kd_lamp_patma"         TEXT,
    "no_kep_patma"          TEXT,
    "tmt_kep_patma"         TIMESTAMP(3),
    "tmt_kep_pangkat"       TIMESTAMP(3),
    "kesarjanaan_mds"       TEXT,
    "th_mds"                TEXT,
    "th_naik_gaji_mds"      TEXT,
    "tmt_mds"               TIMESTAMP(3),
    "urut_no_mds"           TEXT,
    "msk_gol_gaji_5"        TIMESTAMP(3),
    "gol_gaji_8"            TEXT,
    "msk_gol_gaji_9"        TIMESTAMP(3),
    "gaji"                  DECIMAL(65, 30),
    "terbilang_gaji"        TEXT,
    "ket_kep_a"             TEXT,
    "ket_kep_b"             TEXT,
    "ket_kep_c"             TEXT,
    "ket_kep_d"             TEXT,
    "urut_no_keppres"       INTEGER,
    "no_keppres"            TEXT,
    "tmt_keppres"           TIMESTAMP(3),
    "file_keppres"          TEXT,
    "wil_tmp_dik"           TEXT,
    "wil_patma"             TEXT,
    "wil_asal_tujuan"       TEXT,
    "renbut_jaldis"         TEXT,
    "status_verifikasi"     TEXT,
    "created_at"            TIMESTAMP(3),
    "updated_at"            TIMESTAMP(3),
    "deleted_at"            TIMESTAMP(3),

    CONSTRAINT "siswa_patma_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_diktuk"
(
    "id"                         BIGSERIAL NOT NULL,
    "siswa_id"                   BIGINT    NOT NULL,
    "no_urut_tmp_dik"            TEXT,
    "hasil_lari_12_m_jasdiktuk"  DECIMAL(65, 30),
    "n_lari_12_m_jasdiktuk"      DECIMAL(65, 30),
    "hasil_pull_up_jasdiktuk"    DECIMAL(65, 30),
    "n_pull_up_jasdiktuk"        DECIMAL(65, 30),
    "hasil_situp_jasdiktuk"      DECIMAL(65, 30),
    "n_situp_jasdiktuk"          DECIMAL(65, 30),
    "hasil_pushup_jasdiktuk"     DECIMAL(65, 30),
    "n_pushup_jasdiktuk"         DECIMAL(65, 30),
    "hasil_shuttlerun_jasdiktuk" DECIMAL(65, 30),
    "n_shuttlerun_jasdiktuk"     DECIMAL(65, 30),
    "rata2_n_b_jasdiktuk"        DECIMAL(65, 30),
    "n_ab_jasdiktuk"             DECIMAL(65, 30),
    "n_renang_jasdiktuk"         DECIMAL(65, 30),
    "n_jasmani_diktuk"           DECIMAL(65, 30),
    "n_mentalkep_kar_diktuk"     DECIMAL(65, 30),
    "n_akademik_peng_diktuk"     DECIMAL(65, 30),
    "n_keterampilan_diktuk"      DECIMAL(65, 30),
    "n_kesehatan_diktuk"         DECIMAL(65, 30),
    "n_gbgakhir_diktuk"          DECIMAL(65, 30),
    "ranking_diktuk"             DECIMAL(65, 30),
    "catatan_kesehatan_diktuk"   TEXT,
    "catatan_pelanggaran_diktuk" TEXT,
    "catatan_psikologi_diktuk"   TEXT,
    "prestasi_diktuk"            TEXT,
    "catatan_khusus_diktuk"      TEXT,
    "status_diktuk"              TEXT,
    "ket_status_diktuk"          TEXT,
    "created_at"                 TIMESTAMP(3),
    "updated_at"                 TIMESTAMP(3),
    "deleted_at"                 TIMESTAMP(3),

    CONSTRAINT "siswa_diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peraturan_polri"
(
    "id"             BIGSERIAL    NOT NULL,
    "judul"          VARCHAR      NOT NULL,
    "no_surat"       VARCHAR(100) NOT NULL,
    "bentuk"         VARCHAR      NOT NULL,
    "bentuk_singkat" VARCHAR(150) NOT NULL,
    "created_at"     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"     TIMESTAMP(3),
    "deleted_at"     TIMESTAMP(0),

    CONSTRAINT "peraturan_polri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "peraturan_polri_file"
(
    "id"                 BIGSERIAL    NOT NULL,
    "id_peraturan_polri" BIGINT       NOT NULL,
    "originalname"       TEXT,
    "encoding"           TEXT,
    "mimetype"           TEXT,
    "destination"        TEXT,
    "filename"           TEXT,
    "path"               TEXT,
    "size"               BIGINT,
    "key"                TEXT,
    "url"                TEXT,
    "tanggal_penetapan"  DATE         NOT NULL,
    "tanggal_berlaku"    DATE         NOT NULL,
    "created_at"         TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"         TIMESTAMP(0),
    "deleted_at"         TIMESTAMP(0),

    CONSTRAINT "peraturan_polri_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_sertifikasi"
(
    "id"          BIGSERIAL    NOT NULL,
    "tingkat"     VARCHAR(150) NOT NULL,
    "cabang"      VARCHAR      NOT NULL,
    "tanggal"     DATE         NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(0),

    CONSTRAINT "personel_sertifikasi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personel_kemampuan_bahasa"
(
    "id"          BIGSERIAL    NOT NULL,
    "bahasa"      VARCHAR(150) NOT NULL,
    "status"      VARCHAR      NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(0),

    CONSTRAINT "personel_kemampuan_bahasa_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "siswa_keswa_angket"
(
    "id"                      BIGSERIAL NOT NULL,
    "siswa_id"                BIGINT    NOT NULL,
    "rekom_psi_1"             TEXT,
    "rekom_psi_2"             TEXT,
    "catatan_khusus_rekompsi" TEXT,
    "rikkeswa_1"              TEXT,
    "rikkeswa_2"              TEXT,
    "catatan_khusus_rikkeswa" TEXT,
    "angket_1"                BIGINT,
    "angket_2"                BIGINT,
    "angket_3"                BIGINT,
    "angket_4"                BIGINT,
    "angket_5"                BIGINT,
    "ket_angket"              TEXT,

    CONSTRAINT "siswa_keswa_angket_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "nivellering_nivel_down_id_key" ON "nivellering" ("nivel_down_id");

-- CreateIndex
CREATE INDEX "workflow_jenis_surat_idx" ON "workflow" ("jenis_surat");

-- CreateIndex
CREATE UNIQUE INDEX "workflow_flow_workflow_id_step_key" ON "workflow_flow" ("workflow_id", "step");

-- CreateIndex
CREATE INDEX "personel_sign_personel_id_idx" ON "personel_sign" ("personel_id");

-- CreateIndex
CREATE UNIQUE INDEX "bagassus_seleksi_syarat_nama_key" ON "bagassus_seleksi_syarat" ("nama");

-- CreateIndex
CREATE UNIQUE INDEX "bagassus_seleksi_syarat_value_nama_key" ON "bagassus_seleksi_syarat_value" ("nama");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_uid_key" ON "siswa" ("uid");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_penerimaan_siswa_id_key" ON "siswa_penerimaan" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_fisik_siswa_id_key" ON "siswa_fisik" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_keahlian_siswa_id_key" ON "siswa_keahlian" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_keluarga_siswa_id_key" ON "siswa_keluarga" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_alamat_siswa_id_key" ON "siswa_alamat" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_patma_siswa_id_key" ON "siswa_patma" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_diktuk_siswa_id_key" ON "siswa_diktuk" ("siswa_id");

-- CreateIndex
CREATE UNIQUE INDEX "siswa_keswa_angket_siswa_id_key" ON "siswa_keswa_angket" ("siswa_id");

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_agama_id_foreign" FOREIGN KEY ("agama_id") REFERENCES "agama" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_status_aktif_id_foreign" FOREIGN KEY ("status_aktif_id") REFERENCES "status_aktif" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_status_kawin_id_foreign" FOREIGN KEY ("status_kawin_id") REFERENCES "status_kawin" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_suku_id_foreign" FOREIGN KEY ("suku_id") REFERENCES "suku" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "berita"
    ADD CONSTRAINT "berita_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "berita"
    ADD CONSTRAINT "berita_satuan_id_fkey" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "berita"
    ADD CONSTRAINT "berita_kategori_id_fkey" FOREIGN KEY ("kategori_id") REFERENCES "berita_kategori" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "berita_foto"
    ADD CONSTRAINT "berita_foto_id_berita_fkey" FOREIGN KEY ("id_berita") REFERENCES "berita" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes"
    ADD CONSTRAINT "dikbangspes_lokasi_id_foreign" FOREIGN KEY ("lokasi_id") REFERENCES "dikbangspes_lokasi" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes_personel"
    ADD CONSTRAINT "dikbangspes_personel_dikbangspes_id_foreign" FOREIGN KEY ("dikbangspes_id") REFERENCES "dikbangspes" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes_personel"
    ADD CONSTRAINT "dikbangspes_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum"
    ADD CONSTRAINT "dikbangum_dikbangum_kategori_id_foreign" FOREIGN KEY ("dikbangum_kategori_id") REFERENCES "dikbangum_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum_personel"
    ADD CONSTRAINT "dikbangum_personel_dikbangum_id_foreign" FOREIGN KEY ("dikbangum_id") REFERENCES "dikbangum" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum_personel"
    ADD CONSTRAINT "dikbangum_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk"
    ADD CONSTRAINT "diktuk_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "diktuk_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_diktuk_id_foreign" FOREIGN KEY ("diktuk_id") REFERENCES "diktuk" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_gelar_id_foreign" FOREIGN KEY ("gelar_id") REFERENCES "gelar" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_atasan_id_foreign" FOREIGN KEY ("atasan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "jabatan_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_nivellering_id_foreign" FOREIGN KEY ("nivellering_id") REFERENCES "nivellering" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_satuan_id_foreign" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan_personel"
    ADD CONSTRAINT "jabatan_personel_jabatan_id_foreign" FOREIGN KEY ("jabatan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan_personel"
    ADD CONSTRAINT "jabatan_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kabupaten"
    ADD CONSTRAINT "kabupaten_provinsi_id_foreign" FOREIGN KEY ("provinsi_id") REFERENCES "provinsi" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nivellering"
    ADD CONSTRAINT "nivellering_nivel_down_id_fkey" FOREIGN KEY ("nivel_down_id") REFERENCES "nivellering" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nivellering"
    ADD CONSTRAINT "nivellering_eselon_id_foreign" FOREIGN KEY ("eselon_id") REFERENCES "eselon" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat"
    ADD CONSTRAINT "pangkat_golongan_id_foreign" FOREIGN KEY ("golongan_id") REFERENCES "golongan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat"
    ADD CONSTRAINT "pangkat_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "pangkat_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat_personel"
    ADD CONSTRAINT "pangkat_personel_pangkat_id_foreign" FOREIGN KEY ("pangkat_id") REFERENCES "pangkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat_personel"
    ADD CONSTRAINT "pangkat_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_pengakhiran_dinas"
    ADD CONSTRAINT "pengajuan_pengakhiran_dinas_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provinsi"
    ADD CONSTRAINT "provinsi_negara_id_foreign" FOREIGN KEY ("negara_id") REFERENCES "negara" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "satuan"
    ADD CONSTRAINT "satuan_atasan_id_fkey" FOREIGN KEY ("atasan_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "satuan"
    ADD CONSTRAINT "satuan_jenis_id_foreign" FOREIGN KEY ("jenis_id") REFERENCES "satuan_jenis" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users"
    ADD CONSTRAINT "users_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_users_id_fkey" FOREIGN KEY ("users_id") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_level_id_fkey" FOREIGN KEY ("level_id") REFERENCES "level" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "role" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role"
    ADD CONSTRAINT "role_bagian_id_fkey" FOREIGN KEY ("bagian_id") REFERENCES "bagian" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role"
    ADD CONSTRAINT "role_satuan_id_fkey" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role"
    ADD CONSTRAINT "role_role_tipe_id_fkey" FOREIGN KEY ("role_tipe_id") REFERENCES "role_tipe" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permission"
    ADD CONSTRAINT "role_permission_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permission" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permission"
    ADD CONSTRAINT "role_permission_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "role" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "logs_activity"
    ADD CONSTRAINT "logs_activity_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kerjasama"
    ADD CONSTRAINT "kerjasama_status_id_foreign" FOREIGN KEY ("status_id") REFERENCES "kerjasama_status" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kerjasama"
    ADD CONSTRAINT "kerjasama_jenis_institusi_id_fkey" FOREIGN KEY ("jenis_institusi_id") REFERENCES "jenis_institusi" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kerjasama_file_pks"
    ADD CONSTRAINT "kerjasama_file_pks_id_kerjasama_fkey" FOREIGN KEY ("id_kerjasama") REFERENCES "kerjasama" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kerjasama_file_mou"
    ADD CONSTRAINT "kerjasama_file_mou_id_kerjasama_fkey" FOREIGN KEY ("id_kerjasama") REFERENCES "kerjasama" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tanhor_personel"
    ADD CONSTRAINT "tanhor_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tanhor_personel"
    ADD CONSTRAINT "tanhor_personel_tanhor_id_fkey" FOREIGN KEY ("tanhor_id") REFERENCES "tanhor" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_mddp_personel"
    ADD CONSTRAINT "score_mddp_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_nieve_personel"
    ADD CONSTRAINT "score_nieve_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_nieve_personel"
    ADD CONSTRAINT "score_nieve_personel_pangkat_id_foreign" FOREIGN KEY ("pangkat_id") REFERENCES "pangkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_rank_tahun_dikpol"
    ADD CONSTRAINT "score_akpol_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_rank_tahun_dikpol"
    ADD CONSTRAINT "score_akpol_personel_diktuk_id_foreign" FOREIGN KEY ("diktuk_id") REFERENCES "diktuk" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimma_personel"
    ADD CONSTRAINT "score_sespimma_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimma_personel"
    ADD CONSTRAINT "score_sespimma_personel_dikbangum_id_foreign" FOREIGN KEY ("dikbangum_id") REFERENCES "dikbangum" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimmen_personel"
    ADD CONSTRAINT "score_sepimmen_personel_dikbangum_id_foreign" FOREIGN KEY ("dikbangum_id") REFERENCES "dikbangum" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimmen_personel"
    ADD CONSTRAINT "score_sepimmen_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimti_personel"
    ADD CONSTRAINT "score_sespimti_personel_dikbangum_id_foreign" FOREIGN KEY ("dikbangum_id") REFERENCES "dikbangum" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_sespimti_personel"
    ADD CONSTRAINT "score_sespimti_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_dikbangspes_personel"
    ADD CONSTRAINT "score_dikbangspes_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_penghargaan_personel"
    ADD CONSTRAINT "score_penghargaan_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_lama_dinas_personel"
    ADD CONSTRAINT "score_lama_dinas_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_lama_dinas_personel_kawaka_fungsi"
    ADD CONSTRAINT "score_lama_dinas_personel_kawaka_fungsi_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score_lama_dinas_personel_kawaka"
    ADD CONSTRAINT "score_lama_dinas_personel_kawaka_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "penghargaan_personel"
    ADD CONSTRAINT "penghargaan_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "penghargaan_personel"
    ADD CONSTRAINT "penghargaan_personel_penghargaan_id_foreign" FOREIGN KEY ("penghargaan_id") REFERENCES "penghargaan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "penghargaan_personel"
    ADD CONSTRAINT "penghargaan_personel_tingkat_id_foreign" FOREIGN KEY ("tingkat_id") REFERENCES "penghargaan_tingkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lama_mddp_personel"
    ADD CONSTRAINT "lama_mddp_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lama_mddp_personel"
    ADD CONSTRAINT "lama_mddp_personel_pangkat_id_foreign" FOREIGN KEY ("pangkat_id") REFERENCES "pangkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lama_dikbangspes_personel"
    ADD CONSTRAINT "lama_dikbangspes_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lama_dikbangspes_personel"
    ADD CONSTRAINT "lama_dikbangspes_personel_dikbangspes_id_foreign" FOREIGN KEY ("dikbangspes_id") REFERENCES "dikbangspes" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lama_dikbangspes_personel"
    ADD CONSTRAINT "lama_dikbangspes_personel_tingkat_id_foreign" FOREIGN KEY ("tingkat_id") REFERENCES "dikbangspes_tingkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bahasa_personel"
    ADD CONSTRAINT "bahasa_personel_bahasa_id_fkey" FOREIGN KEY ("bahasa_id") REFERENCES "bahasa" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bahasa_personel"
    ADD CONSTRAINT "bahasa_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "management_api"
    ADD CONSTRAINT "management_api_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "management_api_detail"
    ADD CONSTRAINT "management_api_detail_management_api_id_fkey" FOREIGN KEY ("management_api_id") REFERENCES "management_api" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "management_api_detail"
    ADD CONSTRAINT "management_api_detail_management_api_source_id_fkey" FOREIGN KEY ("management_api_source_id") REFERENCES "management_api_source" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_group_personel"
    ADD CONSTRAINT "survey_group_personel_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "survey_group" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_group_personel"
    ADD CONSTRAINT "survey_group_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_question"
    ADD CONSTRAINT "survey_question_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "survey_question_status" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_question"
    ADD CONSTRAINT "survey_question_creator_personel_id_fkey" FOREIGN KEY ("creator_personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_destination"
    ADD CONSTRAINT "survey_destination_survey_id_fkey" FOREIGN KEY ("survey_id") REFERENCES "survey_question" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_destination"
    ADD CONSTRAINT "survey_destination_satuan_id_fkey" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_destination"
    ADD CONSTRAINT "survey_destination_pangkat_id_fkey" FOREIGN KEY ("pangkat_id") REFERENCES "pangkat" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_destination"
    ADD CONSTRAINT "survey_destination_survey_group_id_fkey" FOREIGN KEY ("survey_group_id") REFERENCES "survey_group" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_answers"
    ADD CONSTRAINT "survey_answers_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "survey_answers"
    ADD CONSTRAINT "survey_answers_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "survey_question" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow"
    ADD CONSTRAINT "workflow_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_flow"
    ADD CONSTRAINT "workflow_flow_satuan_id_fkey" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_flow"
    ADD CONSTRAINT "workflow_flow_jabatan_id_fkey" FOREIGN KEY ("jabatan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_flow"
    ADD CONSTRAINT "workflow_flow_id_foreign" FOREIGN KEY ("workflow_id") REFERENCES "workflow" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_personel"
    ADD CONSTRAINT "workflow_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_personel"
    ADD CONSTRAINT "workflow_flow_id_foreign" FOREIGN KEY ("workflow_flow_id") REFERENCES "workflow_flow" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction"
    ADD CONSTRAINT "workflow_transaction_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction"
    ADD CONSTRAINT "workflow_transaction_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "workflow" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction_flow"
    ADD CONSTRAINT "workflow_transaction_flow_satuan_id_fkey" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction_flow"
    ADD CONSTRAINT "workflow_transaction_flow_jabatan_id_fkey" FOREIGN KEY ("jabatan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction_flow"
    ADD CONSTRAINT "workflow_transaction_flow_workflow_transaction_id_fkey" FOREIGN KEY ("workflow_transaction_id") REFERENCES "workflow_transaction" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction_personel"
    ADD CONSTRAINT "workflow_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_transaction_personel"
    ADD CONSTRAINT "workflow_transaction_personel_workflow_transaction_flow_id_fkey" FOREIGN KEY ("workflow_transaction_flow_id") REFERENCES "workflow_transaction_flow" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_dokumen"
    ADD CONSTRAINT "workflow_dokumen_workflow_transaction_id_fkey" FOREIGN KEY ("workflow_transaction_id") REFERENCES "workflow_transaction" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_approval_log"
    ADD CONSTRAINT "workflow_approval_log_approver_id_fkey" FOREIGN KEY ("approver_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_approval_log"
    ADD CONSTRAINT "workflow_approval_log_workflow_transaction_flow_id_fkey" FOREIGN KEY ("workflow_transaction_flow_id") REFERENCES "workflow_transaction_flow" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_validation_log"
    ADD CONSTRAINT "workflow_validation_log_signed_by_fkey" FOREIGN KEY ("signed_by") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow_validation_log"
    ADD CONSTRAINT "workflow_validation_log_workflow_dokumen_id_fkey" FOREIGN KEY ("workflow_dokumen_id") REFERENCES "workflow_dokumen" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pekerjaan_keluarga"
    ADD CONSTRAINT "pekerjaan_keluarga_jenis_pekerjaan_id_foreign" FOREIGN KEY ("jenis_pekerjaan_id") REFERENCES "jenis_pekerjaan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pekerjaan_keluarga"
    ADD CONSTRAINT "pekerjaan_keluarga_keluarga_personel_id_foreign" FOREIGN KEY ("keluarga_personel_id") REFERENCES "keluarga_personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "keluarga_personel"
    ADD CONSTRAINT "parent_keluarga_personel_id_foreign" FOREIGN KEY ("parent_hubungan_keluarga_id") REFERENCES "keluarga_personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "keluarga_personel"
    ADD CONSTRAINT "parent_personel_hubungan_keluarga_id_foreign" FOREIGN KEY ("hubungan_keluarga_id") REFERENCES "hubungan_keluarga" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "keluarga_personel"
    ADD CONSTRAINT "parent_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "keluarga_personel"
    ADD CONSTRAINT "parent_personel_agama_id_foreign" FOREIGN KEY ("agama_id") REFERENCES "agama" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "keluarga_personel"
    ADD CONSTRAINT "parent_personel_golongan_id_foreign" FOREIGN KEY ("golongan_id") REFERENCES "golongan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "alamat"
    ADD CONSTRAINT "alamat_alamat_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "alamat_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "alamat"
    ADD CONSTRAINT "alamat_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "history_status_ncr"
    ADD CONSTRAINT "history_status_ncr_pengajuan_ncr_id_foreign" FOREIGN KEY ("pengajuan_ncr_id") REFERENCES "pengajuan_ncr" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_cuti"
    ADD CONSTRAINT "pengajuan_cuti_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_ncr"
    ADD CONSTRAINT "pengajuan_ncr_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_ncr"
    ADD CONSTRAINT "pengajuan_ncr_status_kawin_pemohon_id_foreign" FOREIGN KEY ("status_kawin_pemohon_id") REFERENCES "status_kawin" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_ncr"
    ADD CONSTRAINT "pengajuan_ncr_status_kawin_pasangan_id_foreign" FOREIGN KEY ("status_kawin_pasangan_id") REFERENCES "status_kawin" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_ncr"
    ADD CONSTRAINT "pengajuan_ncr_alamat_personel_id_foreign" FOREIGN KEY ("alamat_personel_id") REFERENCES "alamat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pengajuan_ncr"
    ADD CONSTRAINT "pengajuan_ncr_satuan_id_foreign" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dokumen_ncr"
    ADD CONSTRAINT "dokumen_ncr_pengajuan_ncr_id_fkey" FOREIGN KEY ("pengajuan_ncr_id") REFERENCES "pengajuan_ncr" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikum_personel"
    ADD CONSTRAINT "dikum_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikum_personel"
    ADD CONSTRAINT "dikum_personel_institusi_id_fkey" FOREIGN KEY ("institusi_id") REFERENCES "institusi" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikum_personel"
    ADD CONSTRAINT "dikum_personel_dikum_id_fkey" FOREIGN KEY ("dikum_id") REFERENCES "dikum" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikum_personel"
    ADD CONSTRAINT "dikum_personel_jurusan_id_fkey" FOREIGN KEY ("jurusan_id") REFERENCES "jurusan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikum_personel"
    ADD CONSTRAINT "dikum_personel_gelar_id_fkey" FOREIGN KEY ("gelar_id") REFERENCES "gelar" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel_sign"
    ADD CONSTRAINT "personel_sign_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_personel"
    ADD CONSTRAINT "bagassus_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_personel"
    ADD CONSTRAINT "bagassus_personel_instansi_id_fkey" FOREIGN KEY ("instansi_id") REFERENCES "penugasan_instansi" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_personel"
    ADD CONSTRAINT "bagassus_personel_location_type_id_fkey" FOREIGN KEY ("location_type_id") REFERENCES "bagassus_location_type" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_personel"
    ADD CONSTRAINT "bagassus_personel_sprin_file_id_fkey" FOREIGN KEY ("sprin_file_id") REFERENCES "bagassus_sprin_file" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_personel"
    ADD CONSTRAINT "bagassus_personel_kep_file_id_fkey" FOREIGN KEY ("kep_file_id") REFERENCES "bagassus_kep_file" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "penugasan_instansi"
    ADD CONSTRAINT "penugasan_instansi_location_type_id_fkey" FOREIGN KEY ("location_type_id") REFERENCES "bagassus_location_type" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_seleksi"
    ADD CONSTRAINT "bagassus_seleksi_logo_file_id_fkey" FOREIGN KEY ("logo_file_id") REFERENCES "bagassus_seleksi_file" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_seleksi"
    ADD CONSTRAINT "bagassus_seleksi_document_file_id_fkey" FOREIGN KEY ("document_file_id") REFERENCES "bagassus_seleksi_file" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_tahap_seleksi"
    ADD CONSTRAINT "bagassus_tahap_seleksi_seleksi_id_fkey" FOREIGN KEY ("seleksi_id") REFERENCES "bagassus_seleksi" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_seleksi_syarat_value"
    ADD CONSTRAINT "bagassus_seleksi_syarat_value_syarat_id_fkey" FOREIGN KEY ("syarat_id") REFERENCES "bagassus_seleksi_syarat" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_seleksi_personel"
    ADD CONSTRAINT "bagassus_seleksi_personel_seleksi_id_fkey" FOREIGN KEY ("seleksi_id") REFERENCES "bagassus_seleksi" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bagassus_seleksi_personel"
    ADD CONSTRAINT "bagassus_seleksi_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "seleksi_bagrimdik_tahap"
    ADD CONSTRAINT "seleksi_bagrimdik_tahap_seleksi_bagrimdik_id_fkey" FOREIGN KEY ("seleksi_bagrimdik_id") REFERENCES "seleksi_bagrimdik" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_device_token"
    ADD CONSTRAINT "users_device_token_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi_personel"
    ADD CONSTRAINT "konsultasi_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jasmani_personel"
    ADD CONSTRAINT "jasmani_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessment_personel"
    ADD CONSTRAINT "assessment_personel_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_agama_id_fkey" FOREIGN KEY ("agama_id") REFERENCES "agama" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_status_kawin_id_fkey" FOREIGN KEY ("status_kawin_id") REFERENCES "status_kawin" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_jenis_diktuk_id_fkey" FOREIGN KEY ("jenis_diktuk_id") REFERENCES "jenis_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_kompetensi_diktuk_id_fkey" FOREIGN KEY ("kompetensi_diktuk_id") REFERENCES "kompetensi_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_sub_kompetensi_diktuk_id_fkey" FOREIGN KEY ("sub_kompetensi_diktuk_id") REFERENCES "sub_kompetensi_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_sub_sub_kompetensi_diktuk_id_fkey" FOREIGN KEY ("sub_sub_kompetensi_diktuk_id") REFERENCES "sub_sub_kompetensi_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_tmp_dik_id_fkey" FOREIGN KEY ("tmp_dik_id") REFERENCES "tmpdik" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_asal_rim_polda_id_fkey" FOREIGN KEY ("asal_rim_polda_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa"
    ADD CONSTRAINT "siswa_asal_rim_polres_id_fkey" FOREIGN KEY ("asal_rim_polres_id") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kompetensi_diktuk"
    ADD CONSTRAINT "kompetensi_diktuk_diktuk_id_fkey" FOREIGN KEY ("diktuk_id") REFERENCES "jenis_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sub_kompetensi_diktuk"
    ADD CONSTRAINT "sub_kompetensi_diktuk_kompetensi_id_fkey" FOREIGN KEY ("kompetensi_id") REFERENCES "kompetensi_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sub_sub_kompetensi_diktuk"
    ADD CONSTRAINT "sub_sub_kompetensi_diktuk_sub_kompetensi_id_fkey" FOREIGN KEY ("sub_kompetensi_id") REFERENCES "sub_kompetensi_diktuk" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_penerimaan"
    ADD CONSTRAINT "siswa_penerimaan_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_fisik"
    ADD CONSTRAINT "siswa_fisik_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keahlian"
    ADD CONSTRAINT "siswa_keahlian_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keluarga"
    ADD CONSTRAINT "siswa_keluarga_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kecamatan"
    ADD CONSTRAINT "kecamatan_kabupaten_id_fkey" FOREIGN KEY ("kabupaten_id") REFERENCES "kabupaten" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kelurahan"
    ADD CONSTRAINT "kelurahan_kecamatan_id_fkey" FOREIGN KEY ("kecamatan_id") REFERENCES "kecamatan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_alamat"
    ADD CONSTRAINT "siswa_alamat_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_alamat"
    ADD CONSTRAINT "siswa_alamat_kecamatan_id_fkey" FOREIGN KEY ("kecamatan_id") REFERENCES "kecamatan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_alamat"
    ADD CONSTRAINT "siswa_alamat_kelurahan_id_fkey" FOREIGN KEY ("kelurahan_id") REFERENCES "kelurahan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_alamat"
    ADD CONSTRAINT "siswa_alamat_kabupaten_id_fkey" FOREIGN KEY ("kabupaten_id") REFERENCES "kabupaten" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_alamat"
    ADD CONSTRAINT "siswa_alamat_provinsi_id_fkey" FOREIGN KEY ("provinsi_id") REFERENCES "provinsi" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi"
    ADD CONSTRAINT "konsultasi_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi"
    ADD CONSTRAINT "konsultasi_polda_id_fkey" FOREIGN KEY ("polda_id") REFERENCES "satuan" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi"
    ADD CONSTRAINT "konsultasi_konselor_id_fkey" FOREIGN KEY ("konselor_id") REFERENCES "konsultasi_konselor" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi"
    ADD CONSTRAINT "konsultasi_konsultasi_jenis_id_fkey" FOREIGN KEY ("konsultasi_jenis_id") REFERENCES "konsultasi_jenis" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi_file"
    ADD CONSTRAINT "konsultasi_file_konsultasi_id_fkey" FOREIGN KEY ("konsultasi_id") REFERENCES "konsultasi" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "konsultasi_konselor"
    ADD CONSTRAINT "konsultasi_konselor_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_patma"
    ADD CONSTRAINT "siswa_patma_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_patma"
    ADD CONSTRAINT "siswa_patma_satker_patma_fkey" FOREIGN KEY ("satker_patma") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_diktuk"
    ADD CONSTRAINT "siswa_diktuk_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "peraturan_polri_file"
    ADD CONSTRAINT "peraturan_polri_file_id_peraturan_polri_fkey" FOREIGN KEY ("id_peraturan_polri") REFERENCES "peraturan_polri" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel_sertifikasi"
    ADD CONSTRAINT "personel_sertifikasi_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel_kemampuan_bahasa"
    ADD CONSTRAINT "personel_kemampuan_bahasa_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_siswa_id_fkey" FOREIGN KEY ("siswa_id") REFERENCES "siswa" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_angket_1_fkey" FOREIGN KEY ("angket_1") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_angket_2_fkey" FOREIGN KEY ("angket_2") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_angket_3_fkey" FOREIGN KEY ("angket_3") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_angket_4_fkey" FOREIGN KEY ("angket_4") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "siswa_keswa_angket"
    ADD CONSTRAINT "siswa_keswa_angket_angket_5_fkey" FOREIGN KEY ("angket_5") REFERENCES "satuan" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
