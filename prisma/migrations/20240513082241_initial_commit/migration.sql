-- CreateTable
CREATE TABLE "personel"
(
    "id"                    BIGSERIAL NOT NULL,
    "uid"                   UUID      NOT NULL,
    "nrp"                   VARCHAR(255),
    "nama_lengkap"          VARCHAR(255),
    "tanggal_lahir"         DATE,
    "jenis_k<PERSON>min"         VARCHAR(255),
    "foto_file"             VARCHAR(255),
    "tempat_lahir_id"       BIGINT,
    "agama_id"              BIGINT,
    "ktp_nomor"             VARCHAR(255),
    "ktp_file"              VARCHAR(255),
    "kk_nomor"              VARCHAR(255),
    "kk_file"               VARCHAR(255),
    "status_kawin_id"       BIGINT,
    "golongan_darah"        VARCHAR(255),
    "suku_id"               BIGINT,
    "email"                 VARCHAR(255),
    "no_hp"                 VARCHAR(255),
    "akta_kelahiran_file"   VARCHAR(255),
    "asabri_nomor"          VARCHAR(255),
    "asabri_file"           VARCHAR(255),
    "bpjs_nomor"            VARCHAR(255),
    "bpjs_file"             VARCHAR(255),
    "paspor_nomor"          VARCHAR(255),
    "paspor_file"           VARCHAR(255),
    "npwp_nomor"            VARCHAR(255),
    "npwp_file"             VARCHAR(255),
    "lhkpn_file"            VARCHAR(255),
    "masa_dinas_surut_tmt"  DATE,
    "masa_dinas_surut_file" VARCHAR(255),
    "anak_ke"               INTEGER,
    "jumlah_saudara"        INTEGER,
    "status_aktif_id"       BIGINT,
    "created_at"            TIMESTAMP(0),
    "updated_at"            TIMESTAMP(0),
    "deleted_at"            TIMESTAMP(0),

    CONSTRAINT "personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "agama"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "agama_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "berita"
(
    "id"          SERIAL       NOT NULL,
    "title"       TEXT         NOT NULL,
    "description" TEXT         NOT NULL,
    "createdBy"   TEXT,
    "createdAt"   TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt"   TIMESTAMP(3),
    "deletedAt"   TIMESTAMP(3),

    CONSTRAINT "berita_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "berita_foto"
(
    "id"           SERIAL       NOT NULL,
    "id_berita"    INTEGER      NOT NULL,
    "originalname" TEXT         NOT NULL,
    "encoding"     TEXT         NOT NULL,
    "mimetype"     TEXT         NOT NULL,
    "destination"  TEXT         NOT NULL,
    "filename"     TEXT         NOT NULL,
    "path"         TEXT         NOT NULL,
    "size"         BIGINT       NOT NULL,
    "createdAt"    TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt"    TIMESTAMP(3),
    "deletedAt"    TIMESTAMP(3),

    CONSTRAINT "berita_foto_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "lokasi_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangspes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes_lokasi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangspes_lokasi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangspes_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "dikbangspes_id"       BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),

    CONSTRAINT "dikbangspes_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum"
(
    "id"                    BIGSERIAL    NOT NULL,
    "nama"                  VARCHAR(255) NOT NULL,
    "nama_alternatif"       VARCHAR(255) NOT NULL,
    "dikbangum_kategori_id" BIGINT,
    "created_at"            TIMESTAMP(0),
    "updated_at"            TIMESTAMP(0),
    "deleted_at"            TIMESTAMP(0),
    "kategori_id"           BIGINT,

    CONSTRAINT "dikbangum_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "dikbangum_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dikbangum_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "dikbangum_id"         BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),
    "gelar_id"             BIGINT,
    "is_tampil_gelar"      BOOLEAN,

    CONSTRAINT "dikbangum_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk"
(
    "id"          BIGSERIAL    NOT NULL,
    "nama"        VARCHAR(255) NOT NULL,
    "kategori_id" BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "diktuk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "diktuk_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "diktuk_personel"
(
    "id"                   BIGSERIAL NOT NULL,
    "diktuk_id"            BIGINT    NOT NULL,
    "personel_id"          BIGINT    NOT NULL,
    "tanggal_masuk"        DATE,
    "tanggal_selesai"      DATE,
    "nilai"                DOUBLE PRECISION,
    "transkrip_nilai_file" VARCHAR(255),
    "ijazah_no"            VARCHAR(255),
    "ijazah_file"          VARCHAR(255),
    "ranking"              INTEGER,
    "jumlah_siswa"         INTEGER,
    "gelar_id"             BIGINT,
    "is_tampil_gelar"      BOOLEAN DEFAULT true,
    "created_at"           TIMESTAMP(0),
    "updated_at"           TIMESTAMP(0),
    "deleted_at"           TIMESTAMP(0),

    CONSTRAINT "diktuk_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "eselon"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "eselon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gelar"
(
    "id"                BIGSERIAL    NOT NULL,
    "nama"              VARCHAR(255) NOT NULL,
    "is_gelar_belakang" BOOLEAN      NOT NULL DEFAULT true,
    "created_at"        TIMESTAMP(0),
    "updated_at"        TIMESTAMP(0),
    "deleted_at"        TIMESTAMP(0),

    CONSTRAINT "gelar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "golongan"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "golongan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan"
(
    "id"             BIGSERIAL    NOT NULL,
    "nama"           VARCHAR(255) NOT NULL,
    "dsp"            INTEGER,
    "nivellering_id" BIGINT,
    "satuan_id"      BIGINT       NOT NULL,
    "atasan_id"      BIGINT       NOT NULL,
    "kategori_id"    BIGINT       NOT NULL,
    "created_at"     TIMESTAMP(0),
    "updated_at"     TIMESTAMP(0),
    "deleted_at"     TIMESTAMP(0),

    CONSTRAINT "jabatan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "jabatan_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan_personel"
(
    "id"          BIGSERIAL NOT NULL,
    "personel_id" BIGINT,
    "jabatan_id"  BIGINT,
    "tmt_jabatan" DATE,
    "jabatan"     VARCHAR(255),
    "skep_no"     VARCHAR(255),
    "skep_file"   VARCHAR(255),
    "is_ps"       BOOLEAN,
    "keterangan"  VARCHAR(255),
    "st_no"       VARCHAR(255),
    "st_file"     VARCHAR(255),
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "test_jabatan_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jabatan_personel_non_relational"
(
    "id"          BIGINT,
    "personel_id" BIGINT,
    "jabatan_id"  BIGINT,
    "tmt_jabatan" DATE,
    "jabatan"     VARCHAR(255),
    "skep_no"     VARCHAR(255),
    "skep_file"   VARCHAR(255),
    "is_ps"       BOOLEAN,
    "keterangan"  VARCHAR(255),
    "st_no"       VARCHAR(255),
    "st_file"     VARCHAR(255),
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0)
);

-- CreateTable
CREATE TABLE "kabupaten"
(
    "id"          BIGSERIAL    NOT NULL,
    "nama"        VARCHAR(255) NOT NULL,
    "provinsi_id" BIGINT,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "kabupaten_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "negara"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "negara_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "nivellering"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "eselon_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "nivellering_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat"
(
    "id"           BIGSERIAL    NOT NULL,
    "nama"         VARCHAR(255) NOT NULL,
    "nama_singkat" VARCHAR(255) NOT NULL,
    "kategori_id"  BIGINT,
    "golongan_id"  BIGINT,
    "created_at"   TIMESTAMP(0),
    "updated_at"   TIMESTAMP(0),
    "deleted_at"   TIMESTAMP(0),

    CONSTRAINT "pangkat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat_kategori"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "pangkat_kategori_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pangkat_personel"
(
    "id"          BIGSERIAL NOT NULL,
    "pangkat_id"  BIGINT    NOT NULL,
    "tmt"         VARCHAR(255),
    "kep_nomor"   VARCHAR(255),
    "kep_file"    VARCHAR(255),
    "kep_tanggal" VARCHAR(255),
    "personel_id" BIGINT    NOT NULL,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "pangkat_personel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provinsi"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "negara_id"  BIGINT       NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "provinsi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "satuan"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "alamat"     TEXT,
    "jenis_id"   BIGINT       NOT NULL,
    "is_aktif"   BOOLEAN      NOT NULL,
    "atasan_id"  BIGINT,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "satuan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "satuan_jenis"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "satuan_jenis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "status_aktif"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "status_aktif_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "status_kawin"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "status_kawin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "suku"
(
    "id"         BIGSERIAL    NOT NULL,
    "nama"       VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "suku_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users"
(
    "id"          BIGSERIAL    NOT NULL,
    "password"    VARCHAR(255) NOT NULL,
    "personel_id" BIGINT,
    "created_at"  TIMESTAMP(0),
    "updated_at"  TIMESTAMP(0),
    "deleted_at"  TIMESTAMP(0),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users_role"
(
    "id"         BIGSERIAL NOT NULL,
    "users_id"   BIGINT,
    "level_id"   BIGINT,
    "role_id"    INTEGER,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "users_role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "level"
(
    "id"         BIGSERIAL NOT NULL,
    "nama"       TEXT,
    "created_at" TIMESTAMP(0),
    "updated_at" TIMESTAMP(0),
    "deleted_at" TIMESTAMP(0),

    CONSTRAINT "level_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role"
(
    "id"        SERIAL       NOT NULL,
    "nama"      TEXT         NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permission"
(
    "id"        SERIAL       NOT NULL,
    "nama"      TEXT         NOT NULL,
    "desc"      TEXT         NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "role_permission"
(
    "id"            SERIAL       NOT NULL,
    "permission_id" INTEGER      NOT NULL,
    "role_id"       INTEGER      NOT NULL,
    "createdAt"     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt"     TIMESTAMP(3),
    "deletedAt"     TIMESTAMP(3),

    CONSTRAINT "role_permission_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "berita_foto_id_berita_key" ON "berita_foto" ("id_berita");

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_agama_id_foreign" FOREIGN KEY ("agama_id") REFERENCES "agama" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_status_aktif_id_foreign" FOREIGN KEY ("status_aktif_id") REFERENCES "status_aktif" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_status_kawin_id_foreign" FOREIGN KEY ("status_kawin_id") REFERENCES "status_kawin" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_suku_id_foreign" FOREIGN KEY ("suku_id") REFERENCES "suku" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "personel"
    ADD CONSTRAINT "personel_tempat_lahir_id_foreign" FOREIGN KEY ("tempat_lahir_id") REFERENCES "kabupaten" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "berita_foto"
    ADD CONSTRAINT "berita_foto_id_berita_fkey" FOREIGN KEY ("id_berita") REFERENCES "berita" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes"
    ADD CONSTRAINT "dikbangspes_lokasi_id_foreign" FOREIGN KEY ("lokasi_id") REFERENCES "dikbangspes_lokasi" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes_personel"
    ADD CONSTRAINT "dikbangspes_personel_dikbangspes_id_foreign" FOREIGN KEY ("dikbangspes_id") REFERENCES "dikbangspes" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangspes_personel"
    ADD CONSTRAINT "dikbangspes_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum"
    ADD CONSTRAINT "dikbangum_dikbangum_kategori_id_foreign" FOREIGN KEY ("dikbangum_kategori_id") REFERENCES "dikbangum_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum_personel"
    ADD CONSTRAINT "dikbangum_personel_dikbangum_id_foreign" FOREIGN KEY ("dikbangum_id") REFERENCES "dikbangum" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dikbangum_personel"
    ADD CONSTRAINT "dikbangum_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk"
    ADD CONSTRAINT "diktuk_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "diktuk_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_diktuk_id_foreign" FOREIGN KEY ("diktuk_id") REFERENCES "diktuk" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_gelar_id_foreign" FOREIGN KEY ("gelar_id") REFERENCES "gelar" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "diktuk_personel"
    ADD CONSTRAINT "diktuk_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_atasan_id_foreign" FOREIGN KEY ("atasan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "jabatan_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_nivellering_id_foreign" FOREIGN KEY ("nivellering_id") REFERENCES "nivellering" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan"
    ADD CONSTRAINT "jabatan_satuan_id_foreign" FOREIGN KEY ("satuan_id") REFERENCES "satuan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan_personel"
    ADD CONSTRAINT "jabatan_personel_jabatan_id_foreign" FOREIGN KEY ("jabatan_id") REFERENCES "jabatan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "jabatan_personel"
    ADD CONSTRAINT "jabatan_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kabupaten"
    ADD CONSTRAINT "kabupaten_provinsi_id_foreign" FOREIGN KEY ("provinsi_id") REFERENCES "provinsi" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "nivellering"
    ADD CONSTRAINT "nivellering_eselon_id_foreign" FOREIGN KEY ("eselon_id") REFERENCES "eselon" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat"
    ADD CONSTRAINT "pangkat_golongan_id_foreign" FOREIGN KEY ("golongan_id") REFERENCES "golongan" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat"
    ADD CONSTRAINT "pangkat_kategori_id_foreign" FOREIGN KEY ("kategori_id") REFERENCES "pangkat_kategori" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat_personel"
    ADD CONSTRAINT "pangkat_personel_pangkat_id_foreign" FOREIGN KEY ("pangkat_id") REFERENCES "pangkat" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pangkat_personel"
    ADD CONSTRAINT "pangkat_personel_personel_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provinsi"
    ADD CONSTRAINT "provinsi_negara_id_foreign" FOREIGN KEY ("negara_id") REFERENCES "negara" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "satuan"
    ADD CONSTRAINT "satuan_jenis_id_foreign" FOREIGN KEY ("jenis_id") REFERENCES "satuan_jenis" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users"
    ADD CONSTRAINT "users_id_foreign" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_users_id_fkey" FOREIGN KEY ("users_id") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_level_id_fkey" FOREIGN KEY ("level_id") REFERENCES "level" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users_role"
    ADD CONSTRAINT "users_role_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "role" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permission"
    ADD CONSTRAINT "role_permission_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permission" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permission"
    ADD CONSTRAINT "role_permission_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "role" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
