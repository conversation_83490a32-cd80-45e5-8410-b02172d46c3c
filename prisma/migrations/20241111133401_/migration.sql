-- CreateEnum
CREATE TYPE "ekta_bank" AS ENUM ('BRI', 'BNI', 'MANDIR<PERSON>', 'BTN', "BSI");

-- CreateTable
CREATE TABLE "ekta"
(
    "id"          BIGSERIAL    NOT NULL,
    "personel_id" BIGINT       NOT NULL,
    "ekta_bank"   "ekta_bank"  NOT NULL,
    "tanggal"     TIMESTAMP(3) NOT NULL,
    "created_by"  BIGINT       NOT NULL,
    "created_at"  TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at"  TIMESTAMP(0),

    CONSTRAINT "ekta_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ekta"
    ADD CONSTRAINT "ekta_personel_id_fkey" FOREIGN KEY ("personel_id") REFERENCES "personel" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
