model tanhor {
  id                      BigInt             @id @default(autoincrement())
  nama                    String             @db.Var<PERSON>har(255)
  is_aktif                <PERSON>            @default(true)
  created_at              DateTime?          @default(now()) @db.Timestamp(0)
  updated_at              DateTime?          @updatedAt @db.Timestamp(0)
  deleted_at              DateTime?          @db.Timestamp(0)
  pengajuan_tanhor_tanhor pengajuan_tanhor[] @relation("tanhor_to_tanhor")
  tanhor_personel         tanhor_personel[]
}

model tanhor_personel {
  id                BigInt             @id @default(autoincrement())
  personel_id       BigInt?
  tanhor_id         BigInt?
  tgl_tanhor        DateTime?          @db.Date
  surat_no          String?            @db.VarChar(255)
  surat_file        String?            @db.VarChar(255)
  tanggal_perubahan DateTime?          @db.Timestamp(0)
  created_at        DateTime?          @default(now()) @db.Timestamp(0)
  updated_at        DateTime?          @updatedAt @db.Timestamp(0)
  deleted_at        DateTime?          @db.Timestamp(0)
  pengajuan_tanhor  pengajuan_tanhor[]
  personel          personel?          @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "tanhor_personel_personel_id_foreign")
  tanhor            tanhor?            @relation(fields: [tanhor_id], references: [id], onDelete: Cascade, map: "tanhor_personel_tanhor_id_foreign")
}

model pengajuan_tanhor {
  id                        BigInt           @id @default(autoincrement())
  personel_id               BigInt?
  tanhor_id                 BigInt?
  status                    String?
  files                     Json[]           @db.Json
  created_by                BigInt?
  approved_by               BigInt?
  rejected_by               BigInt?
  created_at                DateTime?        @default(now()) @db.Date
  updated_at                DateTime?        @updatedAt @db.Date
  approved_at               DateTime?        @db.Date
  rejected_at               DateTime?        @db.Date
  tanhor_personel_id        BigInt?
  kep_tanhor                String?
  alasan_penolakan          String?
  nomor_surat_kep           String?
  personel                  personel?        @relation(fields: [personel_id], references: [id])
  pengajuan_tanhor_personel personel?        @relation("personel_to_personel", fields: [personel_id], references: [id], map: "pengajuan_tanhor_personel_id_foreign")
  pengajuan_tanhor_tanhor   tanhor?          @relation("tanhor_to_tanhor", fields: [tanhor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_tanhor_personel_tanhor_id_foreign")
  tanhor_personel           tanhor_personel? @relation(fields: [tanhor_personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_tanhor_personel_tanhor_personel_id_foreign")
}

