model selection_bagassus {
  id           BigInt              @id @default(autoincrement())
  title        String              @db.VarChar
  total_stages Int                 @db.Integer
  banner_id    BigInt?             @unique @db.BigInt
  description  String?             @db.VarChar
  type         selection_type_enum
  is_closed    Boolean?            @default(false)
  created_by   BigInt              @db.BigInt
  created_at   DateTime            @default(now()) @db.Timestamp(6)
  updated_by   BigInt?             @db.BigInt
  updated_at   DateTime?           @db.Timestamp(6)
  deleted_by   BigInt?             @db.BigInt
  deleted_at   DateTime?           @db.Timestamp(6)

  created_by_user                     users                                 @relation(fields: [created_by], references: [id])
  selection_bagassus_stage            selection_bagassus_stage[]
  selection_bagassus_participant      selection_bagassus_participant[]
  selection_bagassus_requirement      selection_bagassus_requirement[]
  selection_bagassus_file_requirement selection_bagassus_file_requirement[]
  selection_bagassus_files_banner     selection_file?                       @relation(fields: [banner_id], references: [id])
}

model selection_bagassus_stage {
  id           BigInt                      @id @default(autoincrement()) @db.BigInt
  stage        Int                         @db.Integer
  name         String                      @db.VarChar(200)
  selection_id BigInt                      @db.BigInt
  banner_id    BigInt?                     @unique @db.BigInt
  status       selection_stage_status_enum
  start_date   DateTime                    @db.Date
  end_date     DateTime                    @db.Date
  updated_by   BigInt?                     @db.BigInt
  updated_at   DateTime?                   @updatedAt @db.Timestamp(0)

  selection                             selection_bagassus               @relation(fields: [selection_id], references: [id])
  selection_bagassus_stage_files_banner selection_file?                  @relation(fields: [banner_id], references: [id])
  participants                          selection_bagassus_participant[]
}

model selection_bagassus_participant {
  id                  BigInt  @id() @default(autoincrement())
  registration_number String  @db.VarChar
  exam_number         String? @db.VarChar
  personel_id         BigInt  @db.BigInt
  selection_id        BigInt  @db.BigInt
  current_stage_id    BigInt  @db.BigInt
  history_stages      Json    @db.Json
  file_uploads        Json?   @db.Json

  selection       selection_bagassus       @relation(fields: [selection_id], references: [id])
  selection_stage selection_bagassus_stage @relation(fields: [current_stage_id], references: [id])
  personel        personel                 @relation(fields: [personel_id], references: [id])

  @@unique([registration_number])
}

model selection_bagassus_requirement {
  id              BigInt               @id @default(autoincrement()) @db.BigInt
  selection_id    BigInt               @db.BigInt
  requirement_id  BigInt               @db.BigInt
  is_required     Boolean              @default(false) @db.Boolean
  value           String?              @db.Text
  comparison_type comparison_type_enum
  created_at      DateTime             @default(now())
  updated_at      DateTime?            @updatedAt

  selection             selection_bagassus    @relation(fields: [selection_id], references: [id])
  selection_requirement selection_requirement @relation(fields: [requirement_id], references: [id])

  @@unique([selection_id, requirement_id, comparison_type])
}

model selection_bagassus_file_requirement {
  id           BigInt   @id @default(autoincrement()) @db.BigInt
  selection_id BigInt   @db.BigInt
  is_required  Boolean  @default(false) @db.Boolean
  title        String   @db.VarChar()
  extensions   String[]
  max_size     Int?     @db.Integer
  min_size     Int?     @db.Integer
  created_at   DateTime @default(now())

  selection selection_bagassus @relation(fields: [selection_id], references: [id])
}

model selection_bagrimdik {
  id           BigInt              @id @default(autoincrement())
  title        String              @db.VarChar
  total_stages Int                 @db.Integer
  banner_id    BigInt?             @unique @db.BigInt
  description  String?             @db.VarChar
  type         selection_type_enum
  created_by   BigInt              @db.BigInt
  created_at   DateTime            @default(now()) @db.Timestamp(6)
  updated_by   BigInt?             @db.BigInt
  updated_at   DateTime?           @db.Timestamp(6)
  deleted_by   BigInt?             @db.BigInt
  deleted_at   DateTime?           @db.Timestamp(6)

  created_by_user                      users                                  @relation(fields: [created_by], references: [id])
  selection_bagrimdik_stage            selection_bagrimdik_stage[]
  selection_bagrimdik_participant      selection_bagrimdik_participant[]
  selection_bagrimdik_requirement      selection_bagrimdik_requirement[]
  selection_bagrimdik_file_requirement selection_bagrimdik_file_requirement[]
  selection_bagrimdik_files_banner     selection_file?                        @relation(fields: [banner_id], references: [id])
}

model selection_bagrimdik_stage {
  id           BigInt                      @id @default(autoincrement()) @db.BigInt
  stage        Int                         @db.Integer
  name         String                      @db.VarChar(200)
  selection_id BigInt                      @db.BigInt
  banner_id    BigInt?                     @unique @db.BigInt
  status       selection_stage_status_enum
  start_date   DateTime                    @db.Date
  end_date     DateTime                    @db.Date
  updated_by   BigInt?                     @db.BigInt
  updated_at   DateTime?                   @updatedAt @db.Timestamp(0)

  selection                              selection_bagrimdik               @relation(fields: [selection_id], references: [id])
  selection_bagrimdik_stage_files_banner selection_file?                   @relation(fields: [banner_id], references: [id])
  participants                           selection_bagrimdik_participant[]
}

model selection_bagrimdik_participant {
  id                  BigInt  @id() @default(autoincrement())
  registration_number String  @db.VarChar
  exam_number         String? @db.VarChar
  personel_id         BigInt  @db.BigInt
  selection_id        BigInt  @db.BigInt
  current_stage_id    BigInt  @db.BigInt
  history_stages      Json    @db.Json
  file_uploads        Json?   @db.Json

  selection       selection_bagrimdik       @relation(fields: [selection_id], references: [id])
  selection_stage selection_bagrimdik_stage @relation(fields: [current_stage_id], references: [id])
  personel        personel                  @relation(fields: [personel_id], references: [id])

  @@unique([registration_number])
}

model selection_bagrimdik_requirement {
  id              BigInt               @id @default(autoincrement()) @db.BigInt
  selection_id    BigInt               @db.BigInt
  requirement_id  BigInt               @db.BigInt
  is_required     Boolean              @default(false) @db.Boolean
  value           String?              @db.Text
  comparison_type comparison_type_enum
  created_at      DateTime             @default(now())
  updated_at      DateTime?            @updatedAt

  selection             selection_bagrimdik   @relation(fields: [selection_id], references: [id])
  selection_requirement selection_requirement @relation(fields: [requirement_id], references: [id])

  @@unique([selection_id, requirement_id, comparison_type])
}

model selection_bagrimdik_file_requirement {
  id           BigInt   @id @default(autoincrement()) @db.BigInt
  selection_id BigInt   @db.BigInt
  is_required  Boolean  @default(false) @db.Boolean
  title        String   @db.VarChar()
  extensions   String[]
  max_size     Int?     @db.Integer
  min_size     Int?     @db.Integer
  created_at   DateTime @default(now())

  selection selection_bagrimdik @relation(fields: [selection_id], references: [id])
}

model selection_baglekdik {
  id           BigInt              @id @default(autoincrement())
  title        String              @db.VarChar
  total_stages Int                 @db.Integer
  banner_id    BigInt?             @unique @db.BigInt
  description  String?             @db.VarChar
  type         selection_type_enum
  created_by   BigInt              @db.BigInt
  created_at   DateTime            @default(now()) @db.Timestamp(6)
  updated_by   BigInt?             @db.BigInt
  updated_at   DateTime?           @db.Timestamp(6)
  deleted_by   BigInt?             @db.BigInt
  deleted_at   DateTime?           @db.Timestamp(6)

  created_by_user                      users                                  @relation(fields: [created_by], references: [id])
  selection_baglekdik_stage            selection_baglekdik_stage[]
  selection_baglekdik_participant      selection_baglekdik_participant[]
  selection_baglekdik_requirement      selection_baglekdik_requirement[]
  selection_baglekdik_file_requirement selection_baglekdik_file_requirement[]
  selection_baglekdik_files_banner     selection_file?                        @relation(fields: [banner_id], references: [id])
}

model selection_baglekdik_stage {
  id           BigInt                      @id @default(autoincrement()) @db.BigInt
  stage        Int                         @db.Integer
  name         String                      @db.VarChar(200)
  selection_id BigInt                      @db.BigInt
  banner_id    BigInt?                     @unique @db.BigInt
  status       selection_stage_status_enum
  start_date   DateTime                    @db.Date
  end_date     DateTime                    @db.Date
  updated_by   BigInt?                     @db.BigInt
  updated_at   DateTime?                   @updatedAt @db.Timestamp(0)

  selection                              selection_baglekdik               @relation(fields: [selection_id], references: [id])
  selection_baglekdik_stage_files_banner selection_file?                   @relation(fields: [banner_id], references: [id])
  participants                           selection_baglekdik_participant[]
}

model selection_baglekdik_participant {
  id                  BigInt  @id() @default(autoincrement())
  registration_number String  @db.VarChar
  exam_number         String? @db.VarChar
  personel_id         BigInt  @db.BigInt
  selection_id        BigInt  @db.BigInt
  current_stage_id    BigInt  @db.BigInt
  history_stages      Json    @db.Json
  file_uploads        Json?   @db.Json

  selection       selection_baglekdik       @relation(fields: [selection_id], references: [id])
  selection_stage selection_baglekdik_stage @relation(fields: [current_stage_id], references: [id])
  personel        personel                  @relation(fields: [personel_id], references: [id])

  @@unique([registration_number])
}

model selection_baglekdik_requirement {
  id              BigInt               @id @default(autoincrement()) @db.BigInt
  selection_id    BigInt               @db.BigInt
  requirement_id  BigInt               @db.BigInt
  is_required     Boolean              @default(false) @db.Boolean
  value           String?              @db.Text
  comparison_type comparison_type_enum
  created_at      DateTime             @default(now())
  updated_at      DateTime?            @updatedAt

  selection             selection_baglekdik   @relation(fields: [selection_id], references: [id])
  selection_requirement selection_requirement @relation(fields: [requirement_id], references: [id])

  @@unique([selection_id, requirement_id, comparison_type])
}

model selection_baglekdik_file_requirement {
  id           BigInt   @id @default(autoincrement()) @db.BigInt
  selection_id BigInt   @db.BigInt
  is_required  Boolean  @default(false) @db.Boolean
  title        String   @db.VarChar()
  extensions   String[]
  max_size     Int?     @db.Integer
  min_size     Int?     @db.Integer
  created_at   DateTime @default(now())

  selection selection_baglekdik @relation(fields: [selection_id], references: [id])
}

model selection_requirement {
  id               BigInt                @id @default(autoincrement()) @db.BigInt
  name             String                @db.VarChar()
  input_type       input_type_enum
  selection_module selection_module_enum
  url_data         String?               @db.VarChar()
  table_foreign    String?               @db.VarChar()
  auto_validate    Boolean?              @default(false)
  created_by       BigInt                @db.BigInt
  created_at       DateTime              @default(now())
  updated_by       BigInt?               @db.BigInt
  updated_at       DateTime?             @updatedAt @map("updated_at")

  selection_bagassus_requirement  selection_bagassus_requirement[]
  selection_bagrimdik_requirement selection_bagrimdik_requirement[]
  selection_baglekdik_requirement selection_baglekdik_requirement[]
}

model selection_file {
  id            BigInt    @id @default(autoincrement())
  original_name String    @db.VarChar
  encoding      String    @db.VarChar
  mime_type     String    @db.VarChar
  file_name     String?   @db.VarChar
  size          Int?
  key           String?   @db.VarChar
  url           String?   @db.VarChar
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @updatedAt @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)

  selection_bagassus        selection_bagassus[]
  selection_bagassus_stage  selection_bagassus_stage[]
  selection_bagrimdik       selection_bagrimdik[]
  selection_bagrimdik_stage selection_bagrimdik_stage[]
  selection_baglekdik       selection_baglekdik[]
  selection_baglekdik_stage selection_baglekdik_stage[]
}

model bagassus_deposit {
  id                     BigInt              @id @default(autoincrement())
  no_pendaftaran_seleksi String?
  no_ujian_seleksi       String?
  personel_id            BigInt
  instansi_id            BigInt
  type                   selection_type_enum
  created_at             DateTime
  updated_at             DateTime?
  deleted_at             DateTime?

  personel personel          @relation(fields: [personel_id], references: [id])
  instansi bagassus_instansi @relation(fields: [instansi_id], references: [id])
}

model bagassus_instansi {
  id             BigInt    @id @default(autoincrement())
  nama           String    @db.VarChar
  nama_lowercase String    @unique @db.VarChar()
  created_at     DateTime? @db.Timestamp()
  updated_at     DateTime? @db.Timestamp()
  deleted_at     DateTime? @db.Timestamp()

  bagassus_deposit bagassus_deposit[]
}

model bagassus_file {
  id                   BigInt    @id(map: "bagassus_file_pk") @default(autoincrement())
  bagassus_personel_id BigInt
  originalname         String?   @db.VarChar
  encoding             String?   @db.VarChar
  mimetype             String?   @db.VarChar
  destination          String?   @db.VarChar
  filename             String?   @db.VarChar
  path                 String?   @db.VarChar
  size                 Int?
  key                  String?   @db.VarChar
  url                  String?   @db.VarChar
  type                 String    @db.VarChar(50)
  created_at           DateTime  @default(now()) @db.Timestamp(6)
  updated_at           DateTime  @db.Timestamp(6)
  deleted_at           DateTime? @db.Timestamp(6)

  bagassus_personel       bagassus_personel         @relation(fields: [bagassus_personel_id], references: [id])
  bagassus_personel_draft bagassus_personel_draft[]
}

model bagassus_personel {
  id              BigInt              @id @default(autoincrement())
  personel_id     BigInt
  instansi_id     BigInt
  tanggal_mulai   DateTime            @db.Timestamp(6)
  tanggal_selesai DateTime            @db.Timestamp(6)
  durasi_hari     Int
  jabatan         String              @db.VarChar
  type            selection_type_enum
  created_at      DateTime            @default(now()) @db.Timestamp(6)
  updated_at      DateTime            @default(now()) @db.Timestamp(6)

  personel           personel           @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "bagassus_personel_id_foreign")
  penugasan_instansi penugasan_instansi @relation(fields: [instansi_id], references: [id])

  bagassus_file            bagassus_file[]
  bagassus_personel_report bagassus_personel_report[]
}

model bagassus_personel_draft {
  id               BigInt         @id @default(autoincrement())
  created_by_id    Int
  no_excel         Int
  nama_jabatan     String         @db.VarChar
  personel_id      Int
  nama_personel    String         @db.VarChar
  nrp              String         @db.VarChar
  gender           String         @db.VarChar
  instansi_id      Int
  instansi_name    String         @db.VarChar
  location_type_id Int
  sprin_file_id    BigInt?
  kep_file_id      BigInt?
  created_at       DateTime?      @default(now()) @db.Timestamp(6)
  updated_at       DateTime?      @db.Timestamp(6)
  tanggal_mulai    String?        @db.VarChar
  tanggal_selesai  String?        @db.VarChar
  bagassus_file    bagassus_file? @relation(fields: [bagassus_fileId], references: [id])
  bagassus_fileId  BigInt?
}

model bagassus_personel_report {
  id                   BigInt                      @id @default(autoincrement())
  personel_id          BigInt
  bagassus_personel_id BigInt
  triwulan             Int
  tahun                String                      @db.VarChar(5)
  status               bagassus_report_status_enum
  created_at           DateTime                    @default(now()) @db.Timestamp(6)
  updated_at           DateTime                    @default(now()) @db.Timestamp(6)

  bagassus_personel             bagassus_personel               @relation(fields: [bagassus_personel_id], references: [id], onDelete: Cascade)
  bagassus_personel_report_file bagassus_personel_report_file[]

  @@unique([bagassus_personel_id, triwulan, tahun])
}

model bagassus_personel_report_file {
  id                          BigInt    @id() @default(autoincrement())
  bagassus_personel_report_id BigInt
  originalname                String?   @db.VarChar
  encoding                    String?   @db.VarChar
  mimetype                    String?   @db.VarChar
  destination                 String?   @db.VarChar
  filename                    String?   @db.VarChar
  path                        String?   @db.VarChar
  size                        Int?
  key                         String?   @db.VarChar
  url                         String?   @db.VarChar
  created_at                  DateTime  @default(now()) @db.Timestamp(6)
  updated_at                  DateTime  @db.Timestamp(6)
  deleted_at                  DateTime? @db.Timestamp(6)

  bagassus_personel_report bagassus_personel_report @relation(fields: [bagassus_personel_report_id], references: [id])
}

enum selection_stage_status_enum {
  DIBUKA
  DITUTUP
  DIUNDUR
}

enum selection_module_enum {
  ALL
  BAGRIMDIK
  BAGASSUS
  BAGLEKDIK
  PROMOSI_JABATAN
}

enum selection_type_enum {
  DALAM_NEGERI
  LUAR_NEGERI
}

enum bagassus_report_status_enum {
  APPROVED
  WAITING_APPROVAL
  REJECTED
}
