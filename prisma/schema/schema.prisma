generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["views", "prismaSchemaFolder"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model agama {
  id                  BigInt                @id @default(autoincrement())
  nama                String                @db.VarChar(255)
  created_at          DateTime?             @default(now()) @db.Timestamp(0)
  updated_at          DateTime?             @updatedAt @db.Timestamp(0)
  deleted_at          DateTime?             @db.Timestamp(0)
  keluarga_personel   keluarga_personel[]
  personel            personel[]
  siswa               siswa[]
  data_keluarga_siswa data_keluarga_siswa[]
}

model akreditasi {
  id               BigInt             @id @default(autoincrement())
  nama             String             @db.VarChar(25)
  created_by       BigInt?
  created_at       DateTime           @default(now()) @db.Timestamp(0)
  updated_by       BigInt?
  updated_at       DateTime?          @updatedAt @db.Timestamp(0)
  deleted_by       BigInt?
  deleted_at       DateTime?          @db.Timestamp(0)
  data_dikum_siswa data_dikum_siswa[]
}

model akses_api_key {
  id         BigInt    @id @default(autoincrement())
  api_key_id BigInt
  api_id     BigInt
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  api        api       @relation(fields: [api_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  api_key    api_key   @relation(fields: [api_key_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([api_key_id, api_id], map: "unique_api_key_api")
}

model alamat {
  id              BigInt           @id(map: "alamat_pkey1") @default(autoincrement())
  alamat          String?
  no_rt           String?          @db.VarChar(255)
  no_rw           String?          @db.VarChar(255)
  kelurahan       String?          @db.VarChar(255)
  kecamatan       String?          @db.VarChar(255)
  kabupaten       String?          @db.VarChar(255)
  provinsi        String?          @db.VarChar(255)
  personel_id     BigInt
  created_at      DateTime?        @default(now()) @db.Timestamp(0)
  updated_at      DateTime?        @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?        @db.Timestamp(0)
  kategori_id     BigInt?
  alamat_kategori alamat_kategori? @relation(fields: [kategori_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alamat_alamat_kategori_id_foreign")
  personel        personel         @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "alamat_personel_id_foreign")
  pengajuan_ncr   pengajuan_ncr[]
}

model alamat_kategori {
  id         BigInt    @id @default(autoincrement())
  nama       String    @db.VarChar(255)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  alamat     alamat[]
}

model angket_patma {
  id                    BigInt                 @id @default(autoincrement())
  jenis_diktuk_id       BigInt
  kompetensi_diktuk_id  BigInt?
  sub_kompetensi_id     BigInt?
  pilihan_angket        String                 @db.VarChar(255)
  isi_angket            String
  created_at            DateTime?              @default(now()) @db.Timestamp(0)
  updated_at            DateTime?              @updatedAt @db.Timestamp(0)
  jenis_diktuk          jenis_diktuk           @relation(fields: [jenis_diktuk_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  kompetensi_diktuk     kompetensi_diktuk?     @relation(fields: [kompetensi_diktuk_id], references: [id], onUpdate: NoAction)
  sub_kompetensi_diktuk sub_kompetensi_diktuk? @relation(fields: [sub_kompetensi_id], references: [id], onUpdate: NoAction)
}

model api_management_clients {
  id         BigInt    @id @default(autoincrement())
  name       String
  email      String?   @db.VarChar(150)
  is_active  Boolean   @default(true)
  created_by BigInt
  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_by BigInt?
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_by BigInt?
  deleted_at DateTime? @db.Timestamp(0)
  uid        String    @unique @db.Uuid

  auth      api_management_client_auth?
  endpoints api_management_client_endpoints[]
}

model api_management_client_auth {
  id         BigInt    @id @default(autoincrement())
  client_id  BigInt    @unique
  username   String    @unique @db.VarChar(150)
  password   String?   @db.VarChar(150)
  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt() @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)

  client api_management_clients @relation(fields: [client_id], references: [id])
}

model api_management_client_endpoints {
  id              BigInt    @id @default(autoincrement())
  endpoint_id     Int
  client_id       BigInt
  request_fields  Json      @default("{}")
  response_fields Json      @default("{}")
  is_active       Boolean   @default(true)
  created_at      DateTime  @default(now()) @db.Timestamp(0)
  created_by      BigInt
  updated_at      DateTime? @updatedAt() @db.Timestamp(0)
  updated_by      BigInt?
  deleted_at      DateTime? @db.Timestamp(0)
  deleted_by      BigInt?

  client   api_management_clients   @relation(fields: [client_id], references: [id])
  endpoint api_management_endpoints @relation(fields: [endpoint_id], references: [id])
}

model api_management_endpoints {
  id         Int       @id @default(autoincrement())
  name       String    @db.VarChar(150)
  path       String    @unique @db.VarChar(255)
  method     String    @db.VarChar(10)
  table      String    @db.VarChar(100)
  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt() @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)

  clients api_management_client_endpoints[]
}

model api {
  id               BigInt   @id @default(autoincrement())
  application_name String   @db.VarChar(255)
  method           String   @db.VarChar(255)
  table            String   @db.VarChar(255)
  api_url          String   @db.VarChar(255)
  is_active        Boolean? @default(false)
  created_by       BigInt

  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  akses_api_key      akses_api_key[]
  created_by_users   users                @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  api_fields         api_fields[]
  log_management_api log_management_api[]
}

model api_fields {
  id     BigInt @id @default(autoincrement())
  api_id BigInt
  name   String @db.VarChar(255)
  api    api    @relation(fields: [api_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model api_key {
  id      BigInt @id @default(autoincrement())
  api_key String @unique @db.VarChar(255)

  created_at DateTime? @default(now()) @db.Timestamptz(6)
  updated_at DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  deleted_at DateTime? @db.Timestamptz(6)

  akses_api_key akses_api_key[]
}

model assessment_personel {
  id               BigInt    @id @default(autoincrement())
  personel_id      BigInt
  nama             String    @db.VarChar(255)
  tmt_mulai        DateTime  @db.Date
  tmt_selesai      DateTime? @db.Date
  nilai            Float?
  nilai_file       String?   @db.VarChar(255)
  keterangan       String?   @db.VarChar(255)
  nilai_kualitatif String?   @db.VarChar(255)
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @updatedAt @db.Timestamp(0)
  deleted_at       DateTime? @db.Timestamp(0)
  personel         personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "assessment_personel_personel_id_foreign")
}

model bagassus_seleksi {
  id                             BigInt                   @id @default(autoincrement())
  title                          String                   @db.VarChar
  description                    String                   @db.VarChar
  location_type_id               Int
  penugasan_id                   Int
  tanggal_mulai                  DateTime                 @db.Timestamp(6)
  tanggal_selesai                DateTime                 @db.Timestamp(6)
  submission_start               DateTime                 @db.Timestamp(6)
  submission_end                 DateTime                 @db.Timestamp(6)
  tahapan_seleksi                Int
  created_at                     DateTime                 @default(now()) @db.Timestamp(6)
  updated_at                     DateTime?                @db.Timestamp(6)
  logo_file_id                   BigInt
  document_file_id               BigInt
  has_been_ended                 Boolean?
  bagassus_seleksi_file_logo     bagassus_seleksi_file    @relation("logo_to_bagassus_seleksi_file", fields: [logo_file_id], references: [id])
  bagassus_seleksi_file_document bagassus_seleksi_file    @relation("document_to_bagassus_seleksi_file", fields: [document_file_id], references: [id])
  tanggal_tahapan_seleksi        bagassus_tahap_seleksi[]

  bagassus_seleksi_personel bagassus_seleksi_personel[]
}

model bagassus_seleksi_file {
  id                             BigInt             @id @default(autoincrement())
  originalname                   String?            @db.VarChar
  encoding                       String?            @db.VarChar
  mimetype                       String?            @db.VarChar
  destination                    String?            @db.VarChar
  filename                       String?            @db.VarChar
  path                           String?            @db.VarChar
  key                            String?            @db.VarChar
  url                            String?            @db.VarChar
  created_at                     DateTime           @default(now()) @db.Timestamp(6)
  updated_at                     DateTime?          @db.Timestamp(6)
  size                           Int?
  bagassus_seleksi_file_logo     bagassus_seleksi[] @relation("logo_to_bagassus_seleksi_file")
  bagassus_seleksi_file_document bagassus_seleksi[] @relation("document_to_bagassus_seleksi_file")
}

model bagassus_tahap_seleksi {
  id              BigInt           @id @default(autoincrement())
  seleksi_id      BigInt
  tahap           Int
  tanggal_mulai   DateTime         @db.Timestamp(6)
  tanggal_selesai DateTime         @db.Timestamp(6)
  created_at      DateTime         @default(now()) @db.Timestamp(6)
  updated_at      DateTime?        @db.Timestamp(6)
  seleksi         bagassus_seleksi @relation(fields: [seleksi_id], references: [id], map: "bagassus_tahap_seleksi_seleksi_id_foreign")
}

model bagpsikologi {
  id                 Int       @id @default(autoincrement())
  personel_id        BigInt?
  pangkat_id         BigInt?
  jabatan_id         BigInt?
  satuan_id          BigInt?
  tgl_konsultasi     DateTime? @db.Date
  jenis_permasalahan String?   @db.VarChar(255)
  metode_konsultasi  String?   @db.VarChar(255)
  jenis_konsultasi   String?   @db.VarChar(255)
  hasil_konsultasi   String?
  tindak_lanjut      String?
  keterangan         String?
  konselor           String?   @db.VarChar(255)
  lampiran           String?
  status             String?   @db.VarChar
  pangkat            pangkat?  @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_pangkat_id")
  personel           personel? @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_personel_id")
  satuan             satuan?   @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_satuan_id")
}

model bagrimdik_dokumen_perintah_latsar_file {
  id                              BigInt                            @id @default(autoincrement())
  originalname                    String?
  encoding                        String?
  mimetype                        String?
  destination                     String?
  filename                        String?
  path                            String?
  size                            Int?
  key                             String?
  url                             String?
  created_at                      DateTime                          @default(now())
  updated_at                      DateTime?                         @db.Timestamp(0)
  bagrimdik_draft_siap_latsar_asn bagrimdik_draft_siap_latsar_asn[]
  bagrimdik_peserta_rekrutmen_asn bagrimdik_peserta_rekrutmen_asn[]
}

model bagrimdik_dokumen_skep_file {
  id                               BigInt                             @id @default(autoincrement())
  originalname                     String?
  encoding                         String?
  mimetype                         String?
  destination                      String?
  filename                         String?
  path                             String?
  size                             Int?
  key                              String?
  url                              String?
  created_at                       DateTime                           @default(now())
  updated_at                       DateTime?                          @db.Timestamp(0)
  bagrimdik_peserta_rekrutmen_pppk bagrimdik_peserta_rekrutmen_pppk[]
}

model bagrimdik_draft_siap_latsar_asn {
  id                                        BigInt                                  @id @default(autoincrement())
  bagrimdik_peserta_rekrutmen_asn_id        BigInt                                  @unique
  bagrimdik_dokumen_perintah_latsar_file_id BigInt?
  bagrimdik_dokumen_perintah_latsar_file    bagrimdik_dokumen_perintah_latsar_file? @relation(fields: [bagrimdik_dokumen_perintah_latsar_file_id], references: [id], onDelete: NoAction, map: "bagrimdik_dokumen_perintah_latsar_file_on_foreign_draft")
  bagrimdik_peserta_rekrutmen_asn           bagrimdik_peserta_rekrutmen_asn         @relation(fields: [bagrimdik_peserta_rekrutmen_asn_id], references: [id], onDelete: Cascade, map: "bagrimdik_draft_siap_latsar_asn_foreign")
}

model bagrimdik_nilai_sk_detil_asn {
  id                              BigInt                          @id @default(autoincrement())
  no_peserta                      String
  nama_peserta                    String
  jabatan_kode                    String?                         @db.VarChar(255)
  jabatan_nama                    String?                         @db.VarChar(255)
  urutan                          Int                             @db.SmallInt
  nama_komponen                   String?                         @db.VarChar(255)
  nilai                           Float                           @default(0.0)
  nilai_skala_100                 Float                           @default(0.0)
  bobot                           Float                           @default(0.0)
  skor                            Float                           @default(0.0)
  status_peserta                  String?                         @db.VarChar(255)
  is_delete                       Boolean                         @default(false)
  created_at                      DateTime?                       @default(now())
  updated_at                      DateTime?                       @updatedAt
  deleted_at                      DateTime?                       @db.Timestamp(0)
  jenis_nilai                     jenis_nilai_komponen_enum?
  bagrimdik_peserta_rekrutmen_asn bagrimdik_peserta_rekrutmen_asn @relation(fields: [no_peserta], references: [no_peserta], onDelete: Cascade, map: "bagrimdik_peserta_rekrutmen_asn_foreign")
}

model bagrimdik_peserta_rekrutmen_asn {
  id                                        BigInt                                  @id @default(autoincrement())
  no_peserta                                String                                  @unique
  nik                                       String?                                 @db.VarChar(255)
  nama                                      String
  tempat_lahir                              String?                                 @db.VarChar
  tanggal_lahir                             DateTime?                               @db.Date
  instansi_kode                             String?                                 @db.VarChar(255)
  instansi_nama                             String?                                 @db.VarChar(255)
  lokasi_formasi_kode                       String?                                 @db.VarChar(255)
  lokasi_formasi_nama                       String?                                 @db.VarChar(255)
  jenis_formasi_kode                        String?                                 @db.VarChar(255)
  jenis_formasi_nama                        String?                                 @db.VarChar(255)
  jabatan_kode                              String?                                 @db.VarChar(255)
  jabatan_nama                              String?                                 @db.VarChar(255)
  nilai_sk_teknis_murni                     Float                                   @default(0.0)
  nilai_sk_eptp                             Float                                   @default(0.0)
  nilai_sk_bing                             Float                                   @default(0.0)
  nilai_sk_ppm                              Float                                   @default(0.0)
  nilai_sk_dpsi                             Float                                   @default(0.0)
  nilai_sk_afirmasi_teknis                  Float                                   @default(0.0)
  nilai_sk_total_teknis                     Float                                   @default(0.0)
  nilai_sk_manajerial                       Float                                   @default(0.0)
  nilai_sk_sosiokultural                    Float                                   @default(0.0)
  nilai_sk_wawancara                        Float                                   @default(0.0)
  nilai_sk_total                            Float                                   @default(0.0)
  nilai_skd_twk                             Float                                   @default(0.0)
  nilai_skd_tiu                             Float                                   @default(0.0)
  nilai_skd_tkp                             Float                                   @default(0.0)
  nilai_skd_total                           Float                                   @default(0.0)
  nilai_skd_total_skala_100                 Float                                   @default(0.0)
  skor_skd                                  Float                                   @default(0.0)
  skor_skb_60_persen                        Float                                   @default(0.0)
  skor_skb                                  Float                                   @default(0.0)
  nilai_akhir                               Float                                   @default(0.0)
  keterangan                                String?                                 @db.VarChar(255)
  afirmasi                                  String?                                 @db.VarChar(255)
  lokasi_ujian                              String?                                 @db.VarChar(255)
  status_peserta                            String?                                 @db.VarChar(255)
  import_file_date                          DateTime?                               @db.Timestamp(0)
  is_delete                                 Boolean                                 @default(false)
  created_at                                DateTime?                               @default(now())
  updated_at                                DateTime?                               @updatedAt
  deleted_at                                DateTime?                               @db.Timestamp(0)
  bagrimdik_dokumen_perintah_latsar_file_id BigInt?
  status_rekrutmen                          status_rekrutmen_asn_enum               @default(AKTIF)
  jenis_nilai                               jenis_nilai_komponen_enum?
  bagrimdik_draft_siap_latsar_asn           bagrimdik_draft_siap_latsar_asn?
  bagrimdik_nilai_sk_detil_asn              bagrimdik_nilai_sk_detil_asn[]
  bagrimdik_dokumen_perintah_latsar_file    bagrimdik_dokumen_perintah_latsar_file? @relation(fields: [bagrimdik_dokumen_perintah_latsar_file_id], references: [id], onDelete: NoAction, map: "bagrimdik_dokumen_perintah_latsar_file_on_foreign")
}

model bagrimdik_peserta_rekrutmen_pppk {
  id                                  BigInt                       @id @default(autoincrement())
  no_peserta                          String                       @unique
  nama                                String
  nip                                 String?                      @db.VarChar(255)
  jenis_formasi_nama                  String?                      @db.VarChar(255)
  periode                             Int                          @db.SmallInt
  tanggal_usulan                      DateTime?                    @db.Date
  tanggal_pertek                      DateTime?                    @db.Date
  no_pertek                           String?                      @db.VarChar
  status_usulan                       String?                      @db.VarChar
  tahapan                             String?                      @db.VarChar
  tempat_lahir                        String?                      @db.VarChar
  tanggal_lahir                       DateTime?                    @db.Date
  pendidikan_pertama_nama             String?                      @db.VarChar(255)
  kode_dik                            String?                      @db.VarChar(255)
  tanggal_tahun_lulus                 DateTime?                    @db.Date
  nomor_ijazah                        String?                      @db.VarChar(255)
  jabatan_fungsional_umum_nama        String?                      @db.VarChar(255)
  jabatan_fungsional_nama             String?                      @db.VarChar(255)
  sub_jabatan_fungsional_nama         String?                      @db.VarChar(255)
  gaji_pokok                          Int?
  kpkn_nama                           String?                      @db.VarChar(255)
  golongan_nama                       String?                      @db.VarChar(255)
  agama_id                            String?                      @db.VarChar(255)
  agama_nama                          String?                      @db.VarChar(255)
  jenis_kelamin                       String?                      @db.VarChar(255)
  jenis_kelamin_id                    String?                      @db.VarChar(255)
  jenis_kawin_nama                    String?                      @db.VarChar(255)
  unor_nama                           String?                      @db.VarChar(255)
  unor_induk_nama                     String?                      @db.VarChar(255)
  satuan_kerja_nama                   String?                      @db.VarChar(255)
  ket_sehat_dokter                    String?                      @db.VarChar(255)
  ket_sehat_tanggal                   DateTime?                    @db.Date
  ket_sehat_nomor                     String?                      @db.VarChar(255)
  ket_bebas_narkoba_nomor             String?                      @db.VarChar(255)
  ket_bebas_narkoba_tanggal           DateTime?                    @db.Date
  ket_kelakuanbaik_nomor              String?                      @db.VarChar(255)
  ket_kelakuanbaik_pejabat            String?                      @db.VarChar(255)
  ket_kelakuanbaik_tanggal            DateTime?                    @db.Date
  tgl_kontrak_mulai                   DateTime?                    @db.Date
  tgl_kontrak_akhir                   DateTime?                    @db.Date
  kode_polda                          String?                      @db.VarChar(255)
  polda                               String?                      @db.VarChar(255)
  kode_cek                            String?                      @db.VarChar(255)
  ket                                 String?                      @db.VarChar(255)
  status_rekrutmen                    status_rekrutmen_pppk_enum   @default(AKTIF)
  import_file_date                    DateTime?                    @db.Timestamp(0)
  personel_id                         BigInt?
  is_delete                           Boolean                      @default(false)
  created_at                          DateTime?                    @default(now())
  updated_at                          DateTime?                    @updatedAt
  deleted_at                          DateTime?                    @db.Timestamp(0)
  bagrimdik_dokumen_skep_file_id      BigInt?
  jenis_kawin_id                      String?                      @db.VarChar(255)
  rencana_perjanjian_kontrak_atau_tmt DateTime?                    @db.Date
  bagrimdik_dokumen_skep_file         bagrimdik_dokumen_skep_file? @relation(fields: [bagrimdik_dokumen_skep_file_id], references: [id], onDelete: NoAction, map: "bagrimdik_dokumen_skep_file_on_foreign")
  personel                            personel?                    @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "bagrimdik_peserta_rekrutmen_pppk_on_personel")
}

model bahasa {
  id              BigInt            @id @default(autoincrement())
  nama            String            @db.VarChar(255)
  is_aktif        Boolean?          @default(true)
  created_at      DateTime?         @default(now()) @db.Timestamp(0)
  updated_at      DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?         @db.Timestamp(0)
  jenis           bahasa_jenis_enum @default(LOKAL)
  bahasa_personel bahasa_personel[]
}

model bahasa_personel {
  id                BigInt    @id @default(autoincrement())
  bahasa_id         BigInt
  personel_id       BigInt
  tanggal_perubahan DateTime? @updatedAt @db.Timestamp(0)
  created_at        DateTime? @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)
  deleted_at        DateTime? @db.Timestamp(0)
  is_aktif          Boolean

  bahasa   bahasa   @relation(fields: [bahasa_id], references: [id], onDelete: Cascade, map: "bahasa_personel_bahasa_id_foreign")
  personel personel @relation(fields: [personel_id], references: [id], map: "bahasa_personel_personel_id_foreign")
}

model beasiswa_dikum_dinas {
  id                           BigInt                           @id @default(autoincrement())
  personel_id                  BigInt
  jenis_beasiswa               String?                          @db.VarChar(100)
  jenjang_pendidikan           String                           @db.VarChar(50)
  nama_universitas             String                           @db.VarChar(100)
  jurusan                      String                           @db.VarChar(100)
  waktu_mulai                  DateTime?                        @db.Date
  waktu_lulus                  DateTime?                        @db.Date
  status                       status_beasiswa_dikum_dinas_enum
  created_at                   DateTime?                        @default(now()) @db.Timestamp(6)
  updated_at                   DateTime?                        @updatedAt @db.Timestamp(6)
  deleted_at                   DateTime?                        @db.Timestamp(6)
  personel                     personel                         @relation(fields: [personel_id], references: [id], map: "beasiswa_dikum_dinas_personel_id_foreign")
  dokumen_beasiswa_dikum_dinas dokumen_beasiswa_dikum_dinas[]
}

model berita {
  id          BigInt             @id @default(autoincrement())
  title       String
  description String?
  created_by  BigInt?
  created_at  DateTime           @default(now()) @db.Timestamp(0)
  updated_at  DateTime?          @updatedAt @db.Timestamp(0)
  deleted_at  DateTime?          @db.Timestamp(0)
  is_publish  Boolean?
  publish_at  DateTime?          @db.Timestamp(6)
  satuan_id   BigInt?
  kategori_id BigInt?
  image       String?            @db.VarChar(255)
  jenis       jenis_berita_enum?

  berita_kategori  berita_kategori? @relation(fields: [kategori_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "berita_berita_kategori_id_fk")
  created_by_users users?           @relation(fields: [created_by], references: [id], onDelete: Cascade, map: "berita_created_by_id_foreign")
  satuan           satuan?          @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "berita_satuan_by_id_fkey")
  berita_file      berita_file[]
}

model berita_file {
  id            Int       @id @default(autoincrement())
  berita_id     BigInt
  originalname  String?
  encoding      String?
  mimetype      String?
  destination   String?
  filename      String?
  path          String?
  size          BigInt?
  created_at    DateTime  @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @updatedAt @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)
  key           String?   @db.VarChar
  url           String?   @db.VarChar
  jenis_dokumen String?   @db.VarChar(255)
  berita        berita    @relation(fields: [berita_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "berita_file_berita_id_fk")

  @@unique([id, berita_id], map: "berita_file_pk")
  @@index([berita_id], map: "berita_file_id_berita_key")
}

model berita_kategori {
  id         BigInt    @id(map: "berita_kategori_pk") @default(autoincrement())
  nama       String?   @db.VarChar(255)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  berita     berita[]
}

model binjas_formula {
  id            Int       @id @default(autoincrement())
  golongan      String    @db.VarChar(255)
  jenis_kelamin String    @db.VarChar(255)
  gerakan       String    @db.VarChar(255)
  batas_atas    Decimal   @db.Decimal
  batas_bawah   Decimal   @db.Decimal
  nilai         Float
  keterangan    String?   @db.VarChar(255)
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)
}

model bko_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  satuan_id   BigInt
  tmt_bko     DateTime? @db.Date
  sprin_nomor String    @db.VarChar(255)
  sprin_file  String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  satuan      satuan    @relation(fields: [satuan_id], references: [id], onDelete: Cascade, map: "bko_personel_satuan_id_foreign")
  personel    personel  @relation(fields: [personel_id], references: [id], map: "bko_personel_personel_id_foreign")
}

model brevet {
  id              BigInt            @id @default(autoincrement())
  nama            String?           @db.VarChar(255)
  asal            String?           @db.VarChar(255)
  created_at      DateTime?         @default(now()) @db.Timestamp(0)
  updated_at      DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?         @db.Timestamp(0)
  brevet_personel brevet_personel[]
}

model brevet_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  brevet_id   BigInt
  tmt_brevet  DateTime? @db.Date
  kep_nomor   String?   @db.VarChar(255)
  kep_file    String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  brevet      brevet    @relation(fields: [brevet_id], references: [id], onDelete: Cascade, map: "brevet_personel_brevet_id_foreign")
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "brevet_personel_personel_id_foreign")
}

model cabor {
  id                         BigInt                       @id @default(autoincrement())
  nama                       String                       @db.VarChar(255)
  created_at                 DateTime?                    @default(now()) @db.Timestamp(0)
  updated_at                 DateTime?                    @updatedAt @db.Timestamp(0)
  deleted_at                 DateTime?                    @db.Timestamp(0)
  prestasi_olahraga_personel prestasi_olahraga_personel[]
}

model candidate_pangkat_filter {
  id               BigInt            @id @default(autoincrement())
  nivellering_id   BigInt?
  pangkat_id       BigInt
  kategori_id      BigInt?
  golongan_id      BigInt?
  created_at       DateTime?         @default(now()) @db.Timestamp(0)
  updated_at       DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at       DateTime?         @db.Timestamp(0)
  golongan         golongan?         @relation(fields: [golongan_id], references: [id], onDelete: Cascade, map: "candidate_pangkat_filter_golongan_id_foreign")
  pangkat_kategori pangkat_kategori? @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "candidate_pangkat_filter_kategori_id_foreign")
  nivellering      nivellering?      @relation(fields: [nivellering_id], references: [id], onDelete: Cascade, map: "candidate_pangkat_filter_nivellering_id_fk")
  pangkat          pangkat?          @relation(fields: [pangkat_id], references: [id], onDelete: Cascade, map: "candidate_pangkat_filter_pangkat_id_foreign")
}

model recruitment_type {
  id         Int       @id @default(autoincrement())
  nama       String
  deskripsi  String?
  created_by BigInt
  created_at DateTime  @default(now())
  updated_by BigInt?
  updated_at DateTime?
  deleted_by BigInt?
  deleted_at DateTime?

  rekrutmen data_rekrutmen_siswa[]
}

model dikbangspes {
  id                        BigInt                      @id @default(autoincrement())
  nama                      String                      @db.VarChar(255)
  lokasi_id                 BigInt
  tingkat_id                BigInt?
  is_aktif                  Boolean                     @default(true)
  created_at                DateTime?                   @default(now()) @db.Timestamp(0)
  updated_at                DateTime?                   @updatedAt @db.Timestamp(0)
  deleted_at                DateTime?                   @db.Timestamp(0)
  dikbangspes_lokasi        dikbangspes_lokasi          @relation(fields: [lokasi_id], references: [id], onDelete: Cascade, map: "dikbangspes_lokasi_id_foreign")
  dikbangspes_tingkat       dikbangspes_tingkat?        @relation(fields: [tingkat_id], references: [id], onDelete: Cascade, map: "dikbangspes_tingkat_id_foreign")
  dikbangspes_personel      dikbangspes_personel[]
  lama_dikbangspes_personel lama_dikbangspes_personel[]
}

model dikbangspes_lokasi {
  id          BigInt        @id @default(autoincrement())
  nama        String        @db.VarChar(255)
  created_at  DateTime?     @default(now()) @db.Timestamp(0)
  updated_at  DateTime?     @updatedAt @db.Timestamp(0)
  deleted_at  DateTime?     @db.Timestamp(0)
  dikbangspes dikbangspes[]
  pelatihan   pelatihan[]
}

model dikbangspes_personel {
  id                   BigInt      @id @default(autoincrement())
  dikbangspes_id       BigInt
  personel_id          BigInt
  tanggal_masuk        DateTime?   @db.Date
  tanggal_selesai      DateTime?   @db.Date
  nilai                Float?
  transkrip_nilai_file String?     @db.VarChar(255)
  ijazah_no            String?     @db.VarChar(255)
  ijazah_file          String?     @db.VarChar(255)
  ranking              Int?
  jumlah_siswa         Int?
  created_at           DateTime?   @default(now()) @db.Timestamp(0)
  updated_at           DateTime?   @updatedAt @db.Timestamp(0)
  deleted_at           DateTime?   @db.Timestamp(0)
  dikbangspes          dikbangspes @relation(fields: [dikbangspes_id], references: [id], onDelete: Cascade, map: "dikbangspes_personel_dikbangspes_id_foreign")
  personel             personel    @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "dikbangspes_personel_personel_id_foreign")
}

model dikbangspes_tingkat {
  id                         BigInt                       @id @default(autoincrement())
  nama                       String                       @db.VarChar(255)
  created_at                 DateTime?                    @default(now()) @db.Timestamp(0)
  updated_at                 DateTime?                    @updatedAt @db.Timestamp(0)
  deleted_at                 DateTime?                    @db.Timestamp(0)
  dikbangspes                dikbangspes[]
  lama_dikbangspes_personel  lama_dikbangspes_personel[]
  pelatihan                  pelatihan[]
  score_dikbangspes_personel score_dikbangspes_personel[]
}

model dikbangum {
  id                      BigInt                    @id @default(autoincrement())
  nama                    String                    @db.VarChar(255)
  nama_alternatif         String?                   @db.VarChar(255)
  is_aktif                Boolean?                  @default(true)
  dikbangum_kategori_id   BigInt?
  created_at              DateTime?                 @default(now()) @db.Timestamp(0)
  updated_at              DateTime?                 @updatedAt @db.Timestamp(0)
  deleted_at              DateTime?                 @db.Timestamp(0)
  dikbangum_kategori      dikbangum_kategori?       @relation(fields: [dikbangum_kategori_id], references: [id], onDelete: Cascade, map: "dikbangum_dikbangum_kategori_id_foreign")
  dikbangum_personel      dikbangum_personel[]
  score_rank_tahun_dikpol score_rank_tahun_dikpol[]
}

model dikbangum_kategori {
  id         BigInt      @id @default(autoincrement())
  nama       String      @db.VarChar(255)
  created_at DateTime?   @default(now()) @db.Timestamp(0)
  updated_at DateTime?   @updatedAt @db.Timestamp(0)
  deleted_at DateTime?   @db.Timestamp(0)
  dikbangum  dikbangum[]
}

model dikbangum_personel {
  id                   BigInt    @id @default(autoincrement())
  dikbangum_id         BigInt
  personel_id          BigInt
  tanggal_masuk        DateTime? @db.Date
  tanggal_selesai      DateTime? @db.Date
  nilai                Float?
  transkrip_nilai_file String?   @db.VarChar(255)
  ijazah_no            String?   @db.VarChar(255)
  ijazah_file          String?   @db.VarChar(255)
  ranking              Int?
  jumlah_siswa         Int?
  created_at           DateTime? @default(now()) @db.Timestamp(0)
  updated_at           DateTime? @updatedAt @db.Timestamp(0)
  deleted_at           DateTime? @db.Timestamp(0)
  gelar_id             BigInt?
  is_tampil_gelar      Boolean?
  dikbangum            dikbangum @relation(fields: [dikbangum_id], references: [id], onDelete: Cascade, map: "dikbangum_personel_dikbangum_id_foreign")
  personel             personel  @relation(fields: [personel_id], references: [id], map: "dikbangum_personel_personel_id_foreign")
  gelar                gelar?    @relation(fields: [gelar_id], references: [id], map: "dikbangum_personel_gelar_id_foreign")
}

model diklat_personel {
  id                  Int       @id @default(autoincrement())
  personel_id         BigInt?
  jenjang             String?   @db.VarChar
  jurusan             String?   @db.VarChar
  perguruan_tinggi    String?   @db.VarChar
  pangkat_id          BigInt?
  jabatan_sblm_dik_id BigInt?
  jabatan_sblm_dik    String?   @db.VarChar
  thn_masuk           String?   @db.VarChar
  thn_lulus           String?   @db.VarChar
  status              String?   @db.VarChar
  keterangan          String?   @db.VarChar
  created_at          DateTime? @default(now()) @db.Timestamp(6)
  updated_at          DateTime? @updatedAt @db.Timestamp(6)
  pangkat             pangkat?  @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel            personel? @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model diktuk {
  id                      BigInt                    @id @default(autoincrement())
  nama                    String                    @db.VarChar(255)
  is_aktif                Boolean?                  @default(true)
  kategori_id             BigInt?
  created_at              DateTime?                 @default(now()) @db.Timestamp(0)
  updated_at              DateTime?                 @updatedAt @db.Timestamp(0)
  deleted_at              DateTime?                 @db.Timestamp(0)
  diktuk_kategori         diktuk_kategori?          @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "diktuk_kategori_id_foreign")
  diktuk_personel         diktuk_personel[]
  score_rank_tahun_dikpol score_rank_tahun_dikpol[]
}

model diktuk_kategori {
  id         BigInt    @id @default(autoincrement())
  nama       String    @db.VarChar(255)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  diktuk     diktuk[]
}

model diktuk_personel {
  id                   BigInt    @id @default(autoincrement())
  diktuk_id            BigInt
  personel_id          BigInt
  tanggal_masuk        DateTime? @db.Date
  tanggal_selesai      DateTime? @db.Date
  nilai                Float?
  transkrip_nilai_file String?   @db.VarChar(255)
  ijazah_no            String?   @db.VarChar(255)
  ijazah_file          String?   @db.VarChar(255)
  ranking              Int?
  jumlah_siswa         Int?
  gelar_id             BigInt?
  is_tampil_gelar      Boolean?  @default(true)
  created_at           DateTime? @default(now()) @db.Timestamp(0)
  updated_at           DateTime? @updatedAt @db.Timestamp(0)
  deleted_at           DateTime? @db.Timestamp(0)
  diktuk               diktuk    @relation(fields: [diktuk_id], references: [id], onDelete: Cascade, map: "diktuk_personel_diktuk_id_foreign")
  gelar                gelar?    @relation(fields: [gelar_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "diktuk_personel_gelar_id_foreign")
  personel             personel  @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "diktuk_personel_personel_id_foreign")
}

model dikum {
  id                 BigInt             @id @default(autoincrement())
  nama               String             @db.VarChar(255)
  is_aktif           Boolean?           @default(true)
  created_at         DateTime?          @default(now()) @db.Timestamp(0)
  updated_at         DateTime?          @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?          @db.Timestamp(0)
  tingkat_pendidikan String?            @db.VarChar(255)
  dikum_detail       dikum_detail[]
  data_dikum_siswa   data_dikum_siswa[]
  data_siswa         data_siswa[]
}

model dikum_personel {
  id              BigInt        @id @default(autoincrement())
  personel_id     BigInt
  dikum_detail_id BigInt?
  created_at      DateTime?     @default(now()) @db.Timestamp(0)
  updated_at      DateTime?     @db.Timestamp(0)
  deleted_at      DateTime?     @db.Timestamp(0)
  tanggal_mulai   DateTime?     @db.Date
  tanggal_lulus   DateTime?     @db.Date
  personel        personel      @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "jabatan_personel_personel_id_foreign")
  dikum_detail    dikum_detail? @relation(fields: [dikum_detail_id], references: [id])
}

model dokumen_beasiswa_dikum_dinas {
  id                      BigInt               @id @default(autoincrement())
  beasiswa_dikum_dinas_id BigInt
  originalname            String?              @db.VarChar(255)
  encoding                String?              @db.VarChar(50)
  mimetype                String?              @db.VarChar(100)
  destination             String?              @db.VarChar(255)
  filename                String?              @db.VarChar(255)
  path                    String?              @db.VarChar(255)
  size                    BigInt?
  key                     String?              @db.VarChar(255)
  url                     String?              @db.VarChar(255)
  created_at              DateTime?            @default(now()) @db.Timestamp(6)
  updated_at              DateTime?            @updatedAt @db.Timestamp(6)
  deleted_at              DateTime?            @db.Timestamp(6)
  jenis_dokumen           String?              @db.VarChar(255)
  beasiswa_dikum_dinas    beasiswa_dikum_dinas @relation(fields: [beasiswa_dikum_dinas_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model dokumen_ncr {
  id               BigInt                   @id @default(autoincrement())
  pengajuan_ncr_id BigInt?
  jenis_dokumen    String?
  originalname     String?
  encoding         String?
  mimetype         String?
  destination      String?
  filename         String?
  path             String?
  size             BigInt?
  key              String?
  url              String?
  created_at       DateTime                 @default(now()) @db.Timestamp(0)
  updated_at       DateTime?                @updatedAt @db.Timestamp(0)
  deleted_at       DateTime?                @db.Timestamp(0)
  status           status_dokumen_ncr_enum? @default(PROCESS)
  alasan_penolakan String?
  pengajuan_ncr    pengajuan_ncr?           @relation(fields: [pengajuan_ncr_id], references: [id])
}

model dsp_pns {
  id               BigInt             @id @default(autoincrement())
  golongan_dsp     String?            @db.VarChar(10)
  dsp_mabes        Int?
  dsp_polda        Int?
  dsp_polda_mabes  Int?
  golongan_dsp_pns golongan_dsp_pns[]
}

model dsp_polri {
  id                BigInt              @id @default(autoincrement())
  pangkat_dsp       String?             @db.VarChar(50)
  dsp_mabes         Int?
  dsp_polda         Int?
  dsp_polda_mabes   Int?
  pangkat_dsp_polri pangkat_dsp_polri[]
}

model e_kta {
  id                 BigInt    @id @default(autoincrement())
  nama               String?   @db.VarChar(25)
  personel_id        BigInt
  tanggal            DateTime  @db.Date
  status             Boolean   @default(false)
  jenis_permintaan   Int       @default(1)
  surat_permintaan   Json?
  approval_history   Json?
  e_kta_batch_id     BigInt?
  e_kta_pengajuan_id BigInt?
  created_at         DateTime  @default(now())
  created_by         BigInt?
  updated_at         DateTime? @db.Timestamp(0)
  updated_by         BigInt?
  deleted_at         DateTime? @db.Timestamp(0)
  deleted_by         BigInt?
  bank               String?   @db.VarChar(50)

  personel           personel             @relation(fields: [personel_id], references: [id])
  e_kta_pengajuan    e_kta_pengajuan?     @relation(fields: [e_kta_pengajuan_id], references: [id], onUpdate: NoAction, map: "fk_e_kta_pengajuan")
  e_kta_batch        e_kta_batch?         @relation(fields: [e_kta_batch_id], references: [id])
  e_kta_surat_detail e_kta_surat_detail[]

  @@index([approval_history], map: "idx_approval_jsonb", type: Gin)
}

model e_kta_pengajuan {
  id                BigInt    @id @default(autoincrement())
  tanggal_pengajuan DateTime  @db.Date
  status            Boolean?  @default(false)
  satuan_id         BigInt
  created_at        DateTime  @default(now()) @db.Timestamp(6)
  updated_at        DateTime? @default(now()) @db.Timestamp(6)
  e_kta             e_kta[]
}

model e_kta_surat {
  id                 BigInt               @id @default(autoincrement())
  nomor_surat        String               @db.VarChar(255)
  dokumen_surat      Json?
  satuan_id          BigInt
  nama_satuan        String               @db.VarChar(255)
  level_asal         Int
  level_tujuan       Int
  status             Boolean?             @default(false)
  signature          Json?
  created_at         DateTime             @default(now())
  created_by         BigInt
  updated_at         DateTime?            @db.Timestamp(0)
  updated_by         BigInt?
  deleted_at         DateTime?            @db.Timestamp(0)
  deleted_by         BigInt?
  e_kta_surat_detail e_kta_surat_detail[]
  satuan             satuan               @relation(fields: [satuan_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_e_kta_surat_satuan")
}

model e_kta_surat_detail {
  id               BigInt   @id @default(autoincrement())
  e_kta_surat_id   BigInt   @map("e_kta_surat_id")
  e_kta_id         BigInt   @map("e_kta_id")
  jabatan          String?
  pangkat          String?
  bank             String?
  jenis_permintaan String?
  nama_lengkap     String?
  nrp              String?
  foto_file        String?
  created_at       DateTime @default(now())

  // Relasi ke e_kta_surat
  e_kta_surat e_kta_surat @relation(fields: [e_kta_surat_id], references: [id], onDelete: Cascade)

  // Relasi ke e_kta
  e_kta e_kta @relation(fields: [e_kta_id], references: [id], onDelete: Cascade)
}

model bank {
  id                  BigInt        @id @default(autoincrement())
  nama                String
  created_at          DateTime      @default(now())
  updated_at          DateTime?     @default(now())
  deleted_at          DateTime?     @db.Timestamp(0)
  satuan_bank         satuan_bank[] @relation("MainBank")
  satuan_bank_request satuan_bank[] @relation("RequestedBank")
}

model satuan_bank {
  id                BigInt    @id @default(autoincrement())
  satuan_id         BigInt
  bank_id           BigInt?
  requested_bank_id BigInt?
  request_status    String?
  requested_at      DateTime?
  created_at        DateTime  @default(now())
  updated_at        DateTime? @default(now())
  deleted_at        DateTime? @db.Timestamp(0)

  bank         bank?  @relation("MainBank", fields: [bank_id], references: [id])
  bank_request bank?  @relation("RequestedBank", fields: [requested_bank_id], references: [id])
  satuan       satuan @relation(fields: [satuan_id], references: [id])
}

model ekta {
  id               BigInt           @id @default(autoincrement())
  personel_id      BigInt
  tanggal          DateTime
  created_by       BigInt
  created_at       DateTime         @default(now()) @db.Timestamp()
  updated_at       DateTime?        @updatedAt @db.Timestamp(0)
  alasan_ditolak   String?
  status           Int              @default(1)
  ekta_batch_id    BigInt?
  ekta_bank        String?
  foto             String?          @db.VarChar(255)
  jenis_permintaan BigInt           @default(1)
  surat_permintaan String?          @db.VarChar(255)
  nama             String           @db.VarChar(255)
  jabatan          String           @db.VarChar(255)
  nrp              String           @db.VarChar(255)
  pangkat          String           @db.VarChar(255)
  satuan           String?          @db.VarChar(255)
  personel         personel         @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_personel_id_fk")
  ekta_batch       ekta_batch?      @relation(fields: [ekta_batch_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_ekta_batch_id_fk")
  created_by_users users            @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_users_id_fk")
  ekta_rejection   ekta_rejection[]
}

model ekta_batch {
  id         BigInt    @id @default(autoincrement())
  kode       String    @db.VarChar(255)
  satuan_id  BigInt?
  satuan     String?   @db.VarChar(255)
  status     Int?      @default(1)
  ekta_bank  String?   @db.VarChar(255)
  created_by BigInt
  created_at DateTime? @default(now()) @db.Timestamp(6)

  ekta_batch_approval_log   ekta_batch_approval_log[]
  ekta_batch_distribusi_log ekta_batch_distribusi_log[]
  ekta                      ekta[]

  created_by_users  users   @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_users_id_fk")
  ekta_batch_satuan satuan? @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_satuan_id_fk")
}

model e_kta_batch {
  id               BigInt    @id @default(autoincrement())
  kode             String    @db.VarChar(255)
  satuan_id        BigInt?
  nama_satuan      String?   @db.VarChar(255)
  created_by       BigInt
  updated_by       BigInt?
  nama_bank        String    @db.VarChar(100)
  tracking_history Json?
  pic              String?
  jumlah_kartu     Int?
  no_telp          String?
  created_at       DateTime  @default(now())
  updated_at       DateTime?
  deleted_at       DateTime?
  deleted_by       BigInt?
  created_by_users users     @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "e_kta_batch_users_id_fk")

  e_kta e_kta[]
}

model ekta_batch_approval_log {
  id               BigInt    @id @default(autoincrement())
  ekta_batch_id    BigInt
  status           Int
  user_id          Int?
  approval_status  Int
  approval_comment String?   @db.VarChar(255)
  approval_at      DateTime? @db.Timestamp(6)
  approval_dokumen String?   @db.VarChar(255)
  approval_sign    String?   @db.VarChar(255)
  created_by       BigInt?
  created_at       DateTime? @default(now()) @db.Timestamp(6)

  created_by_users users?     @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_approval_log_users_id_fk")
  ekta_batch       ekta_batch @relation(fields: [ekta_batch_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_approval_log_ekta_batch_id_fk")
}

model ekta_batch_distribusi_log {
  id              BigInt   @id @default(autoincrement())
  ekta_batch_id   BigInt
  status          Int
  created_by      BigInt
  created_at      DateTime @default(now()) @db.Timestamp(6)
  bukti_perubahan String?  @db.VarChar(255)

  ekta_batch       ekta_batch @relation(fields: [ekta_batch_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_distribusi_log_ekta_batch_id_fk")
  created_by_users users      @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_batch_distribusi_log_users_id_fk")
}

model ekta_rejection {
  id               BigInt    @id @default(autoincrement())
  ekta_id          BigInt?
  user_id          BigInt?
  rejection_reason String    @db.VarChar(255)
  created_by       BigInt?
  created_at       DateTime? @default(now()) @db.Timestamp(6)
  e_kta_id         BigInt?

  created_by_users users? @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_rejection_users_id_fk")
  ekta             ekta?  @relation(fields: [ekta_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "ekta_rejection_ekta_id_fk")
}

model eselon {
  id          BigInt        @id @default(autoincrement())
  nama        String        @db.VarChar(255)
  created_at  DateTime?     @default(now()) @db.Timestamp(0)
  updated_at  DateTime?     @updatedAt @db.Timestamp(0)
  deleted_at  DateTime?     @db.Timestamp(0)
  nivellering nivellering[]
}

model filter_siswa {
  id           Int       @id @default(autoincrement())
  nama         String?   @db.VarChar
  is_siswa     Boolean?  @default(false)
  is_rekrutmen Boolean?  @default(false)
  created_at   DateTime? @default(now()) @db.Timestamp(6)
  updated_at   DateTime? @updatedAt @db.Timestamp(6)
  is_diktuk    Boolean?  @default(false)
}

model folder_access {
  id            BigInt    @id @default(autoincrement())
  nama_folder   String?
  path          String?
  password      String?
  tanggal_aktif DateTime? @db.Date
  created_by    BigInt?
  created_at    DateTime? @default(now()) @db.Timestamp(6)
  updated_at    DateTime? @updatedAt @db.Timestamp(6)

  created_by_users users? @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "folder_access_users_id_fk")

  @@index([id], map: "ix_public_folder_access_id")
}

model fungsi {
  id                BigInt    @id @default(autoincrement())
  nama              String?   @db.VarChar(255)
  kode              String?   @db.VarChar(255)
  is_aktif          Boolean?  @default(true)
  created_at        DateTime? @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)
  deleted_at        DateTime? @db.Timestamp(0)
  satuan_teratas_id BigInt?

  satuan        satuan?         @relation("fungsi_satuan_teratas", fields: [satuan_teratas_id], references: [id])
  fungsi_satuan fungsi_satuan[]
}

model fungsi_satuan {
  id         BigInt    @id @default(autoincrement())
  fungsi_id  BigInt
  satuan_id  BigInt
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  fungsi     fungsi    @relation(fields: [fungsi_id], references: [id], onDelete: Cascade, map: "fungsi_satuan_fungsi_id_foreign")
  satuan     satuan    @relation(fields: [satuan_id], references: [id], onDelete: Cascade, map: "fungsi_satuan_satuan_id_foreign")
}

model gelar {
  id                BigInt    @id @default(autoincrement())
  nama              String    @db.VarChar(255)
  is_gelar_belakang Boolean   @default(true)
  is_aktif          Boolean   @default(true)
  created_at        DateTime? @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)
  deleted_at        DateTime? @db.Timestamp(0)

  diktuk_personel    diktuk_personel[]
  dikbangum_personel dikbangum_personel[]
  dikum_detail       dikum_detail[]
  data_siswa         data_siswa[]
}

model golongan {
  id                       BigInt                     @id @default(autoincrement())
  nama                     String                     @db.VarChar(255)
  created_at               DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                  @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?                  @db.Timestamp(0)
  candidate_pangkat_filter candidate_pangkat_filter[]
  pangkat                  pangkat[]
  keluarga_personel        keluarga_personel[]
}

model golongan_darah {
  id                   BigInt               @id @default(autoincrement())
  nama                 String               @db.VarChar()
  created_at           DateTime             @default(now()) @db.Timestamp(0)
  created_by           BigInt?
  updated_at           DateTime?            @updatedAt() @db.Timestamp(0)
  updated_by           BigInt?
  deleted_at           DateTime?            @db.Timestamp(0)
  deleted_by           BigInt?
  golongan_darah_siswa data_genetik_siswa[] @relation("data_genetik_siswa_golongan_darah_id_fk")

  @@unique([nama, deleted_at])
}

model golongan_dsp_pns {
  id           BigInt   @id @default(autoincrement())
  golongan     String?  @db.VarChar(50)
  dsp_id       BigInt?
  golongan_dsp String?  @db.VarChar(50)
  dsp_pns      dsp_pns? @relation(fields: [dsp_id], references: [id], onDelete: Cascade, map: "golongan_dsp_pns_dsp_id_foreign")
}

model history_status_ncr {
  id               BigInt                @id @default(autoincrement())
  status           status_pengajuan_enum
  created_by_id    BigInt?
  created_at       DateTime              @default(now()) @db.Timestamp(0)
  updated_at       DateTime?             @updatedAt @db.Timestamp(0)
  pengajuan_ncr_id BigInt?
  pengajuan_ncr    pengajuan_ncr?        @relation(fields: [pengajuan_ncr_id], references: [id], onDelete: Cascade, map: "history_status_ncr_pengajuan_ncr_id_foreign")
}

model hobi {
  id              BigInt            @id @default(autoincrement())
  nama            String?           @db.VarChar(255)
  created_at      DateTime?         @default(now()) @db.Timestamp(0)
  updated_at      DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?         @db.Timestamp(0)
  hobi_personel   hobi_personel[]
  siswa_hobi      siswa_hobi[]
  data_hobi_siswa data_hobi_siswa[]
}

model hobi_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  hobi_id     BigInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  hobi        hobi      @relation(fields: [hobi_id], references: [id], onDelete: Cascade, map: "hobi_personel_hobi_id_foreign")
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "hobi_personel_personel_id_foreign")
}

model hubungan_keluarga {
  id                  BigInt                @id @default(autoincrement())
  hubungan            String?               @db.VarChar(255)
  created_at          DateTime?             @default(now()) @db.Timestamp(0)
  updated_at          DateTime?             @updatedAt @db.Timestamp(0)
  deleted_at          DateTime?             @db.Timestamp(0)
  keluarga_personel   keluarga_personel[]
  data_keluarga_siswa data_keluarga_siswa[]
}

model import_satuan_jabatan {
  id                     BigInt                            @id @default(autoincrement())
  satuan_jenis_tujuan_id BigInt
  status                 status_import_satuan_jabatan_enum
  created_by             BigInt
  created_at             DateTime?                         @default(now()) @db.Timestamp(6)
  updated_by             BigInt?
  updated_at             DateTime?                         @updatedAt @db.Timestamp(6)
  deleted_by             BigInt?
  deleted_at             DateTime?                         @db.Timestamp(6)
  finished_at            DateTime?                         @db.Timestamp(6)
  description            String?
  satuan_jenis           satuan_jenis                      @relation(fields: [satuan_jenis_tujuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_satuan_jenis")
}

model institusi {
  id               BigInt             @id @default(autoincrement())
  nama             String             @db.VarChar(255)
  is_aktif         Boolean?           @default(true)
  created_at       DateTime?          @default(now()) @db.Timestamp(0)
  updated_at       DateTime?          @db.Timestamp(0)
  deleted_at       DateTime?          @db.Timestamp(0)
  dikum_detail     dikum_detail[]
  data_dikum_siswa data_dikum_siswa[]
}

model jabatan {
  id             BigInt    @id @default(autoincrement())
  nama           String?   @db.VarChar(255)
  dsp            Int?
  nivellering_id BigInt?
  satuan_id      BigInt
  atasan_id      BigInt?
  kategori_id    BigInt
  created_at     DateTime? @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @updatedAt @db.Timestamp(0)
  deleted_at     DateTime? @db.Timestamp(0)
  is_aktif       Boolean?  @default(true)

  jabatan_kategori                      jabatan_kategori                        @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "jabatan_kategori_id_foreign")
  atasan_jabatan                        jabatan?                                @relation("atasan_jabatan_to_jabatan", fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "atasan_jabatan_fk")
  jabatan_atasan_jabatan                jabatan[]                               @relation("atasan_jabatan_to_jabatan")
  nivellering                           nivellering?                            @relation(fields: [nivellering_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "nivellering_fk")
  satuan                                satuan                                  @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "satuan_jabatan_fk")
  jabatan_personel                      jabatan_personel[]
  sipp_jabatan_satuan_terakhir_personel sipp_jabatan_satuan_terakhir_personel[]
  workflow_transaction_flow             workflow_transaction_flow[]
  jabatan_pangkat                       jabatan_pangkat[]

  mv_latest_jabatan_personel           mv_latest_jabatan_personel[]           @relation(map: "mv_latest_jabatan_personel_jabatan_id_foreign")
  mv_personel_jabatan_no_gaps          mv_personel_jabatan_no_gaps[]          @relation("mv_personel_jabatan_no_gaps_jabatan_to_jabatan")
  mv_personel_pangkat_jabatan_terakhir mv_personel_pangkat_jabatan_terakhir[] @relation("mv_personel_pangkat_jabatan_terakhir_jabatan_to_jabatan")
  mv_ranked_jabatan                    mv_ranked_jabatan[]                    @relation("mv_ranked_jabatan_jabatan_to_jabatan")
  workflow_flow                        workflow_flow[]

  mv_jabatan_terakhir_jabatan mv_jabatan_terakhir[] @relation(map: "mv_jabatan_terakhir_jabatan_id_foreign")

  @@index([nama], map: "jabatan_name_idx")
}

model jabatan_kategori {
  id         BigInt    @id @default(autoincrement())
  nama       String    @db.VarChar(255)
  is_aktif   Boolean   @default(true)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  jabatan    jabatan[]
}

model jabatan_pangkat {
  id      BigInt  @id(map: "jabatan_pangkat_pk") @default(autoincrement())
  pangkat pangkat @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pangkat_jabatan_fk")
  jabatan jabatan @relation(fields: [jabatan_id], references: [id], onDelete: Cascade)

  pangkat_id BigInt
  jabatan_id BigInt

  @@index([pangkat_id], map: "pangkat_jabatan_pangkat_id_index")
  @@index([jabatan_id], map: "pangkat_jabatan_satuan_jabatan_id_index")
}

model jabatan_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  jabatan_id  BigInt?
  jabatan     String?
  tmt_jabatan DateTime? @db.Date
  kep_nomor   String?   @db.VarChar(255)
  kep_file    String?   @db.VarChar(255)
  is_ps       Boolean?
  keterangan  String?   @db.VarChar(255)
  st_no       String?   @db.VarChar(255)
  st_file     String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  is_aktif    Boolean?
  jabatans    jabatan?  @relation(fields: [jabatan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "jabatan_fk")
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "personel_fk")

  @@index([personel_id])
  @@index([tmt_jabatan])
}

model jasmani_personel {
  id               BigInt    @id @default(autoincrement())
  personel_id      BigInt
  tahun            Int?      @db.SmallInt
  semester         Int?      @db.SmallInt
  nilai_akhir      Float?
  nilai_file       String?   @db.VarChar(255)
  keterangan       String?   @db.VarChar(255)
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @updatedAt @db.Timestamp(0)
  deleted_at       DateTime? @db.Timestamp(0)
  nilai_lari_12_m  Decimal?  @db.Decimal
  hasil_lari_12_m  Decimal?  @db.Decimal
  nilai_pull_up    Decimal?  @db.Decimal
  hasil_pull_up    Decimal?  @db.Decimal
  nilai_push_up    Decimal?  @db.Decimal
  hasil_push_up    Decimal?  @db.Decimal
  nilai_sit_up     Decimal?  @db.Decimal
  hasil_sit_up     Decimal?  @db.Decimal
  nilai_shutle_run Decimal?  @db.Decimal
  hasil_shutle_run Decimal?  @db.Decimal
  nilai_chinning   Decimal?  @db.Decimal
  hasil_chinning   Decimal?  @db.Decimal
  nilai_a          Decimal?  @db.Decimal
  nilai_b          Decimal?  @db.Decimal
  personel         personel  @relation(fields: [personel_id], references: [id], map: "jasmani_personel_personel_id_foreign")
}

model jenis_diktuk {
  id                BigInt              @id @default(autoincrement())
  nama              String              @db.VarChar(255)
  deskripsi         String?             @db.VarChar
  background_color  String?             @db.VarChar(10)
  created_at        DateTime?           @default(now()) @db.Timestamp(0)
  updated_at        DateTime?           @db.Timestamp(0)
  deleted_at        DateTime?           @db.Timestamp(0)
  angket_patma      angket_patma[]
  kompetensi_diktuk kompetensi_diktuk[]
  siswa             siswa[]
  data_siswa        data_siswa[]
}

model jenis_institusi {
  id   Int    @id @default(autoincrement())
  nama String @db.VarChar(255)

  created_at DateTime?   @default(now()) @db.Timestamp(6)
  updated_at DateTime?   @updatedAt @db.Timestamp(6)
  deleted_at DateTime?   @db.Timestamp(6)
  kerjasama  kerjasama[]
}

model jenis_pekerjaan {
  id                 BigInt               @id @default(autoincrement())
  jenis              String?              @db.VarChar(255)
  created_at         DateTime?            @default(now()) @db.Timestamp(0)
  updated_at         DateTime?            @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?            @db.Timestamp(0)
  pekerjaan_keluarga pekerjaan_keluarga[]
}

model jenis_rambut {
  id                 BigInt               @id @default(autoincrement())
  nama               String               @db.VarChar()
  created_at         DateTime             @default(now()) @db.Timestamp(0)
  created_by         BigInt?
  updated_at         DateTime?            @updatedAt() @db.Timestamp(0)
  updated_by         BigInt?
  deleted_at         DateTime?            @db.Timestamp(0)
  deleted_by         BigInt?
  jenis_rambut_siswa data_genetik_siswa[] @relation("data_genetik_siswa_jenis_rambut_id_fk")

  @@unique([nama, deleted_at])
}

model jurusan {
  id               BigInt             @id @default(autoincrement())
  nama             String             @db.VarChar(255)
  is_aktif         Boolean            @default(true)
  created_at       DateTime?          @default(now()) @db.Timestamp(0)
  updated_at       DateTime?          @db.Timestamp(0)
  deleted_at       DateTime?          @db.Timestamp(0)
  dikum_detail     dikum_detail[]
  data_dikum_siswa data_dikum_siswa[]
}

model kabupaten {
  id                BigInt              @id(map: "kabupaten_new_pkey") @default(autoincrement())
  nama              String              @db.VarChar(255)
  provinsi_id       BigInt?
  dukcapil_id       String?             @db.VarChar
  created_at        DateTime?           @default(now()) @db.Timestamp(0)
  updated_at        DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at        DateTime?           @db.Timestamp(0)
  personel          personel[]
  siswa_alamat      siswa_alamat[]
  data_alamat_siswa data_alamat_siswa[]
}

model kecamatan {
  id                BigInt              @id(map: "kecamatan_new_pkey") @default(autoincrement())
  nama              String              @db.VarChar(255)
  kabupaten_id      BigInt?
  dukcapil_id       String?             @db.VarChar
  created_at        DateTime?           @default(now()) @db.Timestamp(0)
  updated_at        DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at        DateTime?           @db.Timestamp(0)
  siswa_alamat      siswa_alamat[]
  data_alamat_siswa data_alamat_siswa[]
}

model keluarga_personel {
  id            BigInt              @id @default(autoincrement())
  nama_keluarga String?             @db.VarChar(255)
  jenis_kelamin jenis_kelamin_enum?

  alamat        String?   @db.VarChar(255)
  no_hp         String?   @db.VarChar(255)
  status        String?   @db.VarChar(255)
  nrp_keluarga  String?   @db.VarChar(255)
  tempat_lahir  String?   @db.VarChar(255)
  tanggal_lahir DateTime? @db.Date

  agama_id             BigInt?
  personel_id          BigInt?
  hubungan_keluarga_id BigInt?
  personel_keluarga_id BigInt?
  golongan_id          BigInt?

  tanggal_nikah    DateTime? @db.Date
  kpis_nomor       String?   @db.VarChar(255)
  kpis_file        String?   @db.VarChar(255)
  buku_nikah_nomor String?   @db.VarChar(255)
  buku_nikah_file  String?   @db.VarChar(255)
  foto_file        String?   @db.VarChar(255)

  status_nikah      Boolean?
  status_pernikahan Boolean?

  tanggal_cerai DateTime? @db.Date
  cerai_file    String?   @db.VarChar(255)

  parent_hubungan_keluarga_id BigInt?
  status_personel             Boolean?
  no_ijinnikah                String?   @db.VarChar(255)
  file_ijinnikah              String?   @db.VarChar(255)
  ktp_nomor                   String?   @db.VarChar(255)
  ktp_file                    String?   @db.VarChar(255)
  created_at                  DateTime? @default(now()) @db.Timestamp(0)
  updated_at                  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at                  DateTime? @db.Timestamptz(6)

  parent_keluarga_personel_keluarga_personel keluarga_personel[]  @relation("parent_keluarga_personel_to_keluarga_personel")
  parent_keluarga_personel                   keluarga_personel?   @relation("parent_keluarga_personel_to_keluarga_personel", fields: [parent_hubungan_keluarga_id], references: [id], onDelete: Cascade, map: "parent_keluarga_personel_id_foreign")
  agama                                      agama?               @relation(fields: [agama_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "parent_personel_agama_id_foreign")
  hubungan_keluarga                          hubungan_keluarga?   @relation(fields: [hubungan_keluarga_id], references: [id], onDelete: Cascade, map: "parent_personel_hubungan_keluarga_id_foreign")
  personel                                   personel?            @relation(fields: [personel_id], references: [id], map: "parent_personel_personel_id_foreign")
  golongan                                   golongan?            @relation(fields: [golongan_id], references: [id], onDelete: Cascade, map: "parent_personel_golongan_id_foreign")
  pekerjaan_keluarga                         pekerjaan_keluarga[]

  @@index([id], map: "ix_public_keluarga_personel_id")
}

model kelurahan {
  id                BigInt              @id(map: "kelurahan_new_pkey") @default(autoincrement())
  nama              String              @db.VarChar(255)
  kecamatan_id      BigInt?
  dukcapil_id       String?             @db.VarChar
  created_at        DateTime?           @default(now()) @db.Timestamp(0)
  updated_at        DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at        DateTime?           @db.Timestamp(0)
  siswa_alamat      siswa_alamat[]
  data_alamat_siswa data_alamat_siswa[]
}

model kerjasama {
  id                 BigInt                        @id @default(autoincrement())
  institusi          String
  durasi_hari        BigInt?
  tanggal_mulai      DateTime                      @db.Timestamp(6)
  tanggal_selesai    DateTime?                     @db.Timestamp(6)
  status_id          BigInt
  created_at         DateTime?                     @default(now()) @db.Timestamp(0)
  updated_at         DateTime?                     @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?                     @db.Timestamp(0)
  satuan_id          BigInt?
  kategori_file      kategori_kerjasama_file_enum?
  jenis_institusi_id Int?
  jenis_institusi    jenis_institusi?              @relation(fields: [jenis_institusi_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_jenis_institusi")
  satuan             satuan?                       @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_polda_satuan")
  kerjasama_status   kerjasama_status              @relation(fields: [status_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "kerjasama_status_id_foreign")
  kerjasama_file_mou kerjasama_file_mou[]
  kerjasama_file_pks kerjasama_file_pks[]
}

model kerjasama_durasi_bulan {
  id         BigInt    @id @default(autoincrement())
  durasi     Int
  display    String
  created_at DateTime  @default(now()) @db.Timestamp(6)
  updated_at DateTime? @updatedAt @db.Timestamp(6)
  deleted_at DateTime? @db.Timestamp(6)
}

model kerjasama_file_mou {
  id           BigInt    @id @default(autoincrement())
  id_kerjasama BigInt
  originalname String    @db.VarChar
  encoding     String    @db.VarChar
  mimetype     String    @db.VarChar
  destination  String?   @db.VarChar
  filename     String?   @db.VarChar
  path         String?   @db.VarChar
  size         BigInt?
  key          String?   @db.VarChar
  url          String?   @db.VarChar
  created_at   DateTime  @default(now()) @db.Timestamp(6)
  updated_at   DateTime? @updatedAt @db.Timestamp(6)
  deleted_at   DateTime? @db.Timestamptz(6)
  kerjasama    kerjasama @relation(fields: [id_kerjasama], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "kerjasama_id_fk")
}

model kerjasama_file_pks {
  id              BigInt    @id @default(autoincrement())
  id_kerjasama    BigInt
  originalname    String    @db.VarChar
  encoding        String    @db.VarChar
  mimetype        String    @db.VarChar
  destination     String?   @db.VarChar
  filename        String?   @db.VarChar
  path            String?   @db.VarChar
  size            BigInt?
  key             String?   @db.VarChar
  url             String?   @db.VarChar
  created_at      DateTime  @default(now()) @db.Timestamp(6)
  updated_at      DateTime? @db.Timestamp(6)
  deleted_at      DateTime? @db.Timestamptz(6)
  tanggal_mulai   DateTime  @db.Timestamp(6)
  tanggal_selesai DateTime  @db.Timestamp(6)
  kerjasama       kerjasama @relation(fields: [id_kerjasama], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "kerjasama_id_fk")
}

model kerjasama_status {
  id         BigInt      @id @default(autoincrement())
  nama       String
  created_at DateTime    @default(now()) @db.Timestamp(6)
  updated_at DateTime?   @updatedAt @db.Timestamp(6)
  deleted_at DateTime?   @db.Timestamp(6)
  kerjasama  kerjasama[]
}

model kompetensi_diktuk {
  id                                   BigInt                  @id @default(autoincrement())
  nama                                 String?                 @db.VarChar(255)
  created_at                           DateTime?               @default(now()) @db.Timestamp(0)
  updated_at                           DateTime?               @db.Timestamp(0)
  deleted_at                           DateTime?               @db.Timestamp(0)
  diktuk_id                            BigInt?
  parent_id                            BigInt?
  angket_patma                         angket_patma[]
  jenis_diktuk                         jenis_diktuk?           @relation(fields: [diktuk_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  sub                                  kompetensi_diktuk?      @relation("kompetensi_diktuk_kompetensi_diktuk_id_fk", fields: [parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa                                siswa[]
  sub_kompetensi_diktuk                sub_kompetensi_diktuk[]
  kompetensi_diktuk                    kompetensi_diktuk[]     @relation("kompetensi_diktuk_kompetensi_diktuk_id_fk")
  data_siswa_kompetensi_diktuk         data_siswa[]            @relation("data_siswa_kompetensi_diktuk_id_fk")
  data_siswa_sub_kompetensi_diktuk     data_siswa[]            @relation("data_siswa_kompetensi_diktuk_id_fk_2")
  data_siswa_sub_sub_kompetensi_diktuk data_siswa[]            @relation("data_siswa_kompetensi_diktuk_id_fk_3")
}

model konsultasi {
  id                  BigInt                 @id(map: "konsultasi_pk") @default(autoincrement())
  personel_id         BigInt
  chat_id             String?                @db.VarChar(255)
  konselor_id         BigInt?
  konsultasi_jenis_id BigInt
  keluhan             String?
  polda_id            BigInt
  created_at          DateTime?              @default(now()) @db.Timestamp(0)
  updated_at          DateTime?              @updatedAt @db.Timestamp(0)
  deleted_at          DateTime?              @db.Timestamp(0)
  is_tindakan         Boolean                @default(false)
  no_hp_tindakan      String?                @db.VarChar
  catatan             String?
  status              status_konsultasi_enum
  konsultasi_jenis    konsultasi_jenis       @relation(fields: [konsultasi_jenis_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_konsultasi_jenis_id_fk")
  konselor            konsultasi_konselor?   @relation(fields: [konselor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_konsultasi_konselor_fk")
  personel            personel               @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_personel_fk")
  polda               satuan                 @relation(fields: [polda_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_satuan_fk")
  konsultasi_file     konsultasi_file[]
}

model konsultasi_file {
  id            BigInt     @id(map: "konsultasi_file_pk") @default(autoincrement())
  konsultasi_id BigInt
  originalname  String     @db.VarChar
  encoding      String     @db.VarChar
  mimetype      String     @db.VarChar
  destination   String?    @db.VarChar
  filename      String?    @db.VarChar
  path          String?    @db.VarChar
  size          BigInt?
  key           String?    @db.VarChar
  url           String?    @db.VarChar
  created_at    DateTime   @default(now()) @db.Timestamp(6)
  updated_at    DateTime?  @updatedAt @db.Timestamp(6)
  deleted_at    DateTime?  @db.Timestamptz(6)
  konsultasi    konsultasi @relation(fields: [konsultasi_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_file_konsultasi_fk")
}

model konsultasi_jenis {
  id         BigInt       @id(map: "konsultasi_jenis_id_pk") @default(autoincrement())
  nama       String?      @db.VarChar(255)
  created_at DateTime?    @default(now()) @db.Timestamp(0)
  updated_at DateTime?    @updatedAt @db.Timestamp(0)
  deleted_at DateTime?    @db.Timestamp(0)
  konsultasi konsultasi[]
}

model konsultasi_konselor {
  id          BigInt       @id(map: "konsultasi_konselor_pk") @default(autoincrement())
  personel_id BigInt       @unique(map: "konsultasi_konselor_unique")
  created_at  DateTime?    @default(now()) @db.Timestamp(0)
  updated_at  DateTime?    @updatedAt @db.Timestamp(0)
  deleted_at  DateTime?    @db.Timestamp(0)
  konsultasi  konsultasi[]
  personel    personel     @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_konselor_personel_fk")
}

model konsultasi_personel {
  id          BigInt    @id(map: "konsultasi_personel_pk") @default(autoincrement())
  personel_id BigInt
  chat_id     String?   @unique(map: "konsultasi_personel_unique") @db.VarChar(255)
  status      String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  konsultasi  String    @db.VarChar(255)
  konselor    String?   @db.VarChar(255)
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "konsultasi_personel_personel_fk")
}

model lama_dikbangspes_personel {
  id                    Int      @id @default(autoincrement())
  personel_id           BigInt
  tingkat_id            BigInt
  dikbangspes_id        BigInt
  tanggal_masuk         DateTime @db.Timestamp(6)
  tanggal_selesai       DateTime @db.Timestamp(6)
  lama_dikbangspes_hari Int
  created_at            DateTime @default(now()) @db.Timestamp(6)
  updated_at            DateTime @updatedAt @db.Timestamp(6)

  dikbangspes         dikbangspes         @relation(fields: [dikbangspes_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel            personel            @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  dikbangspes_tingkat dikbangspes_tingkat @relation(fields: [tingkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([personel_id, dikbangspes_id], map: "idx_lama_dikbangspes_personel_personel_dikbangspes")
  @@index([dikbangspes_id, tingkat_id], map: "idx_lama_dikbangspes_personel_personel_dikbangspes_tingkat")
  @@index([personel_id], map: "idx_lama_dikbangspes_personel_personel_id")
  @@index([personel_id, dikbangspes_id], map: "idx_personel_dikbangspes")
}

model lama_mddn_personel {
  id                  Int         @id @default(autoincrement())
  personel_id         BigInt
  nivellering_id      BigInt
  lama_menjabat_bulan Int
  lama_menjabat_hari  Int
  created_at          DateTime    @default(now()) @db.Timestamp(6)
  updated_at          DateTime    @db.Timestamp(6)
  nivellering         nivellering @relation(fields: [nivellering_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel            personel    @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([nivellering_id], map: "idx_lama_mddn_personel_nivellering_id")
  @@index([personel_id, nivellering_id], map: "idx_lama_mddn_personel_personel_nivellering")
  @@index([nivellering_id], map: "idx_nivellering_id")
  @@index([personel_id, nivellering_id], map: "idx_personel_nivellering")
}

model lama_mddp_personel {
  id                  Int      @id @default(autoincrement())
  personel_id         BigInt
  pangkat_id          BigInt
  tmt                 DateTime @db.Timestamp(6)
  lama_menjabat_bulan Int
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @updatedAt @db.Timestamp(6)
  lama_menjabat_hari  Int
  pangkat             pangkat  @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel            personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([pangkat_id], map: "idx_lama_mddp_personel_pangkat_id")
  @@index([personel_id, pangkat_id], map: "idx_lama_mddp_personel_personel_pangkat")
  @@index([pangkat_id], map: "idx_pangkat_id")
  @@index([personel_id, pangkat_id], map: "idx_personel_pangkat")
}

model level {
  id         Int          @id @default(autoincrement())
  nama       String?
  created_at DateTime     @default(now()) @db.Timestamp(0)
  updated_at DateTime     @updatedAt @db.Timestamp(0)
  deleted_at DateTime?    @db.Timestamp(0)
  role       role[]
  users_role users_role[]
}

model log_management_api {
  id         BigInt    @id @default(autoincrement())
  api_id     BigInt
  user_agent String?
  action     String?   @db.Text
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  api        api       @relation(fields: [api_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model logs_activity {
  id         BigInt    @id @default(autoincrement())
  user_id    BigInt
  activity   String
  detail     String
  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime  @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  user       users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_users")
}

model misi {
  id            BigInt          @id @default(autoincrement())
  nama          String?         @db.VarChar(255)
  kota          String?         @db.VarChar(255)
  provinsi      String?         @db.VarChar(255)
  negara        String?         @db.VarChar(255)
  is_aktif      Boolean?        @default(true)
  created_at    DateTime?       @default(now()) @db.Timestamp(0)
  updated_at    DateTime?       @updatedAt @db.Timestamp(0)
  deleted_at    DateTime?       @db.Timestamp(0)
  misi_personel misi_personel[]
}

model misi_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  misi_id     BigInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  misi        misi      @relation(fields: [misi_id], references: [id], onDelete: Cascade, map: "misi_personel_misi_id_foreign")
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "misi_personel_personel_id_foreign")
}

model mutasi_jabatan {
  id                         BigInt                  @id(map: "mutasi_jabatan_pk") @default(autoincrement())
  personel_id                BigInt
  satuan_lama_id             BigInt
  satuan_baru_id             BigInt
  type                       String                  @db.VarChar
  keterangan                 String?                 @db.VarChar
  created_at                 DateTime                @default(now()) @db.Timestamp(6)
  updated_at                 DateTime?               @updatedAt @db.Timestamp(6)
  deleted_at                 DateTime?               @db.Timestamp(6)
  tmt                        DateTime?               @db.Timestamp(6)
  personel                   personel                @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mutasi_jabatan_personel_fk")
  mutasi_jabatan_satuan_baru satuan                  @relation("satuan_baru_to_mutasi_jabatan", fields: [satuan_baru_id], references: [id], onDelete: Cascade, map: "mutasi_jabatan_satuan_baru_fk")
  mutasi_jabatan_satuan_lama satuan                  @relation("satuan_lama_to_mutasi_jabatan", fields: [satuan_lama_id], references: [id], onDelete: Cascade, map: "mutasi_jabatan_satuan_lama_fk")
  mutasi_jabatan_dokumen     mutasi_jabatan_dokumen?
}

model mutasi_jabatan_dokumen {
  id                BigInt         @id(map: "mutasi_jabatan_dokumen_pk") @default(autoincrement())
  mutasi_jabatan_id BigInt         @unique
  file_renskep      Json?          @db.Json
  file_skep         Json?          @db.Json
  file_telegram     Json?          @db.Json
  file_winjak       Json?          @db.Json
  file_petikan      Json?          @db.Json
  created_at        DateTime       @default(now()) @db.Timestamp(6)
  updated_at        DateTime?      @updatedAt @db.Timestamp(6)
  deleted_at        DateTime?      @db.Timestamp(6)
  mutasi_jabatan    mutasi_jabatan @relation(fields: [mutasi_jabatan_id], references: [id], onDelete: Cascade, map: "mutasi_jabatan_dokumen_fk")
}

model negara {
  id                 BigInt               @id @default(autoincrement())
  nama               String               @db.VarChar(255)
  created_at         DateTime?            @default(now()) @db.Timestamp(0)
  updated_at         DateTime?            @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?            @db.Timestamp(0)
  penugasan_personel penugasan_personel[]
  provinsi           provinsi[]
}

model nivellering {
  id                           BigInt                 @id @default(autoincrement())
  nama                         String                 @db.VarChar(255)
  eselon_id                    BigInt
  created_at                   DateTime?              @default(now()) @db.Timestamp(0)
  updated_at                   DateTime?              @updatedAt @db.Timestamp(0)
  deleted_at                   DateTime?              @db.Timestamp(0)
  nivel_up_id                  BigInt?                @unique
  nivel_down_id                BigInt?                @unique
  jabatan                      jabatan[]
  lama_mddn_personel           lama_mddn_personel[]
  score_nieve_personel         score_nieve_personel[]
  nivellering_up               nivellering?           @relation("nivellring_up_to_nivellering", fields: [nivel_up_id], references: [id])
  nivellering_nivellering_up   nivellering?           @relation("nivellring_up_to_nivellering")
  nivellering_down             nivellering?           @relation("nivellring_down_to_nivellering", fields: [nivel_down_id], references: [id])
  nivellering_nivellering_down nivellering[]          @relation("nivellring_down_to_nivellering")
  eselon                       eselon                 @relation(fields: [eselon_id], references: [id], onDelete: Cascade, map: "nivellering_eselon_id_foreign")

  mv_nivellering_terakhir  mv_nivellering_terakhir[]  @relation(map: "mv_nivellering_terakhir_nivellering_id_foreign")
  candidate_pangkat_filter candidate_pangkat_filter[] @relation(map: "candidate_pangkat_filter_nivellering_id_foreign")
}

model nivellering_mutation_requirements {
  id                 Int    @id @default(autoincrement())
  origin_nivel       String @db.VarChar
  destination_nivel  String @db.VarChar
  min_duration_month Int
}

model notifikasi {
  id          BigInt   @id(map: "notifikasi_pk") @default(autoincrement())
  users_id    BigInt
  title       String   @db.VarChar
  description String   @db.VarChar
  type        String   @db.VarChar
  fcm_token   String?  @db.VarChar
  is_read     Boolean
  created_at  DateTime @default(now()) @db.Timestamp(6)
  updated_at  DateTime @updatedAt @db.Timestamp(6)
  target      String?  @db.VarChar
  users       users    @relation(fields: [users_id], references: [id], onDelete: Cascade, map: "notifikasi_fk")
}

model olahraga {
  id                BigInt              @id @default(autoincrement())
  nama              String?             @db.VarChar(255)
  created_at        DateTime?           @default(now()) @db.Timestamp(0)
  updated_at        DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at        DateTime?           @db.Timestamp(0)
  olahraga_personel olahraga_personel[]
}

model olahraga_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  olahraga_id BigInt
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  olahraga    olahraga  @relation(fields: [olahraga_id], references: [id], onDelete: Cascade, map: "olahraga_personel_olahraga_id_foreign")
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "olahraga_personel_personel_id_foreign")
}

model orang_asli_papua {
  id         BigInt    @id @default(autoincrement())
  nama       String    @unique
  created_by BigInt
  created_at DateTime  @default(now())
  updated_by BigInt?
  updated_at DateTime?
  deleted_by BigInt?
  deleted_at DateTime?

  users_created_by users        @relation(fields: [created_by], references: [id])
  data_siswa       data_siswa[]
}

model p3k {
  id            BigInt    @id @default(autoincrement())
  nama          String    @db.VarChar(255)
  jenis_kelamin String    @db.VarChar(255)
  satuan        String    @db.VarChar(255)
  satuan_id     BigInt?
  created_at    DateTime? @default(now())
  updated_at    DateTime?
  deleted_at    DateTime?
  nip           String?   @db.VarChar(30)
  satuan_       satuan?   @relation(fields: [satuan_id], references: [id])
}

model pangkat {
  id                       BigInt                     @id @default(autoincrement())
  nama                     String                     @db.VarChar(255)
  nama_singkat             String                     @db.VarChar(255)
  kategori_id              BigInt?
  golongan_id              BigInt?
  created_at               DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                  @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?                  @db.Timestamp(0)
  tingkat_id               BigInt?
  bagpsikologi             bagpsikologi[]
  candidate_pangkat_filter candidate_pangkat_filter[]
  diklat_personel          diklat_personel[]
  jabatan_pangkat          jabatan_pangkat[]
  kgb_gaji_pangkat         kgb_gaji_pangkat[]
  lama_mddp_personel       lama_mddp_personel[]
  golongan                 golongan?                  @relation(fields: [golongan_id], references: [id], onDelete: Cascade, map: "pangkat_golongan_id_foreign")
  pangkat_kategori         pangkat_kategori?          @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "pangkat_kategori_id_foreign")
  pangkat_tingkat          pangkat_tingkat?           @relation(fields: [tingkat_id], references: [id], onDelete: Cascade, map: "pangkat_tingkat_id_foreign")
  pangkat_personel         pangkat_personel[]
  score_nieve_personel     score_nieve_personel[]
  sipk_personel_pangkat    sipk_personel[]            @relation("pangkat_to_pangkat")
  survey_destination       survey_destination[]

  mv_pangkat_terakhir                  mv_pangkat_terakhir[]                  @relation(map: "mv_pangkat_terakhir_pangkat_id_foreign")
  mv_personel                          mv_personel[]                          @relation("mv_personel_personel_to_personel")
  mv_personel_pangkat_jabatan_terakhir mv_personel_pangkat_jabatan_terakhir[] @relation("mv_personel_pangkat_jabatan_terakhir_pangkat_to_pangkat")
  mv_personel_pangkat_no_gaps          mv_personel_pangkat_no_gaps[]          @relation("mv_personel_pangkat_no_gaps_pangkat_to_pangkat")
  mv_ranked_pangkat                    mv_ranked_pangkat[]                    @relation("mv_ranked_pangkat_pangkat_to_pangkat")

  @@index([nama])
}

model pangkat_dsp_polri {
  id          BigInt     @id @default(autoincrement())
  pangkat     String?    @db.VarChar(255)
  dsp_id      BigInt?
  pangkat_dsp String?    @db.VarChar(255)
  dsp_polri   dsp_polri? @relation(fields: [dsp_id], references: [id], onDelete: Cascade, map: "pangkat_dsp_polri_dsp_id_foreign")
}

model pangkat_jabatan {
  id         BigInt    @id @default(autoincrement())
  pangkat_id BigInt
  jabatan_id BigInt?
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
}

model pangkat_kategori {
  id                       BigInt                     @id @default(autoincrement())
  nama                     String                     @db.VarChar(255)
  created_at               DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                  @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?                  @db.Timestamp(0)
  candidate_pangkat_filter candidate_pangkat_filter[]
  statistik_satker         mv_statistik_satker[]
  pangkat                  pangkat[]

  mv_statistik_satker_old mv_statistik_satker_old[] @relation("mv_statistik_satker_old_kategori_to_pangkat_kategori")
}

model pangkat_personel {
  id                BigInt    @id(map: "pangkat_personel_pk") @default(autoincrement())
  pangkat_id        BigInt
  tmt               String?   @db.VarChar(255)
  kep_nomor         String?   @db.VarChar(255)
  kep_file          String?   @db.VarChar(255)
  kep_tanggal       String?   @db.VarChar(255)
  tanggal_perubahan DateTime? @db.Timestamp(0)
  personel_id       BigInt
  created_at        DateTime? @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)
  deleted_at        DateTime? @db.Timestamp(0)
  is_aktif          Boolean?
  pangkat           pangkat   @relation(fields: [pangkat_id], references: [id], onDelete: Cascade, map: "pangkat_personel_pangkat_id_foreign")
  personel          personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "pangkat_personel_personel_id_foreign")

  @@index([personel_id])
  @@index([tmt])
}

model pangkat_tingkat {
  id         BigInt    @id @default(autoincrement())
  nama       String    @db.VarChar(255)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  pangkat    pangkat[]
}

model pekerjaan_keluarga {
  id                   BigInt             @id @default(autoincrement())
  nama_institusi       String?            @db.VarChar(255)
  telepon              String?            @db.VarChar(255)
  alamat               String?            @db.VarChar(255)
  status_terakhir      Boolean?
  jenis_pekerjaan_id   BigInt?
  keluarga_personel_id BigInt?
  tanggal_perubahan    DateTime?          @db.Timestamp(0)
  created_at           DateTime?          @default(now()) @db.Timestamp(0)
  updated_at           DateTime?          @updatedAt @db.Timestamp(0)
  deleted_at           DateTime?          @db.Timestamp(0)
  jenis_pekerjaan      jenis_pekerjaan?   @relation(fields: [jenis_pekerjaan_id], references: [id], onDelete: Cascade, map: "pekerjaan_keluarga_jenis_pekerjaan_id_foreign")
  keluarga_personel    keluarga_personel? @relation(fields: [keluarga_personel_id], references: [id], onDelete: Cascade, map: "pekerjaan_keluarga_keluarga_personel_id_foreign")
}

model pelatihan {
  id                  BigInt               @id @default(autoincrement())
  nama                String               @db.VarChar(255)
  lokasi_id           BigInt
  tingkat_id          BigInt?
  kategori_id         BigInt?
  is_aktif            Boolean?             @default(true)
  created_at          DateTime?            @default(now()) @db.Timestamp(0)
  updated_at          DateTime?            @updatedAt @db.Timestamp(0)
  deleted_at          DateTime?            @db.Timestamp(0)
  pelatihan_kategori  pelatihan_kategori?  @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "pelatihan_kategori_id_foreign")
  dikbangspes_lokasi  dikbangspes_lokasi   @relation(fields: [lokasi_id], references: [id], onDelete: Cascade, map: "pelatihan_lokasi_id_foreign")
  dikbangspes_tingkat dikbangspes_tingkat? @relation(fields: [tingkat_id], references: [id], onDelete: Cascade, map: "pelatihan_tingkat_id_foreign")
  pelatihan_personel  pelatihan_personel[]
}

model pelatihan_kategori {
  id         BigInt      @id @default(autoincrement())
  nama       String?     @db.VarChar(255)
  created_at DateTime?   @default(now()) @db.Timestamp(0)
  updated_at DateTime?   @updatedAt @db.Timestamp(0)
  deleted_at DateTime?   @db.Timestamp(0)
  pelatihan  pelatihan[]
}

model pelatihan_personel {
  id                   BigInt    @id @default(autoincrement())
  pelatihan_id         BigInt
  personel_id          BigInt
  tanggal_masuk        DateTime? @db.Date
  tanggal_selesai      DateTime? @db.Date
  nilai                Float?
  transkrip_nilai_file String?   @db.VarChar(255)
  sertifikat_nomor     String?   @db.VarChar(255)
  sertifikat_file      String?   @db.VarChar(255)
  created_at           DateTime? @default(now()) @db.Timestamp(0)
  updated_at           DateTime? @updatedAt @db.Timestamp(0)
  deleted_at           DateTime? @db.Timestamp(0)
  pelatihan            pelatihan @relation(fields: [pelatihan_id], references: [id], onDelete: Cascade, map: "pelatihan_personel_pelatihan_id_foreign")
  personel             personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "pelatihan_personel_personel_id_foreign")
}

model pengajuan_ncr {
  id                       BigInt                @id @default(autoincrement())
  satuan_id                BigInt
  tempat_pernikah          String                @db.VarChar(255)
  tanggal_pernikah         DateTime?             @db.Date
  tanggal_sidang           DateTime?             @db.Date
  status_pengajuan         status_pengajuan_enum
  alamat_personel_id       BigInt
  status_kawin_pemohon_id  BigInt
  status_kawin_pasangan_id BigInt
  jenis_pengajuan          jenis_pengajuan_enum
  nomor_surat_izin_nikah   String?               @db.VarChar(255)
  surat_izin_nikah         BigInt?
  personel_id              BigInt
  pasangan_personel_id     BigInt?
  data_calon_pasangan      Json
  created_at               DateTime              @default(now()) @db.Timestamp(0)
  updated_at               DateTime?             @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?             @db.Timestamp(0)
  surat_akta_ncr           BigInt?
  dokumen_ncr              dokumen_ncr[]
  history_status_ncr       history_status_ncr[]
  personel                 personel              @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "pengajuan_ncr_personel_id_foreign")
  alamat                   alamat                @relation(fields: [alamat_personel_id], references: [id], onDelete: Cascade, map: "pengajuan_ncr_alamat_personel_id_foreign")
  satuan                   satuan                @relation(fields: [satuan_id], references: [id], onDelete: Cascade, map: "pengajuan_ncr_satuan_id_foreign")
  status_kawin_pasangan    status_kawin          @relation("status_kawin_pasangan_to_status_kawin", fields: [status_kawin_pasangan_id], references: [id], onDelete: Cascade, map: "pengajuan_ncr_status_kawin_pasangan_id_foreign")
  status_kawin_pemohon     status_kawin          @relation("status_kawin_pemohon_to_status_kawin", fields: [status_kawin_pemohon_id], references: [id], onDelete: Cascade, map: "pengajuan_ncr_status_kawin_pemohon_id_foreign")
}

model pendataan_perumahan {
  id                 BigInt    @id @default(autoincrement())
  nama_personel      String
  jenis_kelamin      String
  nama_pangkat       String
  nama_satuan        String
  status_kepemilikan String
  lokasi_kepemilikan String
  created_at         DateTime? @db.Timestamp(0)
  updated_at         DateTime? @db.Timestamp(0)
  created_by         String?
  updated_by         Int?
  deleted_at         DateTime? @db.Timestamp(0)
  deleted_by         String?
}

model penugasan_instansi {
  id                 BigInt               @id @default(autoincrement())
  nama               String               @db.VarChar(255)
  is_aktif           Boolean              @default(true)
  created_at         DateTime?            @default(now()) @db.Timestamp(0)
  updated_at         DateTime?            @db.Timestamp(0)
  deleted_at         DateTime?            @db.Timestamp(0)
  penugasan_personel penugasan_personel[]
  bagassus_personel  bagassus_personel[]
}

model penugasan_personel {
  id                  BigInt              @id @default(autoincrement())
  personel_id         BigInt?
  sprin_nomor         String?             @db.VarChar(255)
  sprin_file          String?             @db.VarChar(255)
  sprin_penerbit      String?             @db.VarChar(255)
  kategori_jabatan_id BigInt?
  jabatan_penugasan   String              @db.VarChar(255)
  tmt_mulai           DateTime?           @db.Date
  tmt_selesai         DateTime?           @db.Date
  instansi_id         BigInt?
  negara_id           BigInt?
  tanggal_perubahan   DateTime?           @db.Timestamp(0)
  created_at          DateTime?           @default(now()) @db.Timestamp(0)
  updated_at          DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at          DateTime?           @db.Timestamp(0)
  penugasan_instansi  penugasan_instansi? @relation(fields: [instansi_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "penugasan_personel_instansi_id_foreign")
  negara              negara?             @relation(fields: [negara_id], references: [id], onDelete: Cascade, map: "penugasan_personel_negara_id_foreign")
  personel            personel?           @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "penugasan_personel_personel_id_foreign")
}

model role_module {
  id         BigInt    @id @default(autoincrement())
  role_id    Int
  module_id  Int
  created_at DateTime  @default(now())
  updated_at DateTime? @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)

  role   role    @relation(fields: [role_id], references: [id])
  module modules @relation(fields: [module_id], references: [id])
}

model modules {
  id          Int           @id @default(autoincrement())
  nama        String
  name_long   String
  parent_id   Int?
  created_at  DateTime      @default(now())
  deleted_at  DateTime?     @db.Timestamp(0)
  role_access role_access[]
  permission  permission[]
  role_module role_module[]
}

model role_tipe {
  id         Int       @id @default(autoincrement())
  nama       String
  created_at DateTime  @default(now())
  updated_at DateTime? @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  role       role[]
}

model bagian {
  id         Int       @id @default(autoincrement())
  nama       String
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime? @db.Timestamp(0)

  bagian_role        role[]
  survey_destination survey_destination[]
}

model permission {
  id         Int       @id @default(autoincrement())
  nama       String
  desc       String
  label      String
  module_id  Int
  created_at DateTime  @default(now())
  updated_at DateTime? @updatedAt
  deleted_at DateTime? @db.Timestamp(0)

  modules         modules           @relation(fields: [module_id], references: [id])
  role_access     role_access[]
  role_permission role_permission[]
}

model role_permission {
  id            Int       @id @default(autoincrement())
  permission_id Int
  role_id       Int
  created_at    DateTime  @default(now())
  updated_at    DateTime? @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)

  permission permission? @relation(fields: [permission_id], references: [id])
  role       role?       @relation(fields: [role_id], references: [id])
}

model role_access {
  id            Int       @id @default(autoincrement())
  role_id       Int
  module_id     Int
  permission_id Int?
  portal_id     Int?
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
  deleted_at    DateTime? @db.Timestamp(0)

  role       role        @relation(fields: [role_id], references: [id], onDelete: Cascade)
  modules    modules?    @relation(fields: [module_id], references: [id], onDelete: Cascade)
  permission permission? @relation(fields: [permission_id], references: [id], onDelete: Cascade)
  portal     portal?     @relation(fields: [portal_id], references: [id], onDelete: Cascade)

  @@unique([role_id, module_id, permission_id, portal_id])
}

model peraturan {
  id                 BigInt              @id @default(autoincrement())
  nama               String              @db.VarChar(255)
  file               String              @db.VarChar(255)
  kategori_id        BigInt?
  created_at         DateTime?           @default(now()) @db.Timestamp(0)
  updated_at         DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?           @db.Timestamp(0)
  peraturan_kategori peraturan_kategori? @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "peraturan_kategori_id_foreign")
}

model peraturan_kategori {
  id         BigInt      @id @default(autoincrement())
  nama       String      @db.VarChar(255)
  created_at DateTime?   @default(now()) @db.Timestamp(0)
  updated_at DateTime?   @updatedAt @db.Timestamp(0)
  deleted_at DateTime?   @db.Timestamp(0)
  peraturan  peraturan[]
}

model peraturan_polri {
  id             BigInt    @id @default(autoincrement())
  judul          String    @db.VarChar
  no_surat       String    @db.VarChar(100)
  bentuk         String    @db.VarChar
  bentuk_singkat String    @db.VarChar(150)
  created_at     DateTime  @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @updatedAt @db.Timestamp(0)
  deleted_at     DateTime? @db.Timestamp(0)

  peraturan_polri_file peraturan_polri_file[]
}

model peraturan_polri_file {
  id                 BigInt          @id @default(autoincrement())
  id_peraturan_polri BigInt
  originalname       String?
  encoding           String?
  mimetype           String?
  destination        String?
  filename           String?
  path               String?
  size               BigInt?
  key                String?
  url                String?
  tanggal_penetapan  DateTime        @db.Date
  tanggal_berlaku    DateTime        @db.Date
  created_at         DateTime        @default(now()) @db.Timestamp()
  updated_at         DateTime?       @updatedAt @db.Timestamp(0)
  deleted_at         DateTime?       @db.Timestamp(0)
  peraturan_polri    peraturan_polri @relation(fields: [id_peraturan_polri], references: [id])
}

model personel {
  id                                                BigInt                                     @id @default(autoincrement())
  uid                                               String                                     @db.Uuid
  nrp                                               String                                     @db.VarChar(255)
  nama_lengkap                                      String?                                    @db.VarChar(255)
  tanggal_lahir                                     DateTime?                                  @db.Date
  jenis_kelamin                                     gender_enum
  foto_file                                         String?                                    @db.VarChar(255)
  agama_id                                          BigInt?
  ktp_nomor                                         String?                                    @db.VarChar(255)
  ktp_file                                          String?                                    @db.VarChar(255)
  kk_nomor                                          String?                                    @db.VarChar(255)
  kk_file                                           String?                                    @db.VarChar(255)
  status_kawin_id                                   BigInt?
  golongan_darah                                    String?                                    @db.VarChar(255)
  email                                             String?                                    @db.VarChar(255)
  no_hp                                             String?                                    @db.VarChar(255)
  akta_kelahiran_file                               String?                                    @db.VarChar(255)
  asabri_nomor                                      String?                                    @db.VarChar(255)
  asabri_file                                       String?                                    @db.VarChar(255)
  bpjs_nomor                                        String?                                    @db.VarChar(255)
  bpjs_file                                         String?                                    @db.VarChar(255)
  paspor_nomor                                      String?                                    @db.VarChar(255)
  paspor_file                                       String?                                    @db.VarChar(255)
  npwp_nomor                                        String?                                    @db.VarChar(255)
  npwp_file                                         String?                                    @db.VarChar(255)
  lhkpn_file                                        String?                                    @db.VarChar(255)
  masa_dinas_surut_tmt                              DateTime?                                  @db.Date
  masa_dinas_surut_file                             String?                                    @db.VarChar(255)
  anak_ke                                           Int?
  jumlah_saudara                                    Int?
  status_aktif_id                                   BigInt?
  created_at                                        DateTime?                                  @default(now()) @db.Timestamp(0)
  updated_at                                        DateTime?                                  @updatedAt @db.Timestamp(0)
  deleted_at                                        DateTime?                                  @db.Timestamp(0)
  suku_id                                           BigInt?
  tempat_lahir                                      String?
  suku                                              suku?                                      @relation(fields: [suku_id], references: [id], map: "personel_suku_id_foreign")
  alamat                                            alamat[]
  assesment_personel                                assessment_personel[]
  bagpsikologi                                      bagpsikologi[]
  bagrimdik_peserta_rekrutmen_pppk                  bagrimdik_peserta_rekrutmen_pppk[]
  brevet_personel                                   brevet_personel[]
  diklat_personel                                   diklat_personel[]
  jabatan_personel                                  jabatan_personel[]
  kgb_personel                                      kgb_personel[]
  konsultasi                                        konsultasi[]
  konsultasi_konselor                               konsultasi_konselor?
  konsultasi_personel                               konsultasi_personel[]
  lama_dikbangspes_personel                         lama_dikbangspes_personel[]
  lama_mddn_personel                                lama_mddn_personel[]
  lama_mddp_personel                                lama_mddp_personel[]
  misi_personel                                     misi_personel[]
  mutasi_jabatan                                    mutasi_jabatan[]
  olahraga_personel                                 olahraga_personel[]
  pangkat_personel                                  pangkat_personel[]
  pelatihan_personel                                pelatihan_personel[]
  pengajuan_cuti_pengampu_pengajuan_cuti            pengajuan_cuti[]                           @relation("pengajuan_cuti_pengampu_to_pengajuan_cuti")
  pengajuan_cuti_personel                           pengajuan_cuti[]                           @relation("personel_to_personel")
  pengajuan_pengakhiran_dinas_bup_personel          pengajuan_pengakhiran_dinas_bup[]          @relation("personel_to_personel")
  pengajuan_pengakhiran_dinas_bup_personel_diterima pengajuan_pengakhiran_dinas_bup[]          @relation("personel_diterima_to_personel")
  penugasan_personel                                penugasan_personel[]
  agama                                             agama?                                     @relation(fields: [agama_id], references: [id], onDelete: Cascade, map: "personel_agama_id_foreign")
  status_aktif                                      status_aktif?                              @relation(fields: [status_aktif_id], references: [id], onDelete: Cascade, map: "personel_status_aktif_id_foreign")
  personel_sertifikasi                              personel_sertifikasi[]
  prestasi_olahraga_personel                        prestasi_olahraga_personel[]
  reset_password                                    reset_password[]
  score_dikbangspes_personel                        score_dikbangspes_personel[]
  score_lama_dinas_personel                         score_lama_dinas_personel[]
  score_lama_dinas_personel_kawaka                  score_lama_dinas_personel_kawaka[]
  score_lama_dinas_personel_kawaka_fungsi           score_lama_dinas_personel_kawaka_fungsi[]
  score_mddp_personel                               score_mddp_personel[]
  score_nieve_personel                              score_nieve_personel[]
  score_nilai_smk_personel                          score_nilai_smk_personel[]
  score_penghargaan_personel                        score_penghargaan_personel[]
  score_rank_tahun_dikpol                           score_rank_tahun_dikpol[]
  seleksi_baglekdik_selection_participants          seleksi_baglekdik_selection_participants[]
  seleksi_bagrimdik_pns_peserta_created_by_personel seleksi_bagrimdik_pns_peserta[]            @relation("created_by_personel_to_personel")
  seleksi_bagrimdik_pns_peserta_personel            seleksi_bagrimdik_pns_peserta[]            @relation("personel_to_personel")
  seleksi_bagrimdik_pns_tahap_value                 seleksi_bagrimdik_pns_tahap_value[]
  sipk_penilaian                                    sipk_penilaian[]
  sipk_penilaian_rekan                              sipk_penilaian_rekan[]
  sipk_personel                                     sipk_personel[]
  sipp_jabatan_satuan_terakhir_personel             sipp_jabatan_satuan_terakhir_personel?     @relation("personel_to_personel")
  survey_answers                                    survey_answers[]
  tanhor_personel                                   tanhor_personel[]
  users                                             users?
  wofklow                                           workflow[]
  workflow_approval_log                             workflow_approval_log[]
  workflow_personel                                 workflow_personel[]
  workflow_transaction                              workflow_transaction[]
  workflow_transaction_personel                     workflow_transaction_personel[]
  workflow_validation_log                           workflow_validation_log[]
  workflow_archive_letter                           workflow_archive_letter[]
  workflow_letter_report                            workflow_letter_report[]
  survey_question                                   survey_question[]
  dikum_personel                                    dikum_personel[]
  rikkesla_personel                                 rikkesla_personel[]
  jasmani_personel                                  jasmani_personel[]
  rohani_personel                                   rohani_personel[]
  psikologi_personel                                psikologi_personel[]
  keluarga_personel                                 keluarga_personel[]
  personel_fisik                                    personel_fisik?
  status_kawin                                      status_kawin?                              @relation(fields: [status_kawin_id], references: [id])
  dikbangspes_personel                              dikbangspes_personel[]
  dikbangum_personel                                dikbangum_personel[]
  bko_personel                                      bko_personel[]
  penghargaan_personel                              penghargaan_personel[]
  bahasa_personel                                   bahasa_personel[]
  hobi_personel                                     hobi_personel[]
  kgb                                               kgb[]
  bagassus_personel                                 bagassus_personel[]
  bagassus_deposit                                  bagassus_deposit[]
  diktuk_personel                                   diktuk_personel[]
  pengajuan_penghargaan                             pengajuan_penghargaan[]
  pengajuan_tanhor                                  pengajuan_tanhor[]
  pengajuan_pengakhiran_dinas_personel              pengajuan_pengakhiran_dinas[]              @relation("personel_to_personel")
  beasiswa_dikum_dinas                              beasiswa_dikum_dinas[]
  pengajuan_tanhor_personel                         pengajuan_tanhor[]                         @relation("personel_to_personel")
  pengajuan_ncr                                     pengajuan_ncr[]
  promosi_jabatan_selection_participants            promosi_jabatan_selection_participants[]   @relation(name: "promosi_jabatan_selection_participants_personel_id_fk")

  ekta                       ekta[]
  e_kta                      e_kta[]
  mv_gelar                   mv_gelar?                   @relation(map: "mv_gelar_personel_id_foreign")
  mv_jabatan_terakhir        mv_jabatan_terakhir[]       @relation(map: "mv_jabatan_terakhir_personel_id_foreign")
  mv_nivellering_terakhir    mv_nivellering_terakhir[]   @relation(map: "mv_nivellering_terakhir_personel_id_foreign")
  mv_latest_jabatan_personel mv_latest_jabatan_personel? @relation(map: "mv_latest_jabatan_personel_personel_id_foreign")
  mv_pangkat_terakhir        mv_pangkat_terakhir?        @relation(map: "mv_pangkat_terakhir_personel_id_foreign")
  mv_mdp                     mv_mdp[]
  mv_mddp                    mv_mddp[]
  mv_lama_dinas              mv_lama_dinas[]             @relation("personel_to_mv_lama_dinas")
  mv_kgb_terakhir            mv_kgb_terakhir?            @relation(map: "mv_kgb_terkahir_personel_id_foreign")

  mv_personel_pangkat_jabatan_terakhir                 mv_personel_pangkat_jabatan_terakhir[]                 @relation("mv_personel_pangkat_jabatan_terakhir_personel_to_personel")
  mv_personel_pangkat_no_gaps                          mv_personel_pangkat_no_gaps[]                          @relation("mv_personel_pangkat_no_gaps_personel_to_personel")
  mv_personel_score_compilation_complete               mv_personel_score_compilation_complete[]               @relation("mv_personel_score_compilation_complete_personel_to_personel")
  mv_personel_jabatan_no_gaps                          mv_personel_jabatan_no_gaps[]                          @relation("mv_personel_jabatan_no_gaps_personel_to_personel")
  mv_personel_score_compilation_complete_kawaka        mv_personel_score_compilation_complete_kawaka[]        @relation("mv_personel_score_compilation_complete_kawaka_personel_to_personel")
  mv_personel_score_compilation_complete_kawaka_fungsi mv_personel_score_compilation_complete_kawaka_fungsi[] @relation("mv_personel_score_compilation_complete_kawaka_fungsi_personel_to_personel")
  mv_polisi                                            mv_polisi[]                                            @relation("mv_polisi_personel_to_personel")
  mv_ranked_jabatan                                    mv_ranked_jabatan[]                                    @relation("mv_ranked_jabatan_personel_to_personel")
  mv_ranked_pangkat                                    mv_ranked_pangkat[]                                    @relation("mv_ranked_pangkat_personel_to_personel")
  seleksi_bagrimdik_peserta                            seleksi_bagrimdik_peserta[]
  personel_kemampuan_bahasa                            personel_kemampuan_bahasa[]                            @relation("personel_to_personel")
  bagassus_seleksi_personel                            bagassus_seleksi_personel[]
  workflow_transaction_created_by                      workflow_transaction[]                                 @relation("created_by_to_personel")
  personel_sign                                        personel_sign[]
  kabupaten                                            kabupaten[]

  selection_bagassus_participant  selection_bagassus_participant[]
  selection_baglekdik_participant selection_baglekdik_participant[]
  selection_bagrimdik_participant selection_bagrimdik_participant[]

  @@unique([nrp, uid])
  @@index([created_at])
  @@index([nama_lengkap])
  @@index([nrp])
}

model personel_fisik {
  id           BigInt    @id @default(autoincrement())
  tinggi_badan Int?
  berat_badan  Int?
  warna_kulit  String?   @db.VarChar(255)
  jenis_rambut String?   @db.VarChar(255)
  warna_rambut String?   @db.VarChar(255)
  warna_mata   String?   @db.VarChar(255)
  sidik_jari_1 String?   @db.VarChar(255)
  sidik_jari_2 String?   @db.VarChar(255)
  personel_id  BigInt    @unique
  created_at   DateTime? @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @db.Timestamp(0)
  deleted_at   DateTime? @db.Timestamp(0)
  personel     personel  @relation(fields: [personel_id], references: [id])
}

model personel_sertifikasi {
  id          BigInt    @id @default(autoincrement())
  tingkat     String?   @db.VarChar(150)
  cabang      String?   @db.VarChar(255)
  tanggal     DateTime? @db.Date
  personel_id BigInt?
  created_at  DateTime? @default(now()) @db.Timestamp(6)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  personel    personel? @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_personel")
}

model portal {
  id          Int           @id @default(autoincrement())
  nama        String        @db.VarChar(255)
  created_at  DateTime      @default(now()) @db.Timestamp(0)
  updated_at  DateTime      @updatedAt @db.Timestamp(0)
  deleted_at  DateTime?     @db.Timestamp(0)
  desc        String?       @db.VarChar(255)
  role_access role_access[]
}

model prestasi_olahraga_personel {
  id                BigInt    @id @default(autoincrement())
  personel_id       BigInt?
  nrp               String?   @db.VarChar(255)
  nama              String?   @db.VarChar(255)
  jenis_kelamin     String?   @db.VarChar(255)
  kesatuan          String?   @db.VarChar(255)
  jabatan_id        BigInt?
  jabatan           String?   @db.VarChar(255)
  tingkat_satuan_id BigInt?
  tingkat_satuan    String?   @db.VarChar(255)
  cabor_id          BigInt?
  cabor             String?   @db.VarChar(255)
  tingkat_kejuaraan String?   @db.VarChar(255)
  tahun             Int?
  status_aktif      String?   @db.VarChar(255)
  jenis_peserta     String?   @db.VarChar(255)
  keterangan        String?   @db.VarChar(255)
  created_at        DateTime? @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)
  deleted_at        DateTime? @db.Timestamp(0)
  cabor_            cabor?    @relation(fields: [cabor_id], references: [id], onDelete: Cascade, map: "prestasi_olahraga_personel_cabor_id_foreign")
  personel          personel? @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "prestasi_olahraga_personel_personel_id_foreign")
  satuan            satuan?   @relation(fields: [tingkat_satuan_id], references: [id], onDelete: Cascade, map: "prestasi_olahraga_personel_satuan_id_foreign")
}

model promosi_jabatan_selections {
  id           BigInt    @id @default(autoincrement()) @db.BigInt
  title        String    @db.VarChar(200)
  description  String?   @db.Text
  total_stages Int       @db.Integer
  banner_id    BigInt?   @unique @db.BigInt
  created_by   BigInt    @db.BigInt
  created_at   DateTime  @default(now()) @db.Timestamp(0)
  updated_by   BigInt?   @db.BigInt
  updated_at   DateTime? @updatedAt @db.Timestamp(0)
  deleted_by   BigInt?   @db.BigInt
  deleted_at   DateTime? @db.Timestamp(0)

  created_by_user                             users                                         @relation(fields: [created_by], references: [id], name: "promosi_jabatan_selections_created_by_fk")
  promosi_jabatan_files_banner_selection      promosi_jabatan_files?                        @relation(fields: [banner_id], references: [id], name: "promosi_jabatan_selections_banner_id_fk")
  promosi_jabatan_selection_stages            promosi_jabatan_selection_stages[]            @relation(name: "promosi_jabatan_selection_stages_selection_id_fk")
  promosi_jabatan_selection_participants      promosi_jabatan_selection_participants[]      @relation(name: "promosi_jabatan_selection_participants_selection_id_fk")
  promosi_jabatan_selection_requirements      promosi_jabatan_selection_requirements[]      @relation(name: "promosi_jabatan_selection_requirements_selection_id_fk")
  promosi_jabatan_selection_requirement_files promosi_jabatan_selection_requirement_files[] @relation(name: "promosi_jabatan_selection_requirement_files_selection_id_fk")
  usersId                                     BigInt?
}

model promosi_jabatan_selection_stages {
  id           BigInt                            @id @default(autoincrement()) @db.BigInt
  stage        Int                               @db.Integer
  name         String                            @db.VarChar(200)
  location     String?                           @db.Text
  selection_id BigInt                            @db.BigInt
  banner_id    BigInt?                           @unique @db.BigInt
  status       promosi_jabatan_stage_status_enum
  start_date   DateTime                          @db.Date
  end_date     DateTime                          @db.Date
  updated_by   BigInt?                           @db.BigInt
  updated_at   DateTime?                         @updatedAt @db.Timestamp(0)

  selection                              promosi_jabatan_selections               @relation(fields: [selection_id], references: [id], name: "promosi_jabatan_selection_stages_selection_id_fk")
  promosi_jabatan_files_banner_stage     promosi_jabatan_files?                   @relation(fields: [banner_id], references: [id], name: "promosi_jabatan_selection_stages_banner_id_fk")
  promosi_jabatan_selection_participants promosi_jabatan_selection_participants[] @relation(name: "promosi_jabatan_selection_participants_current_stage_id_fk")
}

model promosi_jabatan_selection_participants {
  id                  BigInt  @id @default(autoincrement()) @db.BigInt
  registration_number String  @db.VarChar
  exam_number         String? @db.VarChar
  personel_id         BigInt  @db.BigInt
  selection_id        BigInt  @db.BigInt
  current_stage_id    BigInt  @db.BigInt
  history_stages      Json    @db.Json
  file_uploads        Json?   @db.Json

  personel                         personel                         @relation(fields: [personel_id], references: [id], name: "promosi_jabatan_selection_participants_personel_id_fk")
  promosi_jabatan_selections       promosi_jabatan_selections       @relation(fields: [selection_id], references: [id], name: "promosi_jabatan_selection_participants_selection_id_fk")
  promosi_jabatan_selection_stages promosi_jabatan_selection_stages @relation(fields: [current_stage_id], references: [id], name: "promosi_jabatan_selection_participants_current_stage_id_fk")
}

model promosi_jabatan_selection_requirements {
  id              BigInt               @id @default(autoincrement()) @db.BigInt
  selection_id    BigInt               @db.BigInt
  requirement_id  BigInt               @db.BigInt
  is_required     Boolean              @default(false) @db.Boolean
  value           String               @db.Text
  comparison_type comparison_type_enum
  created_at      DateTime             @default(now())
  updated_at      DateTime?            @updatedAt

  promosi_jabatan_selections   promosi_jabatan_selections   @relation(fields: [selection_id], references: [id], name: "promosi_jabatan_selection_requirements_selection_id_fk")
  promosi_jabatan_requirements promosi_jabatan_requirements @relation(fields: [requirement_id], references: [id], name: "promosi_jabatan_selection_requirements_requirement_id_fk")
}

model promosi_jabatan_selection_requirement_files {
  id           BigInt    @id @default(autoincrement()) @db.BigInt
  selection_id BigInt    @db.BigInt
  is_required  Boolean   @default(false) @db.Boolean
  title        String    @db.VarChar()
  extensions   Json?     @db.Json
  max_size     Int?      @db.Integer
  min_size     Int?      @db.Integer
  created_at   DateTime  @default(now())
  updated_at   DateTime? @updatedAt

  promosi_jabatan_selections promosi_jabatan_selections @relation(fields: [selection_id], references: [id], name: "promosi_jabatan_selection_requirement_files_selection_id_fk")
}

model promosi_jabatan_files {
  id            BigInt    @id(map: "promosi_jabatan_files_pk") @default(autoincrement())
  original_name String    @db.VarChar
  encoding      String    @db.VarChar
  mime_type     String    @db.VarChar
  file_name     String?   @db.VarChar
  size          Int?
  key           String?   @db.VarChar
  url           String?   @db.VarChar
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @updatedAt @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)

  promosi_jabatan_selections       promosi_jabatan_selections[]       @relation(name: "promosi_jabatan_selections_banner_id_fk")
  promosi_jabatan_selection_stages promosi_jabatan_selection_stages[] @relation(name: "promosi_jabatan_selection_stages_banner_id_fk")
}

model promosi_jabatan_requirements {
  id            BigInt          @id @default(autoincrement()) @db.BigInt
  name          String          @db.VarChar()
  input_type    input_type_enum
  url_data      String?         @db.VarChar()
  table_foreign String?         @db.VarChar()
  created_by    BigInt          @db.BigInt
  created_at    DateTime        @default(now())
  updated_by    BigInt?         @db.BigInt
  updated_at    DateTime?       @updatedAt @map("updated_at")

  promosi_jabatan_selection_requirement promosi_jabatan_selection_requirements[] @relation(name: "promosi_jabatan_selection_requirements_requirement_id_fk")
}

model provinsi {
  id                                              BigInt                     @id @default(autoincrement())
  nama                                            String                     @db.VarChar(255)
  negara_id                                       BigInt
  created_at                                      DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at                                      DateTime?                  @updatedAt @db.Timestamp(0)
  deleted_at                                      DateTime?                  @db.Timestamp(0)
  negara                                          negara                     @relation(fields: [negara_id], references: [id], onDelete: Cascade, map: "provinsi_negara_id_foreign")
  siswa_alamat                                    siswa_alamat[]
  siswa_riwayat_pendidikan_prov_perguruan_tnggi_2 siswa_riwayat_pendidikan[] @relation("prov_perguruan_tinggi_2_to_provinsi")
  siswa_riwayat_pendidikan_prov_perguruan_tinggi  siswa_riwayat_pendidikan[] @relation("prov_perguruan_tinggi_to_provinsi")
  siswa_riwayat_pendidikan_prov_sd                siswa_riwayat_pendidikan[] @relation("prov_sd_to_provinsi")
  siswa_riwayat_pendidikan_prov_sma               siswa_riwayat_pendidikan[] @relation("prov_sma_to_provinsi")
  siswa_riwayat_pendidikan_prov_smp               siswa_riwayat_pendidikan[] @relation("prov_smp_to_provinsi")
  data_dikum_siswa                                data_dikum_siswa[]
  data_alamat_siswa                               data_alamat_siswa[]
}

model psikologi_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  tahun       Int       @db.SmallInt
  semester    Int       @db.SmallInt
  nilai       Float
  nilai_file  String?   @db.VarChar(255)
  keterangan  String    @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "psikologi_personel_personel_id_foreign")
}

model reset_password {
  id          BigInt    @id(map: "reset_password_pk") @default(autoincrement())
  kode_unique String    @unique(map: "reset_password_unique") @db.VarChar(100)
  personel_id BigInt    @unique(map: "reset_password_unique_1")
  created_at  DateTime? @default(now()) @db.Timestamp(6)
  expired_at  DateTime  @db.Timestamp(6)
  is_aktif    Boolean?  @default(true)
  personel    personel  @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "reset_password_personel_fk")
}

model rikkesla_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  tahun       Int?      @db.SmallInt
  semester    Int?      @db.SmallInt
  nilai       Float
  nilai_file  String    @db.VarChar(255)
  keterangan  String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  personel    personel  @relation(fields: [personel_id], references: [id], map: "rikkesla_personel_personel_id_foreign")
}

model rohani_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  tahun       Int       @db.SmallInt
  semester    Int       @db.SmallInt
  nilai       Float
  nilai_file  String?   @db.VarChar(255)
  keterangan  String    @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  personel    personel  @relation(fields: [personel_id], references: [id], map: "rohani_personel_personel_id_foreign")
}

model role {
  id               Int               @id @default(autoincrement())
  nama             String
  bagian_id        Int?
  is_admin         Boolean?
  created_at       DateTime          @default(now()) @db.Timestamp(0)
  updated_at       DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at       DateTime?         @db.Timestamp(0)
  satuan_id        BigInt?
  role_tipe_id     Int?
  atasan_id        Int?
  level_id         Int?
  is_fungsi        Boolean?
  atasan_role      role?             @relation("atasan_role_to_role", fields: [atasan_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "role_atasan_fk")
  role_atasan_role role[]            @relation("atasan_role_to_role")
  bagian           bagian?           @relation(fields: [bagian_id], references: [id], onDelete: Restrict)
  level            level?            @relation(fields: [level_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "role_level_fk")
  satuan           satuan?           @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  role_tipe        role_tipe?        @relation(fields: [role_tipe_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "role_tipe_id_fkey")
  role_access      role_access[]
  role_module      role_module[]
  role_permission  role_permission[]
  users_role       users_role[]
}

model satker_patma {
  id                         Int       @id @default(autoincrement())
  nama_satker                String?   @db.VarChar(255)
  satker_id                  BigInt?
  sub_satker                 String?   @db.VarChar(255)
  sub_satker_id              BigInt?
  created_at                 DateTime? @default(now()) @db.Timestamp(6)
  satker_patma_satker_id     satuan?   @relation("satker_id_to_satuan", fields: [satker_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  satker_patma_sub_satker_id satuan?   @relation("sub_satker_id_to_satuan", fields: [sub_satker_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model satuan {
  id                                                   BigInt                                  @id @default(autoincrement())
  nama                                                 String                                  @db.VarChar(255)
  alamat                                               String?
  jenis_id                                             BigInt
  is_aktif                                             Boolean
  atasan_id                                            BigInt?
  created_at                                           DateTime?                               @default(now()) @db.Timestamp(0)
  updated_at                                           DateTime?                               @updatedAt @db.Timestamp(0)
  deleted_at                                           DateTime?                               @db.Timestamp(0)
  deleted_by                                           BigInt?
  bagpsikologi                                         bagpsikologi[]
  berita                                               berita[]
  bko_personel                                         bko_personel[]
  fungsi_satuan                                        fungsi_satuan[]
  kerjasama                                            kerjasama[]
  konsultasi                                           konsultasi[]
  e_kta_surat                                          e_kta_surat[]
  ekta_batch                                           ekta_batch[]
  mutasi_jabatan_satuan_baru                           mutasi_jabatan[]                        @relation("satuan_baru_to_mutasi_jabatan")
  mutasi_jabatan_satuan_lama                           mutasi_jabatan[]                        @relation("satuan_lama_to_mutasi_jabatan")
  p3k                                                  p3k[]
  pengajuan_ncr                                        pengajuan_ncr[]
  prestasi_olahraga_personel                           prestasi_olahraga_personel[]
  role                                                 role[]
  satker_patma_satker_id                               satker_patma[]                          @relation("satker_id_to_satuan")
  satker_patma_sub_satker_id                           satker_patma[]                          @relation("sub_satker_id_to_satuan")
  satuan_jenis                                         satuan_jenis                            @relation(fields: [jenis_id], references: [id], onDelete: Cascade, map: "satuan_jenis_id_foreign")
  atasan_satuan                                        satuan?                                 @relation("atasan_satuan_to_satuan", fields: [atasan_id], references: [id], map: "satuan_atasan_id_foreign")
  satuan_atasan_satuan                                 satuan[]                                @relation("atasan_satuan_to_satuan")
  users                                                users?                                  @relation(fields: [deleted_by], references: [id], onDelete: Cascade, map: "satuan_user_id_foreign")
  satuan_provinsi                                      satuan_provinsi[]
  sipp_jabatan_satuan_terakhir_personel_satuan         sipp_jabatan_satuan_terakhir_personel[] @relation("satuan_to_satuan")
  sipp_jabatan_satuan_terakhir_personel_satuan_dibawah sipp_jabatan_satuan_terakhir_personel[] @relation("satuan_dibawah_to_satuan")
  siswa_asal_rim_polda                                 siswa[]                                 @relation("siswa_asal_rim_polda_to_satuan")
  siswa_asal_rim_polres                                siswa[]                                 @relation("siswa_asal_rim_polres_to_satuan")
  siswa_asal_rim_polsek                                siswa[]                                 @relation("siswa_asal_rim_polsek_to_satuan")
  siswa_keswa_angket_satuan_angket_1                   siswa_keswa_angket?                     @relation("satuan_angket_1_to_satuan")
  siswa_keswa_angket_satuan_angket_2                   siswa_keswa_angket?                     @relation("satuan_angket2_to_satuan")
  siswa_keswa_angket_satuan_angket_3                   siswa_keswa_angket?                     @relation("satuan_angket3_to_satuan")
  siswa_keswa_angket_satuan_angket_4                   siswa_keswa_angket?                     @relation("satuan_angket4_to_satuan")
  siswa_keswa_angket_satuan_angket_5                   siswa_keswa_angket?                     @relation("satuan_angket5_to_satuan")
  survey_destination                                   survey_destination[]
  workflow_flow                                        workflow_flow[]
  workflow_transaction_flow                            workflow_transaction_flow[]
  jabatan                                              jabatan[]

  siswa_patma siswa_patma[]

  mv_latest_jabatan_personel                   mv_latest_jabatan_personel[]            @relation("satuan_to_satuan")
  mv_satuan_with_top_parents_atasan            mv_satuan_with_top_parents[]            @relation("atasan_to_mv_satuan_with_top_parents")
  mv_satuan_with_top_parents_second_top_parent mv_satuan_with_top_parents[]            @relation("second_top_parent_to_mv_satuan_with_top_parents")
  mv_satuan_with_top_parents_self              mv_satuan_with_top_parents?             @relation("self_to_mv_satuan_with_top_parents")
  mv_satuan_with_top_parents_third_top_parent  mv_satuan_with_top_parents[]            @relation("third_top_parent_to_mv_satuan_with_top_parents")
  mv_satuan_with_top_parents_top_parent        mv_satuan_with_top_parents[]            @relation("top_parent_to_mv_satuan_with_top_parents")
  mv_statistik_satker                          mv_statistik_satker[]
  mv_jabatan_terakhir                          mv_jabatan_terakhir[]                   @relation(map: "mv_jabatan_terakhir_satuan_id_foreign")
  sipp_jabatan_satuan_terakhir_personel        sipp_jabatan_satuan_terakhir_personel[]
  mv_personel                                  mv_personel[]                           @relation("mv_personel_satuan_to_satuan")

  mv_personel_pangkat_jabatan_terakhir mv_personel_pangkat_jabatan_terakhir[] @relation("mv_personel_pangkat_jabatan_terakhir_satuan_to_satuan")

  mv_statistik_satker_old mv_statistik_satker_old[] @relation("mv_statistik_satker_old_second_top_parent_to_satuan")

  mv_satuan_hirarki_satuan mv_satuan_hirarki[] @relation("mv_satuan_hirarki_satuan_to_satuan")

  mv_satuan_hirarki_atasan mv_satuan_hirarki[] @relation("mv_satuan_hirarki_atasan_to_satuan")

  satuanFullname    satuan_fullname?    @relation("SatuanToSatuanFullname")
  mv_satuanFullname mv_satuan_fullname? @relation("MVSatuanToSatuanFullname")
  satuan_bank       satuan_bank[]
  asal_rim_polda    data_siswa[]        @relation("data_siswa_satuan_id_fk")
  asal_rim_polres   data_siswa[]        @relation("data_siswa_satuan_id_fk_2")
  fungsi            fungsi[]            @relation("fungsi_satuan_teratas")
}

model satuan_fullname {
  child_id        BigInt  @id
  satuan_fullname String?

  satuan satuan @relation("SatuanToSatuanFullname", fields: [child_id], references: [id])
}

model mv_satuan_fullname {
  child_id        BigInt  @id
  satuan_fullname String?

  satuan satuan @relation("MVSatuanToSatuanFullname", fields: [child_id], references: [id])
}

model satuan_hirarki {
  satuan_id Int?
  atasan_id Int[]
  id        BigInt @id(map: "satuan_hirarki_pk") @default(autoincrement())
}

model satuan_jenis {
  id                    BigInt                  @id @default(autoincrement())
  nama                  String                  @db.VarChar(255)
  created_at            DateTime?               @default(now()) @db.Timestamp(0)
  updated_at            DateTime?               @updatedAt @db.Timestamp(0)
  deleted_at            DateTime?               @db.Timestamp(0)
  import_satuan_jabatan import_satuan_jabatan[]
  satuan                satuan[]
}

model satuan_provinsi {
  id                Int     @id @default(autoincrement())
  satuan_id         BigInt
  nama_satuan       String  @db.VarChar(255)
  nama_atasan       String? @db.VarChar(255)
  nama_atasan_polda String? @db.VarChar(255)
  provinsi          String? @db.VarChar(255)
  satuan            satuan  @relation(fields: [satuan_id], references: [id], onDelete: Cascade, map: "satuan_provinsi_satuan_id_foreign")
}

model score_dikbangspes_personel {
  id                     Int                 @id @default(autoincrement())
  personel_id            BigInt
  dikbangspes_tingkat_id BigInt
  lama_dikbangspes_hari  Int
  score_dikbangspes      Float?
  created_at             DateTime            @default(now()) @db.Timestamp(6)
  updated_at             DateTime            @updatedAt @db.Timestamp(6)
  dikbangspes_tingkat    dikbangspes_tingkat @relation(fields: [dikbangspes_tingkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel               personel            @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_dikbangspes_personel_personel_id_foreign")

  @@index([personel_id, dikbangspes_tingkat_id], map: "idx_score_dikbangspes_personel_personel_dikbangspes_tingkat")
  @@index([personel_id], map: "idx_score_dikbangspes_personel_personel_id")
}

model score_lama_dinas_personel {
  id                  Int      @id @default(autoincrement())
  personel_id         BigInt
  jabatan             String?
  tmt_jabatan         DateTime @db.Timestamp(6)
  fungsi              String?
  tempat_dinas        String?  @db.VarChar
  score_lama_dinas    Float?
  lama_menjabat_bulan Int
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @updatedAt @db.Timestamp(6)
  personel            personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_lama_dinas_personel_personel_id_foreign")

  @@index([personel_id], map: "idx_score_lama_dinas_personel_personel_id")
}

model score_lama_dinas_personel_kawaka {
  id                  Int      @id @default(autoincrement())
  personel_id         BigInt
  jabatan             String?
  tmt_jabatan         DateTime @db.Timestamp(6)
  tempat_dinas        String?  @db.VarChar
  lama_menjabat_bulan Int
  score_lama_dinas    Float?
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @updatedAt @db.Timestamp(6)
  personel            personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_lama_dinas_personel_kawaka_personel_id_foreign")

  @@index([personel_id], map: "idx_score_lama_dinas_personel_kawaka_personel_id")
}

model score_lama_dinas_personel_kawaka_fungsi {
  id                  Int      @id @default(autoincrement())
  personel_id         BigInt
  jabatan             String?
  tmt_jabatan         DateTime @db.Timestamp(6)
  fungsi              String?
  tempat_dinas        String?  @db.VarChar
  score_lama_dinas    Float?
  lama_menjabat_bulan Int
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @updatedAt @db.Timestamp(6)
  personel            personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_lama_dinas_personel_kawaka_fungsi_personel_id_foreign")

  @@index([personel_id], map: "idx_score_lama_dinas_personel_kawaka_fungsi_personel_id")
}

model score_mddp_personel {
  id                  Int      @id @default(autoincrement())
  personel_id         BigInt
  score_mddp          Float?
  lama_menjabat_bulan Int
  created_at          DateTime @default(now()) @db.Timestamp(6)
  updated_at          DateTime @updatedAt @db.Timestamp(6)
  personel            personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_mddp_personel_personel_id_foreign")

  @@index([personel_id], map: "idx_personel_id")
  @@index([personel_id], map: "idx_score_mddp_personel_personel_id")
}

model score_nieve_personel {
  id                  Int         @id @default(autoincrement())
  personel_id         BigInt
  nivellering_id      BigInt
  lama_menjabat_bulan Int
  lama_menjabat_hari  Int
  score_nievelering   Int?
  created_at          DateTime    @default(now()) @db.Timestamp(6)
  updated_at          DateTime    @updatedAt @db.Timestamp(6)
  pangkat_id          BigInt?
  nivellering         nivellering @relation(fields: [nivellering_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  pangkat             pangkat?    @relation(fields: [pangkat_id], references: [id])
  personel            personel    @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_nieve_personel_personel_id_foreign")

  @@index([nivellering_id], map: "idx_score_nieve_personel_nivellering_id")
  @@index([personel_id, nivellering_id], map: "idx_score_nieve_personel_personel_nivellering")
}

model score_nilai_smk_personel {
  id              Int      @id @default(autoincrement())
  personel_id     BigInt
  tahun           Int?
  semester        Int
  nilai           Float?
  score_nilai_smk Float?
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @updatedAt @db.Timestamp(6)
  personel        personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([personel_id], map: "idx_score_nilai_smk_personel_personel_id")
}

model score_penghargaan_personel {
  id                 Int      @id @default(autoincrement())
  personel_id        BigInt
  level_penghargaan  String?  @db.VarChar
  jumlah_penghargaan Int
  score_penghargaan  Float?
  created_at         DateTime @default(now()) @db.Timestamp(6)
  updated_at         DateTime @updatedAt @db.Timestamp(6)
  personel           personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_penghargaan_personel_personel_id_foreign")

  @@index([personel_id], map: "idx_score_penghargaan_personel_personel_id")
}

model score_rank_tahun_dikpol {
  id                 Int        @id @default(autoincrement())
  personel_id        BigInt
  diktuk_id          BigInt?
  dikbangum_id       BigInt?
  diklat_tingkat     String?    @db.VarChar
  tanggal_masuk      DateTime   @db.Timestamp(6)
  tanggal_selesai    DateTime   @db.Timestamp(6)
  jumlah_siswa       Float?
  ranking            Float?
  lama_dikpol_bulan  Int
  score_tahun_dikpol Float?
  score_rank_dikpol  Float?
  created_at         DateTime   @default(now()) @db.Timestamp(6)
  updated_at         DateTime   @updatedAt @db.Timestamp(6)
  dikbangum          dikbangum? @relation(fields: [dikbangum_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_akpol_personel_dikbangum_id_foreign")
  diktuk             diktuk?    @relation(fields: [diktuk_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_akpol_personel_diktuk_id_foreign")
  personel           personel   @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "score_akpol_personel_personel_id_foreign")

  @@index([diktuk_id], map: "idx_diktuk_id")
  @@index([diktuk_id, dikbangum_id], map: "idx_personel_diktuk_dikbangum")
  @@index([diktuk_id], map: "idx_score_rank_tahun_dikpol_diktuk_id")
  @@index([diktuk_id, dikbangum_id], map: "idx_score_rank_tahun_dikpol_personel_diktuk_dikbangum")
  @@index([personel_id], map: "idx_score_rank_tahun_dikpol_personel_id")
}

model search_filter_siswa {
  id               Int       @id @default(autoincrement())
  category_name    String?   @db.VarChar(100)
  subcategory_name String?   @db.VarChar(100)
  filter_name      String    @db.VarChar(100)
  table_name       String?   @db.VarChar(255)
  column_name      String?   @db.VarChar(255)
  created_at       DateTime? @default(now()) @db.Timestamp(6)
}

model seleksi_baglekdik_files {
  id                               BigInt                         @id(map: "seleksi_baglekdik_files_pk") @default(autoincrement())
  original_name                    String                         @db.VarChar
  encoding                         String                         @db.VarChar
  mime_type                        String                         @db.VarChar
  destination                      String?                        @db.VarChar
  file_name                        String?                        @db.VarChar
  path                             String?                        @db.VarChar
  size                             Int?
  key                              String?                        @db.VarChar
  url                              String?                        @db.VarChar
  created_at                       DateTime?                      @default(now()) @db.Timestamp(0)
  updated_at                       DateTime?                      @updatedAt @db.Timestamp(0)
  deleted_at                       DateTime?                      @db.Timestamp(0)
  seleksi_baglekdik_files_document seleksi_baglekdik_selections[] @relation("document_to_seleksi_baglekdik_selections")
  seleksi_baglekdik_files_logo     seleksi_baglekdik_selections[] @relation("logo_to_seleksi_baglekdik_selections")
}

model seleksi_baglekdik_requirements {
  id                                      BigInt                                    @id(map: "seleksi_baglekdik_requirements_pk") @default(autoincrement())
  name                                    String                                    @db.VarChar
  input_type                              seleksi_baglekdik_input_type_enum
  created_at                              DateTime?                                 @default(now()) @db.Timestamp(0)
  updated_at                              DateTime?                                 @updatedAt @db.Timestamp(0)
  seleksi_baglekdik_selection_requirement seleksi_baglekdik_selection_requirement[]
}

model seleksi_baglekdik_selection_participants {
  id                                 BigInt                                    @id(map: "seleksi_baglekdik_selections_participants_pk") @default(autoincrement())
  registration_number                String                                    @db.VarChar
  exam_number                        String?                                   @db.VarChar
  personel_id                        BigInt
  stage_id                           BigInt
  selection_id                       BigInt
  file_uploads                       Json?                                     @db.Json
  status                             seleksi_baglekdik_participant_status_enum
  reason                             String?                                   @db.VarChar
  created_by                         BigInt?
  created_at                         DateTime?                                 @default(now()) @db.Timestamp(0)
  updated_by                         BigInt?
  updated_at                         DateTime?                                 @updatedAt @db.Timestamp(0)
  seleksi_baglekdik_selections       seleksi_baglekdik_selections?             @relation(fields: [selection_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selection_participants_seleksi_baglekdik_sele")
  personel                           personel?                                 @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_participants_personel_id_fk")
  seleksi_baglekdik_selection_stages seleksi_baglekdik_selection_stages?       @relation(fields: [stage_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_participants_stage_id_fk")
  created_by_users                   users?                                    @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_participants_users_fk")
}

model seleksi_baglekdik_selection_requirement {
  id                             BigInt                         @id(map: "seleksi_baglekdik_selection_requirement_pk") @default(autoincrement())
  selection_id                   BigInt
  requirement_id                 BigInt
  is_required                    Boolean?                       @default(false)
  min_value                      Int?
  max_value                      Int?
  value                          String?
  title                          String?                        @db.VarChar
  file_extension                 String?                        @db.VarChar
  created_at                     DateTime?                      @default(now()) @db.Timestamp(0)
  updated_at                     DateTime?                      @updatedAt @db.Timestamp(0)
  seleksi_baglekdik_requirements seleksi_baglekdik_requirements @relation(fields: [requirement_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selection_requirement_requirement_id_fk")
  seleksi_baglekdik_selections   seleksi_baglekdik_selections   @relation(fields: [selection_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selection_requirement_requirement_selection_i")
}

model seleksi_baglekdik_selection_stages {
  id                                       BigInt                                     @id(map: "seleksi_baglekdik_stages_pk") @default(autoincrement())
  stage                                    Int
  start_date                               DateTime                                   @db.Timestamp(0)
  end_date                                 DateTime                                   @db.Timestamp(0)
  created_by                               BigInt?
  created_at                               DateTime?                                  @default(now()) @db.Timestamp(0)
  updated_by                               BigInt?
  updated_at                               DateTime?                                  @updatedAt @db.Timestamp(0)
  selection_id                             BigInt
  status                                   seleksi_baglekdik_stage_status_enum
  seleksi_baglekdik_selection_participants seleksi_baglekdik_selection_participants[]
  seleksi_baglekdik_selections             seleksi_baglekdik_selections               @relation(fields: [selection_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_stages_seleksi_baglekdik_selections_fk")
  craeted_by_users                         users?                                     @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_stages_users_fk")
}

model seleksi_baglekdik_selections {
  id                                       BigInt                                     @id(map: "seleksi_baglekdik_selections_pk") @default(autoincrement())
  title                                    String                                     @db.VarChar
  description                              String?
  type                                     seleksi_baglekdik_type_enum
  selection_stages                         Int
  logo_file_id                             BigInt?
  document_file_id                         BigInt?
  created_by                               BigInt?
  created_at                               DateTime?                                  @default(now()) @db.Timestamp(0)
  updated_by                               BigInt?
  updated_at                               DateTime?                                  @updatedAt @db.Timestamp(0)
  deleted_at                               DateTime?                                  @db.Timestamp(0)
  deleted_by                               BigInt?
  seleksi_baglekdik_selection_participants seleksi_baglekdik_selection_participants[]
  seleksi_baglekdik_selection_requirement  seleksi_baglekdik_selection_requirement[]
  seleksi_baglekdik_selection_stages       seleksi_baglekdik_selection_stages[]
  seleksi_baglekdik_files_document         seleksi_baglekdik_files?                   @relation("document_to_seleksi_baglekdik_selections", fields: [document_file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_document_file_id_fk")
  seleksi_baglekdik_files_logo             seleksi_baglekdik_files?                   @relation("logo_to_seleksi_baglekdik_selections", fields: [logo_file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_logo_file_id_fk")
  created_by_users                         users?                                     @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_baglekdik_selections_users_fk")
}

model seleksi_bagrimdik {
  id                       BigInt                        @id(map: "seleksi_bagrimdik_pk") @default(autoincrement())
  judul                    String?                       @db.VarChar(255)
  deskripsi                String?                       @db.VarChar(255)
  jenis                    jenis_seleksi_bagrimdik_enum?
  startdate                DateTime?                     @db.Timestamp(0)
  enddate                  DateTime?                     @db.Timestamp(0)
  logo_file                String?                       @db.VarChar(255)
  dokumen_file             String?                       @db.VarChar(255)
  created_at               DateTime?                     @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                     @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?                     @db.Timestamp(0)
  tahap                    Int?
  persyaratan_list         Json                          @db.Json
  tahapan_seleksi          Int?
  seleksi_bagrimdik_syarat seleksi_bagrimdik_syarat[]
  seleksi_bagrimdik_tahap  seleksi_bagrimdik_tahap[]
}

model seleksi_bagrimdik_peserta {
  id                      BigInt                                     @id(map: "seleksi_bagrimdik_peserta_pk") @default(autoincrement())
  personel_id             BigInt
  no_pendaftaran          BigInt
  no_ujian                String?                                    @db.VarChar(255)
  tahap_id                BigInt?
  nama_peserta            String?                                    @db.VarChar(255)
  nrp                     BigInt?
  pangkat                 String?                                    @db.VarChar(255)
  jabatan                 String?                                    @db.VarChar(255)
  satuan                  String?                                    @db.VarChar(255)
  created_at              DateTime?                                  @default(now()) @db.Timestamp(0)
  updated_at              DateTime?                                  @updatedAt @db.Timestamp(0)
  deleted_at              DateTime?                                  @db.Timestamp(0)
  status_dokumen          status_dokumen_seleksi_bagrimdik_pns_enum?
  status                  status_seleksi_bagrimdik_pns_enum?
  personel                personel?                                  @relation(fields: [personel_id], references: [id])
  seleksi_bagrimdik_tahap seleksi_bagrimdik_tahap?                   @relation(fields: [tahap_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_bagrimdik_peserta_seleksi_bagrimdik_tahap_fk")
}

model seleksi_bagrimdik_pns {
  id                            BigInt                           @id @default(autoincrement())
  judul                         String                           @db.VarChar(255)
  deskripsi                     String                           @db.VarChar(255)
  jenis                         jenis_seleksi_bagrimdik_pns_enum
  logo_file                     String?                          @db.VarChar(255)
  dokumen_file                  String?                          @db.VarChar(255)
  pendaftaran_mulai             DateTime                         @db.Date
  pendaftaran_selesai           DateTime                         @db.Date
  persyaratan_list              Json
  sedang_tahap                  Int
  tahapan_seleksi               Int
  created_at                    DateTime                         @default(now()) @db.Timestamp(0)
  updated_at                    DateTime?                        @updatedAt @db.Timestamp(0)
  deleted_at                    DateTime?                        @db.Timestamp(0)
  seleksi_bagrimdik_pns_peserta seleksi_bagrimdik_pns_peserta[]
  seleksi_bagrimdik_pns_syarat  seleksi_bagrimdik_pns_syarat[]
  seleksi_bagrimdik_pns_tahap   seleksi_bagrimdik_pns_tahap[]
}

model seleksi_bagrimdik_pns_peserta {
  id                                                BigInt                                     @id @default(autoincrement())
  personel_id                                       BigInt
  no_pendaftaran                                    String                                     @db.VarChar(255)
  no_ujian                                          String?                                    @db.VarChar(255)
  status_dokumen                                    status_dokumen_seleksi_bagrimdik_pns_enum?
  status                                            status_seleksi_bagrimdik_pns_enum?
  created_at                                        DateTime                                   @default(now())
  updated_at                                        DateTime?
  deleted_at                                        DateTime?                                  @db.Timestamp(0)
  seleksi_bagrimdik_pns_id                          BigInt
  seleksi_bagrimdik_pns_tahap_id                    BigInt
  created_by                                        BigInt?
  seleksi_bagrimdik_pns_peserta_created_by_personel personel?                                  @relation("created_by_personel_to_personel", fields: [created_by], references: [id], onDelete: NoAction, map: "personel_created_by_id_on_pns_peserta")
  seleksi_bagrimdik_pns_peserta_personel            personel                                   @relation("personel_to_personel", fields: [personel_id], references: [id], onDelete: NoAction, map: "personel_on_pns_peserta")
  seleksi_bagrimdik_pns                             seleksi_bagrimdik_pns                      @relation(fields: [seleksi_bagrimdik_pns_id], references: [id], onDelete: NoAction, map: "seleksi_bagrimdik_pns_on_peserta")
  seleksi_bagrimdik_pns_tahap                       seleksi_bagrimdik_pns_tahap                @relation(fields: [seleksi_bagrimdik_pns_tahap_id], references: [id], onDelete: NoAction, map: "seleksi_bagrimdik_pns_tahap_on_peserta")
}

model seleksi_bagrimdik_pns_peserta_file {
  id                                BigInt                              @id @default(autoincrement())
  originalname                      String
  encoding                          String
  mimetype                          String
  destination                       String?
  filename                          String?
  path                              String?
  size                              Int?
  key                               String?
  url                               String?
  created_at                        DateTime                            @default(now()) @db.Timestamp(0)
  updated_at                        DateTime?                           @updatedAt @db.Timestamp(0)
  deleted_at                        DateTime?                           @db.Timestamp(0)
  seleksi_bagrimdik_pns_tahap_value seleksi_bagrimdik_pns_tahap_value[]
}

model seleksi_bagrimdik_pns_syarat {
  id                                        BigInt                                 @id @default(autoincrement())
  min_value                                 String                                 @db.VarChar(255)
  max_value                                 String?                                @db.VarChar(255)
  prioritas                                 Boolean                                @default(false)
  created_at                                DateTime                               @default(now()) @db.Timestamp(0)
  updated_at                                DateTime?                              @updatedAt @db.Timestamp(0)
  deleted_at                                DateTime?                              @db.Timestamp(0)
  seleksi_bagrimdik_pns_id                  BigInt
  seleksi_bagrimdik_pns_tipe_persyaratan_id BigInt
  seleksi_bagrimdik_pns                     seleksi_bagrimdik_pns                  @relation(fields: [seleksi_bagrimdik_pns_id], references: [id], onDelete: Cascade, map: "seleksi_bagrimdik_pns_on_syarat")
  seleksi_bagrimdik_pns_tipe_persyaratan    seleksi_bagrimdik_pns_tipe_persyaratan @relation(fields: [seleksi_bagrimdik_pns_tipe_persyaratan_id], references: [id], onDelete: NoAction, map: "seleksi_bagrimdik_pns_tipe_persyaratan_on_syarat")
}

model seleksi_bagrimdik_pns_tahap {
  id                                BigInt                              @id @default(autoincrement())
  tahap                             BigInt
  tahap_mulai                       DateTime?                           @db.Date
  tahap_selesai                     DateTime?                           @db.Date
  created_at                        DateTime?                           @default(now()) @db.Timestamp(0)
  updated_at                        DateTime?                           @updatedAt @db.Timestamp(0)
  deleted_at                        DateTime?                           @db.Timestamp(0)
  seleksi_bagrimdik_pns_id          BigInt
  seleksi_bagrimdik_pns_peserta     seleksi_bagrimdik_pns_peserta[]
  seleksi_bagrimdik_pns             seleksi_bagrimdik_pns               @relation(fields: [seleksi_bagrimdik_pns_id], references: [id], onDelete: Cascade, map: "seleksi_bagrimdik_pns_on_tahap")
  seleksi_bagrimdik_pns_tahap_value seleksi_bagrimdik_pns_tahap_value[]
}

model seleksi_bagrimdik_pns_tahap_value {
  id                                    BigInt                                                @id @default(autoincrement())
  value                                 String?                                               @db.VarChar(255)
  seleksi_bagrimdik_pns_peserta_file_id BigInt?
  status_dokumen                        status_dokumen_seleksi_bagrimdik_pns_enum?
  personel_id                           BigInt?
  seleksi_bagrimdik_pns_tahap_id        BigInt?
  type_file_dokumen                     type_file_document_upload_seleksi_bagrimdik_pns_enum?
  personel                              personel?                                             @relation(fields: [personel_id], references: [id], onDelete: NoAction, map: "personel_on_pns_tahap_value")
  seleksi_bagrimdik_pns_peserta_file    seleksi_bagrimdik_pns_peserta_file?                   @relation(fields: [seleksi_bagrimdik_pns_peserta_file_id], references: [id], onDelete: NoAction, map: "seleksi_bagrimdik_pns_peserta_file_on_tahap_value")
  seleksi_bagrimdik_pns_tahap           seleksi_bagrimdik_pns_tahap?                          @relation(fields: [seleksi_bagrimdik_pns_tahap_id], references: [id], onDelete: NoAction, map: "seleksi_bagrimdik_pns_tahap_on_pns_tahap_value")
}

model seleksi_bagrimdik_pns_tipe_persyaratan {
  id                           BigInt                                                @id @default(autoincrement())
  name                         String?                                               @db.VarChar(255)
  type                         type_persyaratan_seleksi_bagrimdik_pns_enum           @default(UMUR)
  type_input                   type_input_seleksi_bagrimdik_pns_enum                 @default(STRING)
  created_at                   DateTime                                              @default(now()) @db.Timestamp(0)
  updated_at                   DateTime?                                             @updatedAt @db.Timestamp(0)
  deleted_at                   DateTime?                                             @db.Timestamp(0)
  type_file                    type_file_document_upload_seleksi_bagrimdik_pns_enum?
  seleksi_bagrimdik_pns_syarat seleksi_bagrimdik_pns_syarat[]
}

model seleksi_bagrimdik_syarat {
  id                     BigInt              @id(map: "seleksi_bagrimdik_syarat_pk") @default(autoincrement())
  seleksi_bagrimdik_id   BigInt
  seleksi_persyaratan_id BigInt
  min_value              String              @db.VarChar(255)
  max_value              String?             @db.VarChar(255)
  prioritas              Boolean?            @default(false)
  created_at             DateTime?           @default(now()) @db.Timestamp(0)
  updated_at             DateTime?           @updatedAt @db.Timestamp(0)
  deleted_at             DateTime?           @db.Timestamp(0)
  seleksi_persyaratan    seleksi_persyaratan @relation(fields: [seleksi_persyaratan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_bagrimdik_syarat_fk")
  seleksi_bagrimdik      seleksi_bagrimdik   @relation(fields: [seleksi_bagrimdik_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_bagrimdik_syarat_fk_1")
}

model seleksi_bagrimdik_tahap {
  id                        BigInt                      @id(map: "seleksi_bagrimdik_tahap_pk") @default(autoincrement())
  seleksi_bagrimdik_id      BigInt
  startdate                 DateTime?                   @db.Timestamp(0)
  enddate                   DateTime?                   @db.Timestamp(0)
  created_at                DateTime?                   @default(now()) @db.Timestamp(0)
  updated_at                DateTime?                   @db.Timestamp(0)
  deleted_at                DateTime?                   @db.Timestamp(0)
  tahap                     BigInt
  seleksi_bagrimdik_peserta seleksi_bagrimdik_peserta[]
  seleksi_bagrimdik         seleksi_bagrimdik           @relation(fields: [seleksi_bagrimdik_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "seleksi_bagrimdik_tahap_fk")
}

model seleksi_persyaratan {
  id                       BigInt                     @id(map: "seleksi_persyaratan_pk") @default(autoincrement())
  name                     String?                    @db.VarChar(255)
  type                     String?                    @db.VarChar
  created_at               DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                  @updatedAt @db.Timestamp(0)
  deleted_at               DateTime?                  @db.Timestamp(0)
  seleksi_bagrimdik_syarat seleksi_bagrimdik_syarat[]
}

model sipk_hukuman {
  id            BigInt    @id @default(autoincrement())
  penilaian_id  BigInt
  jenis_hukuman String    @db.VarChar(100)
  no_kep        String    @db.VarChar(100)
  tgl_kep       DateTime  @db.Date
  uraian_kep    String
  poin          Int?
  created_at    DateTime? @default(now()) @db.Timestamp(6)
  updated_at    DateTime? @updatedAt @db.Timestamp(6)

  sipk_penilaian       sipk_penilaian         @relation(fields: [penilaian_id], references: [id], onDelete: Cascade)
  sipk_hukuman_dokumen sipk_hukuman_dokumen[]
}

model sipk_hukuman_dokumen {
  id           BigInt    @id @default(autoincrement())
  hukuman_id   BigInt
  originalname String?
  encoding     String?
  mimetype     String?
  destination  String?
  filename     String?
  path         String?
  size         BigInt?
  key          String?
  url          String?
  created_at   DateTime  @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @updatedAt @db.Timestamp(0)

  sipk_hukuman sipk_hukuman @relation(fields: [hukuman_id], references: [id])
}

model sipk_kontrak_kerja {
  id              BigInt    @id @default(autoincrement())
  penilaian_id    BigInt
  uraian          String
  indikator       String
  target_tw_1     Int
  pencapaian_tw_1 Int       @default(0)
  target_tw_2     Int
  pencapaian_tw_2 Int       @default(0)
  satuan          String
  poin            Int?
  created_at      DateTime? @default(now()) @db.Timestamp(0)
  updated_at      DateTime? @updatedAt @db.Timestamp(0)

  sipk_penilaian sipk_penilaian @relation(fields: [penilaian_id], references: [id], onDelete: Cascade)
}

model sipk_pejabat_diluar_struktur {
  id         BigInt    @id @default(autoincrement())
  nik        String
  satuan_id  String
  nama       String
  tgl_lahir  DateTime  @db.Date
  pangkat    String
  jabatan    String
  created_at DateTime? @default(now()) @db.Timestamp(6)
  updated_at DateTime? @updatedAt @db.Timestamp(6)
}

model sipk_penghargaan {
  id                   BigInt                               @id @default(autoincrement())
  penilaian_id         BigInt
  jenis_penghargaan_id BigInt
  no_kep               String                               @db.VarChar(100)
  tgl_kep              DateTime                             @db.Date
  uraian_kep           String
  source               String?                              @default("SIPP") @db.VarChar(20)
  surat_file           String?
  status_verifikasi    sipk_status_verifikasi_lainnya_enum? @default(WAITING)
  poin                 Int
  rejected_note        String?
  created_at           DateTime?                            @default(now()) @db.Timestamp(6)
  updated_at           DateTime?                            @updatedAt @db.Timestamp(6)

  sipk_penilaian           sipk_penilaian             @relation(fields: [penilaian_id], references: [id], onDelete: Cascade)
  sipk_penghargaan_dokumen sipk_penghargaan_dokumen[]

  jenis_penghargaan penghargaan @relation(fields: [jenis_penghargaan_id], references: [id])
}

model sipk_penghargaan_dokumen {
  id             BigInt    @id @default(autoincrement())
  penghargaan_id BigInt
  originalname   String?
  encoding       String?
  mimetype       String?
  destination    String?
  filename       String?
  path           String?
  size           BigInt?
  key            String?
  url            String?
  created_at     DateTime  @default(now()) @db.Timestamp(0)
  updated_at     DateTime? @updatedAt @db.Timestamp(0)

  sipk_penghargaan sipk_penghargaan @relation(fields: [penghargaan_id], references: [id])
}

model sipk_penilaian {
  id              BigInt    @id @default(autoincrement())
  personel_id     BigInt
  penilai_id      BigInt
  semester_id     BigInt
  status          Int       @default(0)
  nilai_fs        Float     @default(0)
  nilai_fa        Float     @default(0)
  nilai_akhir     Float     @default(0)
  rejected_reason String?   @db.VarChar(255)
  created_at      DateTime? @default(now()) @db.Timestamp(0)
  updated_at      DateTime? @updatedAt @db.Timestamp(0)

  sipk_hukuman         sipk_hukuman[]
  sipk_kontrak_kerja   sipk_kontrak_kerja[]
  sipk_penghargaan     sipk_penghargaan[]
  personel             personel               @relation(fields: [personel_id], references: [id], onDelete: Cascade)
  sipk_semester        sipk_semester          @relation(fields: [semester_id], references: [id], onDelete: Cascade)
  sipk_penilaian_rekan sipk_penilaian_rekan[]
  sipk_tugas_tambahan  sipk_tugas_tambahan[]

  @@index([personel_id])
  @@index([semester_id, personel_id])
  @@index([status])
  @@index([penilai_id])
}

model sipk_penilaian_rekan {
  id                       BigInt    @id @default(autoincrement())
  penilaian_id             BigInt
  personel_id              BigInt
  nilai_kepemimpinan       Int?
  nilai_pelayanan          Int?
  nilai_komunikasi         Int?
  nilai_pengendalian_emosi Int?
  nilai_integritas         Int?
  nilai_empati             Int?
  nilai_komitmen           Int?
  nilai_inisiatif          Int?
  nilai_disiplin           Int?
  nilai_kerjasama          Int?
  nilai_total              Float?
  created_at               DateTime? @default(now()) @db.Timestamp(6)
  updated_at               DateTime? @updatedAt @db.Timestamp(6)

  sipk_penilaian sipk_penilaian @relation(fields: [penilaian_id], references: [id], onDelete: Cascade)
  personel       personel       @relation(fields: [personel_id], references: [id])

  @@unique([personel_id, penilaian_id])
}

model sipk_personel {
  id           BigInt    @id @default(autoincrement())
  personel_id  BigInt
  penilaian_id BigInt
  nama         String
  pangkat      String
  pangkat_id   BigInt?
  nrp          String
  jabatan      String
  jabatan_id   BigInt?
  satuan_id    BigInt?
  created_at   DateTime? @default(now()) @db.Timestamp(6)
  updated_at   DateTime? @updatedAt @db.Timestamp(6)

  sipk_personel_pangkat pangkat? @relation("pangkat_to_pangkat", fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  personel              personel @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sipk_personel_personel_id_fk")

  @@index([personel_id, penilaian_id])
}

model sipk_semester {
  id          BigInt    @id @default(autoincrement())
  semester    Int
  tahun       Int
  tgl_mulai   DateTime? @db.Date
  tgl_selesai DateTime? @db.Date

  sipk_penilaian sipk_penilaian[]

  @@unique([semester, tahun])
}

model sipk_tugas_tambahan {
  id                BigInt                               @id @default(autoincrement())
  penilaian_id      BigInt
  no_sprin          String                               @db.VarChar(100)
  tgl_sprin         DateTime                             @db.Date
  uraian_sprin      String
  poin              Int?
  rejected_note     String?
  status_verifikasi sipk_status_verifikasi_lainnya_enum? @default(WAITING)
  created_at        DateTime?                            @default(now()) @db.Timestamp(6)
  updated_at        DateTime?                            @updatedAt @db.Timestamp(6)

  sipk_penilaian              sipk_penilaian                @relation(fields: [penilaian_id], references: [id], onDelete: Cascade)
  sipk_tugas_tambahan_dokumen sipk_tugas_tambahan_dokumen[]
}

model sipk_tugas_tambahan_dokumen {
  id                BigInt    @id @default(autoincrement())
  tugas_tambahan_id BigInt
  originalname      String?
  encoding          String?
  mimetype          String?
  destination       String?
  filename          String?
  path              String?
  size              BigInt?
  key               String?
  url               String?
  created_at        DateTime  @default(now()) @db.Timestamp(0)
  updated_at        DateTime? @updatedAt @db.Timestamp(0)

  sipk_tugas_tambahan sipk_tugas_tambahan @relation(fields: [tugas_tambahan_id], references: [id])
}

model sipp_jabatan_satuan_terakhir_personel {
  id                                                   BigInt    @id(map: "sipp_jabatan_satuan_terakhir_personel_pk") @default(autoincrement())
  personel_id                                          BigInt    @unique
  satuan_id                                            BigInt
  satuan_dibawah_id                                    BigInt?
  jabatan_id                                           BigInt
  sipp_jabatan_satuan_terakhir_personel_personel       personel? @relation("personel_to_personel", fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sipp_jabatan_satuan_terakhir_personel_jabatan_fk")
  sipp_jabatan_satuan_terakhir_personel_satuan         satuan    @relation("satuan_to_satuan", fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sipp_jabatan_satuan_terakhir_personel_satuan_fk")
  sipp_jabatan_satuan_terakhir_personel_satuan_dibawah satuan?   @relation("satuan_dibawah_to_satuan", fields: [satuan_dibawah_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "sipp_jabatan_satuan_terakhir_personel_satuan_fk_1")
  jabatan                                              jabatan?  @relation(fields: [personel_id], references: [id], map: "sipp_jabatan_satuan_terakhir_personel_personel_fk")

  satuan   satuan? @relation(fields: [satuanId], references: [id])
  satuanId BigInt?
}

model sipp_pangkat_personel {
  personel_id BigInt
  pangkat_id  BigInt
  kategori_id BigInt?
  id          BigInt  @id(map: "sipp_pangkat_personel_pk") @default(autoincrement())
}

model sipp_satker_personel {
  personel_id BigInt
  satuan_id   BigInt
  id          BigInt @id(map: "sipp_satker_personel_pk") @default(autoincrement())
}

model siswa {
  id                             BigInt                     @id(map: "rim_siswa_pkey") @default(autoincrement())
  uid                            String                     @unique(map: "siswa_uid_unique") @default(uuid()) @db.Uuid
  nama_lengkap                   String?                    @db.VarChar(255)
  gelar                          String?                    @db.VarChar(255)
  nama_dengan_gelar              String?                    @db.VarChar(255)
  nama_panggilan                 String?                    @db.VarChar(255)
  tanggal_lahir                  DateTime?                  @db.Date
  jenis_kelamin                  String                     @db.VarChar(255)
  tempat_lahir                   String?                    @db.VarChar
  agama_id                       BigInt?
  nik                            String?                    @unique(map: "siswa_nik_unique") @db.VarChar(255)
  status_kawin_id                BigInt?
  golongan_darah                 String?                    @db.VarChar(255)
  email                          String?                    @db.VarChar(255)
  no_hp                          String?                    @db.VarChar(255)
  suku_id                        BigInt?
  jenis_pekerjaan                String?                    @db.VarChar(255)
  hobi                           String?
  medsos_instagram               String?                    @db.VarChar(255)
  medsos_facebook                String?                    @db.VarChar(255)
  medsos_twitter                 String?                    @db.VarChar(255)
  no_ujian_polda                 String?                    @db.VarChar(255)
  no_registrasi_online           String?                    @db.VarChar(255)
  no_ak_nosis                    String?                    @db.VarChar(255)
  jenis_diktuk_id                BigInt?
  ta_rim_diktuk                  Int?
  gelombang_rim_masuk_diktuk     String?                    @db.VarChar(255)
  kompetensi_diktuk_id           BigInt?
  sub_kompetensi_diktuk_id       BigInt?
  sub_sub_kompetensi_diktuk_id   BigInt?
  ket_jalur_rekpro               String?                    @db.VarChar(255)
  tmp_dik_id                     BigInt?
  asal_rim_polda_id              BigInt?
  asal_rim_polres_id             BigInt?
  ta_pat_diktuk                  String?                    @db.VarChar(255)
  gelombang_pat_diktuk           String?                    @db.VarChar(255)
  ijazah_dikum_seleksi_rim       String?                    @db.VarChar(255)
  th_lulus_dikum_gun_seleksi_rim String?                    @db.VarChar(255)
  created_at                     DateTime?                  @default(now()) @db.Timestamp(0)
  updated_at                     DateTime?                  @db.Timestamp(0)
  deleted_at                     DateTime?                  @db.Timestamp(0)
  pers_id                        String?                    @db.VarChar
  no_urut_asalrim                Int?
  unique_id                      String?                    @unique(map: "siswa_unique_id_unique")
  username                       String                     @unique(map: "siswa_username_unique") @db.VarChar
  password                       String?                    @db.VarChar
  oap_orang_asli_papua           String?                    @db.VarChar
  asal_rim_polsek_id             BigInt?
  medsos_tiktok                  String?                    @db.VarChar
  status_validation              Boolean?                   @default(false)
  status_upload                  Boolean?                   @default(false)
  status_generate_nrp            Boolean?
  status_angket                  Boolean?
  status_display_angket          Boolean?
  suku                           String?                    @db.VarChar
  agama                          agama?                     @relation(fields: [agama_id], references: [id], onUpdate: NoAction, map: "agama_siswa_fkey")
  sukus                          suku?                      @relation(fields: [suku_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_suku_ref")
  siswa_asal_rim_polda           satuan?                    @relation("siswa_asal_rim_polda_to_satuan", fields: [asal_rim_polda_id], references: [id], onUpdate: NoAction, map: "rim_siswa_asal_rim_polda_id_fkey")
  siswa_asal_rim_polres          satuan?                    @relation("siswa_asal_rim_polres_to_satuan", fields: [asal_rim_polres_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "rim_siswa_asal_rim_polres_id_fkey")
  siswa_asal_rim_polsek          satuan?                    @relation("siswa_asal_rim_polsek_to_satuan", fields: [asal_rim_polsek_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_asal_rim_polsek_id")
  jenis_diktuk                   jenis_diktuk?              @relation(fields: [jenis_diktuk_id], references: [id], onUpdate: NoAction, map: "rim_siswa_jenis_diktuk_id_fkey")
  kompetensi_diktuk              kompetensi_diktuk?         @relation(fields: [kompetensi_diktuk_id], references: [id], onUpdate: NoAction, map: "rim_siswa_kompetensi_diktuk_id_fkey")
  sub_kompetensi_diktuk          sub_kompetensi_diktuk?     @relation(fields: [sub_kompetensi_diktuk_id], references: [id], onUpdate: NoAction, map: "rim_siswa_sub_kompetensi_diktuk_id_fkey")
  sub_sub_kompetensi_diktuk      sub_sub_kompetensi_diktuk? @relation(fields: [sub_sub_kompetensi_diktuk_id], references: [id], onUpdate: NoAction, map: "rim_siswa_sub_sub_kompetensi_diktuk_id_fkey")
  tmpdik                         tmpdik?                    @relation(fields: [tmp_dik_id], references: [id], onUpdate: NoAction, map: "rim_siswa_tmp_dik_id_fkey")
  status_kawin                   status_kawin?              @relation(fields: [status_kawin_id], references: [id], onUpdate: NoAction, map: "status_kawin_siswa_fkey")
  siswa_alamat_ref               siswa_alamat?
  siswa_diktuk                   siswa_diktuk?
  siswa_fisik_ref                siswa_fisik?
  siswa_hobi                     siswa_hobi[]
  siswa_keahlian_ref             siswa_keahlian?
  siswa_keluarga_ref             siswa_keluarga?
  siswa_keswa_angket             siswa_keswa_angket?
  siswa_patma                    siswa_patma?
  siswa_penerimaan_ref           siswa_penerimaan?
  siswa_riwayat_pendidikan       siswa_riwayat_pendidikan?

  @@index([gelombang_pat_diktuk], map: "siswa_gelombang_pat_diktuk_index")
  @@index([gelombang_rim_masuk_diktuk, ta_rim_diktuk], map: "siswa_gelombang_rim_masuk_diktuk_ta_rim_diktuk_index")
}

model siswa_alamat {
  id           BigInt     @id(map: "alamat_siswa_pkey") @default(autoincrement())
  siswa_id     BigInt     @unique
  alamat       String?
  no_rt        String?    @db.VarChar(255)
  no_rw        String?    @db.VarChar(255)
  kecamatan_id BigInt?
  kelurahan_id BigInt?
  kabupaten_id BigInt?
  provinsi_id  BigInt?
  created_at   DateTime?  @default(now()) @db.Timestamp(6)
  updated_at   DateTime?  @updatedAt @db.Timestamp(6)
  deleted_at   DateTime?  @db.Timestamp(6)
  kabupaten    kabupaten? @relation(fields: [kabupaten_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alamat_kabupaten_fkey")
  kecamatan    kecamatan? @relation(fields: [kecamatan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alamat_kecamatan_fkey")
  kelurahan    kelurahan? @relation(fields: [kelurahan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alamat_kelurahan_fkey")
  provinsi     provinsi?  @relation(fields: [provinsi_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "alamat_provinsi_fkey")
  siswa        siswa?     @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "alamat_siswa_id_fkey")
}

model siswa_diktuk {
  id                         BigInt    @id(map: "penilaian_diktuk_pkey") @default(autoincrement())
  siswa_id                   BigInt    @unique(map: "unique_siswa_id")
  no_urut_tmp_dik            String?   @db.VarChar(255)
  hasil_lari_12_m_jasdiktuk  Decimal?  @db.Decimal
  n_lari_12_m_jasdiktuk      Decimal?  @db.Decimal
  hasil_pull_up_jasdiktuk    Decimal?  @db.Decimal
  n_pull_up_jasdiktuk        Decimal?  @db.Decimal
  hasil_situp_jasdiktuk      Decimal?  @db.Decimal
  n_situp_jasdiktuk          Decimal?  @db.Decimal
  hasil_pushup_jasdiktuk     Decimal?  @db.Decimal
  n_pushup_jasdiktuk         Decimal?  @db.Decimal
  hasil_shuttlerun_jasdiktuk Decimal?  @db.Decimal
  n_shuttlerun_jasdiktuk     Decimal?  @db.Decimal
  rata2_n_b_jasdiktuk        Decimal?  @db.Decimal
  n_ab_jasdiktuk             Decimal?  @db.Decimal
  n_renang_jasdiktuk         Decimal?  @db.Decimal
  n_jasmani_diktuk           Decimal?  @db.Decimal
  n_mentalkep_kar_diktuk     Decimal?  @db.Decimal
  n_akademik_peng_diktuk     Decimal?  @db.Decimal
  n_keterampilan_diktuk      Decimal?  @db.Decimal
  n_kesehatan_diktuk         Decimal?  @db.Decimal
  n_gbgakhir_diktuk          Decimal?  @db.Decimal
  ranking_diktuk             Decimal?  @db.Decimal
  catatan_kesehatan_diktuk   String?
  catatan_pelanggaran_diktuk String?
  catatan_psikologi_diktuk   String?
  prestasi_diktuk            String?
  catatan_khusus_diktuk      String?
  status_diktuk              String?   @db.VarChar(255)
  ket_status_diktuk          String?   @db.VarChar(255)
  created_at                 DateTime? @default(now()) @db.Timestamp(0)
  updated_at                 DateTime? @updatedAt @db.Timestamp(0)
  deleted_at                 DateTime? @db.Timestamp(0)
  siswa                      siswa     @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "penilaian_diktuk_siswa_id_fkey")
}

model siswa_fisik {
  id            BigInt    @id(map: "detail_siswa_pkey") @default(autoincrement())
  siswa_id      BigInt    @unique
  tb_cm         Int?
  bb_kg         Int?
  warna_kulit   String?   @db.VarChar(255)
  warna_mata    String?   @db.VarChar(255)
  warna_rambut  String?   @db.VarChar(255)
  jenis_rambut  String?   @db.VarChar(255)
  ukuran_topi   String?   @db.VarChar(255)
  ukuran_celana String?   @db.VarChar(255)
  ukuran_baju   String?   @db.VarChar(255)
  ukuran_sepatu String?   @db.VarChar(255)
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @updatedAt @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)
  siswa         siswa?    @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fisik_siswa_id_fkey")
}

model siswa_hobi {
  id         BigInt    @id(map: "hobi_siswa_pkey") @default(autoincrement())
  siswa_id   BigInt
  hobi_id    BigInt
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  hobi       hobi      @relation(fields: [hobi_id], references: [id], onDelete: Cascade, map: "siswa_hobi_hobi_id_foreign")
  siswa      siswa     @relation(fields: [siswa_id], references: [id], onDelete: Cascade, map: "siswa_hobi_siswa_id_foreign")
}

model siswa_keahlian {
  id                         BigInt    @id(map: "keahlian_siswa_pkey") @default(autoincrement())
  siswa_id                   BigInt    @unique
  puan_bhs_daerah            String?   @db.VarChar(255)
  ket_puan_bhs_daerah        String?   @db.VarChar(255)
  puan_bhs_asing             String?   @db.VarChar(255)
  ket_puan_bhs_asing         String?   @db.VarChar(255)
  keahlian_aplikasi_komputer String?   @db.VarChar(255)
  keahlian_mengemudi         String?   @db.VarChar(255)
  kepemilikan_sim            String?   @db.VarChar(255)
  created_at                 DateTime? @default(now()) @db.Timestamp(0)
  updated_at                 DateTime? @updatedAt @db.Timestamp(0)
  deleted_at                 DateTime? @db.Timestamp(0)
  keahlian_beladiri          String?   @db.VarChar
  sabuk_tingkatan_beladiri   String?   @db.VarChar
  siswa                      siswa?    @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "keahlian_siswa_id_fkey")
}

model siswa_keluarga {
  id               BigInt    @id(map: "keluarga_siswa_pkey") @default(autoincrement())
  siswa_id         BigInt    @unique
  anak_ke          String?   @db.VarChar(255)
  jumlah_saudara   String?   @db.VarChar(255)
  nama_ayah        String?   @db.VarChar(255)
  pek_ayah         String?   @db.VarChar(255)
  status_ayah      String?   @db.VarChar(255)
  gol_pangkat_ayah String?   @db.VarChar(255)
  jabatan_ayah     String?   @db.VarChar(255)
  nama_ibu         String?   @db.VarChar(255)
  pek_ibu          String?   @db.VarChar(255)
  status_ibu       String?   @db.VarChar(255)
  gol_pangkat_ibu  String?   @db.VarChar(255)
  jabatan_ibu      String?   @db.VarChar(255)
  no_hp_ortu       String?   @db.VarChar(255)
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @updatedAt @db.Timestamp(0)
  deleted_at       DateTime? @db.Timestamp(0)
  umur_ayah        Int?
  umur_ibu         Int?
  siswa            siswa?    @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "keluarga_siswa_id_fkey")
}

model siswa_keswa_angket {
  id                                 BigInt  @id @default(autoincrement())
  siswa_id                           BigInt  @unique
  rekom_psi_1                        String? @db.VarChar(255)
  rekom_psi_2                        String? @db.VarChar(255)
  catatan_khusus_rekompsi            String? @db.VarChar(255)
  rikkeswa_1                         String? @db.VarChar(255)
  rikkeswa_2                         String? @db.VarChar(255)
  catatan_khusus_rikkeswa            String? @db.VarChar(255)
  angket_1                           BigInt? @unique
  angket_2                           BigInt? @unique
  angket_3                           BigInt? @unique
  angket_4                           BigInt? @unique
  angket_5                           BigInt? @unique
  ket_angket                         String? @db.VarChar(255)
  siswa_keswa_angket_satuan_angket_1 satuan? @relation("satuan_angket_1_to_satuan", fields: [angket_1], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa_keswa_angket_satuan_angket_2 satuan? @relation("satuan_angket2_to_satuan", fields: [angket_2], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa_keswa_angket_satuan_angket_3 satuan? @relation("satuan_angket3_to_satuan", fields: [angket_3], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa_keswa_angket_satuan_angket_4 satuan? @relation("satuan_angket4_to_satuan", fields: [angket_4], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa_keswa_angket_satuan_angket_5 satuan? @relation("satuan_angket5_to_satuan", fields: [angket_5], references: [id], onDelete: NoAction, onUpdate: NoAction)
  siswa                              siswa?  @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model siswa_patma {
  id                    BigInt    @id(map: "patma_siswa_pkey") @default(autoincrement())
  siswa_id              BigInt    @unique
  nrp                   String?   @db.VarChar
  urut_no_patma_gbg     BigInt?
  urut_no_patma_per_spn BigInt?
  patma                 String?   @db.VarChar(255)
  sub_patma             String?   @db.VarChar(255)
  sub_sub_patma         String?   @db.VarChar(255)
  satker_patma          BigInt?
  jab_patma             String?   @db.VarChar(255)
  ket_jab_patma         String?
  patma_mabes_polda     String?   @db.VarChar(255)
  data_penarikan_satker String?
  catatan_khusus_patma  String?
  nama_kasubbag         String?   @db.VarChar(255)
  nama_kabag            String?   @db.VarChar(255)
  nrp_kabag             String?   @db.VarChar(255)
  nama_karo             String?   @db.VarChar(255)
  nama_as_sdm           String?   @db.VarChar(255)
  nama_kapolri          String?   @db.VarChar(255)
  pangkat               String?   @db.VarChar(50)
  kd_lamp_patma         String?   @db.VarChar(50)
  no_kep_patma          String?   @db.VarChar(50)
  tmt_kep_patma         DateTime? @db.Date
  tmt_kep_pangkat       DateTime? @db.Date
  kesarjanaan_mds       String?   @db.VarChar(255)
  th_mds                String?   @db.VarChar
  th_naik_gaji_mds      String?   @db.VarChar
  tmt_mds               DateTime? @db.Date
  urut_no_mds           String?   @db.VarChar
  msk_gol_gaji_5        DateTime? @db.Date
  gol_gaji_8            String?   @db.VarChar
  msk_gol_gaji_9        DateTime? @db.Date
  gaji                  Decimal?  @db.Decimal
  terbilang_gaji        String?   @db.VarChar(255)
  ket_kep_a             String?
  ket_kep_b             String?
  ket_kep_c             String?
  ket_kep_d             String?
  urut_no_keppres       Int?
  no_keppres            String?   @db.VarChar(50)
  tmt_keppres           DateTime? @db.Date
  file_keppres          String?   @db.VarChar(255)
  wil_tmp_dik           String?   @db.VarChar(255)
  wil_patma             String?   @db.VarChar(255)
  wil_asal_tujuan       String?   @db.VarChar(255)
  renbut_jaldis         String?
  status_verifikasi     String?   @db.VarChar(50)
  created_at            DateTime? @default(now()) @db.Timestamp(0)
  updated_at            DateTime? @updatedAt @db.Timestamp(0)
  deleted_at            DateTime? @db.Timestamp(0)
  is_verified_nrp       Boolean?  @default(false)
  siswa                 siswa     @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "siswa_patma_fk")
  satker_patma_ref      satuan?   @relation(fields: [satker_patma], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "siswa_patma_satker_patma_foreign")
}

model siswa_penerimaan {
  id                                    BigInt    @id(map: "rim_nilai_siswa_pkey") @default(autoincrement())
  siswa_id                              BigInt    @unique(map: "siswa_penerimaan_pk")
  n_kualitatif_rikmin_awal              String?   @db.VarChar
  ket_rikmin_awal                       String?   @db.VarChar
  n_kualitatif_rikmin_akhir             String?   @db.VarChar
  n_kuantitatif_rikmin_akhir            Decimal?  @db.Decimal
  ket_rikmin_akhir                      String?   @db.VarChar
  n_kualitatif_rikkes_1                 String?   @db.VarChar
  n_kuantitatif_rikkes_1                Decimal?  @db.Decimal
  diagnosa_rikkes_1                     String?   @db.VarChar
  n_kualitatif_rikkes_2                 String?   @db.VarChar
  n_kuantitatif_rikkes_2                Decimal?  @db.Decimal
  diagnosa_rikkes_2                     String?   @db.VarChar
  n_kualitatif_rikpsi_1                 String?   @db.VarChar
  n_kuantitatif_rikpsi_1                String?   @db.VarChar
  ket_rikpsi_1                          String?   @db.VarChar
  n_kualitatif_rikpsi_2                 String?   @db.VarChar
  n_kuantitatif_rikpsi_2                Decimal?  @db.Decimal
  temuan_rikpsi_2                       String?   @db.VarChar
  ket_rikpsi_2                          String?   @db.VarChar
  n_peng_u_akademik                     Decimal?  @db.Decimal
  n_wwsn_k_akademik                     Decimal?  @db.Decimal
  n_mtk_akademik                        Decimal?  @db.Decimal
  n_b_ing_akademik                      Decimal?  @db.Decimal
  n_b_ind_akademik                      Decimal?  @db.Decimal
  n_tkk_pengetahuan_sipss_akademik      Decimal?  @db.Decimal
  n_tkm_sipss_akademik                  Decimal?  @db.Decimal
  n_tkk_pengetahuan_bakomsus_akademik   Decimal?  @db.Decimal
  n_tkk_keterampilan_bakomsus_akademik  Decimal?  @db.Decimal
  n_tkk_perilaku_bakomsus_akademik      Decimal?  @db.Decimal
  n_gbg_tkk_bakomsus_akademik           Decimal?  @db.Decimal
  n_gbgakhir_akademik                   Decimal?  @db.Decimal
  ket_akademik                          Decimal?  @db.Decimal
  hasil_lari_12_m_ukj                   Decimal?  @db.Decimal
  n_a_lari_12_menit_ukj                 Decimal?  @db.Decimal
  hasil_pull_up_ukj                     Decimal?  @db.Decimal
  n_pull_up_ukj                         Decimal?  @db.Decimal
  hasil_situp_ukj                       Decimal?  @db.Decimal
  n_situp_ukj                           Decimal?  @db.Decimal
  hasil_pushup_ukj                      Decimal?  @db.Decimal
  n_pushup_ukj                          Decimal?  @db.Decimal
  hasil_shuttlerun_ukj                  Decimal?  @db.Decimal
  n_shuttle_run_ukj                     Decimal?  @db.Decimal
  rata2_n_b_ukj                         Decimal?  @db.Decimal
  n_ab_ukj                              Decimal?  @db.Decimal
  jarak_renang_ukj                      Decimal?  @db.Decimal
  waktu_renang_ukj                      String?   @db.VarChar
  n_renang_ukj                          Decimal?  @db.Decimal
  n_kualitatif_antro_ukj                String?   @db.VarChar
  n_kuantitatif_antro_ukj               Decimal?  @db.Decimal
  kelainan_antro_ukj                    String?   @db.VarChar
  n_kuantitatif_akhir_ukj               Decimal?  @db.Decimal
  n_kualitatif_akhir_ukj                String?   @db.VarChar
  ket_ukj                               String?   @db.VarChar
  n_kualitatif_pmk                      String?   @db.VarChar
  n_kuantitatif_pmk                     Decimal?  @db.Decimal
  temuan_pmk                            String?   @db.VarChar
  ket_pmk                               String?   @db.VarChar
  data_deteksi_dini_densus              String?   @db.VarChar
  ket_deteksi_dini_densus               String?   @db.VarChar
  prestasi_seleksi_rim                  String?   @db.VarChar
  catatan_khusus_seleksi_rim            String?   @db.VarChar
  n_kualitatif_rikmin_awal_pus          String?   @db.VarChar
  ket_rikmin_awal_pus                   String?   @db.VarChar
  n_kualitatif_rikmin_akhir_pus         String?   @db.VarChar
  ket_rikmin_akhir_pus                  String?   @db.VarChar
  n_kualitatif_rikkes_1_pus             String?   @db.VarChar
  n_kuantitatif_rikkes_1_pus            Decimal?  @db.Decimal
  diagnosa_rikkes_1_pus                 String?   @db.VarChar
  n_kualitatif_rikkes_2_pus             String?   @db.VarChar
  n_kuantitatif_rikkes_2_pus            Decimal?  @db.Decimal
  diagnosa_rikkes_2_pus                 String?   @db.VarChar
  n_kualitatif_rikpsi_1_pus             String?   @db.VarChar
  n_kuantitatif_rikpsi_1_pus            Decimal?  @db.Decimal
  ket_rikpsi_1_pus                      String?   @db.VarChar
  n_kualitatif_rikpsi_2_pus             String?   @db.VarChar
  n_kuantitatif_rikpsi_2_pus            Decimal?  @db.Decimal
  temuan_rikpsi_2_pus                   String?   @db.VarChar
  ket_rikpsi_2_pus                      String?   @db.VarChar
  n_peng_u_akademik_pus                 Decimal?  @db.Decimal
  n_wwsn_k_akademik_pus                 Decimal?  @db.Decimal
  n_mtk_akademik_pus                    Decimal?  @db.Decimal
  n_b_ing_akademik_pus                  Decimal?  @db.Decimal
  n_tkk_sipss_akademik_pus              Decimal?  @db.Decimal
  n_tkm_sipss_akademik_pus              Decimal?  @db.Decimal
  n_gbgakhir_akademik_pus               Decimal?  @db.Decimal
  ket_akademik_pus                      Decimal?  @db.Decimal
  hasil_lari_12_m_ukj_pus               Decimal?  @db.Decimal
  n_a_lari_12_menit_ukj_pus             Decimal?  @db.Decimal
  hasil_pull_up_ukj_pus                 Decimal?  @db.Decimal
  n_pull_up_ukj_pus                     Decimal?  @db.Decimal
  hasil_situp_ukj_pus                   Decimal?  @db.Decimal
  n_situp_ukj_pus                       Decimal?  @db.Decimal
  hasil_pushup_ukj_pus                  Decimal?  @db.Decimal
  n_pushup_ukj_pus                      Decimal?  @db.Decimal
  hasil_shuttlerun_ukj_pus              Decimal?  @db.Decimal
  n_shuttle_run_ukj_pus                 Decimal?  @db.Decimal
  rata2_n_b_ukj_pus                     Decimal?  @db.Decimal
  n_ab_ukj_pus                          Decimal?  @db.Decimal
  jarak_renang_ukj_pus                  Decimal?  @db.Decimal
  waktu_renang_ukj_pus                  String?   @db.VarChar
  n_renang_ukj_pus                      Decimal?  @db.Decimal
  n_kualitatif_antro_ukj_pus            String?   @db.VarChar
  n_kuantitatif_antro_ukj_pus           Decimal?  @db.Decimal
  kelainan_antro_ukj_pus                String?   @db.VarChar
  n_kuantitatif_akhir_ukj_pus           Decimal?  @db.Decimal
  n_kualitatif_akhir_ukj_pus            String?   @db.VarChar
  ket_ukj_pus                           String?   @db.VarChar
  n_kualitatif_pmk_pus                  String?   @db.VarChar
  n_kuantitatif_pmk_pus                 Decimal?  @db.Decimal
  temuan_pmk_pus                        String?   @db.VarChar
  ket_pmk_pus                           String?   @db.VarChar
  n_rikpil_pus                          Decimal?  @db.Decimal
  ket_rikpil_pus                        String?   @db.VarChar
  data_deteksi_dini_densus_pus          String?   @db.VarChar
  ket_deteksi_dini_densus_pus           String?   @db.VarChar
  prestasi_seleksi_rim_pus              String?   @db.VarChar
  catatan_khusus_seleksi_rim_pus        String?   @db.VarChar
  created_at                            DateTime? @default(now()) @db.Timestamp(0)
  updated_at                            DateTime? @updatedAt @db.Timestamp(0)
  deleted_at                            DateTime? @db.Timestamp(0)
  n_tkk_keterampilan_sipss_akademik_pus Decimal?  @db.Decimal
  n_tkk_perilaku_sipss_akademik_pus     Decimal?  @db.Decimal
  n_tpa_akademik_pus                    Decimal?  @db.Decimal
  siswa                                 siswa?    @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "penerimaan_siswa_id_fkey")
}

model siswa_riwayat_pendidikan {
  id                                              BigInt    @id(map: "riwayat_pendidikan_siswa_pkey") @default(autoincrement())
  siswa_id                                        BigInt    @unique
  sd                                              String?   @db.VarChar(5)
  nama_sd                                         String?   @db.VarChar(255)
  thn_lulus_sd                                    String?   @db.VarChar(255)
  smp                                             String?   @db.VarChar(5)
  nama_smp                                        String?   @db.VarChar(255)
  thn_lulus_smp                                   String?   @db.VarChar(255)
  sma                                             String?   @db.VarChar(5)
  jurusan_sma                                     String?   @db.VarChar(255)
  nama_sma                                        String?   @db.VarChar(255)
  thn_lulus_sma                                   String?   @db.VarChar(255)
  perguruan_tinggi                                String?   @db.VarChar(5)
  nama_perguruan_tinggi                           String?   @db.VarChar(255)
  akreditas_banpt                                 String?   @db.VarChar(50)
  ipk                                             Decimal?  @db.Decimal
  created_at                                      DateTime? @default(now()) @db.Timestamp(0)
  updated_at                                      DateTime? @updatedAt @db.Timestamp(0)
  deleted_at                                      DateTime? @db.Timestamp(0)
  prestasi_dikum                                  String?   @db.VarChar
  jurusan_prodi                                   String?   @db.VarChar
  thn_lulus_perguruan_tinggi                      String?   @db.VarChar
  prov_sd_id                                      BigInt?
  prov_smp_id                                     BigInt?
  prov_sma_id                                     BigInt?
  nilai_un_sma                                    Decimal?  @db.Decimal
  rata2_nrapot_kelas_xii_smt1                     Decimal?  @db.Decimal
  prov_perguruan_tinggi_id                        BigInt?
  perguruan_tinggi_2                              String?   @db.VarChar
  nama_perguruan_tinggi_2                         String?   @db.VarChar
  prov_perguruan_tinggi_2_id                      BigInt?
  jurusan_prodi_2                                 String?   @db.VarChar
  thn_lulus_perguruan_tinggi_2                    String?   @db.VarChar
  akreditasi_banpt_2                              String?   @db.VarChar
  ipk_2                                           Decimal?  @db.Decimal
  siswa_riwayat_pendidikan_prov_perguruan_tnggi_2 provinsi? @relation("prov_perguruan_tinggi_2_to_provinsi", fields: [prov_perguruan_tinggi_2_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_prov_perguruan_tinggi_2_id")
  siswa_riwayat_pendidikan_prov_perguruan_tinggi  provinsi? @relation("prov_perguruan_tinggi_to_provinsi", fields: [prov_perguruan_tinggi_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_prov_perguruan_tinggi_id")
  siswa_riwayat_pendidikan_prov_sd                provinsi? @relation("prov_sd_to_provinsi", fields: [prov_sd_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_prov_sd_id")
  siswa_riwayat_pendidikan_prov_sma               provinsi? @relation("prov_sma_to_provinsi", fields: [prov_sma_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_prov_sma_id")
  siswa_riwayat_pendidikan_prov_smp               provinsi? @relation("prov_smp_to_provinsi", fields: [prov_smp_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_prov_smp_id")
  siswa                                           siswa?    @relation(fields: [siswa_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "riwayat_pendidikan_siswa_id_fkey")
}

model smk_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  tahun       Int       @db.SmallInt
  semester    Int       @db.SmallInt
  nilai       Float
  nilai_file  String?   @db.VarChar(255)
  keterangan  String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
}

model status_aktif {
  id             BigInt     @id @default(autoincrement())
  nama           String     @db.VarChar(255)
  status_pilihan String     @db.VarChar(255)
  created_at     DateTime?  @default(now()) @db.Timestamp(0)
  updated_at     DateTime?  @updatedAt @db.Timestamp(0)
  deleted_at     DateTime?  @db.Timestamp(0)
  personel       personel[]
}

model status_kawin {
  id                     BigInt          @id @default(autoincrement())
  nama                   String          @db.VarChar(255)
  created_at             DateTime?       @default(now()) @db.Timestamp(0)
  updated_at             DateTime?       @updatedAt @db.Timestamp(0)
  deleted_at             DateTime?       @db.Timestamp(0)
  pengajuan_ncr_pasangan pengajuan_ncr[] @relation("status_kawin_pasangan_to_status_kawin")
  pengajuan_ncr_pemohon  pengajuan_ncr[] @relation("status_kawin_pemohon_to_status_kawin")
  siswa                  siswa[]
  personel               personel[]
  data_siswa             data_siswa[]
}

model sub_kompetensi_diktuk {
  id                        BigInt                      @id @default(autoincrement())
  nama                      String?                     @db.VarChar(255)
  created_at                DateTime?                   @default(now()) @db.Timestamp(0)
  updated_at                DateTime?                   @db.Timestamp(0)
  kompetensi_id             BigInt?
  angket_patma              angket_patma[]
  siswa                     siswa[]
  kompetensi_diktuk         kompetensi_diktuk?          @relation(fields: [kompetensi_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sub_sub_kompetensi_diktuk sub_sub_kompetensi_diktuk[]
}

model sub_sub_kompetensi_diktuk {
  id                    BigInt                 @id @default(autoincrement())
  nama                  String?                @db.VarChar(255)
  created_at            DateTime?              @default(now()) @db.Timestamp(0)
  updated_at            DateTime?              @db.Timestamp(0)
  sub_kompetensi_id     BigInt?
  siswa                 siswa[]
  sub_kompetensi_diktuk sub_kompetensi_diktuk? @relation(fields: [sub_kompetensi_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model suku {
  id                  BigInt                @id(map: "pk_suku") @default(autoincrement())
  nama                String?
  is_aktif            Boolean?              @default(true)
  created_at          DateTime?             @default(now()) @db.Timestamp(6)
  updated_at          DateTime?             @updatedAt @db.Timestamp(6)
  deleted_at          DateTime?
  siswa               siswa[]
  personel            personel[]
  data_keluarga_siswa data_keluarga_siswa[]
}

model suku_temp {
  id         BigInt    @id @default(autoincrement())
  nama       String?
  created_at DateTime? @default(now()) @db.Timestamp(6)
  updated_at DateTime? @updatedAt @db.Timestamp(6)
  deleted_at String?
}

model survey_agg {
  id         BigInt    @id(map: "survey_agg_pk") @default(autoincrement())
  total      Int?
  respondens Int?
  responses  Int?
  created_at DateTime? @default(now()) @db.Timestamp(6)
  updated_at DateTime? @updatedAt @db.Timestamp(6)
}

model survey_answers {
  id               BigInt          @id(map: "survey_answers_pk") @default(autoincrement())
  personel_id      BigInt
  question_id      BigInt
  submission_start DateTime        @db.Timestamp(0)
  submission_end   DateTime?       @db.Timestamp(0)
  list             Json?
  created_at       DateTime?       @default(now()) @db.Timestamp(0)
  updated_at       DateTime?       @updatedAt @db.Timestamp(0)
  deleted_at       DateTime?       @db.Timestamp(0)
  personel         personel        @relation(fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_answers_fk_personel")
  survey_question  survey_question @relation(fields: [question_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_answers_fk_survey_question")
}

model survey_destination {
  id              BigInt          @id(map: "survey_destination_pk") @default(autoincrement())
  survey_id       BigInt
  satuan_id       BigInt?
  pangkat_id      BigInt?
  survey_group_id BigInt?
  created_at      DateTime?       @default(now()) @db.Timestamp(0)
  updated_at      DateTime?       @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?       @db.Timestamp(0)
  bagian_id       Int?
  bagian          bagian?         @relation(fields: [bagian_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_destination_fk_bagian")
  pangkat         pangkat?        @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_destination_fk_pangkat")
  satuan          satuan?         @relation(fields: [satuan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_destination_fk_satuan")
  survey_group    survey_group?   @relation(fields: [survey_group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_destination_fk_survey_group")
  survey_question survey_question @relation(fields: [survey_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_destination_fk_survey_question")
}

model survey_file_extensions {
  id           BigInt    @id(map: "survey_file_extensions_pk") @default(autoincrement())
  display_name String    @db.VarChar(255)
  created_at   DateTime? @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @updatedAt @db.Timestamp(0)
  deleted_at   DateTime? @db.Timestamp(0)
  extensions   String?   @db.VarChar(255)
}

model survey_files {
  id           BigInt    @id @default(autoincrement())
  survey_id    BigInt
  answer_id    BigInt
  originalname String    @db.VarChar
  encoding     String    @db.VarChar
  mimetype     String    @db.VarChar
  destination  String?   @db.VarChar
  filename     String?   @db.VarChar
  path         String?   @db.VarChar
  size         Int?
  key          String?   @db.VarChar
  url          String?   @db.VarChar
  created_at   DateTime  @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @updatedAt @db.Timestamp(0)
  deleted_at   DateTime? @db.Timestamp(0)
}

model survey_group {
  id                    BigInt                  @id(map: "survey_group_pk") @default(autoincrement())
  nama                  String                  @db.VarChar(255)
  created_at            DateTime?               @default(now()) @db.Timestamp(0)
  updated_at            DateTime?               @updatedAt @db.Timestamp(0)
  deleted_at            DateTime?               @db.Timestamp(0)
  survey_destination    survey_destination[]
  survey_group_personel survey_group_personel[]
}

model survey_group_personel {
  group_id     BigInt
  personel_id  BigInt
  created_at   DateTime?    @default(now()) @db.Timestamp(0)
  updated_at   DateTime?    @db.Timestamp(0)
  deleted_at   DateTime?    @db.Timestamp(0)
  id           BigInt       @id(map: "survey_group_personel_pk") @default(autoincrement())
  survey_group survey_group @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_group_personel_fk")
}

model survey_personel {
  personel_id BigInt
  survey_id   BigInt
  created_at  DateTime @default(now()) @db.Timestamp(6)

  id BigInt @id(map: "survey_personel_pk") @default(autoincrement())

  @@unique([personel_id, survey_id], map: "survey_personel_pk_2")
}

model survey_question {
  id                     BigInt                 @id(map: "survey_question_pk") @default(autoincrement())
  title                  String                 @db.VarChar(255)
  start_date             DateTime               @db.Timestamp(0)
  end_date               DateTime               @db.Timestamp(0)
  countdown_second       BigInt?
  status_id              BigInt
  created_by_personel    BigInt
  question_list          Json                   @db.Json
  created_at             DateTime?              @default(now()) @db.Timestamp(0)
  updated_at             DateTime?              @db.Timestamp(0)
  deleted_at             DateTime?              @db.Timestamp(0)
  description            String?
  question_count         Int?
  survey_answers         survey_answers[]
  survey_destination     survey_destination[]
  survey_question_status survey_question_status @relation(fields: [status_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_question_fk_survey_question_status")
  survey_personel        personel               @relation(fields: [created_by_personel], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "survey_question_fk_creator_personel_id")
}

model survey_question_status {
  id              BigInt            @id(map: "survey_question_status_pk") @default(autoincrement())
  name            String            @db.VarChar(255)
  created_at      DateTime?         @default(now()) @db.Timestamp(0)
  updated_at      DateTime?         @updatedAt @db.Timestamp(0)
  deleted_at      DateTime?         @db.Timestamp(0)
  survey_question survey_question[]
}

model survey_question_type {
  id            BigInt    @id(map: "survey_question_type_pk") @default(autoincrement())
  display_name  String    @db.VarChar(255)
  created_at    DateTime? @default(now()) @db.Timestamp(0)
  updated_at    DateTime? @updatedAt @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)
  question_type String    @db.VarChar(255)
  answer_type   String    @db.VarChar(255)
}

model survey_view_log {
  personel_id BigInt?
  survey_id   BigInt?
  viewed_at   DateTime? @db.Timestamp(0)
  id          BigInt    @id(map: "survey_view_log_pk") @default(autoincrement())
}

model tmpdik {
  id             BigInt       @id @default(autoincrement())
  nama           String       @db.VarChar(255)
  tmpdik_singkat String?      @db.VarChar(255)
  created_at     DateTime?    @default(now()) @db.Timestamp(0)
  updated_at     DateTime?    @db.Timestamp(0)
  deleted_at     DateTime?    @db.Timestamp(0)
  siswa          siswa[]
  data_siswa     data_siswa[]
}

model ukuran_fisik {
  id         BigInt    @id @default(autoincrement())
  nama       String    @db.VarChar()
  kategori   String    @db.VarChar()
  created_at DateTime  @default(now()) @db.Timestamp(0)
  created_by BigInt
  updated_at DateTime? @updatedAt() @db.Timestamp(0)
  updated_by BigInt?
  deleted_at DateTime? @db.Timestamp(0)
  deleted_by BigInt?

  created_by_users  users                @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ukuran_topi_siswa data_genetik_siswa[] @relation("data_genetik_siswa_ukuran_fisik_id_fk")
  ukuran_baju_siswa data_genetik_siswa[] @relation("data_genetik_siswa_ukuran_fisik_id_fk_2")

  @@unique([nama, kategori, deleted_at])
}

model users {
  id                                       BigInt                                     @id @default(autoincrement())
  password                                 String                                     @db.VarChar(255)
  personel_id                              BigInt?                                    @unique
  created_at                               DateTime?                                  @default(now()) @db.Timestamp(0)
  updated_at                               DateTime?                                  @updatedAt @db.Timestamp(0)
  deleted_at                               DateTime?                                  @db.Timestamp(0)
  api                                      api[]
  berita                                   berita[]
  logs_activity                            logs_activity[]
  notifikasi                               notifikasi[]
  satuan                                   satuan[]
  seleksi_baglekdik_selection_participants seleksi_baglekdik_selection_participants[]
  seleksi_baglekdik_selection_stages       seleksi_baglekdik_selection_stages[]
  seleksi_baglekdik_selections             seleksi_baglekdik_selections[]
  selection_bagassus                       selection_bagassus[]
  selection_baglekdik                      selection_baglekdik[]
  selection_bagrimdik                      selection_bagrimdik[]
  personel                                 personel?                                  @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "users_id_foreign")
  users_device_token                       users_device_token?
  users_jwt_token                          users_jwt_token?
  users_role                               users_role[]
  ekta                                     ekta[]
  ekta_rejection                           ekta_rejection[]
  ekta_batch                               ekta_batch[]
  ekta_batch_approval_log                  ekta_batch_approval_log[]
  ekta_batch_distribusi_log                ekta_batch_distribusi_log[]
  folder_access                            folder_access[]
  promosi_jabatan_selections_users         promosi_jabatan_selections[]               @relation("promosi_jabatan_selections_created_by_fk")
  e_kta_batch                              e_kta_batch[]                              @relation(map: "ekta_batch_users_id_fk")
  orang_asli_papua                         orang_asli_papua[]
  ukuran_fisik                             ukuran_fisik[]
}

model users_device_token {
  id         BigInt    @id(map: "users_device_token_pk") @default(autoincrement())
  user_id    BigInt    @unique(map: "users_device_token_user_id_pk_2")
  token      String    @unique(map: "users_device_token_token_pk_2") @db.VarChar(255)
  created_at DateTime? @default(now())
  updated_at DateTime? @default(now()) @updatedAt
  deleted_at DateTime?
  user       users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_device_token_users_fk")
}

model users_jwt_token {
  id         BigInt    @id(map: "users_jwt_token_pk") @default(autoincrement())
  user_id    BigInt    @unique(map: "users_jwt_token_user_id_pk")
  token      String    @db.VarChar(255)
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  user       users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_jwt_token_users_fk")
}

model users_role {
  id         BigInt    @id @default(autoincrement())
  users_id   BigInt?
  level_id   Int?
  role_id    Int?
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  level      level?    @relation(fields: [level_id], references: [id])
  role       role?     @relation(fields: [role_id], references: [id])
  user       users?    @relation(fields: [users_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_role_users_id_fk")
}

model warna_fisik {
  id                 BigInt               @id @default(autoincrement())
  nama               String               @db.VarChar()
  kategori           String               @db.VarChar()
  created_at         DateTime             @default(now()) @db.Timestamp(0)
  created_by         BigInt
  updated_at         DateTime?            @updatedAt() @db.Timestamp(0)
  updated_by         BigInt?
  deleted_at         DateTime?            @db.Timestamp(0)
  deleted_by         BigInt?
  warna_kulit_siswa  data_genetik_siswa[] @relation("data_genetik_siswa_warna_fisik_id_fk")
  warna_mata_siswa   data_genetik_siswa[] @relation("data_genetik_siswa_ukuran_fisik_id_fk_3")
  warna_rambut_siswa data_genetik_siswa[] @relation("data_genetik_siswa_warna_fisik_id_fk_2")

  @@unique([nama, kategori, deleted_at])
}

model workflow {
  id                            BigInt                 @id @default(autoincrement())
  nama                          String                 @db.VarChar(255)
  jenis_surat                   String                 @db.VarChar(50)
  klasifikasi                   String                 @db.VarChar(50)
  created_by                    BigInt
  created_at                    DateTime               @default(now()) @db.Timestamp(0)
  updated_at                    DateTime?              @updatedAt @db.Timestamp(0)
  deleted_at                    DateTime?              @db.Timestamp(0)
  created_by_personel           personel               @relation(fields: [created_by], references: [id])
  workflow_flow                 workflow_flow[]
  workflow_transaction_workflow workflow_transaction[]
}

model workflow_letter_report {
  id            BigInt                  @id @default(autoincrement())
  letter_number String
  letter_type   letter_report_type_enum
  letter_date   DateTime
  to            String
  subject       String
  tembusan      String[]
  participant   String
  created_by    BigInt
  created_at    DateTime                @default(now())
  updated_at    DateTime                @default(now()) @updatedAt
  deleted_at    DateTime?

  createdBy personel                           @relation(fields: [created_by], references: [id], onDelete: Cascade)
  document  workflow_archive_letter_document[]
}

model workflow_archive_letter {
  id            BigInt                   @id @default(autoincrement())
  letter_number String
  letter_date   DateTime
  from          String
  to            String?
  subject       String
  type          archive_letter_type_enum
  information   String?
  keyword       String?
  created_by    BigInt
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @default(now()) @updatedAt
  deleted_at    DateTime?

  createdBy personel                           @relation(fields: [created_by], references: [id], onDelete: Cascade)
  document  workflow_archive_letter_document[]
}

model workflow_archive_letter_document {
  id               BigInt                            @id @default(autoincrement())
  type             archive_letter_document_type_enum
  archive_id       BigInt?
  letter_report_id BigInt?
  originalname     String?
  encoding         String?
  mimetype         String?
  filename         String?
  size             BigInt?
  key              String?
  url              String?
  created_at       DateTime                          @default(now())
  updated_at       DateTime?                         @updatedAt
  deleted_at       DateTime?                         @db.Timestamp(0)

  archive       workflow_archive_letter? @relation(fields: [archive_id], references: [id])
  letter_report workflow_letter_report?  @relation(fields: [letter_report_id], references: [id])
}

model workflow_approval_log {
  id                           BigInt                    @id @default(autoincrement())
  approver_id                  BigInt
  workflow_transaction_flow_id BigInt
  created_at                   DateTime                  @default(now()) @db.Timestamp(0)
  updated_at                   DateTime?                 @updatedAt @db.Timestamp(0)
  personel                     personel                  @relation(fields: [approver_id], references: [id], onDelete: Cascade)
  workflow_transaction_flow    workflow_transaction_flow @relation(fields: [workflow_transaction_flow_id], references: [id], onDelete: Cascade)
}

model workflow_dokumen {
  id                      BigInt                    @id @default(autoincrement())
  workflow_transaction_id BigInt
  originalname            String?
  encoding                String?
  mimetype                String?
  destination             String?
  filename                String?
  path                    String?
  size                    BigInt?
  key                     String?
  url                     String?
  used_in_step            Int[]
  current_step            Int
  created_at              DateTime                  @default(now()) @db.Timestamp(0)
  updated_at              DateTime?                 @updatedAt @db.Timestamp(0)
  deleted_at              DateTime?                 @db.Timestamp(0)
  workflow_transaction    workflow_transaction      @relation(fields: [workflow_transaction_id], references: [id], onDelete: Cascade)
  workflow_validation_log workflow_validation_log[]
}

model workflow_flow {
  id          BigInt  @id @default(autoincrement())
  workflow_id BigInt
  step        Int
  satuan_id   BigInt?
  note        String? @db.VarChar(255)

  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  jabatan_id BigInt

  workflow               workflow            @relation(fields: [workflow_id], references: [id], onDelete: Cascade, map: "workflow_flow_id_foreign")
  workflow_flow_satuan   satuan?             @relation(fields: [satuan_id], references: [id], onDelete: Cascade)
  workflow_flow_jabatan  jabatan?            @relation(fields: [jabatan_id], references: [id], onDelete: Cascade)
  workflow_flow_personel workflow_personel[]

  @@unique([workflow_id, step])
}

model workflow_personel {
  id               BigInt    @id @default(autoincrement())
  workflow_flow_id BigInt?
  personel_id      BigInt
  tanda_tangan     Boolean   @default(false)
  paraf            Boolean   @default(false)
  created_at       DateTime  @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @updatedAt @db.Timestamp(0)

  workflow_flow     workflow_flow? @relation(fields: [workflow_flow_id], references: [id], map: "workflow_flow_id_foreign")
  workflow_personel personel       @relation(fields: [personel_id], references: [id], map: "workflow_personel_id_foreign")
}

model workflow_transaction {
  id          BigInt @id @default(autoincrement())
  personel_id BigInt
  workflow_id BigInt

  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)

  status                        workflow_status_enum?       @default(ON_PROCESS)
  current_step                  Int?                        @default(1)
  note                          String?                     @db.VarChar
  deleted_at                    DateTime?                   @db.Timestamp(6)
  created_by                    BigInt
  created_by_personel           personel?                   @relation("created_by_to_personel", fields: [created_by], references: [id], onDelete: Cascade)
  workflow_dokumen              workflow_dokumen[]
  personel                      personel?                   @relation(fields: [personel_id], references: [id], onDelete: Cascade)
  workflow_transaction_workflow workflow?                   @relation(fields: [workflow_id], references: [id], onDelete: Cascade)
  workflow_transaction_flow     workflow_transaction_flow[]
}

model workflow_transaction_flow {
  id                      BigInt                @id @default(autoincrement())
  workflow_transaction_id BigInt
  step                    Int
  satuan_id               BigInt?
  status                  workflow_status_enum? @default(WAITING)
  next_step               Int?
  sign_length             Int                   @default(0)
  must_sign_length        Int

  note       String?   @db.VarChar(255)
  created_at DateTime  @default(now()) @db.Timestamp(0)
  updated_at DateTime? @updatedAt @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  jabatan_id BigInt

  workflow_approval_log         workflow_approval_log[]
  satuan                        satuan?                         @relation(fields: [satuan_id], references: [id], onDelete: Cascade)
  jabatan                       jabatan                         @relation(fields: [jabatan_id], references: [id], onDelete: Cascade)
  workflow_transaction          workflow_transaction            @relation(fields: [workflow_transaction_id], references: [id], onDelete: Cascade)
  workflow_transaction_personel workflow_transaction_personel[]
}

model workflow_transaction_personel {
  id                           BigInt                    @id @default(autoincrement())
  workflow_transaction_flow_id BigInt
  personel_id                  BigInt
  tanda_tangan                 Boolean                   @default(false)
  paraf                        Boolean                   @default(false)
  has_paraf                    Boolean                   @default(false)
  has_tanda_tangan             Boolean                   @default(false)
  created_at                   DateTime                  @default(now())
  updated_at                   DateTime?                 @updatedAt
  order                        Int?
  deleted_at                   DateTime?                 @db.Timestamp(6)
  personel                     personel                  @relation(fields: [personel_id], references: [id], map: "workflow_personel_id_foreign")
  workflow_transaction_flow    workflow_transaction_flow @relation(fields: [workflow_transaction_flow_id], references: [id])
}

model workflow_validation_log {
  id                  BigInt    @id @default(autoincrement())
  workflow_dokumen_id BigInt
  signed_by           BigInt
  created_at          DateTime  @default(now()) @db.Timestamp(0)
  updated_at          DateTime? @updatedAt @db.Timestamp(0)

  signed_by_personel personel         @relation(fields: [signed_by], references: [id], onDelete: Cascade)
  workflow_dokumen   workflow_dokumen @relation(fields: [workflow_dokumen_id], references: [id], onDelete: Cascade)
}

enum ability_level_enum {
  PASIF
  AKTIF
}

enum api_field_type_enum {
  TEXT
  NUMBER
  PHONE_NUMBER
  DATE
  DATETIME
  BOOLEAN
  UUID
}

enum api_filter_type_enum {
  DATE_RANGE
  CONTAIN
  ICONTAIN
  EXACT
  BOOLEAN
}

enum api_method_enum {
  GET
  POST
  PUT
}

enum archive_letter_document_type_enum {
  ARCHIVE
  LETTER_REPORT
}

enum archive_letter_type_enum {
  BIASA
  RAHASIA
}

enum bahasa_jenis_enum {
  LOKAL
  INTERNASIONAL
}

enum comparison_type_enum {
  MIN
  MAX
  EQUAL
}

enum ekta_bank_enum {
  BRI
  BNI
  MANDIRI
  BTN
}

enum file_type_enum {
  FILE
  FOLDER
}

enum gender_enum {
  LAKI_LAKI @map("LAKI-LAKI")
  PEREMPUAN
}

enum gerakan_enum {
  LARI_LAP__12_MENIT___METER__  @map("LARI LAP. 12 MENIT ( METER )")
  PULL_UP_1___MENIT_____GERAK__ @map("PULL-UP 1 ( MENIT ) ( GERAK )")
  SIT___UP_1_MENIT___GERAK__    @map("SIT – UP 1 MENIT ( GERAK )")
  PUSH___UP_1_MENIT___GERAK__   @map("PUSH – UP 1 MENIT ( GERAK )")
  SHUTLE_RUN_6_X_10_M___DETIK__ @map("SHUTLE-RUN 6 X 10 M ( DETIK )")
}

enum golongan_binjas_enum {
  GOLONGAN_I__USIA_18___30_TH_   @map("GOLONGAN I (USIA 18 - 30 TH)")
  GOLONGAN_II__USIA_31___40_TH_  @map("GOLONGAN II (USIA 31 - 40 TH)")
  GOLONGAN_III__USIA_41___50_TH_ @map("GOLONGAN III (USIA 41 - 50 TH)")
}

enum input_type_enum {
  CHECKBOX
  RADIO
  TEXT
  TEXTAREA
  PASSWORD
  NUMBER
  DATE
  TIME
  EMAIL
  TEL
  URL
  FILE
  RANGE
  MULTISELECT
}

enum jenis_berita_enum {
  PENGUMUMAN
  TELEGRAM
}

enum jenis_kelamin_enum {
  LAKI_LAKI @map("LAKI-LAKI")
  PEREMPUAN
}

enum jenis_nilai_komponen_enum {
  KOMPONEN_SKT @map("Komponen_SKT")
  KOMPONEN_SKB @map("Komponen_SKB")
}

enum jenis_pengajuan_enum {
  NIKAH
  CERAI
  RUJUK
}

enum jenis_seleksi_bagrimdik_enum {
  LATSAR
  DIKBANGUM
}

enum jenis_seleksi_bagrimdik_pns_enum {
  PKA
  PKP
}

enum kategori_kerjasama_file_enum {
  MOU
  PKS
}

enum letter_report_type_enum {
  NOTA_DINAS
  SURAT_TELEGRAM
  KEPUTUSAN
}

enum live_status_enum {
  HIDUP
  MENINGGAL
}

enum mutasi_type_enum {
  ASN
  POLRI
}

enum promosi_jabatan_stage_status_enum {
  DIBUKA
  DITUTUP
  DIUNDUR
}

enum rest_method_enum {
  POST
  GET
}

enum relationship_status_enum {
  DIRI
  AYAH
  IBU
  WALI
}

enum seleksi_baglekdik_input_type_enum {
  CHECKBOX
  RADIO
  TEXT
  TEXTAREA
  PASSWORD
  NUMBER
  DATE
  TIME
  EMAIL
  TEL
  URL
  FILE
  RANGE
  MULTISELECT
}

enum seleksi_baglekdik_participant_status_enum {
  BELUM_DIPERIKSA
  SUDAH_DIPERIKSA
  DITOLAK
  REVISI
}

enum seleksi_baglekdik_stage_status_enum {
  DIBUKA
  SELEKSI_TAHAP
  DITUTUP
  DIUNDUR
}

enum seleksi_baglekdik_type_enum {
  DIKBANGUM
  DIKBANGPES
}

enum sipk_status_verifikasi_lainnya_enum {
  APPROVE
  REJECT
  WAITING
}

enum skills_category_enum {
  SOFT_SKILL @map("SOFT SKILL")
  HARD_SKILL @map("HARD SKILL")
  BAHASA     @map("BAHASA")
  TEKNIS     @map("TEKNIS")
}

enum status_beasiswa_dikum_dinas_enum {
  DINAS
  DINAS_DGN_BIAYA_PRIBADI
  DROPOUT
}

enum status_dokumen_ncr_enum {
  ACCEPTED
  REJECTED
  PROCESS
}

enum status_dokumen_seleksi_bagrimdik_pns_enum {
  BELUM_DIPERIKSA @map("Belum Diperiksa")
  DIPERIKSA       @map("Diperiksa")
  DITERIMA        @map("Diterima")
  DITOLAK         @map("Ditolak")
  REVISI          @map("Revisi")
  LENGKAP         @map("Lengkap")
}

enum status_enum {
  HIDUP
  MENINGGAL
}

enum status_import_satuan_jabatan_enum {
  BERHASIL
  GAGAL
  PENDING
}

enum status_konsultasi_enum {
  PENGAJUAN
  SEDANG_BERLANGSUNG
  MENUNGGU_HASIL
  SELESAI
}

enum status_pengajuan_enum {
  PROSES
  DISETUJUI
  DITOLAK
  SELESAI
}

enum status_pengajuan_konsultasi_enum {
  BELUM_DIPERIKSA
  SEDANG_KONSULTASI
  SELESAI
}

enum status_rekrutmen_asn_enum {
  AKTIF         @map("Aktif")
  SIAP_LATSAR   @map("Siap Latsar")
  SEDANG_LATSAR @map("Sedang Latsar")
}

enum status_rekrutmen_pppk_enum {
  AKTIF              @map("Aktif")
  KONTRAK_AKAN_HABIS @map("Kontrak akan habis")
  KADULAWARSA        @map("Kadaluwarsa")
}

enum status_seleksi_bagrimdik_pns_enum {
  LOLOS       @map("Lolos")
  TIDAK_LOLOS @map("Tidak Lolos")
}

enum type_file_document_upload_seleksi_bagrimdik_pns_enum {
  KARTU_PESERTA                 @map("Kartu Peserta")
  SKEP                          @map("Skep")
  SERTIFIKAT                    @map("Sertifikat")
  SKHP
  SURAT_REKOMENDASI             @map("Surat Rekomendasi")
  SURAT_KESANGGUPAN             @map("Surat Kesanggupan")
  PERNYATAAN_SEDANG_TIDAK_HAMIL @map("Pernyataan Sedang Tidak Hamil")
}

enum type_input_seleksi_bagrimdik_pns_enum {
  STRING @map("String")
  NUMBER @map("Number")
  MONTH  @map("Month")
  UPLOAD @map("Upload")
}

enum type_persyaratan_seleksi_bagrimdik_pns_enum {
  PANGKAT_RELATION                    @map("Pangkat Relation")
  MASA_DINAS_PERWIRA                  @map("Masa Dinas Perwira Relation")
  MASA_DINAS_DALAM_PANGKAT_RELATION   @map("Masa Dinas Dalam Pangkat Relation")
  TINGKAT_PENDIDIKAN_UMUM_RELATION    @map("Tingkat Pendidikan Umum Relation")
  AKREDITASI_PENDIDIKAN_UMUM_RELATION @map("Akreditasi Pendidikan Umum")
  UMUR                                @map("Umur")
  NILAI_KINERJA                       @map("Nilai Kinerja")
  NILAI_KESEHATAN                     @map("Nilai Kesehatan")
  NILAI_JASMANI                       @map("Nilai Jasmani")
  NILAI_ROHANI                        @map("Nilai Rohani")
  NILAI_MENTAL                        @map("Nilai Mental")
  NILAI_AKADEMIS                      @map("Nilai Akademis")
  UPLOAD_DOKUMEN                      @map("Upload Dokumen")
}

enum workflow_jenis_surat_enum {
  SURAT_MASUK
  SURAT_KELUAR
}

enum workflow_personel_status_enum {
  WAITING
  NEED_SIGN
  REJECTED
  DONE
}

enum workflow_status_enum {
  WAITING
  ON_PROCESS
  REJECTED
  DONE
}

// ================================= VIEW ======================

// ================================= MATERIALIZED VIEW ======================

model mv_atasan_satuan {
  id          BigInt  @id
  nama        String?
  nama_atasan String?

  @@map("mv_atasan_satuan")
}

model mv_detail_satuan_hirarki {
  id        Int     @id
  satuan    String
  leaf_id_1 Int?
  leaf_1    String?
  leaf_id_2 Int?
  leaf_2    String?
  leaf_id_3 Int?
  leaf_3    String?
  leaf_id_4 Int?
  leaf_4    String?
  leaf_id_5 Int?
  leaf_5    String?
  leaf_id_6 Int?
  leaf_6    String?
  leaf_id_7 Int?
  leaf_7    String?
  leaf_id_8 Int?
  leaf_8    String?

  @@map("mv_detail_satuan_hirarki")
}

model mv_detail_satuan_hirarki_old {
  id     Int     @id
  satuan String
  leaf_1 String?
  leaf_2 String?
  leaf_3 String?
  leaf_4 String?
  leaf_5 String?
  leaf_6 String?
  leaf_7 String?
  leaf_8 String?

  @@map("mv_detail_satuan_hirarki_old")
}

model mv_gelar {
  personel_id    BigInt  @unique
  gelar_depan    String?
  gelar_belakang String?

  mv_gelar_personel personel? @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_gelar_personel_id_foreign")

  @@map("mv_gelar")
}

model mv_jabatan_terakhir {
  id          Int       @id
  personel_id BigInt?
  jabatan_id  BigInt?
  jabatan     String
  tmt         DateTime?
  satuan_id   BigInt?

  mv_jabatan_terakhir_personel personel? @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_jabatan_terakhir_personel_id_foreign")
  mv_jabatan_terakhir_satuan   satuan?   @relation(fields: [satuan_id], references: [id], onDelete: Cascade, map: "mv_jabatan_terakhir_satuan_id_foreign")
  mv_jabatan_terakhir_jabatan  jabatan?  @relation(fields: [jabatan_id], references: [id], onDelete: Cascade, map: "mv_jabatan_terakhir_jabatan_id_foreign")

  @@map("mv_jabatan_terakhir")
}

model mv_kelengkapan_data {
  satker        String  @unique @db.VarChar(255)
  jumlah        BigInt
  belum_lengkap BigInt
  persentase    Decimal @db.Decimal(10, 4)

  @@map("mv_kelengkapan_data")
}

model mv_kelengkapan_data_detail {
  no           String? @db.Text
  nama         String  @db.Text
  nrp          String  @db.VarChar(255)
  pangkat      String? @db.VarChar(255)
  jabatan      String  @db.Text
  satker       String? @db.VarChar(255)
  subsatker    String? @db.VarChar(255)
  subsubsatker String? @db.VarChar(255)
  catatan      String? @db.Text
  operator     String? @db.Text

  @@unique([nrp, nama])
  @@map("mv_kelengkapan_data_detail")
}

model mv_lama_dinas {
  personel_id      BigInt @unique
  fungsi           String @db.Text
  score_lama_dinas Float  @db.DoublePrecision
  jabatan_terkini  String @db.Text

  mv_lama_dinas_personel personel? @relation("personel_to_mv_lama_dinas", fields: [personel_id], references: [id], onDelete: Cascade)

  @@map("mv_lama_dinas") // Maps to the actual table name
}

model mv_latest_jabatan_personel {
  id               BigInt    @id @default(autoincrement())
  personel_id      BigInt?   @unique
  jabatan_id       BigInt?
  satuan_id        BigInt?
  tmt_jabatan      DateTime? @db.Date
  jabatan          String?   @db.VarChar(255)
  kep_nomor        String?   @db.VarChar(255)
  kep_file         String?   @db.VarChar(255)
  is_ps            Boolean?
  keterangan       String?   @db.VarChar(255)
  st_no            String?   @db.VarChar(255)
  st_file          String?   @db.VarChar(255)
  created_at       DateTime? @default(now()) @db.Timestamp(0)
  updated_at       DateTime? @updatedAt @db.Timestamp(0)
  deleted_at       DateTime? @db.Timestamp(0)
  jabatan_sekarang jabatan?  @relation(fields: [jabatan_id], references: [id], onDelete: Cascade, map: "mv_latest_jabatan_personel_jabatan_id_foreign")
  personel         personel? @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_latest_jabatan_personel_personel_id_foreign")
  satuan           satuan?   @relation("satuan_to_satuan", fields: [satuan_id], references: [id], onDelete: Cascade)

  @@map("mv_latest_jabatan_personel")
}

model mv_mddp {
  personel_id        BigInt   @unique
  tmt_date           DateTime
  lama_menjabat_hari BigInt?

  mv_mddp_personel personel @relation(fields: [personel_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model mv_mdp {
  personel_id        BigInt   @unique
  tmt_date           DateTime
  lama_menjabat_hari BigInt?

  mv_mdp_personel personel @relation(fields: [personel_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model mv_nivellering_terakhir {
  personel_id    BigInt
  nivellering_id BigInt
  duration_day   Int?

  mv_nivellering_terakhir_personel    personel?    @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_nivellering_terakhir_personel_id_foreign")
  mv_nivellering_terakhir_nivellering nivellering? @relation(fields: [nivellering_id], references: [id], onDelete: Cascade, map: "mv_nivellering_terakhir_nivellering_id_foreign")

  @@unique([personel_id, nivellering_id])
  @@map("mv_nivellering_terakhir")
}

model mv_pangkat_terakhir {
  id                                   BigInt   @id @default(autoincrement())
  personel_id                          BigInt   @unique
  pangkat                              String   @db.VarChar(100)
  tmt                                  DateTime @db.Date
  pangkat_id                           BigInt
  mv_pangkat_terakhir_pangkat_sekarang pangkat  @relation(fields: [pangkat_id], references: [id], onDelete: Cascade, map: "mv_pangkat_terkahir_pangkat_id_foreign")
  mv_pangkat_terakhir_personel         personel @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_pangkat_terkahir_personel_id_foreign")

  @@map("mv_pangkat_terakhir")
}

model mv_personel {
  id                 Int     @id
  nrp                String
  nama               String
  pangkat            String
  jabatan            String
  pangkat_id         BigInt
  satuan_id          BigInt?
  kode_satuan_satker Int?
  hirarki_satuan     Int[] // Array type
  is_polisi          Boolean
  agama_id           Int?
  is_aktif           Boolean
  status_id          Int?

  mv_personel_pangkat pangkat? @relation("mv_personel_personel_to_personel", fields: [pangkat_id], references: [id], onDelete: Cascade)
  mv_personel_satuan  satuan?  @relation("mv_personel_satuan_to_satuan", fields: [satuan_id], references: [id], onDelete: Cascade)

  @@map("mv_personel")
}

model mv_personel_jabatan_no_gaps {
  personel_id BigInt   @unique
  tmt_date    DateTime
  tmt_year    Int
  jabatan_id  BigInt?

  mv_personel_jabatan_no_gaps_personel personel? @relation("mv_personel_jabatan_no_gaps_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)
  mv_personel_jabatan_no_gaps_jabatan  jabatan?  @relation("mv_personel_jabatan_no_gaps_jabatan_to_jabatan", fields: [jabatan_id], references: [id], onDelete: Cascade)

  @@map("mv_personel_jabatan_no_gaps")
}

model mv_personel_pangkat_jabatan_terakhir {
  personel_id BigInt
  pangkat_id  BigInt
  jabatan_id  BigInt
  satuan_id   BigInt?

  mv_personel_pangkat_jabatan_terakhir_personel personel? @relation("mv_personel_pangkat_jabatan_terakhir_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)
  mv_personel_pangkat_jabatan_terakhir_jabatan  jabatan?  @relation("mv_personel_pangkat_jabatan_terakhir_jabatan_to_jabatan", fields: [jabatan_id], references: [id], onDelete: Cascade)
  mv_personel_pangkat_jabatan_terakhir_satuan   satuan?   @relation("mv_personel_pangkat_jabatan_terakhir_satuan_to_satuan", fields: [satuan_id], references: [id], onDelete: Cascade)
  mv_personel_pangkat_jabatan_terakhir_pangkat  pangkat?  @relation("mv_personel_pangkat_jabatan_terakhir_pangkat_to_pangkat", fields: [pangkat_id], references: [id], onDelete: Cascade)

  @@unique([personel_id, pangkat_id, jabatan_id])
  @@map("mv_personel_pangkat_jabatan_terakhir")
}

model mv_personel_pangkat_no_gaps {
  personel_id BigInt
  tmt_date    DateTime @db.Date
  tmt_year    Int
  pangkat_id  BigInt

  mv_personel_pangkat_no_gaps_personel personel? @relation("mv_personel_pangkat_no_gaps_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)
  mv_personel_pangkat_no_gaps_pangkat  pangkat?  @relation("mv_personel_pangkat_no_gaps_pangkat_to_pangkat", fields: [pangkat_id], references: [id], onDelete: Cascade)

  @@unique([personel_id, pangkat_id])
  @@map("mv_personel_pangkat_no_gaps")
}

model mv_personel_score_compilation_complete {
  id                      BigInt  @id @default(autoincrement())
  personel_id             BigInt
  nrp                     String
  nama_lengkap            String
  nievelering_terkini     String?
  pangkat_terkini         String?
  jabatan_terkini         String
  fungsi                  String?
  score_nilai_smk         Float
  score_lama_dinas        Float
  score_nievelering       Float
  score_mddp              Float
  score_tahun_dikpol      Float
  score_rank_dikpol       Float
  score_dikbangspes       Float
  score_penghargaan       Float
  total_score             Float
  pangkat_terkini_singkat String

  personel personel? @relation("mv_personel_score_compilation_complete_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)

  @@map("mv_personel_score_compilation_complete")
}

model mv_personel_score_compilation_complete_kawaka {
  id                  BigInt  @id
  personel_id         BigInt
  nrp                 String
  nama_lengkap        String
  nievelering_terkini String?
  pangkat_terkini     String?
  jabatan_terkini     String
  score_nilai_smk     Float
  score_lama_dinas    Float
  score_nievelering   Float
  score_mddp          Float
  score_tahun_dikpol  Float
  score_rank_dikpol   Float
  score_dikbangspes   Float
  score_penghargaan   Float
  total_score         Float

  mv_personel_score_compilation_complete_kawaka_personel personel? @relation("mv_personel_score_compilation_complete_kawaka_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)

  @@map("mv_personel_score_compilation_complete_kawaka")
}

model mv_personel_score_compilation_complete_kawaka_fungsi {
  id                      BigInt  @id
  personel_id             BigInt
  nrp                     String
  nama_lengkap            String
  nievelering_terkini     String?
  pangkat_terkini         String?
  fungsi                  String?
  tempat_dinas            String
  jabatan_terkini         String
  lokasi_kerja            String?
  score_nilai_smk         Float
  score_lama_dinas        Float
  score_nievelering       Float
  score_mddp              Float
  score_tahun_dikpol      Float
  score_rank_dikpol       Float
  score_dikbangspes       Float
  score_penghargaan       Float
  total_score             Float
  pangkat_terkini_singkat String

  mv_personel_score_compilation_complete_kawaka_fungsi_personel personel? @relation("mv_personel_score_compilation_complete_kawaka_fungsi_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)

  @@map("mv_personel_score_compilation_complete_kawaka_fungsi")
}

model mv_polisi {
  personel_id             BigInt
  nievelering             String @db.VarChar(255)
  pangkat                 String @db.VarChar(255)
  pangkat_terkini_singkat String @db.VarChar(255)
  nrp                     String @db.VarChar(255)
  nama_lengkap            String @db.VarChar(255)

  mv_polisi_personel personel? @relation("mv_polisi_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)

  @@unique([personel_id, nievelering, pangkat])
  @@map("mv_polisi") // Maps to the actual table name
}

model mv_ranked_jabatan {
  personel_id BigInt
  tmt_jabatan DateTime @db.Date
  jabatan_id  BigInt
  rn          BigInt

  mv_ranked_jabatan_personel personel? @relation("mv_ranked_jabatan_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)
  mv_ranked_jabatan_jabatan  jabatan?  @relation("mv_ranked_jabatan_jabatan_to_jabatan", fields: [jabatan_id], references: [id], onDelete: Cascade)

  @@unique([personel_id, jabatan_id])
  @@map("mv_ranked_jabatan") // Maps to the actual table name
}

model mv_ranked_pangkat {
  personel_id BigInt
  pangkat_id  BigInt
  tmt         String @db.VarChar(255)
  rn          BigInt

  mv_ranked_pangkat_personel personel? @relation("mv_ranked_pangkat_personel_to_personel", fields: [personel_id], references: [id], onDelete: Cascade)
  mv_ranked_pangkat_pangkat  pangkat?  @relation("mv_ranked_pangkat_pangkat_to_pangkat", fields: [pangkat_id], references: [id], onDelete: Cascade)

  @@unique([personel_id, pangkat_id])
  @@map("mv_ranked_pangkat") // Maps to the actual table name
}

model mv_satuan_hirarki {
  satuan_id BigInt
  atasan_id BigInt[]
  jenis_id  Int[]

  mv_satuan_hirarki_atasan satuan? @relation("mv_satuan_hirarki_atasan_to_satuan", fields: [atasan_id], references: [id], onDelete: Cascade)
  mv_satuan_hirarki_satuan satuan? @relation("mv_satuan_hirarki_satuan_to_satuan", fields: [satuan_id], references: [id], onDelete: Cascade)

  @@unique([satuan_id, atasan_id, jenis_id])
  @@map("mv_satuan_hirarki")
}

model mv_satuan_with_top_parents {
  id                                           BigInt  @unique
  nama                                         String? @db.VarChar(255)
  atasan_id                                    BigInt?
  top_parent_id                                BigInt?
  second_top_parent_id                         BigInt?
  third_top_parent_id                          BigInt?
  top_parent_nama                              String? @db.VarChar(255)
  second_top_parent_nama                       String? @db.VarChar(255)
  third_top_parent_nama                        String? @db.VarChar(255)
  mv_satuan_with_top_parents_atasan            satuan? @relation("atasan_to_mv_satuan_with_top_parents", fields: [atasan_id], references: [id], onDelete: Cascade, map: "mv_satuan_with_top_parents_atasan_id_foreign")
  mv_satuan_with_top_parents_second_top_parent satuan? @relation("second_top_parent_to_mv_satuan_with_top_parents", fields: [second_top_parent_id], references: [id], onDelete: Cascade, map: "mv_satuan_with_top_parents_second_top_parent_id_foreign")
  mv_satuan_with_top_parents_self              satuan  @relation("self_to_mv_satuan_with_top_parents", fields: [id], references: [id], map: "mv_satuan_with_top_parents_self_id_foreign")
  mv_satuan_with_top_parents_third_top_parent  satuan? @relation("third_top_parent_to_mv_satuan_with_top_parents", fields: [third_top_parent_id], references: [id], onDelete: Cascade, map: "mv_satuan_with_top_parents_third_top_parent_id_foreign")
  mv_satuan_with_top_parents_top_parent        satuan? @relation("top_parent_to_mv_satuan_with_top_parents", fields: [top_parent_id], references: [id], onDelete: Cascade, map: "mv_satuan_with_top_parents_top_parent_id_foreign")

  @@map("mv_satuan_with_top_parents")
}

model mv_statistik_satker {
  second_top_parent_id BigInt
  kategori_id          BigInt
  count                BigInt
  second_top_parent    satuan           @relation(fields: [second_top_parent_id], references: [id], onDelete: Cascade, map: "second_top_parent_id_foreign")
  kategori             pangkat_kategori @relation(fields: [kategori_id], references: [id], onDelete: Cascade, map: "second_top_parent_pangkat_id_foreign")

  @@unique([second_top_parent_id, kategori_id])
  @@map("mv_statistik_satker")
}

model mv_statistik_satker_old {
  second_top_parent_id BigInt
  kategori_id          BigInt
  count                BigInt

  mv_statistik_satker_old_second_top_parent satuan?           @relation("mv_statistik_satker_old_second_top_parent_to_satuan", fields: [second_top_parent_id], references: [id], onDelete: Cascade)
  mv_statistik_satker_old_kategori          pangkat_kategori? @relation("mv_statistik_satker_old_kategori_to_pangkat_kategori", fields: [kategori_id], references: [id], onDelete: Cascade)

  @@unique([second_top_parent_id, kategori_id])
  @@map("mv_statistik_satker_old") // Maps to the actual table name
}

// [TABLE CONFIRMATION]
model personel_kemampuan_bahasa {
  id          BigInt    @id @default(autoincrement())
  bahasa      String    @db.VarChar(150)
  status      String    @db.VarChar
  personel_id BigInt?
  created_at  DateTime  @default(now())
  updated_at  DateTime? @db.Timestamp(0)

  personel personel? @relation("personel_to_personel", fields: [personel_id], references: [id])
}

model bagassus_seleksi_syarat {
  id           BigInt                          @id @default(autoincrement())
  nama         String                          @unique
  created_at   DateTime                        @default(now())
  updated_at   DateTime?                       @db.Timestamp(0)
  syarat_value bagassus_seleksi_syarat_value[]
}

model bagassus_seleksi_syarat_value {
  id         BigInt                  @id @default(autoincrement())
  nama       String                  @unique
  syarat_id  BigInt
  created_at DateTime                @default(now())
  updated_at DateTime?               @db.Timestamp(0)
  syarat     bagassus_seleksi_syarat @relation(fields: [syarat_id], references: [id])
}

model bagassus_seleksi_personel {
  id          BigInt           @id @default(autoincrement())
  seleksi_id  BigInt
  personel_id BigInt
  created_at  DateTime         @default(now())
  updated_at  DateTime?        @db.Timestamp(0)
  seleksi     bagassus_seleksi @relation(fields: [seleksi_id], references: [id])
  personel    personel         @relation(fields: [personel_id], references: [id])
}

model personel_sign {
  id           BigInt    @id @default(autoincrement())
  personel_id  BigInt
  originalname String?
  encoding     String?
  mimetype     String?
  destination  String?
  filename     String?
  path         String?
  size         BigInt?
  key          String?
  url          String?
  qr_data      String?   @db.Text
  created_at   DateTime  @default(now())
  updated_at   DateTime? @db.Timestamp(0)

  personel personel @relation(fields: [personel_id], references: [id], onDelete: Cascade)

  @@index([personel_id])
}

model konsentrasi {
  id                  BigInt              @id @default(autoincrement())
  nama                String              @db.VarChar(255)
  is_aktif            Boolean             @default(true)
  created_at          DateTime?           @default(now()) @db.Timestamp(0)
  updated_at          DateTime?           @db.Timestamp(0)
  deleted_at          DateTime?           @db.Timestamp(0)
  konsentrasi_jurusan dikum_konsentrasi[]
}

model dikum_konsentrasi {
  id              BigInt    @id @default(autoincrement())
  konsentrasi_id  BigInt?
  dikum_detail_id BigInt?
  verified        Boolean
  created_at      DateTime? @default(now()) @db.Timestamp(0)
  updated_at      DateTime? @db.Timestamp(0)
  deleted_at      DateTime? @db.Timestamp(0)

  konsentrasi  konsentrasi?  @relation(fields: [konsentrasi_id], references: [id])
  dikum_detail dikum_detail? @relation(fields: [dikum_detail_id], references: [id])
}

model dikum_detail {
  id           BigInt    @id @default(autoincrement())
  institusi_id BigInt?
  dikum_id     BigInt?
  jurusan_id   BigInt?
  gelar_id     BigInt?
  is_aktif     Boolean   @default(true)
  created_at   DateTime? @default(now()) @db.Timestamp(0)
  updated_at   DateTime? @updatedAt @db.Timestamp(0)
  deleted_at   DateTime? @db.Timestamp(0)

  institusi         institusi?          @relation(fields: [institusi_id], references: [id])
  dikum             dikum?              @relation(fields: [dikum_id], references: [id])
  jurusan           jurusan?            @relation(fields: [jurusan_id], references: [id])
  gelar             gelar?              @relation(fields: [gelar_id], references: [id])
  dikum_konsentrasi dikum_konsentrasi[]
  dikum_personel    dikum_personel[]
}
