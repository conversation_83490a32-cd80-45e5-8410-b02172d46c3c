model pengajuan_cuti {
  id                                     BigInt    @id @default(autoincrement())
  personel_id                            BigInt?
  satuan                                 String?
  jenis_cuti                             String?
  tanggal_mulai                          DateTime? @db.Date
  tanggal_akhir                          DateTime? @db.Date
  durasi                                 String?
  status                                 String?
  alasan_cuti                            String?
  keterangan                             String?
  dokumen                                <PERSON>?     @db.<PERSON><PERSON>
  created_at                             DateTime? @default(now()) @db.Date
  updated_at                             DateTime? @updatedAt @db.Date
  deleted_at                             DateTime? @db.Date
  alasan_penolakan                       String?
  pengampu_personel_id                   BigInt?
  alamat_cuti                            String?
  skep_cuti                              String?
  usulan_kasatfung_file                  String?
  surat_berdinas_file                    String?
  surat_penugasan_file                   String?
  sket_dokter_file                       String?
  pengajuan_cuti_pengampu_pengajuan_cuti personel? @relation("pengajuan_cuti_pengampu_to_pengajuan_cuti", fields: [pengampu_personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_cuti_pengampu_personel_fk")
  pengajuan_cuti_personel                personel? @relation("personel_to_personel", fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_cuti_personel_fk")
}