model penghargaan {
  id                    BigInt                  @id @default(autoincrement())
  nama                  String                  @db.VarChar(255)
  is_aktif              <PERSON>?                @default(true)
  created_at            DateTime?               @default(now()) @db.Timestamp(0)
  updated_at            DateTime?               @updatedAt @db.Timestamp(0)
  deleted_at            DateTime?               @db.Timestamp(0)
  tingkat_id            BigInt?
  is_validated          Boolean?
  keterangan            String?                 @db.VarChar(255)
  pengajuan_penghargaan pengajuan_penghargaan[]
  penghargaan_tingkat   penghargaan_tingkat?    @relation(fields: [tingkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "penghargaan_penghargaan_tingkat_id_fk")
  penghargaan_personel  penghargaan_personel[]
  sipk_penghargaan      sipk_penghargaan[]
}

model penghargaan_personel {
  id                    BigInt                  @id @default(autoincrement())
  personel_id           BigInt
  penghargaan_id        BigInt
  tingkat_id            BigInt
  tgl_penghargaan       DateTime?               @db.Date
  surat_no              String?                 @db.VarChar(255)
  surat_file            String?                 @db.VarChar(255)
  penghargaan_file      String?                 @db.VarChar(255)
  tanggal_perubahan     DateTime?               @db.Timestamp(0)
  created_at            DateTime?               @default(now()) @db.Timestamp(0)
  updated_at            DateTime?               @updatedAt @db.Timestamp(0)
  deleted_at            DateTime?               @db.Timestamp(0)
  note                  String?
  pengajuan_penghargaan pengajuan_penghargaan[]
  penghargaan           penghargaan             @relation(fields: [penghargaan_id], references: [id], onDelete: Cascade, map: "penghargaan_personel_penghargaan_id_foreign")
  penghargaan_tingkat   penghargaan_tingkat     @relation(fields: [tingkat_id], references: [id], onDelete: Cascade, map: "penghargaan_personel_tingkat_id_foreign")
  personel              personel                @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "penghargaan_personel_personel_id_foreign")
}

model penghargaan_tingkat {
  id                    BigInt                  @id @default(autoincrement())
  nama                  String                  @db.VarChar(255)
  is_aktif              Boolean?                @default(true)
  created_at            DateTime?               @default(now()) @db.Timestamp(0)
  updated_at            DateTime?               @updatedAt @db.Timestamp(0)
  deleted_at            DateTime?               @db.Timestamp(0)
  sipk_poin             Int?
  pengajuan_penghargaan pengajuan_penghargaan[]
  penghargaan           penghargaan[]
  penghargaan_personel  penghargaan_personel[]
}

model pengajuan_penghargaan {
  id                      BigInt                @id @default(autoincrement())
  personel_id             BigInt?
  penghargaan_id          BigInt?
  status                  String?
  files                   Json[]                @db.Json
  created_by              BigInt?
  approved_by             BigInt?
  rejected_by             BigInt?
  created_at              DateTime?             @default(now()) @db.Date
  updated_at              DateTime?             @updatedAt @db.Date
  approved_at             DateTime?             @db.Date
  rejected_at             DateTime?             @db.Date
  penghargaan_personel_id BigInt?
  tingkat_id              BigInt?
  kep_penghargaan         String?
  alasan_penolakan        String?
  nomor_surat_kep         String?               @db.VarChar
  personel                personel?             @relation(fields: [personel_id], references: [id])
  penghargaan             penghargaan?          @relation(fields: [penghargaan_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_penghargaan_personel_penghargaan_id_foreign")
  penghargaan_personel    penghargaan_personel? @relation(fields: [penghargaan_personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_penghargaan_personel_penghargaan_personel_id_foreign")
  penghargaan_tingkat     penghargaan_tingkat?  @relation(fields: [tingkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_penghargaan_personel_penghargaan_tingkat_id_foreign")
}