model kgb {
  id          BigInt    @id(map: "kgb_pk") @default(autoincrement())
  personel_id BigInt
  tmt         DateTime? @db.Date
  kep_nomor   String?   @db.VarChar(255)
  kep_file    String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)
  personel    personel  @relation(fields: [personel_id], references: [id], map: "kgb_personel_id_foreign")
}

model kgb_gaji_pangkat {
  id         BigInt   @id(map: "kgb_gaji_pangkat_pk") @default(autoincrement())
  pangkat_id BigInt
  mkg        Int
  gaji       BigInt
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @updatedAt @db.Timestamp(6)
  pangkat    pangkat  @relation(fields: [pangkat_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "kgb_gaji_pangkat_pangkat_id_foreign")
}

model kgb_personel {
  id          BigInt    @id @default(autoincrement())
  personel_id BigInt
  tmt         DateTime? @db.Date
  kep_nomor   String?   @db.VarChar(255)
  kep_file    String?   @db.VarChar(255)
  created_at  DateTime? @default(now()) @db.Timestamp(0)
  updated_at  DateTime? @updatedAt @db.Timestamp(0)
  deleted_at  DateTime? @db.Timestamp(0)

  gaji_pokok_baru        BigInt?
  tanggal_kgb_berikutnya DateTime?

  personel        personel          @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "kgb_personel_id_foreign")
  mv_kgb_terakhir mv_kgb_terakhir[] @relation(map: "mv_kgb_terkahir_kgb_personel_id_foreign")
}

model mv_kgb_terakhir {
  personel_id     BigInt
  kgb_terakhir_id BigInt?

  tmt_kgb_terakhir DateTime?
  tmt_awal         DateTime?

  kgb_selanjutnya DateTime?

  personel     personel      @relation(fields: [personel_id], references: [id], onDelete: Cascade, map: "mv_kgb_terkahir_personel_id_foreign")
  kgb_personel kgb_personel? @relation(fields: [kgb_terakhir_id], references: [id], onDelete: Cascade, map: "mv_kgb_terkahir_kgb_personel_id_foreign")

  @@unique([personel_id])
  @@unique([kgb_terakhir_id])
}