model pengajuan_pengakhiran_dinas {
  id                                                    BigInt    @id @default(autoincrement())
  personel_id                                           BigInt?
  pengakhiran_dinas                                     String?
  status_pengajuan                                      String?
  surat_kkep_file                                       String?
  tanggal_surat                                         DateTime? @db.Date
  tanggal_diterima                                      DateTime? @db.Date
  kasus                                                 String?
  dokumen_putusan_kkep_nama                             String?
  dokumen_putusan_kkep_file                             String?
  dokumen_putusan_kkep_date                             DateTime? @db.Date
  dokumen_putusan_kkep_status                           String?
  dokumen_putusan_kkep_alasan_penolakan                 String?
  dokumen_surat_usulan_nama                             String?
  dokumen_surat_usulan_file                             String?
  dokumen_surat_usulan_date                             DateTime? @db.Date
  dokumen_surat_usulan_status                           String?
  dokumen_surat_usulan_alasan_penolakan                 String?
  dokumen_berkas_pemeriksaan_nama                       String?
  dokumen_berkas_pemeriksaan_file                       String?
  dokumen_berkas_pemeriksaan_date                       DateTime? @db.Date
  dokumen_berkas_pemeriksaan_status                     String?
  dokumen_berkas_pemeriksaan_alasan_penolakan           String?
  dokumen_kep_pengangkatan_pertama_nama                 String?
  dokumen_kep_pengangkatan_pertama_file                 String?
  dokumen_kep_pengangkatan_pertama_date                 DateTime? @db.Date
  dokumen_kep_pengangkatan_pertama_status               String?
  dokumen_kep_pengangkatan_pertama_alasan_penolakan     String?
  dokumen_keputusan_pangkat_terakhir_nama               String?
  dokumen_keputusan_pangkat_terakhir_file               String?
  dokumen_keputusan_pangkat_terakhir_date               DateTime? @db.Date
  dokumen_keputusan_pangkat_terakhir_status             String?
  dokumen_keputusan_pangkat_terakhir_alasan_penolakan   String?
  dokumen_kartu_tanda_peserta_nama                      String?
  dokumen_kartu_tanda_peserta_file                      String?
  dokumen_kartu_tanda_peserta_date                      DateTime? @db.Date
  dokumen_kartu_tanda_peserta_status                    String?
  dokumen_kartu_tanda_peserta_alasan_penolakan          String?
  dokumen_surat_keterangan_tidak_layak_nama             String?
  dokumen_surat_keterangan_tidak_layak_file             String?
  dokumen_surat_keterangan_tidak_layak_date             DateTime? @db.Date
  dokumen_surat_keterangan_tidak_layak_status           String?
  dokumen_surat_keterangan_tidak_layak_alasan_penolakan String?
  dokumen_putusan_pengadilan_nama                       String?
  dokumen_putusan_pengadilan_file                       String?
  dokumen_putusan_pengadilan_date                       DateTime? @db.Date
  dokumen_putusan_pengadilan_status                     String?
  dokumen_putusan_pengadilan_alasan_penolakan           String?
  dokumen_pengambilan_barang_nama                       String?
  dokumen_pengambilan_barang_file                       String?
  dokumen_pengambilan_barang_date                       DateTime? @db.Date
  dokumen_pengambilan_barang_status                     String?
  dokumen_pengambilan_barang_alasan_penolakan           String?
  dokumen_kkep_tingkat_banding_nama                     String?
  dokumen_kkep_tingkat_banding_file                     String?
  dokumen_kkep_tingkat_banding_date                     DateTime? @db.Date
  dokumen_kkep_tingkat_banding_status                   String?
  dokumen_kkep_tingkat_banding_alasan_penolakan         String?
  dokumen_bap_nama                                      String?
  dokumen_bap_file                                      String?
  dokumen_bap_date                                      DateTime? @db.Date
  dokumen_bap_status                                    String?
  dokumen_bap_alasan_penolakan                          String?
  dokumen_ptdh_nama                                     String?
  dokumen_ptdh_file                                     String?
  dokumen_ptdh_date                                     DateTime? @db.Date
  dokumen_ptdh_status                                   String?
  dokumen_ptdh_alasan_penolakan                         String?
  created_at                                            DateTime? @default(now()) @db.Date
  updated_at                                            DateTime? @updatedAt @db.Date
  deleted_at                                            DateTime? @db.Date
  dokumen_petikan_ptdh_nama                             String?
  dokumen_petikan_ptdh_file                             String?
  dokumen_petikan_ptdh_date                             DateTime? @db.Date
  dokumen_petikan_ptdh_status                           String?
  dokumen_petikan_ptdh_alasan_penolakan                 String?
  dokumen_salinan_ptdh_nama                             String?
  dokumen_salinan_ptdh_file                             String?
  dokumen_salinan_ptdh_date                             DateTime? @db.Date
  dokumen_salinan_ptdh_status                           String?
  dokumen_salinan_ptdh_alasan_penolakan                 String?
  completed_at                                          DateTime? @db.Date
  submit_deadline_date                                  DateTime? @db.Date
  pengajuan_pengakhiran_dinas_personel                  personel? @relation("personel_to_personel", fields: [personel_id], references: [id], map: "pengajuan_pengakhiran_dinas_personel_id_foreign")
}

model pengajuan_pengakhiran_dinas_bup {
  id                                                BigInt    @id(map: "pengajuan_pengakhiran_dinas_bup_pk") @default(autoincrement())
  personel_id                                       BigInt?
  status                                            String?   @db.VarChar
  tanggal_pengajuan                                 DateTime? @db.Date
  tanggal_waktu_ditolak                             DateTime? @db.Timestamp(6)
  tanggal_waktu_diterima                            DateTime? @db.Timestamp(6)
  diterima_oleh                                     BigInt?
  detail_penolakan                                  Json?     @db.Json
  skep_pensiun_file                                 String?
  skep_pensiun_no                                   String?
  created_at                                        DateTime? @default(now()) @db.Timestamp(6)
  updated_at                                        DateTime? @updatedAt @db.Timestamp(6)
  pengajuan_pengakhiran_dinas_bup_personel          personel? @relation("personel_to_personel", fields: [personel_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_pengakhiran_dinas_bup_personel_fk")
  pengajuan_pengakhiran_dinas_bup_personel_diterima personel? @relation("personel_diterima_to_personel", fields: [diterima_oleh], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "pengajuan_pengakhiran_dinas_bup_personel_fk_1")
}