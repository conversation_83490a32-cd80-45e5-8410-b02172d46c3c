model data_siswa {
  id                              BigInt             @id @default(autoincrement())
  unique_id                       String             @db.Char(13)
  pers_id                         BigInt?
  username                        String?            @unique @db.Char(15)
  password                        String?            @db.Char(6)
  nama                            String
  nama_panggilan                  String?
  gelar_id                        BigInt?
  nama_dengan_gelar               String
  jenis_kelamin                   jenis_kelamin_enum
  tempat_lahir                    String
  tanggal_lahir                   DateTime           @db.Date
  nrp                             String?
  nik                             String             @unique @db.Char(16)
  jenis_pekerjaan                 String
  status_kawin_id                 BigInt
  no_ujian_polda                  String             @unique @db.VarChar(50)
  no_registrasi_online            String             @unique @db.VarChar(50)
  no_ak_nosis                     String?            @db.VarChar(30)
  jenis_diktuk_id                 BigInt
  ta_rim_diktuk                   String             @db.VarChar(6)
  gelombang_rim_diktuk            String             @db.VarChar(6)
  kompetensi_diktuk_id            BigInt
  sub_kompetensi_diktuk_id        BigInt?
  sub_sub_kompetensi_diktuk_id    BigInt?
  ket_jalur_rekpro                String?
  orang_asli_papua_id             BigInt
  tmp_dik                         BigInt
  asal_rim_polda_id               BigInt
  asal_rim_polres_id              BigInt
  ta_pat_diktuk                   String             @db.VarChar(6)
  gelombang_pat_diktuk            String             @db.VarChar(6)
  created_at                      DateTime           @default(now())
  updated_at                      DateTime?
  deleted_at                      DateTime?
  ijazah_dikum_gun_seleksi_rim_id BigInt?

  gelar                        gelar?                 @relation(fields: [gelar_id], references: [id])
  jenis_diktuk                 jenis_diktuk           @relation(fields: [jenis_diktuk_id], references: [id])
  status_kawin                 status_kawin           @relation(fields: [status_kawin_id], references: [id])
  tempat_pendidikan            tmpdik                 @relation(fields: [tmp_dik], references: [id])
  asal_rim_polda               satuan                 @relation("data_siswa_satuan_id_fk", fields: [asal_rim_polda_id], references: [id])
  asal_rim_polres              satuan                 @relation("data_siswa_satuan_id_fk_2", fields: [asal_rim_polres_id], references: [id])
  kompetensi_diktuk            kompetensi_diktuk      @relation("data_siswa_kompetensi_diktuk_id_fk", fields: [kompetensi_diktuk_id], references: [id])
  sub_kompetensi_diktuk        kompetensi_diktuk?     @relation("data_siswa_kompetensi_diktuk_id_fk_2", fields: [sub_kompetensi_diktuk_id], references: [id])
  sub_sub_kompetensi_diktuk    kompetensi_diktuk?     @relation("data_siswa_kompetensi_diktuk_id_fk_3", fields: [sub_sub_kompetensi_diktuk_id], references: [id])
  ijazah_dikum_gun_seleksi_rim dikum?                 @relation(fields: [ijazah_dikum_gun_seleksi_rim_id], references: [id])
  orang_asli_papua             orang_asli_papua       @relation(fields: [orang_asli_papua_id], references: [id])
  rekrutmen                    data_rekrutmen_siswa[]
  keluarga                     data_keluarga_siswa[]
  keahlian                     data_keahlian_siswa[]
  hobi                         data_hobi_siswa[]
  dikum                        data_dikum_siswa[]
  genetik                      data_genetik_siswa?
  alamat                       data_alamat_siswa?
  validation                   data_validation_siswa?
  document                     data_document_siswa?
  jasdiktuk                    data_jasdiktuk_siswa?
}

model data_rekrutmen_siswa {
  id                                Int       @id @default(autoincrement())
  siswa_id                          BigInt
  type_rekrutmen_id                 Int
  hasil_lari_12_m_ukj               Decimal?
  n_a_lari_12_menit_ukj             Decimal?
  hasil_pull_up_ukj                 Decimal?
  n_pull_up_ukj                     Decimal?
  hasil_sit_up_ukj                  Decimal?
  n_sit_up_ukj                      Decimal?
  hasil_push_up_ukj                 Decimal?
  n_push_up_ukj                     Decimal?
  n_shuttle_run_ukj                 Decimal?
  hasil_shuttle_run_ukj             Decimal?
  rata2_n_b_ukj                     Decimal?
  n_ab_ukj                          Decimal?
  jarak_renang_meter_ukj            Decimal?
  waktu_renang_detik_ukj            Decimal?
  n_renang_ukj                      Decimal?
  n_kualitatif_antro_ukj            String?   @db.VarChar(150)
  n_kuantitatif_antro_ukj           Decimal?
  kelainan_antro_ukj                String?
  n_kualitatif_akhir_ukj            String?   @db.VarChar(150)
  n_kuantitatif_akhir_ukj           Decimal?
  ket_ukj                           String?
  n_kualitatif_rikpsi_1             String?   @db.VarChar(150)
  n_kuantitatif_rikpsi_1            Decimal?
  ket_rikpsi_1                      String?
  n_kualitatif_rikpsi_2             String?   @db.VarChar(150)
  n_kuantitatif_rikpsi_2            Decimal?
  temuan_rikpsi_2                   String?
  ket_rikpsi_2                      String?
  n_kualitatif_rikmin_awal          String?   @db.VarChar(150)
  ket_rikmin_awal                   String?
  n_kualitatif_rikmin_akhir         String?   @db.VarChar(150)
  ket_rikmin_akhir                  String?
  n_kualitatif_rikkes_1             String?   @db.VarChar(150)
  n_kuantitatif_rikkes_1            Decimal?
  diagnosa_rikkes_1                 String?
  n_kualitatif_rikkes_2             String?   @db.VarChar(150)
  n_kuantitatif_rikkes_2            Decimal?
  diagnosa_rikkes_2                 String?
  n_kualitatif_pmk                  String?   @db.VarChar(150)
  n_kuantitatif_pmk                 Decimal?
  temuan_pmk                        String?
  ket_pmk                           String?
  n_kuantitatif_rikpil              Decimal?
  data_deteksi_dini_densus          String?
  prestasi_seleksi_rim              String?
  catatan_khusus_seleksi_rim        String?
  n_tpa_akademik                    Decimal?
  n_peng_u_akademik                 Decimal?
  n_wwsn_k_akademik                 Decimal?
  n_mtk_akademik                    Decimal?
  n_b_ing_akademik                  Decimal?
  n_b_ind_akademik                  Decimal?
  n_tkk_pengetahuan_sipss_akademik  Decimal?
  n_tkk_keterampilan_sipss_akademik Decimal?
  n_tkk_perilaku_sipss_akademik     Decimal?
  n_gbg_akhir_akademik              Decimal?
  ket_akademik                      String?
  created_at                        DateTime  @default(now())
  updated_at                        DateTime?
  deleted_at                        DateTime?

  siswa            data_siswa       @relation(fields: [siswa_id], references: [id])
  recruitment_type recruitment_type @relation(fields: [type_rekrutmen_id], references: [id])
}

model data_validation_siswa {
  id              BigInt  @id @default(autoincrement())
  siswa_id        BigInt  @unique
  is_validation   Boolean @default(false)
  is_upload       Boolean @default(false)
  is_angket       Boolean @default(false)
  is_generate_nrp Boolean @default(false)
  is_verified     Boolean @default(false)
  is_verified_nrp Boolean @default(false)
  diktuk          BigInt?

  siswa         data_siswa     @relation(fields: [siswa_id], references: [id])
  status_diktuk status_diktuk? @relation(fields: [diktuk], references: [id])
}

model status_diktuk {
  id         BigInt    @id @default(autoincrement())
  nama       String    @unique
  created_by BigInt?
  created_at DateTime  @default(now())
  updated_by BigInt?
  updated_at DateTime?
  deleted_by String?
  deleted_at DateTime?

  validation data_validation_siswa[]
}

model data_keluarga_siswa {
  id                 Int               @id @default(autoincrement())
  siswa_id           BigInt
  status_hubungan_id BigInt
  agama_id           BigInt
  suku_id            BigInt
  nama               String
  pekerjaan          String?
  jabatan            String?
  umur               Int
  status_hidup       live_status_enum?
  anak_ke            Int?
  gol_pangkat        String?
  email              String?
  no_hp              String?           @db.Char(13)
  medsos_instagram   String?
  medsos_facebook    String?
  medsos_twitter     String?
  keterangan_lain    String?
  created_at         DateTime          @default(now())
  updated_at         DateTime?
  deleted_at         DateTime?

  siswa    data_siswa        @relation(fields: [siswa_id], references: [id])
  suku     suku              @relation(fields: [suku_id], references: [id])
  agama    agama             @relation(fields: [agama_id], references: [id])
  hubungan hubungan_keluarga @relation(fields: [status_hubungan_id], references: [id])
}

model data_keahlian_siswa {
  id                Int                  @id @default(autoincrement())
  siswa_id          BigInt
  nama_keahlian     String
  kategori_keahlian skills_category_enum
  tingkat_kemampuan ability_level_enum
  tahun_pengalaman  Decimal?
  deskripsi         String?
  created_at        DateTime             @default(now())
  updated_at        DateTime?
  deleted_at        DateTime?

  siswa data_siswa @relation(fields: [siswa_id], references: [id])
}

model data_hobi_siswa {
  id         Int       @id @default(autoincrement())
  siswa_id   BigInt
  hobi_id    BigInt
  created_at DateTime  @default(now())
  updated_at DateTime?
  deleted_at DateTime?

  siswa data_siswa @relation(fields: [siswa_id], references: [id])
  hobi  hobi       @relation(fields: [hobi_id], references: [id])
}

model data_genetik_siswa {
  id                Int       @id @default(autoincrement())
  siswa_id          BigInt    @unique
  tinggi_badan_cm   Decimal?
  berat_badan_kg    Decimal?
  warna_kulit_id    BigInt?
  warna_mata_id     BigInt?
  warna_rambut_id   BigInt?
  jenis_rambut_id   BigInt?
  golongan_darah_id BigInt?
  ukuran_topi_id    BigInt?
  ukuran_celana     Decimal?
  ukuran_baju_id    BigInt?
  ukuran_sepatu     Decimal?
  created_at        DateTime  @default(now())
  updated_at        DateTime?
  deleted_at        DateTime?

  siswa          data_siswa      @relation(fields: [siswa_id], references: [id])
  ukuran_topi    ukuran_fisik?   @relation("data_genetik_siswa_ukuran_fisik_id_fk", fields: [ukuran_topi_id], references: [id])
  ukuran_baju    ukuran_fisik?   @relation("data_genetik_siswa_ukuran_fisik_id_fk_2", fields: [ukuran_baju_id], references: [id])
  warna_kulit    warna_fisik?    @relation("data_genetik_siswa_warna_fisik_id_fk", fields: [warna_kulit_id], references: [id])
  warna_mata     warna_fisik?    @relation("data_genetik_siswa_ukuran_fisik_id_fk_3", fields: [warna_mata_id], references: [id])
  warna_rambut   warna_fisik?    @relation("data_genetik_siswa_warna_fisik_id_fk_2", fields: [warna_rambut_id], references: [id])
  jenis_rambut   jenis_rambut?   @relation("data_genetik_siswa_jenis_rambut_id_fk", fields: [jenis_rambut_id], references: [id])
  golongan_darah golongan_darah? @relation("data_genetik_siswa_golongan_darah_id_fk", fields: [golongan_darah_id], references: [id])
}

model data_dikum_siswa {
  id                    Int       @id @default(autoincrement())
  siswa_id              BigInt
  dikum_id              BigInt
  institusi_id          BigInt
  provinsi_institusi_id BigInt
  tahun_lulus           String    @db.VarChar(10)
  nilai                 Decimal?
  jurusan_id            BigInt?
  rata2_nilai_rapot     Decimal?
  akreditasi_banpt_id   BigInt?
  gun_seleksi_rim       Boolean   @default(false)
  created_at            DateTime  @default(now())
  updated_at            DateTime?
  deleted_at            DateTime?

  siswa              data_siswa  @relation(fields: [siswa_id], references: [id])
  dikum              dikum       @relation(fields: [dikum_id], references: [id])
  institusi          institusi   @relation(fields: [institusi_id], references: [id])
  provinsi_institusi provinsi    @relation(fields: [provinsi_institusi_id], references: [id])
  jurusan            jurusan?    @relation(fields: [jurusan_id], references: [id])
  akreditasi_banpt   akreditasi? @relation(fields: [akreditasi_banpt_id], references: [id])
}

model data_alamat_siswa {
  id             Int       @id @default(autoincrement())
  siswa_id       BigInt    @unique
  kabupaten_id   BigInt
  kecamatan_id   BigInt
  kelurahan_id   BigInt
  provinsi_id    BigInt
  alamat_lengkap String
  created_at     DateTime  @default(now())
  updated_at     DateTime?
  deleted_at     DateTime?

  siswa     data_siswa @relation(fields: [siswa_id], references: [id])
  provinsi  provinsi   @relation(fields: [provinsi_id], references: [id])
  kecamatan kecamatan  @relation(fields: [kecamatan_id], references: [id])
  kelurahan kelurahan  @relation(fields: [kelurahan_id], references: [id])
  kabupaten kabupaten  @relation(fields: [kabupaten_id], references: [id])
}

model data_document_siswa {
  id               Int     @id @default(autoincrement())
  siswa_id         BigInt  @unique
  pas_photo_rim    String?
  pas_photo_diktuk String?
  ktp              String?
  kk               String?
  akte_lahir       String?
  sim_c            String?
  ijazah_sd        String?
  ijazah_smp       String?
  ijazah_sma       String?
  ijazah_d3        String?
  ijazah_d4_s1     String?
  ijazah_s2_spes   String?

  siswa data_siswa @relation(fields: [siswa_id], references: [id])
}

model data_jasdiktuk_siswa {
  id                  BigInt   @id @default(autoincrement())
  siswa_id            BigInt   @unique
  hasil_lari_12_m     Decimal? @db.Decimal(10, 2)
  n_a_lari_12_menit   Decimal? @db.Decimal(10, 2)
  hasil_pull_up       Decimal? @db.Decimal(10, 2)
  n_pull_up           Decimal? @db.Decimal(10, 2)
  hasil_sit_up        Decimal? @db.Decimal(10, 2)
  n_sit_up            Decimal? @db.Decimal(10, 2)
  hasil_push_up       Decimal? @db.Decimal(10, 2)
  n_push_up           Decimal? @db.Decimal(10, 2)
  hasil_shuttle_run   Decimal? @db.Decimal(10, 2)
  n_shuttle_run       Decimal? @db.Decimal(10, 2)
  rata2_n_b           Decimal? @db.Decimal(10, 2)
  n_ab                Decimal? @db.Decimal(10, 2)
  n_jasmani           Decimal? @db.Decimal(10, 2)
  n_mental_kep_kar    Decimal? @db.Decimal(10, 2)
  n_akademik_peng     Decimal? @db.Decimal(10, 2)
  n_gbg_akhir         Decimal? @db.Decimal(10, 2)
  ranking             Int?
  catatan_kesehatan   String?
  catatan_pelanggaran String?
  catatan_psikologi   String?
  prestasi            String?
  catatan_khusus      String?

  siswa data_siswa @relation(fields: [siswa_id], references: [id])
}

model file_manager_patma {
  id           BigInt         @id @default(autoincrement())
  name         String
  type         file_type_enum
  parent_id    BigInt?
  originalname String?        @db.VarChar
  filename     String?        @db.VarChar
  path         String
  mime_type    String?
  size         Int?
  created_at   DateTime       @default(now())
  created_by   BigInt?
  updated_at   DateTime?      @updatedAt
  updated_by   BigInt?
  deleted_at   DateTime?
  deleted_by   BigInt?

  parent   file_manager_patma?  @relation("folder_parent", fields: [parent_id], references: [id])
  children file_manager_patma[] @relation("folder_parent")
}
