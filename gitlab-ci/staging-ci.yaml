#SSDM_BACKEND_STG
# test run
# Config cache
cache:
  paths:
    - node_modules/

variables:
  GIT_STRATEGY: none  # Prevents GitLab from automatically cloning

build_ssdm_backend-stg:
  stage: build
  image: 'docker:latest'
  tags:
    - satusdm
  before_script:
    - apk add --no-cache git
    - rm -rf ./* || true  # Ensure the directory is empty
    - find . -mindepth 1 -delete || true  # Extra safeguard
    - git config --global http.sslVerify false  # Disable SSL verification if needed
    - git clone --depth=1 -b staging "http://oauth2:$GITLAB_ACCESS_TOKEN@**************:8099/satusdm/ssdm-backend.git" .
    - apk update && apk add --no-cache python3 py3-pip curl wget
    - 'wget https://dl.min.io/client/mc/release/linux-amd64/mc'
    - chmod +x mc
    - mv mc /usr/local/bin/mc
    - mc alias set minio-staging http://*************:9000 $MINIO_ACCESS_KEY_STG $MINIO_SECRET_KEY_STG --api s3v4
    - mc config host add minio-staging http://*************:9000 $MINIO_ACCESS_KEY_STG $MINIO_SECRET_KEY_STG --api s3v4
    - mc --version
    - mc cp --attr x-amz-acl=public-read --recursive minio-staging/ssdm-datastores/staging/ssdm-backend/env_ssdm_backend.stg  ./
    - mc mv ./env_ssdm_backend.stg ./.env
    - docker version
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker build -t $TAG_COMMIT -t $TAG_LATEST -f Dockerfile .
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
#  services:
#    - 'docker:dind'
  only:
    - staging
  when: manual

#SAST
scan_ssdm-backend-stg:
  stage: SAST
  image: 'alpine:3.19'
  tags:
    - satusdm
  variables:
    GIT_STRATEGY: none
    IMAGE: "$TAG_LATEST"
  allow_failure: true
  before_script:
    - export TRIVY_VERSION=$(wget -qO - "https://api.github.com/repos/aquasecurity/trivy/releases" | grep '"tag_name":v0.51.2' | sed -E 's/.*"v([^"]+)".*/\1/')
    - apk add --no-cache curl docker-cli
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - echo $TRIVY_VERSION
    - wget --no-verbose https://github.com/aquasecurity/trivy/releases/download/v0.51.2/trivy_0.51.2_Linux-64bit.tar.gz -O - | tar -zxvf -
  script:
    #- trivy --exit-code 0 --cache-dir .trivycache/ --no-progress --format template --template "@/tmp/trivy-gitlab.tpl" -o gl-container-scanning-report.json $IMAGE
    - ./trivy image --exit-code 0 --scanners vuln --cache-dir .trivycache/ --no-progress --format template --template "@contrib/gitlab.tpl" -o gl-container-scanning-report.json $IMAGE
    # Print report
    - ./trivy image --exit-code 0 --scanners vuln --cache-dir .trivycache/ --no-progress --severity HIGH $IMAGE
    # Fail on critical vulnerability
    - ./trivy image --exit-code 0 --scanners vuln --cache-dir .trivycache/ --severity CRITICAL --no-progress $IMAGE
  cache:
    paths:
      - .trivycache/
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
  only:
    - staging
  needs:
    - build_ssdm_backend-stg

deploy_ssdm_backend-stg:
  stage: deployment
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  environment:
    name: staging
  before_script:
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | sed 's/\\n/\n/g' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
#    - export TAG_LATEST=dev-v1.$CI_PIPELINE_IID
#    - export TAG_COMMIT=$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:$CI_COMMIT_SHORT_SHA
  tags:
    - satusdm
  script:
    - apt-get install -y rsync wget
    - 'wget https://dl.min.io/client/mc/release/linux-amd64/mc'
    - chmod +x mc
    - mv mc /usr/local/bin/mc
    - mc alias set minio-staging http://*************:9000 $MINIO_ACCESS_KEY $MINIO_SECRET_KEY --api s3v4
    - mc config host add minio-staging http://*************:9000 $MINIO_ACCESS_KEY $MINIO_SECRET_KEY --api s3v4
    - mc --version
    - mc cp --attr x-amz-acl=public-read --recursive minio-staging/ssdm-datastores/staging/ssdm-backend/env_ssdm_backend.stg ./
    - mc mv ./env_ssdm_backend.stg ./.env
    # Login to Gitlab Container registry
    - ssh -o StrictHostKeyChecking=no -t clouduser@************* docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - ssh -o StrictHostKeyChecking=no -t clouduser@************* docker pull $TAG_LATEST
    - ssh -o StrictHostKeyChecking=no -t clouduser@************* docker container rm -f ssdm-backend-staging || true
    - ssh -o StrictHostKeyChecking=no -t clouduser@************* docker run --restart unless-stopped -d -p 4020:5000 --add-host storage.satusdm.polri.go.id:************* --name ssdm-backend-staging $TAG_LATEST
  only:
    - staging
  when: manual