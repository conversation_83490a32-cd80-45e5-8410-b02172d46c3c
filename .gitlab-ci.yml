#SSDM_BACKEND
stages:
  - build
  - SAST
  - deployment

variables:
  TAG_LATEST: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:ssdm-backend-v1.$CI_COMMIT_REF_NAME.$CI_PIPELINE_IID
  TAG_COMMIT: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:latest

include:
  - project: 'satusdm/ssdm-backend'
    ref: $CI_COMMIT_REF_NAME
    file: 'gitlab-ci/development-ci.yaml'
    rules:
      - if: '$CI_COMMIT_REF_NAME == "development"'
  - project: 'satusdm/ssdm-backend'
    ref: $CI_COMMIT_REF_NAME
    file: 'gitlab-ci/staging-ci.yaml'
    rules:
      - if: '$CI_COMMIT_REF_NAME == "staging"'
  - project: 'satusdm/ssdm-backend'
    ref: $CI_COMMIT_REF_NAME
    file: 'gitlab-ci/production-ci.yaml'
    rules:
      - if: '$CI_COMMIT_REF_NAME == "production"'