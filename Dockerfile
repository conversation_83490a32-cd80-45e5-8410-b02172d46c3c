# Step 1: Build Stage (use node:18 as base image for building the app)
FROM node:18 AS build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .

# Generate Prisma client (if you are using Prisma)
ENV PRISMA_BINARIES_MIRROR http://prisma-builds.s3-eu-west-1.amazonaws.com
RUN npx prisma generate

# Build the application (compile TypeScript to JavaScript)
RUN npm run build

# Step 2: Production Stage (use a smaller node:18-slim image)
FROM node:18-slim AS production
WORKDIR /app
COPY --from=build /app/.env /app/.env
COPY --from=build /app/prisma /app/prisma
COPY --from=build /app/dist /app/dist
COPY --from=build /app/package*.json /app/

# Install OpenSSL and required libraries for Prisma
RUN apt-get update && apt-get install -y \
    openssl \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*  # Clean up the package manager cache to reduce image size
    
RUN npm install --production --frozen-lockfile

EXPOSE 3000

ENV NODE_ENV=production

# Run the application in production mode (adjust this based on your package.json scripts)
CMD ["npm", "run", "start:prod"]
